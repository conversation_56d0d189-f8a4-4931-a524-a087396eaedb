{"ast": null, "code": "import React,{useState,useRef,useEffect}from'react';import'./FileSelector.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const GlobalFileSelector=_ref=>{let{isVisible,onClose,onFileChanged}=_ref;const[selectedFile,setSelectedFile]=useState(null);const[isUploading,setIsUploading]=useState(false);const[uploadStatus,setUploadStatus]=useState('');const[currentFileInfo,setCurrentFileInfo]=useState(null);const fileInputRef=useRef(null);// 获取当前Excel文件信息\nuseEffect(()=>{if(isVisible){fetchCurrentFileInfo();}},[isVisible]);const fetchCurrentFileInfo=async()=>{try{const response=await fetch('http://localhost:3001/api/current-excel-info');if(response.ok){const result=await response.json();setCurrentFileInfo(result);}}catch(error){console.error('获取文件信息失败:',error);}};const handleFileChange=event=>{const file=event.target.files[0];if(file){// 验证文件类型\nif(!file.name.endsWith('.xlsx')&&!file.name.endsWith('.xls')){setUploadStatus('❌ 请选择Excel文件 (.xlsx 或 .xls)');return;}// 验证文件大小 (限制为10MB)\nif(file.size>10*1024*1024){setUploadStatus('❌ 文件大小不能超过10MB');return;}setSelectedFile(file);setUploadStatus(\"\\u2705 \\u5DF2\\u9009\\u62E9\\u6587\\u4EF6: \".concat(file.name));}};const handleUpload=async()=>{if(!selectedFile){setUploadStatus('❌ 请先选择文件');return;}setIsUploading(true);setUploadStatus('⏳ 正在上传文件...');try{const formData=new FormData();formData.append('excelFile',selectedFile);const response=await fetch('http://localhost:3001/api/upload-excel',{method:'POST',body:formData});const result=await response.json();if(response.ok){setUploadStatus('✅ 文件上传成功！正在重新加载数据...');// 通知父组件文件已更新\nif(onFileChanged){onFileChanged(selectedFile.name);}// 更新当前文件信息\nawait fetchCurrentFileInfo();// 延迟关闭弹窗，让用户看到成功消息\nsetTimeout(()=>{onClose();setSelectedFile(null);setUploadStatus('');},2000);}else{setUploadStatus(\"\\u274C \\u4E0A\\u4F20\\u5931\\u8D25: \".concat(result.message||'未知错误'));}}catch(error){console.error('上传文件失败:',error);setUploadStatus('❌ 网络错误，请检查服务器连接');}finally{setIsUploading(false);}};const handleClose=()=>{setSelectedFile(null);setUploadStatus('');onClose();};if(!isVisible)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"file-selector-overlay\",onClick:handleClose,children:/*#__PURE__*/_jsxs(\"div\",{className:\"file-selector-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"file-selector-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\uD83D\\uDCC1 \\u9009\\u62E9Excel\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:handleClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-selector-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"current-file-info\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5F53\\u524D\\u6587\\u4EF6:\"}),\" \",(currentFileInfo===null||currentFileInfo===void 0?void 0:currentFileInfo.fileName)||'未设置']}),currentFileInfo&&/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u6587\\u4EF6\\u5927\\u5C0F:\"}),\" \",(currentFileInfo.fileSize/1024/1024).toFixed(2),\" MB\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-upload-area\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"upload-zone\",onClick:()=>{var _fileInputRef$current;return(_fileInputRef$current=fileInputRef.current)===null||_fileInputRef$current===void 0?void 0:_fileInputRef$current.click();},children:[/*#__PURE__*/_jsx(\"div\",{className:\"upload-icon\",children:\"\\uD83D\\uDCC4\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u70B9\\u51FB\\u9009\\u62E9Excel\\u6587\\u4EF6\"}),/*#__PURE__*/_jsx(\"p\",{className:\"upload-hint\",children:\"\\u652F\\u6301 .xlsx \\u548C .xls \\u683C\\u5F0F\\uFF0C\\u6700\\u592710MB\"})]}),/*#__PURE__*/_jsx(\"input\",{ref:fileInputRef,type:\"file\",accept:\".xlsx,.xls\",onChange:handleFileChange,style:{display:'none'}})]}),selectedFile&&/*#__PURE__*/_jsxs(\"div\",{className:\"selected-file-info\",children:[/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5DF2\\u9009\\u62E9:\"}),\" \",selectedFile.name]}),/*#__PURE__*/_jsxs(\"p\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"\\u5927\\u5C0F:\"}),\" \",(selectedFile.size/1024/1024).toFixed(2),\" MB\"]})]}),uploadStatus&&/*#__PURE__*/_jsx(\"div\",{className:\"upload-status \".concat(uploadStatus.includes('❌')?'error':uploadStatus.includes('✅')?'success':'info'),children:uploadStatus})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"file-selector-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-btn\",onClick:handleClose,disabled:isUploading,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(\"button\",{className:\"upload-btn\",onClick:handleUpload,disabled:!selectedFile||isUploading,children:isUploading?'上传中...':'上传文件'})]})]})});};export default GlobalFileSelector;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "GlobalFileSelector", "_ref", "isVisible", "onClose", "onFileChanged", "selectedFile", "setSelectedFile", "isUploading", "setIsUploading", "uploadStatus", "setUploadStatus", "currentFileInfo", "setCurrentFileInfo", "fileInputRef", "fetchCurrentFileInfo", "response", "fetch", "ok", "result", "json", "error", "console", "handleFileChange", "event", "file", "target", "files", "name", "endsWith", "size", "concat", "handleUpload", "formData", "FormData", "append", "method", "body", "setTimeout", "message", "handleClose", "className", "onClick", "children", "e", "stopPropagation", "fileName", "fileSize", "toFixed", "_fileInputRef$current", "current", "click", "ref", "type", "accept", "onChange", "style", "display", "includes", "disabled"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/components/GlobalFileSelector.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport './FileSelector.css';\r\n\r\nconst GlobalFileSelector = ({ isVisible, onClose, onFileChanged }) => {\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadStatus, setUploadStatus] = useState('');\r\n  const [currentFileInfo, setCurrentFileInfo] = useState(null);\r\n  const fileInputRef = useRef(null);\r\n\r\n  // 获取当前Excel文件信息\r\n  useEffect(() => {\r\n    if (isVisible) {\r\n      fetchCurrentFileInfo();\r\n    }\r\n  }, [isVisible]);\r\n\r\n  const fetchCurrentFileInfo = async () => {\r\n    try {\r\n      const response = await fetch('http://localhost:3001/api/current-excel-info');\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        setCurrentFileInfo(result);\r\n      }\r\n    } catch (error) {\r\n      console.error('获取文件信息失败:', error);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (event) => {\r\n    const file = event.target.files[0];\r\n    if (file) {\r\n      // 验证文件类型\r\n      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {\r\n        setUploadStatus('❌ 请选择Excel文件 (.xlsx 或 .xls)');\r\n        return;\r\n      }\r\n      \r\n      // 验证文件大小 (限制为10MB)\r\n      if (file.size > 10 * 1024 * 1024) {\r\n        setUploadStatus('❌ 文件大小不能超过10MB');\r\n        return;\r\n      }\r\n      \r\n      setSelectedFile(file);\r\n      setUploadStatus(`✅ 已选择文件: ${file.name}`);\r\n    }\r\n  };\r\n\r\n  const handleUpload = async () => {\r\n    if (!selectedFile) {\r\n      setUploadStatus('❌ 请先选择文件');\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    setUploadStatus('⏳ 正在上传文件...');\r\n\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('excelFile', selectedFile);\r\n\r\n      const response = await fetch('http://localhost:3001/api/upload-excel', {\r\n        method: 'POST',\r\n        body: formData,\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (response.ok) {\r\n        setUploadStatus('✅ 文件上传成功！正在重新加载数据...');\r\n        \r\n        // 通知父组件文件已更新\r\n        if (onFileChanged) {\r\n          onFileChanged(selectedFile.name);\r\n        }\r\n        \r\n        // 更新当前文件信息\r\n        await fetchCurrentFileInfo();\r\n        \r\n        // 延迟关闭弹窗，让用户看到成功消息\r\n        setTimeout(() => {\r\n          onClose();\r\n          setSelectedFile(null);\r\n          setUploadStatus('');\r\n        }, 2000);\r\n      } else {\r\n        setUploadStatus(`❌ 上传失败: ${result.message || '未知错误'}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('上传文件失败:', error);\r\n      setUploadStatus('❌ 网络错误，请检查服务器连接');\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setSelectedFile(null);\r\n    setUploadStatus('');\r\n    onClose();\r\n  };\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"file-selector-overlay\" onClick={handleClose}>\r\n      <div className=\"file-selector-modal\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"file-selector-header\">\r\n          <h3>📁 选择Excel文件</h3>\r\n          <button className=\"close-btn\" onClick={handleClose}>×</button>\r\n        </div>\r\n        \r\n        <div className=\"file-selector-content\">\r\n          <div className=\"current-file-info\">\r\n            <p><strong>当前文件:</strong> {currentFileInfo?.fileName || '未设置'}</p>\r\n            {currentFileInfo && (\r\n              <p><strong>文件大小:</strong> {(currentFileInfo.fileSize / 1024 / 1024).toFixed(2)} MB</p>\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"file-upload-area\">\r\n            <div \r\n              className=\"upload-zone\"\r\n              onClick={() => fileInputRef.current?.click()}\r\n            >\r\n              <div className=\"upload-icon\">📄</div>\r\n              <p>点击选择Excel文件</p>\r\n              <p className=\"upload-hint\">支持 .xlsx 和 .xls 格式，最大10MB</p>\r\n            </div>\r\n            \r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\".xlsx,.xls\"\r\n              onChange={handleFileChange}\r\n              style={{ display: 'none' }}\r\n            />\r\n          </div>\r\n          \r\n          {selectedFile && (\r\n            <div className=\"selected-file-info\">\r\n              <p><strong>已选择:</strong> {selectedFile.name}</p>\r\n              <p><strong>大小:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>\r\n            </div>\r\n          )}\r\n          \r\n          {uploadStatus && (\r\n            <div className={`upload-status ${uploadStatus.includes('❌') ? 'error' : uploadStatus.includes('✅') ? 'success' : 'info'}`}>\r\n              {uploadStatus}\r\n            </div>\r\n          )}\r\n        </div>\r\n        \r\n        <div className=\"file-selector-actions\">\r\n          <button \r\n            className=\"cancel-btn\" \r\n            onClick={handleClose}\r\n            disabled={isUploading}\r\n          >\r\n            取消\r\n          </button>\r\n          <button \r\n            className=\"upload-btn\" \r\n            onClick={handleUpload}\r\n            disabled={!selectedFile || isUploading}\r\n          >\r\n            {isUploading ? '上传中...' : '上传文件'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default GlobalFileSelector; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,MAAM,CAAEC,SAAS,KAAQ,OAAO,CAC1D,MAAO,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5B,KAAM,CAAAC,kBAAkB,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,SAAS,CAAEC,OAAO,CAAEC,aAAc,CAAC,CAAAH,IAAA,CAC/D,KAAM,CAACI,YAAY,CAAEC,eAAe,CAAC,CAAGb,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACc,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACgB,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkB,eAAe,CAAEC,kBAAkB,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAAAoB,YAAY,CAAGnB,MAAM,CAAC,IAAI,CAAC,CAEjC;AACAC,SAAS,CAAC,IAAM,CACd,GAAIO,SAAS,CAAE,CACbY,oBAAoB,CAAC,CAAC,CACxB,CACF,CAAC,CAAE,CAACZ,SAAS,CAAC,CAAC,CAEf,KAAM,CAAAY,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvC,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,8CAA8C,CAAC,CAC5E,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpCP,kBAAkB,CAACM,MAAM,CAAC,CAC5B,CACF,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAIC,KAAK,EAAK,CAClC,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAClC,GAAIF,IAAI,CAAE,CACR;AACA,GAAI,CAACA,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAI,CAACJ,IAAI,CAACG,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC/DlB,eAAe,CAAC,6BAA6B,CAAC,CAC9C,OACF,CAEA;AACA,GAAIc,IAAI,CAACK,IAAI,CAAG,EAAE,CAAG,IAAI,CAAG,IAAI,CAAE,CAChCnB,eAAe,CAAC,gBAAgB,CAAC,CACjC,OACF,CAEAJ,eAAe,CAACkB,IAAI,CAAC,CACrBd,eAAe,2CAAAoB,MAAA,CAAaN,IAAI,CAACG,IAAI,CAAE,CAAC,CAC1C,CACF,CAAC,CAED,KAAM,CAAAI,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,GAAI,CAAC1B,YAAY,CAAE,CACjBK,eAAe,CAAC,UAAU,CAAC,CAC3B,OACF,CAEAF,cAAc,CAAC,IAAI,CAAC,CACpBE,eAAe,CAAC,aAAa,CAAC,CAE9B,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAE7B,YAAY,CAAC,CAE1C,KAAM,CAAAU,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,wCAAwC,CAAE,CACrEmB,MAAM,CAAE,MAAM,CACdC,IAAI,CAAEJ,QACR,CAAC,CAAC,CAEF,KAAM,CAAAd,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAEpC,GAAIJ,QAAQ,CAACE,EAAE,CAAE,CACfP,eAAe,CAAC,sBAAsB,CAAC,CAEvC;AACA,GAAIN,aAAa,CAAE,CACjBA,aAAa,CAACC,YAAY,CAACsB,IAAI,CAAC,CAClC,CAEA;AACA,KAAM,CAAAb,oBAAoB,CAAC,CAAC,CAE5B;AACAuB,UAAU,CAAC,IAAM,CACflC,OAAO,CAAC,CAAC,CACTG,eAAe,CAAC,IAAI,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CACrB,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,IAAM,CACLA,eAAe,qCAAAoB,MAAA,CAAYZ,MAAM,CAACoB,OAAO,EAAI,MAAM,CAAE,CAAC,CACxD,CACF,CAAE,MAAOlB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BV,eAAe,CAAC,iBAAiB,CAAC,CACpC,CAAC,OAAS,CACRF,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA+B,WAAW,CAAGA,CAAA,GAAM,CACxBjC,eAAe,CAAC,IAAI,CAAC,CACrBI,eAAe,CAAC,EAAE,CAAC,CACnBP,OAAO,CAAC,CAAC,CACX,CAAC,CAED,GAAI,CAACD,SAAS,CAAE,MAAO,KAAI,CAE3B,mBACEL,IAAA,QAAK2C,SAAS,CAAC,uBAAuB,CAACC,OAAO,CAAEF,WAAY,CAAAG,QAAA,cAC1D3C,KAAA,QAAKyC,SAAS,CAAC,qBAAqB,CAACC,OAAO,CAAGE,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAAF,QAAA,eACvE3C,KAAA,QAAKyC,SAAS,CAAC,sBAAsB,CAAAE,QAAA,eACnC7C,IAAA,OAAA6C,QAAA,CAAI,4CAAY,CAAI,CAAC,cACrB7C,IAAA,WAAQ2C,SAAS,CAAC,WAAW,CAACC,OAAO,CAAEF,WAAY,CAAAG,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC3D,CAAC,cAEN3C,KAAA,QAAKyC,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACpC3C,KAAA,QAAKyC,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChC3C,KAAA,MAAA2C,QAAA,eAAG7C,IAAA,WAAA6C,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAAC,CAAA/B,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEkC,QAAQ,GAAI,KAAK,EAAI,CAAC,CACjElC,eAAe,eACdZ,KAAA,MAAA2C,QAAA,eAAG7C,IAAA,WAAA6C,QAAA,CAAQ,2BAAK,CAAQ,CAAC,IAAC,CAAC,CAAC/B,eAAe,CAACmC,QAAQ,CAAG,IAAI,CAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAG,CACtF,EACE,CAAC,cAENhD,KAAA,QAAKyC,SAAS,CAAC,kBAAkB,CAAAE,QAAA,eAC/B3C,KAAA,QACEyC,SAAS,CAAC,aAAa,CACvBC,OAAO,CAAEA,CAAA,QAAAO,qBAAA,QAAAA,qBAAA,CAAMnC,YAAY,CAACoC,OAAO,UAAAD,qBAAA,iBAApBA,qBAAA,CAAsBE,KAAK,CAAC,CAAC,EAAC,CAAAR,QAAA,eAE7C7C,IAAA,QAAK2C,SAAS,CAAC,aAAa,CAAAE,QAAA,CAAC,cAAE,CAAK,CAAC,cACrC7C,IAAA,MAAA6C,QAAA,CAAG,2CAAW,CAAG,CAAC,cAClB7C,IAAA,MAAG2C,SAAS,CAAC,aAAa,CAAAE,QAAA,CAAC,mEAAyB,CAAG,CAAC,EACrD,CAAC,cAEN7C,IAAA,UACEsD,GAAG,CAAEtC,YAAa,CAClBuC,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,YAAY,CACnBC,QAAQ,CAAEhC,gBAAiB,CAC3BiC,KAAK,CAAE,CAAEC,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,EACC,CAAC,CAELnD,YAAY,eACXN,KAAA,QAAKyC,SAAS,CAAC,oBAAoB,CAAAE,QAAA,eACjC3C,KAAA,MAAA2C,QAAA,eAAG7C,IAAA,WAAA6C,QAAA,CAAQ,qBAAI,CAAQ,CAAC,IAAC,CAACrC,YAAY,CAACsB,IAAI,EAAI,CAAC,cAChD5B,KAAA,MAAA2C,QAAA,eAAG7C,IAAA,WAAA6C,QAAA,CAAQ,eAAG,CAAQ,CAAC,IAAC,CAAC,CAACrC,YAAY,CAACwB,IAAI,CAAG,IAAI,CAAG,IAAI,EAAEkB,OAAO,CAAC,CAAC,CAAC,CAAC,KAAG,EAAG,CAAC,EAC1E,CACN,CAEAtC,YAAY,eACXZ,IAAA,QAAK2C,SAAS,kBAAAV,MAAA,CAAmBrB,YAAY,CAACgD,QAAQ,CAAC,GAAG,CAAC,CAAG,OAAO,CAAGhD,YAAY,CAACgD,QAAQ,CAAC,GAAG,CAAC,CAAG,SAAS,CAAG,MAAM,CAAG,CAAAf,QAAA,CACvHjC,YAAY,CACV,CACN,EACE,CAAC,cAENV,KAAA,QAAKyC,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACpC7C,IAAA,WACE2C,SAAS,CAAC,YAAY,CACtBC,OAAO,CAAEF,WAAY,CACrBmB,QAAQ,CAAEnD,WAAY,CAAAmC,QAAA,CACvB,cAED,CAAQ,CAAC,cACT7C,IAAA,WACE2C,SAAS,CAAC,YAAY,CACtBC,OAAO,CAAEV,YAAa,CACtB2B,QAAQ,CAAE,CAACrD,YAAY,EAAIE,WAAY,CAAAmC,QAAA,CAEtCnC,WAAW,CAAG,QAAQ,CAAG,MAAM,CAC1B,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}