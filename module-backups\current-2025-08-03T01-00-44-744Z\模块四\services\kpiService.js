import * as XLSX from 'xlsx';

class KPIService {
  constructor() {
    this.workbook = null;
    this.data = [];
    this.syncCallbacks = {
      onSuccess: null,
      onError: null
    };
  }

  // 加载KPI数据
  async loadKPIData() {
    try {
      // 首先尝试从后端API加载真实数据（从"月度重点KPI"工作表）
      console.log('正在从"月度重点KPI"工作表加载数据...');
      const response = await fetch('http://localhost:3001/api/monthly-kpi-data');
      
      if (response.ok) {
        const result = await response.json();
        if (result.data && result.data.length > 0) {
          this.data = result.data;
          this.lastModified = new Date(result.lastModified);
          console.log('从"月度重点KPI"工作表成功加载数据:', this.data);
          return this.data;
        } else {
          console.warn('月度重点KPI工作表数据不完整');
        }
      } else {
        console.warn('月度重点KPI API响应失败，状态码:', response.status);
      }
    } catch (error) {
      console.error('从月度重点KPI工作表加载数据失败:', error);
    }

    // 如果API失败，使用本地静态数据
    console.log('使用本地静态KPI数据');
    this.data = this.getStaticKPIData();
    return this.data;
  }

  // 获取静态KPI数据（模拟"月度重点KPI"工作表数据）
  getStaticKPIData() {
    return [
      {
        序号: 1,
        指标: '产品线毛利率',
        目标值: '橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',
        分值: 6,
        '统计方式&口径': '财务部门统计',
        考核标准: '完成目标得满分，未完成按比例得分。',
        '4月': '4月',
        '4月完成情况': '',
        '4月得分': '',
        '5月': '5月', 
        '5月完成情况': '',
        '5月得分': ''
      },
      {
        序号: 2,
        指标: '开发降本（万元）',
        目标值: '≥5000',
        分值: 8,
        '统计方式&口径': '成本管控部门统计',
        考核标准: '完成目标得满分，未完成按比例得分。',
        '4月': '4月',
        '4月完成情况': '',
        '4月得分': '',
        '5月': '5月',
        '5月完成情况': '',
        '5月得分': ''
      }
    ];
  }

  // 更新数据 - 实现双向同步
  async updateData(index, field, value) {
    if (this.data[index]) {
      // 更新内存中的数据
      this.data[index][field] = value;
      
      try {
        // 调用后端API同步到Excel文件
        await this.syncToExcel(index, field, value);
        
        if (this.syncCallbacks.onSuccess) {
          this.syncCallbacks.onSuccess();
        }
      } catch (error) {
        console.error('同步到Excel失败:', error);
        if (this.syncCallbacks.onError) {
          this.syncCallbacks.onError(error);
        }
      }
    }
  }

  // 同步到Excel文件（月度重点KPI工作表）
  async syncToExcel(index, field, value) {
    try {
      const response = await fetch('http://localhost:3001/api/update-monthly-kpi-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rowIndex: index,
          field: field,
          value: value,
          worksheet: '月度重点KPI',
          timestamp: new Date().toISOString()
        })
      });

      if (!response.ok) {
        throw new Error(`同步到月度重点KPI工作表失败: ${response.status}`);
      }

      const result = await response.json();
      console.log('月度重点KPI工作表同步成功:', result);
    } catch (error) {
      console.error('月度重点KPI工作表同步错误:', error);
      throw error;
    }
  }

  // 转换数据为Excel格式
  convertDataForExcel(monthPair) {
    const [firstMonth, secondMonth] = monthPair;
    
    return this.data.map(row => {
      // 过滤掉内部使用的属性
      const { rowSpan, isMergedStart, isMergedCell, ...cleanRow } = row;
      
      // 只保留固定列和指定月份的列
      const result = {
        序号: cleanRow.序号,
        指标: cleanRow.指标,
        目标值: cleanRow.目标值,
        分值: cleanRow.分值,
        '统计方式&口径': cleanRow['统计方式&口径'] || cleanRow.统计方式,
        考核标准: cleanRow.考核标准,
      };
      
      // 添加指定月份的列
      result[`${firstMonth}月`] = cleanRow[`${firstMonth}月`];
      result[`${firstMonth}月完成情况`] = cleanRow[`${firstMonth}月完成情况`];
      result[`${firstMonth}月得分`] = cleanRow[`${firstMonth}月得分`];
      result[`${secondMonth}月`] = cleanRow[`${secondMonth}月`];
      result[`${secondMonth}月完成情况`] = cleanRow[`${secondMonth}月完成情况`];
      result[`${secondMonth}月得分`] = cleanRow[`${secondMonth}月得分`];
      
      return result;
    });
  }

  // 设置同步回调
  setSyncCallbacks(onSuccess, onError) {
    this.syncCallbacks.onSuccess = onSuccess;
    this.syncCallbacks.onError = onError;
  }

  // 获取数据统计
  getDataStats() {
    const totalScore = this.data.reduce((sum, item) => {
      const score = item.分值;
      if (score === '' || score === null || score === undefined) return sum;
      return sum + (typeof score === 'number' ? score : (score ? Number(score) || 0 : 0));
    }, 0);

    return {
      totalRecords: this.data.length,
      totalScore: totalScore,
      lastModified: this.lastModified
    };
  }

  // 历史追溯功能
  async getChangeHistory(rowIndex, field) {
    try {
      const response = await fetch(`http://localhost:3001/api/monthly-kpi-history?rowIndex=${rowIndex}&field=${field}&worksheet=月度重点KPI`);
      
      if (response.ok) {
        const history = await response.json();
        return history;
      } else {
        console.warn('获取月度重点KPI历史记录失败');
        return [];
      }
    } catch (error) {
      console.error('获取月度重点KPI历史记录错误:', error);
      return [];
    }
  }

  // 批量导入数据
  async importData(file) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('worksheet', '月度重点KPI');
      
      const response = await fetch('http://localhost:3001/api/import-monthly-kpi-data', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 重新加载数据
          await this.loadKPIData();
          return { success: true, message: result.message };
        } else {
          return { success: false, message: result.message };
        }
      } else {
        return { success: false, message: '导入月度重点KPI数据失败' };
      }
    } catch (error) {
      console.error('导入月度重点KPI数据错误:', error);
      return { success: false, message: error.message };
    }
  }

  // 验证数据格式
  validateData(data) {
    const requiredFields = ['序号', '指标', '目标值', '分值', '统计方式', '考核标准'];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      for (const field of requiredFields) {
        if (!(field in row)) {
          return {
            valid: false,
            message: `第${i + 1}行缺少必需字段: ${field}`
          };
        }
      }
    }
    
    return { valid: true };
  }
}

const kpiService = new KPIService();
export default kpiService; 