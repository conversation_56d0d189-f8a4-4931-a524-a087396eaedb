class TrackingService {
  constructor() {
    this.data = [];
    this.lastModified = null;
    this.syncCallbacks = {
      onSuccess: null,
      onError: null
    };
  }

  // 加载重点工作跟踪数据
  async loadTrackingData() {
    try {
      console.log('正在从API加载重点工作跟踪数据...');
      const response = await fetch('http://localhost:3001/api/tracking-data');
      
      if (response.ok) {
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          this.data = result.data;
          this.lastModified = new Date(result.lastModified);
          console.log('重点工作跟踪数据加载成功:', this.data.length, '条记录');
          return this.data;
        } else {
          console.warn('API返回的跟踪数据格式不正确');
          return [];
        }
      } else {
        console.warn('跟踪数据API响应失败，状态码:', response.status);
        return [];
      }
    } catch (error) {
      console.error('从API加载重点工作跟踪数据失败:', error);
      return [];
    }
  }

  // 更新跟踪数据
  async updateTrackingData(rowIndex, field, value) {
    try {
      console.log('更新跟踪数据:', { rowIndex, field, value });
      
      // 更新本地数据
      if (this.data[rowIndex]) {
        this.data[rowIndex][field] = value;
      }

      // 同步到后端
      const response = await fetch('http://localhost:3001/api/update-tracking-excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rowIndex,
          field,
          value
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('跟踪数据更新成功');
          if (this.syncCallbacks.onSuccess) {
            this.syncCallbacks.onSuccess();
          }
          return true;
        } else {
          console.error('跟踪数据更新失败:', result.message);
          if (this.syncCallbacks.onError) {
            this.syncCallbacks.onError(result.message);
          }
          return false;
        }
      } else {
        console.error('跟踪数据更新请求失败，状态码:', response.status);
        if (this.syncCallbacks.onError) {
          this.syncCallbacks.onError(`HTTP ${response.status}`);
        }
        return false;
      }
    } catch (error) {
      console.error('跟踪数据更新错误:', error);
      if (this.syncCallbacks.onError) {
        this.syncCallbacks.onError(error.message);
      }
      return false;
    }
  }

  // 获取所有重点工作类型
  getWorkTypes() {
    const types = [...new Set(this.data.map(item => item.重点工作类型).filter(type => type && type.trim() !== ''))];
    return types;
  }

  // 按工作类型分组数据
  getDataByWorkType() {
    const grouped = {};
    this.data.forEach(item => {
      const type = item.重点工作类型 || '未分类';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(item);
    });
    return grouped;
  }

  // 获取月份列表
  getMonthlyFields() {
    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const fields = [];
    months.forEach(month => {
      fields.push(`${month}工作计划`);
      fields.push(`${month}完成情况`);
    });
    return fields;
  }

  // 获取统计数据
  getTrackingStats() {
    const workTypes = this.getWorkTypes();
    const totalRecords = this.data.length;
    const monthlyFields = this.getMonthlyFields();
    
    // 计算完成度统计
    let totalFields = 0;
    let completedFields = 0;
    
    this.data.forEach(item => {
      monthlyFields.forEach(field => {
        totalFields++;
        if (item[field] && item[field].toString().trim() !== '') {
          completedFields++;
        }
      });
    });
    
    const completionRate = totalFields > 0 ? ((completedFields / totalFields) * 100).toFixed(1) : 0;

    return {
      totalRecords,
      workTypeCount: workTypes.length,
      completionRate,
      monthlyFieldCount: monthlyFields.length
    };
  }

  // 设置同步回调
  setSyncCallbacks(onSuccess, onError) {
    this.syncCallbacks.onSuccess = onSuccess;
    this.syncCallbacks.onError = onError;
  }

  // 获取原始数据
  getData() {
    return this.data;
  }
}

// 创建单例实例
const trackingService = new TrackingService();

export default trackingService; 