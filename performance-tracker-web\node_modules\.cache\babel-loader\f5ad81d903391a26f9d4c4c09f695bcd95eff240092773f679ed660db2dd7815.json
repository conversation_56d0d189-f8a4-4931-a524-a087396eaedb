{"ast": null, "code": "import React,{useState,useEffect,useMemo,useRef}from'react';import{Chart as ChartJS,CategoryScale,LinearScale,PointElement,LineElement,BarElement,Title,Tooltip,Legend}from'chart.js';import ChartDataLabels from'chartjs-plugin-datalabels';import{Line,Bar}from'react-chartjs-2';import'../styles/DataVisualization.css';// 注册Chart.js组件\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,PointElement,LineElement,BarElement,Title,Tooltip,Legend,ChartDataLabels);const DataVisualization=_ref=>{let{allDepartmentData}=_ref;const[selectedMonth,setSelectedMonth]=useState('all');// 默认选择全部月份\nconst[sortType,setSortType]=useState('scoreDesc');// 排序类型：scoreDesc, scoreAsc, nameAsc\nconst[chartType,setChartType]=useState('bar');// 图表类型：bar 或 line\nconst[selectedDepartments,setSelectedDepartments]=useState([]);// 选中的部门列表\nconst[isDeptDropdownOpen,setIsDeptDropdownOpen]=useState(false);// 控制下拉菜单的显示和隐藏\nconst[dropdownPosition,setDropdownPosition]=useState({top:0,left:0});// 下拉菜单位置\n// 图表引用，用于管理图表实例\nconst chartRef=useRef(null);// 11个部门列表\nconst departments=['金属橡胶件','空簧','系统','客户技术','工艺模具','仿真','特装','技术研究与发展','车端','属地化','车体新材料'];// 月份列表\nconst months=[2,3,4,5,6,7,8,9,10,11,12];// 初始化选中的部门列表（默认全选）\nuseEffect(()=>{if(selectedDepartments.length===0){setSelectedDepartments([...departments]);}},[]);// 处理月份选择\nconst handleMonthChange=event=>{const newMonth=event.target.value==='all'?'all':parseInt(event.target.value);console.log('月份选择变更:',newMonth);// 调试日志\nsetSelectedMonth(newMonth);};// 处理部门选择\nconst handleDepartmentChange=department=>{setSelectedDepartments(prev=>prev.includes(department)?prev.filter(dept=>dept!==department):[...prev,department]);};// 处理全选/取消全选\nconst handleSelectAllDepartments=event=>{if(event.target.checked){setSelectedDepartments([...departments]);}else{setSelectedDepartments([]);}};// 计算下拉菜单位置\nconst calculateDropdownPosition=buttonElement=>{const rect=buttonElement.getBoundingClientRect();const scrollTop=window.pageYOffset||document.documentElement.scrollTop;const scrollLeft=window.pageXOffset||document.documentElement.scrollLeft;return{top:rect.bottom+scrollTop+5,left:rect.left+scrollLeft};};// 处理部门下拉菜单开关\nconst handleDeptDropdownToggle=event=>{if(!isDeptDropdownOpen){const position=calculateDropdownPosition(event.currentTarget);setDropdownPosition(position);}setIsDeptDropdownOpen(!isDeptDropdownOpen);};// 点击外部关闭下拉菜单\nuseEffect(()=>{const handleClickOutside=event=>{if(!event.target.closest('.module6_custom-select-container')){setIsDeptDropdownOpen(false);}};// 处理ESC键关闭下拉菜单\nconst handleEscKey=event=>{if(event.key==='Escape'){setIsDeptDropdownOpen(false);}};document.addEventListener('mousedown',handleClickOutside);document.addEventListener('keydown',handleEscKey);return()=>{document.removeEventListener('mousedown',handleClickOutside);document.removeEventListener('keydown',handleEscKey);};},[]);// 组件卸载时清理图表实例\nuseEffect(()=>{return()=>{if(chartRef.current){chartRef.current.destroy();}};},[]);// 计算各部门月度评分总和\nconst calculateDepartmentScores=useMemo(()=>{const scores={};departments.forEach(dept=>{scores[dept]={};months.forEach(month=>{let totalScore=0;let hasData=false;if(allDepartmentData&&allDepartmentData[dept]){const deptData=allDepartmentData[dept];// 计算关键指标评分\nif(deptData.keyIndicators){deptData.keyIndicators.forEach(item=>{const scoreField=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");if(item[scoreField]&&item[scoreField].value!==''&&item[scoreField].value!==null){const score=parseFloat(item[scoreField].value)||0;totalScore+=score;hasData=true;}});}// 计算重点工作评分\nif(deptData.keyWork){deptData.keyWork.forEach(item=>{const scoreField=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");if(item[scoreField]&&item[scoreField].value!==''&&item[scoreField].value!==null){const score=parseFloat(item[scoreField].value)||0;totalScore+=score;hasData=true;}});}}scores[dept][month]=hasData?totalScore:0;});});return scores;},[allDepartmentData]);const isAllDepartmentsSelected=useMemo(()=>selectedDepartments.length===departments.length,[selectedDepartments,departments]);// 强制图表重新调整大小\nuseEffect(()=>{const timer1=setTimeout(()=>{if(chartRef.current){chartRef.current.resize();}},100);const timer2=setTimeout(()=>{if(chartRef.current){chartRef.current.resize();}},500);const timer3=setTimeout(()=>{if(chartRef.current){chartRef.current.resize();}},1000);return()=>{clearTimeout(timer1);clearTimeout(timer2);clearTimeout(timer3);};},[chartType,selectedDepartments,sortType,isAllDepartmentsSelected]);// 根据月份选择计算部门评分\nconst departmentScoresForDisplay=useMemo(()=>{return departments.map(dept=>{let totalScore=0;if(selectedMonth==='all'){// 全部月份显示：所有月份评分总和\ntotalScore=months.reduce((sum,month)=>{var _calculateDepartmentS;return sum+(((_calculateDepartmentS=calculateDepartmentScores[dept])===null||_calculateDepartmentS===void 0?void 0:_calculateDepartmentS[month])||0);},0);}else{var _calculateDepartmentS2;// 单月显示：指定月份的评分\ntotalScore=((_calculateDepartmentS2=calculateDepartmentScores[dept])===null||_calculateDepartmentS2===void 0?void 0:_calculateDepartmentS2[selectedMonth])||0;}return{department:dept,score:totalScore};});},[calculateDepartmentScores,departments,months,selectedMonth]);// 保持原有的年度总评分计算（用于兼容性）\nconst annualTotalScores=useMemo(()=>{return departments.map(dept=>{const totalScore=months.reduce((sum,month)=>{var _calculateDepartmentS3;return sum+(((_calculateDepartmentS3=calculateDepartmentScores[dept])===null||_calculateDepartmentS3===void 0?void 0:_calculateDepartmentS3[month])||0);},0);return{department:dept,score:totalScore};});},[calculateDepartmentScores,departments,months]);// 条形图数据\nconst barChartData=useMemo(()=>{const colors=['rgba(255, 215, 0, 0.8)',// 金黄色\n'rgba(255, 193, 7, 0.8)',// 琥珀色\n'rgba(255, 235, 59, 0.8)',// 黄色\n'rgba(255, 152, 0, 0.8)',// 橙色\n'rgba(255, 87, 34, 0.8)',// 深橙色\n'rgba(244, 67, 54, 0.8)',// 红色\n'rgba(233, 30, 99, 0.8)',// 粉色\n'rgba(156, 39, 176, 0.8)',// 紫色\n'rgba(103, 58, 183, 0.8)',// 深紫色\n'rgba(63, 81, 181, 0.8)',// 靛蓝色\n'rgba(33, 150, 243, 0.8)'// 蓝色\n];if(isAllDepartmentsSelected){// 全选模式：根据月份选择显示对应数据\nconst sortedData=[...departmentScoresForDisplay].sort((a,b)=>{switch(sortType){case'scoreAsc':return a.score-b.score;case'nameAsc':return a.department.localeCompare(b.department);case'scoreDesc':default:return b.score-a.score;}});// 根据月份选择确定标签\nconst datasetLabel=selectedMonth==='all'?'全部月份总评分':\"\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\");return{labels:sortedData.map(item=>item.department),datasets:[{label:datasetLabel,data:sortedData.map(item=>item.score),backgroundColor:colors,borderColor:colors.map(c=>c.replace('0.8','1')),borderWidth:2,borderRadius:8,borderSkipped:false}]};}else{// 单选/多选模式：根据月份选择显示数据\nif(selectedMonth==='all'){// 全部月份模式：显示月度趋势\nconst datasets=selectedDepartments.map((dept,index)=>({label:dept,data:months.map(month=>{var _calculateDepartmentS4;return((_calculateDepartmentS4=calculateDepartmentScores[dept])===null||_calculateDepartmentS4===void 0?void 0:_calculateDepartmentS4[month])||0;}),backgroundColor:colors[index%colors.length],borderColor:colors[index%colors.length].replace('0.8','1'),borderWidth:2,borderRadius:8,borderSkipped:false}));return{labels:months.map(m=>\"\".concat(m,\"\\u6708\")),datasets};}else{// 单月模式：显示指定月份的部门对比\nconst datasets=[{label:\"\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\"),data:selectedDepartments.map(dept=>{var _calculateDepartmentS5;return((_calculateDepartmentS5=calculateDepartmentScores[dept])===null||_calculateDepartmentS5===void 0?void 0:_calculateDepartmentS5[selectedMonth])||0;}),backgroundColor:colors.slice(0,selectedDepartments.length),borderColor:colors.slice(0,selectedDepartments.length).map(c=>c.replace('0.8','1')),borderWidth:2,borderRadius:8,borderSkipped:false}];return{labels:selectedDepartments,datasets};}}},[isAllDepartmentsSelected,departmentScoresForDisplay,selectedDepartments,months,calculateDepartmentScores,sortType,selectedMonth]);// 折线图数据（月度趋势）\nconst lineChartData=useMemo(()=>{const colors=['rgba(255, 215, 0, 1)',// 金黄色\n'rgba(255, 193, 7, 1)',// 琥珀色\n'rgba(255, 235, 59, 1)',// 黄色\n'rgba(255, 152, 0, 1)',// 橙色\n'rgba(255, 87, 34, 1)',// 深橙色\n'rgba(244, 67, 54, 1)',// 红色\n'rgba(233, 30, 99, 1)',// 粉色\n'rgba(156, 39, 176, 1)',// 紫色\n'rgba(103, 58, 183, 1)',// 深紫色\n'rgba(63, 81, 181, 1)',// 靛蓝色\n'rgba(33, 150, 243, 1)'// 蓝色\n];if(selectedMonth==='all'){// 全部月份模式：显示月度趋势\nconst datasets=selectedDepartments.map((dept,index)=>{return{label:dept,data:months.map(month=>calculateDepartmentScores[dept]?calculateDepartmentScores[dept][month]||0:0),borderColor:colors[index%colors.length],backgroundColor:colors[index%colors.length].replace('1)','0.1)'),tension:0.4,pointRadius:6,pointHoverRadius:8,borderWidth:3};});return{labels:months.map(month=>\"\".concat(month,\"\\u6708\")),datasets};}else{// 单月模式：显示单个数据点（转换为柱状图样式的数据）\nconst datasets=selectedDepartments.map((dept,index)=>{return{label:dept,data:[calculateDepartmentScores[dept]?calculateDepartmentScores[dept][selectedMonth]||0:0],borderColor:colors[index%colors.length],backgroundColor:colors[index%colors.length].replace('1)','0.1)'),tension:0.4,pointRadius:8,pointHoverRadius:10,borderWidth:3};});return{labels:[\"\".concat(selectedMonth,\"\\u6708\")],datasets};}},[calculateDepartmentScores,selectedDepartments,selectedMonth,months]);// 图表配置选项\nconst chartOptions=useMemo(()=>{const isBarChartAllSelected=chartType==='bar'&&isAllDepartmentsSelected;const isBarChartSomeSelected=chartType==='bar'&&!isAllDepartmentsSelected;let titleText='各部门月度绩效评分趋势';let xTitle='月份';let yTitle='评分';if(isBarChartAllSelected){if(selectedMonth==='all'){titleText='各部门全部月份总评分对比';xTitle='部门';yTitle='全部月份总评分';}else{titleText=\"\\u5404\\u90E8\\u95E8\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\\u5BF9\\u6BD4\");xTitle='部门';yTitle=\"\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\");}}else if(isBarChartSomeSelected){if(selectedMonth==='all'){titleText='所选部门月度评分趋势';xTitle='月份';yTitle='评分';}else{titleText=\"\\u6240\\u9009\\u90E8\\u95E8\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\\u5BF9\\u6BD4\");xTitle='部门';yTitle=\"\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\");}}else if(chartType==='line'){if(selectedMonth==='all'){titleText='所选部门月度评分趋势';xTitle='月份';yTitle='评分';}else{titleText=\"\\u6240\\u9009\\u90E8\\u95E8\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\");xTitle='部门';yTitle=\"\".concat(selectedMonth,\"\\u6708\\u8BC4\\u5206\");}}return{responsive:true,maintainAspectRatio:false,// 允许图表填充容器\nresizeDelay:0,// 立即响应尺寸变化\naspectRatio:undefined,// 禁用宽高比限制\ninteraction:{intersect:false,mode:'index'},layout:{padding:{top:5,bottom:5,left:5,right:5}},plugins:{legend:{position:'top',labels:{color:'#ffffff',font:{size:16,// 调大二号字体\nfamily:'Rajdhani, sans-serif'}}},title:{display:true,text:titleText,color:'#ffd700',font:{size:24,// 调大二号字体\nfamily:'Rajdhani, sans-serif',weight:'bold'},align:'center',// 确保标题居中\nposition:'top'// 标题位置在顶部\n},tooltip:{backgroundColor:'rgba(0, 0, 0, 0.8)',titleColor:'#ffd700',bodyColor:'#ffffff',borderColor:'#ffd700',borderWidth:1,callbacks:{label:function(context){return\"\".concat(context.dataset.label,\": \").concat(context.parsed.y.toFixed(2),\"\\u5206\");}}},datalabels:{display:true,color:'#ffd700',font:{size:15,// 调大二号字体\nfamily:'Rajdhani, sans-serif',weight:'bold'},formatter:function(value){return value.toFixed(2);},anchor:chartType==='bar'?'end':'top',align:chartType==='bar'?'top':'top',offset:4,textShadowColor:'rgba(0, 0, 0, 0.5)',textShadowBlur:2}},scales:{x:{title:{display:true,text:xTitle,color:'#ffd700',font:{size:20,// 调大二号字体\nfamily:'Rajdhani, sans-serif',weight:'bold'}},ticks:{color:'#ffffff',font:{size:16,// 调大二号字体\nfamily:'Rajdhani, sans-serif'}},grid:{color:'rgba(255, 255, 255, 0.1)'}},y:{title:{display:true,text:yTitle,color:'#ffd700',font:{size:20,// 调大二号字体\nfamily:'Rajdhani, sans-serif',weight:'bold'}},ticks:{color:'#ffffff',font:{size:16,// 调大二号字体\nfamily:'Rajdhani, sans-serif'}},grid:{color:'rgba(255, 255, 255, 0.1)'},// 动态调整Y轴范围，不从0开始，突出数据差异\nbeginAtZero:false,// 不从0开始\ngrace:'5%'// 在数据范围基础上增加5%的空间\n}}};},[chartType,isAllDepartmentsSelected,sortType,selectedMonth]);return/*#__PURE__*/_jsxs(\"div\",{className:\"module6_data-visualization\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_visualization-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_control-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"module6_control-label\",children:\"\\u6708\\u4EFD\\u9009\\u62E9\\uFF1A\"}),/*#__PURE__*/_jsxs(\"select\",{value:selectedMonth,onChange:handleMonthChange,className:\"module6_month-select\",disabled:false,title:\"\\u9009\\u62E9\\u8981\\u663E\\u793A\\u7684\\u6708\\u4EFD\\u6570\\u636E\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u5168\\u90E8\\u6708\\u4EFD\"}),months.map(month=>/*#__PURE__*/_jsxs(\"option\",{value:month,children:[month,\"\\u6708\"]},month))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_control-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"module6_control-label\",children:\"\\u56FE\\u8868\\u7C7B\\u578B\\uFF1A\"}),/*#__PURE__*/_jsxs(\"select\",{value:chartType,onChange:e=>setChartType(e.target.value),className:\"module6_chart-type-select\",children:[/*#__PURE__*/_jsx(\"option\",{value:\"bar\",children:\"\\u6761\\u5F62\\u56FE\\uFF08\\u90E8\\u95E8\\u5BF9\\u6BD4\\uFF09\"}),/*#__PURE__*/_jsx(\"option\",{value:\"line\",children:\"\\u6298\\u7EBF\\u56FE\\uFF08\\u6708\\u5EA6\\u8D8B\\u52BF\\uFF09\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_control-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"module6_control-label\",children:\"\\u6392\\u5E8F\\u65B9\\u5F0F\\uFF1A\"}),/*#__PURE__*/_jsxs(\"select\",{value:sortType,onChange:e=>setSortType(e.target.value),className:\"module6_sort-select\",disabled:chartType==='line'||chartType==='bar'&&!isAllDepartmentsSelected,children:[/*#__PURE__*/_jsx(\"option\",{value:\"scoreDesc\",children:\"\\u8BC4\\u5206\\u964D\\u5E8F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"scoreAsc\",children:\"\\u8BC4\\u5206\\u5347\\u5E8F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"nameAsc\",children:\"\\u90E8\\u95E8\\u540D\\u79F0\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_control-group\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"module6_control-label\",children:\"\\u90E8\\u95E8\\u7B5B\\u9009\\uFF1A\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_custom-select-container\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"module6_department-select\",onClick:handleDeptDropdownToggle,children:[selectedDepartments.length===departments.length?'已全选':selectedDepartments.length===0?'未选择':\"\\u5DF2\\u9009\\u62E9 \".concat(selectedDepartments.length,\" \\u9879\"),/*#__PURE__*/_jsx(\"span\",{className:\"module6_dropdown-arrow \".concat(isDeptDropdownOpen?'open':''),children:\"\\u25BC\"})]}),isDeptDropdownOpen&&/*#__PURE__*/_jsxs(\"div\",{className:\"module6_custom-select-menu\",style:{top:\"\".concat(dropdownPosition.top,\"px\"),left:\"\".concat(dropdownPosition.left,\"px\")},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_checkbox-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"select-all-departments\",className:\"module6_custom-checkbox\",checked:selectedDepartments.length===departments.length,onChange:handleSelectAllDepartments}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"select-all-departments\",children:\"\\u5168\\u9009\"})]}),departments.map(dept=>/*#__PURE__*/_jsxs(\"div\",{className:\"module6_checkbox-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",id:\"dept-\".concat(dept),className:\"module6_custom-checkbox\",checked:selectedDepartments.includes(dept),onChange:()=>handleDepartmentChange(dept)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"dept-\".concat(dept),children:dept})]},dept))]})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_chart-container\",children:chartType==='bar'?/*#__PURE__*/_jsx(Bar,{ref:chartRef,data:barChartData,options:chartOptions},\"bar-\".concat(chartType,\"-\").concat(selectedDepartments.length)):/*#__PURE__*/_jsx(Line,{ref:chartRef,data:lineChartData,options:chartOptions},\"line-\".concat(chartType,\"-\").concat(selectedDepartments.length))})]});};export default DataVisualization;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useMemo", "useRef", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "ChartDataLabels", "Line", "Bar", "jsx", "_jsx", "jsxs", "_jsxs", "register", "DataVisualization", "_ref", "allDepartmentData", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "sortType", "setSortType", "chartType", "setChartType", "selectedDepartments", "setSelectedDepartments", "isDeptDropdownOpen", "setIsDeptDropdownOpen", "dropdownPosition", "setDropdownPosition", "top", "left", "chartRef", "departments", "months", "length", "handleMonthChange", "event", "newMonth", "target", "value", "parseInt", "console", "log", "handleDepartmentChange", "department", "prev", "includes", "filter", "dept", "handleSelectAllDepartments", "checked", "calculateDropdownPosition", "buttonElement", "rect", "getBoundingClientRect", "scrollTop", "window", "pageYOffset", "document", "documentElement", "scrollLeft", "pageXOffset", "bottom", "handleDeptDropdownToggle", "position", "currentTarget", "handleClickOutside", "closest", "handleEscKey", "key", "addEventListener", "removeEventListener", "current", "destroy", "calculateDepartmentScores", "scores", "for<PERSON>ach", "month", "totalScore", "hasData", "deptData", "keyIndicators", "item", "scoreField", "concat", "score", "parseFloat", "keyWork", "isAllDepartmentsSelected", "timer1", "setTimeout", "resize", "timer2", "timer3", "clearTimeout", "departmentScoresForDisplay", "map", "reduce", "sum", "_calculateDepartmentS", "_calculateDepartmentS2", "annualTotalScores", "_calculateDepartmentS3", "barChartData", "colors", "sortedData", "sort", "a", "b", "localeCompare", "datasetLabel", "labels", "datasets", "label", "data", "backgroundColor", "borderColor", "c", "replace", "borderWidth", "borderRadius", "borderSkipped", "index", "_calculateDepartmentS4", "m", "_calculateDepartmentS5", "slice", "lineChartData", "tension", "pointRadius", "pointHoverRadius", "chartOptions", "isBarChartAllSelected", "isBarChartSomeSelected", "titleText", "xTitle", "yTitle", "responsive", "maintainAspectRatio", "resizeDelay", "aspectRatio", "undefined", "interaction", "intersect", "mode", "layout", "padding", "right", "plugins", "legend", "color", "font", "size", "family", "title", "display", "text", "weight", "align", "tooltip", "titleColor", "bodyColor", "callbacks", "context", "dataset", "parsed", "y", "toFixed", "datalabels", "formatter", "anchor", "offset", "textShadowColor", "textShadowBlur", "scales", "x", "ticks", "grid", "beginAtZero", "grace", "className", "children", "onChange", "disabled", "e", "onClick", "style", "type", "id", "htmlFor", "ref", "options"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块六/components/DataVisualization.js"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useRef } from 'react';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport ChartDataLabels from 'chartjs-plugin-datalabels';\nimport { Line, Bar } from 'react-chartjs-2';\nimport '../styles/DataVisualization.css';\n\n// 注册Chart.js组件\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n  ChartDataLabels\n);\n\nconst DataVisualization = ({ allDepartmentData }) => {\n  const [selectedMonth, setSelectedMonth] = useState('all'); // 默认选择全部月份\n  const [sortType, setSortType] = useState('scoreDesc'); // 排序类型：scoreDesc, scoreAsc, nameAsc\n  const [chartType, setChartType] = useState('bar'); // 图表类型：bar 或 line\n  const [selectedDepartments, setSelectedDepartments] = useState([]); // 选中的部门列表\n  const [isDeptDropdownOpen, setIsDeptDropdownOpen] = useState(false); // 控制下拉菜单的显示和隐藏\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 }); // 下拉菜单位置\n\n  // 图表引用，用于管理图表实例\n  const chartRef = useRef(null);\n\n  // 11个部门列表\n  const departments = [\n    '金属橡胶件',\n    '空簧',\n    '系统',\n    '客户技术',\n    '工艺模具',\n    '仿真',\n    '特装',\n    '技术研究与发展',\n    '车端',\n    '属地化',\n    '车体新材料'\n  ];\n\n  // 月份列表\n  const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\n\n  // 初始化选中的部门列表（默认全选）\n  useEffect(() => {\n    if (selectedDepartments.length === 0) {\n      setSelectedDepartments([...departments]);\n    }\n  }, []);\n\n  // 处理月份选择\n  const handleMonthChange = (event) => {\n    const newMonth = event.target.value === 'all' ? 'all' : parseInt(event.target.value);\n    console.log('月份选择变更:', newMonth); // 调试日志\n    setSelectedMonth(newMonth);\n  };\n\n  // 处理部门选择\n  const handleDepartmentChange = (department) => {\n    setSelectedDepartments(prev =>\n      prev.includes(department)\n        ? prev.filter(dept => dept !== department)\n        : [...prev, department]\n    );\n  };\n\n  // 处理全选/取消全选\n  const handleSelectAllDepartments = (event) => {\n    if (event.target.checked) {\n      setSelectedDepartments([...departments]);\n    } else {\n      setSelectedDepartments([]);\n    }\n  };\n\n  // 计算下拉菜单位置\n  const calculateDropdownPosition = (buttonElement) => {\n    const rect = buttonElement.getBoundingClientRect();\n    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\n\n    return {\n      top: rect.bottom + scrollTop + 5,\n      left: rect.left + scrollLeft\n    };\n  };\n\n  // 处理部门下拉菜单开关\n  const handleDeptDropdownToggle = (event) => {\n    if (!isDeptDropdownOpen) {\n      const position = calculateDropdownPosition(event.currentTarget);\n      setDropdownPosition(position);\n    }\n    setIsDeptDropdownOpen(!isDeptDropdownOpen);\n  };\n\n  // 点击外部关闭下拉菜单\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (!event.target.closest('.module6_custom-select-container')) {\n        setIsDeptDropdownOpen(false);\n      }\n    };\n\n    // 处理ESC键关闭下拉菜单\n    const handleEscKey = (event) => {\n      if (event.key === 'Escape') {\n        setIsDeptDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    document.addEventListener('keydown', handleEscKey);\n\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n      document.removeEventListener('keydown', handleEscKey);\n    };\n  }, []);\n\n  // 组件卸载时清理图表实例\n  useEffect(() => {\n    return () => {\n      if (chartRef.current) {\n        chartRef.current.destroy();\n      }\n    };\n  }, []);\n\n  // 计算各部门月度评分总和\n  const calculateDepartmentScores = useMemo(() => {\n    const scores = {};\n    \n    departments.forEach(dept => {\n      scores[dept] = {};\n      months.forEach(month => {\n        let totalScore = 0;\n        let hasData = false;\n        \n        if (allDepartmentData && allDepartmentData[dept]) {\n          const deptData = allDepartmentData[dept];\n          \n          // 计算关键指标评分\n          if (deptData.keyIndicators) {\n            deptData.keyIndicators.forEach(item => {\n              const scoreField = `${month}月评分`;\n              if (item[scoreField] && item[scoreField].value !== '' && item[scoreField].value !== null) {\n                const score = parseFloat(item[scoreField].value) || 0;\n                totalScore += score;\n                hasData = true;\n              }\n            });\n          }\n          \n          // 计算重点工作评分\n          if (deptData.keyWork) {\n            deptData.keyWork.forEach(item => {\n              const scoreField = `${month}月评分`;\n              if (item[scoreField] && item[scoreField].value !== '' && item[scoreField].value !== null) {\n                const score = parseFloat(item[scoreField].value) || 0;\n                totalScore += score;\n                hasData = true;\n              }\n            });\n          }\n        }\n        \n        scores[dept][month] = hasData ? totalScore : 0;\n      });\n    });\n    \n    return scores;\n  }, [allDepartmentData]);\n\n  const isAllDepartmentsSelected = useMemo(() => selectedDepartments.length === departments.length, [selectedDepartments, departments]);\n\n  // 强制图表重新调整大小\n  useEffect(() => {\n    const timer1 = setTimeout(() => {\n      if (chartRef.current) {\n        chartRef.current.resize();\n      }\n    }, 100);\n\n    const timer2 = setTimeout(() => {\n      if (chartRef.current) {\n        chartRef.current.resize();\n      }\n    }, 500);\n\n    const timer3 = setTimeout(() => {\n      if (chartRef.current) {\n        chartRef.current.resize();\n      }\n    }, 1000);\n\n    return () => {\n      clearTimeout(timer1);\n      clearTimeout(timer2);\n      clearTimeout(timer3);\n    };\n  }, [chartType, selectedDepartments, sortType, isAllDepartmentsSelected]);\n\n  // 根据月份选择计算部门评分\n  const departmentScoresForDisplay = useMemo(() => {\n    return departments.map(dept => {\n      let totalScore = 0;\n\n      if (selectedMonth === 'all') {\n        // 全部月份显示：所有月份评分总和\n        totalScore = months.reduce((sum, month) => {\n          return sum + (calculateDepartmentScores[dept]?.[month] || 0);\n        }, 0);\n      } else {\n        // 单月显示：指定月份的评分\n        totalScore = calculateDepartmentScores[dept]?.[selectedMonth] || 0;\n      }\n\n      return { department: dept, score: totalScore };\n    });\n  }, [calculateDepartmentScores, departments, months, selectedMonth]);\n\n  // 保持原有的年度总评分计算（用于兼容性）\n  const annualTotalScores = useMemo(() => {\n    return departments.map(dept => {\n        const totalScore = months.reduce((sum, month) => {\n            return sum + (calculateDepartmentScores[dept]?.[month] || 0);\n        }, 0);\n        return { department: dept, score: totalScore };\n    });\n  }, [calculateDepartmentScores, departments, months]);\n\n  // 条形图数据\n  const barChartData = useMemo(() => {\n    const colors = [\n      'rgba(255, 215, 0, 0.8)',   // 金黄色\n      'rgba(255, 193, 7, 0.8)',   // 琥珀色\n      'rgba(255, 235, 59, 0.8)',  // 黄色\n      'rgba(255, 152, 0, 0.8)',   // 橙色\n      'rgba(255, 87, 34, 0.8)',   // 深橙色\n      'rgba(244, 67, 54, 0.8)',   // 红色\n      'rgba(233, 30, 99, 0.8)',   // 粉色\n      'rgba(156, 39, 176, 0.8)',  // 紫色\n      'rgba(103, 58, 183, 0.8)',  // 深紫色\n      'rgba(63, 81, 181, 0.8)',   // 靛蓝色\n      'rgba(33, 150, 243, 0.8)'   // 蓝色\n    ];\n\n    if (isAllDepartmentsSelected) {\n      // 全选模式：根据月份选择显示对应数据\n      const sortedData = [...departmentScoresForDisplay].sort((a, b) => {\n        switch (sortType) {\n          case 'scoreAsc':\n            return a.score - b.score;\n          case 'nameAsc':\n            return a.department.localeCompare(b.department);\n          case 'scoreDesc':\n          default:\n            return b.score - a.score;\n        }\n      });\n\n      // 根据月份选择确定标签\n      const datasetLabel = selectedMonth === 'all' ? '全部月份总评分' : `${selectedMonth}月评分`;\n\n      return {\n        labels: sortedData.map(item => item.department),\n        datasets: [\n          {\n            label: datasetLabel,\n            data: sortedData.map(item => item.score),\n            backgroundColor: colors,\n            borderColor: colors.map(c => c.replace('0.8', '1')),\n            borderWidth: 2,\n            borderRadius: 8,\n            borderSkipped: false,\n          }\n        ]\n      };\n    } else {\n      // 单选/多选模式：根据月份选择显示数据\n      if (selectedMonth === 'all') {\n        // 全部月份模式：显示月度趋势\n        const datasets = selectedDepartments.map((dept, index) => ({\n          label: dept,\n          data: months.map(month => calculateDepartmentScores[dept]?.[month] || 0),\n          backgroundColor: colors[index % colors.length],\n          borderColor: colors[index % colors.length].replace('0.8', '1'),\n          borderWidth: 2,\n          borderRadius: 8,\n          borderSkipped: false,\n        }));\n        return {\n          labels: months.map(m => `${m}月`),\n          datasets\n        };\n      } else {\n        // 单月模式：显示指定月份的部门对比\n        const datasets = [{\n          label: `${selectedMonth}月评分`,\n          data: selectedDepartments.map(dept => calculateDepartmentScores[dept]?.[selectedMonth] || 0),\n          backgroundColor: colors.slice(0, selectedDepartments.length),\n          borderColor: colors.slice(0, selectedDepartments.length).map(c => c.replace('0.8', '1')),\n          borderWidth: 2,\n          borderRadius: 8,\n          borderSkipped: false,\n        }];\n        return {\n          labels: selectedDepartments,\n          datasets\n        };\n      }\n    }\n  }, [isAllDepartmentsSelected, departmentScoresForDisplay, selectedDepartments, months, calculateDepartmentScores, sortType, selectedMonth]);\n\n  // 折线图数据（月度趋势）\n  const lineChartData = useMemo(() => {\n    const colors = [\n      'rgba(255, 215, 0, 1)',   // 金黄色\n      'rgba(255, 193, 7, 1)',   // 琥珀色\n      'rgba(255, 235, 59, 1)',  // 黄色\n      'rgba(255, 152, 0, 1)',   // 橙色\n      'rgba(255, 87, 34, 1)',   // 深橙色\n      'rgba(244, 67, 54, 1)',   // 红色\n      'rgba(233, 30, 99, 1)',   // 粉色\n      'rgba(156, 39, 176, 1)',  // 紫色\n      'rgba(103, 58, 183, 1)',  // 深紫色\n      'rgba(63, 81, 181, 1)',   // 靛蓝色\n      'rgba(33, 150, 243, 1)'   // 蓝色\n    ];\n\n    if (selectedMonth === 'all') {\n      // 全部月份模式：显示月度趋势\n      const datasets = selectedDepartments.map((dept, index) => {\n        return {\n          label: dept,\n          data: months.map(month => calculateDepartmentScores[dept] ? calculateDepartmentScores[dept][month] || 0 : 0),\n          borderColor: colors[index % colors.length],\n          backgroundColor: colors[index % colors.length].replace('1)', '0.1)'),\n          tension: 0.4,\n          pointRadius: 6,\n          pointHoverRadius: 8,\n          borderWidth: 3,\n        };\n      });\n\n      return {\n        labels: months.map(month => `${month}月`),\n        datasets\n      };\n    } else {\n      // 单月模式：显示单个数据点（转换为柱状图样式的数据）\n      const datasets = selectedDepartments.map((dept, index) => {\n        return {\n          label: dept,\n          data: [calculateDepartmentScores[dept] ? calculateDepartmentScores[dept][selectedMonth] || 0 : 0],\n          borderColor: colors[index % colors.length],\n          backgroundColor: colors[index % colors.length].replace('1)', '0.1)'),\n          tension: 0.4,\n          pointRadius: 8,\n          pointHoverRadius: 10,\n          borderWidth: 3,\n        };\n      });\n\n      return {\n        labels: [`${selectedMonth}月`],\n        datasets\n      };\n    }\n  }, [calculateDepartmentScores, selectedDepartments, selectedMonth, months]);\n\n  // 图表配置选项\n  const chartOptions = useMemo(() => {\n    const isBarChartAllSelected = chartType === 'bar' && isAllDepartmentsSelected;\n    const isBarChartSomeSelected = chartType === 'bar' && !isAllDepartmentsSelected;\n\n    let titleText = '各部门月度绩效评分趋势';\n    let xTitle = '月份';\n    let yTitle = '评分';\n\n    if (isBarChartAllSelected) {\n      if (selectedMonth === 'all') {\n        titleText = '各部门全部月份总评分对比';\n        xTitle = '部门';\n        yTitle = '全部月份总评分';\n      } else {\n        titleText = `各部门${selectedMonth}月评分对比`;\n        xTitle = '部门';\n        yTitle = `${selectedMonth}月评分`;\n      }\n    } else if (isBarChartSomeSelected) {\n      if (selectedMonth === 'all') {\n        titleText = '所选部门月度评分趋势';\n        xTitle = '月份';\n        yTitle = '评分';\n      } else {\n        titleText = `所选部门${selectedMonth}月评分对比`;\n        xTitle = '部门';\n        yTitle = `${selectedMonth}月评分`;\n      }\n    } else if (chartType === 'line') {\n      if (selectedMonth === 'all') {\n        titleText = '所选部门月度评分趋势';\n        xTitle = '月份';\n        yTitle = '评分';\n      } else {\n        titleText = `所选部门${selectedMonth}月评分`;\n        xTitle = '部门';\n        yTitle = `${selectedMonth}月评分`;\n      }\n    }\n\n    return {\n      responsive: true,\n      maintainAspectRatio: false, // 允许图表填充容器\n      resizeDelay: 0, // 立即响应尺寸变化\n      aspectRatio: undefined, // 禁用宽高比限制\n      interaction: {\n        intersect: false,\n        mode: 'index'\n      },\n      layout: {\n        padding: {\n          top: 5,\n          bottom: 5,\n          left: 5,\n          right: 5\n        }\n      },\n      plugins: {\n        legend: {\n          position: 'top',\n          labels: {\n            color: '#ffffff',\n            font: {\n              size: 16, // 调大二号字体\n              family: 'Rajdhani, sans-serif'\n            }\n          }\n        },\n        title: {\n          display: true,\n          text: titleText,\n          color: '#ffd700',\n          font: {\n            size: 24, // 调大二号字体\n            family: 'Rajdhani, sans-serif',\n            weight: 'bold'\n          },\n          align: 'center', // 确保标题居中\n          position: 'top' // 标题位置在顶部\n        },\n        tooltip: {\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          titleColor: '#ffd700',\n          bodyColor: '#ffffff',\n          borderColor: '#ffd700',\n          borderWidth: 1,\n          callbacks: {\n            label: function(context) {\n              return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}分`;\n            }\n          }\n        },\n        datalabels: {\n          display: true,\n          color: '#ffd700',\n          font: {\n            size: 15, // 调大二号字体\n            family: 'Rajdhani, sans-serif',\n            weight: 'bold'\n          },\n          formatter: function(value) {\n            return value.toFixed(2);\n          },\n          anchor: chartType === 'bar' ? 'end' : 'top',\n          align: chartType === 'bar' ? 'top' : 'top',\n          offset: 4,\n          textShadowColor: 'rgba(0, 0, 0, 0.5)',\n          textShadowBlur: 2\n        }\n      },\n      scales: {\n        x: {\n          title: {\n            display: true,\n            text: xTitle,\n            color: '#ffd700',\n            font: {\n              size: 20, // 调大二号字体\n              family: 'Rajdhani, sans-serif',\n              weight: 'bold'\n            }\n          },\n          ticks: {\n            color: '#ffffff',\n            font: {\n              size: 16, // 调大二号字体\n              family: 'Rajdhani, sans-serif'\n            }\n          },\n          grid: {\n            color: 'rgba(255, 255, 255, 0.1)'\n          }\n        },\n        y: {\n          title: {\n            display: true,\n            text: yTitle,\n            color: '#ffd700',\n            font: {\n              size: 20, // 调大二号字体\n              family: 'Rajdhani, sans-serif',\n              weight: 'bold'\n            }\n          },\n          ticks: {\n            color: '#ffffff',\n            font: {\n              size: 16, // 调大二号字体\n              family: 'Rajdhani, sans-serif'\n            }\n          },\n          grid: {\n            color: 'rgba(255, 255, 255, 0.1)'\n          },\n          // 动态调整Y轴范围，不从0开始，突出数据差异\n          beginAtZero: false, // 不从0开始\n          grace: '5%' // 在数据范围基础上增加5%的空间\n        }\n      }\n    };\n  }, [chartType, isAllDepartmentsSelected, sortType, selectedMonth]);\n\n  return (\n    <div className=\"module6_data-visualization\">\n      {/* 控制面板 */}\n      <div className=\"module6_visualization-controls\">\n        <div className=\"module6_control-group\">\n          <label className=\"module6_control-label\">月份选择：</label>\n          <select\n            value={selectedMonth}\n            onChange={handleMonthChange}\n            className=\"module6_month-select\"\n            disabled={false}\n            title=\"选择要显示的月份数据\"\n          >\n            <option value=\"all\">全部月份</option>\n            {months.map(month => (\n              <option key={month} value={month}>{month}月</option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"module6_control-group\">\n          <label className=\"module6_control-label\">图表类型：</label>\n          <select \n            value={chartType} \n            onChange={(e) => setChartType(e.target.value)}\n            className=\"module6_chart-type-select\"\n          >\n            <option value=\"bar\">条形图（部门对比）</option>\n            <option value=\"line\">折线图（月度趋势）</option>\n          </select>\n        </div>\n\n        <div className=\"module6_control-group\">\n          <label className=\"module6_control-label\">排序方式：</label>\n          <select\n            value={sortType}\n            onChange={(e) => setSortType(e.target.value)}\n            className=\"module6_sort-select\"\n            disabled={chartType === 'line' || (chartType === 'bar' && !isAllDepartmentsSelected)}\n          >\n            <option value=\"scoreDesc\">评分降序</option>\n            <option value=\"scoreAsc\">评分升序</option>\n            <option value=\"nameAsc\">部门名称</option>\n          </select>\n        </div>\n\n        <div className=\"module6_control-group\">\n          <label className=\"module6_control-label\">部门筛选：</label>\n          <div className=\"module6_custom-select-container\">\n            <button\n              className=\"module6_department-select\"\n              onClick={handleDeptDropdownToggle}\n            >\n              {selectedDepartments.length === departments.length\n                ? '已全选'\n                : selectedDepartments.length === 0\n                ? '未选择'\n                : `已选择 ${selectedDepartments.length} 项`}\n              <span className={`module6_dropdown-arrow ${isDeptDropdownOpen ? 'open' : ''}`}>▼</span>\n            </button>\n            {isDeptDropdownOpen && (\n              <div\n                className=\"module6_custom-select-menu\"\n                style={{\n                  top: `${dropdownPosition.top}px`,\n                  left: `${dropdownPosition.left}px`\n                }}\n              >\n                <div className=\"module6_checkbox-item\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"select-all-departments\"\n                    className=\"module6_custom-checkbox\"\n                    checked={selectedDepartments.length === departments.length}\n                    onChange={handleSelectAllDepartments}\n                  />\n                  <label htmlFor=\"select-all-departments\">全选</label>\n                </div>\n                {departments.map(dept => (\n                  <div key={dept} className=\"module6_checkbox-item\">\n                    <input\n                      type=\"checkbox\"\n                      id={`dept-${dept}`}\n                      className=\"module6_custom-checkbox\"\n                      checked={selectedDepartments.includes(dept)}\n                      onChange={() => handleDepartmentChange(dept)}\n                    />\n                    <label htmlFor={`dept-${dept}`}>{dept}</label>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 图表容器 */}\n      <div className=\"module6_chart-container\">\n        {chartType === 'bar' ? (\n          <Bar\n            ref={chartRef}\n            data={barChartData}\n            options={chartOptions}\n            key={`bar-${chartType}-${selectedDepartments.length}`} // 添加key强制重新渲染\n          />\n        ) : (\n          <Line\n            ref={chartRef}\n            data={lineChartData}\n            options={chartOptions}\n            key={`line-${chartType}-${selectedDepartments.length}`} // 添加key强制重新渲染\n          />\n        )}\n      </div>\n\n\n    </div>\n  );\n};\n\nexport default DataVisualization;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,OAAO,CAAEC,MAAM,KAAQ,OAAO,CACnE,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,KACD,UAAU,CACjB,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,OAASC,IAAI,CAAEC,GAAG,KAAQ,iBAAiB,CAC3C,MAAO,iCAAiC,CAExC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAf,OAAO,CAACgB,QAAQ,CACdf,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,CACNC,eACF,CAAC,CAED,KAAM,CAAAQ,iBAAiB,CAAGC,IAAA,EAA2B,IAA1B,CAAEC,iBAAkB,CAAC,CAAAD,IAAA,CAC9C,KAAM,CAACE,aAAa,CAAEC,gBAAgB,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAAE;AAC3D,KAAM,CAAC2B,QAAQ,CAAEC,WAAW,CAAC,CAAG5B,QAAQ,CAAC,WAAW,CAAC,CAAE;AACvD,KAAM,CAAC6B,SAAS,CAAEC,YAAY,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAAE;AACnD,KAAM,CAAC+B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAAE;AACpE,KAAM,CAACiC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAAE;AACrE,KAAM,CAACmC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGpC,QAAQ,CAAC,CAAEqC,GAAG,CAAE,CAAC,CAAEC,IAAI,CAAE,CAAE,CAAC,CAAC,CAAE;AAE/E;AACA,KAAM,CAAAC,QAAQ,CAAGpC,MAAM,CAAC,IAAI,CAAC,CAE7B;AACA,KAAM,CAAAqC,WAAW,CAAG,CAClB,OAAO,CACP,IAAI,CACJ,IAAI,CACJ,MAAM,CACN,MAAM,CACN,IAAI,CACJ,IAAI,CACJ,SAAS,CACT,IAAI,CACJ,KAAK,CACL,OAAO,CACR,CAED;AACA,KAAM,CAAAC,MAAM,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEnD;AACAxC,SAAS,CAAC,IAAM,CACd,GAAI8B,mBAAmB,CAACW,MAAM,GAAK,CAAC,CAAE,CACpCV,sBAAsB,CAAC,CAAC,GAAGQ,WAAW,CAAC,CAAC,CAC1C,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,iBAAiB,CAAIC,KAAK,EAAK,CACnC,KAAM,CAAAC,QAAQ,CAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,GAAK,KAAK,CAAG,KAAK,CAAGC,QAAQ,CAACJ,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CACpFE,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEL,QAAQ,CAAC,CAAE;AAClCnB,gBAAgB,CAACmB,QAAQ,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAM,sBAAsB,CAAIC,UAAU,EAAK,CAC7CpB,sBAAsB,CAACqB,IAAI,EACzBA,IAAI,CAACC,QAAQ,CAACF,UAAU,CAAC,CACrBC,IAAI,CAACE,MAAM,CAACC,IAAI,EAAIA,IAAI,GAAKJ,UAAU,CAAC,CACxC,CAAC,GAAGC,IAAI,CAAED,UAAU,CAC1B,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAK,0BAA0B,CAAIb,KAAK,EAAK,CAC5C,GAAIA,KAAK,CAACE,MAAM,CAACY,OAAO,CAAE,CACxB1B,sBAAsB,CAAC,CAAC,GAAGQ,WAAW,CAAC,CAAC,CAC1C,CAAC,IAAM,CACLR,sBAAsB,CAAC,EAAE,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAA2B,yBAAyB,CAAIC,aAAa,EAAK,CACnD,KAAM,CAAAC,IAAI,CAAGD,aAAa,CAACE,qBAAqB,CAAC,CAAC,CAClD,KAAM,CAAAC,SAAS,CAAGC,MAAM,CAACC,WAAW,EAAIC,QAAQ,CAACC,eAAe,CAACJ,SAAS,CAC1E,KAAM,CAAAK,UAAU,CAAGJ,MAAM,CAACK,WAAW,EAAIH,QAAQ,CAACC,eAAe,CAACC,UAAU,CAE5E,MAAO,CACL/B,GAAG,CAAEwB,IAAI,CAACS,MAAM,CAAGP,SAAS,CAAG,CAAC,CAChCzB,IAAI,CAAEuB,IAAI,CAACvB,IAAI,CAAG8B,UACpB,CAAC,CACH,CAAC,CAED;AACA,KAAM,CAAAG,wBAAwB,CAAI3B,KAAK,EAAK,CAC1C,GAAI,CAACX,kBAAkB,CAAE,CACvB,KAAM,CAAAuC,QAAQ,CAAGb,yBAAyB,CAACf,KAAK,CAAC6B,aAAa,CAAC,CAC/DrC,mBAAmB,CAACoC,QAAQ,CAAC,CAC/B,CACAtC,qBAAqB,CAAC,CAACD,kBAAkB,CAAC,CAC5C,CAAC,CAED;AACAhC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyE,kBAAkB,CAAI9B,KAAK,EAAK,CACpC,GAAI,CAACA,KAAK,CAACE,MAAM,CAAC6B,OAAO,CAAC,kCAAkC,CAAC,CAAE,CAC7DzC,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAA0C,YAAY,CAAIhC,KAAK,EAAK,CAC9B,GAAIA,KAAK,CAACiC,GAAG,GAAK,QAAQ,CAAE,CAC1B3C,qBAAqB,CAAC,KAAK,CAAC,CAC9B,CACF,CAAC,CAEDgC,QAAQ,CAACY,gBAAgB,CAAC,WAAW,CAAEJ,kBAAkB,CAAC,CAC1DR,QAAQ,CAACY,gBAAgB,CAAC,SAAS,CAAEF,YAAY,CAAC,CAElD,MAAO,IAAM,CACXV,QAAQ,CAACa,mBAAmB,CAAC,WAAW,CAAEL,kBAAkB,CAAC,CAC7DR,QAAQ,CAACa,mBAAmB,CAAC,SAAS,CAAEH,YAAY,CAAC,CACvD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA3E,SAAS,CAAC,IAAM,CACd,MAAO,IAAM,CACX,GAAIsC,QAAQ,CAACyC,OAAO,CAAE,CACpBzC,QAAQ,CAACyC,OAAO,CAACC,OAAO,CAAC,CAAC,CAC5B,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,yBAAyB,CAAGhF,OAAO,CAAC,IAAM,CAC9C,KAAM,CAAAiF,MAAM,CAAG,CAAC,CAAC,CAEjB3C,WAAW,CAAC4C,OAAO,CAAC5B,IAAI,EAAI,CAC1B2B,MAAM,CAAC3B,IAAI,CAAC,CAAG,CAAC,CAAC,CACjBf,MAAM,CAAC2C,OAAO,CAACC,KAAK,EAAI,CACtB,GAAI,CAAAC,UAAU,CAAG,CAAC,CAClB,GAAI,CAAAC,OAAO,CAAG,KAAK,CAEnB,GAAI/D,iBAAiB,EAAIA,iBAAiB,CAACgC,IAAI,CAAC,CAAE,CAChD,KAAM,CAAAgC,QAAQ,CAAGhE,iBAAiB,CAACgC,IAAI,CAAC,CAExC;AACA,GAAIgC,QAAQ,CAACC,aAAa,CAAE,CAC1BD,QAAQ,CAACC,aAAa,CAACL,OAAO,CAACM,IAAI,EAAI,CACrC,KAAM,CAAAC,UAAU,IAAAC,MAAA,CAAMP,KAAK,sBAAK,CAChC,GAAIK,IAAI,CAACC,UAAU,CAAC,EAAID,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,GAAK,EAAE,EAAI2C,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,GAAK,IAAI,CAAE,CACxF,KAAM,CAAA8C,KAAK,CAAGC,UAAU,CAACJ,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,CAAC,EAAI,CAAC,CACrDuC,UAAU,EAAIO,KAAK,CACnBN,OAAO,CAAG,IAAI,CAChB,CACF,CAAC,CAAC,CACJ,CAEA;AACA,GAAIC,QAAQ,CAACO,OAAO,CAAE,CACpBP,QAAQ,CAACO,OAAO,CAACX,OAAO,CAACM,IAAI,EAAI,CAC/B,KAAM,CAAAC,UAAU,IAAAC,MAAA,CAAMP,KAAK,sBAAK,CAChC,GAAIK,IAAI,CAACC,UAAU,CAAC,EAAID,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,GAAK,EAAE,EAAI2C,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,GAAK,IAAI,CAAE,CACxF,KAAM,CAAA8C,KAAK,CAAGC,UAAU,CAACJ,IAAI,CAACC,UAAU,CAAC,CAAC5C,KAAK,CAAC,EAAI,CAAC,CACrDuC,UAAU,EAAIO,KAAK,CACnBN,OAAO,CAAG,IAAI,CAChB,CACF,CAAC,CAAC,CACJ,CACF,CAEAJ,MAAM,CAAC3B,IAAI,CAAC,CAAC6B,KAAK,CAAC,CAAGE,OAAO,CAAGD,UAAU,CAAG,CAAC,CAChD,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,CAAAH,MAAM,CACf,CAAC,CAAE,CAAC3D,iBAAiB,CAAC,CAAC,CAEvB,KAAM,CAAAwE,wBAAwB,CAAG9F,OAAO,CAAC,IAAM6B,mBAAmB,CAACW,MAAM,GAAKF,WAAW,CAACE,MAAM,CAAE,CAACX,mBAAmB,CAAES,WAAW,CAAC,CAAC,CAErI;AACAvC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAgG,MAAM,CAAGC,UAAU,CAAC,IAAM,CAC9B,GAAI3D,QAAQ,CAACyC,OAAO,CAAE,CACpBzC,QAAQ,CAACyC,OAAO,CAACmB,MAAM,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAAC,MAAM,CAAGF,UAAU,CAAC,IAAM,CAC9B,GAAI3D,QAAQ,CAACyC,OAAO,CAAE,CACpBzC,QAAQ,CAACyC,OAAO,CAACmB,MAAM,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,GAAG,CAAC,CAEP,KAAM,CAAAE,MAAM,CAAGH,UAAU,CAAC,IAAM,CAC9B,GAAI3D,QAAQ,CAACyC,OAAO,CAAE,CACpBzC,QAAQ,CAACyC,OAAO,CAACmB,MAAM,CAAC,CAAC,CAC3B,CACF,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM,CACXG,YAAY,CAACL,MAAM,CAAC,CACpBK,YAAY,CAACF,MAAM,CAAC,CACpBE,YAAY,CAACD,MAAM,CAAC,CACtB,CAAC,CACH,CAAC,CAAE,CAACxE,SAAS,CAAEE,mBAAmB,CAAEJ,QAAQ,CAAEqE,wBAAwB,CAAC,CAAC,CAExE;AACA,KAAM,CAAAO,0BAA0B,CAAGrG,OAAO,CAAC,IAAM,CAC/C,MAAO,CAAAsC,WAAW,CAACgE,GAAG,CAAChD,IAAI,EAAI,CAC7B,GAAI,CAAA8B,UAAU,CAAG,CAAC,CAElB,GAAI7D,aAAa,GAAK,KAAK,CAAE,CAC3B;AACA6D,UAAU,CAAG7C,MAAM,CAACgE,MAAM,CAAC,CAACC,GAAG,CAAErB,KAAK,GAAK,KAAAsB,qBAAA,CACzC,MAAO,CAAAD,GAAG,EAAI,EAAAC,qBAAA,CAAAzB,yBAAyB,CAAC1B,IAAI,CAAC,UAAAmD,qBAAA,iBAA/BA,qBAAA,CAAkCtB,KAAK,CAAC,GAAI,CAAC,CAAC,CAC9D,CAAC,CAAE,CAAC,CAAC,CACP,CAAC,IAAM,KAAAuB,sBAAA,CACL;AACAtB,UAAU,CAAG,EAAAsB,sBAAA,CAAA1B,yBAAyB,CAAC1B,IAAI,CAAC,UAAAoD,sBAAA,iBAA/BA,sBAAA,CAAkCnF,aAAa,CAAC,GAAI,CAAC,CACpE,CAEA,MAAO,CAAE2B,UAAU,CAAEI,IAAI,CAAEqC,KAAK,CAAEP,UAAW,CAAC,CAChD,CAAC,CAAC,CACJ,CAAC,CAAE,CAACJ,yBAAyB,CAAE1C,WAAW,CAAEC,MAAM,CAAEhB,aAAa,CAAC,CAAC,CAEnE;AACA,KAAM,CAAAoF,iBAAiB,CAAG3G,OAAO,CAAC,IAAM,CACtC,MAAO,CAAAsC,WAAW,CAACgE,GAAG,CAAChD,IAAI,EAAI,CAC3B,KAAM,CAAA8B,UAAU,CAAG7C,MAAM,CAACgE,MAAM,CAAC,CAACC,GAAG,CAAErB,KAAK,GAAK,KAAAyB,sBAAA,CAC7C,MAAO,CAAAJ,GAAG,EAAI,EAAAI,sBAAA,CAAA5B,yBAAyB,CAAC1B,IAAI,CAAC,UAAAsD,sBAAA,iBAA/BA,sBAAA,CAAkCzB,KAAK,CAAC,GAAI,CAAC,CAAC,CAChE,CAAC,CAAE,CAAC,CAAC,CACL,MAAO,CAAEjC,UAAU,CAAEI,IAAI,CAAEqC,KAAK,CAAEP,UAAW,CAAC,CAClD,CAAC,CAAC,CACJ,CAAC,CAAE,CAACJ,yBAAyB,CAAE1C,WAAW,CAAEC,MAAM,CAAC,CAAC,CAEpD;AACA,KAAM,CAAAsE,YAAY,CAAG7G,OAAO,CAAC,IAAM,CACjC,KAAM,CAAA8G,MAAM,CAAG,CACb,wBAAwB,CAAI;AAC5B,wBAAwB,CAAI;AAC5B,yBAAyB,CAAG;AAC5B,wBAAwB,CAAI;AAC5B,wBAAwB,CAAI;AAC5B,wBAAwB,CAAI;AAC5B,wBAAwB,CAAI;AAC5B,yBAAyB,CAAG;AAC5B,yBAAyB,CAAG;AAC5B,wBAAwB,CAAI;AAC5B,yBAA4B;AAAA,CAC7B,CAED,GAAIhB,wBAAwB,CAAE,CAC5B;AACA,KAAM,CAAAiB,UAAU,CAAG,CAAC,GAAGV,0BAA0B,CAAC,CAACW,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAChE,OAAQzF,QAAQ,EACd,IAAK,UAAU,CACb,MAAO,CAAAwF,CAAC,CAACtB,KAAK,CAAGuB,CAAC,CAACvB,KAAK,CAC1B,IAAK,SAAS,CACZ,MAAO,CAAAsB,CAAC,CAAC/D,UAAU,CAACiE,aAAa,CAACD,CAAC,CAAChE,UAAU,CAAC,CACjD,IAAK,WAAW,CAChB,QACE,MAAO,CAAAgE,CAAC,CAACvB,KAAK,CAAGsB,CAAC,CAACtB,KAAK,CAC5B,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAyB,YAAY,CAAG7F,aAAa,GAAK,KAAK,CAAG,SAAS,IAAAmE,MAAA,CAAMnE,aAAa,sBAAK,CAEhF,MAAO,CACL8F,MAAM,CAAEN,UAAU,CAACT,GAAG,CAACd,IAAI,EAAIA,IAAI,CAACtC,UAAU,CAAC,CAC/CoE,QAAQ,CAAE,CACR,CACEC,KAAK,CAAEH,YAAY,CACnBI,IAAI,CAAET,UAAU,CAACT,GAAG,CAACd,IAAI,EAAIA,IAAI,CAACG,KAAK,CAAC,CACxC8B,eAAe,CAAEX,MAAM,CACvBY,WAAW,CAAEZ,MAAM,CAACR,GAAG,CAACqB,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,KAAK,CAAE,GAAG,CAAC,CAAC,CACnDC,WAAW,CAAE,CAAC,CACdC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,KACjB,CAAC,CAEL,CAAC,CACH,CAAC,IAAM,CACL;AACA,GAAIxG,aAAa,GAAK,KAAK,CAAE,CAC3B;AACA,KAAM,CAAA+F,QAAQ,CAAGzF,mBAAmB,CAACyE,GAAG,CAAC,CAAChD,IAAI,CAAE0E,KAAK,IAAM,CACzDT,KAAK,CAAEjE,IAAI,CACXkE,IAAI,CAAEjF,MAAM,CAAC+D,GAAG,CAACnB,KAAK,OAAA8C,sBAAA,OAAI,EAAAA,sBAAA,CAAAjD,yBAAyB,CAAC1B,IAAI,CAAC,UAAA2E,sBAAA,iBAA/BA,sBAAA,CAAkC9C,KAAK,CAAC,GAAI,CAAC,GAAC,CACxEsC,eAAe,CAAEX,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAC9CkF,WAAW,CAAEZ,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAACoF,OAAO,CAAC,KAAK,CAAE,GAAG,CAAC,CAC9DC,WAAW,CAAE,CAAC,CACdC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,KACjB,CAAC,CAAC,CAAC,CACH,MAAO,CACLV,MAAM,CAAE9E,MAAM,CAAC+D,GAAG,CAAC4B,CAAC,KAAAxC,MAAA,CAAOwC,CAAC,UAAG,CAAC,CAChCZ,QACF,CAAC,CACH,CAAC,IAAM,CACL;AACA,KAAM,CAAAA,QAAQ,CAAG,CAAC,CAChBC,KAAK,IAAA7B,MAAA,CAAKnE,aAAa,sBAAK,CAC5BiG,IAAI,CAAE3F,mBAAmB,CAACyE,GAAG,CAAChD,IAAI,OAAA6E,sBAAA,OAAI,EAAAA,sBAAA,CAAAnD,yBAAyB,CAAC1B,IAAI,CAAC,UAAA6E,sBAAA,iBAA/BA,sBAAA,CAAkC5G,aAAa,CAAC,GAAI,CAAC,GAAC,CAC5FkG,eAAe,CAAEX,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAEvG,mBAAmB,CAACW,MAAM,CAAC,CAC5DkF,WAAW,CAAEZ,MAAM,CAACsB,KAAK,CAAC,CAAC,CAAEvG,mBAAmB,CAACW,MAAM,CAAC,CAAC8D,GAAG,CAACqB,CAAC,EAAIA,CAAC,CAACC,OAAO,CAAC,KAAK,CAAE,GAAG,CAAC,CAAC,CACxFC,WAAW,CAAE,CAAC,CACdC,YAAY,CAAE,CAAC,CACfC,aAAa,CAAE,KACjB,CAAC,CAAC,CACF,MAAO,CACLV,MAAM,CAAExF,mBAAmB,CAC3ByF,QACF,CAAC,CACH,CACF,CACF,CAAC,CAAE,CAACxB,wBAAwB,CAAEO,0BAA0B,CAAExE,mBAAmB,CAAEU,MAAM,CAAEyC,yBAAyB,CAAEvD,QAAQ,CAAEF,aAAa,CAAC,CAAC,CAE3I;AACA,KAAM,CAAA8G,aAAa,CAAGrI,OAAO,CAAC,IAAM,CAClC,KAAM,CAAA8G,MAAM,CAAG,CACb,sBAAsB,CAAI;AAC1B,sBAAsB,CAAI;AAC1B,uBAAuB,CAAG;AAC1B,sBAAsB,CAAI;AAC1B,sBAAsB,CAAI;AAC1B,sBAAsB,CAAI;AAC1B,sBAAsB,CAAI;AAC1B,uBAAuB,CAAG;AAC1B,uBAAuB,CAAG;AAC1B,sBAAsB,CAAI;AAC1B,uBAA0B;AAAA,CAC3B,CAED,GAAIvF,aAAa,GAAK,KAAK,CAAE,CAC3B;AACA,KAAM,CAAA+F,QAAQ,CAAGzF,mBAAmB,CAACyE,GAAG,CAAC,CAAChD,IAAI,CAAE0E,KAAK,GAAK,CACxD,MAAO,CACLT,KAAK,CAAEjE,IAAI,CACXkE,IAAI,CAAEjF,MAAM,CAAC+D,GAAG,CAACnB,KAAK,EAAIH,yBAAyB,CAAC1B,IAAI,CAAC,CAAG0B,yBAAyB,CAAC1B,IAAI,CAAC,CAAC6B,KAAK,CAAC,EAAI,CAAC,CAAG,CAAC,CAAC,CAC5GuC,WAAW,CAAEZ,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAC1CiF,eAAe,CAAEX,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAACoF,OAAO,CAAC,IAAI,CAAE,MAAM,CAAC,CACpEU,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,CAAC,CACdC,gBAAgB,CAAE,CAAC,CACnBX,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC,CAEF,MAAO,CACLR,MAAM,CAAE9E,MAAM,CAAC+D,GAAG,CAACnB,KAAK,KAAAO,MAAA,CAAOP,KAAK,UAAG,CAAC,CACxCmC,QACF,CAAC,CACH,CAAC,IAAM,CACL;AACA,KAAM,CAAAA,QAAQ,CAAGzF,mBAAmB,CAACyE,GAAG,CAAC,CAAChD,IAAI,CAAE0E,KAAK,GAAK,CACxD,MAAO,CACLT,KAAK,CAAEjE,IAAI,CACXkE,IAAI,CAAE,CAACxC,yBAAyB,CAAC1B,IAAI,CAAC,CAAG0B,yBAAyB,CAAC1B,IAAI,CAAC,CAAC/B,aAAa,CAAC,EAAI,CAAC,CAAG,CAAC,CAAC,CACjGmG,WAAW,CAAEZ,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAC1CiF,eAAe,CAAEX,MAAM,CAACkB,KAAK,CAAGlB,MAAM,CAACtE,MAAM,CAAC,CAACoF,OAAO,CAAC,IAAI,CAAE,MAAM,CAAC,CACpEU,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,CAAC,CACdC,gBAAgB,CAAE,EAAE,CACpBX,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC,CAEF,MAAO,CACLR,MAAM,CAAE,IAAA3B,MAAA,CAAInE,aAAa,WAAI,CAC7B+F,QACF,CAAC,CACH,CACF,CAAC,CAAE,CAACtC,yBAAyB,CAAEnD,mBAAmB,CAAEN,aAAa,CAAEgB,MAAM,CAAC,CAAC,CAE3E;AACA,KAAM,CAAAkG,YAAY,CAAGzI,OAAO,CAAC,IAAM,CACjC,KAAM,CAAA0I,qBAAqB,CAAG/G,SAAS,GAAK,KAAK,EAAImE,wBAAwB,CAC7E,KAAM,CAAA6C,sBAAsB,CAAGhH,SAAS,GAAK,KAAK,EAAI,CAACmE,wBAAwB,CAE/E,GAAI,CAAA8C,SAAS,CAAG,aAAa,CAC7B,GAAI,CAAAC,MAAM,CAAG,IAAI,CACjB,GAAI,CAAAC,MAAM,CAAG,IAAI,CAEjB,GAAIJ,qBAAqB,CAAE,CACzB,GAAInH,aAAa,GAAK,KAAK,CAAE,CAC3BqH,SAAS,CAAG,cAAc,CAC1BC,MAAM,CAAG,IAAI,CACbC,MAAM,CAAG,SAAS,CACpB,CAAC,IAAM,CACLF,SAAS,sBAAAlD,MAAA,CAASnE,aAAa,kCAAO,CACtCsH,MAAM,CAAG,IAAI,CACbC,MAAM,IAAApD,MAAA,CAAMnE,aAAa,sBAAK,CAChC,CACF,CAAC,IAAM,IAAIoH,sBAAsB,CAAE,CACjC,GAAIpH,aAAa,GAAK,KAAK,CAAE,CAC3BqH,SAAS,CAAG,YAAY,CACxBC,MAAM,CAAG,IAAI,CACbC,MAAM,CAAG,IAAI,CACf,CAAC,IAAM,CACLF,SAAS,4BAAAlD,MAAA,CAAUnE,aAAa,kCAAO,CACvCsH,MAAM,CAAG,IAAI,CACbC,MAAM,IAAApD,MAAA,CAAMnE,aAAa,sBAAK,CAChC,CACF,CAAC,IAAM,IAAII,SAAS,GAAK,MAAM,CAAE,CAC/B,GAAIJ,aAAa,GAAK,KAAK,CAAE,CAC3BqH,SAAS,CAAG,YAAY,CACxBC,MAAM,CAAG,IAAI,CACbC,MAAM,CAAG,IAAI,CACf,CAAC,IAAM,CACLF,SAAS,4BAAAlD,MAAA,CAAUnE,aAAa,sBAAK,CACrCsH,MAAM,CAAG,IAAI,CACbC,MAAM,IAAApD,MAAA,CAAMnE,aAAa,sBAAK,CAChC,CACF,CAEA,MAAO,CACLwH,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAAE;AAC5BC,WAAW,CAAE,CAAC,CAAE;AAChBC,WAAW,CAAEC,SAAS,CAAE;AACxBC,WAAW,CAAE,CACXC,SAAS,CAAE,KAAK,CAChBC,IAAI,CAAE,OACR,CAAC,CACDC,MAAM,CAAE,CACNC,OAAO,CAAE,CACPrH,GAAG,CAAE,CAAC,CACNiC,MAAM,CAAE,CAAC,CACThC,IAAI,CAAE,CAAC,CACPqH,KAAK,CAAE,CACT,CACF,CAAC,CACDC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNrF,QAAQ,CAAE,KAAK,CACf+C,MAAM,CAAE,CACNuC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBACV,CACF,CACF,CAAC,CACDC,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAEtB,SAAS,CACfgB,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBAAsB,CAC9BI,MAAM,CAAE,MACV,CAAC,CACDC,KAAK,CAAE,QAAQ,CAAE;AACjB9F,QAAQ,CAAE,KAAM;AAClB,CAAC,CACD+F,OAAO,CAAE,CACP5C,eAAe,CAAE,oBAAoB,CACrC6C,UAAU,CAAE,SAAS,CACrBC,SAAS,CAAE,SAAS,CACpB7C,WAAW,CAAE,SAAS,CACtBG,WAAW,CAAE,CAAC,CACd2C,SAAS,CAAE,CACTjD,KAAK,CAAE,QAAAA,CAASkD,OAAO,CAAE,CACvB,SAAA/E,MAAA,CAAU+E,OAAO,CAACC,OAAO,CAACnD,KAAK,OAAA7B,MAAA,CAAK+E,OAAO,CAACE,MAAM,CAACC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC,WACjE,CACF,CACF,CAAC,CACDC,UAAU,CAAE,CACVb,OAAO,CAAE,IAAI,CACbL,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBAAsB,CAC9BI,MAAM,CAAE,MACV,CAAC,CACDY,SAAS,CAAE,QAAAA,CAASlI,KAAK,CAAE,CACzB,MAAO,CAAAA,KAAK,CAACgI,OAAO,CAAC,CAAC,CAAC,CACzB,CAAC,CACDG,MAAM,CAAErJ,SAAS,GAAK,KAAK,CAAG,KAAK,CAAG,KAAK,CAC3CyI,KAAK,CAAEzI,SAAS,GAAK,KAAK,CAAG,KAAK,CAAG,KAAK,CAC1CsJ,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,oBAAoB,CACrCC,cAAc,CAAE,CAClB,CACF,CAAC,CACDC,MAAM,CAAE,CACNC,CAAC,CAAE,CACDrB,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAErB,MAAM,CACZe,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBAAsB,CAC9BI,MAAM,CAAE,MACV,CACF,CAAC,CACDmB,KAAK,CAAE,CACL1B,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBACV,CACF,CAAC,CACDwB,IAAI,CAAE,CACJ3B,KAAK,CAAE,0BACT,CACF,CAAC,CACDgB,CAAC,CAAE,CACDZ,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAEpB,MAAM,CACZc,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBAAsB,CAC9BI,MAAM,CAAE,MACV,CACF,CAAC,CACDmB,KAAK,CAAE,CACL1B,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CAAE;AACVC,MAAM,CAAE,sBACV,CACF,CAAC,CACDwB,IAAI,CAAE,CACJ3B,KAAK,CAAE,0BACT,CAAC,CACD;AACA4B,WAAW,CAAE,KAAK,CAAE;AACpBC,KAAK,CAAE,IAAK;AACd,CACF,CACF,CAAC,CACH,CAAC,CAAE,CAAC9J,SAAS,CAAEmE,wBAAwB,CAAErE,QAAQ,CAAEF,aAAa,CAAC,CAAC,CAElE,mBACEL,KAAA,QAAKwK,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eAEzCzK,KAAA,QAAKwK,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7CzK,KAAA,QAAKwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3K,IAAA,UAAO0K,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAO,CAAC,cACtDzK,KAAA,WACE2B,KAAK,CAAEtB,aAAc,CACrBqK,QAAQ,CAAEnJ,iBAAkB,CAC5BiJ,SAAS,CAAC,sBAAsB,CAChCG,QAAQ,CAAE,KAAM,CAChB7B,KAAK,CAAC,8DAAY,CAAA2B,QAAA,eAElB3K,IAAA,WAAQ6B,KAAK,CAAC,KAAK,CAAA8I,QAAA,CAAC,0BAAI,CAAQ,CAAC,CAChCpJ,MAAM,CAAC+D,GAAG,CAACnB,KAAK,eACfjE,KAAA,WAAoB2B,KAAK,CAAEsC,KAAM,CAAAwG,QAAA,EAAExG,KAAK,CAAC,QAAC,GAA7BA,KAAqC,CACnD,CAAC,EACI,CAAC,EACN,CAAC,cAENjE,KAAA,QAAKwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3K,IAAA,UAAO0K,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAO,CAAC,cACtDzK,KAAA,WACE2B,KAAK,CAAElB,SAAU,CACjBiK,QAAQ,CAAGE,CAAC,EAAKlK,YAAY,CAACkK,CAAC,CAAClJ,MAAM,CAACC,KAAK,CAAE,CAC9C6I,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eAErC3K,IAAA,WAAQ6B,KAAK,CAAC,KAAK,CAAA8I,QAAA,CAAC,wDAAS,CAAQ,CAAC,cACtC3K,IAAA,WAAQ6B,KAAK,CAAC,MAAM,CAAA8I,QAAA,CAAC,wDAAS,CAAQ,CAAC,EACjC,CAAC,EACN,CAAC,cAENzK,KAAA,QAAKwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3K,IAAA,UAAO0K,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAO,CAAC,cACtDzK,KAAA,WACE2B,KAAK,CAAEpB,QAAS,CAChBmK,QAAQ,CAAGE,CAAC,EAAKpK,WAAW,CAACoK,CAAC,CAAClJ,MAAM,CAACC,KAAK,CAAE,CAC7C6I,SAAS,CAAC,qBAAqB,CAC/BG,QAAQ,CAAElK,SAAS,GAAK,MAAM,EAAKA,SAAS,GAAK,KAAK,EAAI,CAACmE,wBAA0B,CAAA6F,QAAA,eAErF3K,IAAA,WAAQ6B,KAAK,CAAC,WAAW,CAAA8I,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACvC3K,IAAA,WAAQ6B,KAAK,CAAC,UAAU,CAAA8I,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACtC3K,IAAA,WAAQ6B,KAAK,CAAC,SAAS,CAAA8I,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC/B,CAAC,EACN,CAAC,cAENzK,KAAA,QAAKwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3K,IAAA,UAAO0K,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,gCAAK,CAAO,CAAC,cACtDzK,KAAA,QAAKwK,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CzK,KAAA,WACEwK,SAAS,CAAC,2BAA2B,CACrCK,OAAO,CAAE1H,wBAAyB,CAAAsH,QAAA,EAEjC9J,mBAAmB,CAACW,MAAM,GAAKF,WAAW,CAACE,MAAM,CAC9C,KAAK,CACLX,mBAAmB,CAACW,MAAM,GAAK,CAAC,CAChC,KAAK,uBAAAkD,MAAA,CACE7D,mBAAmB,CAACW,MAAM,WAAI,cACzCxB,IAAA,SAAM0K,SAAS,2BAAAhG,MAAA,CAA4B3D,kBAAkB,CAAG,MAAM,CAAG,EAAE,CAAG,CAAA4J,QAAA,CAAC,QAAC,CAAM,CAAC,EACjF,CAAC,CACR5J,kBAAkB,eACjBb,KAAA,QACEwK,SAAS,CAAC,4BAA4B,CACtCM,KAAK,CAAE,CACL7J,GAAG,IAAAuD,MAAA,CAAKzD,gBAAgB,CAACE,GAAG,MAAI,CAChCC,IAAI,IAAAsD,MAAA,CAAKzD,gBAAgB,CAACG,IAAI,MAChC,CAAE,CAAAuJ,QAAA,eAEFzK,KAAA,QAAKwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC3K,IAAA,UACEiL,IAAI,CAAC,UAAU,CACfC,EAAE,CAAC,wBAAwB,CAC3BR,SAAS,CAAC,yBAAyB,CACnClI,OAAO,CAAE3B,mBAAmB,CAACW,MAAM,GAAKF,WAAW,CAACE,MAAO,CAC3DoJ,QAAQ,CAAErI,0BAA2B,CACtC,CAAC,cACFvC,IAAA,UAAOmL,OAAO,CAAC,wBAAwB,CAAAR,QAAA,CAAC,cAAE,CAAO,CAAC,EAC/C,CAAC,CACLrJ,WAAW,CAACgE,GAAG,CAAChD,IAAI,eACnBpC,KAAA,QAAgBwK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eAC/C3K,IAAA,UACEiL,IAAI,CAAC,UAAU,CACfC,EAAE,SAAAxG,MAAA,CAAUpC,IAAI,CAAG,CACnBoI,SAAS,CAAC,yBAAyB,CACnClI,OAAO,CAAE3B,mBAAmB,CAACuB,QAAQ,CAACE,IAAI,CAAE,CAC5CsI,QAAQ,CAAEA,CAAA,GAAM3I,sBAAsB,CAACK,IAAI,CAAE,CAC9C,CAAC,cACFtC,IAAA,UAAOmL,OAAO,SAAAzG,MAAA,CAAUpC,IAAI,CAAG,CAAAqI,QAAA,CAAErI,IAAI,CAAQ,CAAC,GARtCA,IASL,CACN,CAAC,EACC,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAGNtC,IAAA,QAAK0K,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CACrChK,SAAS,GAAK,KAAK,cAClBX,IAAA,CAACF,GAAG,EACFsL,GAAG,CAAE/J,QAAS,CACdmF,IAAI,CAAEX,YAAa,CACnBwF,OAAO,CAAE5D,YAAa,SAAA/C,MAAA,CACV/D,SAAS,MAAA+D,MAAA,CAAI7D,mBAAmB,CAACW,MAAM,CACpD,CAAC,cAEFxB,IAAA,CAACH,IAAI,EACHuL,GAAG,CAAE/J,QAAS,CACdmF,IAAI,CAAEa,aAAc,CACpBgE,OAAO,CAAE5D,YAAa,UAAA/C,MAAA,CACT/D,SAAS,MAAA+D,MAAA,CAAI7D,mBAAmB,CAACW,MAAM,CACrD,CACF,CACE,CAAC,EAGH,CAAC,CAEV,CAAC,CAED,cAAe,CAAApB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}