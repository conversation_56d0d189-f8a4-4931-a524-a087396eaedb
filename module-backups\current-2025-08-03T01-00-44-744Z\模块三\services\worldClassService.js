class WorldClassService {
  constructor() {
    this.baseURL = 'http://localhost:3001';
    this.data = [];
    this.lastModified = null;
  }

  // 设置同步回调（复用模块一和模块二的设计）
  setSyncCallbacks(onSuccess, onError) {
    this.onSyncSuccess = onSuccess;
    this.onSyncError = onError;
  }

  // 加载对标世界一流数据
  async loadWorldClassData() {
    try {
      console.log('正在从API加载对标世界一流数据...');
      const response = await fetch(`${this.baseURL}/api/world-class-data`);
      
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          this.data = result.data;
          this.lastModified = new Date(result.lastModified);
          console.log('从Excel文件成功加载数据:', this.data);
          return this.data;
        } else {
          console.warn('API返回的数据格式不正确');
          return this.getMockData();
        }
      } else {
        console.warn('API响应失败，状态码:', response.status);
        return this.getMockData();
      }
    } catch (error) {
      console.error('从API加载对标世界一流数据失败:', error);
      return this.getMockData();
    }
  }

  // 获取模拟数据（按照提示词要求的数据结构）
  getMockData() {
    console.log('使用模拟数据');
    this.data = [
      {
        序号: 1,
        '相关指标或方向（一级）': '提升平台的效率或效益',
        '相关指标或方向（二级）': '优化业务流程',
        '相关指标或方向（三级）': '数字化转型',
        '2025年目标': '完成业务流程数字化转型，提升效率30%',
        '2025年举措': '实施ERP系统集成，建立数据分析平台，优化审批流程',
        负责人: '技术部',
        权重: 15,
        跟踪频次: '月度',
        '2月工作计划': '完成需求调研和系统设计',
        '2月完成情况': '已完成需求调研，系统设计进行中',
        '3月工作计划': '开发核心功能模块',
        '3月完成情况': '核心模块开发完成80%',
        '4月工作计划': '系统集成和测试',
        '4月完成情况': '集成测试通过，用户验收中',
        '5月工作计划': '用户培训和系统上线',
        '5月完成情况': '',
        '6月工作计划': '系统优化和性能调整',
        '6月完成情况': '',
        '7月工作计划': '数据迁移和验证',
        '7月完成情况': '',
        '8月工作计划': '全面推广应用',
        '8月完成情况': '',
        '9月工作计划': '效果评估和改进',
        '9月完成情况': '',
        '10月工作计划': '制定标准化流程',
        '10月完成情况': '',
        '11月工作计划': '培训推广和经验总结',
        '11月完成情况': '',
        '12月工作计划': '年度总结和下年规划',
        '12月完成情况': '',
        '1月工作计划': '新年度计划制定',
        '1月完成情况': '',
        备注: '重点项目，需要跨部门协作'
      },
      {
        序号: 2,
        '相关指标或方向（一级）': '提升平台的效率或效益',
        '相关指标或方向（二级）': '技术创新',
        '相关指标或方向（三级）': '智能化升级',
        '2025年目标': '建立智能化生产管理系统，降低成本20%',
        '2025年举措': '引入AI算法，建设智能工厂，实施预测性维护',
        负责人: '生产部',
        权重: 20,
        跟踪频次: '月度',
        '2月工作计划': 'AI算法选型和测试',
        '2月完成情况': '完成3种算法对比测试',
        '3月工作计划': '智能工厂方案设计',
        '3月完成情况': '方案设计完成并通过评审',
        '4月工作计划': '设备采购和安装',
        '4月完成情况': '',
        '5月工作计划': '系统集成和调试',
        '5月完成情况': '',
        '6月工作计划': '试运行和优化',
        '6月完成情况': '',
        '7月工作计划': '正式投产运行',
        '7月完成情况': '',
        '8月工作计划': '效果监测和分析',
        '8月完成情况': '',
        '9月工作计划': '系统优化改进',
        '9月完成情况': '',
        '10月工作计划': '扩大应用范围',
        '10月完成情况': '',
        '11月工作计划': '培训和推广',
        '11月完成情况': '',
        '12月工作计划': '年度效果评估',
        '12月完成情况': '',
        '1月工作计划': '制定扩展计划',
        '1月完成情况': '',
        备注: '核心技术项目'
      },
      {
        序号: 3,
        '相关指标或方向（一级）': '新产品、新平台、新技术开发',
        '相关指标或方向（二级）': '产品创新',
        '相关指标或方向（三级）': '新材料研发',
        '2025年目标': '开发3款新材料产品，申请5项专利',
        '2025年举措': '建立新材料实验室，与高校合作研发，建立产业化生产线',
        负责人: '研发中心',
        权重: 25,
        跟踪频次: '季度',
        '2月工作计划': '实验室建设和设备采购',
        '2月完成情况': '实验室建设完成60%',
        '3月工作计划': '与高校签署合作协议',
        '3月完成情况': '已与3所高校签署合作协议',
        '4月工作计划': '第一款新材料研发',
        '4月完成情况': '',
        '5月工作计划': '产品性能测试',
        '5月完成情况': '',
        '6月工作计划': '第二款新材料研发',
        '6月完成情况': '',
        '7月工作计划': '专利申请准备',
        '7月完成情况': '',
        '8月工作计划': '第三款新材料研发',
        '8月完成情况': '',
        '9月工作计划': '产业化可行性分析',
        '9月完成情况': '',
        '10月工作计划': '生产线设计',
        '10月完成情况': '',
        '11月工作计划': '设备调试和试产',
        '11月完成情况': '',
        '12月工作计划': '产品验证和改进',
        '12月完成情况': '',
        '1月工作计划': '市场推广准备',
        '1月完成情况': '',
        备注: '创新驱动发展项目'
      },
      {
        序号: 4,
        '相关指标或方向（一级）': '新产品、新平台、新技术开发',
        '相关指标或方向（二级）': '平台建设',
        '相关指标或方向（三级）': '数据平台',
        '2025年目标': '建设统一数据平台，实现数据共享和分析',
        '2025年举措': '搭建大数据平台，建立数据标准，实施数据治理',
        负责人: '信息中心',
        权重: 18,
        跟踪频次: '月度',
        '2月工作计划': '数据平台架构设计',
        '2月完成情况': '架构设计完成并通过技术评审',
        '3月工作计划': '基础设施建设',
        '3月完成情况': '服务器采购安装完成',
        '4月工作计划': '数据接入和清洗',
        '4月完成情况': '',
        '5月工作计划': '数据分析工具开发',
        '5月完成情况': '',
        '6月工作计划': '用户界面设计',
        '6月完成情况': '',
        '7月工作计划': '系统测试和优化',
        '7月完成情况': '',
        '8月工作计划': '用户培训和推广',
        '8月完成情况': '',
        '9月工作计划': '数据治理规范制定',
        '9月完成情况': '',
        '10月工作计划': '平台功能扩展',
        '10月完成情况': '',
        '11月工作计划': '数据安全加固',
        '11月完成情况': '',
        '12月工作计划': '年度数据分析报告',
        '12月完成情况': '',
        '1月工作计划': '平台升级规划',
        '1月完成情况': '',
        备注: '基础支撑平台'
      },
      {
        序号: 5,
        '相关指标或方向（一级）': '质量管理体系优化',
        '相关指标或方向（二级）': '质量标准',
        '相关指标或方向（三级）': 'ISO认证',
        '2025年目标': '通过ISO 9001:2015质量管理体系认证',
        '2025年举措': '建立质量管理体系，培训质量管理人员，实施质量改进',
        负责人: '质量部',
        权重: 12,
        跟踪频次: '季度',
        '2月工作计划': '质量体系文件编制',
        '2月完成情况': '完成质量手册和程序文件编制',
        '3月工作计划': '质量管理培训',
        '3月完成情况': '培训质量管理人员50人次',
        '4月工作计划': '质量体系试运行',
        '4月完成情况': '',
        '5月工作计划': '内部质量审核',
        '5月完成情况': '',
        '6月工作计划': '管理评审',
        '6月完成情况': '',
        '7月工作计划': '纠正预防措施实施',
        '7月完成情况': '',
        '8月工作计划': '认证机构审核准备',
        '8月完成情况': '',
        '9月工作计划': '第三方审核',
        '9月完成情况': '',
        '10月工作计划': '不符合项整改',
        '10月完成情况': '',
        '11月工作计划': '认证证书获取',
        '11月完成情况': '',
        '12月工作计划': '质量体系持续改进',
        '12月完成情况': '',
        '1月工作计划': '年度质量目标制定',
        '1月完成情况': '',
        备注: '质量保障基础工作'
      }
    ];
    
    return this.data;
  }

  // 更新数据（双向同步到Excel）
  async updateData(rowIndex, field, value) {
    try {
      console.log('正在同步对标世界一流数据到Excel:', { rowIndex, field, value });
      
      // 更新内存中的数据
      if (this.data[rowIndex]) {
        this.data[rowIndex][field] = value;
      }
      
      // 调用API同步到Excel
      const response = await fetch(`${this.baseURL}/api/update-world-class-excel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rowIndex,
          field,
          value
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('对标世界一流数据同步成功');
          this.onSyncSuccess && this.onSyncSuccess();
          return true;
        } else {
          console.error('对标世界一流数据同步失败:', result.message);
          this.onSyncError && this.onSyncError(result.message);
          return false;
        }
      } else {
        console.error('对标世界一流数据同步请求失败，状态码:', response.status);
        this.onSyncError && this.onSyncError(`HTTP ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('对标世界一流数据同步错误:', error);
      this.onSyncError && this.onSyncError(error.message);
      return false;
    }
  }

  // 获取层级选项
  getLevelOptions(level) {
    if (!this.data || this.data.length === 0) return [];
    
    const field = level === '一级' ? '相关指标或方向（一级）' : 
                  level === '二级' ? '相关指标或方向（二级）' : 
                  '相关指标或方向（三级）';
    
    const options = [...new Set(this.data
      .map(item => item[field])
      .filter(val => val && String(val).trim() !== '')
      .map(val => String(val))
    )];
    
    return options;
  }

  // 按层级筛选数据
  filterByLevel(level, value) {
    if (!this.data || this.data.length === 0) return [];
    
    if (value === '全部') return this.data;
    
    const field = level === '一级' ? '相关指标或方向（一级）' : 
                  level === '二级' ? '相关指标或方向（二级）' : 
                  '相关指标或方向（三级）';

    return this.data.filter(item => 
      String(item[field] || '').trim() === value
    );
  }

  // 获取月份字段
  getMonthFields() {
    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月', '1月'];
    const monthFields = [];
    
    months.forEach(month => {
      monthFields.push(`${month}工作计划`);
      monthFields.push(`${month}完成情况`);
    });
    
    return monthFields;
  }

  // 数据统计
  getDataStats() {
    if (!this.data || this.data.length === 0) {
      return {
        totalItems: 0,
        levelCounts: { 一级: 0, 二级: 0, 三级: 0 },
        totalWeight: 0,
        avgWeight: 0
      };
    }

    const levelCounts = { 一级: 0, 二级: 0, 三级: 0 };
    let totalWeight = 0;
    
    this.data.forEach(item => {
      // 统计各层级数据量
      if (item['相关指标或方向（一级）']) levelCounts.一级++;
      if (item['相关指标或方向（二级）']) levelCounts.二级++;
      if (item['相关指标或方向（三级）']) levelCounts.三级++;
      
      // 统计权重
      const weight = item.权重;
      if (weight && !isNaN(Number(weight))) {
        totalWeight += Number(weight);
      }
    });

    return {
      totalItems: this.data.length,
      levelCounts,
      totalWeight,
      avgWeight: this.data.length > 0 ? (totalWeight / this.data.length).toFixed(1) : 0
    };
  }

  // 数据验证（提示词要求的数据验证机制）
  async validateData() {
    try {
      const response = await fetch(`${this.baseURL}/api/validate-world-class-data`);
      
      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        return { success: false, message: '数据验证失败' };
      }
    } catch (error) {
      console.error('数据验证错误:', error);
      return { success: false, message: error.message };
    }
  }

  // 导出数据
  async exportData(options = {}) {
    try {
      const response = await fetch(`${this.baseURL}/api/export-world-class-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options)
      });

      if (response.ok) {
        return response.blob();
      } else {
        throw new Error('导出数据失败');
      }
    } catch (error) {
      console.error('导出数据错误:', error);
      throw error;
    }
  }

  // 获取数据
  getData() {
    return this.data;
  }

  // 检查数据是否已加载
  isDataLoaded() {
    return this.data && this.data.length > 0;
  }

  // 获取最后修改时间
  getLastModified() {
    return this.lastModified;
  }
}

export default new WorldClassService(); 