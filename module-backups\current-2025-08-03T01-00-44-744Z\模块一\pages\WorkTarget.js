import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../styles/WorkTarget.css';
import excelService from '../services/excelService';
import SelectiveDownloadModal from '../components/SelectiveDownloadModal';
import downloadService from '../services/downloadService';

const WorkTarget = ({ onNavigate }) => {
  const [activeSection, setActiveSection] = useState(null);
  const [data, setData] = useState({
    keyIndicators: [],
    qualityIndicators: [],
    keyWork: []
  });
  const [loading, setLoading] = useState(true);
  const [editingCell, setEditingCell] = useState(null);
  const [dataStats, setDataStats] = useState({});
  const [syncStatus, setSyncStatus] = useState('已同步');

  // 选择性下载相关状态
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  // 数据输入优化相关状态
  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值
  const saveTimeoutRef = useRef(null);
  const pendingSaveRef = useRef(null);

  useEffect(() => {
    // 初始化数据加载
    loadData();

    // 设置同步回调
    excelService.setSyncCallbacks(
      () => setSyncStatus('同步成功'),
      (error) => setSyncStatus('同步失败')
    );

    // 清理函数
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }
    };
  }, []);

  // 强制刷新数据的方法
  const forceRefresh = async () => {
    console.log('强制刷新数据...');
    setSyncStatus('刷新中...');
    await loadData();
    setSyncStatus('刷新完成');
  };

  const loadData = async () => {
    setLoading(true);
    try {
      // 强制重新加载数据
      console.log('开始加载数据...');
      const excelData = await excelService.loadExcel();
      console.log('加载的数据:', excelData);
      
      if (excelData) {
        setData(excelData);
        setDataStats(excelService.getDataStats());
        console.log('数据设置完成 - keyWork:', excelData.keyWork?.length || 0, '项');
      } else {
        console.error('未获取到数据');
      }
    } catch (error) {
      console.error('数据加载失败:', error);
    }
    setLoading(false);
  };

  const sections = [
    {
      key: 'keyIndicators',
      title: '关键指标（40分）',
      color: '#20ff4d',
      icon: '🎯'
    },
    {
      key: 'qualityIndicators',
      title: '质量指标（20分）',
      color: '#00d4aa',
      icon: '⭐'
    },
    {
      key: 'keyWork',
      title: '重点工作（40分）',
      color: '#4dd0ff',
      icon: '🚀'
    }
  ];

  const handleSectionClick = (sectionKey) => {
    setActiveSection(activeSection === sectionKey ? null : sectionKey);
  };

  // 防抖保存函数
  const debouncedSave = useCallback(async (section, rowIndex, field, value) => {
    try {
      setSyncStatus('同步中...');

      // 取消之前的保存操作
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }

      // 创建新的保存操作
      const controller = new AbortController();
      pendingSaveRef.current = controller;

      // 调用双向同步
      await excelService.updateData(section, rowIndex, field, value);

      // 如果没有被取消，更新状态
      if (!controller.signal.aborted) {
        setSyncStatus('同步成功');
        setTimeout(() => setSyncStatus('已同步'), 1000);
        pendingSaveRef.current = null;
      }
    } catch (error) {
      if (!error.name === 'AbortError') {
        console.error('保存失败:', error);
        setSyncStatus('同步失败');
      }
    }
  }, []);

  // 处理输入变化（实时更新UI，延迟保存）
  const handleInputChange = (section, rowIndex, field, value) => {
    // 立即更新UI显示
    const newData = { ...data };
    newData[section][rowIndex][field] = value;
    setData(newData);

    // 存储临时值
    const key = `${section}-${rowIndex}-${field}`;
    setTempValues(prev => ({ ...prev, [key]: value }));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的防抖定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(section, rowIndex, field, value);
    }, 800); // 800ms防抖延迟
  };

  // 处理失焦保存（立即保存）
  const handleBlurSave = async (section, rowIndex, field) => {
    const key = `${section}-${rowIndex}-${field}`;
    const value = tempValues[key];

    if (value !== undefined) {
      // 清除防抖定时器
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      // 立即保存
      await debouncedSave(section, rowIndex, field, value);

      // 清除临时值
      setTempValues(prev => {
        const newTemp = { ...prev };
        delete newTemp[key];
        return newTemp;
      });
    }
  };

  // 兼容原有的handleCellEdit接口
  const handleCellEdit = async (section, rowIndex, field, value) => {
    handleInputChange(section, rowIndex, field, value);
  };

  const startEdit = (section, rowIndex, field) => {
    setEditingCell(`${section}-${rowIndex}-${field}`);
  };

  const finishEdit = () => {
    setEditingCell(null);
  };

  // 处理选择性下载
  const handleDownload = async (selectionData) => {
    setDownloadLoading(true);
    try {
      const result = await downloadService.handleSelectiveDownload(selectionData);
      if (result.success) {
        console.log('下载成功:', result.message);
        // 可以在这里添加成功提示
        alert(result.message);
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败: ' + error.message);
    } finally {
      setDownloadLoading(false);
      setShowDownloadModal(false);
    }
  };

  // 打开下载模态框
  const openDownloadModal = () => {
    setShowDownloadModal(true);
  };

  // 关闭下载模态框
  const closeDownloadModal = () => {
    setShowDownloadModal(false);
  };

  const renderTable = (sectionKey) => {
    const sectionData = data[sectionKey] || [];
    console.log(`渲染${sectionKey}数据:`, sectionData);
    
    if (sectionData.length === 0) {
      return (
        <div className="no-data">
          <p>此部分暂无数据</p>
          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '10px' }}>
            调试信息: {sectionKey} 数据长度为 {sectionData.length}
          </p>
        </div>
      );
    }

    const totalWeight = sectionData.reduce((sum, item) => {
      const weight = item.权重;
      // 处理空字符串权重的情况
      if (weight === '' || weight === null || weight === undefined) return sum;
      return sum + (typeof weight === 'number' ? weight : (weight ? Number(weight) || 0 : 0));
    }, 0);

    return (
      <div className="data-table-container">
        <div className="table-summary">
          <span>
            {sectionKey === 'keyWork' ? 
              `工作项目: ${sectionData.length} 项` : 
              `指标数量: ${sectionData.filter(row => !row.isMergedCell || (row.isMergedCell && row.目标值)).length} 项`
            }
          </span>
          
          {/* 重点工作说明 - 放在中间位置 */}
          {sectionKey === 'keyWork' && (
            <span 
              className="key-work-notice-inline"
              style={{ fontSize: '16px' }}
            >
              淡橙色标识为新材级重点工作
            </span>
          )}
          
          {totalWeight > 0 && <span style={{ fontSize: '16px' }}>权重: {totalWeight} 分</span>}
        </div>
        
        <table className="data-table">
          <thead>
            <tr>
              <th className="col-number" style={{ fontSize: '15px' }}>序号</th>
              <th className="col-indicator" style={{ fontSize: '15px' }}>指标</th>
              <th className="col-target" style={{ fontSize: '15px' }}>目标值</th>
              <th className="col-weight" style={{ fontSize: '15px' }}>权重（分）</th>
              <th className="col-standard" style={{ fontSize: '15px' }}>考核标准</th>
              <th className="col-category" style={{ fontSize: '15px' }}>指标分类</th>
              <th className="col-responsible" style={{ fontSize: '15px' }}>责任人</th>
            </tr>
          </thead>
          <tbody>
            {sectionData.map((row, index) => {
              const isMergedStart = row.isMergedStart;
              const isMergedCell = row.isMergedCell;
              const rowSpan = row.rowSpan || 1;
              
              // 判断是否为新材级重点工作（序号1和序号19的所有相关行）
              const isHighlightWork = sectionKey === 'keyWork' && (row.序号 === 1 || row.序号 === 19);
              
              return (
                <tr key={index} className={`data-row ${isMergedCell ? 'merged-row' : ''} ${isMergedStart ? 'merged-start-row' : ''} ${isHighlightWork ? 'highlight-work' : ''}`}>
                  {Object.entries(row).filter(([field]) => !['rowSpan', 'isMergedStart', 'isMergedCell'].includes(field)).map(([field, value]) => {
                    const cellKey = `${sectionKey}-${index}-${field}`;
                    const isEditing = editingCell === cellKey;
                    const isEditable = field !== '序号';

                    // 处理合并单元格
                    if (isMergedCell && (field === '序号' || field === '指标')) {
                      return null; 
                    }

                    let cellProps = {
                      key: field,
                      className: `data-cell col-${field.toLowerCase()} ${isEditable ? 'editable' : ''} ${isHighlightWork ? 'highlight-cell' : ''}`
                    };

                    if (isMergedStart && (field === '序号' || field === '指标')) {
                      cellProps.rowSpan = rowSpan;
                      cellProps.className += ' merged-cell-content';
                      if (isHighlightWork) {
                        cellProps.className += ' highlight-merged-cell';
                      }
                    }
                    
                    // 安全地提取和处理内容 - 处理可能的对象值
                    let contentToRender = value;
                    
                    // 处理对象类型的值（如 {font, text} 或 {richText}）
                    if (typeof value === 'object' && value !== null) {
                      if (value.text !== undefined) {
                        contentToRender = value.text;
                      } else if (value.richText !== undefined) {
                        contentToRender = value.richText;
                      } else if (value.value !== undefined) {
                        contentToRender = value.value;
                      } else {
                        contentToRender = '';
                      }
                    }
                    
                    // 确保 contentToRender 是字符串
                    contentToRender = String(contentToRender || '');
                    
                    // 处理包含换行符的文本
                    if (contentToRender.includes('\n')) {
                        contentToRender = contentToRender.split('\n').map((line, i, arr) => (
                            <React.Fragment key={i}>
                                {line}
                                {i < arr.length - 1 && <br />}
                            </React.Fragment>
                        ));
                    } else {
                        // 对于权重字段，只有当值不为空且不是空字符串时才添加单位
                        if (field === '权重' && contentToRender && contentToRender.trim() !== '') {
                            contentToRender = `${contentToRender} 分`;
                        }
                    }

                    return (
                      <td {...cellProps}>
                        {isEditing ? (
                          <textarea
                            value={typeof value === 'object' && value !== null ?
                              (value.text || value.richText || value.value || '') :
                              (value || '')}
                            onChange={(e) => handleInputChange(sectionKey, index, field, e.target.value)}
                            onBlur={() => {
                              handleBlurSave(sectionKey, index, field);
                              finishEdit();
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter' && e.ctrlKey) {
                                handleBlurSave(sectionKey, index, field);
                                finishEdit();
                              }
                              if (e.key === 'Escape') {
                                finishEdit();
                              }
                            }}
                            className="cell-input"
                            rows={typeof value === 'string' && value.includes('\n') ? value.split('\n').length + 1 : 2}
                          />
                        ) : (
                          <span
                            onClick={() => isEditable && startEdit(sectionKey, index, field)}
                            className={`cell-content ${isEditable ? 'editable-cell' : ''}`}
                            title={isEditable ? '点击编辑' : ''}
                          >
                            {contentToRender}
                          </span>
                        )}
                      </td>
                    );
                  })}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="work-target-loading">
        <div className="loading-spinner"></div>
        <p>正在加载数据...</p>
      </div>
    );
  }

  return (
    <div className="work-target">
      {/* 页面头部 */}
      <div className="page-header">
        <button 
          className="back-btn-top"
          onClick={() => onNavigate('home')}
          style={{ fontSize: '18px' }}
        >
          返回首页
        </button>
        
        <div className="header-center">
          <h1 className="page-title">开发中心2025年工作目标管理责任书</h1>
          <p className="page-subtitle">关键指标·质量指标·重点工作管理</p>
        </div>
        
        <div 
          className="sync-status"
          style={{ fontSize: '16px', marginRight: '10px' }}
        >
          <span 
            className={`status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}
            style={{ fontSize: '18px' }}
          >
            {syncStatus}
          </span>
        </div>
      </div>

      {/* 数据概览 */}
      <div className="data-overview">
        <div className="overview-item">
          <span 
            className="overview-number"
            style={{ fontSize: '32px' }}
          >
            {data.keyIndicators ? 
              Math.max(...data.keyIndicators.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 
              : 0
            }
          </span>
          <span 
            className="overview-label"
            style={{ fontSize: '16px' }}
          >
            关键指标
          </span>
        </div>
        <div className="overview-item">
          <span 
            className="overview-number"
            style={{ fontSize: '32px' }}
          >
            {data.qualityIndicators ? 
              Math.max(...data.qualityIndicators.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 
              : 0
            }
          </span>
          <span 
            className="overview-label"
            style={{ fontSize: '16px' }}
          >
            质量指标
          </span>
        </div>
        <div className="overview-item">
          <span 
            className="overview-number"
            style={{ fontSize: '32px' }}
          >
            {data.keyWork ? 
              Math.max(...data.keyWork.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 
              : 0
            }
          </span>
          <span 
            className="overview-label"
            style={{ fontSize: '16px' }}
          >
            重点工作
          </span>
        </div>
        <div className="overview-item">
          <span 
            className="overview-number"
            style={{ fontSize: '32px' }}
          >
            {(dataStats.totalWeight?.keyIndicators || 0) + 
             (dataStats.totalWeight?.qualityIndicators || 0) + 
             (dataStats.totalWeight?.keyWork || 0)}
          </span>
          <span 
            className="overview-label"
            style={{ fontSize: '16px' }}
          >
            总权重
          </span>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="content-area">
        {/* 三个大标题 */}
        <div className="section-tabs">
          {sections.map((section) => (
            <div
              key={section.key}
              className={`section-tab ${activeSection === section.key ? 'active' : ''}`}
              style={{ '--section-color': section.color }}
              onClick={() => handleSectionClick(section.key)}
            >
              <div 
                className="tab-icon"
                style={{ fontSize: '40px' }}
              >
                {section.icon}
              </div>
              <div className="tab-content">
                <h2 
                  className="tab-title"
                  style={{ fontSize: '20px' }}
                >
                  {section.title}
                </h2>
                <div 
                  className="tab-count"
                  style={{ fontSize: '15px' }}
                >
                  {data[section.key] ? 
                    Math.max(...data[section.key].filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 
                    : 0
                  } 项指标
                </div>
              </div>
              <div className="tab-arrow">
                {activeSection === section.key ? '▼' : '▶'}
              </div>
            </div>
          ))}
        </div>

        {/* 数据表格展示区域 */}
        {activeSection && (
          <div className="table-section">
            <div className="table-header">
              <h3>
                {sections.find(s => s.key === activeSection)?.title} - 详细数据
              </h3>
              <div className="table-actions">
                <span 
                  className="edit-hint"
                  style={{ fontSize: '15px' }}
                >
                  💡 点击单元格即可编辑，自动同步Excel
                </span>
                <button className="refresh-btn" onClick={forceRefresh}>
                  🔄 刷新数据
                </button>
                <button 
                  className="download-btn" 
                  onClick={openDownloadModal}
                  style={{ marginLeft: '10px' }}
                >
                  📊 选择性下载
                </button>
              </div>
            </div>
            {renderTable(activeSection)}
          </div>
        )}

        {!activeSection && (
          <div className="welcome-message">
            <h3>请选择要查看的指标类型</h3>
            <p>点击上方任意标题查看对应的详细数据</p>
          </div>
        )}
      </div>

      {/* 选择性下载模态框 */}
      <SelectiveDownloadModal
        isOpen={showDownloadModal}
        onClose={closeDownloadModal}
        data={data}
        onDownload={handleDownload}
      />
    </div>
  );
};

export default WorkTarget; 