@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');

/* 文件状态提示组件样式 */

.file-status-tip {
  position: fixed;
  top: 20px;
  right: 20px;
  min-width: 320px;
  max-width: 400px;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 77, 255, 0.3);
  font-family: 'Rajdhani', sans-serif;
  z-index: 9999;
  animation: slideInRight 0.3s ease-out;
}

.file-status-tip.error {
  border-color: rgba(255, 77, 77, 0.5);
  box-shadow: 0 8px 32px rgba(255, 77, 77, 0.2);
}

.file-status-tip.success {
  border-color: rgba(77, 255, 77, 0.5);
  box-shadow: 0 8px 32px rgba(77, 255, 77, 0.2);
}

.file-status-tip.loading {
  border-color: rgba(77, 200, 255, 0.5);
  box-shadow: 0 8px 32px rgba(77, 200, 255, 0.2);
}

.tip-header {
  display: flex;
  align-items: center;
  padding: 15px 20px 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tip-icon {
  font-size: 20px;
  margin-right: 10px;
}

.tip-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  text-shadow: 0 0 5px rgba(255, 77, 255, 0.5);
}

.tip-close {
  background: none;
  border: none;
  color: #ccc;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.tip-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.tip-content {
  padding: 10px 20px 20px;
}

.tip-message {
  color: #e0e0e0;
  font-size: 14px;
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.tip-suggestions {
  margin-bottom: 15px;
}

.tip-suggestions p {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.tip-suggestions ul {
  margin: 0;
  padding-left: 20px;
  color: #ccc;
}

.tip-suggestions li {
  font-size: 13px;
  line-height: 1.4;
  margin-bottom: 4px;
}

.tip-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.retry-button {
  padding: 8px 16px;
  background: linear-gradient(135deg, #ff4dff, #ff6b4d);
  color: #fff;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Rajdhani', sans-serif;
}

.retry-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 77, 255, 0.4);
}

.retry-button:active {
  transform: translateY(0);
}

/* 动画效果 */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 加载动画 */
.file-status-tip.loading .tip-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-status-tip {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
}
