{"ast": null, "code": "import _objectWithoutProperties from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"rowSpan\",\"isMergedStart\",\"isMergedCell\"];import*as XLSX from'xlsx';import kpiService from'./kpiService';class KPIDownloadService{constructor(){this.downloadHistory=[];}// 处理选择性下载（基于指标选择）\nasync handleSelectiveDownload(selectionData){try{console.log('开始处理KPI选择性下载:',selectionData);const{selectedData,selectedIndicators,format,includeAllMonths,statistics}=selectionData;// 验证选择数据\nif(!selectedData||selectedData.length===0){throw new Error('没有选择任何数据');}if(!selectedIndicators||selectedIndicators.length===0){throw new Error('没有选择任何指标');}// 根据格式生成文件\nconst result=await this.generateSelectiveFile(selectedData,selectionData);// 记录下载历史\nthis.recordSelectiveDownloadHistory(selectionData,result);return{success:true,message:\"\\u6210\\u529F\\u4E0B\\u8F7D \".concat(selectedIndicators.length,\" \\u4E2A\\u6307\\u6807\\u7684\\u6570\\u636E\"),filename:result.filename,downloadUrl:result.downloadUrl};}catch(error){console.error('KPI选择性下载处理失败:',error);return{success:false,message:error.message};}}// 处理KPI数据下载\nasync handleKPIDownload(selectionData){try{console.log('开始处理KPI下载:',selectionData);// 验证选择数据\nconst validation=this.validateSelection(selectionData);if(!validation.valid){throw new Error(validation.message);}// 根据选择条件过滤数据\nconst filteredData=this.filterDataBySelection(selectionData);if(filteredData.length===0){throw new Error('没有符合条件的数据可供下载');}// 根据格式生成文件\nconst result=await this.generateFile(filteredData,selectionData);// 记录下载历史\nthis.recordDownloadHistory(selectionData,result);return{success:true,message:\"\\u6210\\u529F\\u4E0B\\u8F7D \".concat(filteredData.length,\" \\u9879KPI\\u6570\\u636E\"),filename:result.filename,downloadUrl:result.downloadUrl};}catch(error){console.error('KPI下载处理失败:',error);return{success:false,message:error.message};}}// 验证选择数据\nvalidateSelection(selectionData){if(!selectionData){return{valid:false,message:'选择数据不能为空'};}if(!selectionData.items||selectionData.items.length===0){return{valid:false,message:'请至少选择一项KPI指标'};}if(!selectionData.monthRange||selectionData.monthRange.length!==2){return{valid:false,message:'请选择有效的月份范围'};}const[firstMonth,secondMonth]=selectionData.monthRange;if(firstMonth<1||firstMonth>12||secondMonth<1||secondMonth>12){return{valid:false,message:'月份范围无效'};}if(secondMonth<=firstMonth){return{valid:false,message:'结束月份必须大于开始月份'};}return{valid:true};}// 根据选择条件过滤数据\nfilterDataBySelection(selectionData){const{items,workTypes,responsibles,monthRange,data}=selectionData;// 基础过滤：选中的项目索引\nlet filteredData=data.filter((_,index)=>items.includes(index));// 按工作类型过滤\nif(workTypes&&workTypes.length>0){filteredData=filteredData.filter(item=>{return workTypes.some(type=>this.matchWorkType(item,type));});}// 按负责人过滤\nif(responsibles&&responsibles.length>0){filteredData=filteredData.filter(item=>{return responsibles.includes(item['统计方式&口径'])||responsibles.includes(item.统计方式);});}// 转换数据格式，只包含选定的月份\nreturn this.transformDataForDownload(filteredData,monthRange);}// 判断工作项是否匹配工作类型\nmatchWorkType(item,type){const indicator=item.指标||'';switch(type){case'开发相关':return indicator.includes('产品线')||indicator.includes('开发')||indicator.includes('降本')||indicator.includes('新产品');case'管理相关':return indicator.includes('管理')||indicator.includes('归口')||indicator.includes('预算')||indicator.includes('编制');case'质量相关':return indicator.includes('质量')||indicator.includes('及时率')||indicator.includes('命中率')||indicator.includes('周期');case'其他':return!this.matchWorkType(item,'开发相关')&&!this.matchWorkType(item,'管理相关')&&!this.matchWorkType(item,'质量相关');default:return true;}}// 转换数据用于下载\ntransformDataForDownload(data,monthRange){const[firstMonth,secondMonth]=monthRange;return data.map(row=>{// 过滤掉内部属性\nconst{rowSpan,isMergedStart,isMergedCell}=row,cleanRow=_objectWithoutProperties(row,_excluded);// 构建下载数据结构\nconst result={序号:cleanRow.序号,指标:cleanRow.指标,目标值:cleanRow.目标值,分值:cleanRow.分值,统计方式:cleanRow.统计方式||cleanRow['统计方式&口径'],考核标准:cleanRow.考核标准};// 添加指定月份的数据\nresult[\"\".concat(firstMonth,\"\\u6708\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\")]||\"\".concat(firstMonth,\"\\u6708\");result[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]||'';result[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]||'';result[\"\".concat(secondMonth,\"\\u6708\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\")]||\"\".concat(secondMonth,\"\\u6708\");result[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]||'';result[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]||'';return result;});}// 生成文件\nasync generateFile(data,selectionData){const{format,monthRange}=selectionData;const[firstMonth,secondMonth]=monthRange;const timestamp=new Date().toISOString().replace(/[:.]/g,'-').slice(0,19);if(format==='excel'){return await this.generateExcelFile(data,firstMonth,secondMonth,timestamp);}else{return await this.generateCSVFile(data,firstMonth,secondMonth,timestamp);}}// 生成Excel文件\nasync generateExcelFile(data,firstMonth,secondMonth,timestamp){try{// 创建工作簿\nconst workbook=XLSX.utils.book_new();// 准备数据表头\nconst headers=['序号','指标','目标值','分值(30)','统计方式&口径','考核标准',\"\".concat(firstMonth,\"\\u6708\"),\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\"),\"\".concat(secondMonth,\"\\u6708\"),\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")];// 准备工作表数据\nconst wsData=[headers];data.forEach(row=>{const rowData=[row.序号,row.指标,row.目标值,row.分值,row.统计方式,row.考核标准,row[\"\".concat(firstMonth,\"\\u6708\")],row[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")],row[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")],row[\"\".concat(secondMonth,\"\\u6708\")],row[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")],row[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]];wsData.push(rowData);});// 创建工作表\nconst worksheet=XLSX.utils.aoa_to_sheet(wsData);// 设置列宽\nconst colWidths=[{wch:8},// 序号\n{wch:25},// 指标\n{wch:40},// 目标值\n{wch:12},// 分值\n{wch:20},// 统计方式\n{wch:30},// 考核标准\n{wch:10},// 月份1\n{wch:25},// 完成情况1\n{wch:10},// 得分1\n{wch:10},// 月份2\n{wch:25},// 完成情况2\n{wch:10}// 得分2\n];worksheet['!cols']=colWidths;// 设置行高\nconst rowHeights=wsData.map(()=>({hpt:20}));worksheet['!rows']=rowHeights;// 设置表头样式\nthis.setHeaderStyle(worksheet,headers.length);// 添加工作表到工作簿\nXLSX.utils.book_append_sheet(workbook,worksheet,\"KPI\\u6570\\u636E_\".concat(firstMonth,\"-\").concat(secondMonth,\"\\u6708\"));// 创建汇总页\nthis.createSummarySheet(workbook,data,firstMonth,secondMonth);// 生成文件名\nconst filename=\"KPI\\u8DDF\\u8E2A\\u6570\\u636E_\".concat(firstMonth,\"-\").concat(secondMonth,\"\\u6708_\").concat(timestamp,\".xlsx\");// 写入文件\nconst excelBuffer=XLSX.write(workbook,{bookType:'xlsx',type:'array',compression:true});// 创建下载\nthis.downloadFile(excelBuffer,filename,'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');return{filename:filename,downloadUrl:\"data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,\".concat(this.arrayBufferToBase64(excelBuffer))};}catch(error){console.error('生成Excel文件失败:',error);throw new Error('Excel文件生成失败: '+error.message);}}// 生成CSV文件\nasync generateCSVFile(data,firstMonth,secondMonth,timestamp){try{// 准备CSV头部\nconst headers=['序号','指标','目标值','分值','统计方式','考核标准',\"\".concat(firstMonth,\"\\u6708\"),\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\"),\"\".concat(secondMonth,\"\\u6708\"),\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")];// 构建CSV内容\nlet csvContent=headers.join(',')+'\\n';data.forEach(row=>{const rowData=[this.escapeCsvValue(row.序号),this.escapeCsvValue(row.指标),this.escapeCsvValue(row.目标值),this.escapeCsvValue(row.分值),this.escapeCsvValue(row.统计方式),this.escapeCsvValue(row.考核标准),this.escapeCsvValue(row[\"\".concat(firstMonth,\"\\u6708\")]),this.escapeCsvValue(row[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.escapeCsvValue(row[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]),this.escapeCsvValue(row[\"\".concat(secondMonth,\"\\u6708\")]),this.escapeCsvValue(row[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.escapeCsvValue(row[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")])];csvContent+=rowData.join(',')+'\\n';});// 添加BOM以支持中文\nconst BOM='\\uFEFF';const finalContent=BOM+csvContent;// 生成文件名\nconst filename=\"KPI\\u8DDF\\u8E2A\\u6570\\u636E_\".concat(firstMonth,\"-\").concat(secondMonth,\"\\u6708_\").concat(timestamp,\".csv\");// 创建下载\nthis.downloadFile(finalContent,filename,'text/csv;charset=utf-8');return{filename:filename,downloadUrl:\"data:text/csv;charset=utf-8,\".concat(encodeURIComponent(finalContent))};}catch(error){console.error('生成CSV文件失败:',error);throw new Error('CSV文件生成失败: '+error.message);}}// 转义CSV值\nescapeCsvValue(value){if(value===null||value===undefined){return'';}const stringValue=String(value);// 如果包含逗号、引号或换行符，需要用引号包围并转义引号\nif(stringValue.includes(',')||stringValue.includes('\"')||stringValue.includes('\\n')){return'\"'+stringValue.replace(/\"/g,'\"\"')+'\"';}return stringValue;}// 下载文件\ndownloadFile(content,filename,mimeType){try{let blob;if(content instanceof ArrayBuffer){blob=new Blob([content],{type:mimeType});}else{blob=new Blob([content],{type:mimeType});}const url=window.URL.createObjectURL(blob);const link=document.createElement('a');link.href=url;link.download=filename;link.style.display='none';document.body.appendChild(link);link.click();document.body.removeChild(link);// 释放URL对象\nsetTimeout(()=>window.URL.revokeObjectURL(url),100);}catch(error){console.error('文件下载失败:',error);throw new Error('文件下载失败: '+error.message);}}// ArrayBuffer转Base64\narrayBufferToBase64(buffer){let binary='';const bytes=new Uint8Array(buffer);const len=bytes.byteLength;for(let i=0;i<len;i++){binary+=String.fromCharCode(bytes[i]);}return window.btoa(binary);}// 记录下载历史\nrecordDownloadHistory(selectionData,result){const historyRecord={timestamp:new Date().toISOString(),filename:result.filename,itemCount:selectionData.items.length,monthRange:selectionData.monthRange,format:selectionData.format,workTypes:selectionData.workTypes,responsibles:selectionData.responsibles};this.downloadHistory.push(historyRecord);// 只保留最近100条记录\nif(this.downloadHistory.length>100){this.downloadHistory=this.downloadHistory.slice(-100);}console.log('下载历史记录已保存:',historyRecord);}// 获取下载历史\ngetDownloadHistory(){return[...this.downloadHistory].reverse();// 返回副本，最新的在前\n}// 清除下载历史\nclearDownloadHistory(){this.downloadHistory=[];console.log('下载历史已清除');}// 获取下载统计\ngetDownloadStats(){const stats={totalDownloads:this.downloadHistory.length,excelDownloads:this.downloadHistory.filter(record=>record.format==='excel').length,csvDownloads:this.downloadHistory.filter(record=>record.format==='csv').length,totalItems:this.downloadHistory.reduce((sum,record)=>sum+record.itemCount,0)};if(this.downloadHistory.length>0){stats.lastDownload=this.downloadHistory[this.downloadHistory.length-1].timestamp;stats.mostUsedFormat=stats.excelDownloads>=stats.csvDownloads?'excel':'csv';}return stats;}// 设置表头样式\nsetHeaderStyle(worksheet,colCount){for(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(!worksheet[cellRef])continue;worksheet[cellRef].s={font:{bold:true,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"20FF4D\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}// 创建汇总页\ncreateSummarySheet(workbook,data,firstMonth,secondMonth){const summaryData=[['KPI跟踪数据汇总'],[''],['导出时间',this.formatDateTime(new Date())],['月份范围',\"\".concat(firstMonth,\"\\u6708 - \").concat(secondMonth,\"\\u6708\")],['总KPI项数',data.length],[''],['统计信息'],['项目','数量','占比']];// 计算统计信息\nconst totalItems=data.length;const itemsWithFirstMonth=data.filter(item=>item[\"\".concat(firstMonth,\"\\u6708\")]&&item[\"\".concat(firstMonth,\"\\u6708\")]!=='').length;const itemsWithSecondMonth=data.filter(item=>item[\"\".concat(secondMonth,\"\\u6708\")]&&item[\"\".concat(secondMonth,\"\\u6708\")]!=='').length;const itemsWithFirstScore=data.filter(item=>item[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]&&item[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]!=='').length;const itemsWithSecondScore=data.filter(item=>item[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]&&item[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]!=='').length;summaryData.push([\"\".concat(firstMonth,\"\\u6708\\u6709\\u6570\\u636E\"),itemsWithFirstMonth,\"\".concat((itemsWithFirstMonth/totalItems*100).toFixed(1),\"%\")],[\"\".concat(secondMonth,\"\\u6708\\u6709\\u6570\\u636E\"),itemsWithSecondMonth,\"\".concat((itemsWithSecondMonth/totalItems*100).toFixed(1),\"%\")],[\"\".concat(firstMonth,\"\\u6708\\u6709\\u5F97\\u5206\"),itemsWithFirstScore,\"\".concat((itemsWithFirstScore/totalItems*100).toFixed(1),\"%\")],[\"\".concat(secondMonth,\"\\u6708\\u6709\\u5F97\\u5206\"),itemsWithSecondScore,\"\".concat((itemsWithSecondScore/totalItems*100).toFixed(1),\"%\")]);// 创建汇总工作表\nconst summaryWorksheet=XLSX.utils.aoa_to_sheet(summaryData);// 设置列宽\nsummaryWorksheet['!cols']=[{wch:20},{wch:15},{wch:10}];// 设置标题样式\nconst titleCellRef=XLSX.utils.encode_cell({r:0,c:0});if(summaryWorksheet[titleCellRef]){summaryWorksheet[titleCellRef].s={font:{bold:true,size:16,color:{rgb:\"000000\"}},fill:{fgColor:{rgb:\"FFE066\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}// 添加到工作簿\nXLSX.utils.book_append_sheet(workbook,summaryWorksheet,'汇总信息');}// 格式化日期时间\nformatDateTime(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day,\" \").concat(hours,\":\").concat(minutes);}// 格式化日期（用于文件名）\nformatDate(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');return\"\".concat(year).concat(month).concat(day,\"_\").concat(hours).concat(minutes);}// 生成选择性文件\nasync generateSelectiveFile(selectedData,selectionData){const{format}=selectionData;switch(format){case'excel':return await this.generateSelectiveExcelFile(selectedData,selectionData);case'csv':return await this.generateSelectiveCSVFile(selectedData,selectionData);default:throw new Error(\"\\u4E0D\\u652F\\u6301\\u7684\\u683C\\u5F0F: \".concat(format));}}// 生成选择性Excel文件（包含全年数据）\nasync generateSelectiveExcelFile(selectedData,selectionData){try{const{selectedIndicators,statistics}=selectionData;// 创建工作簿\nconst workbook=XLSX.utils.book_new();// 准备全年数据（2月-12月）\nconst fullYearData=this.prepareFullYearData(selectedData);// 创建主数据表\nconst mainSheet=this.createSelectiveMainSheet(fullYearData);XLSX.utils.book_append_sheet(workbook,mainSheet,'KPI数据');// 创建汇总表\nconst summarySheet=this.createSelectiveSummarySheet(selectedData,selectionData);XLSX.utils.book_append_sheet(workbook,summarySheet,'下载汇总');// 生成文件\nconst filename=\"KPI\\u9009\\u62E9\\u6027\\u4E0B\\u8F7D_\".concat(selectedIndicators.length,\"\\u4E2A\\u6307\\u6807_\").concat(this.formatDate(new Date()),\".xlsx\");const buffer=XLSX.write(workbook,{type:'array',bookType:'xlsx'});// 下载文件\nthis.downloadFile(buffer,filename,'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');return{success:true,filename:filename,downloadUrl:null};}catch(error){console.error('生成选择性Excel文件失败:',error);throw error;}}// 生成选择性CSV文件（包含全年数据）\nasync generateSelectiveCSVFile(selectedData,selectionData){try{const{selectedIndicators}=selectionData;// 准备全年数据（2月-12月）\nconst fullYearData=this.prepareFullYearData(selectedData);// 构建CSV头部\nconst months=[2,3,4,5,6,7,8,9,10,11,12];const headers=['序号','指标','目标值','分值','统计方式&口径','考核标准'];// 添加月份列\nmonths.forEach(month=>{headers.push(\"\".concat(month,\"\\u6708\\u4EFD\"));headers.push(\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\"));headers.push(\"\".concat(month,\"\\u6708\\u5F97\\u5206\"));});// 构建CSV内容\nlet csvContent='\\ufeff';// BOM for UTF-8\ncsvContent+=headers.map(header=>\"\\\"\".concat(header,\"\\\"\")).join(',')+'\\n';// 添加数据行\nfullYearData.forEach(item=>{const row=[item.序号||'',item.指标||'',item.目标值||'',item.分值||'',item['统计方式&口径']||'',item.考核标准||''];// 添加月份数据\nmonths.forEach(month=>{row.push(item[\"\".concat(month,\"\\u6708\\u4EFD\")]||'');row.push(item[\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\")]||'');row.push(item[\"\".concat(month,\"\\u6708\\u5F97\\u5206\")]||'');});csvContent+=row.map(cell=>\"\\\"\".concat(String(cell).replace(/\"/g,'\"\"'),\"\\\"\")).join(',')+'\\n';});// 生成文件名\nconst filename=\"KPI\\u9009\\u62E9\\u6027\\u4E0B\\u8F7D_\".concat(selectedIndicators.length,\"\\u4E2A\\u6307\\u6807_\").concat(this.formatDate(new Date()),\".csv\");// 下载文件\nthis.downloadFile(csvContent,filename,'text/csv;charset=utf-8');return{success:true,filename:filename,downloadUrl:null};}catch(error){console.error('生成选择性CSV文件失败:',error);throw error;}}// 准备全年数据（2月-12月）\nprepareFullYearData(selectedData){const months=[2,3,4,5,6,7,8,9,10,11,12];return selectedData.map(item=>{const fullYearItem={序号:item.序号||'',指标:item.指标||'',目标值:item.目标值||'',分值:item.分值||'','统计方式&口径':item['统计方式&口径']||item.统计方式||'',考核标准:item.考核标准||''};// 添加所有月份的数据\nmonths.forEach(month=>{fullYearItem[\"\".concat(month,\"\\u6708\\u4EFD\")]=item[\"\".concat(month,\"\\u6708\\u4EFD\")]||'';fullYearItem[\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\")]=item[\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\")]||'';fullYearItem[\"\".concat(month,\"\\u6708\\u5F97\\u5206\")]=item[\"\".concat(month,\"\\u6708\\u5F97\\u5206\")]||'';});return fullYearItem;});}// 创建选择性主数据表\ncreateSelectiveMainSheet(fullYearData){const months=[2,3,4,5,6,7,8,9,10,11,12];// 构建表头\nconst headers=['序号','指标','目标值','分值','统计方式&口径','考核标准'];// 添加月份列\nmonths.forEach(month=>{headers.push(\"\".concat(month,\"\\u6708\\u4EFD\"));headers.push(\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\"));headers.push(\"\".concat(month,\"\\u6708\\u5F97\\u5206\"));});// 构建数据行\nconst rows=[headers];fullYearData.forEach(item=>{const row=[item.序号,item.指标,item.目标值,item.分值,item['统计方式&口径'],item.考核标准];// 添加月份数据\nmonths.forEach(month=>{row.push(item[\"\".concat(month,\"\\u6708\\u4EFD\")]);row.push(item[\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\")]);row.push(item[\"\".concat(month,\"\\u6708\\u5F97\\u5206\")]);});rows.push(row);});return XLSX.utils.aoa_to_sheet(rows);}// 创建选择性汇总表\ncreateSelectiveSummarySheet(selectedData,selectionData){const{selectedIndicators,statistics}=selectionData;const summaryData=[['KPI选择性下载汇总'],[''],['下载时间',this.formatDate(new Date())],['选择指标数量',selectedIndicators.length],['总指标数量',statistics.totalItems],['选择比例',\"\".concat((selectedIndicators.length/statistics.totalItems*100).toFixed(1),\"%\")],[''],['选择的指标列表'],...selectedIndicators.map((indicator,index)=>[\"\".concat(index+1,\". \").concat(indicator)]),[''],['数据范围说明'],['包含月份','2月 - 12月（全年数据）'],['数据列','序号、指标、目标值、分值、统计方式&口径、考核标准'],['月份列','每月的计划、完成情况、得分'],[''],['注意事项'],['1. 只包含选中指标的数据行'],['2. 包含完整的2月-12月全年数据'],['3. 完成情况列为只读数据，请勿修改']];return XLSX.utils.aoa_to_sheet(summaryData);}// 记录选择性下载历史\nrecordSelectiveDownloadHistory(selectionData,result){const{selectedIndicators,format,statistics}=selectionData;this.downloadHistory.push({type:'selective',timestamp:new Date(),selectedIndicators:selectedIndicators,indicatorCount:selectedIndicators.length,totalIndicators:statistics.totalItems,format:format,filename:result.filename,success:result.success});// 保持历史记录不超过100条\nif(this.downloadHistory.length>100){this.downloadHistory=this.downloadHistory.slice(-100);}}}const kpiDownloadService=new KPIDownloadService();export default kpiDownloadService;", "map": {"version": 3, "names": ["XLSX", "kpiService", "KPIDownloadService", "constructor", "downloadHistory", "handleSelectiveDownload", "selectionData", "console", "log", "selectedData", "selectedIndicators", "format", "includeAllMonths", "statistics", "length", "Error", "result", "generateSelectiveFile", "recordSelectiveDownloadHistory", "success", "message", "concat", "filename", "downloadUrl", "error", "handleKPIDownload", "validation", "validateSelection", "valid", "filteredData", "filterDataBySelection", "generateFile", "recordDownloadHistory", "items", "<PERSON><PERSON><PERSON><PERSON>", "firstMonth", "second<PERSON><PERSON><PERSON>", "workTypes", "responsibles", "data", "filter", "_", "index", "includes", "item", "some", "type", "matchWorkType", "统计方式", "transformDataForDownload", "indicator", "指标", "map", "row", "rowSpan", "isMergedStart", "isMergedCell", "cleanRow", "_objectWithoutProperties", "_excluded", "序号", "目标值", "分值", "考核标准", "timestamp", "Date", "toISOString", "replace", "slice", "generateExcelFile", "generateCSVFile", "workbook", "utils", "book_new", "headers", "wsData", "for<PERSON>ach", "rowData", "push", "worksheet", "aoa_to_sheet", "col<PERSON><PERSON><PERSON>", "wch", "rowHeights", "hpt", "setHeaderStyle", "book_append_sheet", "createSummarySheet", "excelBuffer", "write", "bookType", "compression", "downloadFile", "arrayBufferToBase64", "csv<PERSON><PERSON>nt", "join", "escapeCsvValue", "BOM", "finalContent", "encodeURIComponent", "value", "undefined", "stringValue", "String", "content", "mimeType", "blob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "revokeObjectURL", "buffer", "binary", "bytes", "Uint8Array", "len", "byteLength", "i", "fromCharCode", "btoa", "historyRecord", "itemCount", "getDownloadHistory", "reverse", "clearDownloadHistory", "getDownloadStats", "stats", "totalDownloads", "excelDownloads", "record", "csvDownloads", "totalItems", "reduce", "sum", "lastDownload", "mostUsedFormat", "col<PERSON>ount", "cellRef", "encode_cell", "r", "c", "s", "font", "bold", "color", "rgb", "fill", "fgColor", "alignment", "horizontal", "vertical", "summaryData", "formatDateTime", "itemsWithFirstMonth", "itemsWithSecondMonth", "itemsWithFirstScore", "itemsWithSecondScore", "toFixed", "summaryWorksheet", "titleCellRef", "size", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "formatDate", "generateSelectiveExcelFile", "generateSelectiveCSVFile", "fullYearData", "prepareFullYearData", "mainSheet", "createSelectiveMainSheet", "summarySheet", "createSelectiveSummarySheet", "months", "header", "cell", "fullYearItem", "rows", "indicatorCount", "totalIndicators", "kpiDownloadService"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块四/services/kpiDownloadService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\nimport kpiService from './kpiService';\r\n\r\nclass KPIDownloadService {\r\n  constructor() {\r\n    this.downloadHistory = [];\r\n  }\r\n\r\n  // 处理选择性下载（基于指标选择）\r\n  async handleSelectiveDownload(selectionData) {\r\n    try {\r\n      console.log('开始处理KPI选择性下载:', selectionData);\r\n\r\n      const { selectedData, selectedIndicators, format, includeAllMonths, statistics } = selectionData;\r\n\r\n      // 验证选择数据\r\n      if (!selectedData || selectedData.length === 0) {\r\n        throw new Error('没有选择任何数据');\r\n      }\r\n\r\n      if (!selectedIndicators || selectedIndicators.length === 0) {\r\n        throw new Error('没有选择任何指标');\r\n      }\r\n\r\n      // 根据格式生成文件\r\n      const result = await this.generateSelectiveFile(selectedData, selectionData);\r\n\r\n      // 记录下载历史\r\n      this.recordSelectiveDownloadHistory(selectionData, result);\r\n\r\n      return {\r\n        success: true,\r\n        message: `成功下载 ${selectedIndicators.length} 个指标的数据`,\r\n        filename: result.filename,\r\n        downloadUrl: result.downloadUrl\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('KPI选择性下载处理失败:', error);\r\n      return {\r\n        success: false,\r\n        message: error.message\r\n      };\r\n    }\r\n  }\r\n\r\n  // 处理KPI数据下载\r\n  async handleKPIDownload(selectionData) {\r\n    try {\r\n      console.log('开始处理KPI下载:', selectionData);\r\n      \r\n      // 验证选择数据\r\n      const validation = this.validateSelection(selectionData);\r\n      if (!validation.valid) {\r\n        throw new Error(validation.message);\r\n      }\r\n\r\n      // 根据选择条件过滤数据\r\n      const filteredData = this.filterDataBySelection(selectionData);\r\n      \r\n      if (filteredData.length === 0) {\r\n        throw new Error('没有符合条件的数据可供下载');\r\n      }\r\n\r\n      // 根据格式生成文件\r\n      const result = await this.generateFile(filteredData, selectionData);\r\n      \r\n      // 记录下载历史\r\n      this.recordDownloadHistory(selectionData, result);\r\n      \r\n      return {\r\n        success: true,\r\n        message: `成功下载 ${filteredData.length} 项KPI数据`,\r\n        filename: result.filename,\r\n        downloadUrl: result.downloadUrl\r\n      };\r\n      \r\n    } catch (error) {\r\n      console.error('KPI下载处理失败:', error);\r\n      return {\r\n        success: false,\r\n        message: error.message\r\n      };\r\n    }\r\n  }\r\n\r\n  // 验证选择数据\r\n  validateSelection(selectionData) {\r\n    if (!selectionData) {\r\n      return { valid: false, message: '选择数据不能为空' };\r\n    }\r\n\r\n    if (!selectionData.items || selectionData.items.length === 0) {\r\n      return { valid: false, message: '请至少选择一项KPI指标' };\r\n    }\r\n\r\n    if (!selectionData.monthRange || selectionData.monthRange.length !== 2) {\r\n      return { valid: false, message: '请选择有效的月份范围' };\r\n    }\r\n\r\n    const [firstMonth, secondMonth] = selectionData.monthRange;\r\n    if (firstMonth < 1 || firstMonth > 12 || secondMonth < 1 || secondMonth > 12) {\r\n      return { valid: false, message: '月份范围无效' };\r\n    }\r\n\r\n    if (secondMonth <= firstMonth) {\r\n      return { valid: false, message: '结束月份必须大于开始月份' };\r\n    }\r\n\r\n    return { valid: true };\r\n  }\r\n\r\n  // 根据选择条件过滤数据\r\n  filterDataBySelection(selectionData) {\r\n    const { items, workTypes, responsibles, monthRange, data } = selectionData;\r\n    \r\n    // 基础过滤：选中的项目索引\r\n    let filteredData = data.filter((_, index) => items.includes(index));\r\n\r\n    // 按工作类型过滤\r\n    if (workTypes && workTypes.length > 0) {\r\n      filteredData = filteredData.filter(item => {\r\n        return workTypes.some(type => this.matchWorkType(item, type));\r\n      });\r\n    }\r\n\r\n    // 按负责人过滤\r\n    if (responsibles && responsibles.length > 0) {\r\n      filteredData = filteredData.filter(item => {\r\n        return responsibles.includes(item['统计方式&口径']) || responsibles.includes(item.统计方式);\r\n      });\r\n    }\r\n\r\n    // 转换数据格式，只包含选定的月份\r\n    return this.transformDataForDownload(filteredData, monthRange);\r\n  }\r\n\r\n  // 判断工作项是否匹配工作类型\r\n  matchWorkType(item, type) {\r\n    const indicator = item.指标 || '';\r\n    \r\n    switch (type) {\r\n      case '开发相关':\r\n        return indicator.includes('产品线') || indicator.includes('开发') || \r\n               indicator.includes('降本') || indicator.includes('新产品');\r\n      case '管理相关':\r\n        return indicator.includes('管理') || indicator.includes('归口') || \r\n               indicator.includes('预算') || indicator.includes('编制');\r\n      case '质量相关':\r\n        return indicator.includes('质量') || indicator.includes('及时率') || \r\n               indicator.includes('命中率') || indicator.includes('周期');\r\n      case '其他':\r\n        return !this.matchWorkType(item, '开发相关') && \r\n               !this.matchWorkType(item, '管理相关') && \r\n               !this.matchWorkType(item, '质量相关');\r\n      default:\r\n        return true;\r\n    }\r\n  }\r\n\r\n  // 转换数据用于下载\r\n  transformDataForDownload(data, monthRange) {\r\n    const [firstMonth, secondMonth] = monthRange;\r\n    \r\n    return data.map(row => {\r\n      // 过滤掉内部属性\r\n      const { rowSpan, isMergedStart, isMergedCell, ...cleanRow } = row;\r\n      \r\n      // 构建下载数据结构\r\n      const result = {\r\n        序号: cleanRow.序号,\r\n        指标: cleanRow.指标,\r\n        目标值: cleanRow.目标值,\r\n        分值: cleanRow.分值,\r\n        统计方式: cleanRow.统计方式 || cleanRow['统计方式&口径'],\r\n        考核标准: cleanRow.考核标准\r\n      };\r\n      \r\n      // 添加指定月份的数据\r\n      result[`${firstMonth}月`] = cleanRow[`${firstMonth}月`] || `${firstMonth}月`;\r\n      result[`${firstMonth}月完成情况`] = cleanRow[`${firstMonth}月完成情况`] || '';\r\n      result[`${firstMonth}月得分`] = cleanRow[`${firstMonth}月得分`] || '';\r\n      result[`${secondMonth}月`] = cleanRow[`${secondMonth}月`] || `${secondMonth}月`;\r\n      result[`${secondMonth}月完成情况`] = cleanRow[`${secondMonth}月完成情况`] || '';\r\n      result[`${secondMonth}月得分`] = cleanRow[`${secondMonth}月得分`] || '';\r\n      \r\n      return result;\r\n    });\r\n  }\r\n\r\n  // 生成文件\r\n  async generateFile(data, selectionData) {\r\n    const { format, monthRange } = selectionData;\r\n    const [firstMonth, secondMonth] = monthRange;\r\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);\r\n    \r\n    if (format === 'excel') {\r\n      return await this.generateExcelFile(data, firstMonth, secondMonth, timestamp);\r\n    } else {\r\n      return await this.generateCSVFile(data, firstMonth, secondMonth, timestamp);\r\n    }\r\n  }\r\n\r\n  // 生成Excel文件\r\n  async generateExcelFile(data, firstMonth, secondMonth, timestamp) {\r\n    try {\r\n      // 创建工作簿\r\n      const workbook = XLSX.utils.book_new();\r\n      \r\n      // 准备数据表头\r\n      const headers = [\r\n        '序号', '指标', '目标值', '分值(30)', '统计方式&口径', '考核标准',\r\n        `${firstMonth}月`, `${firstMonth}月完成情况`, `${firstMonth}月得分`,\r\n        `${secondMonth}月`, `${secondMonth}月完成情况`, `${secondMonth}月得分`\r\n      ];\r\n      \r\n      // 准备工作表数据\r\n      const wsData = [headers];\r\n      \r\n      data.forEach(row => {\r\n        const rowData = [\r\n          row.序号,\r\n          row.指标,\r\n          row.目标值,\r\n          row.分值,\r\n          row.统计方式,\r\n          row.考核标准,\r\n          row[`${firstMonth}月`],\r\n          row[`${firstMonth}月完成情况`],\r\n          row[`${firstMonth}月得分`],\r\n          row[`${secondMonth}月`],\r\n          row[`${secondMonth}月完成情况`],\r\n          row[`${secondMonth}月得分`]\r\n        ];\r\n        wsData.push(rowData);\r\n      });\r\n      \r\n      // 创建工作表\r\n      const worksheet = XLSX.utils.aoa_to_sheet(wsData);\r\n      \r\n      // 设置列宽\r\n      const colWidths = [\r\n        { wch: 8 },   // 序号\r\n        { wch: 25 },  // 指标\r\n        { wch: 40 },  // 目标值\r\n        { wch: 12 },  // 分值\r\n        { wch: 20 },  // 统计方式\r\n        { wch: 30 },  // 考核标准\r\n        { wch: 10 },  // 月份1\r\n        { wch: 25 },  // 完成情况1\r\n        { wch: 10 },  // 得分1\r\n        { wch: 10 },  // 月份2\r\n        { wch: 25 },  // 完成情况2\r\n        { wch: 10 }   // 得分2\r\n      ];\r\n      worksheet['!cols'] = colWidths;\r\n      \r\n      // 设置行高\r\n      const rowHeights = wsData.map(() => ({ hpt: 20 }));\r\n      worksheet['!rows'] = rowHeights;\r\n\r\n      // 设置表头样式\r\n      this.setHeaderStyle(worksheet, headers.length);\r\n\r\n      // 添加工作表到工作簿\r\n      XLSX.utils.book_append_sheet(workbook, worksheet, `KPI数据_${firstMonth}-${secondMonth}月`);\r\n\r\n      // 创建汇总页\r\n      this.createSummarySheet(workbook, data, firstMonth, secondMonth);\r\n      \r\n      // 生成文件名\r\n      const filename = `KPI跟踪数据_${firstMonth}-${secondMonth}月_${timestamp}.xlsx`;\r\n      \r\n      // 写入文件\r\n      const excelBuffer = XLSX.write(workbook, { \r\n        bookType: 'xlsx', \r\n        type: 'array',\r\n        compression: true\r\n      });\r\n      \r\n      // 创建下载\r\n      this.downloadFile(excelBuffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\r\n      \r\n      return {\r\n        filename: filename,\r\n        downloadUrl: `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${this.arrayBufferToBase64(excelBuffer)}`\r\n      };\r\n      \r\n    } catch (error) {\r\n      console.error('生成Excel文件失败:', error);\r\n      throw new Error('Excel文件生成失败: ' + error.message);\r\n    }\r\n  }\r\n\r\n  // 生成CSV文件\r\n  async generateCSVFile(data, firstMonth, secondMonth, timestamp) {\r\n    try {\r\n      // 准备CSV头部\r\n      const headers = [\r\n        '序号', '指标', '目标值', '分值', '统计方式', '考核标准',\r\n        `${firstMonth}月`, `${firstMonth}月完成情况`, `${firstMonth}月得分`,\r\n        `${secondMonth}月`, `${secondMonth}月完成情况`, `${secondMonth}月得分`\r\n      ];\r\n      \r\n      // 构建CSV内容\r\n      let csvContent = headers.join(',') + '\\n';\r\n      \r\n      data.forEach(row => {\r\n        const rowData = [\r\n          this.escapeCsvValue(row.序号),\r\n          this.escapeCsvValue(row.指标),\r\n          this.escapeCsvValue(row.目标值),\r\n          this.escapeCsvValue(row.分值),\r\n          this.escapeCsvValue(row.统计方式),\r\n          this.escapeCsvValue(row.考核标准),\r\n          this.escapeCsvValue(row[`${firstMonth}月`]),\r\n          this.escapeCsvValue(row[`${firstMonth}月完成情况`]),\r\n          this.escapeCsvValue(row[`${firstMonth}月得分`]),\r\n          this.escapeCsvValue(row[`${secondMonth}月`]),\r\n          this.escapeCsvValue(row[`${secondMonth}月完成情况`]),\r\n          this.escapeCsvValue(row[`${secondMonth}月得分`])\r\n        ];\r\n        csvContent += rowData.join(',') + '\\n';\r\n      });\r\n      \r\n      // 添加BOM以支持中文\r\n      const BOM = '\\uFEFF';\r\n      const finalContent = BOM + csvContent;\r\n      \r\n      // 生成文件名\r\n      const filename = `KPI跟踪数据_${firstMonth}-${secondMonth}月_${timestamp}.csv`;\r\n      \r\n      // 创建下载\r\n      this.downloadFile(finalContent, filename, 'text/csv;charset=utf-8');\r\n      \r\n      return {\r\n        filename: filename,\r\n        downloadUrl: `data:text/csv;charset=utf-8,${encodeURIComponent(finalContent)}`\r\n      };\r\n      \r\n    } catch (error) {\r\n      console.error('生成CSV文件失败:', error);\r\n      throw new Error('CSV文件生成失败: ' + error.message);\r\n    }\r\n  }\r\n\r\n  // 转义CSV值\r\n  escapeCsvValue(value) {\r\n    if (value === null || value === undefined) {\r\n      return '';\r\n    }\r\n    \r\n    const stringValue = String(value);\r\n    \r\n    // 如果包含逗号、引号或换行符，需要用引号包围并转义引号\r\n    if (stringValue.includes(',') || stringValue.includes('\"') || stringValue.includes('\\n')) {\r\n      return '\"' + stringValue.replace(/\"/g, '\"\"') + '\"';\r\n    }\r\n    \r\n    return stringValue;\r\n  }\r\n\r\n  // 下载文件\r\n  downloadFile(content, filename, mimeType) {\r\n    try {\r\n      let blob;\r\n      \r\n      if (content instanceof ArrayBuffer) {\r\n        blob = new Blob([content], { type: mimeType });\r\n      } else {\r\n        blob = new Blob([content], { type: mimeType });\r\n      }\r\n      \r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = filename;\r\n      link.style.display = 'none';\r\n      \r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      \r\n      // 释放URL对象\r\n      setTimeout(() => window.URL.revokeObjectURL(url), 100);\r\n      \r\n    } catch (error) {\r\n      console.error('文件下载失败:', error);\r\n      throw new Error('文件下载失败: ' + error.message);\r\n    }\r\n  }\r\n\r\n  // ArrayBuffer转Base64\r\n  arrayBufferToBase64(buffer) {\r\n    let binary = '';\r\n    const bytes = new Uint8Array(buffer);\r\n    const len = bytes.byteLength;\r\n    for (let i = 0; i < len; i++) {\r\n      binary += String.fromCharCode(bytes[i]);\r\n    }\r\n    return window.btoa(binary);\r\n  }\r\n\r\n  // 记录下载历史\r\n  recordDownloadHistory(selectionData, result) {\r\n    const historyRecord = {\r\n      timestamp: new Date().toISOString(),\r\n      filename: result.filename,\r\n      itemCount: selectionData.items.length,\r\n      monthRange: selectionData.monthRange,\r\n      format: selectionData.format,\r\n      workTypes: selectionData.workTypes,\r\n      responsibles: selectionData.responsibles\r\n    };\r\n    \r\n    this.downloadHistory.push(historyRecord);\r\n    \r\n    // 只保留最近100条记录\r\n    if (this.downloadHistory.length > 100) {\r\n      this.downloadHistory = this.downloadHistory.slice(-100);\r\n    }\r\n    \r\n    console.log('下载历史记录已保存:', historyRecord);\r\n  }\r\n\r\n  // 获取下载历史\r\n  getDownloadHistory() {\r\n    return [...this.downloadHistory].reverse(); // 返回副本，最新的在前\r\n  }\r\n\r\n  // 清除下载历史\r\n  clearDownloadHistory() {\r\n    this.downloadHistory = [];\r\n    console.log('下载历史已清除');\r\n  }\r\n\r\n  // 获取下载统计\r\n  getDownloadStats() {\r\n    const stats = {\r\n      totalDownloads: this.downloadHistory.length,\r\n      excelDownloads: this.downloadHistory.filter(record => record.format === 'excel').length,\r\n      csvDownloads: this.downloadHistory.filter(record => record.format === 'csv').length,\r\n      totalItems: this.downloadHistory.reduce((sum, record) => sum + record.itemCount, 0)\r\n    };\r\n    \r\n    if (this.downloadHistory.length > 0) {\r\n      stats.lastDownload = this.downloadHistory[this.downloadHistory.length - 1].timestamp;\r\n      stats.mostUsedFormat = stats.excelDownloads >= stats.csvDownloads ? 'excel' : 'csv';\r\n    }\r\n    \r\n    return stats;\r\n  }\r\n\r\n  // 设置表头样式\r\n  setHeaderStyle(worksheet, colCount) {\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (!worksheet[cellRef]) continue;\r\n\r\n      worksheet[cellRef].s = {\r\n        font: { bold: true, color: { rgb: \"FFFFFF\" } },\r\n        fill: { fgColor: { rgb: \"20FF4D\" } },\r\n        alignment: { horizontal: \"center\", vertical: \"center\" }\r\n      };\r\n    }\r\n  }\r\n\r\n  // 创建汇总页\r\n  createSummarySheet(workbook, data, firstMonth, secondMonth) {\r\n    const summaryData = [\r\n      ['KPI跟踪数据汇总'],\r\n      [''],\r\n      ['导出时间', this.formatDateTime(new Date())],\r\n      ['月份范围', `${firstMonth}月 - ${secondMonth}月`],\r\n      ['总KPI项数', data.length],\r\n      [''],\r\n      ['统计信息'],\r\n      ['项目', '数量', '占比'],\r\n    ];\r\n\r\n    // 计算统计信息\r\n    const totalItems = data.length;\r\n    const itemsWithFirstMonth = data.filter(item => item[`${firstMonth}月`] && item[`${firstMonth}月`] !== '').length;\r\n    const itemsWithSecondMonth = data.filter(item => item[`${secondMonth}月`] && item[`${secondMonth}月`] !== '').length;\r\n    const itemsWithFirstScore = data.filter(item => item[`${firstMonth}月得分`] && item[`${firstMonth}月得分`] !== '').length;\r\n    const itemsWithSecondScore = data.filter(item => item[`${secondMonth}月得分`] && item[`${secondMonth}月得分`] !== '').length;\r\n\r\n    summaryData.push(\r\n      [`${firstMonth}月有数据`, itemsWithFirstMonth, `${((itemsWithFirstMonth / totalItems) * 100).toFixed(1)}%`],\r\n      [`${secondMonth}月有数据`, itemsWithSecondMonth, `${((itemsWithSecondMonth / totalItems) * 100).toFixed(1)}%`],\r\n      [`${firstMonth}月有得分`, itemsWithFirstScore, `${((itemsWithFirstScore / totalItems) * 100).toFixed(1)}%`],\r\n      [`${secondMonth}月有得分`, itemsWithSecondScore, `${((itemsWithSecondScore / totalItems) * 100).toFixed(1)}%`]\r\n    );\r\n\r\n    // 创建汇总工作表\r\n    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);\r\n\r\n    // 设置列宽\r\n    summaryWorksheet['!cols'] = [\r\n      { wch: 20 },\r\n      { wch: 15 },\r\n      { wch: 10 }\r\n    ];\r\n\r\n    // 设置标题样式\r\n    const titleCellRef = XLSX.utils.encode_cell({ r: 0, c: 0 });\r\n    if (summaryWorksheet[titleCellRef]) {\r\n      summaryWorksheet[titleCellRef].s = {\r\n        font: { bold: true, size: 16, color: { rgb: \"000000\" } },\r\n        fill: { fgColor: { rgb: \"FFE066\" } },\r\n        alignment: { horizontal: \"center\", vertical: \"center\" }\r\n      };\r\n    }\r\n\r\n    // 添加到工作簿\r\n    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '汇总信息');\r\n  }\r\n\r\n  // 格式化日期时间\r\n  formatDateTime(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n  }\r\n\r\n  // 格式化日期（用于文件名）\r\n  formatDate(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${year}${month}${day}_${hours}${minutes}`;\r\n  }\r\n\r\n  // 生成选择性文件\r\n  async generateSelectiveFile(selectedData, selectionData) {\r\n    const { format } = selectionData;\r\n\r\n    switch (format) {\r\n      case 'excel':\r\n        return await this.generateSelectiveExcelFile(selectedData, selectionData);\r\n      case 'csv':\r\n        return await this.generateSelectiveCSVFile(selectedData, selectionData);\r\n      default:\r\n        throw new Error(`不支持的格式: ${format}`);\r\n    }\r\n  }\r\n\r\n  // 生成选择性Excel文件（包含全年数据）\r\n  async generateSelectiveExcelFile(selectedData, selectionData) {\r\n    try {\r\n      const { selectedIndicators, statistics } = selectionData;\r\n\r\n      // 创建工作簿\r\n      const workbook = XLSX.utils.book_new();\r\n\r\n      // 准备全年数据（2月-12月）\r\n      const fullYearData = this.prepareFullYearData(selectedData);\r\n\r\n      // 创建主数据表\r\n      const mainSheet = this.createSelectiveMainSheet(fullYearData);\r\n      XLSX.utils.book_append_sheet(workbook, mainSheet, 'KPI数据');\r\n\r\n      // 创建汇总表\r\n      const summarySheet = this.createSelectiveSummarySheet(selectedData, selectionData);\r\n      XLSX.utils.book_append_sheet(workbook, summarySheet, '下载汇总');\r\n\r\n      // 生成文件\r\n      const filename = `KPI选择性下载_${selectedIndicators.length}个指标_${this.formatDate(new Date())}.xlsx`;\r\n      const buffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });\r\n\r\n      // 下载文件\r\n      this.downloadFile(buffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');\r\n\r\n      return {\r\n        success: true,\r\n        filename: filename,\r\n        downloadUrl: null\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('生成选择性Excel文件失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成选择性CSV文件（包含全年数据）\r\n  async generateSelectiveCSVFile(selectedData, selectionData) {\r\n    try {\r\n      const { selectedIndicators } = selectionData;\r\n\r\n      // 准备全年数据（2月-12月）\r\n      const fullYearData = this.prepareFullYearData(selectedData);\r\n\r\n      // 构建CSV头部\r\n      const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\r\n      const headers = [\r\n        '序号', '指标', '目标值', '分值', '统计方式&口径', '考核标准'\r\n      ];\r\n\r\n      // 添加月份列\r\n      months.forEach(month => {\r\n        headers.push(`${month}月份`);\r\n        headers.push(`${month}月份完成情况`);\r\n        headers.push(`${month}月得分`);\r\n      });\r\n\r\n      // 构建CSV内容\r\n      let csvContent = '\\ufeff'; // BOM for UTF-8\r\n      csvContent += headers.map(header => `\"${header}\"`).join(',') + '\\n';\r\n\r\n      // 添加数据行\r\n      fullYearData.forEach(item => {\r\n        const row = [\r\n          item.序号 || '',\r\n          item.指标 || '',\r\n          item.目标值 || '',\r\n          item.分值 || '',\r\n          item['统计方式&口径'] || '',\r\n          item.考核标准 || ''\r\n        ];\r\n\r\n        // 添加月份数据\r\n        months.forEach(month => {\r\n          row.push(item[`${month}月份`] || '');\r\n          row.push(item[`${month}月份完成情况`] || '');\r\n          row.push(item[`${month}月得分`] || '');\r\n        });\r\n\r\n        csvContent += row.map(cell => `\"${String(cell).replace(/\"/g, '\"\"')}\"`).join(',') + '\\n';\r\n      });\r\n\r\n      // 生成文件名\r\n      const filename = `KPI选择性下载_${selectedIndicators.length}个指标_${this.formatDate(new Date())}.csv`;\r\n\r\n      // 下载文件\r\n      this.downloadFile(csvContent, filename, 'text/csv;charset=utf-8');\r\n\r\n      return {\r\n        success: true,\r\n        filename: filename,\r\n        downloadUrl: null\r\n      };\r\n\r\n    } catch (error) {\r\n      console.error('生成选择性CSV文件失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 准备全年数据（2月-12月）\r\n  prepareFullYearData(selectedData) {\r\n    const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\r\n\r\n    return selectedData.map(item => {\r\n      const fullYearItem = {\r\n        序号: item.序号 || '',\r\n        指标: item.指标 || '',\r\n        目标值: item.目标值 || '',\r\n        分值: item.分值 || '',\r\n        '统计方式&口径': item['统计方式&口径'] || item.统计方式 || '',\r\n        考核标准: item.考核标准 || ''\r\n      };\r\n\r\n      // 添加所有月份的数据\r\n      months.forEach(month => {\r\n        fullYearItem[`${month}月份`] = item[`${month}月份`] || '';\r\n        fullYearItem[`${month}月份完成情况`] = item[`${month}月份完成情况`] || '';\r\n        fullYearItem[`${month}月得分`] = item[`${month}月得分`] || '';\r\n      });\r\n\r\n      return fullYearItem;\r\n    });\r\n  }\r\n\r\n  // 创建选择性主数据表\r\n  createSelectiveMainSheet(fullYearData) {\r\n    const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];\r\n\r\n    // 构建表头\r\n    const headers = [\r\n      '序号', '指标', '目标值', '分值', '统计方式&口径', '考核标准'\r\n    ];\r\n\r\n    // 添加月份列\r\n    months.forEach(month => {\r\n      headers.push(`${month}月份`);\r\n      headers.push(`${month}月份完成情况`);\r\n      headers.push(`${month}月得分`);\r\n    });\r\n\r\n    // 构建数据行\r\n    const rows = [headers];\r\n    fullYearData.forEach(item => {\r\n      const row = [\r\n        item.序号, item.指标, item.目标值, item.分值,\r\n        item['统计方式&口径'], item.考核标准\r\n      ];\r\n\r\n      // 添加月份数据\r\n      months.forEach(month => {\r\n        row.push(item[`${month}月份`]);\r\n        row.push(item[`${month}月份完成情况`]);\r\n        row.push(item[`${month}月得分`]);\r\n      });\r\n\r\n      rows.push(row);\r\n    });\r\n\r\n    return XLSX.utils.aoa_to_sheet(rows);\r\n  }\r\n\r\n  // 创建选择性汇总表\r\n  createSelectiveSummarySheet(selectedData, selectionData) {\r\n    const { selectedIndicators, statistics } = selectionData;\r\n\r\n    const summaryData = [\r\n      ['KPI选择性下载汇总'],\r\n      [''],\r\n      ['下载时间', this.formatDate(new Date())],\r\n      ['选择指标数量', selectedIndicators.length],\r\n      ['总指标数量', statistics.totalItems],\r\n      ['选择比例', `${((selectedIndicators.length / statistics.totalItems) * 100).toFixed(1)}%`],\r\n      [''],\r\n      ['选择的指标列表'],\r\n      ...selectedIndicators.map((indicator, index) => [`${index + 1}. ${indicator}`]),\r\n      [''],\r\n      ['数据范围说明'],\r\n      ['包含月份', '2月 - 12月（全年数据）'],\r\n      ['数据列', '序号、指标、目标值、分值、统计方式&口径、考核标准'],\r\n      ['月份列', '每月的计划、完成情况、得分'],\r\n      [''],\r\n      ['注意事项'],\r\n      ['1. 只包含选中指标的数据行'],\r\n      ['2. 包含完整的2月-12月全年数据'],\r\n      ['3. 完成情况列为只读数据，请勿修改']\r\n    ];\r\n\r\n    return XLSX.utils.aoa_to_sheet(summaryData);\r\n  }\r\n\r\n  // 记录选择性下载历史\r\n  recordSelectiveDownloadHistory(selectionData, result) {\r\n    const { selectedIndicators, format, statistics } = selectionData;\r\n\r\n    this.downloadHistory.push({\r\n      type: 'selective',\r\n      timestamp: new Date(),\r\n      selectedIndicators: selectedIndicators,\r\n      indicatorCount: selectedIndicators.length,\r\n      totalIndicators: statistics.totalItems,\r\n      format: format,\r\n      filename: result.filename,\r\n      success: result.success\r\n    });\r\n\r\n    // 保持历史记录不超过100条\r\n    if (this.downloadHistory.length > 100) {\r\n      this.downloadHistory = this.downloadHistory.slice(-100);\r\n    }\r\n  }\r\n}\r\n\r\nconst kpiDownloadService = new KPIDownloadService();\r\nexport default kpiDownloadService;"], "mappings": "+RAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAC5B,MAAO,CAAAC,UAAU,KAAM,cAAc,CAErC,KAAM,CAAAC,kBAAmB,CACvBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,eAAe,CAAG,EAAE,CAC3B,CAEA;AACA,KAAM,CAAAC,uBAAuBA,CAACC,aAAa,CAAE,CAC3C,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEF,aAAa,CAAC,CAE3C,KAAM,CAAEG,YAAY,CAAEC,kBAAkB,CAAEC,MAAM,CAAEC,gBAAgB,CAAEC,UAAW,CAAC,CAAGP,aAAa,CAEhG;AACA,GAAI,CAACG,YAAY,EAAIA,YAAY,CAACK,MAAM,GAAK,CAAC,CAAE,CAC9C,KAAM,IAAI,CAAAC,KAAK,CAAC,UAAU,CAAC,CAC7B,CAEA,GAAI,CAACL,kBAAkB,EAAIA,kBAAkB,CAACI,MAAM,GAAK,CAAC,CAAE,CAC1D,KAAM,IAAI,CAAAC,KAAK,CAAC,UAAU,CAAC,CAC7B,CAEA;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,KAAI,CAACC,qBAAqB,CAACR,YAAY,CAAEH,aAAa,CAAC,CAE5E;AACA,IAAI,CAACY,8BAA8B,CAACZ,aAAa,CAAEU,MAAM,CAAC,CAE1D,MAAO,CACLG,OAAO,CAAE,IAAI,CACbC,OAAO,6BAAAC,MAAA,CAAUX,kBAAkB,CAACI,MAAM,yCAAS,CACnDQ,QAAQ,CAAEN,MAAM,CAACM,QAAQ,CACzBC,WAAW,CAAEP,MAAM,CAACO,WACtB,CAAC,CAEH,CAAE,MAAOC,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,MAAO,CACLL,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEI,KAAK,CAACJ,OACjB,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAK,iBAAiBA,CAACnB,aAAa,CAAE,CACrC,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEF,aAAa,CAAC,CAExC;AACA,KAAM,CAAAoB,UAAU,CAAG,IAAI,CAACC,iBAAiB,CAACrB,aAAa,CAAC,CACxD,GAAI,CAACoB,UAAU,CAACE,KAAK,CAAE,CACrB,KAAM,IAAI,CAAAb,KAAK,CAACW,UAAU,CAACN,OAAO,CAAC,CACrC,CAEA;AACA,KAAM,CAAAS,YAAY,CAAG,IAAI,CAACC,qBAAqB,CAACxB,aAAa,CAAC,CAE9D,GAAIuB,YAAY,CAACf,MAAM,GAAK,CAAC,CAAE,CAC7B,KAAM,IAAI,CAAAC,KAAK,CAAC,eAAe,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,KAAI,CAACe,YAAY,CAACF,YAAY,CAAEvB,aAAa,CAAC,CAEnE;AACA,IAAI,CAAC0B,qBAAqB,CAAC1B,aAAa,CAAEU,MAAM,CAAC,CAEjD,MAAO,CACLG,OAAO,CAAE,IAAI,CACbC,OAAO,6BAAAC,MAAA,CAAUQ,YAAY,CAACf,MAAM,0BAAS,CAC7CQ,QAAQ,CAAEN,MAAM,CAACM,QAAQ,CACzBC,WAAW,CAAEP,MAAM,CAACO,WACtB,CAAC,CAEH,CAAE,MAAOC,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,CACLL,OAAO,CAAE,KAAK,CACdC,OAAO,CAAEI,KAAK,CAACJ,OACjB,CAAC,CACH,CACF,CAEA;AACAO,iBAAiBA,CAACrB,aAAa,CAAE,CAC/B,GAAI,CAACA,aAAa,CAAE,CAClB,MAAO,CAAEsB,KAAK,CAAE,KAAK,CAAER,OAAO,CAAE,UAAW,CAAC,CAC9C,CAEA,GAAI,CAACd,aAAa,CAAC2B,KAAK,EAAI3B,aAAa,CAAC2B,KAAK,CAACnB,MAAM,GAAK,CAAC,CAAE,CAC5D,MAAO,CAAEc,KAAK,CAAE,KAAK,CAAER,OAAO,CAAE,cAAe,CAAC,CAClD,CAEA,GAAI,CAACd,aAAa,CAAC4B,UAAU,EAAI5B,aAAa,CAAC4B,UAAU,CAACpB,MAAM,GAAK,CAAC,CAAE,CACtE,MAAO,CAAEc,KAAK,CAAE,KAAK,CAAER,OAAO,CAAE,YAAa,CAAC,CAChD,CAEA,KAAM,CAACe,UAAU,CAAEC,WAAW,CAAC,CAAG9B,aAAa,CAAC4B,UAAU,CAC1D,GAAIC,UAAU,CAAG,CAAC,EAAIA,UAAU,CAAG,EAAE,EAAIC,WAAW,CAAG,CAAC,EAAIA,WAAW,CAAG,EAAE,CAAE,CAC5E,MAAO,CAAER,KAAK,CAAE,KAAK,CAAER,OAAO,CAAE,QAAS,CAAC,CAC5C,CAEA,GAAIgB,WAAW,EAAID,UAAU,CAAE,CAC7B,MAAO,CAAEP,KAAK,CAAE,KAAK,CAAER,OAAO,CAAE,cAAe,CAAC,CAClD,CAEA,MAAO,CAAEQ,KAAK,CAAE,IAAK,CAAC,CACxB,CAEA;AACAE,qBAAqBA,CAACxB,aAAa,CAAE,CACnC,KAAM,CAAE2B,KAAK,CAAEI,SAAS,CAAEC,YAAY,CAAEJ,UAAU,CAAEK,IAAK,CAAC,CAAGjC,aAAa,CAE1E;AACA,GAAI,CAAAuB,YAAY,CAAGU,IAAI,CAACC,MAAM,CAAC,CAACC,CAAC,CAAEC,KAAK,GAAKT,KAAK,CAACU,QAAQ,CAACD,KAAK,CAAC,CAAC,CAEnE;AACA,GAAIL,SAAS,EAAIA,SAAS,CAACvB,MAAM,CAAG,CAAC,CAAE,CACrCe,YAAY,CAAGA,YAAY,CAACW,MAAM,CAACI,IAAI,EAAI,CACzC,MAAO,CAAAP,SAAS,CAACQ,IAAI,CAACC,IAAI,EAAI,IAAI,CAACC,aAAa,CAACH,IAAI,CAAEE,IAAI,CAAC,CAAC,CAC/D,CAAC,CAAC,CACJ,CAEA;AACA,GAAIR,YAAY,EAAIA,YAAY,CAACxB,MAAM,CAAG,CAAC,CAAE,CAC3Ce,YAAY,CAAGA,YAAY,CAACW,MAAM,CAACI,IAAI,EAAI,CACzC,MAAO,CAAAN,YAAY,CAACK,QAAQ,CAACC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAIN,YAAY,CAACK,QAAQ,CAACC,IAAI,CAACI,IAAI,CAAC,CACnF,CAAC,CAAC,CACJ,CAEA;AACA,MAAO,KAAI,CAACC,wBAAwB,CAACpB,YAAY,CAAEK,UAAU,CAAC,CAChE,CAEA;AACAa,aAAaA,CAACH,IAAI,CAAEE,IAAI,CAAE,CACxB,KAAM,CAAAI,SAAS,CAAGN,IAAI,CAACO,EAAE,EAAI,EAAE,CAE/B,OAAQL,IAAI,EACV,IAAK,MAAM,CACT,MAAO,CAAAI,SAAS,CAACP,QAAQ,CAAC,KAAK,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EACrDO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,KAAK,CAAC,CAC9D,IAAK,MAAM,CACT,MAAO,CAAAO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EACpDO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,CAC7D,IAAK,MAAM,CACT,MAAO,CAAAO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,KAAK,CAAC,EACrDO,SAAS,CAACP,QAAQ,CAAC,KAAK,CAAC,EAAIO,SAAS,CAACP,QAAQ,CAAC,IAAI,CAAC,CAC9D,IAAK,IAAI,CACP,MAAO,CAAC,IAAI,CAACI,aAAa,CAACH,IAAI,CAAE,MAAM,CAAC,EACjC,CAAC,IAAI,CAACG,aAAa,CAACH,IAAI,CAAE,MAAM,CAAC,EACjC,CAAC,IAAI,CAACG,aAAa,CAACH,IAAI,CAAE,MAAM,CAAC,CAC1C,QACE,MAAO,KAAI,CACf,CACF,CAEA;AACAK,wBAAwBA,CAACV,IAAI,CAAEL,UAAU,CAAE,CACzC,KAAM,CAACC,UAAU,CAAEC,WAAW,CAAC,CAAGF,UAAU,CAE5C,MAAO,CAAAK,IAAI,CAACa,GAAG,CAACC,GAAG,EAAI,CACrB;AACA,KAAM,CAAEC,OAAO,CAAEC,aAAa,CAAEC,YAA0B,CAAC,CAAGH,GAAG,CAAhBI,QAAQ,CAAAC,wBAAA,CAAKL,GAAG,CAAAM,SAAA,EAEjE;AACA,KAAM,CAAA3C,MAAM,CAAG,CACb4C,EAAE,CAAEH,QAAQ,CAACG,EAAE,CACfT,EAAE,CAAEM,QAAQ,CAACN,EAAE,CACfU,GAAG,CAAEJ,QAAQ,CAACI,GAAG,CACjBC,EAAE,CAAEL,QAAQ,CAACK,EAAE,CACfd,IAAI,CAAES,QAAQ,CAACT,IAAI,EAAIS,QAAQ,CAAC,SAAS,CAAC,CAC1CM,IAAI,CAAEN,QAAQ,CAACM,IACjB,CAAC,CAED;AACA/C,MAAM,IAAAK,MAAA,CAAIc,UAAU,WAAI,CAAGsB,QAAQ,IAAApC,MAAA,CAAIc,UAAU,WAAI,KAAAd,MAAA,CAAOc,UAAU,UAAG,CACzEnB,MAAM,IAAAK,MAAA,CAAIc,UAAU,mCAAQ,CAAGsB,QAAQ,IAAApC,MAAA,CAAIc,UAAU,mCAAQ,EAAI,EAAE,CACnEnB,MAAM,IAAAK,MAAA,CAAIc,UAAU,uBAAM,CAAGsB,QAAQ,IAAApC,MAAA,CAAIc,UAAU,uBAAM,EAAI,EAAE,CAC/DnB,MAAM,IAAAK,MAAA,CAAIe,WAAW,WAAI,CAAGqB,QAAQ,IAAApC,MAAA,CAAIe,WAAW,WAAI,KAAAf,MAAA,CAAOe,WAAW,UAAG,CAC5EpB,MAAM,IAAAK,MAAA,CAAIe,WAAW,mCAAQ,CAAGqB,QAAQ,IAAApC,MAAA,CAAIe,WAAW,mCAAQ,EAAI,EAAE,CACrEpB,MAAM,IAAAK,MAAA,CAAIe,WAAW,uBAAM,CAAGqB,QAAQ,IAAApC,MAAA,CAAIe,WAAW,uBAAM,EAAI,EAAE,CAEjE,MAAO,CAAApB,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAe,YAAYA,CAACQ,IAAI,CAAEjC,aAAa,CAAE,CACtC,KAAM,CAAEK,MAAM,CAAEuB,UAAW,CAAC,CAAG5B,aAAa,CAC5C,KAAM,CAAC6B,UAAU,CAAEC,WAAW,CAAC,CAAGF,UAAU,CAC5C,KAAM,CAAA8B,SAAS,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,OAAO,CAAE,GAAG,CAAC,CAACC,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAE7E,GAAIzD,MAAM,GAAK,OAAO,CAAE,CACtB,MAAO,MAAM,KAAI,CAAC0D,iBAAiB,CAAC9B,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAE4B,SAAS,CAAC,CAC/E,CAAC,IAAM,CACL,MAAO,MAAM,KAAI,CAACM,eAAe,CAAC/B,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAE4B,SAAS,CAAC,CAC7E,CACF,CAEA;AACA,KAAM,CAAAK,iBAAiBA,CAAC9B,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAE4B,SAAS,CAAE,CAChE,GAAI,CACF;AACA,KAAM,CAAAO,QAAQ,CAAGvE,IAAI,CAACwE,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAC,OAAO,CAAG,CACd,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,QAAQ,CAAE,SAAS,CAAE,MAAM,IAAArD,MAAA,CAC3Cc,UAAU,cAAAd,MAAA,CAAQc,UAAU,sCAAAd,MAAA,CAAYc,UAAU,0BAAAd,MAAA,CAClDe,WAAW,cAAAf,MAAA,CAAQe,WAAW,sCAAAf,MAAA,CAAYe,WAAW,uBACzD,CAED;AACA,KAAM,CAAAuC,MAAM,CAAG,CAACD,OAAO,CAAC,CAExBnC,IAAI,CAACqC,OAAO,CAACvB,GAAG,EAAI,CAClB,KAAM,CAAAwB,OAAO,CAAG,CACdxB,GAAG,CAACO,EAAE,CACNP,GAAG,CAACF,EAAE,CACNE,GAAG,CAACQ,GAAG,CACPR,GAAG,CAACS,EAAE,CACNT,GAAG,CAACL,IAAI,CACRK,GAAG,CAACU,IAAI,CACRV,GAAG,IAAAhC,MAAA,CAAIc,UAAU,WAAI,CACrBkB,GAAG,IAAAhC,MAAA,CAAIc,UAAU,mCAAQ,CACzBkB,GAAG,IAAAhC,MAAA,CAAIc,UAAU,uBAAM,CACvBkB,GAAG,IAAAhC,MAAA,CAAIe,WAAW,WAAI,CACtBiB,GAAG,IAAAhC,MAAA,CAAIe,WAAW,mCAAQ,CAC1BiB,GAAG,IAAAhC,MAAA,CAAIe,WAAW,uBAAM,CACzB,CACDuC,MAAM,CAACG,IAAI,CAACD,OAAO,CAAC,CACtB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,SAAS,CAAG/E,IAAI,CAACwE,KAAK,CAACQ,YAAY,CAACL,MAAM,CAAC,CAEjD;AACA,KAAM,CAAAM,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,CAAE,CAAC,CAAI;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAI;AAAA,CACf,CACDH,SAAS,CAAC,OAAO,CAAC,CAAGE,SAAS,CAE9B;AACA,KAAM,CAAAE,UAAU,CAAGR,MAAM,CAACvB,GAAG,CAAC,KAAO,CAAEgC,GAAG,CAAE,EAAG,CAAC,CAAC,CAAC,CAClDL,SAAS,CAAC,OAAO,CAAC,CAAGI,UAAU,CAE/B;AACA,IAAI,CAACE,cAAc,CAACN,SAAS,CAAEL,OAAO,CAAC5D,MAAM,CAAC,CAE9C;AACAd,IAAI,CAACwE,KAAK,CAACc,iBAAiB,CAACf,QAAQ,CAAEQ,SAAS,oBAAA1D,MAAA,CAAWc,UAAU,MAAAd,MAAA,CAAIe,WAAW,UAAG,CAAC,CAExF;AACA,IAAI,CAACmD,kBAAkB,CAAChB,QAAQ,CAAEhC,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAC,CAEhE;AACA,KAAM,CAAAd,QAAQ,gCAAAD,MAAA,CAAcc,UAAU,MAAAd,MAAA,CAAIe,WAAW,YAAAf,MAAA,CAAK2C,SAAS,SAAO,CAE1E;AACA,KAAM,CAAAwB,WAAW,CAAGxF,IAAI,CAACyF,KAAK,CAAClB,QAAQ,CAAE,CACvCmB,QAAQ,CAAE,MAAM,CAChB5C,IAAI,CAAE,OAAO,CACb6C,WAAW,CAAE,IACf,CAAC,CAAC,CAEF;AACA,IAAI,CAACC,YAAY,CAACJ,WAAW,CAAElE,QAAQ,CAAE,mEAAmE,CAAC,CAE7G,MAAO,CACLA,QAAQ,CAAEA,QAAQ,CAClBC,WAAW,kFAAAF,MAAA,CAAmF,IAAI,CAACwE,mBAAmB,CAACL,WAAW,CAAC,CACrI,CAAC,CAEH,CAAE,MAAOhE,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,KAAM,IAAI,CAAAT,KAAK,CAAC,eAAe,CAAGS,KAAK,CAACJ,OAAO,CAAC,CAClD,CACF,CAEA;AACA,KAAM,CAAAkD,eAAeA,CAAC/B,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAE4B,SAAS,CAAE,CAC9D,GAAI,CACF;AACA,KAAM,CAAAU,OAAO,CAAG,CACd,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,IAAI,CAAE,MAAM,CAAE,MAAM,IAAArD,MAAA,CACpCc,UAAU,cAAAd,MAAA,CAAQc,UAAU,sCAAAd,MAAA,CAAYc,UAAU,0BAAAd,MAAA,CAClDe,WAAW,cAAAf,MAAA,CAAQe,WAAW,sCAAAf,MAAA,CAAYe,WAAW,uBACzD,CAED;AACA,GAAI,CAAA0D,UAAU,CAAGpB,OAAO,CAACqB,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CAEzCxD,IAAI,CAACqC,OAAO,CAACvB,GAAG,EAAI,CAClB,KAAM,CAAAwB,OAAO,CAAG,CACd,IAAI,CAACmB,cAAc,CAAC3C,GAAG,CAACO,EAAE,CAAC,CAC3B,IAAI,CAACoC,cAAc,CAAC3C,GAAG,CAACF,EAAE,CAAC,CAC3B,IAAI,CAAC6C,cAAc,CAAC3C,GAAG,CAACQ,GAAG,CAAC,CAC5B,IAAI,CAACmC,cAAc,CAAC3C,GAAG,CAACS,EAAE,CAAC,CAC3B,IAAI,CAACkC,cAAc,CAAC3C,GAAG,CAACL,IAAI,CAAC,CAC7B,IAAI,CAACgD,cAAc,CAAC3C,GAAG,CAACU,IAAI,CAAC,CAC7B,IAAI,CAACiC,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIc,UAAU,WAAI,CAAC,CAC1C,IAAI,CAAC6D,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIc,UAAU,mCAAQ,CAAC,CAC9C,IAAI,CAAC6D,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIc,UAAU,uBAAM,CAAC,CAC5C,IAAI,CAAC6D,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIe,WAAW,WAAI,CAAC,CAC3C,IAAI,CAAC4D,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIe,WAAW,mCAAQ,CAAC,CAC/C,IAAI,CAAC4D,cAAc,CAAC3C,GAAG,IAAAhC,MAAA,CAAIe,WAAW,uBAAM,CAAC,CAC9C,CACD0D,UAAU,EAAIjB,OAAO,CAACkB,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CACxC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,GAAG,CAAG,QAAQ,CACpB,KAAM,CAAAC,YAAY,CAAGD,GAAG,CAAGH,UAAU,CAErC;AACA,KAAM,CAAAxE,QAAQ,gCAAAD,MAAA,CAAcc,UAAU,MAAAd,MAAA,CAAIe,WAAW,YAAAf,MAAA,CAAK2C,SAAS,QAAM,CAEzE;AACA,IAAI,CAAC4B,YAAY,CAACM,YAAY,CAAE5E,QAAQ,CAAE,wBAAwB,CAAC,CAEnE,MAAO,CACLA,QAAQ,CAAEA,QAAQ,CAClBC,WAAW,gCAAAF,MAAA,CAAiC8E,kBAAkB,CAACD,YAAY,CAAC,CAC9E,CAAC,CAEH,CAAE,MAAO1E,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,IAAI,CAAAT,KAAK,CAAC,aAAa,CAAGS,KAAK,CAACJ,OAAO,CAAC,CAChD,CACF,CAEA;AACA4E,cAAcA,CAACI,KAAK,CAAE,CACpB,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,CAAE,CACzC,MAAO,EAAE,CACX,CAEA,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACH,KAAK,CAAC,CAEjC;AACA,GAAIE,WAAW,CAAC3D,QAAQ,CAAC,GAAG,CAAC,EAAI2D,WAAW,CAAC3D,QAAQ,CAAC,GAAG,CAAC,EAAI2D,WAAW,CAAC3D,QAAQ,CAAC,IAAI,CAAC,CAAE,CACxF,MAAO,GAAG,CAAG2D,WAAW,CAACnC,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,CAAG,GAAG,CACpD,CAEA,MAAO,CAAAmC,WAAW,CACpB,CAEA;AACAV,YAAYA,CAACY,OAAO,CAAElF,QAAQ,CAAEmF,QAAQ,CAAE,CACxC,GAAI,CACF,GAAI,CAAAC,IAAI,CAER,GAAIF,OAAO,WAAY,CAAAG,WAAW,CAAE,CAClCD,IAAI,CAAG,GAAI,CAAAE,IAAI,CAAC,CAACJ,OAAO,CAAC,CAAE,CAAE1D,IAAI,CAAE2D,QAAS,CAAC,CAAC,CAChD,CAAC,IAAM,CACLC,IAAI,CAAG,GAAI,CAAAE,IAAI,CAAC,CAACJ,OAAO,CAAC,CAAE,CAAE1D,IAAI,CAAE2D,QAAS,CAAC,CAAC,CAChD,CAEA,KAAM,CAAAI,GAAG,CAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC,CAC5C,KAAM,CAAAO,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGP,GAAG,CACfI,IAAI,CAACI,QAAQ,CAAG/F,QAAQ,CACxB2F,IAAI,CAACK,KAAK,CAACC,OAAO,CAAG,MAAM,CAE3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CAE/B;AACAW,UAAU,CAAC,IAAMd,MAAM,CAACC,GAAG,CAACc,eAAe,CAAChB,GAAG,CAAC,CAAE,GAAG,CAAC,CAExD,CAAE,MAAOrF,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,IAAI,CAAAT,KAAK,CAAC,UAAU,CAAGS,KAAK,CAACJ,OAAO,CAAC,CAC7C,CACF,CAEA;AACAyE,mBAAmBA,CAACiC,MAAM,CAAE,CAC1B,GAAI,CAAAC,MAAM,CAAG,EAAE,CACf,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAC,UAAU,CAACH,MAAM,CAAC,CACpC,KAAM,CAAAI,GAAG,CAAGF,KAAK,CAACG,UAAU,CAC5B,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGF,GAAG,CAAEE,CAAC,EAAE,CAAE,CAC5BL,MAAM,EAAIxB,MAAM,CAAC8B,YAAY,CAACL,KAAK,CAACI,CAAC,CAAC,CAAC,CACzC,CACA,MAAO,CAAAtB,MAAM,CAACwB,IAAI,CAACP,MAAM,CAAC,CAC5B,CAEA;AACA/F,qBAAqBA,CAAC1B,aAAa,CAAEU,MAAM,CAAE,CAC3C,KAAM,CAAAuH,aAAa,CAAG,CACpBvE,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC5C,QAAQ,CAAEN,MAAM,CAACM,QAAQ,CACzBkH,SAAS,CAAElI,aAAa,CAAC2B,KAAK,CAACnB,MAAM,CACrCoB,UAAU,CAAE5B,aAAa,CAAC4B,UAAU,CACpCvB,MAAM,CAAEL,aAAa,CAACK,MAAM,CAC5B0B,SAAS,CAAE/B,aAAa,CAAC+B,SAAS,CAClCC,YAAY,CAAEhC,aAAa,CAACgC,YAC9B,CAAC,CAED,IAAI,CAAClC,eAAe,CAAC0E,IAAI,CAACyD,aAAa,CAAC,CAExC;AACA,GAAI,IAAI,CAACnI,eAAe,CAACU,MAAM,CAAG,GAAG,CAAE,CACrC,IAAI,CAACV,eAAe,CAAG,IAAI,CAACA,eAAe,CAACgE,KAAK,CAAC,CAAC,GAAG,CAAC,CACzD,CAEA7D,OAAO,CAACC,GAAG,CAAC,YAAY,CAAE+H,aAAa,CAAC,CAC1C,CAEA;AACAE,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CAAC,GAAG,IAAI,CAACrI,eAAe,CAAC,CAACsI,OAAO,CAAC,CAAC,CAAE;AAC9C,CAEA;AACAC,oBAAoBA,CAAA,CAAG,CACrB,IAAI,CAACvI,eAAe,CAAG,EAAE,CACzBG,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC,CACxB,CAEA;AACAoI,gBAAgBA,CAAA,CAAG,CACjB,KAAM,CAAAC,KAAK,CAAG,CACZC,cAAc,CAAE,IAAI,CAAC1I,eAAe,CAACU,MAAM,CAC3CiI,cAAc,CAAE,IAAI,CAAC3I,eAAe,CAACoC,MAAM,CAACwG,MAAM,EAAIA,MAAM,CAACrI,MAAM,GAAK,OAAO,CAAC,CAACG,MAAM,CACvFmI,YAAY,CAAE,IAAI,CAAC7I,eAAe,CAACoC,MAAM,CAACwG,MAAM,EAAIA,MAAM,CAACrI,MAAM,GAAK,KAAK,CAAC,CAACG,MAAM,CACnFoI,UAAU,CAAE,IAAI,CAAC9I,eAAe,CAAC+I,MAAM,CAAC,CAACC,GAAG,CAAEJ,MAAM,GAAKI,GAAG,CAAGJ,MAAM,CAACR,SAAS,CAAE,CAAC,CACpF,CAAC,CAED,GAAI,IAAI,CAACpI,eAAe,CAACU,MAAM,CAAG,CAAC,CAAE,CACnC+H,KAAK,CAACQ,YAAY,CAAG,IAAI,CAACjJ,eAAe,CAAC,IAAI,CAACA,eAAe,CAACU,MAAM,CAAG,CAAC,CAAC,CAACkD,SAAS,CACpF6E,KAAK,CAACS,cAAc,CAAGT,KAAK,CAACE,cAAc,EAAIF,KAAK,CAACI,YAAY,CAAG,OAAO,CAAG,KAAK,CACrF,CAEA,MAAO,CAAAJ,KAAK,CACd,CAEA;AACAxD,cAAcA,CAACN,SAAS,CAAEwE,QAAQ,CAAE,CAClC,IAAK,GAAI,CAAAnB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGmB,QAAQ,CAAEnB,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAoB,OAAO,CAAGxJ,IAAI,CAACwE,KAAK,CAACiF,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAEvB,CAAE,CAAC,CAAC,CACtD,GAAI,CAACrD,SAAS,CAACyE,OAAO,CAAC,CAAE,SAEzBzE,SAAS,CAACyE,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CAEA;AACA9E,kBAAkBA,CAAChB,QAAQ,CAAEhC,IAAI,CAAEJ,UAAU,CAAEC,WAAW,CAAE,CAC1D,KAAM,CAAAkI,WAAW,CAAG,CAClB,CAAC,WAAW,CAAC,CACb,CAAC,EAAE,CAAC,CACJ,CAAC,MAAM,CAAE,IAAI,CAACC,cAAc,CAAC,GAAI,CAAAtG,IAAI,CAAC,CAAC,CAAC,CAAC,CACzC,CAAC,MAAM,IAAA5C,MAAA,CAAKc,UAAU,cAAAd,MAAA,CAAOe,WAAW,WAAI,CAC5C,CAAC,QAAQ,CAAEG,IAAI,CAACzB,MAAM,CAAC,CACvB,CAAC,EAAE,CAAC,CACJ,CAAC,MAAM,CAAC,CACR,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CACnB,CAED;AACA,KAAM,CAAAoI,UAAU,CAAG3G,IAAI,CAACzB,MAAM,CAC9B,KAAM,CAAA0J,mBAAmB,CAAGjI,IAAI,CAACC,MAAM,CAACI,IAAI,EAAIA,IAAI,IAAAvB,MAAA,CAAIc,UAAU,WAAI,EAAIS,IAAI,IAAAvB,MAAA,CAAIc,UAAU,WAAI,GAAK,EAAE,CAAC,CAACrB,MAAM,CAC/G,KAAM,CAAA2J,oBAAoB,CAAGlI,IAAI,CAACC,MAAM,CAACI,IAAI,EAAIA,IAAI,IAAAvB,MAAA,CAAIe,WAAW,WAAI,EAAIQ,IAAI,IAAAvB,MAAA,CAAIe,WAAW,WAAI,GAAK,EAAE,CAAC,CAACtB,MAAM,CAClH,KAAM,CAAA4J,mBAAmB,CAAGnI,IAAI,CAACC,MAAM,CAACI,IAAI,EAAIA,IAAI,IAAAvB,MAAA,CAAIc,UAAU,uBAAM,EAAIS,IAAI,IAAAvB,MAAA,CAAIc,UAAU,uBAAM,GAAK,EAAE,CAAC,CAACrB,MAAM,CACnH,KAAM,CAAA6J,oBAAoB,CAAGpI,IAAI,CAACC,MAAM,CAACI,IAAI,EAAIA,IAAI,IAAAvB,MAAA,CAAIe,WAAW,uBAAM,EAAIQ,IAAI,IAAAvB,MAAA,CAAIe,WAAW,uBAAM,GAAK,EAAE,CAAC,CAACtB,MAAM,CAEtHwJ,WAAW,CAACxF,IAAI,CACd,IAAAzD,MAAA,CAAIc,UAAU,6BAAQqI,mBAAmB,IAAAnJ,MAAA,CAAK,CAAEmJ,mBAAmB,CAAGtB,UAAU,CAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,MAAI,CACvG,IAAAvJ,MAAA,CAAIe,WAAW,6BAAQqI,oBAAoB,IAAApJ,MAAA,CAAK,CAAEoJ,oBAAoB,CAAGvB,UAAU,CAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,MAAI,CAC1G,IAAAvJ,MAAA,CAAIc,UAAU,6BAAQuI,mBAAmB,IAAArJ,MAAA,CAAK,CAAEqJ,mBAAmB,CAAGxB,UAAU,CAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,MAAI,CACvG,IAAAvJ,MAAA,CAAIe,WAAW,6BAAQuI,oBAAoB,IAAAtJ,MAAA,CAAK,CAAEsJ,oBAAoB,CAAGzB,UAAU,CAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,MACxG,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAG7K,IAAI,CAACwE,KAAK,CAACQ,YAAY,CAACsF,WAAW,CAAC,CAE7D;AACAO,gBAAgB,CAAC,OAAO,CAAC,CAAG,CAC1B,CAAE3F,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACZ,CAED;AACA,KAAM,CAAA4F,YAAY,CAAG9K,IAAI,CAACwE,KAAK,CAACiF,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC3D,GAAIkB,gBAAgB,CAACC,YAAY,CAAC,CAAE,CAClCD,gBAAgB,CAACC,YAAY,CAAC,CAAClB,CAAC,CAAG,CACjCC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEiB,IAAI,CAAE,EAAE,CAAEhB,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CACxDC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CAEA;AACArK,IAAI,CAACwE,KAAK,CAACc,iBAAiB,CAACf,QAAQ,CAAEsG,gBAAgB,CAAE,MAAM,CAAC,CAClE,CAEA;AACAN,cAAcA,CAACS,IAAI,CAAE,CACnB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAG5E,MAAM,CAACyE,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAG/E,MAAM,CAACyE,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAG,KAAK,CAAGjF,MAAM,CAACyE,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAK,OAAO,CAAGnF,MAAM,CAACyE,IAAI,CAACW,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,SAAAhK,MAAA,CAAU4J,IAAI,MAAA5J,MAAA,CAAI8J,KAAK,MAAA9J,MAAA,CAAIiK,GAAG,MAAAjK,MAAA,CAAImK,KAAK,MAAAnK,MAAA,CAAIqK,OAAO,EACpD,CAEA;AACAE,UAAUA,CAACZ,IAAI,CAAE,CACf,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAG5E,MAAM,CAACyE,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAG/E,MAAM,CAACyE,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAG,KAAK,CAAGjF,MAAM,CAACyE,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAK,OAAO,CAAGnF,MAAM,CAACyE,IAAI,CAACW,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,SAAAhK,MAAA,CAAU4J,IAAI,EAAA5J,MAAA,CAAG8J,KAAK,EAAA9J,MAAA,CAAGiK,GAAG,MAAAjK,MAAA,CAAImK,KAAK,EAAAnK,MAAA,CAAGqK,OAAO,EACjD,CAEA;AACA,KAAM,CAAAzK,qBAAqBA,CAACR,YAAY,CAAEH,aAAa,CAAE,CACvD,KAAM,CAAEK,MAAO,CAAC,CAAGL,aAAa,CAEhC,OAAQK,MAAM,EACZ,IAAK,OAAO,CACV,MAAO,MAAM,KAAI,CAACkL,0BAA0B,CAACpL,YAAY,CAAEH,aAAa,CAAC,CAC3E,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACwL,wBAAwB,CAACrL,YAAY,CAAEH,aAAa,CAAC,CACzE,QACE,KAAM,IAAI,CAAAS,KAAK,0CAAAM,MAAA,CAAYV,MAAM,CAAE,CAAC,CACxC,CACF,CAEA;AACA,KAAM,CAAAkL,0BAA0BA,CAACpL,YAAY,CAAEH,aAAa,CAAE,CAC5D,GAAI,CACF,KAAM,CAAEI,kBAAkB,CAAEG,UAAW,CAAC,CAAGP,aAAa,CAExD;AACA,KAAM,CAAAiE,QAAQ,CAAGvE,IAAI,CAACwE,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAsH,YAAY,CAAG,IAAI,CAACC,mBAAmB,CAACvL,YAAY,CAAC,CAE3D;AACA,KAAM,CAAAwL,SAAS,CAAG,IAAI,CAACC,wBAAwB,CAACH,YAAY,CAAC,CAC7D/L,IAAI,CAACwE,KAAK,CAACc,iBAAiB,CAACf,QAAQ,CAAE0H,SAAS,CAAE,OAAO,CAAC,CAE1D;AACA,KAAM,CAAAE,YAAY,CAAG,IAAI,CAACC,2BAA2B,CAAC3L,YAAY,CAAEH,aAAa,CAAC,CAClFN,IAAI,CAACwE,KAAK,CAACc,iBAAiB,CAACf,QAAQ,CAAE4H,YAAY,CAAE,MAAM,CAAC,CAE5D;AACA,KAAM,CAAA7K,QAAQ,sCAAAD,MAAA,CAAeX,kBAAkB,CAACI,MAAM,wBAAAO,MAAA,CAAO,IAAI,CAACuK,UAAU,CAAC,GAAI,CAAA3H,IAAI,CAAC,CAAC,CAAC,SAAO,CAC/F,KAAM,CAAA6D,MAAM,CAAG9H,IAAI,CAACyF,KAAK,CAAClB,QAAQ,CAAE,CAAEzB,IAAI,CAAE,OAAO,CAAE4C,QAAQ,CAAE,MAAO,CAAC,CAAC,CAExE;AACA,IAAI,CAACE,YAAY,CAACkC,MAAM,CAAExG,QAAQ,CAAE,mEAAmE,CAAC,CAExG,MAAO,CACLH,OAAO,CAAE,IAAI,CACbG,QAAQ,CAAEA,QAAQ,CAClBC,WAAW,CAAE,IACf,CAAC,CAEH,CAAE,MAAOC,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAsK,wBAAwBA,CAACrL,YAAY,CAAEH,aAAa,CAAE,CAC1D,GAAI,CACF,KAAM,CAAEI,kBAAmB,CAAC,CAAGJ,aAAa,CAE5C;AACA,KAAM,CAAAyL,YAAY,CAAG,IAAI,CAACC,mBAAmB,CAACvL,YAAY,CAAC,CAE3D;AACA,KAAM,CAAA4L,MAAM,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACnD,KAAM,CAAA3H,OAAO,CAAG,CACd,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,IAAI,CAAE,SAAS,CAAE,MAAM,CAC3C,CAED;AACA2H,MAAM,CAACzH,OAAO,CAACuG,KAAK,EAAI,CACtBzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,gBAAI,CAAC,CAC1BzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,wCAAQ,CAAC,CAC9BzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,sBAAK,CAAC,CAC7B,CAAC,CAAC,CAEF;AACA,GAAI,CAAArF,UAAU,CAAG,QAAQ,CAAE;AAC3BA,UAAU,EAAIpB,OAAO,CAACtB,GAAG,CAACkJ,MAAM,OAAAjL,MAAA,CAAQiL,MAAM,MAAG,CAAC,CAACvG,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CAEnE;AACAgG,YAAY,CAACnH,OAAO,CAAChC,IAAI,EAAI,CAC3B,KAAM,CAAAS,GAAG,CAAG,CACVT,IAAI,CAACgB,EAAE,EAAI,EAAE,CACbhB,IAAI,CAACO,EAAE,EAAI,EAAE,CACbP,IAAI,CAACiB,GAAG,EAAI,EAAE,CACdjB,IAAI,CAACkB,EAAE,EAAI,EAAE,CACblB,IAAI,CAAC,SAAS,CAAC,EAAI,EAAE,CACrBA,IAAI,CAACmB,IAAI,EAAI,EAAE,CAChB,CAED;AACAsI,MAAM,CAACzH,OAAO,CAACuG,KAAK,EAAI,CACtB9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,iBAAK,EAAI,EAAE,CAAC,CAClC9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,yCAAS,EAAI,EAAE,CAAC,CACtC9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,uBAAM,EAAI,EAAE,CAAC,CACrC,CAAC,CAAC,CAEFrF,UAAU,EAAIzC,GAAG,CAACD,GAAG,CAACmJ,IAAI,OAAAlL,MAAA,CAAQkF,MAAM,CAACgG,IAAI,CAAC,CAACpI,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,MAAG,CAAC,CAAC4B,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CACzF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAzE,QAAQ,sCAAAD,MAAA,CAAeX,kBAAkB,CAACI,MAAM,wBAAAO,MAAA,CAAO,IAAI,CAACuK,UAAU,CAAC,GAAI,CAAA3H,IAAI,CAAC,CAAC,CAAC,QAAM,CAE9F;AACA,IAAI,CAAC2B,YAAY,CAACE,UAAU,CAAExE,QAAQ,CAAE,wBAAwB,CAAC,CAEjE,MAAO,CACLH,OAAO,CAAE,IAAI,CACbG,QAAQ,CAAEA,QAAQ,CAClBC,WAAW,CAAE,IACf,CAAC,CAEH,CAAE,MAAOC,KAAK,CAAE,CACdjB,OAAO,CAACiB,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAwK,mBAAmBA,CAACvL,YAAY,CAAE,CAChC,KAAM,CAAA4L,MAAM,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEnD,MAAO,CAAA5L,YAAY,CAAC2C,GAAG,CAACR,IAAI,EAAI,CAC9B,KAAM,CAAA4J,YAAY,CAAG,CACnB5I,EAAE,CAAEhB,IAAI,CAACgB,EAAE,EAAI,EAAE,CACjBT,EAAE,CAAEP,IAAI,CAACO,EAAE,EAAI,EAAE,CACjBU,GAAG,CAAEjB,IAAI,CAACiB,GAAG,EAAI,EAAE,CACnBC,EAAE,CAAElB,IAAI,CAACkB,EAAE,EAAI,EAAE,CACjB,SAAS,CAAElB,IAAI,CAAC,SAAS,CAAC,EAAIA,IAAI,CAACI,IAAI,EAAI,EAAE,CAC7Ce,IAAI,CAAEnB,IAAI,CAACmB,IAAI,EAAI,EACrB,CAAC,CAED;AACAsI,MAAM,CAACzH,OAAO,CAACuG,KAAK,EAAI,CACtBqB,YAAY,IAAAnL,MAAA,CAAI8J,KAAK,iBAAK,CAAGvI,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,iBAAK,EAAI,EAAE,CACrDqB,YAAY,IAAAnL,MAAA,CAAI8J,KAAK,yCAAS,CAAGvI,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,yCAAS,EAAI,EAAE,CAC7DqB,YAAY,IAAAnL,MAAA,CAAI8J,KAAK,uBAAM,CAAGvI,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,uBAAM,EAAI,EAAE,CACzD,CAAC,CAAC,CAEF,MAAO,CAAAqB,YAAY,CACrB,CAAC,CAAC,CACJ,CAEA;AACAN,wBAAwBA,CAACH,YAAY,CAAE,CACrC,KAAM,CAAAM,MAAM,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAEnD;AACA,KAAM,CAAA3H,OAAO,CAAG,CACd,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,IAAI,CAAE,SAAS,CAAE,MAAM,CAC3C,CAED;AACA2H,MAAM,CAACzH,OAAO,CAACuG,KAAK,EAAI,CACtBzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,gBAAI,CAAC,CAC1BzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,wCAAQ,CAAC,CAC9BzG,OAAO,CAACI,IAAI,IAAAzD,MAAA,CAAI8J,KAAK,sBAAK,CAAC,CAC7B,CAAC,CAAC,CAEF;AACA,KAAM,CAAAsB,IAAI,CAAG,CAAC/H,OAAO,CAAC,CACtBqH,YAAY,CAACnH,OAAO,CAAChC,IAAI,EAAI,CAC3B,KAAM,CAAAS,GAAG,CAAG,CACVT,IAAI,CAACgB,EAAE,CAAEhB,IAAI,CAACO,EAAE,CAAEP,IAAI,CAACiB,GAAG,CAAEjB,IAAI,CAACkB,EAAE,CACnClB,IAAI,CAAC,SAAS,CAAC,CAAEA,IAAI,CAACmB,IAAI,CAC3B,CAED;AACAsI,MAAM,CAACzH,OAAO,CAACuG,KAAK,EAAI,CACtB9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,iBAAK,CAAC,CAC5B9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,yCAAS,CAAC,CAChC9H,GAAG,CAACyB,IAAI,CAAClC,IAAI,IAAAvB,MAAA,CAAI8J,KAAK,uBAAM,CAAC,CAC/B,CAAC,CAAC,CAEFsB,IAAI,CAAC3H,IAAI,CAACzB,GAAG,CAAC,CAChB,CAAC,CAAC,CAEF,MAAO,CAAArD,IAAI,CAACwE,KAAK,CAACQ,YAAY,CAACyH,IAAI,CAAC,CACtC,CAEA;AACAL,2BAA2BA,CAAC3L,YAAY,CAAEH,aAAa,CAAE,CACvD,KAAM,CAAEI,kBAAkB,CAAEG,UAAW,CAAC,CAAGP,aAAa,CAExD,KAAM,CAAAgK,WAAW,CAAG,CAClB,CAAC,YAAY,CAAC,CACd,CAAC,EAAE,CAAC,CACJ,CAAC,MAAM,CAAE,IAAI,CAACsB,UAAU,CAAC,GAAI,CAAA3H,IAAI,CAAC,CAAC,CAAC,CAAC,CACrC,CAAC,QAAQ,CAAEvD,kBAAkB,CAACI,MAAM,CAAC,CACrC,CAAC,OAAO,CAAED,UAAU,CAACqI,UAAU,CAAC,CAChC,CAAC,MAAM,IAAA7H,MAAA,CAAK,CAAEX,kBAAkB,CAACI,MAAM,CAAGD,UAAU,CAACqI,UAAU,CAAI,GAAG,EAAE0B,OAAO,CAAC,CAAC,CAAC,MAAI,CACtF,CAAC,EAAE,CAAC,CACJ,CAAC,SAAS,CAAC,CACX,GAAGlK,kBAAkB,CAAC0C,GAAG,CAAC,CAACF,SAAS,CAAER,KAAK,GAAK,IAAArB,MAAA,CAAIqB,KAAK,CAAG,CAAC,OAAArB,MAAA,CAAK6B,SAAS,EAAG,CAAC,CAC/E,CAAC,EAAE,CAAC,CACJ,CAAC,QAAQ,CAAC,CACV,CAAC,MAAM,CAAE,gBAAgB,CAAC,CAC1B,CAAC,KAAK,CAAE,2BAA2B,CAAC,CACpC,CAAC,KAAK,CAAE,eAAe,CAAC,CACxB,CAAC,EAAE,CAAC,CACJ,CAAC,MAAM,CAAC,CACR,CAAC,gBAAgB,CAAC,CAClB,CAAC,oBAAoB,CAAC,CACtB,CAAC,oBAAoB,CAAC,CACvB,CAED,MAAO,CAAAlD,IAAI,CAACwE,KAAK,CAACQ,YAAY,CAACsF,WAAW,CAAC,CAC7C,CAEA;AACApJ,8BAA8BA,CAACZ,aAAa,CAAEU,MAAM,CAAE,CACpD,KAAM,CAAEN,kBAAkB,CAAEC,MAAM,CAAEE,UAAW,CAAC,CAAGP,aAAa,CAEhE,IAAI,CAACF,eAAe,CAAC0E,IAAI,CAAC,CACxBhC,IAAI,CAAE,WAAW,CACjBkB,SAAS,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CACrBvD,kBAAkB,CAAEA,kBAAkB,CACtCgM,cAAc,CAAEhM,kBAAkB,CAACI,MAAM,CACzC6L,eAAe,CAAE9L,UAAU,CAACqI,UAAU,CACtCvI,MAAM,CAAEA,MAAM,CACdW,QAAQ,CAAEN,MAAM,CAACM,QAAQ,CACzBH,OAAO,CAAEH,MAAM,CAACG,OAClB,CAAC,CAAC,CAEF;AACA,GAAI,IAAI,CAACf,eAAe,CAACU,MAAM,CAAG,GAAG,CAAE,CACrC,IAAI,CAACV,eAAe,CAAG,IAAI,CAACA,eAAe,CAACgE,KAAK,CAAC,CAAC,GAAG,CAAC,CACzD,CACF,CACF,CAEA,KAAM,CAAAwI,kBAAkB,CAAG,GAAI,CAAA1M,kBAAkB,CAAC,CAAC,CACnD,cAAe,CAAA0M,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}