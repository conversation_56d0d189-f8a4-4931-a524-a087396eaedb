/* WorldClass.css - 对标世界一流举措页面样式 */

.world-class-container {
  min-height: 100vh;
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
  padding: 15px;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

/* 页面头部样式 - 复用模块一设计 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
  padding: 10px 0;
  width: 100%;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-center {
  text-align: center;
  flex: 1;
}

.back-btn-top {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  order: -1;
}

.back-btn-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

.page-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.2rem;
  color: #00d4aa;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
}

.page-subtitle {
  font-size: 1rem;
  color: #20ff4d;
  margin-bottom: 0;
  opacity: 0.8;
}

.sync-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 600;
}

.status-indicator.success {
  background: rgba(32, 255, 77, 0.2);
  color: #20ff4d;
  border: 1px solid #20ff4d;
}

.status-indicator.error {
  background: rgba(255, 87, 87, 0.2);
  color: #ff5757;
  border: 1px solid #ff5757;
}

.status-indicator.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid #ffc107;
}

/* 控制面板样式 */
.control-panel {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  padding: 15px 20px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 20px;
}

/* 左侧控件容器 */
.controls-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 30px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  color: #00d4aa;
  font-weight: 600;
  font-size: 1rem;
}

.level-selector {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.5);
  border-radius: 8px;
  color: #ffffff;
  padding: 8px 15px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  width: 130px;
}

.level-selector:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.level-selector option {
  background: rgba(26, 26, 46, 0.95);
  color: #00d4aa;
  padding: 8px 15px;
  font-weight: 600;
  border: none;
}

.level-selector option:hover {
  background: rgba(0, 212, 170, 0.2);
  color: #ffffff;
}

.value-selector {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.5);
  border-radius: 8px;
  color: #ffffff;
  padding: 8px 15px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  width: 280px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.value-selector:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.value-selector option {
  background: rgba(26, 26, 46, 0.95);
  color: #00d4aa;
  padding: 8px 15px;
  font-weight: 600;
  border: none;
}

.value-selector option:hover {
  background: rgba(0, 212, 170, 0.2);
  color: #ffffff;
}

/* 月份导航样式 - 参考模块二设计但保持独立 */
.month-navigation-new {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  background: rgba(0, 212, 170, 0.08);
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 170, 0.4);
  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);
  width: fit-content;
}

.nav-btn-new {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
  border: none;
  border-radius: 15px;
  padding: 6px 12px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: none;
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);
}

.nav-btn-new:hover:not(:disabled) {
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
  background: linear-gradient(45deg, #20ff4d, #00d4aa);
}

.nav-btn-new:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.current-months-new {
  color: #00d4aa;
  font-weight: 700;
  font-size: 0.95rem;
  min-width: 80px;
  text-align: center;
  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);
  font-family: 'Orbitron', monospace;
}

.refresh-btn-new {
  background: linear-gradient(45deg, #4dd0ff, #00d4aa);
  color: #000;
  border: none;
  border-radius: 15px;
  padding: 10px 20px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: none;
  box-shadow: 0 4px 12px rgba(77, 208, 255, 0.2);
  order: -1;
}

.refresh-btn-new:hover {
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 6px 20px rgba(77, 208, 255, 0.4);
}

/* 右侧按钮容器 */
.action-buttons {
  display: flex;
  gap: 15px;
  align-items: center;
}

/* 数据统计样式 */
.data-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 10px;
  padding: 10px 15px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.stat-label {
  color: #00d4aa;
  font-size: 0.9rem;
  margin-right: 8px;
}

.stat-value {
  color: #4dd0ff;
  font-weight: 600;
  font-size: 1rem;
}

/* 内联统计样式 - 与筛选框同行显示 */
.stat-item-inline {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 8px;
  padding: 8px 15px;
  text-align: center;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.stat-item-inline .stat-label {
  color: #00d4aa;
  font-size: 0.9rem;
}

.stat-item-inline .stat-value {
  color: #4dd0ff;
  font-weight: 600;
  font-size: 1rem;
}

/* 表格容器样式 - 最大化显示 */
.table-container {
  overflow-x: auto;
  border-radius: 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  width: 100%;
  margin: 0 auto;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.world-class-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'Rajdhani', sans-serif;
}

/* 表头固定样式 - 强化固定效果 */
.world-class-table thead {
  position: sticky;
  top: -20px;
  z-index: 100;
}

.world-class-table th {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #00d4aa;
  padding: 15px 10px;
  text-align: center;
  border: 1px solid rgba(0, 212, 170, 0.3);
  font-weight: 700;
  font-size: 1rem;
  position: sticky;
  top: -20px;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 表格样式更新 - 数据居中显示 */
.world-class-table td {
  padding: 12px 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  vertical-align: middle;
  text-align: center;
  position: relative;
}

.data-row:hover td {
  background: rgba(0, 212, 170, 0.1);
  border-color: rgba(0, 212, 170, 0.3);
}

/* 列宽设置 - 自适应屏幕宽度 */
.col-number { width: 4%; min-width: 60px; } /* 序号列 - 二字宽度 */
.col-level { width: 8%; min-width: 120px; } /* 相关指标或方向列 - 分级展示用 */
.col-criteria { width: 8%; min-width: 120px; } /* 工作准则列 - 缩小一点 */
.col-target { width: 13%; min-width: 180px; } /* 2025年目标列 - 稍微缩小一点 */
.col-measure { width: 15%; min-width: 200px; } /* 2025年举措列 - 详细内容显示 */
.col-responsible { width: 8%; min-width: 100px; } /* 负责人列 - 再扩大一点点 */
.col-weight { width: 4%; min-width: 50px; } /* 权重列 - 二字宽 */
.col-remark { width: 8%; min-width: 120px; } /* 备注列 - 缩小一点点 */
.col-month-plan, .col-month-complete { width: 9%; min-width: 140px; } /* 月度计划和完成情况列 - 增加宽度 */

/* 可编辑单元格样式 */
.editable-cell {
  cursor: pointer;
  transition: all 0.3s ease;
}

.editable-cell:hover {
  background: rgba(77, 208, 255, 0.1);
  border-color: #4dd0ff;
}

.cell-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid #4dd0ff;
  border-radius: 4px;
  color: #ffffff;
  padding: 8px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  resize: vertical;
  text-align: center;
}

.cell-input:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.cell-content {
  display: block;
  word-break: break-word;
  white-space: pre-wrap;
  min-height: 20px;
  text-align: center;
}

/* 月份列样式 */
.month-plan {
  background: rgba(32, 255, 77, 0.1) !important;
  border-color: rgba(32, 255, 77, 0.3) !important;
}

.month-complete {
  background: rgba(77, 208, 255, 0.1) !important;
  border-color: rgba(77, 208, 255, 0.3) !important;
}

/* 加载状态样式更新 */
.world-class-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  flex-direction: column;
  gap: 20px;
}

.world-class-loading .loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 212, 170, 0.3);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.world-class-loading p {
  color: #00d4aa;
  font-size: 1.1rem;
  text-align: center;
}

/* 统计信息样式 */
.stats-section {
  display: flex;
  gap: 20px;
  margin-bottom: 25px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-width: 150px;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(transparent, rgba(0, 212, 170, 0.1), transparent 30%);
  animation: rotate 6s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-5px);
  border-color: #00d4aa;
  box-shadow: 0 10px 30px rgba(0, 212, 170, 0.2);
}

.stat-number {
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  color: #4dd0ff;
  margin-bottom: 8px;
  text-shadow: 0 0 15px rgba(77, 208, 255, 0.5);
}

.stat-text {
  color: #ffffff;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 下载功能样式 */
.download-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
}

.download-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 5px 10px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .filter-controls {
    gap: 20px;
  }
  
  .month-navigation-new {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .stats-section {
    gap: 15px;
  }
  
  .stat-card {
    min-width: 120px;
  }
  
  /* 在中等屏幕上调整列宽 */
  .col-month-plan, .col-month-complete { width: 10%; min-width: 120px; }
}

@media (max-width: 768px) {
  .world-class-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 1.8rem;
  }
  
  .page-header {
    flex-direction: column;
    gap: 15px;
  }
  
  .header-center {
    order: 1;
  }
  
  .back-btn-top {
    order: 2;
    width: 100%;
  }
  
  .sync-status {
    order: 3;
  }
  
  .stat-card {
    flex: 1;
    min-width: 100px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .nav-btn-new {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
  
  .current-months-new {
    font-size: 1rem;
  }
  
  .control-panel {
    padding: 15px;
  }
  
  .filter-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .world-class-table {
    font-size: 0.9rem;
  }
  
  .world-class-table th,
  .world-class-table td {
    padding: 8px 5px;
  }
  
  /* 移动端表格高度调整 */
  .table-container {
    max-height: calc(100vh - 280px);
  }
}

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: rgba(0, 212, 170, 0.5);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 212, 170, 0.7);
}

/* 动画定义 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 新的高科技感筛选按钮样式 */
@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.6), 0 0 25px rgba(0, 212, 170, 0.5);
  }
  100% {
    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);
  }
}

@keyframes rotate-border {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.responsible-filter-btn-new {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 55px;
  height: 55px;
  background: #1a1a2e;
  border: 2px solid rgba(0, 212, 170, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  color: #00d4aa;
  font-size: 22px;
  animation: pulse-glow 4s infinite ease-in-out;
}

.responsible-filter-btn-new::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top-color: #20ff4d;
  transition: transform 1s linear;
  animation: rotate-border 2s linear infinite;
}

.responsible-filter-btn-new:hover {
  transform: scale(1.15);
  border-color: rgba(32, 255, 77, 0.8);
  background: rgba(32, 255, 77, 0.1);
}

.responsible-filter-btn-new:hover::before {
  animation-duration: 1s;
}

.responsible-filter-btn-new .filter-icon {
  transition: transform 0.3s ease;
}

.responsible-filter-btn-new:hover .filter-icon {
  transform: rotate(15deg);
}

.responsible-filter-btn-new .filter-badge {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 3;
  transform: translate(20%, -20%);
}

/* 空数据状态 */
.no-data {
  text-align: center;
  padding: 50px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.1rem;
  width: 100%;
}

/* 负责人筛选面板样式（复用模块二设计） */
.world-class-responsible-filter-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 320px;
  max-height: 500px;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.98), rgba(0, 40, 60, 0.98));
  border: 2px solid rgba(0, 255, 204, 0.4);
  border-radius: 15px;
  backdrop-filter: blur(25px);
  box-shadow: 
    0 15px 50px rgba(0, 0, 0, 0.4),
    0 0 40px rgba(0, 255, 204, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 2000;
  overflow: hidden;
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.world-class-filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(90deg, rgba(0, 255, 204, 0.1), rgba(0, 184, 148, 0.1));
  border-bottom: 1px solid rgba(0, 255, 204, 0.2);
}

.world-class-filter-panel-header h3 {
  margin: 0;
  color: #00ffcc;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0, 255, 204, 0.5);
}

.world-class-close-panel-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.world-class-close-panel-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  transform: rotate(90deg);
}

.world-class-filter-panel-content {
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px 20px;
  max-height: 400px;
  overflow-y: hidden;
}

.world-class-filter-options {
  flex: 1;
  overflow-y: auto;
  padding-top: 20px;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
}

.world-class-filter-option {
  background: rgba(0, 255, 204, 0.1);
  border: 1px solid rgba(0, 255, 204, 0.3);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  text-align: center;
  flex: 0 0 calc(33.33% - 4px);
  box-sizing: border-box;
}

.world-class-filter-option:hover {
  background: rgba(0, 255, 204, 0.2);
  border-color: rgba(0, 255, 204, 0.5);
  box-shadow: 0 0 15px rgba(0, 255, 204, 0.3);
}

.world-class-filter-option.selected {
  background: linear-gradient(135deg, rgba(0, 255, 204, 0.3), rgba(0, 184, 148, 0.3));
  border-color: #00ffcc;
  color: #00ffcc;
  box-shadow: 0 0 20px rgba(0, 255, 204, 0.4);
}

.world-class-filter-actions {
  flex-shrink: 0;
  display: flex;
  gap: 8px;
}

.world-class-clear-filter-btn, .world-class-apply-filter-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.world-class-clear-filter-btn {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.world-class-clear-filter-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.world-class-apply-filter-btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: 1px solid rgba(0, 255, 204, 0.3);
}

.world-class-apply-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);
}

/* 自定义滚动条 */
.world-class-filter-panel-content::-webkit-scrollbar {
  width: 6px;
}

.world-class-filter-panel-content::-webkit-scrollbar-track {
  background: rgba(0, 255, 204, 0.1);
  border-radius: 3px;
}

.world-class-filter-panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 204, 0.5);
  border-radius: 3px;
}

.world-class-filter-panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 204, 0.7);
} 