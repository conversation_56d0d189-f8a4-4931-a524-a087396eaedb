{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'../../styles/WorkTracking.css';import trackingService from'../services/trackingService';import WorkTrackingDownloadModal from'../components/WorkTrackingDownloadModal';import workTrackingDownloadService from'../services/workTrackingDownloadService';import{jsx as _jsx,Fragment as _Fragment,jsxs as _jsxs}from\"react/jsx-runtime\";const WorkTracking=_ref=>{let{onNavigate}=_ref;// 状态管理\nconst[data,setData]=useState([]);const[loading,setLoading]=useState(true);const[editingCell,setEditingCell]=useState(null);const[syncStatus,setSyncStatus]=useState('已同步');// 数据输入优化相关状态\nconst[tempValues,setTempValues]=useState({});// 临时存储编辑中的值\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);// 负责人筛选相关状态\nconst[selectedResponsiblePersons,setSelectedResponsiblePersons]=useState([]);const[showResponsibleFilter,setShowResponsibleFilter]=useState(false);const[filteredData,setFilteredData]=useState([]);// 展示状态\nconst[currentMonthPair,setCurrentMonthPair]=useState(()=>{// 根据当前月份设置初始显示的月份对\nconst currentMonth=new Date().getMonth()+1;// getMonth()返回0-11，加1得到1-12\nconst months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];// 找到当前月份在数组中的位置\nlet currentIndex=-1;months.forEach((month,index)=>{const monthNum=parseInt(month.replace('月',''));if(monthNum===currentMonth){currentIndex=index;}});// 如果找到当前月份，返回包含当前月份的月份对索引\nif(currentIndex>=0){// 如果当前月份是第一个月（如2月），则显示第一对\nif(currentIndex===0)return 0;// 否则，尽量让当前月份显示在第一个位置\nreturn Math.max(0,currentIndex-1);}// 如果当前月份不在范围内，默认显示第一对\nreturn 0;});// 显示第几对月份\nconst[collapsedTypes,setCollapsedTypes]=useState(new Set());// 选择性下载相关状态\nconst[showDownloadModal,setShowDownloadModal]=useState(false);const[downloadLoading,setDownloadLoading]=useState(false);// 月份配置\nconst months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];const monthPairs=[];for(let i=0;i<months.length-1;i++){monthPairs.push([months[i],months[i+1]]);}useEffect(()=>{loadData();// 设置同步回调\ntrackingService.setSyncCallbacks(()=>setSyncStatus('同步成功'),error=>setSyncStatus('同步失败'));// 清理函数\nreturn()=>{if(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}if(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}};},[]);// 当数据或筛选条件变化时，更新筛选后的数据\nuseEffect(()=>{applyResponsibleFilter();},[data,selectedResponsiblePersons]);// 确保filteredData有初始值\nuseEffect(()=>{if(data&&data.length>0&&filteredData.length===0&&selectedResponsiblePersons.length===0){setFilteredData(data);}},[data]);const loadData=async()=>{setLoading(true);try{console.log('开始加载重点工作跟踪数据...');const trackingData=await trackingService.loadTrackingData();console.log('加载的跟踪数据:',trackingData);if(trackingData){setData(trackingData);console.log('数据设置完成，共',trackingData.length,'项重点工作');// 获取所有工作类型并设置为默认折叠状态\nconst allWorkTypes=new Set(trackingData.map(item=>item.重点工作类型).filter(Boolean));setCollapsedTypes(allWorkTypes);}else{console.error('未获取到跟踪数据');}}catch(error){console.error('数据加载失败:',error);}setLoading(false);};// 获取所有工作类型\nconst getWorkTypes=()=>{const types=['全部',...new Set(data.map(item=>item.重点工作类型).filter(Boolean))];return types;};// 按工作类型分组数据\nconst getGroupedData=()=>{// 使用筛选后的数据而不是原始数据\nconst dataToUse=filteredData.length>0||selectedResponsiblePersons.length===0?filteredData:data;if(!dataToUse||dataToUse.length===0)return{};const grouped={};dataToUse.forEach(item=>{const type=item.重点工作类型||'其他';if(!grouped[type]){grouped[type]=[];}grouped[type].push(item);});return grouped;};// 防抖保存函数\nconst debouncedSave=useCallback(async(rowIndex,field,value)=>{try{setSyncStatus('同步中...');// 取消之前的保存操作\nif(pendingSaveRef.current){var _pendingSaveRef$curre3,_pendingSaveRef$curre4;(_pendingSaveRef$curre3=(_pendingSaveRef$curre4=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre3===void 0?void 0:_pendingSaveRef$curre3.call(_pendingSaveRef$curre4);}// 创建新的保存操作\nconst controller=new AbortController();pendingSaveRef.current=controller;// 调用双向同步\nawait trackingService.updateTrackingData(rowIndex,field,value);// 如果没有被取消，更新状态\nif(!controller.signal.aborted){setSyncStatus('同步成功');setTimeout(()=>setSyncStatus('已同步'),1000);pendingSaveRef.current=null;}}catch(error){if(!error.name==='AbortError'){console.error('保存失败:',error);setSyncStatus('同步失败');}}},[]);// 处理输入变化（实时更新UI，延迟保存）\nconst handleInputChange=(rowIndex,field,value)=>{// 立即更新UI显示\nconst newData=[...data];newData[rowIndex][field]=value;setData(newData);// 存储临时值\nconst key=\"\".concat(rowIndex,\"-\").concat(field);setTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));// 清除之前的定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的防抖定时器\nsaveTimeoutRef.current=setTimeout(()=>{debouncedSave(rowIndex,field,value);},800);// 800ms防抖延迟\n};// 处理失焦保存（立即保存）\nconst handleBlurSave=async(rowIndex,field)=>{const key=\"\".concat(rowIndex,\"-\").concat(field);const value=tempValues[key];if(value!==undefined){// 清除防抖定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);saveTimeoutRef.current=null;}// 立即保存\nawait debouncedSave(rowIndex,field,value);// 清除临时值\nsetTempValues(prev=>{const newTemp=_objectSpread({},prev);delete newTemp[key];return newTemp;});}};// 兼容原有的handleCellEdit接口\nconst handleCellEdit=async(rowIndex,field,value)=>{handleInputChange(rowIndex,field,value);};const startEdit=(rowIndex,field)=>{setEditingCell(\"\".concat(rowIndex,\"-\").concat(field));};const finishEdit=()=>{setEditingCell(null);};// 切换工作类型展开/折叠\nconst toggleTypeCollapse=type=>{const newCollapsed=new Set(collapsedTypes);if(newCollapsed.has(type)){newCollapsed.delete(type);}else{newCollapsed.add(type);}setCollapsedTypes(newCollapsed);};// 全部展开/折叠控制\nconst toggleAllTypes=()=>{const allWorkTypes=new Set(data.map(item=>item.重点工作类型).filter(Boolean));// 如果当前全部折叠或部分折叠，则全部展开\n// 如果当前全部展开，则全部折叠\nif(collapsedTypes.size===0){// 当前全部展开，执行全部折叠\nsetCollapsedTypes(allWorkTypes);}else{// 当前全部折叠或部分折叠，执行全部展开\nsetCollapsedTypes(new Set());}};// 获取全部展开/折叠按钮的显示状态\nconst getToggleAllStatus=()=>{const allWorkTypes=new Set(data.map(item=>item.重点工作类型).filter(Boolean));if(collapsedTypes.size===0){return{text:'全部折叠',icon:'▼'};}else if(collapsedTypes.size===allWorkTypes.size){return{text:'全部展开',icon:'▶'};}else{return{text:'全部展开',icon:'▶'};}};// 计算总工作数（使用筛选后的数据）\nconst getTotalWorkCount=()=>{const dataToUse=filteredData.length>0||selectedResponsiblePersons.length===0?filteredData:data;return dataToUse?dataToUse.length:0;};// 检测是否为纯数字\nconst isPureNumber=value=>{if(value===null||value===undefined||value==='')return false;const strValue=String(value).trim();return!isNaN(strValue)&&!isNaN(parseFloat(strValue))&&isFinite(strValue);};// 处理Excel公式对象和格式化数据显示\nconst formatDisplayValue=value=>{// 处理null, undefined, 空字符串\nif(value===null||value===undefined||value===''){return'';}// 处理Excel公式对象（[object Object]乱码问题）\nif(typeof value==='object'){// 尝试提取对象中的值\nif(value.hasOwnProperty('v')){value=value.v;// Excel.js格式\n}else if(value.hasOwnProperty('w')){value=value.w;// 另一种Excel格式\n}else if(value.hasOwnProperty('t')&&value.hasOwnProperty('v')){value=value.v;// 包含类型信息的格式\n}else if(value.hasOwnProperty('result')){value=value.result;// 公式结果\n}else{// 如果无法提取有效值，返回空字符串而不是[object Object]\nreturn'';}}// 只对纯数字进行格式化\nif(isPureNumber(value)){const numValue=parseFloat(value);// 检查是否为整数\nif(Number.isInteger(numValue)){return String(numValue);// 整数按整数显示\n}else{return numValue.toFixed(2);// 小数保留两位小数\n}}// 非纯数字内容保持原样\nreturn String(value);};// 渲染可编辑单元格\nconst renderEditableCell=function(value,rowIndex,field){let className=arguments.length>3&&arguments[3]!==undefined?arguments[3]:'';const cellKey=\"\".concat(rowIndex,\"-\").concat(field);const isEditing=editingCell===cellKey;const isEditable=!['序号','重点工作类型'].includes(field);// 格式化显示值\nconst displayValue=formatDisplayValue(value);if(!isEditable){return/*#__PURE__*/_jsx(\"td\",{className:\"data-cell \".concat(className),children:displayValue});}return/*#__PURE__*/_jsx(\"td\",{className:\"data-cell editable \".concat(className),onClick:()=>startEdit(rowIndex,field),children:isEditing?/*#__PURE__*/_jsx(\"input\",{type:\"text\",value:value||'',onChange:e=>handleInputChange(rowIndex,field,e.target.value),onBlur:()=>{handleBlurSave(rowIndex,field);finishEdit();},onKeyDown:e=>{if(e.key==='Enter'){handleBlurSave(rowIndex,field);finishEdit();}if(e.key==='Escape'){finishEdit();}},autoFocus:true,className:\"cell-input\"}):/*#__PURE__*/_jsx(\"span\",{className:\"cell-content\",children:displayValue})});};// 渲染月份列\nconst renderMonthColumns=(row,rowIndex)=>{if(currentMonthPair>=monthPairs.length)return null;const[month1,month2]=monthPairs[currentMonthPair];const month1Plan=\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\");const month1Complete=\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\");const month2Plan=\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\");const month2Complete=\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\");return/*#__PURE__*/_jsxs(_Fragment,{children:[renderEditableCell(row[month1Plan],rowIndex,month1Plan,'month-plan'),renderEditableCell(row[month1Complete],rowIndex,month1Complete,'month-complete'),renderEditableCell(row[month2Plan],rowIndex,month2Plan,'month-plan'),renderEditableCell(row[month2Complete],rowIndex,month2Complete,'month-complete')]});};// 渲染数据表格\nconst renderDataTable=()=>{const groupedData=getGroupedData();if(Object.keys(groupedData).length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"no-data\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u6682\\u65E0\\u6570\\u636E\"})});}const currentPair=monthPairs[currentMonthPair]||['',''];return/*#__PURE__*/_jsx(\"div\",{className:\"tracking-table-container\",children:Object.entries(groupedData).map(_ref2=>{let[type,items]=_ref2;const isCollapsed=collapsedTypes.has(type);return/*#__PURE__*/_jsxs(\"div\",{className:\"work-type-group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"group-header\",onClick:()=>toggleTypeCollapse(type),children:/*#__PURE__*/_jsxs(\"span\",{className:\"group-title\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"collapse-icon\",children:isCollapsed?'▶':'▼'}),type,\" (\",/*#__PURE__*/_jsxs(\"span\",{className:\"item-count-highlight\",children:[items.length,\"\\u9879\"]}),\")\"]})}),!isCollapsed&&/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"tracking-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"col-number\",children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-type\",children:\"\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7C7B\\u578B\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-indicator\",children:\"\\u76F8\\u5173\\u6307\\u6807\\u6216\\u65B9\\u5411\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-total-target\",children:\"\\u603B\\u4F53\\u76EE\\u6807\\u503C\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-target\",children:\"2025\\u5E74\\u76EE\\u6807\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-method\",children:\"\\u8BA1\\u7B97\\u65B9\\u6CD5&\\u4E3E\\u63AA\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-responsible\",children:\"\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-frequency\",children:\"\\u8DDF\\u8E2A\\u9891\\u6B21\"}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[currentPair[0],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[currentPair[0],\"\\u5B8C\\u6210\\u60C5\\u51B5\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[currentPair[1],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[currentPair[1],\"\\u5B8C\\u6210\\u60C5\\u51B5\"]})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:items.map((row,index)=>{const globalIndex=data.findIndex(item=>item===row);// 每个类型内部序号从1开始重新计算\nconst typeSequenceNumber=index+1;return/*#__PURE__*/_jsxs(\"tr\",{className:\"data-row\",children:[renderEditableCell(typeSequenceNumber,globalIndex,'序号'),renderEditableCell(row.重点工作类型,globalIndex,'重点工作类型'),renderEditableCell(row.相关指标或方向,globalIndex,'相关指标或方向'),renderEditableCell(row.总体目标值,globalIndex,'总体目标值'),renderEditableCell(row['2025年目标'],globalIndex,'2025年目标'),renderEditableCell(row['计算方法&2025年举措'],globalIndex,'计算方法&2025年举措'),renderEditableCell(row.负责人,globalIndex,'负责人'),renderEditableCell(row.跟踪频次,globalIndex,'跟踪频次'),renderMonthColumns(row,globalIndex)]},globalIndex);})})]})})]},type);})});};// 获取所有负责人列表\nconst getAllResponsiblePersons=()=>{if(!data||data.length===0)return[];const responsiblePersons=data.map(item=>item.负责人).filter(person=>person&&String(person).trim()!=='').reduce((acc,person)=>{// 处理多个负责人用逗号、括号等分隔的情况\nconst persons=String(person).split(/[,，;；、（）()]/).map(p=>p.trim()).filter(p=>p);persons.forEach(p=>{if(!acc.includes(p)){acc.push(p);}});return acc;},[]);return responsiblePersons.sort();};// 应用负责人筛选\nconst applyResponsibleFilter=()=>{if(!data||data.length===0){setFilteredData([]);return;}if(selectedResponsiblePersons.length===0){// 如果没有选择任何负责人，显示所有数据\nsetFilteredData(data);}else{// 筛选包含选中负责人的数据行\nconst filtered=data.filter(item=>{if(!item.负责人)return false;// 检查责任人单元格是否包含任何选中的负责人（包含匹配）\nreturn selectedResponsiblePersons.some(selectedPerson=>String(item.负责人).includes(selectedPerson));});setFilteredData(filtered);}};// 切换负责人筛选器显示状态\nconst toggleResponsibleFilter=()=>{setShowResponsibleFilter(!showResponsibleFilter);};// 处理负责人选择\nconst handleResponsiblePersonSelect=person=>{setSelectedResponsiblePersons(prev=>{if(prev.includes(person)){// 如果已选中，则取消选择\nreturn prev.filter(p=>p!==person);}else{// 如果未选中，则添加到选择列表\nreturn[...prev,person];}});};// 清除所有筛选条件\nconst clearResponsibleFilter=()=>{setSelectedResponsiblePersons([]);setShowResponsibleFilter(false);};// 处理选择性下载\nconst handleDownload=async selectionData=>{setDownloadLoading(true);try{const result=await workTrackingDownloadService.handleSelectiveDownload(selectionData);if(result.success){console.log('下载成功:',result.message);alert(result.message);}}catch(error){console.error('下载失败:',error);alert('下载失败: '+error.message);}finally{setDownloadLoading(false);setShowDownloadModal(false);}};// 打开下载模态框\nconst openDownloadModal=()=>{setShowDownloadModal(true);};// 关闭下载模态框\nconst closeDownloadModal=()=>{setShowDownloadModal(false);};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u6570\\u636E...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"work-tracking-container\",children:[showResponsibleFilter&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"div\",{className:\"modal-backdrop\",onClick:()=>setShowResponsibleFilter(false),style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.7)',zIndex:1999,backdropFilter:'blur(5px)'}}),/*#__PURE__*/_jsxs(\"div\",{className:\"responsible-filter-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-panel-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u9009\\u62E9\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-panel-btn\",onClick:()=>setShowResponsibleFilter(false),children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-panel-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-options\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"filter-option \".concat(selectedResponsiblePersons.length===0?'selected':''),onClick:clearResponsibleFilter,children:\"\\u5168\\u90E8\\u8D1F\\u8D23\\u4EBA\"}),getAllResponsiblePersons().map(person=>/*#__PURE__*/_jsx(\"button\",{className:\"filter-option \".concat(selectedResponsiblePersons.includes(person)?'selected':''),onClick:()=>handleResponsiblePersonSelect(person),children:person.length>4?person.substring(0,4):person},person))]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"clear-filter-btn\",onClick:clearResponsibleFilter,children:\"\\u6E05\\u9664\\u7B5B\\u9009\"}),/*#__PURE__*/_jsx(\"button\",{className:\"apply-filter-btn\",onClick:()=>setShowResponsibleFilter(false),children:\"\\u5E94\\u7528\\u7B5B\\u9009\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"back-btn-top\",onClick:()=>onNavigate('home'),style:{fontSize:'18px'},children:\"\\u8FD4\\u56DE\\u9996\\u9875\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"page-title\",children:\"\\u5F00\\u53D1\\u4E2D\\u5FC3\\u91CD\\u70B9\\u5DE5\\u4F5C\\u8DDF\\u8E2A-\\u586B\\u5199\\u8868\"}),/*#__PURE__*/_jsx(\"p\",{className:\"page-subtitle\",children:\"\\u6708\\u5EA6\\u5DE5\\u4F5C\\u8BA1\\u5212\\u4E0E\\u5B8C\\u6210\\u60C5\\u51B5\\u8DDF\\u8E2A\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-status\",style:{fontSize:'16px',marginRight:'10px'},children:/*#__PURE__*/_jsx(\"span\",{className:\"status-indicator \".concat(syncStatus.includes('成功')?'success':syncStatus.includes('失败')?'error':'pending'),style:{fontSize:'18px'},children:syncStatus})})]}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{background:'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',border:'1px solid rgba(0, 212, 170, 0.3)',borderRadius:'12px',margin:'20px auto',width:'100%',maxWidth:'none',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 212, 170, 0.2)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',padding:'16px 24px',gap:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',background:'linear-gradient(45deg, #00d4aa, #20ff4d)',borderRadius:'50%',fontSize:'18px'},children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'rgba(255, 255, 255, 0.7)',marginBottom:'4px',fontWeight:'500'},children:\"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',color:'#fff',fontWeight:'600'},children:(()=>{const workCount=getTotalWorkCount();if(selectedResponsiblePersons.length===1){return/*#__PURE__*/_jsxs(_Fragment,{children:[\"\\u8D1F\\u8D23\\u4EBA\\uFF1A\",/*#__PURE__*/_jsx(\"span\",{style:{color:'#20ff4d',fontWeight:'bold',textShadow:'0 0 10px rgba(32, 255, 77, 0.6)',margin:'0 8px'},children:selectedResponsiblePersons[0]}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#00d4aa',fontSize:'14px'},children:[\"\\uFF08\",workCount,\"\\u9879\\u5DE5\\u4F5C\\uFF09\"]})]});}else{return/*#__PURE__*/_jsxs(_Fragment,{children:[\"\\u8D1F\\u8D23\\u4EBA\\uFF1A\",/*#__PURE__*/_jsx(\"span\",{style:{color:'#20ff4d',fontWeight:'bold',textShadow:'0 0 10px rgba(32, 255, 77, 0.6)',margin:'0 8px'},children:selectedResponsiblePersons.join('、')}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#00d4aa',fontSize:'14px'},children:[\"\\uFF08\",workCount,\"\\u9879\\u5DE5\\u4F5C\\uFF09\"]})]});}})()})]}),/*#__PURE__*/_jsxs(\"button\",{style:{display:'flex',alignItems:'center',gap:'8px',padding:'8px 16px',background:'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',border:'1px solid rgba(255, 75, 75, 0.4)',borderRadius:'8px',color:'#ff6b6b',fontSize:'14px',fontWeight:'500',cursor:'pointer',transition:'all 0.3s ease'},onClick:clearResponsibleFilter,onMouseEnter:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';e.target.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';e.target.style.transform='translateY(0)';},title:\"\\u6E05\\u9664\\u7B5B\\u9009\\u6761\\u4EF6\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2715\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6E05\\u9664\\u7B5B\\u9009\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"unified-control-panel-v2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stats-section-left\",style:{display:'flex',flexDirection:'row',gap:'15px',alignItems:'center'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:getTotalWorkCount()}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-text\",children:\"\\u603B\\u5DE5\\u4F5C\\u9879\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:getWorkTypes().length-1}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-text\",children:\"\\u5DE5\\u4F5C\\u7C7B\\u578B\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-card toggle-all-card\",onClick:toggleAllTypes,children:[/*#__PURE__*/_jsx(\"div\",{className:\"stat-number\",children:/*#__PURE__*/_jsx(\"span\",{className:\"toggle-icon\",children:getToggleAllStatus().icon})}),/*#__PURE__*/_jsx(\"div\",{className:\"stat-text\",children:getToggleAllStatus().text})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"filter-section-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"tech-filter-module\",onClick:toggleResponsibleFilter,children:[/*#__PURE__*/_jsxs(\"div\",{className:\"tech-module-background\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"circuit-pattern\"}),/*#__PURE__*/_jsx(\"div\",{className:\"energy-flow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"hologram-border\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"tech-module-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"icon-container\",style:{display:'flex',alignItems:'center',justifyContent:'center'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"tech-icon\",style:{fontSize:'0px',width:'68px',height:'68px',position:'relative',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsxs(\"svg\",{width:\"60\",height:\"60\",viewBox:\"0 0 32 32\",fill:\"none\",style:{filter:'drop-shadow(0 0 15px rgba(0, 212, 170, 1)) drop-shadow(0 0 25px rgba(32, 255, 77, 0.5))'},children:[/*#__PURE__*/_jsxs(\"g\",{opacity:\"0.3\",children:[/*#__PURE__*/_jsx(\"path\",{d:\"M4 8L28 8M4 16L28 16M4 24L28 24\",stroke:\"url(#gridGradient)\",strokeWidth:\"0.5\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M8 4L8 28M16 4L16 28M24 4L24 28\",stroke:\"url(#gridGradient)\",strokeWidth:\"0.5\"})]}),/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"16\",cy:\"10\",r:\"3.5\",stroke:\"url(#mainGradient)\",strokeWidth:\"2\",fill:\"url(#headFill1)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M9 26V22C9 19.7909 10.7909 18 13 18H19C21.2091 18 23 19.7909 23 22V26\",stroke:\"url(#mainGradient)\",strokeWidth:\"2\",fill:\"url(#bodyFill1)\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsxs(\"circle\",{cx:\"16\",cy:\"10\",r:\"5\",stroke:\"rgba(0, 212, 170, 0.6)\",strokeWidth:\"1\",fill:\"none\",children:[/*#__PURE__*/_jsx(\"animate\",{attributeName:\"r\",values:\"5;6;5\",dur:\"3s\",repeatCount:\"indefinite\"}),/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"0.6;0.3;0.6\",dur:\"3s\",repeatCount:\"indefinite\"})]})]}),/*#__PURE__*/_jsxs(\"g\",{opacity:\"0.7\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"7\",cy:\"12\",r:\"2.5\",stroke:\"url(#secondaryGradient)\",strokeWidth:\"1.5\",fill:\"url(#headFill2)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M2 24V21C2 19.3431 3.34315 18 5 18H9C10.6569 18 12 19.3431 12 21V24\",stroke:\"url(#secondaryGradient)\",strokeWidth:\"1.5\",fill:\"url(#bodyFill2)\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M9.5 12L13 10\",stroke:\"url(#connectGradient)\",strokeWidth:\"1\",strokeDasharray:\"2,1\",children:/*#__PURE__*/_jsx(\"animate\",{attributeName:\"stroke-dashoffset\",values:\"0;3;0\",dur:\"2s\",repeatCount:\"indefinite\"})})]}),/*#__PURE__*/_jsxs(\"g\",{opacity:\"0.7\",children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"25\",cy:\"12\",r:\"2.5\",stroke:\"url(#secondaryGradient)\",strokeWidth:\"1.5\",fill:\"url(#headFill2)\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M20 24V21C20 19.3431 21.3431 18 23 18H27C28.6569 18 30 19.3431 30 21V24\",stroke:\"url(#secondaryGradient)\",strokeWidth:\"1.5\",fill:\"url(#bodyFill2)\",strokeLinecap:\"round\"}),/*#__PURE__*/_jsx(\"path\",{d:\"M22.5 12L19 10\",stroke:\"url(#connectGradient)\",strokeWidth:\"1\",strokeDasharray:\"2,1\",children:/*#__PURE__*/_jsx(\"animate\",{attributeName:\"stroke-dashoffset\",values:\"0;3;0\",dur:\"2s\",repeatCount:\"indefinite\"})})]}),/*#__PURE__*/_jsxs(\"g\",{children:[/*#__PURE__*/_jsx(\"circle\",{cx:\"16\",cy:\"6\",r:\"1\",fill:\"url(#nodeGradient)\",children:/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"1;0.3;1\",dur:\"1.5s\",repeatCount:\"indefinite\"})}),/*#__PURE__*/_jsx(\"circle\",{cx:\"7\",cy:\"8\",r:\"0.8\",fill:\"url(#nodeGradient)\",children:/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"0.3;1;0.3\",dur:\"1.5s\",repeatCount:\"indefinite\"})}),/*#__PURE__*/_jsx(\"circle\",{cx:\"25\",cy:\"8\",r:\"0.8\",fill:\"url(#nodeGradient)\",children:/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"1;0.3;1\",dur:\"1.5s\",repeatCount:\"indefinite\"})})]}),/*#__PURE__*/_jsx(\"g\",{opacity:\"0.8\",children:/*#__PURE__*/_jsxs(\"line\",{x1:\"2\",y1:\"15\",x2:\"30\",y2:\"15\",stroke:\"url(#scanGradient)\",strokeWidth:\"1.5\",children:[/*#__PURE__*/_jsx(\"animate\",{attributeName:\"opacity\",values:\"0;1;0\",dur:\"2.5s\",repeatCount:\"indefinite\"}),/*#__PURE__*/_jsx(\"animate\",{attributeName:\"y1\",values:\"15;17;15\",dur:\"2.5s\",repeatCount:\"indefinite\"}),/*#__PURE__*/_jsx(\"animate\",{attributeName:\"y2\",values:\"15;17;15\",dur:\"2.5s\",repeatCount:\"indefinite\"})]})}),/*#__PURE__*/_jsxs(\"defs\",{children:[/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"mainGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#00d4aa\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"50%\",stopColor:\"#20ff4d\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#4dd0ff\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"secondaryGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#4dd0ff\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#00d4aa\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"connectGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"0%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(32, 255, 77, 0.8)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"50%\",stopColor:\"rgba(0, 212, 170, 1)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(77, 208, 255, 0.8)\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"scanGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"0%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"transparent\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"20%\",stopColor:\"#20ff4d\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"50%\",stopColor:\"#00d4aa\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"80%\",stopColor:\"#4dd0ff\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"transparent\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"gridGradient\",x1:\"0%\",y1:\"0%\",x2:\"100%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(0, 212, 170, 0.3)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(77, 208, 255, 0.3)\"})]}),/*#__PURE__*/_jsxs(\"radialGradient\",{id:\"headFill1\",cx:\"50%\",cy:\"30%\",r:\"80%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(32, 255, 77, 0.4)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(0, 212, 170, 0.1)\"})]}),/*#__PURE__*/_jsxs(\"radialGradient\",{id:\"headFill2\",cx:\"50%\",cy:\"30%\",r:\"80%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(77, 208, 255, 0.3)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(0, 212, 170, 0.1)\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"bodyFill1\",x1:\"0%\",y1:\"0%\",x2:\"0%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(0, 212, 170, 0.25)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(32, 255, 77, 0.1)\"})]}),/*#__PURE__*/_jsxs(\"linearGradient\",{id:\"bodyFill2\",x1:\"0%\",y1:\"0%\",x2:\"0%\",y2:\"100%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"rgba(77, 208, 255, 0.2)\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"rgba(0, 212, 170, 0.1)\"})]}),/*#__PURE__*/_jsxs(\"radialGradient\",{id:\"nodeGradient\",cx:\"50%\",cy:\"50%\",r:\"50%\",children:[/*#__PURE__*/_jsx(\"stop\",{offset:\"0%\",stopColor:\"#20ff4d\"}),/*#__PURE__*/_jsx(\"stop\",{offset:\"100%\",stopColor:\"#00d4aa\"})]})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"scan-line\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module-info\",style:{textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{className:\"module-title\",children:\"\\u8D23\\u4EFB\\u4EBA\\u7B5B\\u9009\"}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"active-indicator\",children:/*#__PURE__*/_jsx(\"span\",{className:\"pulse-dot\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"tech-accent-lines\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"accent-line accent-line-1\"}),/*#__PURE__*/_jsx(\"div\",{className:\"accent-line accent-line-2\"}),/*#__PURE__*/_jsx(\"div\",{className:\"accent-line accent-line-3\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"controls-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"month-navigation-new\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>setCurrentMonthPair(Math.max(0,currentMonthPair-1)),disabled:currentMonthPair===0,children:\"\\u25C0 \\u4E0A\\u4E2A\\u6708\\u4EFD\\u5BF9\"}),/*#__PURE__*/_jsx(\"span\",{className:\"current-months-new\",children:monthPairs[currentMonthPair]?\"\".concat(monthPairs[currentMonthPair][0],\" / \").concat(monthPairs[currentMonthPair][1]):'月份加载中'}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>setCurrentMonthPair(Math.min(monthPairs.length-1,currentMonthPair+1)),disabled:currentMonthPair>=monthPairs.length-1,children:\"\\u4E0B\\u4E2A\\u6708\\u4EFD\\u5BF9 \\u25B6\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons-group\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"refresh-btn-new\",onClick:loadData,children:\"\\uD83D\\uDD04 \\u5237\\u65B0\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"download-btn-tracking\",onClick:openDownloadModal,disabled:downloadLoading,children:downloadLoading?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{className:\"loading-spinner-small\"}),\"\\u5904\\u7406\\u4E2D...\"]}):/*#__PURE__*/_jsx(_Fragment,{children:\"\\uD83D\\uDCC8 \\u9009\\u62E9\\u6027\\u4E0B\\u8F7D\"})})]})]})]}),renderDataTable(),/*#__PURE__*/_jsx(WorkTrackingDownloadModal,{isOpen:showDownloadModal,onClose:closeDownloadModal,data:data,filteredData:filteredData,selectedResponsiblePersons:selectedResponsiblePersons,currentMonthPair:currentMonthPair,monthPairs:monthPairs,onDownload:handleDownload})]});};export default WorkTracking;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "trackingService", "WorkTrackingDownloadModal", "workTrackingDownloadService", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "WorkTracking", "_ref", "onNavigate", "data", "setData", "loading", "setLoading", "editingCell", "setEditingCell", "syncStatus", "setSyncStatus", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "saveTimeoutRef", "pendingSaveRef", "selected<PERSON>espons<PERSON><PERSON><PERSON><PERSON>", "setSelectedResponsiblePersons", "showResponsibleFilter", "setShowResponsibleFilter", "filteredData", "setFilteredData", "currentMonthPair", "setCurrentMonthPair", "currentMonth", "Date", "getMonth", "months", "currentIndex", "for<PERSON>ach", "month", "index", "monthNum", "parseInt", "replace", "Math", "max", "collapsedTypes", "setCollapsedTypes", "Set", "showDownloadModal", "setShowDownloadModal", "downloadLoading", "setDownloadLoading", "monthPairs", "i", "length", "push", "loadData", "setSyncCallbacks", "error", "current", "clearTimeout", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "applyResponsibleFilter", "console", "log", "trackingData", "loadTrackingData", "allWorkTypes", "map", "item", "重点工作类型", "filter", "Boolean", "getWorkTypes", "types", "getGroupedData", "dataToUse", "grouped", "type", "debouncedSave", "rowIndex", "field", "value", "_pendingSaveRef$curre3", "_pendingSaveRef$curre4", "controller", "AbortController", "updateTrackingData", "signal", "aborted", "setTimeout", "name", "handleInputChange", "newData", "key", "concat", "prev", "_objectSpread", "handleBlurSave", "undefined", "newTemp", "handleCellEdit", "startEdit", "finishEdit", "toggleTypeCollapse", "newCollapsed", "has", "delete", "add", "toggleAllTypes", "size", "getToggleAllStatus", "text", "icon", "getTotalWorkCount", "isPureNumber", "strValue", "String", "trim", "isNaN", "parseFloat", "isFinite", "formatDisplayValue", "hasOwnProperty", "v", "w", "result", "numValue", "Number", "isInteger", "toFixed", "renderEditableCell", "className", "arguments", "cellKey", "isEditing", "isEditable", "includes", "displayValue", "children", "onClick", "onChange", "e", "target", "onBlur", "onKeyDown", "autoFocus", "renderMonthColumns", "row", "month1", "month2", "month1Plan", "month1Complete", "month2Plan", "month2Complete", "renderDataTable", "groupedData", "Object", "keys", "currentPair", "entries", "_ref2", "items", "isCollapsed", "globalIndex", "findIndex", "typeSequenceNumber", "相关指标或方向", "总体目标值", "负责人", "跟踪频次", "getAllResponsiblePersons", "<PERSON><PERSON><PERSON><PERSON>", "person", "reduce", "acc", "persons", "split", "p", "sort", "filtered", "some", "<PERSON><PERSON><PERSON>", "toggleResponsibleFilter", "handleResponsiblePersonSelect", "clearResponsibleFilter", "handleDownload", "selectionData", "handleSelectiveDownload", "success", "message", "alert", "openDownloadModal", "closeDownloadModal", "style", "position", "top", "left", "right", "bottom", "backgroundColor", "zIndex", "<PERSON><PERSON>ilter", "substring", "fontSize", "marginRight", "background", "border", "borderRadius", "margin", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "display", "alignItems", "padding", "gap", "justifyContent", "height", "flex", "color", "marginBottom", "fontWeight", "workCount", "textShadow", "join", "cursor", "transition", "onMouseEnter", "transform", "onMouseLeave", "title", "flexDirection", "viewBox", "fill", "opacity", "d", "stroke", "strokeWidth", "cx", "cy", "r", "strokeLinecap", "attributeName", "values", "dur", "repeatCount", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "x1", "y1", "x2", "y2", "id", "offset", "stopColor", "textAlign", "disabled", "min", "isOpen", "onClose", "onDownload"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块二/pages/WorkTracking.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../../styles/WorkTracking.css';\nimport trackingService from '../services/trackingService';\nimport WorkTrackingDownloadModal from '../components/WorkTrackingDownloadModal';\nimport workTrackingDownloadService from '../services/workTrackingDownloadService';\n\nconst WorkTracking = ({ onNavigate }) => {\n  // 状态管理\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingCell, setEditingCell] = useState(null);\n  const [syncStatus, setSyncStatus] = useState('已同步');\n\n  // 数据输入优化相关状态\n  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  \n  // 负责人筛选相关状态\n  const [selectedResponsiblePersons, setSelectedResponsiblePersons] = useState([]);\n  const [showResponsibleFilter, setShowResponsibleFilter] = useState(false);\n  const [filteredData, setFilteredData] = useState([]);\n  \n  // 展示状态\n  const [currentMonthPair, setCurrentMonthPair] = useState(() => {\n    // 根据当前月份设置初始显示的月份对\n    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12\n    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\n    \n    // 找到当前月份在数组中的位置\n    let currentIndex = -1;\n    months.forEach((month, index) => {\n      const monthNum = parseInt(month.replace('月', ''));\n      if (monthNum === currentMonth) {\n        currentIndex = index;\n      }\n    });\n    \n    // 如果找到当前月份，返回包含当前月份的月份对索引\n    if (currentIndex >= 0) {\n      // 如果当前月份是第一个月（如2月），则显示第一对\n      if (currentIndex === 0) return 0;\n      // 否则，尽量让当前月份显示在第一个位置\n      return Math.max(0, currentIndex - 1);\n    }\n    \n    // 如果当前月份不在范围内，默认显示第一对\n    return 0;\n  }); // 显示第几对月份\n  const [collapsedTypes, setCollapsedTypes] = useState(new Set());\n  \n  // 选择性下载相关状态\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n  \n  // 月份配置\n  const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\n  const monthPairs = [];\n  for (let i = 0; i < months.length - 1; i++) {\n    monthPairs.push([months[i], months[i + 1]]);\n  }\n\n  useEffect(() => {\n    loadData();\n\n    // 设置同步回调\n    trackingService.setSyncCallbacks(\n      () => setSyncStatus('同步成功'),\n      (error) => setSyncStatus('同步失败')\n    );\n\n    // 清理函数\n    return () => {\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n      }\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n    };\n  }, []);\n\n  // 当数据或筛选条件变化时，更新筛选后的数据\n  useEffect(() => {\n    applyResponsibleFilter();\n  }, [data, selectedResponsiblePersons]);\n  \n  // 确保filteredData有初始值\n  useEffect(() => {\n    if (data && data.length > 0 && filteredData.length === 0 && selectedResponsiblePersons.length === 0) {\n      setFilteredData(data);\n    }\n  }, [data]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      console.log('开始加载重点工作跟踪数据...');\n      const trackingData = await trackingService.loadTrackingData();\n      console.log('加载的跟踪数据:', trackingData);\n      \n      if (trackingData) {\n        setData(trackingData);\n        console.log('数据设置完成，共', trackingData.length, '项重点工作');\n        \n        // 获取所有工作类型并设置为默认折叠状态\n        const allWorkTypes = new Set(trackingData.map(item => item.重点工作类型).filter(Boolean));\n        setCollapsedTypes(allWorkTypes);\n      } else {\n        console.error('未获取到跟踪数据');\n      }\n    } catch (error) {\n      console.error('数据加载失败:', error);\n    }\n    setLoading(false);\n  };\n\n  // 获取所有工作类型\n  const getWorkTypes = () => {\n    const types = ['全部', ...new Set(data.map(item => item.重点工作类型).filter(Boolean))];\n    return types;\n  };\n\n  // 按工作类型分组数据\n  const getGroupedData = () => {\n    // 使用筛选后的数据而不是原始数据\n    const dataToUse = filteredData.length > 0 || selectedResponsiblePersons.length === 0 ? filteredData : data;\n    \n    if (!dataToUse || dataToUse.length === 0) return {};\n    \n    const grouped = {};\n    dataToUse.forEach(item => {\n      const type = item.重点工作类型 || '其他';\n      if (!grouped[type]) {\n        grouped[type] = [];\n      }\n      grouped[type].push(item);\n    });\n    \n    return grouped;\n  };\n\n  // 防抖保存函数\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      setSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用双向同步\n      await trackingService.updateTrackingData(rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        setSyncStatus('同步成功');\n        setTimeout(() => setSyncStatus('已同步'), 1000);\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (!error.name === 'AbortError') {\n        console.error('保存失败:', error);\n        setSyncStatus('同步失败');\n      }\n    }\n  }, []);\n\n  // 处理输入变化（实时更新UI，延迟保存）\n  const handleInputChange = (rowIndex, field, value) => {\n    // 立即更新UI显示\n    const newData = [...data];\n    newData[rowIndex][field] = value;\n    setData(newData);\n\n    // 存储临时值\n    const key = `${rowIndex}-${field}`;\n    setTempValues(prev => ({ ...prev, [key]: value }));\n\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 800); // 800ms防抖延迟\n  };\n\n  // 处理失焦保存（立即保存）\n  const handleBlurSave = async (rowIndex, field) => {\n    const key = `${rowIndex}-${field}`;\n    const value = tempValues[key];\n\n    if (value !== undefined) {\n      // 清除防抖定时器\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n        saveTimeoutRef.current = null;\n      }\n\n      // 立即保存\n      await debouncedSave(rowIndex, field, value);\n\n      // 清除临时值\n      setTempValues(prev => {\n        const newTemp = { ...prev };\n        delete newTemp[key];\n        return newTemp;\n      });\n    }\n  };\n\n  // 兼容原有的handleCellEdit接口\n  const handleCellEdit = async (rowIndex, field, value) => {\n    handleInputChange(rowIndex, field, value);\n  };\n\n  const startEdit = (rowIndex, field) => {\n    setEditingCell(`${rowIndex}-${field}`);\n  };\n\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 切换工作类型展开/折叠\n  const toggleTypeCollapse = (type) => {\n    const newCollapsed = new Set(collapsedTypes);\n    if (newCollapsed.has(type)) {\n      newCollapsed.delete(type);\n    } else {\n      newCollapsed.add(type);\n    }\n    setCollapsedTypes(newCollapsed);\n  };\n\n  // 全部展开/折叠控制\n  const toggleAllTypes = () => {\n    const allWorkTypes = new Set(data.map(item => item.重点工作类型).filter(Boolean));\n    \n    // 如果当前全部折叠或部分折叠，则全部展开\n    // 如果当前全部展开，则全部折叠\n    if (collapsedTypes.size === 0) {\n      // 当前全部展开，执行全部折叠\n      setCollapsedTypes(allWorkTypes);\n    } else {\n      // 当前全部折叠或部分折叠，执行全部展开\n      setCollapsedTypes(new Set());\n    }\n  };\n\n  // 获取全部展开/折叠按钮的显示状态\n  const getToggleAllStatus = () => {\n    const allWorkTypes = new Set(data.map(item => item.重点工作类型).filter(Boolean));\n    \n    if (collapsedTypes.size === 0) {\n      return { text: '全部折叠', icon: '▼' };\n    } else if (collapsedTypes.size === allWorkTypes.size) {\n      return { text: '全部展开', icon: '▶' };\n    } else {\n      return { text: '全部展开', icon: '▶' };\n    }\n  };\n\n  // 计算总工作数（使用筛选后的数据）\n  const getTotalWorkCount = () => {\n    const dataToUse = filteredData.length > 0 || selectedResponsiblePersons.length === 0 ? filteredData : data;\n    return dataToUse ? dataToUse.length : 0;\n  };\n\n  // 检测是否为纯数字\n  const isPureNumber = (value) => {\n    if (value === null || value === undefined || value === '') return false;\n    const strValue = String(value).trim();\n    return !isNaN(strValue) && !isNaN(parseFloat(strValue)) && isFinite(strValue);\n  };\n\n  // 处理Excel公式对象和格式化数据显示\n  const formatDisplayValue = (value) => {\n    // 处理null, undefined, 空字符串\n    if (value === null || value === undefined || value === '') {\n      return '';\n    }\n\n    // 处理Excel公式对象（[object Object]乱码问题）\n    if (typeof value === 'object') {\n      // 尝试提取对象中的值\n      if (value.hasOwnProperty('v')) {\n        value = value.v; // Excel.js格式\n      } else if (value.hasOwnProperty('w')) {\n        value = value.w; // 另一种Excel格式\n      } else if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) {\n        value = value.v; // 包含类型信息的格式\n      } else if (value.hasOwnProperty('result')) {\n        value = value.result; // 公式结果\n      } else {\n        // 如果无法提取有效值，返回空字符串而不是[object Object]\n        return '';\n      }\n    }\n\n    // 只对纯数字进行格式化\n    if (isPureNumber(value)) {\n      const numValue = parseFloat(value);\n      \n      // 检查是否为整数\n      if (Number.isInteger(numValue)) {\n        return String(numValue); // 整数按整数显示\n      } else {\n        return numValue.toFixed(2); // 小数保留两位小数\n      }\n    }\n\n    // 非纯数字内容保持原样\n    return String(value);\n  };\n\n  // 渲染可编辑单元格\n  const renderEditableCell = (value, rowIndex, field, className = '') => {\n    const cellKey = `${rowIndex}-${field}`;\n    const isEditing = editingCell === cellKey;\n    const isEditable = !['序号', '重点工作类型'].includes(field);\n    \n    // 格式化显示值\n    const displayValue = formatDisplayValue(value);\n\n    if (!isEditable) {\n      return <td className={`data-cell ${className}`}>{displayValue}</td>;\n    }\n\n    return (\n      <td \n        className={`data-cell editable ${className}`}\n        onClick={() => startEdit(rowIndex, field)}\n      >\n        {isEditing ? (\n          <input\n            type=\"text\"\n            value={value || ''}\n            onChange={(e) => handleInputChange(rowIndex, field, e.target.value)}\n            onBlur={() => {\n              handleBlurSave(rowIndex, field);\n              finishEdit();\n            }}\n            onKeyDown={(e) => {\n              if (e.key === 'Enter') {\n                handleBlurSave(rowIndex, field);\n                finishEdit();\n              }\n              if (e.key === 'Escape') {\n                finishEdit();\n              }\n            }}\n            autoFocus\n            className=\"cell-input\"\n          />\n        ) : (\n          <span className=\"cell-content\">{displayValue}</span>\n        )}\n      </td>\n    );\n  };\n\n  // 渲染月份列\n  const renderMonthColumns = (row, rowIndex) => {\n    if (currentMonthPair >= monthPairs.length) return null;\n    \n    const [month1, month2] = monthPairs[currentMonthPair];\n    const month1Plan = `${month1}工作计划`;\n    const month1Complete = `${month1}完成情况`;\n    const month2Plan = `${month2}工作计划`;\n    const month2Complete = `${month2}完成情况`;\n\n    return (\n      <>\n        {renderEditableCell(row[month1Plan], rowIndex, month1Plan, 'month-plan')}\n        {renderEditableCell(row[month1Complete], rowIndex, month1Complete, 'month-complete')}\n        {renderEditableCell(row[month2Plan], rowIndex, month2Plan, 'month-plan')}\n        {renderEditableCell(row[month2Complete], rowIndex, month2Complete, 'month-complete')}\n      </>\n    );\n  };\n\n  // 渲染数据表格\n  const renderDataTable = () => {\n    const groupedData = getGroupedData();\n    \n    if (Object.keys(groupedData).length === 0) {\n      return (\n        <div className=\"no-data\">\n          <p>暂无数据</p>\n        </div>\n      );\n    }\n\n    const currentPair = monthPairs[currentMonthPair] || ['', ''];\n    \n    return (\n      <div className=\"tracking-table-container\">\n        {Object.entries(groupedData).map(([type, items]) => {\n          const isCollapsed = collapsedTypes.has(type);\n          \n          return (\n            <div key={type} className=\"work-type-group\">\n              <div \n                className=\"group-header\"\n                onClick={() => toggleTypeCollapse(type)}\n              >\n                <span className=\"group-title\">\n                  <span className=\"collapse-icon\">{isCollapsed ? '▶' : '▼'}</span>\n                  {type} (<span className=\"item-count-highlight\">{items.length}项</span>)\n                </span>\n              </div>\n              \n              {!isCollapsed && (\n                <div className=\"table-container\">\n                  <table className=\"tracking-table\">\n                    <thead>\n                      <tr>\n                        <th className=\"col-number\">序号</th>\n                        <th className=\"col-type\">重点工作类型</th>\n                        <th className=\"col-indicator\">相关指标或方向</th>\n                        <th className=\"col-total-target\">总体目标值</th>\n                        <th className=\"col-target\">2025年目标</th>\n                        <th className=\"col-method\">计算方法&举措</th>\n                        <th className=\"col-responsible\">负责人</th>\n                        <th className=\"col-frequency\">跟踪频次</th>\n                        <th className=\"col-month-plan\">{currentPair[0]}工作计划</th>\n                        <th className=\"col-month-complete\">{currentPair[0]}完成情况</th>\n                        <th className=\"col-month-plan\">{currentPair[1]}工作计划</th>\n                        <th className=\"col-month-complete\">{currentPair[1]}完成情况</th>\n                      </tr>\n                    </thead>\n                    <tbody>\n                      {items.map((row, index) => {\n                        const globalIndex = data.findIndex(item => item === row);\n                        // 每个类型内部序号从1开始重新计算\n                        const typeSequenceNumber = index + 1;\n                        return (\n                          <tr key={globalIndex} className=\"data-row\">\n                            {renderEditableCell(typeSequenceNumber, globalIndex, '序号')}\n                            {renderEditableCell(row.重点工作类型, globalIndex, '重点工作类型')}\n                            {renderEditableCell(row.相关指标或方向, globalIndex, '相关指标或方向')}\n                            {renderEditableCell(row.总体目标值, globalIndex, '总体目标值')}\n                            {renderEditableCell(row['2025年目标'], globalIndex, '2025年目标')}\n                            {renderEditableCell(row['计算方法&2025年举措'], globalIndex, '计算方法&2025年举措')}\n                            {renderEditableCell(row.负责人, globalIndex, '负责人')}\n                            {renderEditableCell(row.跟踪频次, globalIndex, '跟踪频次')}\n                            {renderMonthColumns(row, globalIndex)}\n                          </tr>\n                        );\n                      })}\n                    </tbody>\n                  </table>\n                </div>\n              )}\n            </div>\n          );\n        })}\n      </div>\n    );\n  };\n\n  // 获取所有负责人列表\n  const getAllResponsiblePersons = () => {\n    if (!data || data.length === 0) return [];\n    \n    const responsiblePersons = data\n      .map(item => item.负责人)\n      .filter(person => person && String(person).trim() !== '')\n      .reduce((acc, person) => {\n        // 处理多个负责人用逗号、括号等分隔的情况\n        const persons = String(person).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);\n        persons.forEach(p => {\n          if (!acc.includes(p)) {\n            acc.push(p);\n          }\n        });\n        return acc;\n      }, []);\n    \n    return responsiblePersons.sort();\n  };\n\n  // 应用负责人筛选\n  const applyResponsibleFilter = () => {\n    if (!data || data.length === 0) {\n      setFilteredData([]);\n      return;\n    }\n\n    if (selectedResponsiblePersons.length === 0) {\n      // 如果没有选择任何负责人，显示所有数据\n      setFilteredData(data);\n    } else {\n      // 筛选包含选中负责人的数据行\n      const filtered = data.filter(item => {\n        if (!item.负责人) return false;\n        \n        // 检查责任人单元格是否包含任何选中的负责人（包含匹配）\n        return selectedResponsiblePersons.some(selectedPerson => \n          String(item.负责人).includes(selectedPerson)\n        );\n      });\n      \n      setFilteredData(filtered);\n    }\n  };\n\n  // 切换负责人筛选器显示状态\n  const toggleResponsibleFilter = () => {\n    setShowResponsibleFilter(!showResponsibleFilter);\n  };\n\n  // 处理负责人选择\n  const handleResponsiblePersonSelect = (person) => {\n    setSelectedResponsiblePersons(prev => {\n      if (prev.includes(person)) {\n        // 如果已选中，则取消选择\n        return prev.filter(p => p !== person);\n      } else {\n        // 如果未选中，则添加到选择列表\n        return [...prev, person];\n      }\n    });\n  };\n\n  // 清除所有筛选条件\n  const clearResponsibleFilter = () => {\n    setSelectedResponsiblePersons([]);\n    setShowResponsibleFilter(false);\n  };\n\n  // 处理选择性下载\n  const handleDownload = async (selectionData) => {\n    setDownloadLoading(true);\n    try {\n      const result = await workTrackingDownloadService.handleSelectiveDownload(selectionData);\n      if (result.success) {\n        console.log('下载成功:', result.message);\n        alert(result.message);\n      }\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败: ' + error.message);\n    } finally {\n      setDownloadLoading(false);\n      setShowDownloadModal(false);\n    }\n  };\n\n  // 打开下载模态框\n  const openDownloadModal = () => {\n    setShowDownloadModal(true);\n  };\n\n  // 关闭下载模态框\n  const closeDownloadModal = () => {\n    setShowDownloadModal(false);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"loading-container\">\n        <div className=\"loading-spinner\"></div>\n        <p>正在加载数据...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"work-tracking-container\">\n      {/* 负责人筛选面板 */}\n      {showResponsibleFilter && (\n        <>\n          <div \n            className=\"modal-backdrop\" \n            onClick={() => setShowResponsibleFilter(false)}\n            style={{\n              position: 'fixed',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.7)',\n              zIndex: 1999,\n              backdropFilter: 'blur(5px)'\n            }}\n          />\n          <div className=\"responsible-filter-panel\">\n            <div className=\"filter-panel-header\">\n              <h3>选择负责人</h3>\n              <button className=\"close-panel-btn\" onClick={() => setShowResponsibleFilter(false)}>×</button>\n            </div>\n          <div className=\"filter-panel-content\">\n            <div className=\"filter-options\">\n              <button \n                className={`filter-option ${selectedResponsiblePersons.length === 0 ? 'selected' : ''}`}\n                onClick={clearResponsibleFilter}\n              >\n                全部负责人\n              </button>\n              {getAllResponsiblePersons().map(person => (\n                <button\n                  key={person}\n                  className={`filter-option ${selectedResponsiblePersons.includes(person) ? 'selected' : ''}`}\n                  onClick={() => handleResponsiblePersonSelect(person)}\n                >\n                  {person.length > 4 ? person.substring(0, 4) : person}\n                </button>\n              ))}\n            </div>\n            <div className=\"filter-actions\">\n              <button className=\"clear-filter-btn\" onClick={clearResponsibleFilter}>\n                清除筛选\n              </button>\n              <button className=\"apply-filter-btn\" onClick={() => setShowResponsibleFilter(false)}>\n                应用筛选\n              </button>\n            </div>\n          </div>\n          </div>\n        </>\n      )}\n\n      {/* 页面头部 - 优化布局 */}\n      <div className=\"page-header\">\n      <button \n          className=\"back-btn-top\"\n        onClick={() => onNavigate('home')}\n        style={{ fontSize: '18px' }}\n      >\n        返回首页\n      </button>\n        \n        <div className=\"header-center\">\n          <h1 className=\"page-title\">开发中心重点工作跟踪-填写表</h1>\n          <p className=\"page-subtitle\">月度工作计划与完成情况跟踪</p>\n        </div>\n        \n        <div \n          className=\"sync-status\"\n          style={{ fontSize: '16px', marginRight: '10px' }}\n        >\n          <span \n            className={`status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}\n            style={{ fontSize: '18px' }}\n          >\n            {syncStatus}\n          </span>\n        </div>\n      </div>\n\n      {/* 筛选状态提示 - 重新设计 */}\n      {selectedResponsiblePersons.length > 0 && (\n        <div \n          style={{\n            background: 'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',\n            border: '1px solid rgba(0, 212, 170, 0.3)',\n            borderRadius: '12px',\n            margin: '20px auto',\n            width: '100%',\n            maxWidth: 'none',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(0, 212, 170, 0.2)'\n          }}\n        >\n          <div \n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              padding: '16px 24px',\n              gap: '16px'\n            }}\n          >\n            <div \n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',\n                borderRadius: '50%',\n                fontSize: '18px'\n              }}\n            >\n              🔍\n            </div>\n            <div style={{ flex: 1 }}>\n              <div \n                style={{\n                  fontSize: '14px',\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  marginBottom: '4px',\n                  fontWeight: '500'\n                }}\n              >\n                当前筛选条件\n              </div>\n              <div \n                style={{\n                  fontSize: '16px',\n                  color: '#fff',\n                  fontWeight: '600'\n                }}\n              >\n                {(() => {\n                  const workCount = getTotalWorkCount();\n                  if (selectedResponsiblePersons.length === 1) {\n                    return (\n                      <>\n                        负责人：\n                        <span style={{ \n                          color: '#20ff4d', \n                          fontWeight: 'bold',\n                          textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',\n                          margin: '0 8px'\n                        }}>\n                          {selectedResponsiblePersons[0]}\n                        </span>\n                        <span style={{ \n                          color: '#00d4aa',\n                          fontSize: '14px'\n                        }}>\n                          （{workCount}项工作）\n                        </span>\n                      </>\n                    );\n                  } else {\n                    return (\n                      <>\n                        负责人：\n                        <span style={{ \n                          color: '#20ff4d', \n                          fontWeight: 'bold',\n                          textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',\n                          margin: '0 8px'\n                        }}>\n                          {selectedResponsiblePersons.join('、')}\n                        </span>\n                        <span style={{ \n                          color: '#00d4aa',\n                          fontSize: '14px'\n                        }}>\n                          （{workCount}项工作）\n                        </span>\n                      </>\n                    );\n                  }\n                })()}\n              </div>\n            </div>\n            <button \n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                padding: '8px 16px',\n                background: 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',\n                border: '1px solid rgba(255, 75, 75, 0.4)',\n                borderRadius: '8px',\n                color: '#ff6b6b',\n                fontSize: '14px',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n              onClick={clearResponsibleFilter}\n              onMouseEnter={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';\n                e.target.style.transform = 'translateY(-1px)';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';\n                e.target.style.transform = 'translateY(0)';\n              }}\n              title=\"清除筛选条件\"\n            >\n              <span>✕</span>\n              <span>清除筛选</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 统一控制面板 - 重新排版布局 */}\n      <div className=\"unified-control-panel-v2\">\n        {/* 左侧：数据统计 */}\n        <div \n          className=\"stats-section-left\"\n          style={{ display: 'flex', flexDirection: 'row', gap: '15px', alignItems: 'center' }}\n        >\n          <div className=\"stat-card\">\n            <div className=\"stat-number\">{getTotalWorkCount()}</div>\n            <div className=\"stat-text\">总工作项</div>\n          </div>\n          <div className=\"stat-card\">\n            <div className=\"stat-number\">{getWorkTypes().length - 1}</div>\n            <div className=\"stat-text\">工作类型</div>\n          </div>\n          <div className=\"stat-card toggle-all-card\" onClick={toggleAllTypes}>\n            <div className=\"stat-number\">\n              <span className=\"toggle-icon\">{getToggleAllStatus().icon}</span>\n            </div>\n            <div className=\"stat-text\">{getToggleAllStatus().text}</div>\n          </div>\n        </div>\n\n        {/* 中间：负责人筛选按钮 */}\n        <div className=\"filter-section-center\">\n          <div className=\"tech-filter-module\" onClick={toggleResponsibleFilter}>\n            <div className=\"tech-module-background\">\n              <div className=\"circuit-pattern\"></div>\n              <div className=\"energy-flow\"></div>\n              <div className=\"hologram-border\"></div>\n            </div>\n            <div className=\"tech-module-content\">\n              <div \n                className=\"icon-container\"\n                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}\n              >\n                <div \n                  className=\"tech-icon\" \n                  style={{ \n                    fontSize: '0px',\n                    width: '68px',\n                    height: '68px',\n                    position: 'relative',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  }}\n                >\n                  {/* 科技感多人员筛选图标 */}\n                  <svg \n                    width=\"60\" \n                    height=\"60\" \n                    viewBox=\"0 0 32 32\" \n                    fill=\"none\"\n                    style={{\n                      filter: 'drop-shadow(0 0 15px rgba(0, 212, 170, 1)) drop-shadow(0 0 25px rgba(32, 255, 77, 0.5))'\n                    }}\n                  >\n                    {/* 背景数据网格 */}\n                    <g opacity=\"0.3\">\n                      <path d=\"M4 8L28 8M4 16L28 16M4 24L28 24\" stroke=\"url(#gridGradient)\" strokeWidth=\"0.5\"/>\n                      <path d=\"M8 4L8 28M16 4L16 28M24 4L24 28\" stroke=\"url(#gridGradient)\" strokeWidth=\"0.5\"/>\n                    </g>\n                    \n                    {/* 主要人物 - 中央 */}\n                    <g>\n                      {/* 头部 */}\n                      <circle cx=\"16\" cy=\"10\" r=\"3.5\" stroke=\"url(#mainGradient)\" strokeWidth=\"2\" fill=\"url(#headFill1)\"/>\n                      {/* 身体 */}\n                      <path d=\"M9 26V22C9 19.7909 10.7909 18 13 18H19C21.2091 18 23 19.7909 23 22V26\" \n                            stroke=\"url(#mainGradient)\" strokeWidth=\"2\" fill=\"url(#bodyFill1)\" strokeLinecap=\"round\"/>\n                      {/* 科技光环 */}\n                      <circle cx=\"16\" cy=\"10\" r=\"5\" stroke=\"rgba(0, 212, 170, 0.6)\" strokeWidth=\"1\" fill=\"none\">\n                        <animate attributeName=\"r\" values=\"5;6;5\" dur=\"3s\" repeatCount=\"indefinite\"/>\n                        <animate attributeName=\"opacity\" values=\"0.6;0.3;0.6\" dur=\"3s\" repeatCount=\"indefinite\"/>\n                      </circle>\n                    </g>\n                    \n                    {/* 左侧人物 - 较小 */}\n                    <g opacity=\"0.7\">\n                      <circle cx=\"7\" cy=\"12\" r=\"2.5\" stroke=\"url(#secondaryGradient)\" strokeWidth=\"1.5\" fill=\"url(#headFill2)\"/>\n                      <path d=\"M2 24V21C2 19.3431 3.34315 18 5 18H9C10.6569 18 12 19.3431 12 21V24\" \n                            stroke=\"url(#secondaryGradient)\" strokeWidth=\"1.5\" fill=\"url(#bodyFill2)\" strokeLinecap=\"round\"/>\n                      {/* 连接线 */}\n                      <path d=\"M9.5 12L13 10\" stroke=\"url(#connectGradient)\" strokeWidth=\"1\" strokeDasharray=\"2,1\">\n                        <animate attributeName=\"stroke-dashoffset\" values=\"0;3;0\" dur=\"2s\" repeatCount=\"indefinite\"/>\n                      </path>\n                    </g>\n                    \n                    {/* 右侧人物 - 较小 */}\n                    <g opacity=\"0.7\">\n                      <circle cx=\"25\" cy=\"12\" r=\"2.5\" stroke=\"url(#secondaryGradient)\" strokeWidth=\"1.5\" fill=\"url(#headFill2)\"/>\n                      <path d=\"M20 24V21C20 19.3431 21.3431 18 23 18H27C28.6569 18 30 19.3431 30 21V24\" \n                            stroke=\"url(#secondaryGradient)\" strokeWidth=\"1.5\" fill=\"url(#bodyFill2)\" strokeLinecap=\"round\"/>\n                      {/* 连接线 */}\n                      <path d=\"M22.5 12L19 10\" stroke=\"url(#connectGradient)\" strokeWidth=\"1\" strokeDasharray=\"2,1\">\n                        <animate attributeName=\"stroke-dashoffset\" values=\"0;3;0\" dur=\"2s\" repeatCount=\"indefinite\"/>\n                      </path>\n                    </g>\n                    \n                    {/* 数据节点 */}\n                    <g>\n                      <circle cx=\"16\" cy=\"6\" r=\"1\" fill=\"url(#nodeGradient)\">\n                        <animate attributeName=\"opacity\" values=\"1;0.3;1\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                      </circle>\n                      <circle cx=\"7\" cy=\"8\" r=\"0.8\" fill=\"url(#nodeGradient)\">\n                        <animate attributeName=\"opacity\" values=\"0.3;1;0.3\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                      </circle>\n                      <circle cx=\"25\" cy=\"8\" r=\"0.8\" fill=\"url(#nodeGradient)\">\n                        <animate attributeName=\"opacity\" values=\"1;0.3;1\" dur=\"1.5s\" repeatCount=\"indefinite\"/>\n                      </circle>\n                    </g>\n                    \n                    {/* 筛选象征 - 扫描线 */}\n                    <g opacity=\"0.8\">\n                      <line x1=\"2\" y1=\"15\" x2=\"30\" y2=\"15\" stroke=\"url(#scanGradient)\" strokeWidth=\"1.5\">\n                        <animate attributeName=\"opacity\" values=\"0;1;0\" dur=\"2.5s\" repeatCount=\"indefinite\"/>\n                        <animate attributeName=\"y1\" values=\"15;17;15\" dur=\"2.5s\" repeatCount=\"indefinite\"/>\n                        <animate attributeName=\"y2\" values=\"15;17;15\" dur=\"2.5s\" repeatCount=\"indefinite\"/>\n                      </line>\n                    </g>\n                    \n                    <defs>\n                      <linearGradient id=\"mainGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"#00d4aa\"/>\n                        <stop offset=\"50%\" stopColor=\"#20ff4d\"/>\n                        <stop offset=\"100%\" stopColor=\"#4dd0ff\"/>\n                      </linearGradient>\n                      \n                      <linearGradient id=\"secondaryGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"#4dd0ff\"/>\n                        <stop offset=\"100%\" stopColor=\"#00d4aa\"/>\n                      </linearGradient>\n                      \n                      <linearGradient id=\"connectGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(32, 255, 77, 0.8)\"/>\n                        <stop offset=\"50%\" stopColor=\"rgba(0, 212, 170, 1)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(77, 208, 255, 0.8)\"/>\n                      </linearGradient>\n                      \n                      <linearGradient id=\"scanGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                        <stop offset=\"0%\" stopColor=\"transparent\"/>\n                        <stop offset=\"20%\" stopColor=\"#20ff4d\"/>\n                        <stop offset=\"50%\" stopColor=\"#00d4aa\"/>\n                        <stop offset=\"80%\" stopColor=\"#4dd0ff\"/>\n                        <stop offset=\"100%\" stopColor=\"transparent\"/>\n                      </linearGradient>\n                      \n                      <linearGradient id=\"gridGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(0, 212, 170, 0.3)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(77, 208, 255, 0.3)\"/>\n                      </linearGradient>\n                      \n                      <radialGradient id=\"headFill1\" cx=\"50%\" cy=\"30%\" r=\"80%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(32, 255, 77, 0.4)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(0, 212, 170, 0.1)\"/>\n                      </radialGradient>\n                      \n                      <radialGradient id=\"headFill2\" cx=\"50%\" cy=\"30%\" r=\"80%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(77, 208, 255, 0.3)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(0, 212, 170, 0.1)\"/>\n                      </radialGradient>\n                      \n                      <linearGradient id=\"bodyFill1\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(0, 212, 170, 0.25)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(32, 255, 77, 0.1)\"/>\n                      </linearGradient>\n                      \n                      <linearGradient id=\"bodyFill2\" x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\">\n                        <stop offset=\"0%\" stopColor=\"rgba(77, 208, 255, 0.2)\"/>\n                        <stop offset=\"100%\" stopColor=\"rgba(0, 212, 170, 0.1)\"/>\n                      </linearGradient>\n                      \n                      <radialGradient id=\"nodeGradient\" cx=\"50%\" cy=\"50%\" r=\"50%\">\n                        <stop offset=\"0%\" stopColor=\"#20ff4d\"/>\n                        <stop offset=\"100%\" stopColor=\"#00d4aa\"/>\n                      </radialGradient>\n                    </defs>\n                  </svg>\n                </div>\n                <div className=\"scan-line\"></div>\n              </div>\n              <div \n                className=\"module-info\"\n                style={{ textAlign: 'center' }}\n              >\n                <div className=\"module-title\">责任人筛选</div>\n                {selectedResponsiblePersons.length > 0 && (\n                  <div className=\"active-indicator\">\n                    <span className=\"pulse-dot\"></span>\n                  </div>\n                )}\n              </div>\n            </div>\n            <div className=\"tech-accent-lines\">\n              <div className=\"accent-line accent-line-1\"></div>\n              <div className=\"accent-line accent-line-2\"></div>\n              <div className=\"accent-line accent-line-3\"></div>\n            </div>\n          </div>\n        </div>\n\n        {/* 右侧：月份导航和操作按钮 */}\n        <div className=\"controls-section\">\n          <div className=\"month-navigation-new\">\n            <button \n              className=\"nav-btn-new\"\n              onClick={() => setCurrentMonthPair(Math.max(0, currentMonthPair - 1))}\n              disabled={currentMonthPair === 0}\n            >\n              ◀ 上个月份对\n            </button>\n            <span className=\"current-months-new\">\n              {monthPairs[currentMonthPair] ? \n                `${monthPairs[currentMonthPair][0]} / ${monthPairs[currentMonthPair][1]}` : \n                '月份加载中'\n              }\n            </span>\n            <button \n              className=\"nav-btn-new\"\n              onClick={() => setCurrentMonthPair(Math.min(monthPairs.length - 1, currentMonthPair + 1))}\n              disabled={currentMonthPair >= monthPairs.length - 1}\n            >\n              下个月份对 ▶\n            </button>\n          </div>\n\n          <div className=\"action-buttons-group\">\n            <button \n              className=\"refresh-btn-new\"\n              onClick={loadData}\n            >\n              🔄 刷新数据\n            </button>\n            \n            <button \n              className=\"download-btn-tracking\"\n              onClick={openDownloadModal}\n              disabled={downloadLoading}\n            >\n              {downloadLoading ? (\n                <>\n                  <span className=\"loading-spinner-small\"></span>\n                  处理中...\n                </>\n              ) : (\n                <>\n                  📈 选择性下载\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* 数据表格 */}\n      {renderDataTable()}\n\n      {/* 选择性下载模态框 */}\n      <WorkTrackingDownloadModal\n        isOpen={showDownloadModal}\n        onClose={closeDownloadModal}\n        data={data}\n        filteredData={filteredData}\n        selectedResponsiblePersons={selectedResponsiblePersons}\n        currentMonthPair={currentMonthPair}\n        monthPairs={monthPairs}\n        onDownload={handleDownload}\n      />\n    </div>\n  );\n};\n\nexport default WorkTracking; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,+BAA+B,CACtC,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,MAAO,CAAAC,yBAAyB,KAAM,yCAAyC,CAC/E,MAAO,CAAAC,2BAA2B,KAAM,yCAAyC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElF,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAClC;AACA,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE;AAClD,KAAM,CAAA0B,cAAc,CAAGxB,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAAyB,cAAc,CAAGzB,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAC0B,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG7B,QAAQ,CAAC,EAAE,CAAC,CAChF,KAAM,CAAC8B,qBAAqB,CAAEC,wBAAwB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACzE,KAAM,CAACgC,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAEpD;AACA,KAAM,CAACkC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnC,QAAQ,CAAC,IAAM,CAC7D;AACA,KAAM,CAAAoC,YAAY,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAE;AAChD,KAAM,CAAAC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAEpF;AACA,GAAI,CAAAC,YAAY,CAAG,CAAC,CAAC,CACrBD,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC/B,KAAM,CAAAC,QAAQ,CAAGC,QAAQ,CAACH,KAAK,CAACI,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAAC,CACjD,GAAIF,QAAQ,GAAKR,YAAY,CAAE,CAC7BI,YAAY,CAAGG,KAAK,CACtB,CACF,CAAC,CAAC,CAEF;AACA,GAAIH,YAAY,EAAI,CAAC,CAAE,CACrB;AACA,GAAIA,YAAY,GAAK,CAAC,CAAE,MAAO,EAAC,CAChC;AACA,MAAO,CAAAO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAER,YAAY,CAAG,CAAC,CAAC,CACtC,CAEA;AACA,MAAO,EAAC,CACV,CAAC,CAAC,CAAE;AACJ,KAAM,CAACS,cAAc,CAAEC,iBAAiB,CAAC,CAAGlD,QAAQ,CAAC,GAAI,CAAAmD,GAAG,CAAC,CAAC,CAAC,CAE/D;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrD,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACsD,eAAe,CAAEC,kBAAkB,CAAC,CAAGvD,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAAuC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACpF,KAAM,CAAAiB,UAAU,CAAG,EAAE,CACrB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGlB,MAAM,CAACmB,MAAM,CAAG,CAAC,CAAED,CAAC,EAAE,CAAE,CAC1CD,UAAU,CAACG,IAAI,CAAC,CAACpB,MAAM,CAACkB,CAAC,CAAC,CAAElB,MAAM,CAACkB,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC,CAC7C,CAEAxD,SAAS,CAAC,IAAM,CACd2D,QAAQ,CAAC,CAAC,CAEV;AACAxD,eAAe,CAACyD,gBAAgB,CAC9B,IAAMtC,aAAa,CAAC,MAAM,CAAC,CAC1BuC,KAAK,EAAKvC,aAAa,CAAC,MAAM,CACjC,CAAC,CAED;AACA,MAAO,IAAM,CACX,GAAIG,cAAc,CAACqC,OAAO,CAAE,CAC1BC,YAAY,CAACtC,cAAc,CAACqC,OAAO,CAAC,CACtC,CACA,GAAIpC,cAAc,CAACoC,OAAO,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAAvC,cAAc,CAACoC,OAAO,EAACI,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACAjE,SAAS,CAAC,IAAM,CACdoE,sBAAsB,CAAC,CAAC,CAC1B,CAAC,CAAE,CAACrD,IAAI,CAAEY,0BAA0B,CAAC,CAAC,CAEtC;AACA3B,SAAS,CAAC,IAAM,CACd,GAAIe,IAAI,EAAIA,IAAI,CAAC0C,MAAM,CAAG,CAAC,EAAI1B,YAAY,CAAC0B,MAAM,GAAK,CAAC,EAAI9B,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAE,CACnGzB,eAAe,CAACjB,IAAI,CAAC,CACvB,CACF,CAAC,CAAE,CAACA,IAAI,CAAC,CAAC,CAEV,KAAM,CAAA4C,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BzC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACFmD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9B,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAApE,eAAe,CAACqE,gBAAgB,CAAC,CAAC,CAC7DH,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEC,YAAY,CAAC,CAErC,GAAIA,YAAY,CAAE,CAChBvD,OAAO,CAACuD,YAAY,CAAC,CACrBF,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEC,YAAY,CAACd,MAAM,CAAE,OAAO,CAAC,CAErD;AACA,KAAM,CAAAgB,YAAY,CAAG,GAAI,CAAAvB,GAAG,CAACqB,YAAY,CAACG,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CACnF7B,iBAAiB,CAACwB,YAAY,CAAC,CACjC,CAAC,IAAM,CACLJ,OAAO,CAACR,KAAK,CAAC,UAAU,CAAC,CAC3B,CACF,CAAE,MAAOA,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACA3C,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAA6D,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAC,KAAK,CAAG,CAAC,IAAI,CAAE,GAAG,GAAI,CAAA9B,GAAG,CAACnC,IAAI,CAAC2D,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC,CAC/E,MAAO,CAAAE,KAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,KAAM,CAAAC,SAAS,CAAGnD,YAAY,CAAC0B,MAAM,CAAG,CAAC,EAAI9B,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAG1B,YAAY,CAAGhB,IAAI,CAE1G,GAAI,CAACmE,SAAS,EAAIA,SAAS,CAACzB,MAAM,GAAK,CAAC,CAAE,MAAO,CAAC,CAAC,CAEnD,KAAM,CAAA0B,OAAO,CAAG,CAAC,CAAC,CAClBD,SAAS,CAAC1C,OAAO,CAACmC,IAAI,EAAI,CACxB,KAAM,CAAAS,IAAI,CAAGT,IAAI,CAACC,MAAM,EAAI,IAAI,CAChC,GAAI,CAACO,OAAO,CAACC,IAAI,CAAC,CAAE,CAClBD,OAAO,CAACC,IAAI,CAAC,CAAG,EAAE,CACpB,CACAD,OAAO,CAACC,IAAI,CAAC,CAAC1B,IAAI,CAACiB,IAAI,CAAC,CAC1B,CAAC,CAAC,CAEF,MAAO,CAAAQ,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAE,aAAa,CAAGnF,WAAW,CAAC,MAAOoF,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CAClE,GAAI,CACFlE,aAAa,CAAC,QAAQ,CAAC,CAEvB;AACA,GAAII,cAAc,CAACoC,OAAO,CAAE,KAAA2B,sBAAA,CAAAC,sBAAA,CAC1B,CAAAD,sBAAA,EAAAC,sBAAA,CAAAhE,cAAc,CAACoC,OAAO,EAACI,KAAK,UAAAuB,sBAAA,iBAA5BA,sBAAA,CAAAtB,IAAA,CAAAuB,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxClE,cAAc,CAACoC,OAAO,CAAG6B,UAAU,CAEnC;AACA,KAAM,CAAAxF,eAAe,CAAC0F,kBAAkB,CAACP,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAEhE;AACA,GAAI,CAACG,UAAU,CAACG,MAAM,CAACC,OAAO,CAAE,CAC9BzE,aAAa,CAAC,MAAM,CAAC,CACrB0E,UAAU,CAAC,IAAM1E,aAAa,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC5CI,cAAc,CAACoC,OAAO,CAAG,IAAI,CAC/B,CACF,CAAE,MAAOD,KAAK,CAAE,CACd,GAAI,CAACA,KAAK,CAACoC,IAAI,GAAK,YAAY,CAAE,CAChC5B,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BvC,aAAa,CAAC,MAAM,CAAC,CACvB,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAA4E,iBAAiB,CAAGA,CAACZ,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACpD;AACA,KAAM,CAAAW,OAAO,CAAG,CAAC,GAAGpF,IAAI,CAAC,CACzBoF,OAAO,CAACb,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CAChCxE,OAAO,CAACmF,OAAO,CAAC,CAEhB;AACA,KAAM,CAAAC,GAAG,IAAAC,MAAA,CAAMf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAClC/D,aAAa,CAAC8E,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,GAAG,EAAGZ,KAAK,EAAG,CAAC,CAElD;AACA,GAAI/D,cAAc,CAACqC,OAAO,CAAE,CAC1BC,YAAY,CAACtC,cAAc,CAACqC,OAAO,CAAC,CACtC,CAEA;AACArC,cAAc,CAACqC,OAAO,CAAGkC,UAAU,CAAC,IAAM,CACxCX,aAAa,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CACvC,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,CAED;AACA,KAAM,CAAAgB,cAAc,CAAG,KAAAA,CAAOlB,QAAQ,CAAEC,KAAK,GAAK,CAChD,KAAM,CAAAa,GAAG,IAAAC,MAAA,CAAMf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAClC,KAAM,CAAAC,KAAK,CAAGjE,UAAU,CAAC6E,GAAG,CAAC,CAE7B,GAAIZ,KAAK,GAAKiB,SAAS,CAAE,CACvB;AACA,GAAIhF,cAAc,CAACqC,OAAO,CAAE,CAC1BC,YAAY,CAACtC,cAAc,CAACqC,OAAO,CAAC,CACpCrC,cAAc,CAACqC,OAAO,CAAG,IAAI,CAC/B,CAEA;AACA,KAAM,CAAAuB,aAAa,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE3C;AACAhE,aAAa,CAAC8E,IAAI,EAAI,CACpB,KAAM,CAAAI,OAAO,CAAAH,aAAA,IAAQD,IAAI,CAAE,CAC3B,MAAO,CAAAI,OAAO,CAACN,GAAG,CAAC,CACnB,MAAO,CAAAM,OAAO,CAChB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,KAAAA,CAAOrB,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACvDU,iBAAiB,CAACZ,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAoB,SAAS,CAAGA,CAACtB,QAAQ,CAAEC,KAAK,GAAK,CACrCnE,cAAc,IAAAiF,MAAA,CAAIf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAAC,CACxC,CAAC,CAED,KAAM,CAAAsB,UAAU,CAAGA,CAAA,GAAM,CACvBzF,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAA0F,kBAAkB,CAAI1B,IAAI,EAAK,CACnC,KAAM,CAAA2B,YAAY,CAAG,GAAI,CAAA7D,GAAG,CAACF,cAAc,CAAC,CAC5C,GAAI+D,YAAY,CAACC,GAAG,CAAC5B,IAAI,CAAC,CAAE,CAC1B2B,YAAY,CAACE,MAAM,CAAC7B,IAAI,CAAC,CAC3B,CAAC,IAAM,CACL2B,YAAY,CAACG,GAAG,CAAC9B,IAAI,CAAC,CACxB,CACAnC,iBAAiB,CAAC8D,YAAY,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAI,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAA1C,YAAY,CAAG,GAAI,CAAAvB,GAAG,CAACnC,IAAI,CAAC2D,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAE3E;AACA;AACA,GAAI9B,cAAc,CAACoE,IAAI,GAAK,CAAC,CAAE,CAC7B;AACAnE,iBAAiB,CAACwB,YAAY,CAAC,CACjC,CAAC,IAAM,CACL;AACAxB,iBAAiB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAC9B,CACF,CAAC,CAED;AACA,KAAM,CAAAmE,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAA5C,YAAY,CAAG,GAAI,CAAAvB,GAAG,CAACnC,IAAI,CAAC2D,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAAC,CAE3E,GAAI9B,cAAc,CAACoE,IAAI,GAAK,CAAC,CAAE,CAC7B,MAAO,CAAEE,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,GAAI,CAAC,CACpC,CAAC,IAAM,IAAIvE,cAAc,CAACoE,IAAI,GAAK3C,YAAY,CAAC2C,IAAI,CAAE,CACpD,MAAO,CAAEE,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,GAAI,CAAC,CACpC,CAAC,IAAM,CACL,MAAO,CAAED,IAAI,CAAE,MAAM,CAAEC,IAAI,CAAE,GAAI,CAAC,CACpC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAtC,SAAS,CAAGnD,YAAY,CAAC0B,MAAM,CAAG,CAAC,EAAI9B,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAG1B,YAAY,CAAGhB,IAAI,CAC1G,MAAO,CAAAmE,SAAS,CAAGA,SAAS,CAACzB,MAAM,CAAG,CAAC,CACzC,CAAC,CAED;AACA,KAAM,CAAAgE,YAAY,CAAIjC,KAAK,EAAK,CAC9B,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKiB,SAAS,EAAIjB,KAAK,GAAK,EAAE,CAAE,MAAO,MAAK,CACvE,KAAM,CAAAkC,QAAQ,CAAGC,MAAM,CAACnC,KAAK,CAAC,CAACoC,IAAI,CAAC,CAAC,CACrC,MAAO,CAACC,KAAK,CAACH,QAAQ,CAAC,EAAI,CAACG,KAAK,CAACC,UAAU,CAACJ,QAAQ,CAAC,CAAC,EAAIK,QAAQ,CAACL,QAAQ,CAAC,CAC/E,CAAC,CAED;AACA,KAAM,CAAAM,kBAAkB,CAAIxC,KAAK,EAAK,CACpC;AACA,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKiB,SAAS,EAAIjB,KAAK,GAAK,EAAE,CAAE,CACzD,MAAO,EAAE,CACX,CAEA;AACA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B;AACA,GAAIA,KAAK,CAACyC,cAAc,CAAC,GAAG,CAAC,CAAE,CAC7BzC,KAAK,CAAGA,KAAK,CAAC0C,CAAC,CAAE;AACnB,CAAC,IAAM,IAAI1C,KAAK,CAACyC,cAAc,CAAC,GAAG,CAAC,CAAE,CACpCzC,KAAK,CAAGA,KAAK,CAAC2C,CAAC,CAAE;AACnB,CAAC,IAAM,IAAI3C,KAAK,CAACyC,cAAc,CAAC,GAAG,CAAC,EAAIzC,KAAK,CAACyC,cAAc,CAAC,GAAG,CAAC,CAAE,CACjEzC,KAAK,CAAGA,KAAK,CAAC0C,CAAC,CAAE;AACnB,CAAC,IAAM,IAAI1C,KAAK,CAACyC,cAAc,CAAC,QAAQ,CAAC,CAAE,CACzCzC,KAAK,CAAGA,KAAK,CAAC4C,MAAM,CAAE;AACxB,CAAC,IAAM,CACL;AACA,MAAO,EAAE,CACX,CACF,CAEA;AACA,GAAIX,YAAY,CAACjC,KAAK,CAAC,CAAE,CACvB,KAAM,CAAA6C,QAAQ,CAAGP,UAAU,CAACtC,KAAK,CAAC,CAElC;AACA,GAAI8C,MAAM,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAE,CAC9B,MAAO,CAAAV,MAAM,CAACU,QAAQ,CAAC,CAAE;AAC3B,CAAC,IAAM,CACL,MAAO,CAAAA,QAAQ,CAACG,OAAO,CAAC,CAAC,CAAC,CAAE;AAC9B,CACF,CAEA;AACA,MAAO,CAAAb,MAAM,CAACnC,KAAK,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAiD,kBAAkB,CAAG,QAAAA,CAACjD,KAAK,CAAEF,QAAQ,CAAEC,KAAK,CAAqB,IAAnB,CAAAmD,SAAS,CAAAC,SAAA,CAAAlF,MAAA,IAAAkF,SAAA,MAAAlC,SAAA,CAAAkC,SAAA,IAAG,EAAE,CAChE,KAAM,CAAAC,OAAO,IAAAvC,MAAA,CAAMf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CACtC,KAAM,CAAAsD,SAAS,CAAG1H,WAAW,GAAKyH,OAAO,CACzC,KAAM,CAAAE,UAAU,CAAG,CAAC,CAAC,IAAI,CAAE,QAAQ,CAAC,CAACC,QAAQ,CAACxD,KAAK,CAAC,CAEpD;AACA,KAAM,CAAAyD,YAAY,CAAGhB,kBAAkB,CAACxC,KAAK,CAAC,CAE9C,GAAI,CAACsD,UAAU,CAAE,CACf,mBAAOvI,IAAA,OAAImI,SAAS,cAAArC,MAAA,CAAeqC,SAAS,CAAG,CAAAO,QAAA,CAAED,YAAY,CAAK,CAAC,CACrE,CAEA,mBACEzI,IAAA,OACEmI,SAAS,uBAAArC,MAAA,CAAwBqC,SAAS,CAAG,CAC7CQ,OAAO,CAAEA,CAAA,GAAMtC,SAAS,CAACtB,QAAQ,CAAEC,KAAK,CAAE,CAAA0D,QAAA,CAEzCJ,SAAS,cACRtI,IAAA,UACE6E,IAAI,CAAC,MAAM,CACXI,KAAK,CAAEA,KAAK,EAAI,EAAG,CACnB2D,QAAQ,CAAGC,CAAC,EAAKlD,iBAAiB,CAACZ,QAAQ,CAAEC,KAAK,CAAE6D,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE,CACpE8D,MAAM,CAAEA,CAAA,GAAM,CACZ9C,cAAc,CAAClB,QAAQ,CAAEC,KAAK,CAAC,CAC/BsB,UAAU,CAAC,CAAC,CACd,CAAE,CACF0C,SAAS,CAAGH,CAAC,EAAK,CAChB,GAAIA,CAAC,CAAChD,GAAG,GAAK,OAAO,CAAE,CACrBI,cAAc,CAAClB,QAAQ,CAAEC,KAAK,CAAC,CAC/BsB,UAAU,CAAC,CAAC,CACd,CACA,GAAIuC,CAAC,CAAChD,GAAG,GAAK,QAAQ,CAAE,CACtBS,UAAU,CAAC,CAAC,CACd,CACF,CAAE,CACF2C,SAAS,MACTd,SAAS,CAAC,YAAY,CACvB,CAAC,cAEFnI,IAAA,SAAMmI,SAAS,CAAC,cAAc,CAAAO,QAAA,CAAED,YAAY,CAAO,CACpD,CACC,CAAC,CAET,CAAC,CAED;AACA,KAAM,CAAAS,kBAAkB,CAAGA,CAACC,GAAG,CAAEpE,QAAQ,GAAK,CAC5C,GAAIrD,gBAAgB,EAAIsB,UAAU,CAACE,MAAM,CAAE,MAAO,KAAI,CAEtD,KAAM,CAACkG,MAAM,CAAEC,MAAM,CAAC,CAAGrG,UAAU,CAACtB,gBAAgB,CAAC,CACrD,KAAM,CAAA4H,UAAU,IAAAxD,MAAA,CAAMsD,MAAM,4BAAM,CAClC,KAAM,CAAAG,cAAc,IAAAzD,MAAA,CAAMsD,MAAM,4BAAM,CACtC,KAAM,CAAAI,UAAU,IAAA1D,MAAA,CAAMuD,MAAM,4BAAM,CAClC,KAAM,CAAAI,cAAc,IAAA3D,MAAA,CAAMuD,MAAM,4BAAM,CAEtC,mBACEjJ,KAAA,CAAAF,SAAA,EAAAwI,QAAA,EACGR,kBAAkB,CAACiB,GAAG,CAACG,UAAU,CAAC,CAAEvE,QAAQ,CAAEuE,UAAU,CAAE,YAAY,CAAC,CACvEpB,kBAAkB,CAACiB,GAAG,CAACI,cAAc,CAAC,CAAExE,QAAQ,CAAEwE,cAAc,CAAE,gBAAgB,CAAC,CACnFrB,kBAAkB,CAACiB,GAAG,CAACK,UAAU,CAAC,CAAEzE,QAAQ,CAAEyE,UAAU,CAAE,YAAY,CAAC,CACvEtB,kBAAkB,CAACiB,GAAG,CAACM,cAAc,CAAC,CAAE1E,QAAQ,CAAE0E,cAAc,CAAE,gBAAgB,CAAC,EACpF,CAAC,CAEP,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,WAAW,CAAGjF,cAAc,CAAC,CAAC,CAEpC,GAAIkF,MAAM,CAACC,IAAI,CAACF,WAAW,CAAC,CAACzG,MAAM,GAAK,CAAC,CAAE,CACzC,mBACElD,IAAA,QAAKmI,SAAS,CAAC,SAAS,CAAAO,QAAA,cACtB1I,IAAA,MAAA0I,QAAA,CAAG,0BAAI,CAAG,CAAC,CACR,CAAC,CAEV,CAEA,KAAM,CAAAoB,WAAW,CAAG9G,UAAU,CAACtB,gBAAgB,CAAC,EAAI,CAAC,EAAE,CAAE,EAAE,CAAC,CAE5D,mBACE1B,IAAA,QAAKmI,SAAS,CAAC,0BAA0B,CAAAO,QAAA,CACtCkB,MAAM,CAACG,OAAO,CAACJ,WAAW,CAAC,CAACxF,GAAG,CAAC6F,KAAA,EAAmB,IAAlB,CAACnF,IAAI,CAAEoF,KAAK,CAAC,CAAAD,KAAA,CAC7C,KAAM,CAAAE,WAAW,CAAGzH,cAAc,CAACgE,GAAG,CAAC5B,IAAI,CAAC,CAE5C,mBACEzE,KAAA,QAAgB+H,SAAS,CAAC,iBAAiB,CAAAO,QAAA,eACzC1I,IAAA,QACEmI,SAAS,CAAC,cAAc,CACxBQ,OAAO,CAAEA,CAAA,GAAMpC,kBAAkB,CAAC1B,IAAI,CAAE,CAAA6D,QAAA,cAExCtI,KAAA,SAAM+H,SAAS,CAAC,aAAa,CAAAO,QAAA,eAC3B1I,IAAA,SAAMmI,SAAS,CAAC,eAAe,CAAAO,QAAA,CAAEwB,WAAW,CAAG,GAAG,CAAG,GAAG,CAAO,CAAC,CAC/DrF,IAAI,CAAC,IAAE,cAAAzE,KAAA,SAAM+H,SAAS,CAAC,sBAAsB,CAAAO,QAAA,EAAEuB,KAAK,CAAC/G,MAAM,CAAC,QAAC,EAAM,CAAC,IACvE,EAAM,CAAC,CACJ,CAAC,CAEL,CAACgH,WAAW,eACXlK,IAAA,QAAKmI,SAAS,CAAC,iBAAiB,CAAAO,QAAA,cAC9BtI,KAAA,UAAO+H,SAAS,CAAC,gBAAgB,CAAAO,QAAA,eAC/B1I,IAAA,UAAA0I,QAAA,cACEtI,KAAA,OAAAsI,QAAA,eACE1I,IAAA,OAAImI,SAAS,CAAC,YAAY,CAAAO,QAAA,CAAC,cAAE,CAAI,CAAC,cAClC1I,IAAA,OAAImI,SAAS,CAAC,UAAU,CAAAO,QAAA,CAAC,sCAAM,CAAI,CAAC,cACpC1I,IAAA,OAAImI,SAAS,CAAC,eAAe,CAAAO,QAAA,CAAC,4CAAO,CAAI,CAAC,cAC1C1I,IAAA,OAAImI,SAAS,CAAC,kBAAkB,CAAAO,QAAA,CAAC,gCAAK,CAAI,CAAC,cAC3C1I,IAAA,OAAImI,SAAS,CAAC,YAAY,CAAAO,QAAA,CAAC,wBAAO,CAAI,CAAC,cACvC1I,IAAA,OAAImI,SAAS,CAAC,YAAY,CAAAO,QAAA,CAAC,uCAAO,CAAI,CAAC,cACvC1I,IAAA,OAAImI,SAAS,CAAC,iBAAiB,CAAAO,QAAA,CAAC,oBAAG,CAAI,CAAC,cACxC1I,IAAA,OAAImI,SAAS,CAAC,eAAe,CAAAO,QAAA,CAAC,0BAAI,CAAI,CAAC,cACvCtI,KAAA,OAAI+H,SAAS,CAAC,gBAAgB,CAAAO,QAAA,EAAEoB,WAAW,CAAC,CAAC,CAAC,CAAC,0BAAI,EAAI,CAAC,cACxD1J,KAAA,OAAI+H,SAAS,CAAC,oBAAoB,CAAAO,QAAA,EAAEoB,WAAW,CAAC,CAAC,CAAC,CAAC,0BAAI,EAAI,CAAC,cAC5D1J,KAAA,OAAI+H,SAAS,CAAC,gBAAgB,CAAAO,QAAA,EAAEoB,WAAW,CAAC,CAAC,CAAC,CAAC,0BAAI,EAAI,CAAC,cACxD1J,KAAA,OAAI+H,SAAS,CAAC,oBAAoB,CAAAO,QAAA,EAAEoB,WAAW,CAAC,CAAC,CAAC,CAAC,0BAAI,EAAI,CAAC,EAC1D,CAAC,CACA,CAAC,cACR9J,IAAA,UAAA0I,QAAA,CACGuB,KAAK,CAAC9F,GAAG,CAAC,CAACgF,GAAG,CAAEhH,KAAK,GAAK,CACzB,KAAM,CAAAgI,WAAW,CAAG3J,IAAI,CAAC4J,SAAS,CAAChG,IAAI,EAAIA,IAAI,GAAK+E,GAAG,CAAC,CACxD;AACA,KAAM,CAAAkB,kBAAkB,CAAGlI,KAAK,CAAG,CAAC,CACpC,mBACE/B,KAAA,OAAsB+H,SAAS,CAAC,UAAU,CAAAO,QAAA,EACvCR,kBAAkB,CAACmC,kBAAkB,CAAEF,WAAW,CAAE,IAAI,CAAC,CACzDjC,kBAAkB,CAACiB,GAAG,CAAC9E,MAAM,CAAE8F,WAAW,CAAE,QAAQ,CAAC,CACrDjC,kBAAkB,CAACiB,GAAG,CAACmB,OAAO,CAAEH,WAAW,CAAE,SAAS,CAAC,CACvDjC,kBAAkB,CAACiB,GAAG,CAACoB,KAAK,CAAEJ,WAAW,CAAE,OAAO,CAAC,CACnDjC,kBAAkB,CAACiB,GAAG,CAAC,SAAS,CAAC,CAAEgB,WAAW,CAAE,SAAS,CAAC,CAC1DjC,kBAAkB,CAACiB,GAAG,CAAC,cAAc,CAAC,CAAEgB,WAAW,CAAE,cAAc,CAAC,CACpEjC,kBAAkB,CAACiB,GAAG,CAACqB,GAAG,CAAEL,WAAW,CAAE,KAAK,CAAC,CAC/CjC,kBAAkB,CAACiB,GAAG,CAACsB,IAAI,CAAEN,WAAW,CAAE,MAAM,CAAC,CACjDjB,kBAAkB,CAACC,GAAG,CAAEgB,WAAW,CAAC,GAT9BA,WAUL,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CACN,GApDOtF,IAqDL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA6F,wBAAwB,CAAGA,CAAA,GAAM,CACrC,GAAI,CAAClK,IAAI,EAAIA,IAAI,CAAC0C,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAEzC,KAAM,CAAAyH,kBAAkB,CAAGnK,IAAI,CAC5B2D,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACoG,GAAG,CAAC,CACrBlG,MAAM,CAACsG,MAAM,EAAIA,MAAM,EAAIxD,MAAM,CAACwD,MAAM,CAAC,CAACvD,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACxDwD,MAAM,CAAC,CAACC,GAAG,CAAEF,MAAM,GAAK,CACvB;AACA,KAAM,CAAAG,OAAO,CAAG3D,MAAM,CAACwD,MAAM,CAAC,CAACI,KAAK,CAAC,aAAa,CAAC,CAAC7G,GAAG,CAAC8G,CAAC,EAAIA,CAAC,CAAC5D,IAAI,CAAC,CAAC,CAAC,CAAC/C,MAAM,CAAC2G,CAAC,EAAIA,CAAC,CAAC,CACrFF,OAAO,CAAC9I,OAAO,CAACgJ,CAAC,EAAI,CACnB,GAAI,CAACH,GAAG,CAACtC,QAAQ,CAACyC,CAAC,CAAC,CAAE,CACpBH,GAAG,CAAC3H,IAAI,CAAC8H,CAAC,CAAC,CACb,CACF,CAAC,CAAC,CACF,MAAO,CAAAH,GAAG,CACZ,CAAC,CAAE,EAAE,CAAC,CAER,MAAO,CAAAH,kBAAkB,CAACO,IAAI,CAAC,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAArH,sBAAsB,CAAGA,CAAA,GAAM,CACnC,GAAI,CAACrD,IAAI,EAAIA,IAAI,CAAC0C,MAAM,GAAK,CAAC,CAAE,CAC9BzB,eAAe,CAAC,EAAE,CAAC,CACnB,OACF,CAEA,GAAIL,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAE,CAC3C;AACAzB,eAAe,CAACjB,IAAI,CAAC,CACvB,CAAC,IAAM,CACL;AACA,KAAM,CAAA2K,QAAQ,CAAG3K,IAAI,CAAC8D,MAAM,CAACF,IAAI,EAAI,CACnC,GAAI,CAACA,IAAI,CAACoG,GAAG,CAAE,MAAO,MAAK,CAE3B;AACA,MAAO,CAAApJ,0BAA0B,CAACgK,IAAI,CAACC,cAAc,EACnDjE,MAAM,CAAChD,IAAI,CAACoG,GAAG,CAAC,CAAChC,QAAQ,CAAC6C,cAAc,CAC1C,CAAC,CACH,CAAC,CAAC,CAEF5J,eAAe,CAAC0J,QAAQ,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAG,uBAAuB,CAAGA,CAAA,GAAM,CACpC/J,wBAAwB,CAAC,CAACD,qBAAqB,CAAC,CAClD,CAAC,CAED;AACA,KAAM,CAAAiK,6BAA6B,CAAIX,MAAM,EAAK,CAChDvJ,6BAA6B,CAAC0E,IAAI,EAAI,CACpC,GAAIA,IAAI,CAACyC,QAAQ,CAACoC,MAAM,CAAC,CAAE,CACzB;AACA,MAAO,CAAA7E,IAAI,CAACzB,MAAM,CAAC2G,CAAC,EAAIA,CAAC,GAAKL,MAAM,CAAC,CACvC,CAAC,IAAM,CACL;AACA,MAAO,CAAC,GAAG7E,IAAI,CAAE6E,MAAM,CAAC,CAC1B,CACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAY,sBAAsB,CAAGA,CAAA,GAAM,CACnCnK,6BAA6B,CAAC,EAAE,CAAC,CACjCE,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAAkK,cAAc,CAAG,KAAO,CAAAC,aAAa,EAAK,CAC9C3I,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAA8E,MAAM,CAAG,KAAM,CAAA/H,2BAA2B,CAAC6L,uBAAuB,CAACD,aAAa,CAAC,CACvF,GAAI7D,MAAM,CAAC+D,OAAO,CAAE,CAClB9H,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE8D,MAAM,CAACgE,OAAO,CAAC,CACpCC,KAAK,CAACjE,MAAM,CAACgE,OAAO,CAAC,CACvB,CACF,CAAE,MAAOvI,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BwI,KAAK,CAAC,QAAQ,CAAGxI,KAAK,CAACuI,OAAO,CAAC,CACjC,CAAC,OAAS,CACR9I,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAAkJ,iBAAiB,CAAGA,CAAA,GAAM,CAC9BlJ,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAmJ,kBAAkB,CAAGA,CAAA,GAAM,CAC/BnJ,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,GAAInC,OAAO,CAAE,CACX,mBACEN,KAAA,QAAK+H,SAAS,CAAC,mBAAmB,CAAAO,QAAA,eAChC1I,IAAA,QAAKmI,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCnI,IAAA,MAAA0I,QAAA,CAAG,yCAAS,CAAG,CAAC,EACb,CAAC,CAEV,CAEA,mBACEtI,KAAA,QAAK+H,SAAS,CAAC,yBAAyB,CAAAO,QAAA,EAErCpH,qBAAqB,eACpBlB,KAAA,CAAAF,SAAA,EAAAwI,QAAA,eACE1I,IAAA,QACEmI,SAAS,CAAC,gBAAgB,CAC1BQ,OAAO,CAAEA,CAAA,GAAMpH,wBAAwB,CAAC,KAAK,CAAE,CAC/C0K,KAAK,CAAE,CACLC,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,eAAe,CAAE,oBAAoB,CACrCC,MAAM,CAAE,IAAI,CACZC,cAAc,CAAE,WAClB,CAAE,CACH,CAAC,cACFrM,KAAA,QAAK+H,SAAS,CAAC,0BAA0B,CAAAO,QAAA,eACvCtI,KAAA,QAAK+H,SAAS,CAAC,qBAAqB,CAAAO,QAAA,eAClC1I,IAAA,OAAA0I,QAAA,CAAI,gCAAK,CAAI,CAAC,cACd1I,IAAA,WAAQmI,SAAS,CAAC,iBAAiB,CAACQ,OAAO,CAAEA,CAAA,GAAMpH,wBAAwB,CAAC,KAAK,CAAE,CAAAmH,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC3F,CAAC,cACRtI,KAAA,QAAK+H,SAAS,CAAC,sBAAsB,CAAAO,QAAA,eACnCtI,KAAA,QAAK+H,SAAS,CAAC,gBAAgB,CAAAO,QAAA,eAC7B1I,IAAA,WACEmI,SAAS,kBAAArC,MAAA,CAAmB1E,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAG,UAAU,CAAG,EAAE,CAAG,CACxFyF,OAAO,CAAE6C,sBAAuB,CAAA9C,QAAA,CACjC,gCAED,CAAQ,CAAC,CACRgC,wBAAwB,CAAC,CAAC,CAACvG,GAAG,CAACyG,MAAM,eACpC5K,IAAA,WAEEmI,SAAS,kBAAArC,MAAA,CAAmB1E,0BAA0B,CAACoH,QAAQ,CAACoC,MAAM,CAAC,CAAG,UAAU,CAAG,EAAE,CAAG,CAC5FjC,OAAO,CAAEA,CAAA,GAAM4C,6BAA6B,CAACX,MAAM,CAAE,CAAAlC,QAAA,CAEpDkC,MAAM,CAAC1H,MAAM,CAAG,CAAC,CAAG0H,MAAM,CAAC8B,SAAS,CAAC,CAAC,CAAE,CAAC,CAAC,CAAG9B,MAAM,EAJ/CA,MAKC,CACT,CAAC,EACC,CAAC,cACNxK,KAAA,QAAK+H,SAAS,CAAC,gBAAgB,CAAAO,QAAA,eAC7B1I,IAAA,WAAQmI,SAAS,CAAC,kBAAkB,CAACQ,OAAO,CAAE6C,sBAAuB,CAAA9C,QAAA,CAAC,0BAEtE,CAAQ,CAAC,cACT1I,IAAA,WAAQmI,SAAS,CAAC,kBAAkB,CAACQ,OAAO,CAAEA,CAAA,GAAMpH,wBAAwB,CAAC,KAAK,CAAE,CAAAmH,QAAA,CAAC,0BAErF,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACD,CAAC,EACN,CACH,cAGDtI,KAAA,QAAK+H,SAAS,CAAC,aAAa,CAAAO,QAAA,eAC5B1I,IAAA,WACImI,SAAS,CAAC,cAAc,CAC1BQ,OAAO,CAAEA,CAAA,GAAMpI,UAAU,CAAC,MAAM,CAAE,CAClC0L,KAAK,CAAE,CAAEU,QAAQ,CAAE,MAAO,CAAE,CAAAjE,QAAA,CAC7B,0BAED,CAAQ,CAAC,cAEPtI,KAAA,QAAK+H,SAAS,CAAC,eAAe,CAAAO,QAAA,eAC5B1I,IAAA,OAAImI,SAAS,CAAC,YAAY,CAAAO,QAAA,CAAC,iFAAc,CAAI,CAAC,cAC9C1I,IAAA,MAAGmI,SAAS,CAAC,eAAe,CAAAO,QAAA,CAAC,gFAAa,CAAG,CAAC,EAC3C,CAAC,cAEN1I,IAAA,QACEmI,SAAS,CAAC,aAAa,CACvB8D,KAAK,CAAE,CAAEU,QAAQ,CAAE,MAAM,CAAEC,WAAW,CAAE,MAAO,CAAE,CAAAlE,QAAA,cAEjD1I,IAAA,SACEmI,SAAS,qBAAArC,MAAA,CAAsBhF,UAAU,CAAC0H,QAAQ,CAAC,IAAI,CAAC,CAAG,SAAS,CAAG1H,UAAU,CAAC0H,QAAQ,CAAC,IAAI,CAAC,CAAG,OAAO,CAAG,SAAS,CAAG,CACzHyD,KAAK,CAAE,CAAEU,QAAQ,CAAE,MAAO,CAAE,CAAAjE,QAAA,CAE3B5H,UAAU,CACP,CAAC,CACJ,CAAC,EACH,CAAC,CAGLM,0BAA0B,CAAC8B,MAAM,CAAG,CAAC,eACpClD,IAAA,QACEiM,KAAK,CAAE,CACLY,UAAU,CAAE,yEAAyE,CACrFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,WAAW,CACnBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBT,cAAc,CAAE,YAAY,CAC5BU,SAAS,CAAE,mCACb,CAAE,CAAAzE,QAAA,cAEFtI,KAAA,QACE6L,KAAK,CAAE,CACLmB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,WAAW,CACpBC,GAAG,CAAE,MACP,CAAE,CAAA7E,QAAA,eAEF1I,IAAA,QACEiM,KAAK,CAAE,CACLmB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,cAAc,CAAE,QAAQ,CACxBP,KAAK,CAAE,MAAM,CACbQ,MAAM,CAAE,MAAM,CACdZ,UAAU,CAAE,0CAA0C,CACtDE,YAAY,CAAE,KAAK,CACnBJ,QAAQ,CAAE,MACZ,CAAE,CAAAjE,QAAA,CACH,cAED,CAAK,CAAC,cACNtI,KAAA,QAAK6L,KAAK,CAAE,CAAEyB,IAAI,CAAE,CAAE,CAAE,CAAAhF,QAAA,eACtB1I,IAAA,QACEiM,KAAK,CAAE,CACLU,QAAQ,CAAE,MAAM,CAChBgB,KAAK,CAAE,0BAA0B,CACjCC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KACd,CAAE,CAAAnF,QAAA,CACH,sCAED,CAAK,CAAC,cACN1I,IAAA,QACEiM,KAAK,CAAE,CACLU,QAAQ,CAAE,MAAM,CAChBgB,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,KACd,CAAE,CAAAnF,QAAA,CAED,CAAC,IAAM,CACN,KAAM,CAAAoF,SAAS,CAAG7G,iBAAiB,CAAC,CAAC,CACrC,GAAI7F,0BAA0B,CAAC8B,MAAM,GAAK,CAAC,CAAE,CAC3C,mBACE9C,KAAA,CAAAF,SAAA,EAAAwI,QAAA,EAAE,0BAEA,cAAA1I,IAAA,SAAMiM,KAAK,CAAE,CACX0B,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,MAAM,CAClBE,UAAU,CAAE,iCAAiC,CAC7Cf,MAAM,CAAE,OACV,CAAE,CAAAtE,QAAA,CACCtH,0BAA0B,CAAC,CAAC,CAAC,CAC1B,CAAC,cACPhB,KAAA,SAAM6L,KAAK,CAAE,CACX0B,KAAK,CAAE,SAAS,CAChBhB,QAAQ,CAAE,MACZ,CAAE,CAAAjE,QAAA,EAAC,QACA,CAACoF,SAAS,CAAC,0BACd,EAAM,CAAC,EACP,CAAC,CAEP,CAAC,IAAM,CACL,mBACE1N,KAAA,CAAAF,SAAA,EAAAwI,QAAA,EAAE,0BAEA,cAAA1I,IAAA,SAAMiM,KAAK,CAAE,CACX0B,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,MAAM,CAClBE,UAAU,CAAE,iCAAiC,CAC7Cf,MAAM,CAAE,OACV,CAAE,CAAAtE,QAAA,CACCtH,0BAA0B,CAAC4M,IAAI,CAAC,GAAG,CAAC,CACjC,CAAC,cACP5N,KAAA,SAAM6L,KAAK,CAAE,CACX0B,KAAK,CAAE,SAAS,CAChBhB,QAAQ,CAAE,MACZ,CAAE,CAAAjE,QAAA,EAAC,QACA,CAACoF,SAAS,CAAC,0BACd,EAAM,CAAC,EACP,CAAC,CAEP,CACF,CAAC,EAAE,CAAC,CACD,CAAC,EACH,CAAC,cACN1N,KAAA,WACE6L,KAAK,CAAE,CACLmB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,KAAK,CACVD,OAAO,CAAE,UAAU,CACnBT,UAAU,CAAE,0EAA0E,CACtFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,KAAK,CACnBY,KAAK,CAAE,SAAS,CAChBhB,QAAQ,CAAE,MAAM,CAChBkB,UAAU,CAAE,KAAK,CACjBI,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,eACd,CAAE,CACFvF,OAAO,CAAE6C,sBAAuB,CAChC2C,YAAY,CAAGtF,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACmD,KAAK,CAACY,UAAU,CAAG,0EAA0E,CACtGhE,CAAC,CAACC,MAAM,CAACmD,KAAK,CAACmC,SAAS,CAAG,kBAAkB,CAC/C,CAAE,CACFC,YAAY,CAAGxF,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACmD,KAAK,CAACY,UAAU,CAAG,0EAA0E,CACtGhE,CAAC,CAACC,MAAM,CAACmD,KAAK,CAACmC,SAAS,CAAG,eAAe,CAC5C,CAAE,CACFE,KAAK,CAAC,sCAAQ,CAAA5F,QAAA,eAEd1I,IAAA,SAAA0I,QAAA,CAAM,QAAC,CAAM,CAAC,cACd1I,IAAA,SAAA0I,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,EACN,CAAC,CACH,CACN,cAGDtI,KAAA,QAAK+H,SAAS,CAAC,0BAA0B,CAAAO,QAAA,eAEvCtI,KAAA,QACE+H,SAAS,CAAC,oBAAoB,CAC9B8D,KAAK,CAAE,CAAEmB,OAAO,CAAE,MAAM,CAAEmB,aAAa,CAAE,KAAK,CAAEhB,GAAG,CAAE,MAAM,CAAEF,UAAU,CAAE,QAAS,CAAE,CAAA3E,QAAA,eAEpFtI,KAAA,QAAK+H,SAAS,CAAC,WAAW,CAAAO,QAAA,eACxB1I,IAAA,QAAKmI,SAAS,CAAC,aAAa,CAAAO,QAAA,CAAEzB,iBAAiB,CAAC,CAAC,CAAM,CAAC,cACxDjH,IAAA,QAAKmI,SAAS,CAAC,WAAW,CAAAO,QAAA,CAAC,0BAAI,CAAK,CAAC,EAClC,CAAC,cACNtI,KAAA,QAAK+H,SAAS,CAAC,WAAW,CAAAO,QAAA,eACxB1I,IAAA,QAAKmI,SAAS,CAAC,aAAa,CAAAO,QAAA,CAAElE,YAAY,CAAC,CAAC,CAACtB,MAAM,CAAG,CAAC,CAAM,CAAC,cAC9DlD,IAAA,QAAKmI,SAAS,CAAC,WAAW,CAAAO,QAAA,CAAC,0BAAI,CAAK,CAAC,EAClC,CAAC,cACNtI,KAAA,QAAK+H,SAAS,CAAC,2BAA2B,CAACQ,OAAO,CAAE/B,cAAe,CAAA8B,QAAA,eACjE1I,IAAA,QAAKmI,SAAS,CAAC,aAAa,CAAAO,QAAA,cAC1B1I,IAAA,SAAMmI,SAAS,CAAC,aAAa,CAAAO,QAAA,CAAE5B,kBAAkB,CAAC,CAAC,CAACE,IAAI,CAAO,CAAC,CAC7D,CAAC,cACNhH,IAAA,QAAKmI,SAAS,CAAC,WAAW,CAAAO,QAAA,CAAE5B,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAAM,CAAC,EACzD,CAAC,EACH,CAAC,cAGN/G,IAAA,QAAKmI,SAAS,CAAC,uBAAuB,CAAAO,QAAA,cACpCtI,KAAA,QAAK+H,SAAS,CAAC,oBAAoB,CAACQ,OAAO,CAAE2C,uBAAwB,CAAA5C,QAAA,eACnEtI,KAAA,QAAK+H,SAAS,CAAC,wBAAwB,CAAAO,QAAA,eACrC1I,IAAA,QAAKmI,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCnI,IAAA,QAAKmI,SAAS,CAAC,aAAa,CAAM,CAAC,cACnCnI,IAAA,QAAKmI,SAAS,CAAC,iBAAiB,CAAM,CAAC,EACpC,CAAC,cACN/H,KAAA,QAAK+H,SAAS,CAAC,qBAAqB,CAAAO,QAAA,eAClCtI,KAAA,QACE+H,SAAS,CAAC,gBAAgB,CAC1B8D,KAAK,CAAE,CAAEmB,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEG,cAAc,CAAE,QAAS,CAAE,CAAA9E,QAAA,eAE3E1I,IAAA,QACEmI,SAAS,CAAC,WAAW,CACrB8D,KAAK,CAAE,CACLU,QAAQ,CAAE,KAAK,CACfM,KAAK,CAAE,MAAM,CACbQ,MAAM,CAAE,MAAM,CACdvB,QAAQ,CAAE,UAAU,CACpBkB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,cAAc,CAAE,QAClB,CAAE,CAAA9E,QAAA,cAGFtI,KAAA,QACE6M,KAAK,CAAC,IAAI,CACVQ,MAAM,CAAC,IAAI,CACXe,OAAO,CAAC,WAAW,CACnBC,IAAI,CAAC,MAAM,CACXxC,KAAK,CAAE,CACL3H,MAAM,CAAE,yFACV,CAAE,CAAAoE,QAAA,eAGFtI,KAAA,MAAGsO,OAAO,CAAC,KAAK,CAAAhG,QAAA,eACd1I,IAAA,SAAM2O,CAAC,CAAC,iCAAiC,CAACC,MAAM,CAAC,oBAAoB,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC,cACzF7O,IAAA,SAAM2O,CAAC,CAAC,iCAAiC,CAACC,MAAM,CAAC,oBAAoB,CAACC,WAAW,CAAC,KAAK,CAAC,CAAC,EACxF,CAAC,cAGJzO,KAAA,MAAAsI,QAAA,eAEE1I,IAAA,WAAQ8O,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACJ,MAAM,CAAC,oBAAoB,CAACC,WAAW,CAAC,GAAG,CAACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,cAEpGzO,IAAA,SAAM2O,CAAC,CAAC,uEAAuE,CACzEC,MAAM,CAAC,oBAAoB,CAACC,WAAW,CAAC,GAAG,CAACJ,IAAI,CAAC,iBAAiB,CAACQ,aAAa,CAAC,OAAO,CAAC,CAAC,cAEhG7O,KAAA,WAAQ0O,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,GAAG,CAACJ,MAAM,CAAC,wBAAwB,CAACC,WAAW,CAAC,GAAG,CAACJ,IAAI,CAAC,MAAM,CAAA/F,QAAA,eACvF1I,IAAA,YAASkP,aAAa,CAAC,GAAG,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,cAC7ErP,IAAA,YAASkP,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,aAAa,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,EACnF,CAAC,EACR,CAAC,cAGJjP,KAAA,MAAGsO,OAAO,CAAC,KAAK,CAAAhG,QAAA,eACd1I,IAAA,WAAQ8O,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACJ,MAAM,CAAC,yBAAyB,CAACC,WAAW,CAAC,KAAK,CAACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,cAC1GzO,IAAA,SAAM2O,CAAC,CAAC,qEAAqE,CACvEC,MAAM,CAAC,yBAAyB,CAACC,WAAW,CAAC,KAAK,CAACJ,IAAI,CAAC,iBAAiB,CAACQ,aAAa,CAAC,OAAO,CAAC,CAAC,cAEvGjP,IAAA,SAAM2O,CAAC,CAAC,eAAe,CAACC,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAACS,eAAe,CAAC,KAAK,CAAA5G,QAAA,cAC1F1I,IAAA,YAASkP,aAAa,CAAC,mBAAmB,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,CACzF,CAAC,EACN,CAAC,cAGJjP,KAAA,MAAGsO,OAAO,CAAC,KAAK,CAAAhG,QAAA,eACd1I,IAAA,WAAQ8O,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,CAAC,CAAC,KAAK,CAACJ,MAAM,CAAC,yBAAyB,CAACC,WAAW,CAAC,KAAK,CAACJ,IAAI,CAAC,iBAAiB,CAAC,CAAC,cAC3GzO,IAAA,SAAM2O,CAAC,CAAC,yEAAyE,CAC3EC,MAAM,CAAC,yBAAyB,CAACC,WAAW,CAAC,KAAK,CAACJ,IAAI,CAAC,iBAAiB,CAACQ,aAAa,CAAC,OAAO,CAAC,CAAC,cAEvGjP,IAAA,SAAM2O,CAAC,CAAC,gBAAgB,CAACC,MAAM,CAAC,uBAAuB,CAACC,WAAW,CAAC,GAAG,CAACS,eAAe,CAAC,KAAK,CAAA5G,QAAA,cAC3F1I,IAAA,YAASkP,aAAa,CAAC,mBAAmB,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,CACzF,CAAC,EACN,CAAC,cAGJjP,KAAA,MAAAsI,QAAA,eACE1I,IAAA,WAAQ8O,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,GAAG,CAACP,IAAI,CAAC,oBAAoB,CAAA/F,QAAA,cACpD1I,IAAA,YAASkP,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,SAAS,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,CACjF,CAAC,cACTrP,IAAA,WAAQ8O,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,KAAK,CAACP,IAAI,CAAC,oBAAoB,CAAA/F,QAAA,cACrD1I,IAAA,YAASkP,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,WAAW,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,CACnF,CAAC,cACTrP,IAAA,WAAQ8O,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,GAAG,CAACC,CAAC,CAAC,KAAK,CAACP,IAAI,CAAC,oBAAoB,CAAA/F,QAAA,cACtD1I,IAAA,YAASkP,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,SAAS,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,CACjF,CAAC,EACR,CAAC,cAGJrP,IAAA,MAAG0O,OAAO,CAAC,KAAK,CAAAhG,QAAA,cACdtI,KAAA,SAAMmP,EAAE,CAAC,GAAG,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACd,MAAM,CAAC,oBAAoB,CAACC,WAAW,CAAC,KAAK,CAAAnG,QAAA,eAChF1I,IAAA,YAASkP,aAAa,CAAC,SAAS,CAACC,MAAM,CAAC,OAAO,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,cACrFrP,IAAA,YAASkP,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,UAAU,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,cACnFrP,IAAA,YAASkP,aAAa,CAAC,IAAI,CAACC,MAAM,CAAC,UAAU,CAACC,GAAG,CAAC,MAAM,CAACC,WAAW,CAAC,YAAY,CAAC,CAAC,EAC/E,CAAC,CACN,CAAC,cAEJjP,KAAA,SAAAsI,QAAA,eACEtI,KAAA,mBAAgBuP,EAAE,CAAC,cAAc,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,MAAM,CAAAhH,QAAA,eACnE1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACvC7P,IAAA,SAAM4P,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACxC7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,EAC3B,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,mBAAmB,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,MAAM,CAAAhH,QAAA,eACxE1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACvC7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,EAC3B,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,iBAAiB,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,IAAI,CAAAhH,QAAA,eACpE1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,cACtD7P,IAAA,SAAM4P,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,sBAAsB,CAAC,CAAC,cACrD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC,EAC3C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,cAAc,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,IAAI,CAAAhH,QAAA,eACjE1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,aAAa,CAAC,CAAC,cAC3C7P,IAAA,SAAM4P,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACxC7P,IAAA,SAAM4P,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACxC7P,IAAA,SAAM4P,MAAM,CAAC,KAAK,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACxC7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,aAAa,CAAC,CAAC,EAC/B,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,cAAc,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAACC,EAAE,CAAC,MAAM,CAAAhH,QAAA,eACnE1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,cACtD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC,EAC3C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,WAAW,CAACb,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAAtG,QAAA,eACtD1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,cACtD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,EAC1C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,WAAW,CAACb,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAAtG,QAAA,eACtD1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC,cACvD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,EAC1C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,WAAW,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAAAhH,QAAA,eAC9D1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC,cACvD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,EAC1C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,WAAW,CAACJ,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,IAAI,CAACC,EAAE,CAAC,MAAM,CAAAhH,QAAA,eAC9D1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,yBAAyB,CAAC,CAAC,cACvD7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,wBAAwB,CAAC,CAAC,EAC1C,CAAC,cAEjBzP,KAAA,mBAAgBuP,EAAE,CAAC,cAAc,CAACb,EAAE,CAAC,KAAK,CAACC,EAAE,CAAC,KAAK,CAACC,CAAC,CAAC,KAAK,CAAAtG,QAAA,eACzD1I,IAAA,SAAM4P,MAAM,CAAC,IAAI,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,cACvC7P,IAAA,SAAM4P,MAAM,CAAC,MAAM,CAACC,SAAS,CAAC,SAAS,CAAC,CAAC,EAC3B,CAAC,EACb,CAAC,EACJ,CAAC,CACH,CAAC,cACN7P,IAAA,QAAKmI,SAAS,CAAC,WAAW,CAAM,CAAC,EAC9B,CAAC,cACN/H,KAAA,QACE+H,SAAS,CAAC,aAAa,CACvB8D,KAAK,CAAE,CAAE6D,SAAS,CAAE,QAAS,CAAE,CAAApH,QAAA,eAE/B1I,IAAA,QAAKmI,SAAS,CAAC,cAAc,CAAAO,QAAA,CAAC,gCAAK,CAAK,CAAC,CACxCtH,0BAA0B,CAAC8B,MAAM,CAAG,CAAC,eACpClD,IAAA,QAAKmI,SAAS,CAAC,kBAAkB,CAAAO,QAAA,cAC/B1I,IAAA,SAAMmI,SAAS,CAAC,WAAW,CAAO,CAAC,CAChC,CACN,EACE,CAAC,EACH,CAAC,cACN/H,KAAA,QAAK+H,SAAS,CAAC,mBAAmB,CAAAO,QAAA,eAChC1I,IAAA,QAAKmI,SAAS,CAAC,2BAA2B,CAAM,CAAC,cACjDnI,IAAA,QAAKmI,SAAS,CAAC,2BAA2B,CAAM,CAAC,cACjDnI,IAAA,QAAKmI,SAAS,CAAC,2BAA2B,CAAM,CAAC,EAC9C,CAAC,EACH,CAAC,CACH,CAAC,cAGN/H,KAAA,QAAK+H,SAAS,CAAC,kBAAkB,CAAAO,QAAA,eAC/BtI,KAAA,QAAK+H,SAAS,CAAC,sBAAsB,CAAAO,QAAA,eACnC1I,IAAA,WACEmI,SAAS,CAAC,aAAa,CACvBQ,OAAO,CAAEA,CAAA,GAAMhH,mBAAmB,CAACY,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEd,gBAAgB,CAAG,CAAC,CAAC,CAAE,CACtEqO,QAAQ,CAAErO,gBAAgB,GAAK,CAAE,CAAAgH,QAAA,CAClC,uCAED,CAAQ,CAAC,cACT1I,IAAA,SAAMmI,SAAS,CAAC,oBAAoB,CAAAO,QAAA,CACjC1F,UAAU,CAACtB,gBAAgB,CAAC,IAAAoE,MAAA,CACxB9C,UAAU,CAACtB,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAAAoE,MAAA,CAAM9C,UAAU,CAACtB,gBAAgB,CAAC,CAAC,CAAC,CAAC,EACvE,OAAO,CAEL,CAAC,cACP1B,IAAA,WACEmI,SAAS,CAAC,aAAa,CACvBQ,OAAO,CAAEA,CAAA,GAAMhH,mBAAmB,CAACY,IAAI,CAACyN,GAAG,CAAChN,UAAU,CAACE,MAAM,CAAG,CAAC,CAAExB,gBAAgB,CAAG,CAAC,CAAC,CAAE,CAC1FqO,QAAQ,CAAErO,gBAAgB,EAAIsB,UAAU,CAACE,MAAM,CAAG,CAAE,CAAAwF,QAAA,CACrD,uCAED,CAAQ,CAAC,EACN,CAAC,cAENtI,KAAA,QAAK+H,SAAS,CAAC,sBAAsB,CAAAO,QAAA,eACnC1I,IAAA,WACEmI,SAAS,CAAC,iBAAiB,CAC3BQ,OAAO,CAAEvF,QAAS,CAAAsF,QAAA,CACnB,uCAED,CAAQ,CAAC,cAET1I,IAAA,WACEmI,SAAS,CAAC,uBAAuB,CACjCQ,OAAO,CAAEoD,iBAAkB,CAC3BgE,QAAQ,CAAEjN,eAAgB,CAAA4F,QAAA,CAEzB5F,eAAe,cACd1C,KAAA,CAAAF,SAAA,EAAAwI,QAAA,eACE1I,IAAA,SAAMmI,SAAS,CAAC,uBAAuB,CAAO,CAAC,wBAEjD,EAAE,CAAC,cAEHnI,IAAA,CAAAE,SAAA,EAAAwI,QAAA,CAAE,6CAEF,CAAE,CACH,CACK,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAGLgB,eAAe,CAAC,CAAC,cAGlB1J,IAAA,CAACH,yBAAyB,EACxBoQ,MAAM,CAAErN,iBAAkB,CAC1BsN,OAAO,CAAElE,kBAAmB,CAC5BxL,IAAI,CAAEA,IAAK,CACXgB,YAAY,CAAEA,YAAa,CAC3BJ,0BAA0B,CAAEA,0BAA2B,CACvDM,gBAAgB,CAAEA,gBAAiB,CACnCsB,UAAU,CAAEA,UAAW,CACvBmN,UAAU,CAAE1E,cAAe,CAC5B,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAApL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}