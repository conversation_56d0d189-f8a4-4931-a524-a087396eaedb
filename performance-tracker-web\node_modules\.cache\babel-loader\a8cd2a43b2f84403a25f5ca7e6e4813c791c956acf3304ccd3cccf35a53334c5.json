{"ast": null, "code": "class TrackingService{constructor(){this.data=[];this.lastModified=null;this.syncCallbacks={onSuccess:null,onError:null};}// 加载重点工作跟踪数据\nasync loadTrackingData(){try{console.log('正在从API加载重点工作跟踪数据...');const response=await fetch('http://localhost:3001/api/tracking-data');if(response.ok){const result=await response.json();if(result.data&&Array.isArray(result.data)){this.data=result.data;this.lastModified=new Date(result.lastModified);console.log('重点工作跟踪数据加载成功:',this.data.length,'条记录');return this.data;}else{console.warn('API返回的跟踪数据格式不正确');return[];}}else{console.warn('跟踪数据API响应失败，状态码:',response.status);return[];}}catch(error){console.error('从API加载重点工作跟踪数据失败:',error);return[];}}// 更新跟踪数据\nasync updateTrackingData(rowIndex,field,value){try{console.log('更新跟踪数据:',{rowIndex,field,value});// 更新本地数据\nif(this.data[rowIndex]){this.data[rowIndex][field]=value;}// 同步到后端\nconst response=await fetch('http://localhost:3001/api/update-tracking-excel',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({rowIndex,field,value})});if(response.ok){const result=await response.json();if(result.success){console.log('跟踪数据更新成功');if(this.syncCallbacks.onSuccess){this.syncCallbacks.onSuccess();}return true;}else{console.error('跟踪数据更新失败:',result.message);if(this.syncCallbacks.onError){this.syncCallbacks.onError(result.message);}return false;}}else{console.error('跟踪数据更新请求失败，状态码:',response.status);if(this.syncCallbacks.onError){this.syncCallbacks.onError(\"HTTP \".concat(response.status));}return false;}}catch(error){console.error('跟踪数据更新错误:',error);if(this.syncCallbacks.onError){this.syncCallbacks.onError(error.message);}return false;}}// 获取所有重点工作类型\ngetWorkTypes(){const types=[...new Set(this.data.map(item=>item.重点工作类型).filter(type=>type&&type.trim()!==''))];return types;}// 按工作类型分组数据\ngetDataByWorkType(){const grouped={};this.data.forEach(item=>{const type=item.重点工作类型||'未分类';if(!grouped[type]){grouped[type]=[];}grouped[type].push(item);});return grouped;}// 获取月份列表\ngetMonthlyFields(){const months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];const fields=[];months.forEach(month=>{fields.push(\"\".concat(month,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"));fields.push(\"\".concat(month,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));});return fields;}// 获取统计数据\ngetTrackingStats(){const workTypes=this.getWorkTypes();const totalRecords=this.data.length;const monthlyFields=this.getMonthlyFields();// 计算完成度统计\nlet totalFields=0;let completedFields=0;this.data.forEach(item=>{monthlyFields.forEach(field=>{totalFields++;if(item[field]&&item[field].toString().trim()!==''){completedFields++;}});});const completionRate=totalFields>0?(completedFields/totalFields*100).toFixed(1):0;return{totalRecords,workTypeCount:workTypes.length,completionRate,monthlyFieldCount:monthlyFields.length};}// 设置同步回调\nsetSyncCallbacks(onSuccess,onError){this.syncCallbacks.onSuccess=onSuccess;this.syncCallbacks.onError=onError;}// 获取原始数据\ngetData(){return this.data;}}// 创建单例实例\nconst trackingService=new TrackingService();export default trackingService;", "map": {"version": 3, "names": ["TrackingService", "constructor", "data", "lastModified", "syncCallbacks", "onSuccess", "onError", "loadTrackingData", "console", "log", "response", "fetch", "ok", "result", "json", "Array", "isArray", "Date", "length", "warn", "status", "error", "updateTrackingData", "rowIndex", "field", "value", "method", "headers", "body", "JSON", "stringify", "success", "message", "concat", "getWorkTypes", "types", "Set", "map", "item", "重点工作类型", "filter", "type", "trim", "getDataByWorkType", "grouped", "for<PERSON>ach", "push", "getMonthly<PERSON><PERSON>s", "months", "fields", "month", "getTrackingStats", "workTypes", "totalRecords", "monthlyFields", "totalFields", "completedFields", "toString", "completionRate", "toFixed", "workTypeCount", "monthlyFieldCount", "setSyncCallbacks", "getData", "trackingService"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块二/services/trackingService.js"], "sourcesContent": ["class TrackingService {\r\n  constructor() {\r\n    this.data = [];\r\n    this.lastModified = null;\r\n    this.syncCallbacks = {\r\n      onSuccess: null,\r\n      onError: null\r\n    };\r\n  }\r\n\r\n  // 加载重点工作跟踪数据\r\n  async loadTrackingData() {\r\n    try {\r\n      console.log('正在从API加载重点工作跟踪数据...');\r\n      const response = await fetch('http://localhost:3001/api/tracking-data');\r\n      \r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.data && Array.isArray(result.data)) {\r\n          this.data = result.data;\r\n          this.lastModified = new Date(result.lastModified);\r\n          console.log('重点工作跟踪数据加载成功:', this.data.length, '条记录');\r\n          return this.data;\r\n        } else {\r\n          console.warn('API返回的跟踪数据格式不正确');\r\n          return [];\r\n        }\r\n      } else {\r\n        console.warn('跟踪数据API响应失败，状态码:', response.status);\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('从API加载重点工作跟踪数据失败:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 更新跟踪数据\r\n  async updateTrackingData(rowIndex, field, value) {\r\n    try {\r\n      console.log('更新跟踪数据:', { rowIndex, field, value });\r\n      \r\n      // 更新本地数据\r\n      if (this.data[rowIndex]) {\r\n        this.data[rowIndex][field] = value;\r\n      }\r\n\r\n      // 同步到后端\r\n      const response = await fetch('http://localhost:3001/api/update-tracking-excel', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          rowIndex,\r\n          field,\r\n          value\r\n        })\r\n      });\r\n\r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          console.log('跟踪数据更新成功');\r\n          if (this.syncCallbacks.onSuccess) {\r\n            this.syncCallbacks.onSuccess();\r\n          }\r\n          return true;\r\n        } else {\r\n          console.error('跟踪数据更新失败:', result.message);\r\n          if (this.syncCallbacks.onError) {\r\n            this.syncCallbacks.onError(result.message);\r\n          }\r\n          return false;\r\n        }\r\n      } else {\r\n        console.error('跟踪数据更新请求失败，状态码:', response.status);\r\n        if (this.syncCallbacks.onError) {\r\n          this.syncCallbacks.onError(`HTTP ${response.status}`);\r\n        }\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error('跟踪数据更新错误:', error);\r\n      if (this.syncCallbacks.onError) {\r\n        this.syncCallbacks.onError(error.message);\r\n      }\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // 获取所有重点工作类型\r\n  getWorkTypes() {\r\n    const types = [...new Set(this.data.map(item => item.重点工作类型).filter(type => type && type.trim() !== ''))];\r\n    return types;\r\n  }\r\n\r\n  // 按工作类型分组数据\r\n  getDataByWorkType() {\r\n    const grouped = {};\r\n    this.data.forEach(item => {\r\n      const type = item.重点工作类型 || '未分类';\r\n      if (!grouped[type]) {\r\n        grouped[type] = [];\r\n      }\r\n      grouped[type].push(item);\r\n    });\r\n    return grouped;\r\n  }\r\n\r\n  // 获取月份列表\r\n  getMonthlyFields() {\r\n    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\r\n    const fields = [];\r\n    months.forEach(month => {\r\n      fields.push(`${month}工作计划`);\r\n      fields.push(`${month}完成情况`);\r\n    });\r\n    return fields;\r\n  }\r\n\r\n  // 获取统计数据\r\n  getTrackingStats() {\r\n    const workTypes = this.getWorkTypes();\r\n    const totalRecords = this.data.length;\r\n    const monthlyFields = this.getMonthlyFields();\r\n    \r\n    // 计算完成度统计\r\n    let totalFields = 0;\r\n    let completedFields = 0;\r\n    \r\n    this.data.forEach(item => {\r\n      monthlyFields.forEach(field => {\r\n        totalFields++;\r\n        if (item[field] && item[field].toString().trim() !== '') {\r\n          completedFields++;\r\n        }\r\n      });\r\n    });\r\n    \r\n    const completionRate = totalFields > 0 ? ((completedFields / totalFields) * 100).toFixed(1) : 0;\r\n\r\n    return {\r\n      totalRecords,\r\n      workTypeCount: workTypes.length,\r\n      completionRate,\r\n      monthlyFieldCount: monthlyFields.length\r\n    };\r\n  }\r\n\r\n  // 设置同步回调\r\n  setSyncCallbacks(onSuccess, onError) {\r\n    this.syncCallbacks.onSuccess = onSuccess;\r\n    this.syncCallbacks.onError = onError;\r\n  }\r\n\r\n  // 获取原始数据\r\n  getData() {\r\n    return this.data;\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nconst trackingService = new TrackingService();\r\n\r\nexport default trackingService; "], "mappings": "AAAA,KAAM,CAAAA,eAAgB,CACpBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,IAAI,CAAG,EAAE,CACd,IAAI,CAACC,YAAY,CAAG,IAAI,CACxB,IAAI,CAACC,aAAa,CAAG,CACnBC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IACX,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,gBAAgBA,CAAA,CAAG,CACvB,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAClC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,yCAAyC,CAAC,CAEvE,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACX,IAAI,EAAIa,KAAK,CAACC,OAAO,CAACH,MAAM,CAACX,IAAI,CAAC,CAAE,CAC7C,IAAI,CAACA,IAAI,CAAGW,MAAM,CAACX,IAAI,CACvB,IAAI,CAACC,YAAY,CAAG,GAAI,CAAAc,IAAI,CAACJ,MAAM,CAACV,YAAY,CAAC,CACjDK,OAAO,CAACC,GAAG,CAAC,eAAe,CAAE,IAAI,CAACP,IAAI,CAACgB,MAAM,CAAE,KAAK,CAAC,CACrD,MAAO,KAAI,CAAChB,IAAI,CAClB,CAAC,IAAM,CACLM,OAAO,CAACW,IAAI,CAAC,iBAAiB,CAAC,CAC/B,MAAO,EAAE,CACX,CACF,CAAC,IAAM,CACLX,OAAO,CAACW,IAAI,CAAC,kBAAkB,CAAET,QAAQ,CAACU,MAAM,CAAC,CACjD,MAAO,EAAE,CACX,CACF,CAAE,MAAOC,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzC,MAAO,EAAE,CACX,CACF,CAEA;AACA,KAAM,CAAAC,kBAAkBA,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,CAC/C,GAAI,CACFjB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CAAEc,QAAQ,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAC,CAElD;AACA,GAAI,IAAI,CAACvB,IAAI,CAACqB,QAAQ,CAAC,CAAE,CACvB,IAAI,CAACrB,IAAI,CAACqB,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CACpC,CAEA;AACA,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,iDAAiD,CAAE,CAC9Ee,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBP,QAAQ,CACRC,KAAK,CACLC,KACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAIf,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACkB,OAAO,CAAE,CAClBvB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvB,GAAI,IAAI,CAACL,aAAa,CAACC,SAAS,CAAE,CAChC,IAAI,CAACD,aAAa,CAACC,SAAS,CAAC,CAAC,CAChC,CACA,MAAO,KAAI,CACb,CAAC,IAAM,CACLG,OAAO,CAACa,KAAK,CAAC,WAAW,CAAER,MAAM,CAACmB,OAAO,CAAC,CAC1C,GAAI,IAAI,CAAC5B,aAAa,CAACE,OAAO,CAAE,CAC9B,IAAI,CAACF,aAAa,CAACE,OAAO,CAACO,MAAM,CAACmB,OAAO,CAAC,CAC5C,CACA,MAAO,MAAK,CACd,CACF,CAAC,IAAM,CACLxB,OAAO,CAACa,KAAK,CAAC,iBAAiB,CAAEX,QAAQ,CAACU,MAAM,CAAC,CACjD,GAAI,IAAI,CAAChB,aAAa,CAACE,OAAO,CAAE,CAC9B,IAAI,CAACF,aAAa,CAACE,OAAO,SAAA2B,MAAA,CAASvB,QAAQ,CAACU,MAAM,CAAE,CAAC,CACvD,CACA,MAAO,MAAK,CACd,CACF,CAAE,MAAOC,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,GAAI,IAAI,CAACjB,aAAa,CAACE,OAAO,CAAE,CAC9B,IAAI,CAACF,aAAa,CAACE,OAAO,CAACe,KAAK,CAACW,OAAO,CAAC,CAC3C,CACA,MAAO,MAAK,CACd,CACF,CAEA;AACAE,YAAYA,CAAA,CAAG,CACb,KAAM,CAAAC,KAAK,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC,IAAI,CAAClC,IAAI,CAACmC,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,EAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAAC,CAAC,CACzG,MAAO,CAAAP,KAAK,CACd,CAEA;AACAQ,iBAAiBA,CAAA,CAAG,CAClB,KAAM,CAAAC,OAAO,CAAG,CAAC,CAAC,CAClB,IAAI,CAAC1C,IAAI,CAAC2C,OAAO,CAACP,IAAI,EAAI,CACxB,KAAM,CAAAG,IAAI,CAAGH,IAAI,CAACC,MAAM,EAAI,KAAK,CACjC,GAAI,CAACK,OAAO,CAACH,IAAI,CAAC,CAAE,CAClBG,OAAO,CAACH,IAAI,CAAC,CAAG,EAAE,CACpB,CACAG,OAAO,CAACH,IAAI,CAAC,CAACK,IAAI,CAACR,IAAI,CAAC,CAC1B,CAAC,CAAC,CACF,MAAO,CAAAM,OAAO,CAChB,CAEA;AACAG,gBAAgBA,CAAA,CAAG,CACjB,KAAM,CAAAC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACpF,KAAM,CAAAC,MAAM,CAAG,EAAE,CACjBD,MAAM,CAACH,OAAO,CAACK,KAAK,EAAI,CACtBD,MAAM,CAACH,IAAI,IAAAb,MAAA,CAAIiB,KAAK,4BAAM,CAAC,CAC3BD,MAAM,CAACH,IAAI,IAAAb,MAAA,CAAIiB,KAAK,4BAAM,CAAC,CAC7B,CAAC,CAAC,CACF,MAAO,CAAAD,MAAM,CACf,CAEA;AACAE,gBAAgBA,CAAA,CAAG,CACjB,KAAM,CAAAC,SAAS,CAAG,IAAI,CAAClB,YAAY,CAAC,CAAC,CACrC,KAAM,CAAAmB,YAAY,CAAG,IAAI,CAACnD,IAAI,CAACgB,MAAM,CACrC,KAAM,CAAAoC,aAAa,CAAG,IAAI,CAACP,gBAAgB,CAAC,CAAC,CAE7C;AACA,GAAI,CAAAQ,WAAW,CAAG,CAAC,CACnB,GAAI,CAAAC,eAAe,CAAG,CAAC,CAEvB,IAAI,CAACtD,IAAI,CAAC2C,OAAO,CAACP,IAAI,EAAI,CACxBgB,aAAa,CAACT,OAAO,CAACrB,KAAK,EAAI,CAC7B+B,WAAW,EAAE,CACb,GAAIjB,IAAI,CAACd,KAAK,CAAC,EAAIc,IAAI,CAACd,KAAK,CAAC,CAACiC,QAAQ,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACvDc,eAAe,EAAE,CACnB,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,KAAM,CAAAE,cAAc,CAAGH,WAAW,CAAG,CAAC,CAAG,CAAEC,eAAe,CAAGD,WAAW,CAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAG,CAAC,CAE/F,MAAO,CACLN,YAAY,CACZO,aAAa,CAAER,SAAS,CAAClC,MAAM,CAC/BwC,cAAc,CACdG,iBAAiB,CAAEP,aAAa,CAACpC,MACnC,CAAC,CACH,CAEA;AACA4C,gBAAgBA,CAACzD,SAAS,CAAEC,OAAO,CAAE,CACnC,IAAI,CAACF,aAAa,CAACC,SAAS,CAAGA,SAAS,CACxC,IAAI,CAACD,aAAa,CAACE,OAAO,CAAGA,OAAO,CACtC,CAEA;AACAyD,OAAOA,CAAA,CAAG,CACR,MAAO,KAAI,CAAC7D,IAAI,CAClB,CACF,CAEA;AACA,KAAM,CAAA8D,eAAe,CAAG,GAAI,CAAAhE,eAAe,CAAC,CAAC,CAE7C,cAAe,CAAAgE,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}