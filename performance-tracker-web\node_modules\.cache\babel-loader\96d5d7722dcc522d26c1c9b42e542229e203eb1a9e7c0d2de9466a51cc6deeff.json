{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./SelectiveDownloadModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SelectiveDownloadModal=_ref=>{let{isOpen,onClose,data,onDownload}=_ref;const[selectionState,setSelectionState]=useState({selectedCategories:new Set(),selectedItems:new Set(),downloadFormat:'excel'});const[statistics,setStatistics]=useState({totalItems:0,totalWeight:0});// 数据分类配置\nconst categories=[{key:'keyIndicators',title:'关键指标（40分）',icon:'🎯'},{key:'qualityIndicators',title:'质量指标（20分）',icon:'⭐'},{key:'keyWork',title:'重点工作（40分）',icon:'🚀'}];useEffect(()=>{updateStatistics();},[selectionState.selectedItems,data]);// 计算统计信息\nconst updateStatistics=()=>{let totalItems=0;let totalWeight=0;categories.forEach(category=>{const categoryData=data[category.key]||[];categoryData.forEach((item,index)=>{const itemKey=\"\".concat(category.key,\"-\").concat(index);if(selectionState.selectedItems.has(itemKey)){totalItems++;const weight=item.权重;if(weight&&typeof weight==='number'){totalWeight+=weight;}else if(weight&&!isNaN(Number(weight))){totalWeight+=Number(weight);}}});});setStatistics({totalItems,totalWeight});};// 处理分类选择\nconst handleCategorySelect=categoryKey=>{const newSelectedCategories=new Set(selectionState.selectedCategories);const newSelectedItems=new Set(selectionState.selectedItems);if(newSelectedCategories.has(categoryKey)){// 取消选择分类，移除该分类下的所有项目\nnewSelectedCategories.delete(categoryKey);const categoryData=data[categoryKey]||[];categoryData.forEach((_,index)=>{newSelectedItems.delete(\"\".concat(categoryKey,\"-\").concat(index));});}else{// 选择分类，添加该分类下的所有项目\nnewSelectedCategories.add(categoryKey);const categoryData=data[categoryKey]||[];categoryData.forEach((_,index)=>{newSelectedItems.add(\"\".concat(categoryKey,\"-\").concat(index));});}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedCategories:newSelectedCategories,selectedItems:newSelectedItems}));};// 处理单项选择\nconst handleItemSelect=(categoryKey,itemIndex)=>{const itemKey=\"\".concat(categoryKey,\"-\").concat(itemIndex);const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedCategories=new Set(selectionState.selectedCategories);if(newSelectedItems.has(itemKey)){newSelectedItems.delete(itemKey);// 检查是否需要取消分类选择\nconst categoryData=data[categoryKey]||[];const categoryItemsSelected=categoryData.some((_,index)=>newSelectedItems.has(\"\".concat(categoryKey,\"-\").concat(index)));if(!categoryItemsSelected){newSelectedCategories.delete(categoryKey);}}else{newSelectedItems.add(itemKey);// 检查是否需要添加分类选择\nconst categoryData=data[categoryKey]||[];const allCategoryItemsSelected=categoryData.every((_,index)=>newSelectedItems.has(\"\".concat(categoryKey,\"-\").concat(index))||index===itemIndex);if(allCategoryItemsSelected){newSelectedCategories.add(categoryKey);}}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedCategories:newSelectedCategories,selectedItems:newSelectedItems}));};// 分类全选\nconst handleCategorySelectAll=categoryKey=>{const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedCategories=new Set(selectionState.selectedCategories);const categoryData=data[categoryKey]||[];categoryData.forEach((_,index)=>{newSelectedItems.add(\"\".concat(categoryKey,\"-\").concat(index));});newSelectedCategories.add(categoryKey);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedCategories:newSelectedCategories,selectedItems:newSelectedItems}));};// 分类反选\nconst handleCategoryUnselectAll=categoryKey=>{const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedCategories=new Set(selectionState.selectedCategories);const categoryData=data[categoryKey]||[];categoryData.forEach((_,index)=>{newSelectedItems.delete(\"\".concat(categoryKey,\"-\").concat(index));});newSelectedCategories.delete(categoryKey);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedCategories:newSelectedCategories,selectedItems:newSelectedItems}));};// 处理下载\nconst handleDownload=()=>{const selectedData={keyIndicators:[],qualityIndicators:[],keyWork:[]};// 收集选中的数据\ncategories.forEach(category=>{const categoryData=data[category.key]||[];categoryData.forEach((item,index)=>{const itemKey=\"\".concat(category.key,\"-\").concat(index);if(selectionState.selectedItems.has(itemKey)){selectedData[category.key].push({index,data:item});}});});onDownload({selectedData,format:selectionState.downloadFormat,statistics});};// 格式化显示值\nconst formatDisplayValue=value=>{if(value===null||value===undefined||value==='')return'';if(typeof value==='object'){if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"selective-download-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"selective-download-modal\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCCA \\u9009\\u62E9\\u8981\\u4E0B\\u8F7D\\u7684\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"modal-content\",children:categories.map(category=>{const categoryData=data[category.key]||[];const isCategorySelected=selectionState.selectedCategories.has(category.key);return/*#__PURE__*/_jsxs(\"div\",{className:\"category-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"category-header\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"category-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isCategorySelected,onChange:()=>handleCategorySelect(category.key)}),/*#__PURE__*/_jsxs(\"span\",{className:\"category-title\",children:[category.icon,\" \",category.title]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"category-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleCategorySelectAll(category.key),children:\"\\u5168\\u9009\"}),/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleCategoryUnselectAll(category.key),children:\"\\u53CD\\u9009\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"items-list\",children:categoryData.map((item,index)=>{const itemKey=\"\".concat(category.key,\"-\").concat(index);const isItemSelected=selectionState.selectedItems.has(itemKey);const indicator=formatDisplayValue(item.指标);const weight=item.权重;const isHighlight=category.key==='keyWork'&&(item.序号===1||item.序号===19);return/*#__PURE__*/_jsx(\"div\",{className:\"item-row \".concat(isHighlight?'highlight-item':''),children:/*#__PURE__*/_jsxs(\"label\",{className:\"item-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isItemSelected,onChange:()=>handleItemSelect(category.key,index)}),/*#__PURE__*/_jsxs(\"span\",{className:\"item-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"item-name\",children:indicator}),weight&&/*#__PURE__*/_jsxs(\"span\",{className:\"item-weight\",children:[\"\\u6743\\u91CD: \",weight,\"\\u5206\"]}),isHighlight&&/*#__PURE__*/_jsx(\"span\",{className:\"highlight-badge\",children:\"\\u65B0\\u6750\\u7EA7\\u91CD\\u70B9\\u5DE5\\u4F5C\"})]})]})},index);})})]},category.key);})}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"statistics\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5DF2\\u9009\\u62E9: \",statistics.totalItems,\"\\u9879\"]}),statistics.totalWeight>0&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u603B\\u6743\\u91CD: \",statistics.totalWeight,\"\\u5206\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"format-selection\",children:/*#__PURE__*/_jsxs(\"label\",{children:[\"\\u4E0B\\u8F7D\\u683C\\u5F0F:\",/*#__PURE__*/_jsxs(\"select\",{value:selectionState.downloadFormat,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{downloadFormat:e.target.value})),children:[/*#__PURE__*/_jsx(\"option\",{value:\"excel\",children:\"Excel\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pdf\",children:\"PDF\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"csv\",children:\"CSV\\u683C\\u5F0F\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-btn\",onClick:onClose,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"download-btn\",onClick:handleDownload,disabled:statistics.totalItems===0,children:[selectionState.downloadFormat.toUpperCase(),\"\\u4E0B\\u8F7D\"]})]})]})]})});};export default SelectiveDownloadModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "SelectiveDownloadModal", "_ref", "isOpen", "onClose", "data", "onDownload", "selectionState", "setSelectionState", "selectedCategories", "Set", "selectedItems", "downloadFormat", "statistics", "setStatistics", "totalItems", "totalWeight", "categories", "key", "title", "icon", "updateStatistics", "for<PERSON>ach", "category", "categoryData", "item", "index", "itemKey", "concat", "has", "weight", "权重", "isNaN", "Number", "handleCategorySelect", "categoryKey", "newSelectedCategories", "newSelectedItems", "delete", "_", "add", "_objectSpread", "handleItemSelect", "itemIndex", "categoryItemsSelected", "some", "allCategoryItemsSelected", "every", "handleCategorySelectAll", "handleCategoryUnselectAll", "handleDownload", "selectedData", "keyIndicators", "qualityIndicators", "keyWork", "push", "format", "formatDisplayValue", "value", "undefined", "text", "richText", "String", "className", "children", "onClick", "map", "isCategorySelected", "type", "checked", "onChange", "isItemSelected", "indicator", "指标", "isHighlight", "序号", "e", "target", "disabled", "toUpperCase"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块一/components/SelectiveDownloadModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './SelectiveDownloadModal.css';\r\n\r\nconst SelectiveDownloadModal = ({ isOpen, onClose, data, onDownload }) => {\r\n  const [selectionState, setSelectionState] = useState({\r\n    selectedCategories: new Set(),\r\n    selectedItems: new Set(),\r\n    downloadFormat: 'excel'\r\n  });\r\n\r\n  const [statistics, setStatistics] = useState({\r\n    totalItems: 0,\r\n    totalWeight: 0\r\n  });\r\n\r\n  // 数据分类配置\r\n  const categories = [\r\n    {\r\n      key: 'keyIndicators',\r\n      title: '关键指标（40分）',\r\n      icon: '🎯'\r\n    },\r\n    {\r\n      key: 'qualityIndicators', \r\n      title: '质量指标（20分）',\r\n      icon: '⭐'\r\n    },\r\n    {\r\n      key: 'keyWork',\r\n      title: '重点工作（40分）',\r\n      icon: '🚀'\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    updateStatistics();\r\n  }, [selectionState.selectedItems, data]);\r\n\r\n  // 计算统计信息\r\n  const updateStatistics = () => {\r\n    let totalItems = 0;\r\n    let totalWeight = 0;\r\n\r\n    categories.forEach(category => {\r\n      const categoryData = data[category.key] || [];\r\n      categoryData.forEach((item, index) => {\r\n        const itemKey = `${category.key}-${index}`;\r\n        if (selectionState.selectedItems.has(itemKey)) {\r\n          totalItems++;\r\n          const weight = item.权重;\r\n          if (weight && typeof weight === 'number') {\r\n            totalWeight += weight;\r\n          } else if (weight && !isNaN(Number(weight))) {\r\n            totalWeight += Number(weight);\r\n          }\r\n        }\r\n      });\r\n    });\r\n\r\n    setStatistics({ totalItems, totalWeight });\r\n  };\r\n\r\n  // 处理分类选择\r\n  const handleCategorySelect = (categoryKey) => {\r\n    const newSelectedCategories = new Set(selectionState.selectedCategories);\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    \r\n    if (newSelectedCategories.has(categoryKey)) {\r\n      // 取消选择分类，移除该分类下的所有项目\r\n      newSelectedCategories.delete(categoryKey);\r\n      const categoryData = data[categoryKey] || [];\r\n      categoryData.forEach((_, index) => {\r\n        newSelectedItems.delete(`${categoryKey}-${index}`);\r\n      });\r\n    } else {\r\n      // 选择分类，添加该分类下的所有项目\r\n      newSelectedCategories.add(categoryKey);\r\n      const categoryData = data[categoryKey] || [];\r\n      categoryData.forEach((_, index) => {\r\n        newSelectedItems.add(`${categoryKey}-${index}`);\r\n      });\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedCategories: newSelectedCategories,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 处理单项选择\r\n  const handleItemSelect = (categoryKey, itemIndex) => {\r\n    const itemKey = `${categoryKey}-${itemIndex}`;\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedCategories = new Set(selectionState.selectedCategories);\r\n\r\n    if (newSelectedItems.has(itemKey)) {\r\n      newSelectedItems.delete(itemKey);\r\n      // 检查是否需要取消分类选择\r\n      const categoryData = data[categoryKey] || [];\r\n      const categoryItemsSelected = categoryData.some((_, index) => \r\n        newSelectedItems.has(`${categoryKey}-${index}`)\r\n      );\r\n      if (!categoryItemsSelected) {\r\n        newSelectedCategories.delete(categoryKey);\r\n      }\r\n    } else {\r\n      newSelectedItems.add(itemKey);\r\n      // 检查是否需要添加分类选择\r\n      const categoryData = data[categoryKey] || [];\r\n      const allCategoryItemsSelected = categoryData.every((_, index) => \r\n        newSelectedItems.has(`${categoryKey}-${index}`) || index === itemIndex\r\n      );\r\n      if (allCategoryItemsSelected) {\r\n        newSelectedCategories.add(categoryKey);\r\n      }\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedCategories: newSelectedCategories,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 分类全选\r\n  const handleCategorySelectAll = (categoryKey) => {\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedCategories = new Set(selectionState.selectedCategories);\r\n    \r\n    const categoryData = data[categoryKey] || [];\r\n    categoryData.forEach((_, index) => {\r\n      newSelectedItems.add(`${categoryKey}-${index}`);\r\n    });\r\n    newSelectedCategories.add(categoryKey);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedCategories: newSelectedCategories,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 分类反选\r\n  const handleCategoryUnselectAll = (categoryKey) => {\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedCategories = new Set(selectionState.selectedCategories);\r\n    \r\n    const categoryData = data[categoryKey] || [];\r\n    categoryData.forEach((_, index) => {\r\n      newSelectedItems.delete(`${categoryKey}-${index}`);\r\n    });\r\n    newSelectedCategories.delete(categoryKey);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedCategories: newSelectedCategories,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 处理下载\r\n  const handleDownload = () => {\r\n    const selectedData = {\r\n      keyIndicators: [],\r\n      qualityIndicators: [],\r\n      keyWork: []\r\n    };\r\n\r\n    // 收集选中的数据\r\n    categories.forEach(category => {\r\n      const categoryData = data[category.key] || [];\r\n      categoryData.forEach((item, index) => {\r\n        const itemKey = `${category.key}-${index}`;\r\n        if (selectionState.selectedItems.has(itemKey)) {\r\n          selectedData[category.key].push({\r\n            index,\r\n            data: item\r\n          });\r\n        }\r\n      });\r\n    });\r\n\r\n    onDownload({\r\n      selectedData,\r\n      format: selectionState.downloadFormat,\r\n      statistics\r\n    });\r\n  };\r\n\r\n  // 格式化显示值\r\n  const formatDisplayValue = (value) => {\r\n    if (value === null || value === undefined || value === '') return '';\r\n    if (typeof value === 'object') {\r\n      if (value.text !== undefined) return value.text;\r\n      if (value.richText !== undefined) return value.richText;\r\n      if (value.value !== undefined) return value.value;\r\n      return String(value);\r\n    }\r\n    return String(value);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"selective-download-overlay\">\r\n      <div className=\"selective-download-modal\">\r\n        <div className=\"modal-header\">\r\n          <h2>📊 选择要下载的数据</h2>\r\n          <button className=\"close-btn\" onClick={onClose}>×</button>\r\n        </div>\r\n\r\n        <div className=\"modal-content\">\r\n          {categories.map(category => {\r\n            const categoryData = data[category.key] || [];\r\n            const isCategorySelected = selectionState.selectedCategories.has(category.key);\r\n            \r\n            return (\r\n              <div key={category.key} className=\"category-section\">\r\n                <div className=\"category-header\">\r\n                  <label className=\"category-checkbox\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      checked={isCategorySelected}\r\n                      onChange={() => handleCategorySelect(category.key)}\r\n                    />\r\n                    <span className=\"category-title\">\r\n                      {category.icon} {category.title}\r\n                    </span>\r\n                  </label>\r\n                  <div className=\"category-actions\">\r\n                    <button \r\n                      className=\"action-btn\"\r\n                      onClick={() => handleCategorySelectAll(category.key)}\r\n                    >\r\n                      全选\r\n                    </button>\r\n                    <button \r\n                      className=\"action-btn\"\r\n                      onClick={() => handleCategoryUnselectAll(category.key)}\r\n                    >\r\n                      反选\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"items-list\">\r\n                  {categoryData.map((item, index) => {\r\n                    const itemKey = `${category.key}-${index}`;\r\n                    const isItemSelected = selectionState.selectedItems.has(itemKey);\r\n                    const indicator = formatDisplayValue(item.指标);\r\n                    const weight = item.权重;\r\n                    const isHighlight = category.key === 'keyWork' && (item.序号 === 1 || item.序号 === 19);\r\n\r\n                    return (\r\n                      <div key={index} className={`item-row ${isHighlight ? 'highlight-item' : ''}`}>\r\n                        <label className=\"item-checkbox\">\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            checked={isItemSelected}\r\n                            onChange={() => handleItemSelect(category.key, index)}\r\n                          />\r\n                          <span className=\"item-content\">\r\n                            <span className=\"item-name\">{indicator}</span>\r\n                            {weight && (\r\n                              <span className=\"item-weight\">权重: {weight}分</span>\r\n                            )}\r\n                            {isHighlight && (\r\n                              <span className=\"highlight-badge\">新材级重点工作</span>\r\n                            )}\r\n                          </span>\r\n                        </label>\r\n                      </div>\r\n                    );\r\n                  })}\r\n                </div>\r\n              </div>\r\n            );\r\n          })}\r\n        </div>\r\n\r\n        <div className=\"modal-footer\">\r\n          <div className=\"statistics\">\r\n            <span>已选择: {statistics.totalItems}项</span>\r\n            {statistics.totalWeight > 0 && (\r\n              <span>总权重: {statistics.totalWeight}分</span>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"format-selection\">\r\n            <label>\r\n              下载格式:\r\n              <select \r\n                value={selectionState.downloadFormat}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  downloadFormat: e.target.value\r\n                })}\r\n              >\r\n                <option value=\"excel\">Excel格式</option>\r\n                <option value=\"pdf\">PDF格式</option>\r\n                <option value=\"csv\">CSV格式</option>\r\n              </select>\r\n            </label>\r\n          </div>\r\n\r\n          <div className=\"action-buttons\">\r\n            <button className=\"cancel-btn\" onClick={onClose}>\r\n              取消\r\n            </button>\r\n            <button \r\n              className=\"download-btn\"\r\n              onClick={handleDownload}\r\n              disabled={statistics.totalItems === 0}\r\n            >\r\n              {selectionState.downloadFormat.toUpperCase()}下载\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SelectiveDownloadModal; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtC,KAAM,CAAAC,sBAAsB,CAAGC,IAAA,EAA2C,IAA1C,CAAEC,MAAM,CAAEC,OAAO,CAAEC,IAAI,CAAEC,UAAW,CAAC,CAAAJ,IAAA,CACnE,KAAM,CAACK,cAAc,CAAEC,iBAAiB,CAAC,CAAGb,QAAQ,CAAC,CACnDc,kBAAkB,CAAE,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC7BC,aAAa,CAAE,GAAI,CAAAD,GAAG,CAAC,CAAC,CACxBE,cAAc,CAAE,OAClB,CAAC,CAAC,CAEF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAGnB,QAAQ,CAAC,CAC3CoB,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CACf,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,CACEC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,IACR,CAAC,CACD,CACEF,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,GACR,CAAC,CACD,CACEF,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,IACR,CAAC,CACF,CAEDxB,SAAS,CAAC,IAAM,CACdyB,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAACd,cAAc,CAACI,aAAa,CAAEN,IAAI,CAAC,CAAC,CAExC;AACA,KAAM,CAAAgB,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAAAN,UAAU,CAAG,CAAC,CAClB,GAAI,CAAAC,WAAW,CAAG,CAAC,CAEnBC,UAAU,CAACK,OAAO,CAACC,QAAQ,EAAI,CAC7B,KAAM,CAAAC,YAAY,CAAGnB,IAAI,CAACkB,QAAQ,CAACL,GAAG,CAAC,EAAI,EAAE,CAC7CM,YAAY,CAACF,OAAO,CAAC,CAACG,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,OAAO,IAAAC,MAAA,CAAML,QAAQ,CAACL,GAAG,MAAAU,MAAA,CAAIF,KAAK,CAAE,CAC1C,GAAInB,cAAc,CAACI,aAAa,CAACkB,GAAG,CAACF,OAAO,CAAC,CAAE,CAC7CZ,UAAU,EAAE,CACZ,KAAM,CAAAe,MAAM,CAAGL,IAAI,CAACM,EAAE,CACtB,GAAID,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACxCd,WAAW,EAAIc,MAAM,CACvB,CAAC,IAAM,IAAIA,MAAM,EAAI,CAACE,KAAK,CAACC,MAAM,CAACH,MAAM,CAAC,CAAC,CAAE,CAC3Cd,WAAW,EAAIiB,MAAM,CAACH,MAAM,CAAC,CAC/B,CACF,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFhB,aAAa,CAAC,CAAEC,UAAU,CAAEC,WAAY,CAAC,CAAC,CAC5C,CAAC,CAED;AACA,KAAM,CAAAkB,oBAAoB,CAAIC,WAAW,EAAK,CAC5C,KAAM,CAAAC,qBAAqB,CAAG,GAAI,CAAA1B,GAAG,CAACH,cAAc,CAACE,kBAAkB,CAAC,CACxE,KAAM,CAAA4B,gBAAgB,CAAG,GAAI,CAAA3B,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAE9D,GAAIyB,qBAAqB,CAACP,GAAG,CAACM,WAAW,CAAC,CAAE,CAC1C;AACAC,qBAAqB,CAACE,MAAM,CAACH,WAAW,CAAC,CACzC,KAAM,CAAAX,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5CX,YAAY,CAACF,OAAO,CAAC,CAACiB,CAAC,CAAEb,KAAK,GAAK,CACjCW,gBAAgB,CAACC,MAAM,IAAAV,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAAC,CACpD,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAU,qBAAqB,CAACI,GAAG,CAACL,WAAW,CAAC,CACtC,KAAM,CAAAX,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5CX,YAAY,CAACF,OAAO,CAAC,CAACiB,CAAC,CAAEb,KAAK,GAAK,CACjCW,gBAAgB,CAACG,GAAG,IAAAZ,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAAC,CACjD,CAAC,CAAC,CACJ,CAEAlB,iBAAiB,CAAAiC,aAAA,CAAAA,aAAA,IACZlC,cAAc,MACjBE,kBAAkB,CAAE2B,qBAAqB,CACzCzB,aAAa,CAAE0B,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAK,gBAAgB,CAAGA,CAACP,WAAW,CAAEQ,SAAS,GAAK,CACnD,KAAM,CAAAhB,OAAO,IAAAC,MAAA,CAAMO,WAAW,MAAAP,MAAA,CAAIe,SAAS,CAAE,CAC7C,KAAM,CAAAN,gBAAgB,CAAG,GAAI,CAAA3B,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAyB,qBAAqB,CAAG,GAAI,CAAA1B,GAAG,CAACH,cAAc,CAACE,kBAAkB,CAAC,CAExE,GAAI4B,gBAAgB,CAACR,GAAG,CAACF,OAAO,CAAC,CAAE,CACjCU,gBAAgB,CAACC,MAAM,CAACX,OAAO,CAAC,CAChC;AACA,KAAM,CAAAH,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5C,KAAM,CAAAS,qBAAqB,CAAGpB,YAAY,CAACqB,IAAI,CAAC,CAACN,CAAC,CAAEb,KAAK,GACvDW,gBAAgB,CAACR,GAAG,IAAAD,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAChD,CAAC,CACD,GAAI,CAACkB,qBAAqB,CAAE,CAC1BR,qBAAqB,CAACE,MAAM,CAACH,WAAW,CAAC,CAC3C,CACF,CAAC,IAAM,CACLE,gBAAgB,CAACG,GAAG,CAACb,OAAO,CAAC,CAC7B;AACA,KAAM,CAAAH,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5C,KAAM,CAAAW,wBAAwB,CAAGtB,YAAY,CAACuB,KAAK,CAAC,CAACR,CAAC,CAAEb,KAAK,GAC3DW,gBAAgB,CAACR,GAAG,IAAAD,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAAC,EAAIA,KAAK,GAAKiB,SAC/D,CAAC,CACD,GAAIG,wBAAwB,CAAE,CAC5BV,qBAAqB,CAACI,GAAG,CAACL,WAAW,CAAC,CACxC,CACF,CAEA3B,iBAAiB,CAAAiC,aAAA,CAAAA,aAAA,IACZlC,cAAc,MACjBE,kBAAkB,CAAE2B,qBAAqB,CACzCzB,aAAa,CAAE0B,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAW,uBAAuB,CAAIb,WAAW,EAAK,CAC/C,KAAM,CAAAE,gBAAgB,CAAG,GAAI,CAAA3B,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAyB,qBAAqB,CAAG,GAAI,CAAA1B,GAAG,CAACH,cAAc,CAACE,kBAAkB,CAAC,CAExE,KAAM,CAAAe,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5CX,YAAY,CAACF,OAAO,CAAC,CAACiB,CAAC,CAAEb,KAAK,GAAK,CACjCW,gBAAgB,CAACG,GAAG,IAAAZ,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAAC,CACjD,CAAC,CAAC,CACFU,qBAAqB,CAACI,GAAG,CAACL,WAAW,CAAC,CAEtC3B,iBAAiB,CAAAiC,aAAA,CAAAA,aAAA,IACZlC,cAAc,MACjBE,kBAAkB,CAAE2B,qBAAqB,CACzCzB,aAAa,CAAE0B,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAY,yBAAyB,CAAId,WAAW,EAAK,CACjD,KAAM,CAAAE,gBAAgB,CAAG,GAAI,CAAA3B,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAyB,qBAAqB,CAAG,GAAI,CAAA1B,GAAG,CAACH,cAAc,CAACE,kBAAkB,CAAC,CAExE,KAAM,CAAAe,YAAY,CAAGnB,IAAI,CAAC8B,WAAW,CAAC,EAAI,EAAE,CAC5CX,YAAY,CAACF,OAAO,CAAC,CAACiB,CAAC,CAAEb,KAAK,GAAK,CACjCW,gBAAgB,CAACC,MAAM,IAAAV,MAAA,CAAIO,WAAW,MAAAP,MAAA,CAAIF,KAAK,CAAE,CAAC,CACpD,CAAC,CAAC,CACFU,qBAAqB,CAACE,MAAM,CAACH,WAAW,CAAC,CAEzC3B,iBAAiB,CAAAiC,aAAA,CAAAA,aAAA,IACZlC,cAAc,MACjBE,kBAAkB,CAAE2B,qBAAqB,CACzCzB,aAAa,CAAE0B,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAa,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,YAAY,CAAG,CACnBC,aAAa,CAAE,EAAE,CACjBC,iBAAiB,CAAE,EAAE,CACrBC,OAAO,CAAE,EACX,CAAC,CAED;AACArC,UAAU,CAACK,OAAO,CAACC,QAAQ,EAAI,CAC7B,KAAM,CAAAC,YAAY,CAAGnB,IAAI,CAACkB,QAAQ,CAACL,GAAG,CAAC,EAAI,EAAE,CAC7CM,YAAY,CAACF,OAAO,CAAC,CAACG,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,OAAO,IAAAC,MAAA,CAAML,QAAQ,CAACL,GAAG,MAAAU,MAAA,CAAIF,KAAK,CAAE,CAC1C,GAAInB,cAAc,CAACI,aAAa,CAACkB,GAAG,CAACF,OAAO,CAAC,CAAE,CAC7CwB,YAAY,CAAC5B,QAAQ,CAACL,GAAG,CAAC,CAACqC,IAAI,CAAC,CAC9B7B,KAAK,CACLrB,IAAI,CAAEoB,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFnB,UAAU,CAAC,CACT6C,YAAY,CACZK,MAAM,CAAEjD,cAAc,CAACK,cAAc,CACrCC,UACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA4C,kBAAkB,CAAIC,KAAK,EAAK,CACpC,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,MAAO,EAAE,CACpE,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACE,IAAI,GAAKD,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACE,IAAI,CAC/C,GAAIF,KAAK,CAACG,QAAQ,GAAKF,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACG,QAAQ,CACvD,GAAIH,KAAK,CAACA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAI,MAAM,CAACJ,KAAK,CAAC,CACtB,CACA,MAAO,CAAAI,MAAM,CAACJ,KAAK,CAAC,CACtB,CAAC,CAED,GAAI,CAACvD,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEL,IAAA,QAAKiE,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzChE,KAAA,QAAK+D,SAAS,CAAC,0BAA0B,CAAAC,QAAA,eACvChE,KAAA,QAAK+D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BlE,IAAA,OAAAkE,QAAA,CAAI,+DAAW,CAAI,CAAC,cACpBlE,IAAA,WAAQiE,SAAS,CAAC,WAAW,CAACE,OAAO,CAAE7D,OAAQ,CAAA4D,QAAA,CAAC,MAAC,CAAQ,CAAC,EACvD,CAAC,cAENlE,IAAA,QAAKiE,SAAS,CAAC,eAAe,CAAAC,QAAA,CAC3B/C,UAAU,CAACiD,GAAG,CAAC3C,QAAQ,EAAI,CAC1B,KAAM,CAAAC,YAAY,CAAGnB,IAAI,CAACkB,QAAQ,CAACL,GAAG,CAAC,EAAI,EAAE,CAC7C,KAAM,CAAAiD,kBAAkB,CAAG5D,cAAc,CAACE,kBAAkB,CAACoB,GAAG,CAACN,QAAQ,CAACL,GAAG,CAAC,CAE9E,mBACElB,KAAA,QAAwB+D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAClDhE,KAAA,QAAK+D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhE,KAAA,UAAO+D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAClClE,IAAA,UACEsE,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEF,kBAAmB,CAC5BG,QAAQ,CAAEA,CAAA,GAAMpC,oBAAoB,CAACX,QAAQ,CAACL,GAAG,CAAE,CACpD,CAAC,cACFlB,KAAA,SAAM+D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAC7BzC,QAAQ,CAACH,IAAI,CAAC,GAAC,CAACG,QAAQ,CAACJ,KAAK,EAC3B,CAAC,EACF,CAAC,cACRnB,KAAA,QAAK+D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BlE,IAAA,WACEiE,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMjB,uBAAuB,CAACzB,QAAQ,CAACL,GAAG,CAAE,CAAA8C,QAAA,CACtD,cAED,CAAQ,CAAC,cACTlE,IAAA,WACEiE,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMhB,yBAAyB,CAAC1B,QAAQ,CAACL,GAAG,CAAE,CAAA8C,QAAA,CACxD,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAENlE,IAAA,QAAKiE,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBxC,YAAY,CAAC0C,GAAG,CAAC,CAACzC,IAAI,CAAEC,KAAK,GAAK,CACjC,KAAM,CAAAC,OAAO,IAAAC,MAAA,CAAML,QAAQ,CAACL,GAAG,MAAAU,MAAA,CAAIF,KAAK,CAAE,CAC1C,KAAM,CAAA6C,cAAc,CAAGhE,cAAc,CAACI,aAAa,CAACkB,GAAG,CAACF,OAAO,CAAC,CAChE,KAAM,CAAA6C,SAAS,CAAGf,kBAAkB,CAAChC,IAAI,CAACgD,EAAE,CAAC,CAC7C,KAAM,CAAA3C,MAAM,CAAGL,IAAI,CAACM,EAAE,CACtB,KAAM,CAAA2C,WAAW,CAAGnD,QAAQ,CAACL,GAAG,GAAK,SAAS,GAAKO,IAAI,CAACkD,EAAE,GAAK,CAAC,EAAIlD,IAAI,CAACkD,EAAE,GAAK,EAAE,CAAC,CAEnF,mBACE7E,IAAA,QAAiBiE,SAAS,aAAAnC,MAAA,CAAc8C,WAAW,CAAG,gBAAgB,CAAG,EAAE,CAAG,CAAAV,QAAA,cAC5EhE,KAAA,UAAO+D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC9BlE,IAAA,UACEsE,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEE,cAAe,CACxBD,QAAQ,CAAEA,CAAA,GAAM5B,gBAAgB,CAACnB,QAAQ,CAACL,GAAG,CAAEQ,KAAK,CAAE,CACvD,CAAC,cACF1B,KAAA,SAAM+D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC5BlE,IAAA,SAAMiE,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAEQ,SAAS,CAAO,CAAC,CAC7C1C,MAAM,eACL9B,KAAA,SAAM+D,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAC,gBAAI,CAAClC,MAAM,CAAC,QAAC,EAAM,CAClD,CACA4C,WAAW,eACV5E,IAAA,SAAMiE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,4CAAO,CAAM,CAChD,EACG,CAAC,EACF,CAAC,EAhBAtC,KAiBL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,GAzDEH,QAAQ,CAACL,GA0Dd,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cAENlB,KAAA,QAAK+D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhE,KAAA,QAAK+D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBhE,KAAA,SAAAgE,QAAA,EAAM,sBAAK,CAACnD,UAAU,CAACE,UAAU,CAAC,QAAC,EAAM,CAAC,CACzCF,UAAU,CAACG,WAAW,CAAG,CAAC,eACzBhB,KAAA,SAAAgE,QAAA,EAAM,sBAAK,CAACnD,UAAU,CAACG,WAAW,CAAC,QAAC,EAAM,CAC3C,EACE,CAAC,cAENlB,IAAA,QAAKiE,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BhE,KAAA,UAAAgE,QAAA,EAAO,2BAEL,cAAAhE,KAAA,WACE0D,KAAK,CAAEnD,cAAc,CAACK,cAAe,CACrC0D,QAAQ,CAAGM,CAAC,EAAKpE,iBAAiB,CAAAiC,aAAA,CAAAA,aAAA,IAC7BlC,cAAc,MACjBK,cAAc,CAAEgE,CAAC,CAACC,MAAM,CAACnB,KAAK,EAC/B,CAAE,CAAAM,QAAA,eAEHlE,IAAA,WAAQ4D,KAAK,CAAC,OAAO,CAAAM,QAAA,CAAC,mBAAO,CAAQ,CAAC,cACtClE,IAAA,WAAQ4D,KAAK,CAAC,KAAK,CAAAM,QAAA,CAAC,iBAAK,CAAQ,CAAC,cAClClE,IAAA,WAAQ4D,KAAK,CAAC,KAAK,CAAAM,QAAA,CAAC,iBAAK,CAAQ,CAAC,EAC5B,CAAC,EACJ,CAAC,CACL,CAAC,cAENhE,KAAA,QAAK+D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BlE,IAAA,WAAQiE,SAAS,CAAC,YAAY,CAACE,OAAO,CAAE7D,OAAQ,CAAA4D,QAAA,CAAC,cAEjD,CAAQ,CAAC,cACThE,KAAA,WACE+D,SAAS,CAAC,cAAc,CACxBE,OAAO,CAAEf,cAAe,CACxB4B,QAAQ,CAAEjE,UAAU,CAACE,UAAU,GAAK,CAAE,CAAAiD,QAAA,EAErCzD,cAAc,CAACK,cAAc,CAACmE,WAAW,CAAC,CAAC,CAAC,cAC/C,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9E,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}