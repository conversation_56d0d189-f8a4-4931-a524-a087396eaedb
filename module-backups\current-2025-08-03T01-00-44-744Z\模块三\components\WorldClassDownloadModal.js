import React, { useState, useEffect } from 'react';
import './WorldClassDownloadModal.css';

const WorldClassDownloadModal = ({ 
  isOpen, 
  onClose, 
  data, 
  filteredData,
  levelFilter,
  currentMonthPair,
  monthPairs,
  onDownload 
}) => {
  const [selectionState, setSelectionState] = useState({
    selectedLevels: new Set(),
    selectedItems: new Set(),
    downloadFormat: 'excel',
    monthRange: { start: 0, end: monthPairs.length - 1 }, // 月份对索引范围
    includeLevelFilter: true
  });

  const [statistics, setStatistics] = useState({
    totalItems: 0,
    levelCount: 0,
    monthsCovered: ''
  });

  // 获取当前使用的数据（筛选后或原始数据）
  const getCurrentData = () => {
    if (levelFilter.value !== '全部' && filteredData && filteredData.length > 0) {
      return filteredData;
    } else if (data && data.length > 0) {
      return data;
    } else {
      return [];
    }
  };

  // 按层级分组数据
  const getGroupedData = () => {
    const dataToUse = getCurrentData();
    if (!dataToUse || dataToUse.length === 0) {
      return {};
    }
    
    const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : 
                      levelFilter.level === '二级' ? '相关指标或方向（二级）' : 
                      '相关指标或方向（三级）';
    
    const grouped = {};
    dataToUse.forEach(item => {
      const level = item[levelField] || '其他';
      if (!grouped[level]) {
        grouped[level] = [];
      }
      grouped[level].push(item);
    });
    
    return grouped;
  };

  // 获取所有层级
  const getLevels = () => {
    const grouped = getGroupedData();
    return Object.keys(grouped);
  };

  useEffect(() => {
    updateStatistics();
  }, [selectionState.selectedItems, selectionState.monthRange, data, filteredData]);

  // 计算统计信息
  const updateStatistics = () => {
    let totalItems = 0;
    const levels = new Set();

    const dataToUse = getCurrentData();
    dataToUse.forEach((item, index) => {
      const itemKey = `item-${index}`;
      if (selectionState.selectedItems.has(itemKey)) {
        totalItems++;
        const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : 
                          levelFilter.level === '二级' ? '相关指标或方向（二级）' : 
                          '相关指标或方向（三级）';
        if (item[levelField]) {
          levels.add(item[levelField]);
        }
      }
    });

    // 计算覆盖的月份范围
    const startMonth = monthPairs[selectionState.monthRange.start]?.[0] || '';
    const endMonth = monthPairs[selectionState.monthRange.end]?.[1] || '';
    const monthsCovered = startMonth && endMonth ? `${startMonth} 至 ${endMonth}` : '';

    setStatistics({
      totalItems,
      levelCount: levels.size,
      monthsCovered
    });
  };

  // 处理层级选择
  const handleLevelSelect = (level) => {
    const newSelectedLevels = new Set(selectionState.selectedLevels);
    const newSelectedItems = new Set(selectionState.selectedItems);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    if (newSelectedLevels.has(level)) {
      // 取消选择层级，移除该层级下的所有项目
      newSelectedLevels.delete(level);
      grouped[level]?.forEach(item => {
        const itemIndex = dataToUse.findIndex(d => d === item);
        if (itemIndex >= 0) {
          newSelectedItems.delete(`item-${itemIndex}`);
        }
      });
    } else {
      // 选择层级，添加该层级下的所有项目
      newSelectedLevels.add(level);
      grouped[level]?.forEach(item => {
        const itemIndex = dataToUse.findIndex(d => d === item);
        if (itemIndex >= 0) {
          newSelectedItems.add(`item-${itemIndex}`);
        }
      });
    }

    setSelectionState({
      ...selectionState,
      selectedLevels: newSelectedLevels,
      selectedItems: newSelectedItems
    });
  };

  // 处理单项选择
  const handleItemSelect = (itemIndex) => {
    const itemKey = `item-${itemIndex}`;
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedLevels = new Set(selectionState.selectedLevels);

    const dataToUse = getCurrentData();
    const item = dataToUse[itemIndex];
    const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : 
                      levelFilter.level === '二级' ? '相关指标或方向（二级）' : 
                      '相关指标或方向（三级）';
    const level = item?.[levelField] || '其他';

    if (newSelectedItems.has(itemKey)) {
      newSelectedItems.delete(itemKey);
      // 检查是否需要取消层级选择
      const grouped = getGroupedData();
      const levelItems = grouped[level] || [];
      const levelItemsSelected = levelItems.some(levelItem => {
        const levelItemIndex = dataToUse.findIndex(d => d === levelItem);
        return levelItemIndex >= 0 && newSelectedItems.has(`item-${levelItemIndex}`);
      });
      if (!levelItemsSelected) {
        newSelectedLevels.delete(level);
      }
    } else {
      newSelectedItems.add(itemKey);
      // 检查是否需要添加层级选择
      const grouped = getGroupedData();
      const levelItems = grouped[level] || [];
      const allLevelItemsSelected = levelItems.every(levelItem => {
        const levelItemIndex = dataToUse.findIndex(d => d === levelItem);
        return levelItemIndex >= 0 && (newSelectedItems.has(`item-${levelItemIndex}`) || levelItemIndex === itemIndex);
      });
      if (allLevelItemsSelected) {
        newSelectedLevels.add(level);
      }
    }

    setSelectionState({
      ...selectionState,
      selectedLevels: newSelectedLevels,
      selectedItems: newSelectedItems
    });
  };

  // 层级全选
  const handleLevelSelectAll = (level) => {
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedLevels = new Set(selectionState.selectedLevels);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    grouped[level]?.forEach(item => {
      const itemIndex = dataToUse.findIndex(d => d === item);
      if (itemIndex >= 0) {
        newSelectedItems.add(`item-${itemIndex}`);
      }
    });
    newSelectedLevels.add(level);

    setSelectionState({
      ...selectionState,
      selectedLevels: newSelectedLevels,
      selectedItems: newSelectedItems
    });
  };

  // 层级反选
  const handleLevelUnselectAll = (level) => {
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedLevels = new Set(selectionState.selectedLevels);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    grouped[level]?.forEach(item => {
      const itemIndex = dataToUse.findIndex(d => d === item);
      if (itemIndex >= 0) {
        newSelectedItems.delete(`item-${itemIndex}`);
      }
    });
    newSelectedLevels.delete(level);

    setSelectionState({
      ...selectionState,
      selectedLevels: newSelectedLevels,
      selectedItems: newSelectedItems
    });
  };

  // 处理下载
  const handleDownload = () => {
    const selectedData = [];
    const dataToUse = getCurrentData();
    
    // 收集选中的数据
    dataToUse.forEach((item, index) => {
      const itemKey = `item-${index}`;
      if (selectionState.selectedItems.has(itemKey)) {
        selectedData.push({
          index,
          data: item
        });
      }
    });

    onDownload({
      selectedData,
      format: selectionState.downloadFormat,
      monthRange: selectionState.monthRange,
      levelFilter: selectionState.includeLevelFilter ? levelFilter : null,
      statistics,
      monthPairs
    });
  };

  // 格式化显示值
  const formatDisplayValue = (value) => {
    if (value === null || value === undefined || value === '') return '';
    if (typeof value === 'object') {
      if (value.hasOwnProperty('v')) return value.v;
      if (value.hasOwnProperty('w')) return value.w;
      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    return String(value);
  };

  if (!isOpen) return null;

  const groupedData = getGroupedData();
  const levels = getLevels();

  return (
    <div className="world-class-download-overlay">
      <div className="world-class-download-modal">
        <div className="modal-header">
          <h2>🌍 选择要下载的对标数据</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          {/* 筛选条件显示 */}
          <div className="current-filters">
            <div className="filter-item">
              <span className="filter-label">📅 月份范围:</span>
              <select
                value={selectionState.monthRange.start}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  monthRange: { ...selectionState.monthRange, start: Number(e.target.value) }
                })}
              >
                {monthPairs.map((pair, index) => (
                  <option key={index} value={index}>{pair[0]}</option>
                ))}
              </select>
              <span> 至 </span>
              <select
                value={selectionState.monthRange.end}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  monthRange: { ...selectionState.monthRange, end: Number(e.target.value) }
                })}
              >
                {monthPairs.map((pair, index) => (
                  <option key={index} value={index}>{pair[1]}</option>
                ))}
              </select>
            </div>
            
            {levelFilter.value !== '全部' && (
              <div className="filter-item">
                <span className="filter-label">🎯 当前筛选:</span>
                <span className="filter-value">{levelFilter.level} - {levelFilter.value}</span>
                <label className="include-filter-checkbox">
                  <input
                    type="checkbox"
                    checked={selectionState.includeLevelFilter}
                    onChange={(e) => setSelectionState({
                      ...selectionState,
                      includeLevelFilter: e.target.checked
                    })}
                  />
                  应用到下载
                </label>
              </div>
            )}
          </div>

          {/* 层级选择 */}
          <div className="levels-section">
            {levels.map(level => {
              const levelData = groupedData[level] || [];
              const isLevelSelected = selectionState.selectedLevels.has(level);
              
              return (
                <div key={level} className="level-section">
                  <div className="level-header">
                    <label className="level-checkbox">
                      <input
                        type="checkbox"
                        checked={isLevelSelected}
                        onChange={() => handleLevelSelect(level)}
                      />
                      <span className="level-title">
                        {level} ({levelData.length}项)
                      </span>
                    </label>
                    <div className="level-actions">
                      <button 
                        className="action-btn"
                        onClick={() => handleLevelSelectAll(level)}
                      >
                        全选
                      </button>
                      <button 
                        className="action-btn"
                        onClick={() => handleLevelUnselectAll(level)}
                      >
                        反选
                      </button>
                    </div>
                  </div>

                  <div className="level-items-list">
                    {levelData.map((item, levelIndex) => {
                      const dataToUse = getCurrentData();
                      const globalIndex = dataToUse.findIndex(d => d === item);
                      const itemKey = `item-${globalIndex}`;
                      const isItemSelected = selectionState.selectedItems.has(itemKey);
                      
                      const target = formatDisplayValue(item['2025年目标']);
                      const responsible = formatDisplayValue(item.负责人);

                      return (
                        <div key={levelIndex} className="level-item-row">
                          <label className="level-item-checkbox">
                            <input
                              type="checkbox"
                              checked={isItemSelected}
                              onChange={() => handleItemSelect(globalIndex)}
                            />
                            <span className="level-item-content">
                              <span className="level-item-target">{target}</span>
                              {responsible && (
                                <span className="level-item-responsible">负责人: {responsible}</span>
                              )}
                            </span>
                          </label>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="modal-footer">
          <div className="statistics">
            <span>已选择: {statistics.totalItems}项目标</span>
            <span>涵盖层级: {statistics.levelCount}种</span>
            {statistics.monthsCovered && (
              <span>月份: {statistics.monthsCovered}</span>
            )}
          </div>

          <div className="format-selection">
            <label>
              下载格式:
              <select 
                value={selectionState.downloadFormat}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  downloadFormat: e.target.value
                })}
              >
                <option value="excel">Excel格式</option>
                <option value="pdf">PDF格式</option>
                <option value="csv">CSV格式</option>
              </select>
            </label>
          </div>

          <div className="action-buttons">
            <button className="cancel-btn" onClick={onClose}>
              取消
            </button>
            <button 
              className="download-btn"
              onClick={handleDownload}
              disabled={statistics.totalItems === 0}
            >
              {selectionState.downloadFormat.toUpperCase()}下载
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorldClassDownloadModal; 