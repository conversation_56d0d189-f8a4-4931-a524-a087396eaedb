@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');

/* 模块四 - KPI跟踪仪表板样式 */

.monthly-kpi {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  padding: 20px;
  font-family: 'Rajdhani', 'Microsoft YaHei', sans-serif;
  color: #e0e0e0;
}

/* 加载状态 */
.kpi-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  color: #fff;
  text-shadow: 0 0 5px #ff4dff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 77, 255, 0.3);
  border-top: 4px solid #ff4dff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
  box-shadow: 0 0 10px rgba(255, 77, 255, 0.5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部区域 */
.kpi-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(15, 15, 35, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px 25px;
  border: 1px solid rgba(77, 200, 255, 0.3);
  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  padding: 10px 20px;
  border: none;
  background: linear-gradient(135deg, #4dc8ff, #ff4dff);
  color: #fff;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  font-family: 'Orbitron', sans-serif;
  text-shadow: 0 1px 3px rgba(0,0,0,0.4);
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);
  background: linear-gradient(135deg, #ff4dff, #4dc8ff);
}

.monthly-kpi .page-title {
  color: #fff;
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 77, 255, 0.8);
  font-family: 'Orbitron', sans-serif;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.sync-status {
  color: #e0e0e0;
  font-size: 14px;
}

.status-indicator {
  padding: 4px 10px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  text-transform: uppercase;
}

.status-indicator.success {
  color: #20ff4d;
  background: rgba(32, 255, 77, 0.2);
  font-size: 16px;
  font-weight: 600;
  display: block;
  border-radius: 6px;
  padding: 2px 10px;
  margin-left: 6px;
  border: 1px solid #20ff4d;
  box-shadow: 0 0 8px rgba(32, 255, 77, 0.3);
}

.status-indicator.pending {
  background: rgba(77, 200, 255, 0.2);
  color: #FFE599;
  border: 1px solid #4dc8ff;
  text-shadow: 0 0 4px #FFE599;
  box-shadow: 0 0 8px rgba(77, 200, 255, 0.3);
}

.refresh-button {
  padding: 10px 20px;
  height: 44px;
  border: 1px solid rgba(77, 200, 255, 0.6);
  background: linear-gradient(135deg, #4dc8ff, #8b5cf6);
  color: #fff;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  backdrop-filter: blur(5px);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.download-button {
  padding: 10px 20px;
  height: 44px;
  border: 1px solid rgba(77, 200, 255, 0.5);
  background: linear-gradient(135deg, #4dc8ff, #ff4dff);
  color: #fff;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  backdrop-filter: blur(5px);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover {
  background: linear-gradient(135deg, #8b5cf6, #4dc8ff);
  border-color: #8b5cf6;
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
}

.download-button:hover {
  background: linear-gradient(135deg, #ff4dff, #4dc8ff);
  border-color: #4dc8ff;
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(77, 200, 255, 0.4);
}

/* 月份导航区域 */
.month-navigation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  background: rgba(15, 15, 35, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 32px;
  padding: 0;
  border: 1.5px solid rgba(255, 107, 77, 0.3);
  box-shadow: 0 0 10px rgba(255, 77, 255, 0.08);
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  min-width: 340px;
}

.nav-button {
  padding: 0 20px;
  height: 44px;
  border: none;
  background: linear-gradient(135deg, #4dc8ff 0%, #ff4dff 100%);
  color: #fff;
  border-radius: 32px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 15px;
  font-weight: 700;
  margin: 0 8px;
  box-shadow: 0 2px 8px rgba(255, 77, 255, 0.08);
  font-family: 'Rajdhani', 'Orbitron', sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: linear-gradient(135deg, #3a3a5e 0%, #1a1a2e 100%);
  color: #aaa;
  border: none;
}

.nav-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff4dff 0%, #4dc8ff 100%);
  transform: translateY(-1px) scale(1.04);
  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.3);
}

.current-months {
  color: #fff;
  font-size: 20px;
  font-weight: 700;
  text-shadow: 0 0 8px rgba(255, 107, 77, 0.7);
  font-family: 'Orbitron', sans-serif;
  margin: 0 16px;
  letter-spacing: 1.5px;
  min-width: 110px;
  text-align: center;
  background: transparent;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 48px;
}

/* 内容区域 */
.kpi-content {
  background: rgba(15, 15, 35, 0.6);
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  margin-bottom: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 77, 255, 0.1);
}

/* 表格容器 */
.kpi-table-container {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid rgba(77, 200, 255, 0.2);
  background: transparent;
  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);
}

/* 表格样式 */
.kpi-data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  line-height: 1.5;
  color: #e0e0e0;
}

.kpi-data-table thead {
  background: linear-gradient(90deg, #4dc8ff, #ff4dff, #ff6b4d);
  color: #fff;
}

.kpi-data-table th {
  padding: 15px 10px;
  text-align: center;
  font-weight: 600;
  font-size: 14px;
  border-right: 1px solid rgba(255, 255, 255, 0.3);
  white-space: nowrap;
  text-transform: uppercase;
  color: #fff;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.kpi-data-table th:last-child {
  border-right: none;
}

/* 表格行样式 */
.kpi-data-row {
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(255, 77, 255, 0.1);
}

.kpi-data-row:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.03);
}

.kpi-data-row:hover {
  background-color: rgba(255, 77, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 合并单元格样式 */
.merged-row {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

.merged-start-row {
  background-color: rgba(255, 107, 77, 0.1);
}

.merged-cell-content {
  background-color: transparent !important;
  font-weight: 600;
  vertical-align: middle;
  color: #ff6b4d;
}

/* 表格单元格 */
.kpi-data-cell {
  padding: 12px 8px;
  border-right: 1px solid rgba(255, 77, 255, 0.1);
  border-bottom: none; /* Handled by row */
  vertical-align: middle;
  text-align: center;
}

.kpi-data-cell:last-child {
  border-right: none;
}

/* 列宽控制 */
.col-number { width: 60px; }
.col-indicator { width: 180px; text-align: left; color: #fff; }
.col-target { width: 250px; text-align: left; }
.col-score { width: 80px; color: #ff6b4d; font-weight: 600; }
.col-method { width: 150px; }
.col-standard { width: 200px; text-align: left; }
.col-month { width: 80px; color: #ff4dff; font-weight: 600; }
.col-completion { width: 180px; }
.col-score-month { width: 80px; color: #ff6b4d; font-weight: 600; }

/* 可编辑单元格 */
.editable {
  cursor: pointer;
  position: relative;
}

.editable:hover .cell-content {
  background-color: rgba(255, 107, 77, 0.2);
}

.cell-content {
  min-height: 20px;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.cell-content.clickable:hover {
  background-color: rgba(255, 77, 255, 0.1);
  border: 1px dashed #ff4dff;
}

/* 只读单元格样式 */
.cell-content.readonly-cell {
  background: rgba(128, 128, 128, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  cursor: not-allowed !important;
  border: 1px solid rgba(128, 128, 128, 0.3) !important;
  transition: all 0.3s ease;
}

.cell-content.readonly-cell:hover {
  background: rgba(128, 128, 128, 0.15) !important;
  border: 1px solid rgba(128, 128, 128, 0.4) !important;
  transform: none !important;
}

/* 只读列的表头样式 */
.col-completion.readonly-header {
  background: rgba(128, 128, 128, 0.2) !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 单元格编辑器 */
.cell-editor {
  width: 100%;
  min-height: 60px;
  border: 2px solid #ff4dff;
  border-radius: 4px;
  padding: 6px;
  font-size: 13px;
  resize: vertical;
  outline: none;
  background: #1a1a2e;
  color: #e0e0e0;
  box-shadow: 0 0 10px rgba(255, 77, 255, 0.3);
}

.cell-editor:focus {
  border-color: #ff6b4d;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: #a0a0a0;
}

.no-data p {
  font-size: 16px;
  margin: 10px 0;
  text-shadow: 0 0 3px rgba(255, 77, 255, 0.5);
}

/* 底部统计 */
.kpi-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: rgba(15, 15, 35, 0.4);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 15px 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.data-stats {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .kpi-data-table {
    font-size: 12px;
  }
  
  .col-indicator,
  .col-target,
  .col-standard {
    min-width: 150px;
  }
}

@media (max-width: 768px) {
  .monthly-kpi {
    padding: 10px;
  }
  
  .kpi-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
    padding: 15px;
  }
  
  .header-left,
  .header-right {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }
  
  .month-navigation {
    flex-direction: column;
    gap: 10px;
    min-width: 0;
    width: 100%;
    border-radius: 20px;
    padding: 6px 0;
  }
  .nav-button, .current-months {
    height: 40px;
    font-size: 15px;
    min-width: 80px;
    margin: 0 4px;
  }
  .current-months {
    font-size: 16px;
    min-width: 80px;
    margin: 0 8px;
  }
  
  .kpi-content {
    padding: 15px;
  }
  
  .kpi-data-table {
    font-size: 11px;
  }
  
  .kpi-data-cell {
    padding: 8px 4px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kpi-content, .kpi-header, .month-navigation, .kpi-footer {
  animation: fadeIn 0.6s ease-out;
}

/* 滚动条样式 */
.kpi-table-container::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.kpi-table-container::-webkit-scrollbar-track {
  background: rgba(15, 15, 35, 0.5);
  border-radius: 4px;
}

.kpi-table-container::-webkit-scrollbar-thumb {
  background: #ff4dff;
  border-radius: 4px;
  border: 1px solid #ff6b4d;
}

.kpi-table-container::-webkit-scrollbar-thumb:hover {
  background: #ff6b4d;
}

/* 控制面板样式 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(15, 15, 35, 0.6);
  border-radius: 15px;
  padding: 20px 25px;
  margin-bottom: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(77, 200, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.control-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.control-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 指标类型选择区域 */
.indicator-type-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.filter-label {
  color: #a78bfa;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 0 5px rgba(167, 139, 250, 0.3);
  min-width: 70px;
}

.indicator-selector-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
}

.indicator-type-selector {
  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.9));
  border: 1px solid rgba(77, 200, 255, 0.3);
  border-radius: 8px;
  color: #4dc8ff;
  padding: 8px 16px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Rajdhani', sans-serif;
  min-width: 120px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(77, 200, 255, 0.1);
}

.indicator-type-selector:hover {
  border-color: rgba(77, 200, 255, 0.6);
  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.2);
  transform: translateY(-1px);
}

.indicator-type-selector:focus {
  outline: none;
  border-color: #4dc8ff;
  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);
}

.indicator-type-selector option {
  background: #1a1a2e;
  color: #4dc8ff;
  padding: 8px;
}



/* 当前显示统计 */
.current-display-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(0, 212, 170, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 170, 0.3);
  backdrop-filter: blur(5px);
}

.stats-label {
  color: #00d4aa;
  font-size: 16px;
  font-weight: 500;
}

.stats-value {
  color: #20ff4d;
  font-size: 16px;
  font-weight: 700;
  text-shadow: 0 0 8px rgba(32, 255, 77, 0.3);
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
  height: 44px;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .control-panel {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .control-left,
  .control-right {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .action-buttons {
    justify-content: center;
    width: 100%;
    gap: 12px;
  }

  .indicator-filter-panel {
    width: 95%;
    max-height: 90vh;
  }

  .filter-header,
  .filter-content,
  .filter-footer {
    padding: 15px 20px;
  }
}