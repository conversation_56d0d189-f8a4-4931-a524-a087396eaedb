{"ast": null, "code": "import React from'react';import routeGuard from'../utils/routeGuards';import{ROLE_DISPLAY_NAMES}from'../utils/rolePermissions';import'../styles/PermissionGuard.css';// 权限守卫组件 - 用于包装需要权限检查的组件或元素\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PermissionGuard=_ref=>{let{children,requiredPermission,userRole,fallback=null,showDeniedMessage=true,customDeniedMessage=null}=_ref;// 检查权限\nconst hasPermission=routeGuard.checkButtonPermission(requiredPermission,userRole);if(hasPermission){return children;}// 权限不足时的处理\nif(fallback){return fallback;}if(showDeniedMessage){const message=customDeniedMessage||routeGuard.getPermissionDeniedMessage(requiredPermission);return/*#__PURE__*/_jsx(\"div\",{className:\"permission-denied-wrapper\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"permission-denied-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"permission-denied-icon\",children:\"\\uD83D\\uDD12\"}),/*#__PURE__*/_jsx(\"span\",{className:\"permission-denied-text\",children:message})]})});}return null;};// 页面级权限守卫组件\nconst PageGuard=_ref2=>{let{children,pageName,userRole,isAuthenticated=false,onAccessDenied=null}=_ref2;// 检查是否已登录\nif(!isAuthenticated){return/*#__PURE__*/_jsx(\"div\",{className:\"auth-required-page\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"auth-required-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"auth-required-icon\",children:\"\\uD83D\\uDD10\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"auth-required-title\",children:\"\\u9700\\u8981\\u767B\\u5F55\"}),/*#__PURE__*/_jsx(\"p\",{className:\"auth-required-message\",children:\"\\u8BF7\\u5148\\u767B\\u5F55\\u7CFB\\u7EDF\\u4EE5\\u8BBF\\u95EE\\u6B64\\u9875\\u9762\"}),/*#__PURE__*/_jsx(\"div\",{className:\"auth-required-hint\",children:\"\\u70B9\\u51FB\\u5DE6\\u4FA7\\u767B\\u5F55\\u6309\\u94AE\\u8FDB\\u884C\\u8EAB\\u4EFD\\u9A8C\\u8BC1\"})]})});}// 检查页面访问权限\nconst accessResult=routeGuard.canAccess(pageName);if(accessResult.allowed){return children;}// 权限不足时的处理\nif(onAccessDenied){onAccessDenied(accessResult);}return/*#__PURE__*/_jsx(\"div\",{className:\"page-access-denied\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"access-denied-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"access-denied-icon\",children:\"\\uD83D\\uDEAB\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"access-denied-title\",children:\"\\u8BBF\\u95EE\\u88AB\\u62D2\\u7EDD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"access-denied-message\",children:accessResult.message}),/*#__PURE__*/_jsxs(\"div\",{className:\"access-denied-details\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"current-role\",children:[\"\\u5F53\\u524D\\u89D2\\u8272: \",ROLE_DISPLAY_NAMES[userRole]||'未知']}),/*#__PURE__*/_jsx(\"div\",{className:\"required-permissions\",children:\"\\u6B64\\u9875\\u9762\\u9700\\u8981\\u66F4\\u9AD8\\u7EA7\\u522B\\u7684\\u6743\\u9650\\u624D\\u80FD\\u8BBF\\u95EE\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"access-denied-actions\",children:/*#__PURE__*/_jsx(\"button\",{className:\"back-button\",onClick:()=>window.history.back(),children:\"\\u8FD4\\u56DE\\u4E0A\\u4E00\\u9875\"})})]})});};// 按钮权限守卫组件\nconst ButtonGuard=_ref3=>{let{children,permission,userRole,showDisabled=true,disabledText=null,onClick=null}=_ref3;const hasPermission=routeGuard.checkButtonPermission(permission,userRole);if(hasPermission){return children;}if(!showDisabled){return null;}// 克隆子元素并添加禁用状态\nconst childElement=React.Children.only(children);const handleDisabledClick=e=>{e.preventDefault();e.stopPropagation();const message=disabledText||routeGuard.getPermissionDeniedMessage(permission);// 显示权限不足提示\nconst notification=document.createElement('div');notification.className='button-permission-denied';notification.innerHTML=\"\\n      <span class=\\\"button-denied-icon\\\">\\uD83D\\uDD12</span>\\n      <span class=\\\"button-denied-text\\\">\".concat(message,\"</span>\\n    \");notification.style.cssText=\"\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      background: rgba(255, 69, 58, 0.95);\\n      color: white;\\n      padding: 15px 20px;\\n      border-radius: 10px;\\n      font-size: 14px;\\n      font-weight: 500;\\n      z-index: 10001;\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n      backdrop-filter: blur(10px);\\n      border: 1px solid rgba(255, 69, 58, 0.3);\\n      box-shadow: 0 4px 20px rgba(255, 69, 58, 0.3);\\n      animation: slideInRight 0.3s ease;\\n      max-width: 300px;\\n    \";document.body.appendChild(notification);setTimeout(()=>{if(notification.parentNode){notification.parentNode.removeChild(notification);}},3000);};return/*#__PURE__*/React.cloneElement(childElement,{disabled:true,className:\"\".concat(childElement.props.className||'',\" permission-disabled\"),onClick:handleDisabledClick,title:disabledText||routeGuard.getPermissionDeniedMessage(permission)});};// 导航菜单权限过滤器\nconst NavigationFilter=_ref4=>{let{navigationItems,userRole,isAuthenticated=false,renderItem}=_ref4;if(!isAuthenticated){return null;}const accessibleItems=navigationItems.filter(item=>{const accessResult=routeGuard.canAccess(item.page||item.id);return accessResult.allowed;});return/*#__PURE__*/_jsx(\"div\",{className:\"filtered-navigation\",children:accessibleItems.map((item,index)=>renderItem?renderItem(item,index):/*#__PURE__*/_jsx(\"div\",{className:\"nav-item\",children:item.title||item.name},item.id||index))});};// 权限信息显示组件\nconst PermissionInfo=_ref5=>{let{userRole,permissions=[]}=_ref5;return/*#__PURE__*/_jsxs(\"div\",{className:\"permission-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"permission-role\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"permission-label\",children:\"\\u5F53\\u524D\\u89D2\\u8272:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"permission-value\",children:ROLE_DISPLAY_NAMES[userRole]||'未知'})]}),permissions.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"permission-list\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"permission-label\",children:\"\\u62E5\\u6709\\u6743\\u9650:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"permission-items\",children:permissions.map((permission,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"permission-item\",children:permission},index))})]})]});};export default PermissionGuard;export{PageGuard,ButtonGuard,NavigationFilter,PermissionInfo};", "map": {"version": 3, "names": ["React", "routeGuard", "ROLE_DISPLAY_NAMES", "jsx", "_jsx", "jsxs", "_jsxs", "PermissionGuard", "_ref", "children", "requiredPermission", "userRole", "fallback", "showDeniedMessage", "customDeniedMessage", "hasPermission", "checkButtonPermission", "message", "getPermissionDeniedMessage", "className", "<PERSON><PERSON><PERSON>", "_ref2", "pageName", "isAuthenticated", "onAccessDenied", "accessResult", "canAccess", "allowed", "onClick", "window", "history", "back", "<PERSON><PERSON><PERSON><PERSON>", "_ref3", "permission", "showDisabled", "disabledText", "childElement", "Children", "only", "handleDisabledClick", "e", "preventDefault", "stopPropagation", "notification", "document", "createElement", "innerHTML", "concat", "style", "cssText", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "cloneElement", "disabled", "props", "title", "NavigationFilter", "_ref4", "navigationItems", "renderItem", "accessibleItems", "filter", "item", "page", "id", "map", "index", "name", "PermissionInfo", "_ref5", "permissions", "length"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/components/PermissionGuard.js"], "sourcesContent": ["import React from 'react';\nimport routeGuard from '../utils/routeGuards';\nimport { ROLE_DISPLAY_NAMES } from '../utils/rolePermissions';\nimport '../styles/PermissionGuard.css';\n\n// 权限守卫组件 - 用于包装需要权限检查的组件或元素\nconst PermissionGuard = ({ \n  children, \n  requiredPermission, \n  userRole, \n  fallback = null,\n  showDeniedMessage = true,\n  customDeniedMessage = null\n}) => {\n  // 检查权限\n  const hasPermission = routeGuard.checkButtonPermission(requiredPermission, userRole);\n\n  if (hasPermission) {\n    return children;\n  }\n\n  // 权限不足时的处理\n  if (fallback) {\n    return fallback;\n  }\n\n  if (showDeniedMessage) {\n    const message = customDeniedMessage || routeGuard.getPermissionDeniedMessage(requiredPermission);\n    \n    return (\n      <div className=\"permission-denied-wrapper\">\n        <div className=\"permission-denied-content\">\n          <span className=\"permission-denied-icon\">🔒</span>\n          <span className=\"permission-denied-text\">{message}</span>\n        </div>\n      </div>\n    );\n  }\n\n  return null;\n};\n\n// 页面级权限守卫组件\nconst PageGuard = ({ \n  children, \n  pageName, \n  userRole, \n  isAuthenticated = false,\n  onAccessDenied = null \n}) => {\n  // 检查是否已登录\n  if (!isAuthenticated) {\n    return (\n      <div className=\"auth-required-page\">\n        <div className=\"auth-required-content\">\n          <div className=\"auth-required-icon\">🔐</div>\n          <h2 className=\"auth-required-title\">需要登录</h2>\n          <p className=\"auth-required-message\">\n            请先登录系统以访问此页面\n          </p>\n          <div className=\"auth-required-hint\">\n            点击左侧登录按钮进行身份验证\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // 检查页面访问权限\n  const accessResult = routeGuard.canAccess(pageName);\n  \n  if (accessResult.allowed) {\n    return children;\n  }\n\n  // 权限不足时的处理\n  if (onAccessDenied) {\n    onAccessDenied(accessResult);\n  }\n\n  return (\n    <div className=\"page-access-denied\">\n      <div className=\"access-denied-content\">\n        <div className=\"access-denied-icon\">🚫</div>\n        <h2 className=\"access-denied-title\">访问被拒绝</h2>\n        <p className=\"access-denied-message\">{accessResult.message}</p>\n        <div className=\"access-denied-details\">\n          <div className=\"current-role\">\n            当前角色: {ROLE_DISPLAY_NAMES[userRole] || '未知'}\n          </div>\n          <div className=\"required-permissions\">\n            此页面需要更高级别的权限才能访问\n          </div>\n        </div>\n        <div className=\"access-denied-actions\">\n          <button \n            className=\"back-button\"\n            onClick={() => window.history.back()}\n          >\n            返回上一页\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// 按钮权限守卫组件\nconst ButtonGuard = ({ \n  children, \n  permission, \n  userRole, \n  showDisabled = true,\n  disabledText = null,\n  onClick = null\n}) => {\n  const hasPermission = routeGuard.checkButtonPermission(permission, userRole);\n\n  if (hasPermission) {\n    return children;\n  }\n\n  if (!showDisabled) {\n    return null;\n  }\n\n  // 克隆子元素并添加禁用状态\n  const childElement = React.Children.only(children);\n  \n  const handleDisabledClick = (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    \n    const message = disabledText || routeGuard.getPermissionDeniedMessage(permission);\n    \n    // 显示权限不足提示\n    const notification = document.createElement('div');\n    notification.className = 'button-permission-denied';\n    notification.innerHTML = `\n      <span class=\"button-denied-icon\">🔒</span>\n      <span class=\"button-denied-text\">${message}</span>\n    `;\n    \n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: rgba(255, 69, 58, 0.95);\n      color: white;\n      padding: 15px 20px;\n      border-radius: 10px;\n      font-size: 14px;\n      font-weight: 500;\n      z-index: 10001;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      backdrop-filter: blur(10px);\n      border: 1px solid rgba(255, 69, 58, 0.3);\n      box-shadow: 0 4px 20px rgba(255, 69, 58, 0.3);\n      animation: slideInRight 0.3s ease;\n      max-width: 300px;\n    `;\n    \n    document.body.appendChild(notification);\n    \n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 3000);\n  };\n\n  return React.cloneElement(childElement, {\n    disabled: true,\n    className: `${childElement.props.className || ''} permission-disabled`,\n    onClick: handleDisabledClick,\n    title: disabledText || routeGuard.getPermissionDeniedMessage(permission)\n  });\n};\n\n// 导航菜单权限过滤器\nconst NavigationFilter = ({ \n  navigationItems, \n  userRole, \n  isAuthenticated = false,\n  renderItem \n}) => {\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  const accessibleItems = navigationItems.filter(item => {\n    const accessResult = routeGuard.canAccess(item.page || item.id);\n    return accessResult.allowed;\n  });\n\n  return (\n    <div className=\"filtered-navigation\">\n      {accessibleItems.map((item, index) => \n        renderItem ? renderItem(item, index) : (\n          <div key={item.id || index} className=\"nav-item\">\n            {item.title || item.name}\n          </div>\n        )\n      )}\n    </div>\n  );\n};\n\n// 权限信息显示组件\nconst PermissionInfo = ({ userRole, permissions = [] }) => {\n  return (\n    <div className=\"permission-info\">\n      <div className=\"permission-role\">\n        <span className=\"permission-label\">当前角色:</span>\n        <span className=\"permission-value\">{ROLE_DISPLAY_NAMES[userRole] || '未知'}</span>\n      </div>\n      {permissions.length > 0 && (\n        <div className=\"permission-list\">\n          <span className=\"permission-label\">拥有权限:</span>\n          <div className=\"permission-items\">\n            {permissions.map((permission, index) => (\n              <span key={index} className=\"permission-item\">\n                {permission}\n              </span>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default PermissionGuard;\nexport { \n  PageGuard, \n  ButtonGuard, \n  NavigationFilter, \n  PermissionInfo \n};\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,OAASC,kBAAkB,KAAQ,0BAA0B,CAC7D,MAAO,+BAA+B,CAEtC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAOlB,IAPmB,CACvBC,QAAQ,CACRC,kBAAkB,CAClBC,QAAQ,CACRC,QAAQ,CAAG,IAAI,CACfC,iBAAiB,CAAG,IAAI,CACxBC,mBAAmB,CAAG,IACxB,CAAC,CAAAN,IAAA,CACC;AACA,KAAM,CAAAO,aAAa,CAAGd,UAAU,CAACe,qBAAqB,CAACN,kBAAkB,CAAEC,QAAQ,CAAC,CAEpF,GAAII,aAAa,CAAE,CACjB,MAAO,CAAAN,QAAQ,CACjB,CAEA;AACA,GAAIG,QAAQ,CAAE,CACZ,MAAO,CAAAA,QAAQ,CACjB,CAEA,GAAIC,iBAAiB,CAAE,CACrB,KAAM,CAAAI,OAAO,CAAGH,mBAAmB,EAAIb,UAAU,CAACiB,0BAA0B,CAACR,kBAAkB,CAAC,CAEhG,mBACEN,IAAA,QAAKe,SAAS,CAAC,2BAA2B,CAAAV,QAAA,cACxCH,KAAA,QAAKa,SAAS,CAAC,2BAA2B,CAAAV,QAAA,eACxCL,IAAA,SAAMe,SAAS,CAAC,wBAAwB,CAAAV,QAAA,CAAC,cAAE,CAAM,CAAC,cAClDL,IAAA,SAAMe,SAAS,CAAC,wBAAwB,CAAAV,QAAA,CAAEQ,OAAO,CAAO,CAAC,EACtD,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAG,SAAS,CAAGC,KAAA,EAMZ,IANa,CACjBZ,QAAQ,CACRa,QAAQ,CACRX,QAAQ,CACRY,eAAe,CAAG,KAAK,CACvBC,cAAc,CAAG,IACnB,CAAC,CAAAH,KAAA,CACC;AACA,GAAI,CAACE,eAAe,CAAE,CACpB,mBACEnB,IAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAV,QAAA,cACjCH,KAAA,QAAKa,SAAS,CAAC,uBAAuB,CAAAV,QAAA,eACpCL,IAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAV,QAAA,CAAC,cAAE,CAAK,CAAC,cAC5CL,IAAA,OAAIe,SAAS,CAAC,qBAAqB,CAAAV,QAAA,CAAC,0BAAI,CAAI,CAAC,cAC7CL,IAAA,MAAGe,SAAS,CAAC,uBAAuB,CAAAV,QAAA,CAAC,0EAErC,CAAG,CAAC,cACJL,IAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAV,QAAA,CAAC,sFAEpC,CAAK,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA;AACA,KAAM,CAAAgB,YAAY,CAAGxB,UAAU,CAACyB,SAAS,CAACJ,QAAQ,CAAC,CAEnD,GAAIG,YAAY,CAACE,OAAO,CAAE,CACxB,MAAO,CAAAlB,QAAQ,CACjB,CAEA;AACA,GAAIe,cAAc,CAAE,CAClBA,cAAc,CAACC,YAAY,CAAC,CAC9B,CAEA,mBACErB,IAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAV,QAAA,cACjCH,KAAA,QAAKa,SAAS,CAAC,uBAAuB,CAAAV,QAAA,eACpCL,IAAA,QAAKe,SAAS,CAAC,oBAAoB,CAAAV,QAAA,CAAC,cAAE,CAAK,CAAC,cAC5CL,IAAA,OAAIe,SAAS,CAAC,qBAAqB,CAAAV,QAAA,CAAC,gCAAK,CAAI,CAAC,cAC9CL,IAAA,MAAGe,SAAS,CAAC,uBAAuB,CAAAV,QAAA,CAAEgB,YAAY,CAACR,OAAO,CAAI,CAAC,cAC/DX,KAAA,QAAKa,SAAS,CAAC,uBAAuB,CAAAV,QAAA,eACpCH,KAAA,QAAKa,SAAS,CAAC,cAAc,CAAAV,QAAA,EAAC,4BACtB,CAACP,kBAAkB,CAACS,QAAQ,CAAC,EAAI,IAAI,EACxC,CAAC,cACNP,IAAA,QAAKe,SAAS,CAAC,sBAAsB,CAAAV,QAAA,CAAC,kGAEtC,CAAK,CAAC,EACH,CAAC,cACNL,IAAA,QAAKe,SAAS,CAAC,uBAAuB,CAAAV,QAAA,cACpCL,IAAA,WACEe,SAAS,CAAC,aAAa,CACvBS,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,CAAE,CAAAtB,QAAA,CACtC,gCAED,CAAQ,CAAC,CACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAuB,WAAW,CAAGC,KAAA,EAOd,IAPe,CACnBxB,QAAQ,CACRyB,UAAU,CACVvB,QAAQ,CACRwB,YAAY,CAAG,IAAI,CACnBC,YAAY,CAAG,IAAI,CACnBR,OAAO,CAAG,IACZ,CAAC,CAAAK,KAAA,CACC,KAAM,CAAAlB,aAAa,CAAGd,UAAU,CAACe,qBAAqB,CAACkB,UAAU,CAAEvB,QAAQ,CAAC,CAE5E,GAAII,aAAa,CAAE,CACjB,MAAO,CAAAN,QAAQ,CACjB,CAEA,GAAI,CAAC0B,YAAY,CAAE,CACjB,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAE,YAAY,CAAGrC,KAAK,CAACsC,QAAQ,CAACC,IAAI,CAAC9B,QAAQ,CAAC,CAElD,KAAM,CAAA+B,mBAAmB,CAAIC,CAAC,EAAK,CACjCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClBD,CAAC,CAACE,eAAe,CAAC,CAAC,CAEnB,KAAM,CAAA1B,OAAO,CAAGmB,YAAY,EAAInC,UAAU,CAACiB,0BAA0B,CAACgB,UAAU,CAAC,CAEjF;AACA,KAAM,CAAAU,YAAY,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAClDF,YAAY,CAACzB,SAAS,CAAG,0BAA0B,CACnDyB,YAAY,CAACG,SAAS,6GAAAC,MAAA,CAEe/B,OAAO,iBAC3C,CAED2B,YAAY,CAACK,KAAK,CAACC,OAAO,whBAmBzB,CAEDL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,YAAY,CAAC,CAEvCS,UAAU,CAAC,IAAM,CACf,GAAIT,YAAY,CAACU,UAAU,CAAE,CAC3BV,YAAY,CAACU,UAAU,CAACC,WAAW,CAACX,YAAY,CAAC,CACnD,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,mBAAO5C,KAAK,CAACwD,YAAY,CAACnB,YAAY,CAAE,CACtCoB,QAAQ,CAAE,IAAI,CACdtC,SAAS,IAAA6B,MAAA,CAAKX,YAAY,CAACqB,KAAK,CAACvC,SAAS,EAAI,EAAE,wBAAsB,CACtES,OAAO,CAAEY,mBAAmB,CAC5BmB,KAAK,CAAEvB,YAAY,EAAInC,UAAU,CAACiB,0BAA0B,CAACgB,UAAU,CACzE,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA0B,gBAAgB,CAAGC,KAAA,EAKnB,IALoB,CACxBC,eAAe,CACfnD,QAAQ,CACRY,eAAe,CAAG,KAAK,CACvBwC,UACF,CAAC,CAAAF,KAAA,CACC,GAAI,CAACtC,eAAe,CAAE,CACpB,MAAO,KAAI,CACb,CAEA,KAAM,CAAAyC,eAAe,CAAGF,eAAe,CAACG,MAAM,CAACC,IAAI,EAAI,CACrD,KAAM,CAAAzC,YAAY,CAAGxB,UAAU,CAACyB,SAAS,CAACwC,IAAI,CAACC,IAAI,EAAID,IAAI,CAACE,EAAE,CAAC,CAC/D,MAAO,CAAA3C,YAAY,CAACE,OAAO,CAC7B,CAAC,CAAC,CAEF,mBACEvB,IAAA,QAAKe,SAAS,CAAC,qBAAqB,CAAAV,QAAA,CACjCuD,eAAe,CAACK,GAAG,CAAC,CAACH,IAAI,CAAEI,KAAK,GAC/BP,UAAU,CAAGA,UAAU,CAACG,IAAI,CAAEI,KAAK,CAAC,cAClClE,IAAA,QAA4Be,SAAS,CAAC,UAAU,CAAAV,QAAA,CAC7CyD,IAAI,CAACP,KAAK,EAAIO,IAAI,CAACK,IAAI,EADhBL,IAAI,CAACE,EAAE,EAAIE,KAEhB,CAET,CAAC,CACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAGC,KAAA,EAAoC,IAAnC,CAAE9D,QAAQ,CAAE+D,WAAW,CAAG,EAAG,CAAC,CAAAD,KAAA,CACpD,mBACEnE,KAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAV,QAAA,eAC9BH,KAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAV,QAAA,eAC9BL,IAAA,SAAMe,SAAS,CAAC,kBAAkB,CAAAV,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC/CL,IAAA,SAAMe,SAAS,CAAC,kBAAkB,CAAAV,QAAA,CAAEP,kBAAkB,CAACS,QAAQ,CAAC,EAAI,IAAI,CAAO,CAAC,EAC7E,CAAC,CACL+D,WAAW,CAACC,MAAM,CAAG,CAAC,eACrBrE,KAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAV,QAAA,eAC9BL,IAAA,SAAMe,SAAS,CAAC,kBAAkB,CAAAV,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC/CL,IAAA,QAAKe,SAAS,CAAC,kBAAkB,CAAAV,QAAA,CAC9BiE,WAAW,CAACL,GAAG,CAAC,CAACnC,UAAU,CAAEoC,KAAK,gBACjClE,IAAA,SAAkBe,SAAS,CAAC,iBAAiB,CAAAV,QAAA,CAC1CyB,UAAU,EADFoC,KAEL,CACP,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA/D,eAAe,CAC9B,OACEa,SAAS,CACTY,WAAW,CACX4B,gBAAgB,CAChBY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}