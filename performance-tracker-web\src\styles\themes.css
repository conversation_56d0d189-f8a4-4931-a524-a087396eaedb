/* 全局主题样式文件 */

/* 默认深色主题 */
body, body.theme-dark {
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
}

/* 浅色主题 */
body.theme-light {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #333333;
}

/* 浅色主题下的组件样式调整 */
body.theme-light .personal-settings {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #333333;
}

body.theme-light .settings-sidebar {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body.theme-light .settings-main {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body.theme-light .form-section {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body.theme-light .preference-item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body.theme-light .nav-item {
  color: #555555;
}

body.theme-light .nav-item:hover {
  background: rgba(78, 205, 196, 0.1);
  color: #4ecdc4;
}

body.theme-light .nav-item.active {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
}

body.theme-light .form-group input,
body.theme-light .form-group select,
body.theme-light .form-select {
  background: rgba(255, 255, 255, 0.9);
  color: #333333;
  border: 1px solid rgba(0, 0, 0, 0.2);
}

body.theme-light .form-select option {
  background: #ffffff !important;
  color: #333333 !important;
}

body.theme-light .info-value {
  background: rgba(255, 255, 255, 0.8);
  color: #333333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

body.theme-light .tab-header h2 {
  color: #4ecdc4;
}

body.theme-light .tab-header p {
  color: #666666;
}

body.theme-light .preference-info h4 {
  color: #333333;
}

body.theme-light .preference-info p {
  color: #666666;
}

body.theme-light .form-section h3 {
  color: #4ecdc4;
}

/* 自动主题 - 跟随系统设置 */
@media (prefers-color-scheme: dark) {
  body.theme-auto {
    background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    color: #ffffff;
  }
  
  body.theme-auto .form-select option {
    background: #4ecdc4 !important;
    color: #ffffff !important;
  }
}

@media (prefers-color-scheme: light) {
  body.theme-auto {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333333;
  }
  
  body.theme-auto .personal-settings {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333333;
  }
  
  body.theme-auto .settings-sidebar {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  body.theme-auto .settings-main {
    background: rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  body.theme-auto .form-section {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  body.theme-auto .preference-item {
    background: rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  body.theme-auto .nav-item {
    color: #555555;
  }
  
  body.theme-auto .nav-item:hover {
    background: rgba(78, 205, 196, 0.1);
    color: #4ecdc4;
  }
  
  body.theme-auto .nav-item.active {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
  }
  
  body.theme-auto .form-group input,
  body.theme-auto .form-group select,
  body.theme-auto .form-select {
    background: rgba(255, 255, 255, 0.9);
    color: #333333;
    border: 1px solid rgba(0, 0, 0, 0.2);
  }
  
  body.theme-auto .form-select option {
    background: #ffffff !important;
    color: #333333 !important;
  }
  
  body.theme-auto .info-value {
    background: rgba(255, 255, 255, 0.8);
    color: #333333;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  body.theme-auto .tab-header h2 {
    color: #4ecdc4;
  }
  
  body.theme-auto .tab-header p {
    color: #666666;
  }
  
  body.theme-auto .preference-info h4 {
    color: #333333;
  }
  
  body.theme-auto .preference-info p {
    color: #666666;
  }
  
  body.theme-auto .form-section h3 {
    color: #4ecdc4;
  }
}

/* 主题切换动画 */
body {
  transition: background 0.3s ease, color 0.3s ease;
}

.personal-settings,
.settings-sidebar,
.settings-main,
.form-section,
.preference-item,
.nav-item,
.form-group input,
.form-group select,
.form-select,
.info-value {
  transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
