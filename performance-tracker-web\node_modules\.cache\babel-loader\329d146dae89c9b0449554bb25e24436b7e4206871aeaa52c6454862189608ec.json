{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import*as XLSX from'xlsx';class ExcelService{constructor(){this.workbook=null;this.data={};}// 加载Excel文件\nasync loadExcel(){try{// 首先尝试从后端API加载真实数据\nconsole.log('正在从API加载Excel数据...');const response=await fetch('http://localhost:3001/api/excel-data');if(response.ok){const result=await response.json();if(result.data&&result.data.keyWork&&result.data.keyWork.length>0){// 只有当API返回的数据包含完整的keyWork数据时才使用\nthis.data=result.data;this.lastModified=new Date(result.lastModified);console.log('从Excel文件成功加载数据:',this.data);return this.data;}else{console.warn('API返回的数据不完整，缺少重点工作数据');}}else{console.warn('API响应失败，状态码:',response.status);}}catch(error){console.error('从API加载Excel数据失败:',error);}// 如果API失败或数据不完整，使用本地静态数据\nconsole.log('使用本地静态数据');this.data=this.getExactExcelData();return this.data;}// 解析工作目标管理责任书数据\nparseWorkTargetData(){const sheetName='3.开发中心';const sheet=this.workbook.Sheets[sheetName];if(!sheet){console.error('找不到工作表:',sheetName);this.data=this.getExactExcelData();return;}// 如果解析失败，直接使用准确的Excel数据\nthis.data=this.getExactExcelData();}// 获取完全按照Excel内容的准确数据（包含正确的合并单元格结构）\ngetExactExcelData(){return{keyIndicators:[{序号:1,指标:'产品线毛利率',目标值:'橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',权重:6,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标'},{序号:2,指标:'开发降本（万元）',目标值:'≥5000',权重:8,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'责任状指标',责任人:''},{序号:3,指标:'工艺模块及时率（橡胶金属、空气弹簧、系统件等）',目标值:'满足模块条件产品转批及时率≥95%',权重:3,考核标准:'完成目标得满分，未完成按比例得分。制造中心计划供应部提供数据作为考核',指标分类:'世界一流指标',责任人:'丁间浩'},{序号:4,指标:'研发可控费用管控',目标值:'≤5501万元',权重:3,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标'},{序号:5,指标:'新产品开发一次命中率',目标值:'≥90%',权重:2,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'世界一流指标',责任人:'产管中心数据'},{序号:6,指标:'推荐选型数量（个）',目标值:'≥90',权重:2,考核标准:'以项目激励为准，完成目标得满分，未完成按比例得分。',指标分类:'世界一流指标',责任人:'产管中心数据'},{序号:7,指标:'新产品开发周期缩短',目标值:'≥15%',权重:2,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'世界一流指标',责任人:'产管中心'},// 归口管理指标 - 完全按照Excel结构\n{// 第1行 \"归口管理指标\"\n序号:8,指标:'归口管理指标',目标值:'专利指标，专项下达',// Excel中这一行的目标值\n权重:2,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标',rowSpan:6,// \"序号\"和\"指标\"列向下合并6行\nisMergedStart:true},{// 第2行 \"归口管理指标\"\n序号:8,// 逻辑上合并，渲染时跳过\n指标:'归口管理指标',// 逻辑上合并，渲染时跳过\n目标值:'部门编制（专项下达）',// Excel中这一行的目标值\n权重:3,考核标准:'完成目标得满分，未完成按比例得分。',// 注意：Excel中此处为\"満\"\n指标分类:'归口指标',责任人:'归口指标',isMergedCell:true},{// 第3行 \"归口管理指标\"\n序号:8,指标:'归口管理指标',目标值:'精益善人均≥2条',权重:1,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标',isMergedCell:true},{// 第4行 \"归口管理指标\"\n序号:8,指标:'归口管理指标',目标值:'落实\"多位一体\"大监管机制要求，开展管理监管≥1项',权重:2,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标',isMergedCell:true},{// 第5行 \"归口管理指标\"\n序号:8,指标:'归口管理指标',目标值:'巡视整改完成率按要求进度完成率 100%',权重:1,考核标准:'完成目标得满分，未完成目标按整改情况扣分。',指标分类:'归口指标',责任人:'',isMergedCell:true},{// 第6行 \"归口管理指标\"\n序号:8,指标:'归口管理指标',目标值:'全面预算管控：\\n1.可控费用控制在5501万内；（3分）\\n2.全面预算工作推进及部门预算管理优化；（2分）',权重:5,考核标准:'完成目标得满分，未完成按比例得分。',指标分类:'归口指标',责任人:'归口指标',isMergedCell:true}],qualityIndicators:[// 根据Excel，质量指标部分暂无数据\n],keyWork:[// 第1行：合并单元格的起始行（序号1）\n{序号:1,指标:'新产品、新平台、新技术开发',目标值:'车轮降噪头：实现批量交付。\\n贝通道：具备贝通道产品批量生产和质量管控能力，获得一家客户贝通道供货资质。\\n低感回收再制造技术的研制：①完成一种复合体件回收技术及其工艺体系的搭建（回收成本在金属采购价格25%以下）；②通过至少2种产品的批量化应用建立回收类型数据库。',权重:'',// 这一行权重为空\n考核标准:'',// 这一行考核标准为空  \n指标分类:'',// 这一行指标分类为空\n责任人:'开发中心-技术与发展研究室',rowSpan:2,// \"序号\"和\"指标\"列向下合并2行\nisMergedStart:true},// 第2行：合并单元格的第二行\n{序号:1,// 逻辑上合并，渲染时跳过\n指标:'新产品、新平台、新技术开发',// 逻辑上合并，渲染时跳过\n目标值:'新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。\\n橡胶制料：',权重:'',// 这一行权重为空\n考核标准:'',// 这一行考核标准为空\n指标分类:'',// 这一行指标分类为空\n责任人:'开发中心-车体新材料',isMergedCell:true},{序号:2,指标:'新产品、新平台、新技术开发',目标值:'新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-技术与发展研究室'},{序号:3,指标:'新产品、新平台、新技术开发',目标值:'车体新材料：\\n1.碳纤维复合材料产品完成自主能力建设及产业初步建立\\n2.完成复合材料货车材料验证及方案设计，产品研制及装车。',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-车体新材料'},{序号:4,指标:'新产品、新平台、新技术开发',目标值:'TPE车轮缓冲器具备批量自主能力，获得三家以上客户批量订单。',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-系统部'},{序号:5,指标:'新产品、新平台、新技术开发',目标值:'橡胶件产品有效满足12年或者15%不到年限：\\n完成满法规的稳定性验证（完成次级验证试验，目试验结果明当）、固化满法规的新高工艺参数。',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-橡胶件总属部'},{序号:6,指标:'新产品、新平台、新技术开发',目标值:'EN45545 H3阻燃橡胶件研究：\\n1.完成沙箱高HL3阻燃胶料产品的型式试验验证\\n2.完成H3双层级雅形消管的工艺研究及型式试验验证',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-橡胶件总属部'},{序号:7,指标:'新产品、新平台、新技术开发',目标值:'仿真技术：①采用公司高端管的超弹性平台，形成自主、快速、标准的仿真数据试与性能验证技术体系；②基于uniqu软件进行二次开发，完成一类典型橡胶减振产品自动网格划分和处理功能且具有可操作性的软件界面。',权重:'',// 权重为空\n考核标准:'',// 考核标准为空\n指标分类:'',// 指标分类为空\n责任人:'开发中心-仿真研究部'}]};}// 获取指定部分的数据\ngetSectionData(section){return this.data[section]||[];}// 更新数据 - 实现双向同步\nasync updateData(section,index,field,value){if(this.data[section]&&this.data[section][index]){// 更新内存中的数据\nthis.data[section][index][field]=value;console.log('数据已更新:',{section,index,field,value});// 同步到Excel文件 - 使用新的单字段更新API\nawait this.syncToExcel(section,index,field,value);}}// 同步数据到Excel文件 - 双向同步机制（参考模块二的逻辑）\nasync syncToExcel(section,index,field,value){try{console.log('正在同步工作目标数据到Excel:',{section,index,field,value});// 调用新的单字段更新API\nconst response=await fetch('http://localhost:3001/api/update-worktarget-excel',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({section,rowIndex:index,field,value})});if(response.ok){const result=await response.json();if(result.success){console.log('工作目标数据同步成功');this.onSyncSuccess&&this.onSyncSuccess();return true;}else{console.error('工作目标数据同步失败:',result.message);this.onSyncError&&this.onSyncError(result.message);return false;}}else{console.error('工作目标数据同步请求失败，状态码:',response.status);this.onSyncError&&this.onSyncError(\"HTTP \".concat(response.status));return false;}}catch(error){console.error('工作目标数据同步错误:',error);this.onSyncError&&this.onSyncError(error.message);return false;}}// 将内存数据转换为Excel格式\nconvertDataForExcel(section){const sectionData=this.data[section]||[];return sectionData.map((row,index)=>_objectSpread(_objectSpread({},row),{},{// 添加详情字段用于处理合并单元格\n序号_详情:row.isMergedStart?row.序号:row.isMergedCell?'':row.序号,指标_详情:row.isMergedStart?row.指标:row.isMergedCell?'':row.指标,目标值_详情:row.目标值,权重_详情:row.权重,考核标准_详情:row.考核标准,指标分类_详情:row.指标分类,责任人_详情:row.责任人,// 保留合并信息\nisMergedStart_序号:row.isMergedStart&&row.rowSpan>1,rowSpan_序号:row.rowSpan||1,isMergedStart_指标:row.isMergedStart&&row.rowSpan>1,rowSpan_指标:row.rowSpan||1}));}// 设置同步回调\nsetSyncCallbacks(onSuccess,onError){this.onSyncSuccess=onSuccess;this.onSyncError=onError;}// 获取数据统计信息\ngetDataStats(){var _this$data$keyIndicat,_this$data$qualityInd,_this$data$keyWork,_this$data$keyIndicat2,_this$data$qualityInd2,_this$data$keyWork2;const getWeight=item=>{const weight=item.权重;return typeof weight==='number'?weight:weight?Number(weight)||0:0;};return{keyIndicators:((_this$data$keyIndicat=this.data.keyIndicators)===null||_this$data$keyIndicat===void 0?void 0:_this$data$keyIndicat.length)||0,qualityIndicators:((_this$data$qualityInd=this.data.qualityIndicators)===null||_this$data$qualityInd===void 0?void 0:_this$data$qualityInd.length)||0,keyWork:((_this$data$keyWork=this.data.keyWork)===null||_this$data$keyWork===void 0?void 0:_this$data$keyWork.length)||0,totalWeight:{keyIndicators:((_this$data$keyIndicat2=this.data.keyIndicators)===null||_this$data$keyIndicat2===void 0?void 0:_this$data$keyIndicat2.reduce((sum,item)=>sum+getWeight(item),0))||0,qualityIndicators:((_this$data$qualityInd2=this.data.qualityIndicators)===null||_this$data$qualityInd2===void 0?void 0:_this$data$qualityInd2.reduce((sum,item)=>sum+getWeight(item),0))||0,keyWork:((_this$data$keyWork2=this.data.keyWork)===null||_this$data$keyWork2===void 0?void 0:_this$data$keyWork2.reduce((sum,item)=>sum+getWeight(item),0))||0}};}}export default new ExcelService();", "map": {"version": 3, "names": ["XLSX", "ExcelService", "constructor", "workbook", "data", "loadExcel", "console", "log", "response", "fetch", "ok", "result", "json", "keyWork", "length", "lastModified", "Date", "warn", "status", "error", "getExactExcelData", "parseWorkTargetData", "sheetName", "sheet", "Sheets", "keyIndicators", "序号", "指标", "目标值", "权重", "考核标准", "指标分类", "责任人", "rowSpan", "isMergedStart", "isMergedCell", "qualityIndicators", "getSectionData", "section", "updateData", "index", "field", "value", "syncToExcel", "method", "headers", "body", "JSON", "stringify", "rowIndex", "success", "onSyncSuccess", "message", "onSyncError", "concat", "convertDataForExcel", "sectionData", "map", "row", "_objectSpread", "序号_详情", "指标_详情", "目标值_详情", "权重_详情", "考核标准_详情", "指标分类_详情", "责任人_详情", "isMergedStart_序号", "rowSpan_序号", "isMergedStart_指标", "rowSpan_指标", "setSyncCallbacks", "onSuccess", "onError", "getDataStats", "_this$data$keyIndicat", "_this$data$qualityInd", "_this$data$keyWork", "_this$data$keyIndicat2", "_this$data$qualityInd2", "_this$data$keyWork2", "getWeight", "item", "weight", "Number", "totalWeight", "reduce", "sum"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块一/services/excelService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\n\nclass ExcelService {\n  constructor() {\n    this.workbook = null;\n    this.data = {};\n  }\n\n  // 加载Excel文件\n  async loadExcel() {\n    try {\n      // 首先尝试从后端API加载真实数据\n      console.log('正在从API加载Excel数据...');\n      const response = await fetch('http://localhost:3001/api/excel-data');\n      \n      if (response.ok) {\n        const result = await response.json();\n        if (result.data && result.data.keyWork && result.data.keyWork.length > 0) {\n          // 只有当API返回的数据包含完整的keyWork数据时才使用\n          this.data = result.data;\n          this.lastModified = new Date(result.lastModified);\n          console.log('从Excel文件成功加载数据:', this.data);\n          return this.data;\n        } else {\n          console.warn('API返回的数据不完整，缺少重点工作数据');\n        }\n      } else {\n        console.warn('API响应失败，状态码:', response.status);\n      }\n    } catch (error) {\n      console.error('从API加载Excel数据失败:', error);\n    }\n\n    // 如果API失败或数据不完整，使用本地静态数据\n    console.log('使用本地静态数据');\n    this.data = this.getExactExcelData();\n    return this.data;\n  }\n\n  // 解析工作目标管理责任书数据\n  parseWorkTargetData() {\n    const sheetName = '3.开发中心';\n    const sheet = this.workbook.Sheets[sheetName];\n    \n    if (!sheet) {\n      console.error('找不到工作表:', sheetName);\n      this.data = this.getExactExcelData();\n      return;\n    }\n\n    // 如果解析失败，直接使用准确的Excel数据\n    this.data = this.getExactExcelData();\n  }\n\n  // 获取完全按照Excel内容的准确数据（包含正确的合并单元格结构）\n  getExactExcelData() {\n    return {\n      keyIndicators: [\n        {\n          序号: 1,\n          指标: '产品线毛利率',\n          目标值: '橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',\n          权重: 6,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '归口指标',\n          责任人: '归口指标'\n        },\n        {\n          序号: 2,\n          指标: '开发降本（万元）',\n          目标值: '≥5000',\n          权重: 8,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '责任状指标',\n          责任人: ''\n        },\n        {\n          序号: 3,\n          指标: '工艺模块及时率（橡胶金属、空气弹簧、系统件等）',\n          目标值: '满足模块条件产品转批及时率≥95%',\n          权重: 3,\n          考核标准: '完成目标得满分，未完成按比例得分。制造中心计划供应部提供数据作为考核',\n          指标分类: '世界一流指标',\n          责任人: '丁间浩'\n        },\n        {\n          序号: 4,\n          指标: '研发可控费用管控',\n          目标值: '≤5501万元',\n          权重: 3,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '归口指标',\n          责任人: '归口指标'\n        },\n        {\n          序号: 5,\n          指标: '新产品开发一次命中率',\n          目标值: '≥90%',\n          权重: 2,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '世界一流指标',\n          责任人: '产管中心数据'\n        },\n        {\n          序号: 6,\n          指标: '推荐选型数量（个）',\n          目标值: '≥90',\n          权重: 2,\n          考核标准: '以项目激励为准，完成目标得满分，未完成按比例得分。',\n          指标分类: '世界一流指标',\n          责任人: '产管中心数据'\n        },\n        {\n          序号: 7,\n          指标: '新产品开发周期缩短',\n          目标值: '≥15%',\n          权重: 2,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '世界一流指标',\n          责任人: '产管中心'\n        },\n        // 归口管理指标 - 完全按照Excel结构\n        { // 第1行 \"归口管理指标\"\n          序号: 8,\n          指标: '归口管理指标',\n          目标值: '专利指标，专项下达', // Excel中这一行的目标值\n          权重: 2,\n          考核标准: '完成目标得满分，未完成按比例得分。',\n          指标分类: '归口指标',\n          责任人: '归口指标',\n          rowSpan: 6, // \"序号\"和\"指标\"列向下合并6行\n          isMergedStart: true\n        },\n        { // 第2行 \"归口管理指标\"\n          序号: 8, // 逻辑上合并，渲染时跳过\n          指标: '归口管理指标', // 逻辑上合并，渲染时跳过\n          目标值: '部门编制（专项下达）', // Excel中这一行的目标值\n          权重: 3,\n          考核标准: '完成目标得满分，未完成按比例得分。', // 注意：Excel中此处为\"満\"\n          指标分类: '归口指标',\n          责任人: '归口指标',\n          isMergedCell: true\n        },\n        { // 第3行 \"归口管理指标\"\n          序号: 8,指标: '归口管理指标',目标值: '精益善人均≥2条',权重: 1,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true\n        },\n        { // 第4行 \"归口管理指标\"\n          序号: 8,指标: '归口管理指标',目标值: '落实\"多位一体\"大监管机制要求，开展管理监管≥1项',权重: 2,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true\n        },\n        { // 第5行 \"归口管理指标\"\n          序号: 8,指标: '归口管理指标',目标值: '巡视整改完成率按要求进度完成率 100%',权重: 1,考核标准: '完成目标得满分，未完成目标按整改情况扣分。',指标分类: '归口指标',责任人: '',isMergedCell: true\n        },\n        { // 第6行 \"归口管理指标\"\n          序号: 8,指标: '归口管理指标',目标值: '全面预算管控：\\n1.可控费用控制在5501万内；（3分）\\n2.全面预算工作推进及部门预算管理优化；（2分）',权重: 5,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true\n        }\n      ],\n      qualityIndicators: [\n        // 根据Excel，质量指标部分暂无数据\n      ],\n      keyWork: [\n        // 第1行：合并单元格的起始行（序号1）\n        {\n          序号: 1,\n          指标: '新产品、新平台、新技术开发',\n          目标值: '车轮降噪头：实现批量交付。\\n贝通道：具备贝通道产品批量生产和质量管控能力，获得一家客户贝通道供货资质。\\n低感回收再制造技术的研制：①完成一种复合体件回收技术及其工艺体系的搭建（回收成本在金属采购价格25%以下）；②通过至少2种产品的批量化应用建立回收类型数据库。',\n          权重: '', // 这一行权重为空\n          考核标准: '', // 这一行考核标准为空  \n          指标分类: '', // 这一行指标分类为空\n          责任人: '开发中心-技术与发展研究室',\n          rowSpan: 2, // \"序号\"和\"指标\"列向下合并2行\n          isMergedStart: true\n        },\n        // 第2行：合并单元格的第二行\n        {\n          序号: 1, // 逻辑上合并，渲染时跳过\n          指标: '新产品、新平台、新技术开发', // 逻辑上合并，渲染时跳过\n          目标值: '新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。\\n橡胶制料：',\n          权重: '', // 这一行权重为空\n          考核标准: '', // 这一行考核标准为空\n          指标分类: '', // 这一行指标分类为空\n          责任人: '开发中心-车体新材料',\n          isMergedCell: true\n        },\n        {\n          序号: 2,\n          指标: '新产品、新平台、新技术开发',\n          目标值: '新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-技术与发展研究室'\n        },\n        {\n          序号: 3,\n          指标: '新产品、新平台、新技术开发',\n          目标值: '车体新材料：\\n1.碳纤维复合材料产品完成自主能力建设及产业初步建立\\n2.完成复合材料货车材料验证及方案设计，产品研制及装车。',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-车体新材料'\n        },\n        {\n          序号: 4,\n          指标: '新产品、新平台、新技术开发',\n          目标值: 'TPE车轮缓冲器具备批量自主能力，获得三家以上客户批量订单。',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-系统部'\n        },\n        {\n          序号: 5,\n          指标: '新产品、新平台、新技术开发',\n          目标值: '橡胶件产品有效满足12年或者15%不到年限：\\n完成满法规的稳定性验证（完成次级验证试验，目试验结果明当）、固化满法规的新高工艺参数。',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-橡胶件总属部'\n        },\n        {\n          序号: 6,\n          指标: '新产品、新平台、新技术开发',\n          目标值: 'EN45545 H3阻燃橡胶件研究：\\n1.完成沙箱高HL3阻燃胶料产品的型式试验验证\\n2.完成H3双层级雅形消管的工艺研究及型式试验验证',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-橡胶件总属部'\n        },\n        {\n          序号: 7,\n          指标: '新产品、新平台、新技术开发',\n          目标值: '仿真技术：①采用公司高端管的超弹性平台，形成自主、快速、标准的仿真数据试与性能验证技术体系；②基于uniqu软件进行二次开发，完成一类典型橡胶减振产品自动网格划分和处理功能且具有可操作性的软件界面。',\n          权重: '', // 权重为空\n          考核标准: '', // 考核标准为空\n          指标分类: '', // 指标分类为空\n          责任人: '开发中心-仿真研究部'\n        }\n      ]\n    };\n  }\n\n  // 获取指定部分的数据\n  getSectionData(section) {\n    return this.data[section] || [];\n  }\n\n  // 更新数据 - 实现双向同步\n  async updateData(section, index, field, value) {\n    if (this.data[section] && this.data[section][index]) {\n      // 更新内存中的数据\n      this.data[section][index][field] = value;\n      console.log('数据已更新:', { section, index, field, value });\n      \n      // 同步到Excel文件 - 使用新的单字段更新API\n      await this.syncToExcel(section, index, field, value);\n    }\n  }\n\n  // 同步数据到Excel文件 - 双向同步机制（参考模块二的逻辑）\n  async syncToExcel(section, index, field, value) {\n    try {\n      console.log('正在同步工作目标数据到Excel:', { section, index, field, value });\n      \n      // 调用新的单字段更新API\n      const response = await fetch('http://localhost:3001/api/update-worktarget-excel', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          section,\n          rowIndex: index,\n          field,\n          value\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success) {\n          console.log('工作目标数据同步成功');\n          this.onSyncSuccess && this.onSyncSuccess();\n          return true;\n        } else {\n          console.error('工作目标数据同步失败:', result.message);\n          this.onSyncError && this.onSyncError(result.message);\n          return false;\n        }\n      } else {\n        console.error('工作目标数据同步请求失败，状态码:', response.status);\n        this.onSyncError && this.onSyncError(`HTTP ${response.status}`);\n        return false;\n      }\n    } catch (error) {\n      console.error('工作目标数据同步错误:', error);\n      this.onSyncError && this.onSyncError(error.message);\n      return false;\n    }\n  }\n\n  // 将内存数据转换为Excel格式\n  convertDataForExcel(section) {\n    const sectionData = this.data[section] || [];\n    return sectionData.map((row, index) => ({\n      ...row,\n      // 添加详情字段用于处理合并单元格\n      序号_详情: row.isMergedStart ? row.序号 : (row.isMergedCell ? '' : row.序号),\n      指标_详情: row.isMergedStart ? row.指标 : (row.isMergedCell ? '' : row.指标),\n      目标值_详情: row.目标值,\n      权重_详情: row.权重,\n      考核标准_详情: row.考核标准,\n      指标分类_详情: row.指标分类,\n      责任人_详情: row.责任人,\n      // 保留合并信息\n      isMergedStart_序号: row.isMergedStart && (row.rowSpan > 1),\n      rowSpan_序号: row.rowSpan || 1,\n      isMergedStart_指标: row.isMergedStart && (row.rowSpan > 1),\n      rowSpan_指标: row.rowSpan || 1\n    }));\n  }\n\n  // 设置同步回调\n  setSyncCallbacks(onSuccess, onError) {\n    this.onSyncSuccess = onSuccess;\n    this.onSyncError = onError;\n  }\n\n  // 获取数据统计信息\n  getDataStats() {\n    const getWeight = (item) => {\n      const weight = item.权重;\n      return typeof weight === 'number' ? weight : (weight ? Number(weight) || 0 : 0);\n    };\n\n    return {\n      keyIndicators: this.data.keyIndicators?.length || 0,\n      qualityIndicators: this.data.qualityIndicators?.length || 0,\n      keyWork: this.data.keyWork?.length || 0,\n      totalWeight: {\n        keyIndicators: this.data.keyIndicators?.reduce((sum, item) => sum + getWeight(item), 0) || 0,\n        qualityIndicators: this.data.qualityIndicators?.reduce((sum, item) => sum + getWeight(item), 0) || 0,\n        keyWork: this.data.keyWork?.reduce((sum, item) => sum + getWeight(item), 0) || 0\n      }\n    };\n  }\n}\n\nexport default new ExcelService(); "], "mappings": "+MAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,YAAa,CACjBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,IAAI,CAAG,CAAC,CAAC,CAChB,CAEA;AACA,KAAM,CAAAC,SAASA,CAAA,CAAG,CAChB,GAAI,CACF;AACAC,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC,CACjC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,sCAAsC,CAAC,CAEpE,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACP,IAAI,EAAIO,MAAM,CAACP,IAAI,CAACS,OAAO,EAAIF,MAAM,CAACP,IAAI,CAACS,OAAO,CAACC,MAAM,CAAG,CAAC,CAAE,CACxE;AACA,IAAI,CAACV,IAAI,CAAGO,MAAM,CAACP,IAAI,CACvB,IAAI,CAACW,YAAY,CAAG,GAAI,CAAAC,IAAI,CAACL,MAAM,CAACI,YAAY,CAAC,CACjDT,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE,IAAI,CAACH,IAAI,CAAC,CACzC,MAAO,KAAI,CAACA,IAAI,CAClB,CAAC,IAAM,CACLE,OAAO,CAACW,IAAI,CAAC,sBAAsB,CAAC,CACtC,CACF,CAAC,IAAM,CACLX,OAAO,CAACW,IAAI,CAAC,cAAc,CAAET,QAAQ,CAACU,MAAM,CAAC,CAC/C,CACF,CAAE,MAAOC,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CAC1C,CAEA;AACAb,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvB,IAAI,CAACH,IAAI,CAAG,IAAI,CAACgB,iBAAiB,CAAC,CAAC,CACpC,MAAO,KAAI,CAAChB,IAAI,CAClB,CAEA;AACAiB,mBAAmBA,CAAA,CAAG,CACpB,KAAM,CAAAC,SAAS,CAAG,QAAQ,CAC1B,KAAM,CAAAC,KAAK,CAAG,IAAI,CAACpB,QAAQ,CAACqB,MAAM,CAACF,SAAS,CAAC,CAE7C,GAAI,CAACC,KAAK,CAAE,CACVjB,OAAO,CAACa,KAAK,CAAC,SAAS,CAAEG,SAAS,CAAC,CACnC,IAAI,CAAClB,IAAI,CAAG,IAAI,CAACgB,iBAAiB,CAAC,CAAC,CACpC,OACF,CAEA;AACA,IAAI,CAAChB,IAAI,CAAG,IAAI,CAACgB,iBAAiB,CAAC,CAAC,CACtC,CAEA;AACAA,iBAAiBA,CAAA,CAAG,CAClB,MAAO,CACLK,aAAa,CAAE,CACb,CACEC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,QAAQ,CACZC,GAAG,CAAE,8EAA8E,CACnFC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,MACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,UAAU,CACdC,GAAG,CAAE,OAAO,CACZC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,OAAO,CACbC,GAAG,CAAE,EACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,yBAAyB,CAC7BC,GAAG,CAAE,mBAAmB,CACxBC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,oCAAoC,CAC1CC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,KACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,UAAU,CACdC,GAAG,CAAE,SAAS,CACdC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,MACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,YAAY,CAChBC,GAAG,CAAE,MAAM,CACXC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,QACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,WAAW,CACfC,GAAG,CAAE,KAAK,CACVC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,2BAA2B,CACjCC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,QACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,WAAW,CACfC,GAAG,CAAE,MAAM,CACXC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,QAAQ,CACdC,GAAG,CAAE,MACP,CAAC,CACD;AACA,CAAE;AACAN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,QAAQ,CACZC,GAAG,CAAE,WAAW,CAAE;AAClBC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CACzBC,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,MAAM,CACXC,OAAO,CAAE,CAAC,CAAE;AACZC,aAAa,CAAE,IACjB,CAAC,CACD,CAAE;AACAR,EAAE,CAAE,CAAC,CAAE;AACPC,EAAE,CAAE,QAAQ,CAAE;AACdC,GAAG,CAAE,YAAY,CAAE;AACnBC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,mBAAmB,CAAE;AAC3BC,IAAI,CAAE,MAAM,CACZC,GAAG,CAAE,MAAM,CACXG,YAAY,CAAE,IAChB,CAAC,CACD,CAAE;AACAT,EAAE,CAAE,CAAC,CAACC,EAAE,CAAE,QAAQ,CAACC,GAAG,CAAE,UAAU,CAACC,EAAE,CAAE,CAAC,CAACC,IAAI,CAAE,mBAAmB,CAACC,IAAI,CAAE,MAAM,CAACC,GAAG,CAAE,MAAM,CAACG,YAAY,CAAE,IAC5G,CAAC,CACD,CAAE;AACAT,EAAE,CAAE,CAAC,CAACC,EAAE,CAAE,QAAQ,CAACC,GAAG,CAAE,2BAA2B,CAACC,EAAE,CAAE,CAAC,CAACC,IAAI,CAAE,mBAAmB,CAACC,IAAI,CAAE,MAAM,CAACC,GAAG,CAAE,MAAM,CAACG,YAAY,CAAE,IAC7H,CAAC,CACD,CAAE;AACAT,EAAE,CAAE,CAAC,CAACC,EAAE,CAAE,QAAQ,CAACC,GAAG,CAAE,sBAAsB,CAACC,EAAE,CAAE,CAAC,CAACC,IAAI,CAAE,uBAAuB,CAACC,IAAI,CAAE,MAAM,CAACC,GAAG,CAAE,EAAE,CAACG,YAAY,CAAE,IACxH,CAAC,CACD,CAAE;AACAT,EAAE,CAAE,CAAC,CAACC,EAAE,CAAE,QAAQ,CAACC,GAAG,CAAE,yDAAyD,CAACC,EAAE,CAAE,CAAC,CAACC,IAAI,CAAE,mBAAmB,CAACC,IAAI,CAAE,MAAM,CAACC,GAAG,CAAE,MAAM,CAACG,YAAY,CAAE,IAC3J,CAAC,CACF,CACDC,iBAAiB,CAAE,CACjB;AAAA,CACD,CACDvB,OAAO,CAAE,CACP;AACA,CACEa,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,uIAAuI,CAC5IC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,eAAe,CACpBC,OAAO,CAAE,CAAC,CAAE;AACZC,aAAa,CAAE,IACjB,CAAC,CACD;AACA,CACER,EAAE,CAAE,CAAC,CAAE;AACPC,EAAE,CAAE,eAAe,CAAE;AACrBC,GAAG,CAAE,sCAAsC,CAC3CC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,YAAY,CACjBG,YAAY,CAAE,IAChB,CAAC,CACD,CACET,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,+BAA+B,CACpCC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,eACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,kEAAkE,CACvEC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,YACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,gCAAgC,CACrCC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,UACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,qEAAqE,CAC1EC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,aACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,wEAAwE,CAC7EC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,aACP,CAAC,CACD,CACEN,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,eAAe,CACnBC,GAAG,CAAE,qGAAqG,CAC1GC,EAAE,CAAE,EAAE,CAAE;AACRC,IAAI,CAAE,EAAE,CAAE;AACVC,IAAI,CAAE,EAAE,CAAE;AACVC,GAAG,CAAE,YACP,CAAC,CAEL,CAAC,CACH,CAEA;AACAK,cAAcA,CAACC,OAAO,CAAE,CACtB,MAAO,KAAI,CAAClC,IAAI,CAACkC,OAAO,CAAC,EAAI,EAAE,CACjC,CAEA;AACA,KAAM,CAAAC,UAAUA,CAACD,OAAO,CAAEE,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAE,CAC7C,GAAI,IAAI,CAACtC,IAAI,CAACkC,OAAO,CAAC,EAAI,IAAI,CAAClC,IAAI,CAACkC,OAAO,CAAC,CAACE,KAAK,CAAC,CAAE,CACnD;AACA,IAAI,CAACpC,IAAI,CAACkC,OAAO,CAAC,CAACE,KAAK,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CACxCpC,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAE,CAAE+B,OAAO,CAAEE,KAAK,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAC,CAEvD;AACA,KAAM,KAAI,CAACC,WAAW,CAACL,OAAO,CAAEE,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAC,CACtD,CACF,CAEA;AACA,KAAM,CAAAC,WAAWA,CAACL,OAAO,CAAEE,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAE,CAC9C,GAAI,CACFpC,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE,CAAE+B,OAAO,CAAEE,KAAK,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAC,CAElE;AACA,KAAM,CAAAlC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mDAAmD,CAAE,CAChFmC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBV,OAAO,CACPW,QAAQ,CAAET,KAAK,CACfC,KAAK,CACLC,KACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAIlC,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACuC,OAAO,CAAE,CAClB5C,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CACzB,IAAI,CAAC4C,aAAa,EAAI,IAAI,CAACA,aAAa,CAAC,CAAC,CAC1C,MAAO,KAAI,CACb,CAAC,IAAM,CACL7C,OAAO,CAACa,KAAK,CAAC,aAAa,CAAER,MAAM,CAACyC,OAAO,CAAC,CAC5C,IAAI,CAACC,WAAW,EAAI,IAAI,CAACA,WAAW,CAAC1C,MAAM,CAACyC,OAAO,CAAC,CACpD,MAAO,MAAK,CACd,CACF,CAAC,IAAM,CACL9C,OAAO,CAACa,KAAK,CAAC,mBAAmB,CAAEX,QAAQ,CAACU,MAAM,CAAC,CACnD,IAAI,CAACmC,WAAW,EAAI,IAAI,CAACA,WAAW,SAAAC,MAAA,CAAS9C,QAAQ,CAACU,MAAM,CAAE,CAAC,CAC/D,MAAO,MAAK,CACd,CACF,CAAE,MAAOC,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,IAAI,CAACkC,WAAW,EAAI,IAAI,CAACA,WAAW,CAAClC,KAAK,CAACiC,OAAO,CAAC,CACnD,MAAO,MAAK,CACd,CACF,CAEA;AACAG,mBAAmBA,CAACjB,OAAO,CAAE,CAC3B,KAAM,CAAAkB,WAAW,CAAG,IAAI,CAACpD,IAAI,CAACkC,OAAO,CAAC,EAAI,EAAE,CAC5C,MAAO,CAAAkB,WAAW,CAACC,GAAG,CAAC,CAACC,GAAG,CAAElB,KAAK,GAAAmB,aAAA,CAAAA,aAAA,IAC7BD,GAAG,MACN;AACAE,KAAK,CAAEF,GAAG,CAACxB,aAAa,CAAGwB,GAAG,CAAChC,EAAE,CAAIgC,GAAG,CAACvB,YAAY,CAAG,EAAE,CAAGuB,GAAG,CAAChC,EAAG,CACpEmC,KAAK,CAAEH,GAAG,CAACxB,aAAa,CAAGwB,GAAG,CAAC/B,EAAE,CAAI+B,GAAG,CAACvB,YAAY,CAAG,EAAE,CAAGuB,GAAG,CAAC/B,EAAG,CACpEmC,MAAM,CAAEJ,GAAG,CAAC9B,GAAG,CACfmC,KAAK,CAAEL,GAAG,CAAC7B,EAAE,CACbmC,OAAO,CAAEN,GAAG,CAAC5B,IAAI,CACjBmC,OAAO,CAAEP,GAAG,CAAC3B,IAAI,CACjBmC,MAAM,CAAER,GAAG,CAAC1B,GAAG,CACf;AACAmC,gBAAgB,CAAET,GAAG,CAACxB,aAAa,EAAKwB,GAAG,CAACzB,OAAO,CAAG,CAAE,CACxDmC,UAAU,CAAEV,GAAG,CAACzB,OAAO,EAAI,CAAC,CAC5BoC,gBAAgB,CAAEX,GAAG,CAACxB,aAAa,EAAKwB,GAAG,CAACzB,OAAO,CAAG,CAAE,CACxDqC,UAAU,CAAEZ,GAAG,CAACzB,OAAO,EAAI,CAAC,EAC5B,CAAC,CACL,CAEA;AACAsC,gBAAgBA,CAACC,SAAS,CAAEC,OAAO,CAAE,CACnC,IAAI,CAACtB,aAAa,CAAGqB,SAAS,CAC9B,IAAI,CAACnB,WAAW,CAAGoB,OAAO,CAC5B,CAEA;AACAC,YAAYA,CAAA,CAAG,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,kBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,mBAAA,CACb,KAAM,CAAAC,SAAS,CAAIC,IAAI,EAAK,CAC1B,KAAM,CAAAC,MAAM,CAAGD,IAAI,CAACrD,EAAE,CACtB,MAAO,OAAO,CAAAsD,MAAM,GAAK,QAAQ,CAAGA,MAAM,CAAIA,MAAM,CAAGC,MAAM,CAACD,MAAM,CAAC,EAAI,CAAC,CAAG,CAAE,CACjF,CAAC,CAED,MAAO,CACL1D,aAAa,CAAE,EAAAkD,qBAAA,KAAI,CAACvE,IAAI,CAACqB,aAAa,UAAAkD,qBAAA,iBAAvBA,qBAAA,CAAyB7D,MAAM,GAAI,CAAC,CACnDsB,iBAAiB,CAAE,EAAAwC,qBAAA,KAAI,CAACxE,IAAI,CAACgC,iBAAiB,UAAAwC,qBAAA,iBAA3BA,qBAAA,CAA6B9D,MAAM,GAAI,CAAC,CAC3DD,OAAO,CAAE,EAAAgE,kBAAA,KAAI,CAACzE,IAAI,CAACS,OAAO,UAAAgE,kBAAA,iBAAjBA,kBAAA,CAAmB/D,MAAM,GAAI,CAAC,CACvCuE,WAAW,CAAE,CACX5D,aAAa,CAAE,EAAAqD,sBAAA,KAAI,CAAC1E,IAAI,CAACqB,aAAa,UAAAqD,sBAAA,iBAAvBA,sBAAA,CAAyBQ,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGN,SAAS,CAACC,IAAI,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CAC5F9C,iBAAiB,CAAE,EAAA2C,sBAAA,KAAI,CAAC3E,IAAI,CAACgC,iBAAiB,UAAA2C,sBAAA,iBAA3BA,sBAAA,CAA6BO,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGN,SAAS,CAACC,IAAI,CAAC,CAAE,CAAC,CAAC,GAAI,CAAC,CACpGrE,OAAO,CAAE,EAAAmE,mBAAA,KAAI,CAAC5E,IAAI,CAACS,OAAO,UAAAmE,mBAAA,iBAAjBA,mBAAA,CAAmBM,MAAM,CAAC,CAACC,GAAG,CAAEL,IAAI,GAAKK,GAAG,CAAGN,SAAS,CAACC,IAAI,CAAC,CAAE,CAAC,CAAC,GAAI,CACjF,CACF,CAAC,CACH,CACF,CAEA,cAAe,IAAI,CAAAjF,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}