{"ast": null, "code": "/*!\n * chartjs-plugin-datalabels v2.2.0\n * https://chartjs-plugin-datalabels.netlify.app\n * (c) 2017-2022 chartjs-plugin-datalabels contributors\n * Released under the MIT license\n */\nimport { isNullOrUndef, merge, toFont, resolve, toPadding, valueOrDefault, callback, isObject, each } from 'chart.js/helpers';\nimport { defaults as defaults$1, ArcElement, PointElement, BarElement } from 'chart.js';\nvar devicePixelRatio = function () {\n  if (typeof window !== 'undefined') {\n    if (window.devicePixelRatio) {\n      return window.devicePixelRatio;\n    }\n\n    // devicePixelRatio is undefined on IE10\n    // https://stackoverflow.com/a/20204180/8837887\n    // https://github.com/chartjs/chartjs-plugin-datalabels/issues/85\n    var screen = window.screen;\n    if (screen) {\n      return (screen.deviceXDPI || 1) / (screen.logicalXDPI || 1);\n    }\n  }\n  return 1;\n}();\nvar utils = {\n  // @todo move this in Chart.helpers.toTextLines\n  toTextLines: function (inputs) {\n    var lines = [];\n    var input;\n    inputs = [].concat(inputs);\n    while (inputs.length) {\n      input = inputs.pop();\n      if (typeof input === 'string') {\n        lines.unshift.apply(lines, input.split('\\n'));\n      } else if (Array.isArray(input)) {\n        inputs.push.apply(inputs, input);\n      } else if (!isNullOrUndef(inputs)) {\n        lines.unshift('' + input);\n      }\n    }\n    return lines;\n  },\n  // @todo move this in Chart.helpers.canvas.textSize\n  // @todo cache calls of measureText if font doesn't change?!\n  textSize: function (ctx, lines, font) {\n    var items = [].concat(lines);\n    var ilen = items.length;\n    var prev = ctx.font;\n    var width = 0;\n    var i;\n    ctx.font = font.string;\n    for (i = 0; i < ilen; ++i) {\n      width = Math.max(ctx.measureText(items[i]).width, width);\n    }\n    ctx.font = prev;\n    return {\n      height: ilen * font.lineHeight,\n      width: width\n    };\n  },\n  /**\n   * Returns value bounded by min and max. This is equivalent to max(min, min(value, max)).\n   * @todo move this method in Chart.helpers.bound\n   * https://doc.qt.io/qt-5/qtglobal.html#qBound\n   */\n  bound: function (min, value, max) {\n    return Math.max(min, Math.min(value, max));\n  },\n  /**\n   * Returns an array of pair [value, state] where state is:\n   * * -1: value is only in a0 (removed)\n   * *  1: value is only in a1 (added)\n   */\n  arrayDiff: function (a0, a1) {\n    var prev = a0.slice();\n    var updates = [];\n    var i, j, ilen, v;\n    for (i = 0, ilen = a1.length; i < ilen; ++i) {\n      v = a1[i];\n      j = prev.indexOf(v);\n      if (j === -1) {\n        updates.push([v, 1]);\n      } else {\n        prev.splice(j, 1);\n      }\n    }\n    for (i = 0, ilen = prev.length; i < ilen; ++i) {\n      updates.push([prev[i], -1]);\n    }\n    return updates;\n  },\n  /**\n   * https://github.com/chartjs/chartjs-plugin-datalabels/issues/70\n   */\n  rasterize: function (v) {\n    return Math.round(v * devicePixelRatio) / devicePixelRatio;\n  }\n};\nfunction orient(point, origin) {\n  var x0 = origin.x;\n  var y0 = origin.y;\n  if (x0 === null) {\n    return {\n      x: 0,\n      y: -1\n    };\n  }\n  if (y0 === null) {\n    return {\n      x: 1,\n      y: 0\n    };\n  }\n  var dx = point.x - x0;\n  var dy = point.y - y0;\n  var ln = Math.sqrt(dx * dx + dy * dy);\n  return {\n    x: ln ? dx / ln : 0,\n    y: ln ? dy / ln : -1\n  };\n}\nfunction aligned(x, y, vx, vy, align) {\n  switch (align) {\n    case 'center':\n      vx = vy = 0;\n      break;\n    case 'bottom':\n      vx = 0;\n      vy = 1;\n      break;\n    case 'right':\n      vx = 1;\n      vy = 0;\n      break;\n    case 'left':\n      vx = -1;\n      vy = 0;\n      break;\n    case 'top':\n      vx = 0;\n      vy = -1;\n      break;\n    case 'start':\n      vx = -vx;\n      vy = -vy;\n      break;\n    case 'end':\n      // keep natural orientation\n      break;\n    default:\n      // clockwise rotation (in degree)\n      align *= Math.PI / 180;\n      vx = Math.cos(align);\n      vy = Math.sin(align);\n      break;\n  }\n  return {\n    x: x,\n    y: y,\n    vx: vx,\n    vy: vy\n  };\n}\n\n// Line clipping (Cohen–Sutherland algorithm)\n// https://en.wikipedia.org/wiki/Cohen–Sutherland_algorithm\n\nvar R_INSIDE = 0;\nvar R_LEFT = 1;\nvar R_RIGHT = 2;\nvar R_BOTTOM = 4;\nvar R_TOP = 8;\nfunction region(x, y, rect) {\n  var res = R_INSIDE;\n  if (x < rect.left) {\n    res |= R_LEFT;\n  } else if (x > rect.right) {\n    res |= R_RIGHT;\n  }\n  if (y < rect.top) {\n    res |= R_TOP;\n  } else if (y > rect.bottom) {\n    res |= R_BOTTOM;\n  }\n  return res;\n}\nfunction clipped(segment, area) {\n  var x0 = segment.x0;\n  var y0 = segment.y0;\n  var x1 = segment.x1;\n  var y1 = segment.y1;\n  var r0 = region(x0, y0, area);\n  var r1 = region(x1, y1, area);\n  var r, x, y;\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (!(r0 | r1) || r0 & r1) {\n      // both points inside or on the same side: no clipping\n      break;\n    }\n\n    // at least one point is outside\n    r = r0 || r1;\n    if (r & R_TOP) {\n      x = x0 + (x1 - x0) * (area.top - y0) / (y1 - y0);\n      y = area.top;\n    } else if (r & R_BOTTOM) {\n      x = x0 + (x1 - x0) * (area.bottom - y0) / (y1 - y0);\n      y = area.bottom;\n    } else if (r & R_RIGHT) {\n      y = y0 + (y1 - y0) * (area.right - x0) / (x1 - x0);\n      x = area.right;\n    } else if (r & R_LEFT) {\n      y = y0 + (y1 - y0) * (area.left - x0) / (x1 - x0);\n      x = area.left;\n    }\n    if (r === r0) {\n      x0 = x;\n      y0 = y;\n      r0 = region(x0, y0, area);\n    } else {\n      x1 = x;\n      y1 = y;\n      r1 = region(x1, y1, area);\n    }\n  }\n  return {\n    x0: x0,\n    x1: x1,\n    y0: y0,\n    y1: y1\n  };\n}\nfunction compute$1(range, config) {\n  var anchor = config.anchor;\n  var segment = range;\n  var x, y;\n  if (config.clamp) {\n    segment = clipped(segment, config.area);\n  }\n  if (anchor === 'start') {\n    x = segment.x0;\n    y = segment.y0;\n  } else if (anchor === 'end') {\n    x = segment.x1;\n    y = segment.y1;\n  } else {\n    x = (segment.x0 + segment.x1) / 2;\n    y = (segment.y0 + segment.y1) / 2;\n  }\n  return aligned(x, y, range.vx, range.vy, config.align);\n}\nvar positioners = {\n  arc: function (el, config) {\n    var angle = (el.startAngle + el.endAngle) / 2;\n    var vx = Math.cos(angle);\n    var vy = Math.sin(angle);\n    var r0 = el.innerRadius;\n    var r1 = el.outerRadius;\n    return compute$1({\n      x0: el.x + vx * r0,\n      y0: el.y + vy * r0,\n      x1: el.x + vx * r1,\n      y1: el.y + vy * r1,\n      vx: vx,\n      vy: vy\n    }, config);\n  },\n  point: function (el, config) {\n    var v = orient(el, config.origin);\n    var rx = v.x * el.options.radius;\n    var ry = v.y * el.options.radius;\n    return compute$1({\n      x0: el.x - rx,\n      y0: el.y - ry,\n      x1: el.x + rx,\n      y1: el.y + ry,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n  bar: function (el, config) {\n    var v = orient(el, config.origin);\n    var x = el.x;\n    var y = el.y;\n    var sx = 0;\n    var sy = 0;\n    if (el.horizontal) {\n      x = Math.min(el.x, el.base);\n      sx = Math.abs(el.base - el.x);\n    } else {\n      y = Math.min(el.y, el.base);\n      sy = Math.abs(el.base - el.y);\n    }\n    return compute$1({\n      x0: x,\n      y0: y + sy,\n      x1: x + sx,\n      y1: y,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n  fallback: function (el, config) {\n    var v = orient(el, config.origin);\n    return compute$1({\n      x0: el.x,\n      y0: el.y,\n      x1: el.x + (el.width || 0),\n      y1: el.y + (el.height || 0),\n      vx: v.x,\n      vy: v.y\n    }, config);\n  }\n};\nvar rasterize = utils.rasterize;\nfunction boundingRects(model) {\n  var borderWidth = model.borderWidth || 0;\n  var padding = model.padding;\n  var th = model.size.height;\n  var tw = model.size.width;\n  var tx = -tw / 2;\n  var ty = -th / 2;\n  return {\n    frame: {\n      x: tx - padding.left - borderWidth,\n      y: ty - padding.top - borderWidth,\n      w: tw + padding.width + borderWidth * 2,\n      h: th + padding.height + borderWidth * 2\n    },\n    text: {\n      x: tx,\n      y: ty,\n      w: tw,\n      h: th\n    }\n  };\n}\nfunction getScaleOrigin(el, context) {\n  var scale = context.chart.getDatasetMeta(context.datasetIndex).vScale;\n  if (!scale) {\n    return null;\n  }\n  if (scale.xCenter !== undefined && scale.yCenter !== undefined) {\n    return {\n      x: scale.xCenter,\n      y: scale.yCenter\n    };\n  }\n  var pixel = scale.getBasePixel();\n  return el.horizontal ? {\n    x: pixel,\n    y: null\n  } : {\n    x: null,\n    y: pixel\n  };\n}\nfunction getPositioner(el) {\n  if (el instanceof ArcElement) {\n    return positioners.arc;\n  }\n  if (el instanceof PointElement) {\n    return positioners.point;\n  }\n  if (el instanceof BarElement) {\n    return positioners.bar;\n  }\n  return positioners.fallback;\n}\nfunction drawRoundedRect(ctx, x, y, w, h, radius) {\n  var HALF_PI = Math.PI / 2;\n  if (radius) {\n    var r = Math.min(radius, h / 2, w / 2);\n    var left = x + r;\n    var top = y + r;\n    var right = x + w - r;\n    var bottom = y + h - r;\n    ctx.moveTo(x, top);\n    if (left < right && top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, -HALF_PI);\n      ctx.arc(right, top, r, -HALF_PI, 0);\n      ctx.arc(right, bottom, r, 0, HALF_PI);\n      ctx.arc(left, bottom, r, HALF_PI, Math.PI);\n    } else if (left < right) {\n      ctx.moveTo(left, y);\n      ctx.arc(right, top, r, -HALF_PI, HALF_PI);\n      ctx.arc(left, top, r, HALF_PI, Math.PI + HALF_PI);\n    } else if (top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, 0);\n      ctx.arc(left, bottom, r, 0, Math.PI);\n    } else {\n      ctx.arc(left, top, r, -Math.PI, Math.PI);\n    }\n    ctx.closePath();\n    ctx.moveTo(x, y);\n  } else {\n    ctx.rect(x, y, w, h);\n  }\n}\nfunction drawFrame(ctx, rect, model) {\n  var bgColor = model.backgroundColor;\n  var borderColor = model.borderColor;\n  var borderWidth = model.borderWidth;\n  if (!bgColor && (!borderColor || !borderWidth)) {\n    return;\n  }\n  ctx.beginPath();\n  drawRoundedRect(ctx, rasterize(rect.x) + borderWidth / 2, rasterize(rect.y) + borderWidth / 2, rasterize(rect.w) - borderWidth, rasterize(rect.h) - borderWidth, model.borderRadius);\n  ctx.closePath();\n  if (bgColor) {\n    ctx.fillStyle = bgColor;\n    ctx.fill();\n  }\n  if (borderColor && borderWidth) {\n    ctx.strokeStyle = borderColor;\n    ctx.lineWidth = borderWidth;\n    ctx.lineJoin = 'miter';\n    ctx.stroke();\n  }\n}\nfunction textGeometry(rect, align, font) {\n  var h = font.lineHeight;\n  var w = rect.w;\n  var x = rect.x;\n  var y = rect.y + h / 2;\n  if (align === 'center') {\n    x += w / 2;\n  } else if (align === 'end' || align === 'right') {\n    x += w;\n  }\n  return {\n    h: h,\n    w: w,\n    x: x,\n    y: y\n  };\n}\nfunction drawTextLine(ctx, text, cfg) {\n  var shadow = ctx.shadowBlur;\n  var stroked = cfg.stroked;\n  var x = rasterize(cfg.x);\n  var y = rasterize(cfg.y);\n  var w = rasterize(cfg.w);\n  if (stroked) {\n    ctx.strokeText(text, x, y, w);\n  }\n  if (cfg.filled) {\n    if (shadow && stroked) {\n      // Prevent drawing shadow on both the text stroke and fill, so\n      // if the text is stroked, remove the shadow for the text fill.\n      ctx.shadowBlur = 0;\n    }\n    ctx.fillText(text, x, y, w);\n    if (shadow && stroked) {\n      ctx.shadowBlur = shadow;\n    }\n  }\n}\nfunction drawText(ctx, lines, rect, model) {\n  var align = model.textAlign;\n  var color = model.color;\n  var filled = !!color;\n  var font = model.font;\n  var ilen = lines.length;\n  var strokeColor = model.textStrokeColor;\n  var strokeWidth = model.textStrokeWidth;\n  var stroked = strokeColor && strokeWidth;\n  var i;\n  if (!ilen || !filled && !stroked) {\n    return;\n  }\n\n  // Adjust coordinates based on text alignment and line height\n  rect = textGeometry(rect, align, font);\n  ctx.font = font.string;\n  ctx.textAlign = align;\n  ctx.textBaseline = 'middle';\n  ctx.shadowBlur = model.textShadowBlur;\n  ctx.shadowColor = model.textShadowColor;\n  if (filled) {\n    ctx.fillStyle = color;\n  }\n  if (stroked) {\n    ctx.lineJoin = 'round';\n    ctx.lineWidth = strokeWidth;\n    ctx.strokeStyle = strokeColor;\n  }\n  for (i = 0, ilen = lines.length; i < ilen; ++i) {\n    drawTextLine(ctx, lines[i], {\n      stroked: stroked,\n      filled: filled,\n      w: rect.w,\n      x: rect.x,\n      y: rect.y + rect.h * i\n    });\n  }\n}\nvar Label = function (config, ctx, el, index) {\n  var me = this;\n  me._config = config;\n  me._index = index;\n  me._model = null;\n  me._rects = null;\n  me._ctx = ctx;\n  me._el = el;\n};\nmerge(Label.prototype, {\n  /**\n   * @private\n   */\n  _modelize: function (display, lines, config, context) {\n    var me = this;\n    var index = me._index;\n    var font = toFont(resolve([config.font, {}], context, index));\n    var color = resolve([config.color, defaults$1.color], context, index);\n    return {\n      align: resolve([config.align, 'center'], context, index),\n      anchor: resolve([config.anchor, 'center'], context, index),\n      area: context.chart.chartArea,\n      backgroundColor: resolve([config.backgroundColor, null], context, index),\n      borderColor: resolve([config.borderColor, null], context, index),\n      borderRadius: resolve([config.borderRadius, 0], context, index),\n      borderWidth: resolve([config.borderWidth, 0], context, index),\n      clamp: resolve([config.clamp, false], context, index),\n      clip: resolve([config.clip, false], context, index),\n      color: color,\n      display: display,\n      font: font,\n      lines: lines,\n      offset: resolve([config.offset, 4], context, index),\n      opacity: resolve([config.opacity, 1], context, index),\n      origin: getScaleOrigin(me._el, context),\n      padding: toPadding(resolve([config.padding, 4], context, index)),\n      positioner: getPositioner(me._el),\n      rotation: resolve([config.rotation, 0], context, index) * (Math.PI / 180),\n      size: utils.textSize(me._ctx, lines, font),\n      textAlign: resolve([config.textAlign, 'start'], context, index),\n      textShadowBlur: resolve([config.textShadowBlur, 0], context, index),\n      textShadowColor: resolve([config.textShadowColor, color], context, index),\n      textStrokeColor: resolve([config.textStrokeColor, color], context, index),\n      textStrokeWidth: resolve([config.textStrokeWidth, 0], context, index)\n    };\n  },\n  update: function (context) {\n    var me = this;\n    var model = null;\n    var rects = null;\n    var index = me._index;\n    var config = me._config;\n    var value, label, lines;\n\n    // We first resolve the display option (separately) to avoid computing\n    // other options in case the label is hidden (i.e. display: false).\n    var display = resolve([config.display, true], context, index);\n    if (display) {\n      value = context.dataset.data[index];\n      label = valueOrDefault(callback(config.formatter, [value, context]), value);\n      lines = isNullOrUndef(label) ? [] : utils.toTextLines(label);\n      if (lines.length) {\n        model = me._modelize(display, lines, config, context);\n        rects = boundingRects(model);\n      }\n    }\n    me._model = model;\n    me._rects = rects;\n  },\n  geometry: function () {\n    return this._rects ? this._rects.frame : {};\n  },\n  rotation: function () {\n    return this._model ? this._model.rotation : 0;\n  },\n  visible: function () {\n    return this._model && this._model.opacity;\n  },\n  model: function () {\n    return this._model;\n  },\n  draw: function (chart, center) {\n    var me = this;\n    var ctx = chart.ctx;\n    var model = me._model;\n    var rects = me._rects;\n    var area;\n    if (!this.visible()) {\n      return;\n    }\n    ctx.save();\n    if (model.clip) {\n      area = model.area;\n      ctx.beginPath();\n      ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n      ctx.clip();\n    }\n    ctx.globalAlpha = utils.bound(0, model.opacity, 1);\n    ctx.translate(rasterize(center.x), rasterize(center.y));\n    ctx.rotate(model.rotation);\n    drawFrame(ctx, rects.frame, model);\n    drawText(ctx, model.lines, rects.text, model);\n    ctx.restore();\n  }\n});\nvar MIN_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991; // eslint-disable-line es/no-number-minsafeinteger\nvar MAX_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991; // eslint-disable-line es/no-number-maxsafeinteger\n\nfunction rotated(point, center, angle) {\n  var cos = Math.cos(angle);\n  var sin = Math.sin(angle);\n  var cx = center.x;\n  var cy = center.y;\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\nfunction projected(points, axis) {\n  var min = MAX_INTEGER;\n  var max = MIN_INTEGER;\n  var origin = axis.origin;\n  var i, pt, vx, vy, dp;\n  for (i = 0; i < points.length; ++i) {\n    pt = points[i];\n    vx = pt.x - origin.x;\n    vy = pt.y - origin.y;\n    dp = axis.vx * vx + axis.vy * vy;\n    min = Math.min(min, dp);\n    max = Math.max(max, dp);\n  }\n  return {\n    min: min,\n    max: max\n  };\n}\nfunction toAxis(p0, p1) {\n  var vx = p1.x - p0.x;\n  var vy = p1.y - p0.y;\n  var ln = Math.sqrt(vx * vx + vy * vy);\n  return {\n    vx: (p1.x - p0.x) / ln,\n    vy: (p1.y - p0.y) / ln,\n    origin: p0,\n    ln: ln\n  };\n}\nvar HitBox = function () {\n  this._rotation = 0;\n  this._rect = {\n    x: 0,\n    y: 0,\n    w: 0,\n    h: 0\n  };\n};\nmerge(HitBox.prototype, {\n  center: function () {\n    var r = this._rect;\n    return {\n      x: r.x + r.w / 2,\n      y: r.y + r.h / 2\n    };\n  },\n  update: function (center, rect, rotation) {\n    this._rotation = rotation;\n    this._rect = {\n      x: rect.x + center.x,\n      y: rect.y + center.y,\n      w: rect.w,\n      h: rect.h\n    };\n  },\n  contains: function (point) {\n    var me = this;\n    var margin = 1;\n    var rect = me._rect;\n    point = rotated(point, me.center(), -me._rotation);\n    return !(point.x < rect.x - margin || point.y < rect.y - margin || point.x > rect.x + rect.w + margin * 2 || point.y > rect.y + rect.h + margin * 2);\n  },\n  // Separating Axis Theorem\n  // https://gamedevelopment.tutsplus.com/tutorials/collision-detection-using-the-separating-axis-theorem--gamedev-169\n  intersects: function (other) {\n    var r0 = this._points();\n    var r1 = other._points();\n    var axes = [toAxis(r0[0], r0[1]), toAxis(r0[0], r0[3])];\n    var i, pr0, pr1;\n    if (this._rotation !== other._rotation) {\n      // Only separate with r1 axis if the rotation is different,\n      // else it's enough to separate r0 and r1 with r0 axis only!\n      axes.push(toAxis(r1[0], r1[1]), toAxis(r1[0], r1[3]));\n    }\n    for (i = 0; i < axes.length; ++i) {\n      pr0 = projected(r0, axes[i]);\n      pr1 = projected(r1, axes[i]);\n      if (pr0.max < pr1.min || pr1.max < pr0.min) {\n        return false;\n      }\n    }\n    return true;\n  },\n  /**\n   * @private\n   */\n  _points: function () {\n    var me = this;\n    var rect = me._rect;\n    var angle = me._rotation;\n    var center = me.center();\n    return [rotated({\n      x: rect.x,\n      y: rect.y\n    }, center, angle), rotated({\n      x: rect.x + rect.w,\n      y: rect.y\n    }, center, angle), rotated({\n      x: rect.x + rect.w,\n      y: rect.y + rect.h\n    }, center, angle), rotated({\n      x: rect.x,\n      y: rect.y + rect.h\n    }, center, angle)];\n  }\n});\nfunction coordinates(el, model, geometry) {\n  var point = model.positioner(el, model);\n  var vx = point.vx;\n  var vy = point.vy;\n  if (!vx && !vy) {\n    // if aligned center, we don't want to offset the center point\n    return {\n      x: point.x,\n      y: point.y\n    };\n  }\n  var w = geometry.w;\n  var h = geometry.h;\n\n  // take in account the label rotation\n  var rotation = model.rotation;\n  var dx = Math.abs(w / 2 * Math.cos(rotation)) + Math.abs(h / 2 * Math.sin(rotation));\n  var dy = Math.abs(w / 2 * Math.sin(rotation)) + Math.abs(h / 2 * Math.cos(rotation));\n\n  // scale the unit vector (vx, vy) to get at least dx or dy equal to\n  // w or h respectively (else we would calculate the distance to the\n  // ellipse inscribed in the bounding rect)\n  var vs = 1 / Math.max(Math.abs(vx), Math.abs(vy));\n  dx *= vx * vs;\n  dy *= vy * vs;\n\n  // finally, include the explicit offset\n  dx += model.offset * vx;\n  dy += model.offset * vy;\n  return {\n    x: point.x + dx,\n    y: point.y + dy\n  };\n}\nfunction collide(labels, collider) {\n  var i, j, s0, s1;\n\n  // IMPORTANT Iterate in the reverse order since items at the end of the\n  // list have an higher weight/priority and thus should be less impacted\n  // by the overlapping strategy.\n\n  for (i = labels.length - 1; i >= 0; --i) {\n    s0 = labels[i].$layout;\n    for (j = i - 1; j >= 0 && s0._visible; --j) {\n      s1 = labels[j].$layout;\n      if (s1._visible && s0._box.intersects(s1._box)) {\n        collider(s0, s1);\n      }\n    }\n  }\n  return labels;\n}\nfunction compute(labels) {\n  var i, ilen, label, state, geometry, center, proxy;\n\n  // Initialize labels for overlap detection\n  for (i = 0, ilen = labels.length; i < ilen; ++i) {\n    label = labels[i];\n    state = label.$layout;\n    if (state._visible) {\n      // Chart.js 3 removed el._model in favor of getProps(), making harder to\n      // abstract reading values in positioners. Also, using string arrays to\n      // read values (i.e. var {a,b,c} = el.getProps([\"a\",\"b\",\"c\"])) would make\n      // positioners inefficient in the normal case (i.e. not the final values)\n      // and the code a bit ugly, so let's use a Proxy instead.\n      proxy = new Proxy(label._el, {\n        get: (el, p) => el.getProps([p], true)[p]\n      });\n      geometry = label.geometry();\n      center = coordinates(proxy, label.model(), geometry);\n      state._box.update(center, geometry, label.rotation());\n    }\n  }\n\n  // Auto hide overlapping labels\n  return collide(labels, function (s0, s1) {\n    var h0 = s0._hidable;\n    var h1 = s1._hidable;\n    if (h0 && h1 || h1) {\n      s1._visible = false;\n    } else if (h0) {\n      s0._visible = false;\n    }\n  });\n}\nvar layout = {\n  prepare: function (datasets) {\n    var labels = [];\n    var i, j, ilen, jlen, label;\n    for (i = 0, ilen = datasets.length; i < ilen; ++i) {\n      for (j = 0, jlen = datasets[i].length; j < jlen; ++j) {\n        label = datasets[i][j];\n        labels.push(label);\n        label.$layout = {\n          _box: new HitBox(),\n          _hidable: false,\n          _visible: true,\n          _set: i,\n          _idx: label._index\n        };\n      }\n    }\n\n    // TODO New `z` option: labels with a higher z-index are drawn\n    // of top of the ones with a lower index. Lowest z-index labels\n    // are also discarded first when hiding overlapping labels.\n    labels.sort(function (a, b) {\n      var sa = a.$layout;\n      var sb = b.$layout;\n      return sa._idx === sb._idx ? sb._set - sa._set : sb._idx - sa._idx;\n    });\n    this.update(labels);\n    return labels;\n  },\n  update: function (labels) {\n    var dirty = false;\n    var i, ilen, label, model, state;\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      model = label.model();\n      state = label.$layout;\n      state._hidable = model && model.display === 'auto';\n      state._visible = label.visible();\n      dirty |= state._hidable;\n    }\n    if (dirty) {\n      compute(labels);\n    }\n  },\n  lookup: function (labels, point) {\n    var i, state;\n\n    // IMPORTANT Iterate in the reverse order since items at the end of\n    // the list have an higher z-index, thus should be picked first.\n\n    for (i = labels.length - 1; i >= 0; --i) {\n      state = labels[i].$layout;\n      if (state && state._visible && state._box.contains(point)) {\n        return labels[i];\n      }\n    }\n    return null;\n  },\n  draw: function (chart, labels) {\n    var i, ilen, label, state, geometry, center;\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      state = label.$layout;\n      if (state._visible) {\n        geometry = label.geometry();\n        center = coordinates(label._el, label.model(), geometry);\n        state._box.update(center, geometry, label.rotation());\n        label.draw(chart, center);\n      }\n    }\n  }\n};\nvar formatter = function (value) {\n  if (isNullOrUndef(value)) {\n    return null;\n  }\n  var label = value;\n  var keys, klen, k;\n  if (isObject(value)) {\n    if (!isNullOrUndef(value.label)) {\n      label = value.label;\n    } else if (!isNullOrUndef(value.r)) {\n      label = value.r;\n    } else {\n      label = '';\n      keys = Object.keys(value);\n      for (k = 0, klen = keys.length; k < klen; ++k) {\n        label += (k !== 0 ? ', ' : '') + keys[k] + ': ' + value[keys[k]];\n      }\n    }\n  }\n  return '' + label;\n};\n\n/**\n * IMPORTANT: make sure to also update tests and TypeScript definition\n * files (`/test/specs/defaults.spec.js` and `/types/options.d.ts`)\n */\n\nvar defaults = {\n  align: 'center',\n  anchor: 'center',\n  backgroundColor: null,\n  borderColor: null,\n  borderRadius: 0,\n  borderWidth: 0,\n  clamp: false,\n  clip: false,\n  color: undefined,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: 1.2,\n    size: undefined,\n    style: undefined,\n    weight: null\n  },\n  formatter: formatter,\n  labels: undefined,\n  listeners: {},\n  offset: 4,\n  opacity: 1,\n  padding: {\n    top: 4,\n    right: 4,\n    bottom: 4,\n    left: 4\n  },\n  rotation: 0,\n  textAlign: 'start',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  textShadowBlur: 0,\n  textShadowColor: undefined\n};\n\n/**\n * @see https://github.com/chartjs/Chart.js/issues/4176\n */\n\nvar EXPANDO_KEY = '$datalabels';\nvar DEFAULT_KEY = '$default';\nfunction configure(dataset, options) {\n  var override = dataset.datalabels;\n  var listeners = {};\n  var configs = [];\n  var labels, keys;\n  if (override === false) {\n    return null;\n  }\n  if (override === true) {\n    override = {};\n  }\n  options = merge({}, [options, override]);\n  labels = options.labels || {};\n  keys = Object.keys(labels);\n  delete options.labels;\n  if (keys.length) {\n    keys.forEach(function (key) {\n      if (labels[key]) {\n        configs.push(merge({}, [options, labels[key], {\n          _key: key\n        }]));\n      }\n    });\n  } else {\n    // Default label if no \"named\" label defined.\n    configs.push(options);\n  }\n\n  // listeners: {<event-type>: {<label-key>: <fn>}}\n  listeners = configs.reduce(function (target, config) {\n    each(config.listeners || {}, function (fn, event) {\n      target[event] = target[event] || {};\n      target[event][config._key || DEFAULT_KEY] = fn;\n    });\n    delete config.listeners;\n    return target;\n  }, {});\n  return {\n    labels: configs,\n    listeners: listeners\n  };\n}\nfunction dispatchEvent(chart, listeners, label, event) {\n  if (!listeners) {\n    return;\n  }\n  var context = label.$context;\n  var groups = label.$groups;\n  var callback$1;\n  if (!listeners[groups._set]) {\n    return;\n  }\n  callback$1 = listeners[groups._set][groups._key];\n  if (!callback$1) {\n    return;\n  }\n  if (callback(callback$1, [context, event]) === true) {\n    // Users are allowed to tweak the given context by injecting values that can be\n    // used in scriptable options to display labels differently based on the current\n    // event (e.g. highlight an hovered label). That's why we update the label with\n    // the output context and schedule a new chart render by setting it dirty.\n    chart[EXPANDO_KEY]._dirty = true;\n    label.update(context);\n  }\n}\nfunction dispatchMoveEvents(chart, listeners, previous, label, event) {\n  var enter, leave;\n  if (!previous && !label) {\n    return;\n  }\n  if (!previous) {\n    enter = true;\n  } else if (!label) {\n    leave = true;\n  } else if (previous !== label) {\n    leave = enter = true;\n  }\n  if (leave) {\n    dispatchEvent(chart, listeners.leave, previous, event);\n  }\n  if (enter) {\n    dispatchEvent(chart, listeners.enter, label, event);\n  }\n}\nfunction handleMoveEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var listeners = expando._listeners;\n  var previous, label;\n  if (!listeners.enter && !listeners.leave) {\n    return;\n  }\n  if (event.type === 'mousemove') {\n    label = layout.lookup(expando._labels, event);\n  } else if (event.type !== 'mouseout') {\n    return;\n  }\n  previous = expando._hovered;\n  expando._hovered = label;\n  dispatchMoveEvents(chart, listeners, previous, label, event);\n}\nfunction handleClickEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var handlers = expando._listeners.click;\n  var label = handlers && layout.lookup(expando._labels, event);\n  if (label) {\n    dispatchEvent(chart, handlers, label, event);\n  }\n}\nvar plugin = {\n  id: 'datalabels',\n  defaults: defaults,\n  beforeInit: function (chart) {\n    chart[EXPANDO_KEY] = {\n      _actives: []\n    };\n  },\n  beforeUpdate: function (chart) {\n    var expando = chart[EXPANDO_KEY];\n    expando._listened = false;\n    expando._listeners = {}; // {<event-type>: {<dataset-index>: {<label-key>: <fn>}}}\n    expando._datasets = []; // per dataset labels: [Label[]]\n    expando._labels = []; // layouted labels: Label[]\n  },\n  afterDatasetUpdate: function (chart, args, options) {\n    var datasetIndex = args.index;\n    var expando = chart[EXPANDO_KEY];\n    var labels = expando._datasets[datasetIndex] = [];\n    var visible = chart.isDatasetVisible(datasetIndex);\n    var dataset = chart.data.datasets[datasetIndex];\n    var config = configure(dataset, options);\n    var elements = args.meta.data || [];\n    var ctx = chart.ctx;\n    var i, j, ilen, jlen, cfg, key, el, label;\n    ctx.save();\n    for (i = 0, ilen = elements.length; i < ilen; ++i) {\n      el = elements[i];\n      el[EXPANDO_KEY] = [];\n      if (visible && el && chart.getDataVisibility(i) && !el.skip) {\n        for (j = 0, jlen = config.labels.length; j < jlen; ++j) {\n          cfg = config.labels[j];\n          key = cfg._key;\n          label = new Label(cfg, ctx, el, i);\n          label.$groups = {\n            _set: datasetIndex,\n            _key: key || DEFAULT_KEY\n          };\n          label.$context = {\n            active: false,\n            chart: chart,\n            dataIndex: i,\n            dataset: dataset,\n            datasetIndex: datasetIndex\n          };\n          label.update(label.$context);\n          el[EXPANDO_KEY].push(label);\n          labels.push(label);\n        }\n      }\n    }\n    ctx.restore();\n\n    // Store listeners at the chart level and per event type to optimize\n    // cases where no listeners are registered for a specific event.\n    merge(expando._listeners, config.listeners, {\n      merger: function (event, target, source) {\n        target[event] = target[event] || {};\n        target[event][args.index] = source[event];\n        expando._listened = true;\n      }\n    });\n  },\n  afterUpdate: function (chart) {\n    chart[EXPANDO_KEY]._labels = layout.prepare(chart[EXPANDO_KEY]._datasets);\n  },\n  // Draw labels on top of all dataset elements\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/29\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/32\n  afterDatasetsDraw: function (chart) {\n    layout.draw(chart, chart[EXPANDO_KEY]._labels);\n  },\n  beforeEvent: function (chart, args) {\n    // If there is no listener registered for this chart, `listened` will be false,\n    // meaning we can immediately ignore the incoming event and avoid useless extra\n    // computation for users who don't implement label interactions.\n    if (chart[EXPANDO_KEY]._listened) {\n      var event = args.event;\n      switch (event.type) {\n        case 'mousemove':\n        case 'mouseout':\n          handleMoveEvents(chart, event);\n          break;\n        case 'click':\n          handleClickEvents(chart, event);\n          break;\n      }\n    }\n  },\n  afterEvent: function (chart) {\n    var expando = chart[EXPANDO_KEY];\n    var previous = expando._actives;\n    var actives = expando._actives = chart.getActiveElements();\n    var updates = utils.arrayDiff(previous, actives);\n    var i, ilen, j, jlen, update, label, labels;\n    for (i = 0, ilen = updates.length; i < ilen; ++i) {\n      update = updates[i];\n      if (update[1]) {\n        labels = update[0].element[EXPANDO_KEY] || [];\n        for (j = 0, jlen = labels.length; j < jlen; ++j) {\n          label = labels[j];\n          label.$context.active = update[1] === 1;\n          label.update(label.$context);\n        }\n      }\n    }\n    if (expando._dirty || updates.length) {\n      layout.update(expando._labels);\n      chart.render();\n    }\n    delete expando._dirty;\n  }\n};\nexport { plugin as default };", "map": {"version": 3, "names": ["isNullOrUndef", "merge", "toFont", "resolve", "toPadding", "valueOrDefault", "callback", "isObject", "each", "defaults", "defaults$1", "ArcElement", "PointElement", "BarElement", "devicePixelRatio", "window", "screen", "deviceXDPI", "logicalXDPI", "utils", "toTextLines", "inputs", "lines", "input", "concat", "length", "pop", "unshift", "apply", "split", "Array", "isArray", "push", "textSize", "ctx", "font", "items", "ilen", "prev", "width", "i", "string", "Math", "max", "measureText", "height", "lineHeight", "bound", "min", "value", "arrayDiff", "a0", "a1", "slice", "updates", "j", "v", "indexOf", "splice", "rasterize", "round", "orient", "point", "origin", "x0", "x", "y0", "y", "dx", "dy", "ln", "sqrt", "aligned", "vx", "vy", "align", "PI", "cos", "sin", "R_INSIDE", "R_LEFT", "R_RIGHT", "R_BOTTOM", "R_TOP", "region", "rect", "res", "left", "right", "top", "bottom", "clipped", "segment", "area", "x1", "y1", "r0", "r1", "r", "compute$1", "range", "config", "anchor", "clamp", "positioners", "arc", "el", "angle", "startAngle", "endAngle", "innerRadius", "outerRadius", "rx", "options", "radius", "ry", "bar", "sx", "sy", "horizontal", "base", "abs", "fallback", "boundingRects", "model", "borderWidth", "padding", "th", "size", "tw", "tx", "ty", "frame", "w", "h", "text", "getScaleOrigin", "context", "scale", "chart", "getDatasetMeta", "datasetIndex", "vScale", "xCenter", "undefined", "yCenter", "pixel", "getBasePixel", "getPositioner", "drawRoundedRect", "HALF_PI", "moveTo", "closePath", "draw<PERSON>rame", "bgColor", "backgroundColor", "borderColor", "beginPath", "borderRadius", "fillStyle", "fill", "strokeStyle", "lineWidth", "lineJoin", "stroke", "textGeometry", "drawTextLine", "cfg", "shadow", "<PERSON><PERSON><PERSON><PERSON>", "stroked", "strokeText", "filled", "fillText", "drawText", "textAlign", "color", "strokeColor", "textStrokeColor", "strokeWidth", "textStrokeWidth", "textBaseline", "textShadowBlur", "shadowColor", "textShadowColor", "Label", "index", "me", "_config", "_index", "_model", "_rects", "_ctx", "_el", "prototype", "_modelize", "display", "chartArea", "clip", "offset", "opacity", "positioner", "rotation", "update", "rects", "label", "dataset", "data", "formatter", "geometry", "visible", "draw", "center", "save", "globalAlpha", "translate", "rotate", "restore", "MIN_INTEGER", "Number", "MIN_SAFE_INTEGER", "MAX_INTEGER", "MAX_SAFE_INTEGER", "rotated", "cx", "cy", "projected", "points", "axis", "pt", "dp", "to<PERSON><PERSON><PERSON>", "p0", "p1", "HitBox", "_rotation", "_rect", "contains", "margin", "intersects", "other", "_points", "axes", "pr0", "pr1", "coordinates", "vs", "collide", "labels", "collider", "s0", "s1", "$layout", "_visible", "_box", "compute", "state", "proxy", "Proxy", "get", "p", "getProps", "h0", "_hidable", "h1", "layout", "prepare", "datasets", "jlen", "_set", "_idx", "sort", "a", "b", "sa", "sb", "dirty", "lookup", "keys", "klen", "k", "Object", "family", "style", "weight", "listeners", "EXPANDO_KEY", "DEFAULT_KEY", "configure", "override", "datalabels", "configs", "for<PERSON>ach", "key", "_key", "reduce", "target", "fn", "event", "dispatchEvent", "$context", "groups", "$groups", "callback$1", "_dirty", "dispatchMoveEvents", "previous", "enter", "leave", "handleMoveEvents", "expando", "_listeners", "type", "_labels", "_hovered", "handleClickEvents", "handlers", "click", "plugin", "id", "beforeInit", "_actives", "beforeUpdate", "_listened", "_datasets", "afterDatasetUpdate", "args", "isDatasetVisible", "elements", "meta", "getDataVisibility", "skip", "active", "dataIndex", "merger", "source", "afterUpdate", "afterDatasetsDraw", "beforeEvent", "afterEvent", "actives", "getActiveElements", "element", "render", "default"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/node_modules/chartjs-plugin-datalabels/dist/chartjs-plugin-datalabels.esm.js"], "sourcesContent": ["/*!\n * chartjs-plugin-datalabels v2.2.0\n * https://chartjs-plugin-datalabels.netlify.app\n * (c) 2017-2022 chartjs-plugin-datalabels contributors\n * Released under the MIT license\n */\nimport { isNullOrUndef, merge, toFont, resolve, toPadding, valueOrDefault, callback, isObject, each } from 'chart.js/helpers';\nimport { defaults as defaults$1, ArcElement, PointElement, BarElement } from 'chart.js';\n\nvar devicePixelRatio = (function() {\n  if (typeof window !== 'undefined') {\n    if (window.devicePixelRatio) {\n      return window.devicePixelRatio;\n    }\n\n    // devicePixelRatio is undefined on IE10\n    // https://stackoverflow.com/a/20204180/8837887\n    // https://github.com/chartjs/chartjs-plugin-datalabels/issues/85\n    var screen = window.screen;\n    if (screen) {\n      return (screen.deviceXDPI || 1) / (screen.logicalXDPI || 1);\n    }\n  }\n\n  return 1;\n}());\n\nvar utils = {\n  // @todo move this in Chart.helpers.toTextLines\n  toTextLines: function(inputs) {\n    var lines = [];\n    var input;\n\n    inputs = [].concat(inputs);\n    while (inputs.length) {\n      input = inputs.pop();\n      if (typeof input === 'string') {\n        lines.unshift.apply(lines, input.split('\\n'));\n      } else if (Array.isArray(input)) {\n        inputs.push.apply(inputs, input);\n      } else if (!isNullOrUndef(inputs)) {\n        lines.unshift('' + input);\n      }\n    }\n\n    return lines;\n  },\n\n  // @todo move this in Chart.helpers.canvas.textSize\n  // @todo cache calls of measureText if font doesn't change?!\n  textSize: function(ctx, lines, font) {\n    var items = [].concat(lines);\n    var ilen = items.length;\n    var prev = ctx.font;\n    var width = 0;\n    var i;\n\n    ctx.font = font.string;\n\n    for (i = 0; i < ilen; ++i) {\n      width = Math.max(ctx.measureText(items[i]).width, width);\n    }\n\n    ctx.font = prev;\n\n    return {\n      height: ilen * font.lineHeight,\n      width: width\n    };\n  },\n\n  /**\n   * Returns value bounded by min and max. This is equivalent to max(min, min(value, max)).\n   * @todo move this method in Chart.helpers.bound\n   * https://doc.qt.io/qt-5/qtglobal.html#qBound\n   */\n  bound: function(min, value, max) {\n    return Math.max(min, Math.min(value, max));\n  },\n\n  /**\n   * Returns an array of pair [value, state] where state is:\n   * * -1: value is only in a0 (removed)\n   * *  1: value is only in a1 (added)\n   */\n  arrayDiff: function(a0, a1) {\n    var prev = a0.slice();\n    var updates = [];\n    var i, j, ilen, v;\n\n    for (i = 0, ilen = a1.length; i < ilen; ++i) {\n      v = a1[i];\n      j = prev.indexOf(v);\n\n      if (j === -1) {\n        updates.push([v, 1]);\n      } else {\n        prev.splice(j, 1);\n      }\n    }\n\n    for (i = 0, ilen = prev.length; i < ilen; ++i) {\n      updates.push([prev[i], -1]);\n    }\n\n    return updates;\n  },\n\n  /**\n   * https://github.com/chartjs/chartjs-plugin-datalabels/issues/70\n   */\n  rasterize: function(v) {\n    return Math.round(v * devicePixelRatio) / devicePixelRatio;\n  }\n};\n\nfunction orient(point, origin) {\n  var x0 = origin.x;\n  var y0 = origin.y;\n\n  if (x0 === null) {\n    return {x: 0, y: -1};\n  }\n  if (y0 === null) {\n    return {x: 1, y: 0};\n  }\n\n  var dx = point.x - x0;\n  var dy = point.y - y0;\n  var ln = Math.sqrt(dx * dx + dy * dy);\n\n  return {\n    x: ln ? dx / ln : 0,\n    y: ln ? dy / ln : -1\n  };\n}\n\nfunction aligned(x, y, vx, vy, align) {\n  switch (align) {\n  case 'center':\n    vx = vy = 0;\n    break;\n  case 'bottom':\n    vx = 0;\n    vy = 1;\n    break;\n  case 'right':\n    vx = 1;\n    vy = 0;\n    break;\n  case 'left':\n    vx = -1;\n    vy = 0;\n    break;\n  case 'top':\n    vx = 0;\n    vy = -1;\n    break;\n  case 'start':\n    vx = -vx;\n    vy = -vy;\n    break;\n  case 'end':\n    // keep natural orientation\n    break;\n  default:\n    // clockwise rotation (in degree)\n    align *= (Math.PI / 180);\n    vx = Math.cos(align);\n    vy = Math.sin(align);\n    break;\n  }\n\n  return {\n    x: x,\n    y: y,\n    vx: vx,\n    vy: vy\n  };\n}\n\n// Line clipping (Cohen–Sutherland algorithm)\n// https://en.wikipedia.org/wiki/Cohen–Sutherland_algorithm\n\nvar R_INSIDE = 0;\nvar R_LEFT = 1;\nvar R_RIGHT = 2;\nvar R_BOTTOM = 4;\nvar R_TOP = 8;\n\nfunction region(x, y, rect) {\n  var res = R_INSIDE;\n\n  if (x < rect.left) {\n    res |= R_LEFT;\n  } else if (x > rect.right) {\n    res |= R_RIGHT;\n  }\n  if (y < rect.top) {\n    res |= R_TOP;\n  } else if (y > rect.bottom) {\n    res |= R_BOTTOM;\n  }\n\n  return res;\n}\n\nfunction clipped(segment, area) {\n  var x0 = segment.x0;\n  var y0 = segment.y0;\n  var x1 = segment.x1;\n  var y1 = segment.y1;\n  var r0 = region(x0, y0, area);\n  var r1 = region(x1, y1, area);\n  var r, x, y;\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (!(r0 | r1) || (r0 & r1)) {\n      // both points inside or on the same side: no clipping\n      break;\n    }\n\n    // at least one point is outside\n    r = r0 || r1;\n\n    if (r & R_TOP) {\n      x = x0 + (x1 - x0) * (area.top - y0) / (y1 - y0);\n      y = area.top;\n    } else if (r & R_BOTTOM) {\n      x = x0 + (x1 - x0) * (area.bottom - y0) / (y1 - y0);\n      y = area.bottom;\n    } else if (r & R_RIGHT) {\n      y = y0 + (y1 - y0) * (area.right - x0) / (x1 - x0);\n      x = area.right;\n    } else if (r & R_LEFT) {\n      y = y0 + (y1 - y0) * (area.left - x0) / (x1 - x0);\n      x = area.left;\n    }\n\n    if (r === r0) {\n      x0 = x;\n      y0 = y;\n      r0 = region(x0, y0, area);\n    } else {\n      x1 = x;\n      y1 = y;\n      r1 = region(x1, y1, area);\n    }\n  }\n\n  return {\n    x0: x0,\n    x1: x1,\n    y0: y0,\n    y1: y1\n  };\n}\n\nfunction compute$1(range, config) {\n  var anchor = config.anchor;\n  var segment = range;\n  var x, y;\n\n  if (config.clamp) {\n    segment = clipped(segment, config.area);\n  }\n\n  if (anchor === 'start') {\n    x = segment.x0;\n    y = segment.y0;\n  } else if (anchor === 'end') {\n    x = segment.x1;\n    y = segment.y1;\n  } else {\n    x = (segment.x0 + segment.x1) / 2;\n    y = (segment.y0 + segment.y1) / 2;\n  }\n\n  return aligned(x, y, range.vx, range.vy, config.align);\n}\n\nvar positioners = {\n  arc: function(el, config) {\n    var angle = (el.startAngle + el.endAngle) / 2;\n    var vx = Math.cos(angle);\n    var vy = Math.sin(angle);\n    var r0 = el.innerRadius;\n    var r1 = el.outerRadius;\n\n    return compute$1({\n      x0: el.x + vx * r0,\n      y0: el.y + vy * r0,\n      x1: el.x + vx * r1,\n      y1: el.y + vy * r1,\n      vx: vx,\n      vy: vy\n    }, config);\n  },\n\n  point: function(el, config) {\n    var v = orient(el, config.origin);\n    var rx = v.x * el.options.radius;\n    var ry = v.y * el.options.radius;\n\n    return compute$1({\n      x0: el.x - rx,\n      y0: el.y - ry,\n      x1: el.x + rx,\n      y1: el.y + ry,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n\n  bar: function(el, config) {\n    var v = orient(el, config.origin);\n    var x = el.x;\n    var y = el.y;\n    var sx = 0;\n    var sy = 0;\n\n    if (el.horizontal) {\n      x = Math.min(el.x, el.base);\n      sx = Math.abs(el.base - el.x);\n    } else {\n      y = Math.min(el.y, el.base);\n      sy = Math.abs(el.base - el.y);\n    }\n\n    return compute$1({\n      x0: x,\n      y0: y + sy,\n      x1: x + sx,\n      y1: y,\n      vx: v.x,\n      vy: v.y\n    }, config);\n  },\n\n  fallback: function(el, config) {\n    var v = orient(el, config.origin);\n\n    return compute$1({\n      x0: el.x,\n      y0: el.y,\n      x1: el.x + (el.width || 0),\n      y1: el.y + (el.height || 0),\n      vx: v.x,\n      vy: v.y\n    }, config);\n  }\n};\n\nvar rasterize = utils.rasterize;\n\nfunction boundingRects(model) {\n  var borderWidth = model.borderWidth || 0;\n  var padding = model.padding;\n  var th = model.size.height;\n  var tw = model.size.width;\n  var tx = -tw / 2;\n  var ty = -th / 2;\n\n  return {\n    frame: {\n      x: tx - padding.left - borderWidth,\n      y: ty - padding.top - borderWidth,\n      w: tw + padding.width + borderWidth * 2,\n      h: th + padding.height + borderWidth * 2\n    },\n    text: {\n      x: tx,\n      y: ty,\n      w: tw,\n      h: th\n    }\n  };\n}\n\nfunction getScaleOrigin(el, context) {\n  var scale = context.chart.getDatasetMeta(context.datasetIndex).vScale;\n\n  if (!scale) {\n    return null;\n  }\n\n  if (scale.xCenter !== undefined && scale.yCenter !== undefined) {\n    return {x: scale.xCenter, y: scale.yCenter};\n  }\n\n  var pixel = scale.getBasePixel();\n  return el.horizontal ?\n    {x: pixel, y: null} :\n    {x: null, y: pixel};\n}\n\nfunction getPositioner(el) {\n  if (el instanceof ArcElement) {\n    return positioners.arc;\n  }\n  if (el instanceof PointElement) {\n    return positioners.point;\n  }\n  if (el instanceof BarElement) {\n    return positioners.bar;\n  }\n  return positioners.fallback;\n}\n\nfunction drawRoundedRect(ctx, x, y, w, h, radius) {\n  var HALF_PI = Math.PI / 2;\n\n  if (radius) {\n    var r = Math.min(radius, h / 2, w / 2);\n    var left = x + r;\n    var top = y + r;\n    var right = x + w - r;\n    var bottom = y + h - r;\n\n    ctx.moveTo(x, top);\n    if (left < right && top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, -HALF_PI);\n      ctx.arc(right, top, r, -HALF_PI, 0);\n      ctx.arc(right, bottom, r, 0, HALF_PI);\n      ctx.arc(left, bottom, r, HALF_PI, Math.PI);\n    } else if (left < right) {\n      ctx.moveTo(left, y);\n      ctx.arc(right, top, r, -HALF_PI, HALF_PI);\n      ctx.arc(left, top, r, HALF_PI, Math.PI + HALF_PI);\n    } else if (top < bottom) {\n      ctx.arc(left, top, r, -Math.PI, 0);\n      ctx.arc(left, bottom, r, 0, Math.PI);\n    } else {\n      ctx.arc(left, top, r, -Math.PI, Math.PI);\n    }\n    ctx.closePath();\n    ctx.moveTo(x, y);\n  } else {\n    ctx.rect(x, y, w, h);\n  }\n}\n\nfunction drawFrame(ctx, rect, model) {\n  var bgColor = model.backgroundColor;\n  var borderColor = model.borderColor;\n  var borderWidth = model.borderWidth;\n\n  if (!bgColor && (!borderColor || !borderWidth)) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  drawRoundedRect(\n    ctx,\n    rasterize(rect.x) + borderWidth / 2,\n    rasterize(rect.y) + borderWidth / 2,\n    rasterize(rect.w) - borderWidth,\n    rasterize(rect.h) - borderWidth,\n    model.borderRadius);\n\n  ctx.closePath();\n\n  if (bgColor) {\n    ctx.fillStyle = bgColor;\n    ctx.fill();\n  }\n\n  if (borderColor && borderWidth) {\n    ctx.strokeStyle = borderColor;\n    ctx.lineWidth = borderWidth;\n    ctx.lineJoin = 'miter';\n    ctx.stroke();\n  }\n}\n\nfunction textGeometry(rect, align, font) {\n  var h = font.lineHeight;\n  var w = rect.w;\n  var x = rect.x;\n  var y = rect.y + h / 2;\n\n  if (align === 'center') {\n    x += w / 2;\n  } else if (align === 'end' || align === 'right') {\n    x += w;\n  }\n\n  return {\n    h: h,\n    w: w,\n    x: x,\n    y: y\n  };\n}\n\nfunction drawTextLine(ctx, text, cfg) {\n  var shadow = ctx.shadowBlur;\n  var stroked = cfg.stroked;\n  var x = rasterize(cfg.x);\n  var y = rasterize(cfg.y);\n  var w = rasterize(cfg.w);\n\n  if (stroked) {\n    ctx.strokeText(text, x, y, w);\n  }\n\n  if (cfg.filled) {\n    if (shadow && stroked) {\n      // Prevent drawing shadow on both the text stroke and fill, so\n      // if the text is stroked, remove the shadow for the text fill.\n      ctx.shadowBlur = 0;\n    }\n\n    ctx.fillText(text, x, y, w);\n\n    if (shadow && stroked) {\n      ctx.shadowBlur = shadow;\n    }\n  }\n}\n\nfunction drawText(ctx, lines, rect, model) {\n  var align = model.textAlign;\n  var color = model.color;\n  var filled = !!color;\n  var font = model.font;\n  var ilen = lines.length;\n  var strokeColor = model.textStrokeColor;\n  var strokeWidth = model.textStrokeWidth;\n  var stroked = strokeColor && strokeWidth;\n  var i;\n\n  if (!ilen || (!filled && !stroked)) {\n    return;\n  }\n\n  // Adjust coordinates based on text alignment and line height\n  rect = textGeometry(rect, align, font);\n\n  ctx.font = font.string;\n  ctx.textAlign = align;\n  ctx.textBaseline = 'middle';\n  ctx.shadowBlur = model.textShadowBlur;\n  ctx.shadowColor = model.textShadowColor;\n\n  if (filled) {\n    ctx.fillStyle = color;\n  }\n  if (stroked) {\n    ctx.lineJoin = 'round';\n    ctx.lineWidth = strokeWidth;\n    ctx.strokeStyle = strokeColor;\n  }\n\n  for (i = 0, ilen = lines.length; i < ilen; ++i) {\n    drawTextLine(ctx, lines[i], {\n      stroked: stroked,\n      filled: filled,\n      w: rect.w,\n      x: rect.x,\n      y: rect.y + rect.h * i\n    });\n  }\n}\n\nvar Label = function(config, ctx, el, index) {\n  var me = this;\n\n  me._config = config;\n  me._index = index;\n  me._model = null;\n  me._rects = null;\n  me._ctx = ctx;\n  me._el = el;\n};\n\nmerge(Label.prototype, {\n  /**\n   * @private\n   */\n  _modelize: function(display, lines, config, context) {\n    var me = this;\n    var index = me._index;\n    var font = toFont(resolve([config.font, {}], context, index));\n    var color = resolve([config.color, defaults$1.color], context, index);\n\n    return {\n      align: resolve([config.align, 'center'], context, index),\n      anchor: resolve([config.anchor, 'center'], context, index),\n      area: context.chart.chartArea,\n      backgroundColor: resolve([config.backgroundColor, null], context, index),\n      borderColor: resolve([config.borderColor, null], context, index),\n      borderRadius: resolve([config.borderRadius, 0], context, index),\n      borderWidth: resolve([config.borderWidth, 0], context, index),\n      clamp: resolve([config.clamp, false], context, index),\n      clip: resolve([config.clip, false], context, index),\n      color: color,\n      display: display,\n      font: font,\n      lines: lines,\n      offset: resolve([config.offset, 4], context, index),\n      opacity: resolve([config.opacity, 1], context, index),\n      origin: getScaleOrigin(me._el, context),\n      padding: toPadding(resolve([config.padding, 4], context, index)),\n      positioner: getPositioner(me._el),\n      rotation: resolve([config.rotation, 0], context, index) * (Math.PI / 180),\n      size: utils.textSize(me._ctx, lines, font),\n      textAlign: resolve([config.textAlign, 'start'], context, index),\n      textShadowBlur: resolve([config.textShadowBlur, 0], context, index),\n      textShadowColor: resolve([config.textShadowColor, color], context, index),\n      textStrokeColor: resolve([config.textStrokeColor, color], context, index),\n      textStrokeWidth: resolve([config.textStrokeWidth, 0], context, index)\n    };\n  },\n\n  update: function(context) {\n    var me = this;\n    var model = null;\n    var rects = null;\n    var index = me._index;\n    var config = me._config;\n    var value, label, lines;\n\n    // We first resolve the display option (separately) to avoid computing\n    // other options in case the label is hidden (i.e. display: false).\n    var display = resolve([config.display, true], context, index);\n\n    if (display) {\n      value = context.dataset.data[index];\n      label = valueOrDefault(callback(config.formatter, [value, context]), value);\n      lines = isNullOrUndef(label) ? [] : utils.toTextLines(label);\n\n      if (lines.length) {\n        model = me._modelize(display, lines, config, context);\n        rects = boundingRects(model);\n      }\n    }\n\n    me._model = model;\n    me._rects = rects;\n  },\n\n  geometry: function() {\n    return this._rects ? this._rects.frame : {};\n  },\n\n  rotation: function() {\n    return this._model ? this._model.rotation : 0;\n  },\n\n  visible: function() {\n    return this._model && this._model.opacity;\n  },\n\n  model: function() {\n    return this._model;\n  },\n\n  draw: function(chart, center) {\n    var me = this;\n    var ctx = chart.ctx;\n    var model = me._model;\n    var rects = me._rects;\n    var area;\n\n    if (!this.visible()) {\n      return;\n    }\n\n    ctx.save();\n\n    if (model.clip) {\n      area = model.area;\n      ctx.beginPath();\n      ctx.rect(\n        area.left,\n        area.top,\n        area.right - area.left,\n        area.bottom - area.top);\n      ctx.clip();\n    }\n\n    ctx.globalAlpha = utils.bound(0, model.opacity, 1);\n    ctx.translate(rasterize(center.x), rasterize(center.y));\n    ctx.rotate(model.rotation);\n\n    drawFrame(ctx, rects.frame, model);\n    drawText(ctx, model.lines, rects.text, model);\n\n    ctx.restore();\n  }\n});\n\nvar MIN_INTEGER = Number.MIN_SAFE_INTEGER || -9007199254740991; // eslint-disable-line es/no-number-minsafeinteger\nvar MAX_INTEGER = Number.MAX_SAFE_INTEGER || 9007199254740991;  // eslint-disable-line es/no-number-maxsafeinteger\n\nfunction rotated(point, center, angle) {\n  var cos = Math.cos(angle);\n  var sin = Math.sin(angle);\n  var cx = center.x;\n  var cy = center.y;\n\n  return {\n    x: cx + cos * (point.x - cx) - sin * (point.y - cy),\n    y: cy + sin * (point.x - cx) + cos * (point.y - cy)\n  };\n}\n\nfunction projected(points, axis) {\n  var min = MAX_INTEGER;\n  var max = MIN_INTEGER;\n  var origin = axis.origin;\n  var i, pt, vx, vy, dp;\n\n  for (i = 0; i < points.length; ++i) {\n    pt = points[i];\n    vx = pt.x - origin.x;\n    vy = pt.y - origin.y;\n    dp = axis.vx * vx + axis.vy * vy;\n    min = Math.min(min, dp);\n    max = Math.max(max, dp);\n  }\n\n  return {\n    min: min,\n    max: max\n  };\n}\n\nfunction toAxis(p0, p1) {\n  var vx = p1.x - p0.x;\n  var vy = p1.y - p0.y;\n  var ln = Math.sqrt(vx * vx + vy * vy);\n\n  return {\n    vx: (p1.x - p0.x) / ln,\n    vy: (p1.y - p0.y) / ln,\n    origin: p0,\n    ln: ln\n  };\n}\n\nvar HitBox = function() {\n  this._rotation = 0;\n  this._rect = {\n    x: 0,\n    y: 0,\n    w: 0,\n    h: 0\n  };\n};\n\nmerge(HitBox.prototype, {\n  center: function() {\n    var r = this._rect;\n    return {\n      x: r.x + r.w / 2,\n      y: r.y + r.h / 2\n    };\n  },\n\n  update: function(center, rect, rotation) {\n    this._rotation = rotation;\n    this._rect = {\n      x: rect.x + center.x,\n      y: rect.y + center.y,\n      w: rect.w,\n      h: rect.h\n    };\n  },\n\n  contains: function(point) {\n    var me = this;\n    var margin = 1;\n    var rect = me._rect;\n\n    point = rotated(point, me.center(), -me._rotation);\n\n    return !(point.x < rect.x - margin\n      || point.y < rect.y - margin\n      || point.x > rect.x + rect.w + margin * 2\n      || point.y > rect.y + rect.h + margin * 2);\n  },\n\n  // Separating Axis Theorem\n  // https://gamedevelopment.tutsplus.com/tutorials/collision-detection-using-the-separating-axis-theorem--gamedev-169\n  intersects: function(other) {\n    var r0 = this._points();\n    var r1 = other._points();\n    var axes = [\n      toAxis(r0[0], r0[1]),\n      toAxis(r0[0], r0[3])\n    ];\n    var i, pr0, pr1;\n\n    if (this._rotation !== other._rotation) {\n      // Only separate with r1 axis if the rotation is different,\n      // else it's enough to separate r0 and r1 with r0 axis only!\n      axes.push(\n        toAxis(r1[0], r1[1]),\n        toAxis(r1[0], r1[3])\n      );\n    }\n\n    for (i = 0; i < axes.length; ++i) {\n      pr0 = projected(r0, axes[i]);\n      pr1 = projected(r1, axes[i]);\n\n      if (pr0.max < pr1.min || pr1.max < pr0.min) {\n        return false;\n      }\n    }\n\n    return true;\n  },\n\n  /**\n   * @private\n   */\n  _points: function() {\n    var me = this;\n    var rect = me._rect;\n    var angle = me._rotation;\n    var center = me.center();\n\n    return [\n      rotated({x: rect.x, y: rect.y}, center, angle),\n      rotated({x: rect.x + rect.w, y: rect.y}, center, angle),\n      rotated({x: rect.x + rect.w, y: rect.y + rect.h}, center, angle),\n      rotated({x: rect.x, y: rect.y + rect.h}, center, angle)\n    ];\n  }\n});\n\nfunction coordinates(el, model, geometry) {\n  var point = model.positioner(el, model);\n  var vx = point.vx;\n  var vy = point.vy;\n\n  if (!vx && !vy) {\n    // if aligned center, we don't want to offset the center point\n    return {x: point.x, y: point.y};\n  }\n\n  var w = geometry.w;\n  var h = geometry.h;\n\n  // take in account the label rotation\n  var rotation = model.rotation;\n  var dx = Math.abs(w / 2 * Math.cos(rotation)) + Math.abs(h / 2 * Math.sin(rotation));\n  var dy = Math.abs(w / 2 * Math.sin(rotation)) + Math.abs(h / 2 * Math.cos(rotation));\n\n  // scale the unit vector (vx, vy) to get at least dx or dy equal to\n  // w or h respectively (else we would calculate the distance to the\n  // ellipse inscribed in the bounding rect)\n  var vs = 1 / Math.max(Math.abs(vx), Math.abs(vy));\n  dx *= vx * vs;\n  dy *= vy * vs;\n\n  // finally, include the explicit offset\n  dx += model.offset * vx;\n  dy += model.offset * vy;\n\n  return {\n    x: point.x + dx,\n    y: point.y + dy\n  };\n}\n\nfunction collide(labels, collider) {\n  var i, j, s0, s1;\n\n  // IMPORTANT Iterate in the reverse order since items at the end of the\n  // list have an higher weight/priority and thus should be less impacted\n  // by the overlapping strategy.\n\n  for (i = labels.length - 1; i >= 0; --i) {\n    s0 = labels[i].$layout;\n\n    for (j = i - 1; j >= 0 && s0._visible; --j) {\n      s1 = labels[j].$layout;\n\n      if (s1._visible && s0._box.intersects(s1._box)) {\n        collider(s0, s1);\n      }\n    }\n  }\n\n  return labels;\n}\n\nfunction compute(labels) {\n  var i, ilen, label, state, geometry, center, proxy;\n\n  // Initialize labels for overlap detection\n  for (i = 0, ilen = labels.length; i < ilen; ++i) {\n    label = labels[i];\n    state = label.$layout;\n\n    if (state._visible) {\n      // Chart.js 3 removed el._model in favor of getProps(), making harder to\n      // abstract reading values in positioners. Also, using string arrays to\n      // read values (i.e. var {a,b,c} = el.getProps([\"a\",\"b\",\"c\"])) would make\n      // positioners inefficient in the normal case (i.e. not the final values)\n      // and the code a bit ugly, so let's use a Proxy instead.\n      proxy = new Proxy(label._el, {get: (el, p) => el.getProps([p], true)[p]});\n\n      geometry = label.geometry();\n      center = coordinates(proxy, label.model(), geometry);\n      state._box.update(center, geometry, label.rotation());\n    }\n  }\n\n  // Auto hide overlapping labels\n  return collide(labels, function(s0, s1) {\n    var h0 = s0._hidable;\n    var h1 = s1._hidable;\n\n    if ((h0 && h1) || h1) {\n      s1._visible = false;\n    } else if (h0) {\n      s0._visible = false;\n    }\n  });\n}\n\nvar layout = {\n  prepare: function(datasets) {\n    var labels = [];\n    var i, j, ilen, jlen, label;\n\n    for (i = 0, ilen = datasets.length; i < ilen; ++i) {\n      for (j = 0, jlen = datasets[i].length; j < jlen; ++j) {\n        label = datasets[i][j];\n        labels.push(label);\n        label.$layout = {\n          _box: new HitBox(),\n          _hidable: false,\n          _visible: true,\n          _set: i,\n          _idx: label._index\n        };\n      }\n    }\n\n    // TODO New `z` option: labels with a higher z-index are drawn\n    // of top of the ones with a lower index. Lowest z-index labels\n    // are also discarded first when hiding overlapping labels.\n    labels.sort(function(a, b) {\n      var sa = a.$layout;\n      var sb = b.$layout;\n\n      return sa._idx === sb._idx\n        ? sb._set - sa._set\n        : sb._idx - sa._idx;\n    });\n\n    this.update(labels);\n\n    return labels;\n  },\n\n  update: function(labels) {\n    var dirty = false;\n    var i, ilen, label, model, state;\n\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      model = label.model();\n      state = label.$layout;\n      state._hidable = model && model.display === 'auto';\n      state._visible = label.visible();\n      dirty |= state._hidable;\n    }\n\n    if (dirty) {\n      compute(labels);\n    }\n  },\n\n  lookup: function(labels, point) {\n    var i, state;\n\n    // IMPORTANT Iterate in the reverse order since items at the end of\n    // the list have an higher z-index, thus should be picked first.\n\n    for (i = labels.length - 1; i >= 0; --i) {\n      state = labels[i].$layout;\n\n      if (state && state._visible && state._box.contains(point)) {\n        return labels[i];\n      }\n    }\n\n    return null;\n  },\n\n  draw: function(chart, labels) {\n    var i, ilen, label, state, geometry, center;\n\n    for (i = 0, ilen = labels.length; i < ilen; ++i) {\n      label = labels[i];\n      state = label.$layout;\n\n      if (state._visible) {\n        geometry = label.geometry();\n        center = coordinates(label._el, label.model(), geometry);\n        state._box.update(center, geometry, label.rotation());\n        label.draw(chart, center);\n      }\n    }\n  }\n};\n\nvar formatter = function(value) {\n  if (isNullOrUndef(value)) {\n    return null;\n  }\n\n  var label = value;\n  var keys, klen, k;\n  if (isObject(value)) {\n    if (!isNullOrUndef(value.label)) {\n      label = value.label;\n    } else if (!isNullOrUndef(value.r)) {\n      label = value.r;\n    } else {\n      label = '';\n      keys = Object.keys(value);\n      for (k = 0, klen = keys.length; k < klen; ++k) {\n        label += (k !== 0 ? ', ' : '') + keys[k] + ': ' + value[keys[k]];\n      }\n    }\n  }\n\n  return '' + label;\n};\n\n/**\n * IMPORTANT: make sure to also update tests and TypeScript definition\n * files (`/test/specs/defaults.spec.js` and `/types/options.d.ts`)\n */\n\nvar defaults = {\n  align: 'center',\n  anchor: 'center',\n  backgroundColor: null,\n  borderColor: null,\n  borderRadius: 0,\n  borderWidth: 0,\n  clamp: false,\n  clip: false,\n  color: undefined,\n  display: true,\n  font: {\n    family: undefined,\n    lineHeight: 1.2,\n    size: undefined,\n    style: undefined,\n    weight: null\n  },\n  formatter: formatter,\n  labels: undefined,\n  listeners: {},\n  offset: 4,\n  opacity: 1,\n  padding: {\n    top: 4,\n    right: 4,\n    bottom: 4,\n    left: 4\n  },\n  rotation: 0,\n  textAlign: 'start',\n  textStrokeColor: undefined,\n  textStrokeWidth: 0,\n  textShadowBlur: 0,\n  textShadowColor: undefined\n};\n\n/**\n * @see https://github.com/chartjs/Chart.js/issues/4176\n */\n\nvar EXPANDO_KEY = '$datalabels';\nvar DEFAULT_KEY = '$default';\n\nfunction configure(dataset, options) {\n  var override = dataset.datalabels;\n  var listeners = {};\n  var configs = [];\n  var labels, keys;\n\n  if (override === false) {\n    return null;\n  }\n  if (override === true) {\n    override = {};\n  }\n\n  options = merge({}, [options, override]);\n  labels = options.labels || {};\n  keys = Object.keys(labels);\n  delete options.labels;\n\n  if (keys.length) {\n    keys.forEach(function(key) {\n      if (labels[key]) {\n        configs.push(merge({}, [\n          options,\n          labels[key],\n          {_key: key}\n        ]));\n      }\n    });\n  } else {\n    // Default label if no \"named\" label defined.\n    configs.push(options);\n  }\n\n  // listeners: {<event-type>: {<label-key>: <fn>}}\n  listeners = configs.reduce(function(target, config) {\n    each(config.listeners || {}, function(fn, event) {\n      target[event] = target[event] || {};\n      target[event][config._key || DEFAULT_KEY] = fn;\n    });\n\n    delete config.listeners;\n    return target;\n  }, {});\n\n  return {\n    labels: configs,\n    listeners: listeners\n  };\n}\n\nfunction dispatchEvent(chart, listeners, label, event) {\n  if (!listeners) {\n    return;\n  }\n\n  var context = label.$context;\n  var groups = label.$groups;\n  var callback$1;\n\n  if (!listeners[groups._set]) {\n    return;\n  }\n\n  callback$1 = listeners[groups._set][groups._key];\n  if (!callback$1) {\n    return;\n  }\n\n  if (callback(callback$1, [context, event]) === true) {\n    // Users are allowed to tweak the given context by injecting values that can be\n    // used in scriptable options to display labels differently based on the current\n    // event (e.g. highlight an hovered label). That's why we update the label with\n    // the output context and schedule a new chart render by setting it dirty.\n    chart[EXPANDO_KEY]._dirty = true;\n    label.update(context);\n  }\n}\n\nfunction dispatchMoveEvents(chart, listeners, previous, label, event) {\n  var enter, leave;\n\n  if (!previous && !label) {\n    return;\n  }\n\n  if (!previous) {\n    enter = true;\n  } else if (!label) {\n    leave = true;\n  } else if (previous !== label) {\n    leave = enter = true;\n  }\n\n  if (leave) {\n    dispatchEvent(chart, listeners.leave, previous, event);\n  }\n  if (enter) {\n    dispatchEvent(chart, listeners.enter, label, event);\n  }\n}\n\nfunction handleMoveEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var listeners = expando._listeners;\n  var previous, label;\n\n  if (!listeners.enter && !listeners.leave) {\n    return;\n  }\n\n  if (event.type === 'mousemove') {\n    label = layout.lookup(expando._labels, event);\n  } else if (event.type !== 'mouseout') {\n    return;\n  }\n\n  previous = expando._hovered;\n  expando._hovered = label;\n  dispatchMoveEvents(chart, listeners, previous, label, event);\n}\n\nfunction handleClickEvents(chart, event) {\n  var expando = chart[EXPANDO_KEY];\n  var handlers = expando._listeners.click;\n  var label = handlers && layout.lookup(expando._labels, event);\n  if (label) {\n    dispatchEvent(chart, handlers, label, event);\n  }\n}\n\nvar plugin = {\n  id: 'datalabels',\n\n  defaults: defaults,\n\n  beforeInit: function(chart) {\n    chart[EXPANDO_KEY] = {\n      _actives: []\n    };\n  },\n\n  beforeUpdate: function(chart) {\n    var expando = chart[EXPANDO_KEY];\n    expando._listened = false;\n    expando._listeners = {};     // {<event-type>: {<dataset-index>: {<label-key>: <fn>}}}\n    expando._datasets = [];      // per dataset labels: [Label[]]\n    expando._labels = [];        // layouted labels: Label[]\n  },\n\n  afterDatasetUpdate: function(chart, args, options) {\n    var datasetIndex = args.index;\n    var expando = chart[EXPANDO_KEY];\n    var labels = expando._datasets[datasetIndex] = [];\n    var visible = chart.isDatasetVisible(datasetIndex);\n    var dataset = chart.data.datasets[datasetIndex];\n    var config = configure(dataset, options);\n    var elements = args.meta.data || [];\n    var ctx = chart.ctx;\n    var i, j, ilen, jlen, cfg, key, el, label;\n\n    ctx.save();\n\n    for (i = 0, ilen = elements.length; i < ilen; ++i) {\n      el = elements[i];\n      el[EXPANDO_KEY] = [];\n\n      if (visible && el && chart.getDataVisibility(i) && !el.skip) {\n        for (j = 0, jlen = config.labels.length; j < jlen; ++j) {\n          cfg = config.labels[j];\n          key = cfg._key;\n\n          label = new Label(cfg, ctx, el, i);\n          label.$groups = {\n            _set: datasetIndex,\n            _key: key || DEFAULT_KEY\n          };\n          label.$context = {\n            active: false,\n            chart: chart,\n            dataIndex: i,\n            dataset: dataset,\n            datasetIndex: datasetIndex\n          };\n\n          label.update(label.$context);\n          el[EXPANDO_KEY].push(label);\n          labels.push(label);\n        }\n      }\n    }\n\n    ctx.restore();\n\n    // Store listeners at the chart level and per event type to optimize\n    // cases where no listeners are registered for a specific event.\n    merge(expando._listeners, config.listeners, {\n      merger: function(event, target, source) {\n        target[event] = target[event] || {};\n        target[event][args.index] = source[event];\n        expando._listened = true;\n      }\n    });\n  },\n\n  afterUpdate: function(chart) {\n    chart[EXPANDO_KEY]._labels = layout.prepare(chart[EXPANDO_KEY]._datasets);\n  },\n\n  // Draw labels on top of all dataset elements\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/29\n  // https://github.com/chartjs/chartjs-plugin-datalabels/issues/32\n  afterDatasetsDraw: function(chart) {\n    layout.draw(chart, chart[EXPANDO_KEY]._labels);\n  },\n\n  beforeEvent: function(chart, args) {\n    // If there is no listener registered for this chart, `listened` will be false,\n    // meaning we can immediately ignore the incoming event and avoid useless extra\n    // computation for users who don't implement label interactions.\n    if (chart[EXPANDO_KEY]._listened) {\n      var event = args.event;\n      switch (event.type) {\n      case 'mousemove':\n      case 'mouseout':\n        handleMoveEvents(chart, event);\n        break;\n      case 'click':\n        handleClickEvents(chart, event);\n        break;\n      }\n    }\n  },\n\n  afterEvent: function(chart) {\n    var expando = chart[EXPANDO_KEY];\n    var previous = expando._actives;\n    var actives = expando._actives = chart.getActiveElements();\n    var updates = utils.arrayDiff(previous, actives);\n    var i, ilen, j, jlen, update, label, labels;\n\n    for (i = 0, ilen = updates.length; i < ilen; ++i) {\n      update = updates[i];\n      if (update[1]) {\n        labels = update[0].element[EXPANDO_KEY] || [];\n        for (j = 0, jlen = labels.length; j < jlen; ++j) {\n          label = labels[j];\n          label.$context.active = (update[1] === 1);\n          label.update(label.$context);\n        }\n      }\n    }\n\n    if (expando._dirty || updates.length) {\n      layout.update(expando._labels);\n      chart.render();\n    }\n\n    delete expando._dirty;\n  }\n};\n\nexport { plugin as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,aAAa,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,QAAQ,kBAAkB;AAC7H,SAASC,QAAQ,IAAIC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,UAAU,QAAQ,UAAU;AAEvF,IAAIC,gBAAgB,GAAI,YAAW;EACjC,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACjC,IAAIA,MAAM,CAACD,gBAAgB,EAAE;MAC3B,OAAOC,MAAM,CAACD,gBAAgB;IAChC;;IAEA;IACA;IACA;IACA,IAAIE,MAAM,GAAGD,MAAM,CAACC,MAAM;IAC1B,IAAIA,MAAM,EAAE;MACV,OAAO,CAACA,MAAM,CAACC,UAAU,IAAI,CAAC,KAAKD,MAAM,CAACE,WAAW,IAAI,CAAC,CAAC;IAC7D;EACF;EAEA,OAAO,CAAC;AACV,CAAC,CAAC,CAAE;AAEJ,IAAIC,KAAK,GAAG;EACV;EACAC,WAAW,EAAE,SAAAA,CAASC,MAAM,EAAE;IAC5B,IAAIC,KAAK,GAAG,EAAE;IACd,IAAIC,KAAK;IAETF,MAAM,GAAG,EAAE,CAACG,MAAM,CAACH,MAAM,CAAC;IAC1B,OAAOA,MAAM,CAACI,MAAM,EAAE;MACpBF,KAAK,GAAGF,MAAM,CAACK,GAAG,CAAC,CAAC;MACpB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;QAC7BD,KAAK,CAACK,OAAO,CAACC,KAAK,CAACN,KAAK,EAAEC,KAAK,CAACM,KAAK,CAAC,IAAI,CAAC,CAAC;MAC/C,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,EAAE;QAC/BF,MAAM,CAACW,IAAI,CAACJ,KAAK,CAACP,MAAM,EAAEE,KAAK,CAAC;MAClC,CAAC,MAAM,IAAI,CAACvB,aAAa,CAACqB,MAAM,CAAC,EAAE;QACjCC,KAAK,CAACK,OAAO,CAAC,EAAE,GAAGJ,KAAK,CAAC;MAC3B;IACF;IAEA,OAAOD,KAAK;EACd,CAAC;EAED;EACA;EACAW,QAAQ,EAAE,SAAAA,CAASC,GAAG,EAAEZ,KAAK,EAAEa,IAAI,EAAE;IACnC,IAAIC,KAAK,GAAG,EAAE,CAACZ,MAAM,CAACF,KAAK,CAAC;IAC5B,IAAIe,IAAI,GAAGD,KAAK,CAACX,MAAM;IACvB,IAAIa,IAAI,GAAGJ,GAAG,CAACC,IAAI;IACnB,IAAII,KAAK,GAAG,CAAC;IACb,IAAIC,CAAC;IAELN,GAAG,CAACC,IAAI,GAAGA,IAAI,CAACM,MAAM;IAEtB,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MACzBD,KAAK,GAAGG,IAAI,CAACC,GAAG,CAACT,GAAG,CAACU,WAAW,CAACR,KAAK,CAACI,CAAC,CAAC,CAAC,CAACD,KAAK,EAAEA,KAAK,CAAC;IAC1D;IAEAL,GAAG,CAACC,IAAI,GAAGG,IAAI;IAEf,OAAO;MACLO,MAAM,EAAER,IAAI,GAAGF,IAAI,CAACW,UAAU;MAC9BP,KAAK,EAAEA;IACT,CAAC;EACH,CAAC;EAED;AACF;AACA;AACA;AACA;EACEQ,KAAK,EAAE,SAAAA,CAASC,GAAG,EAAEC,KAAK,EAAEN,GAAG,EAAE;IAC/B,OAAOD,IAAI,CAACC,GAAG,CAACK,GAAG,EAAEN,IAAI,CAACM,GAAG,CAACC,KAAK,EAAEN,GAAG,CAAC,CAAC;EAC5C,CAAC;EAED;AACF;AACA;AACA;AACA;EACEO,SAAS,EAAE,SAAAA,CAASC,EAAE,EAAEC,EAAE,EAAE;IAC1B,IAAId,IAAI,GAAGa,EAAE,CAACE,KAAK,CAAC,CAAC;IACrB,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAId,CAAC,EAAEe,CAAC,EAAElB,IAAI,EAAEmB,CAAC;IAEjB,KAAKhB,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGe,EAAE,CAAC3B,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MAC3CgB,CAAC,GAAGJ,EAAE,CAACZ,CAAC,CAAC;MACTe,CAAC,GAAGjB,IAAI,CAACmB,OAAO,CAACD,CAAC,CAAC;MAEnB,IAAID,CAAC,KAAK,CAAC,CAAC,EAAE;QACZD,OAAO,CAACtB,IAAI,CAAC,CAACwB,CAAC,EAAE,CAAC,CAAC,CAAC;MACtB,CAAC,MAAM;QACLlB,IAAI,CAACoB,MAAM,CAACH,CAAC,EAAE,CAAC,CAAC;MACnB;IACF;IAEA,KAAKf,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGC,IAAI,CAACb,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MAC7Cc,OAAO,CAACtB,IAAI,CAAC,CAACM,IAAI,CAACE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7B;IAEA,OAAOc,OAAO;EAChB,CAAC;EAED;AACF;AACA;EACEK,SAAS,EAAE,SAAAA,CAASH,CAAC,EAAE;IACrB,OAAOd,IAAI,CAACkB,KAAK,CAACJ,CAAC,GAAG1C,gBAAgB,CAAC,GAAGA,gBAAgB;EAC5D;AACF,CAAC;AAED,SAAS+C,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7B,IAAIC,EAAE,GAAGD,MAAM,CAACE,CAAC;EACjB,IAAIC,EAAE,GAAGH,MAAM,CAACI,CAAC;EAEjB,IAAIH,EAAE,KAAK,IAAI,EAAE;IACf,OAAO;MAACC,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE,CAAC;IAAC,CAAC;EACtB;EACA,IAAID,EAAE,KAAK,IAAI,EAAE;IACf,OAAO;MAACD,CAAC,EAAE,CAAC;MAAEE,CAAC,EAAE;IAAC,CAAC;EACrB;EAEA,IAAIC,EAAE,GAAGN,KAAK,CAACG,CAAC,GAAGD,EAAE;EACrB,IAAIK,EAAE,GAAGP,KAAK,CAACK,CAAC,GAAGD,EAAE;EACrB,IAAII,EAAE,GAAG5B,IAAI,CAAC6B,IAAI,CAACH,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EAErC,OAAO;IACLJ,CAAC,EAAEK,EAAE,GAAGF,EAAE,GAAGE,EAAE,GAAG,CAAC;IACnBH,CAAC,EAAEG,EAAE,GAAGD,EAAE,GAAGC,EAAE,GAAG,CAAC;EACrB,CAAC;AACH;AAEA,SAASE,OAAOA,CAACP,CAAC,EAAEE,CAAC,EAAEM,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;EACpC,QAAQA,KAAK;IACb,KAAK,QAAQ;MACXF,EAAE,GAAGC,EAAE,GAAG,CAAC;MACX;IACF,KAAK,QAAQ;MACXD,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACN;IACF,KAAK,OAAO;MACVD,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MACN;IACF,KAAK,MAAM;MACTD,EAAE,GAAG,CAAC,CAAC;MACPC,EAAE,GAAG,CAAC;MACN;IACF,KAAK,KAAK;MACRD,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC,CAAC;MACP;IACF,KAAK,OAAO;MACVD,EAAE,GAAG,CAACA,EAAE;MACRC,EAAE,GAAG,CAACA,EAAE;MACR;IACF,KAAK,KAAK;MACR;MACA;IACF;MACE;MACAC,KAAK,IAAKjC,IAAI,CAACkC,EAAE,GAAG,GAAI;MACxBH,EAAE,GAAG/B,IAAI,CAACmC,GAAG,CAACF,KAAK,CAAC;MACpBD,EAAE,GAAGhC,IAAI,CAACoC,GAAG,CAACH,KAAK,CAAC;MACpB;EACF;EAEA,OAAO;IACLV,CAAC,EAAEA,CAAC;IACJE,CAAC,EAAEA,CAAC;IACJM,EAAE,EAAEA,EAAE;IACNC,EAAE,EAAEA;EACN,CAAC;AACH;;AAEA;AACA;;AAEA,IAAIK,QAAQ,GAAG,CAAC;AAChB,IAAIC,MAAM,GAAG,CAAC;AACd,IAAIC,OAAO,GAAG,CAAC;AACf,IAAIC,QAAQ,GAAG,CAAC;AAChB,IAAIC,KAAK,GAAG,CAAC;AAEb,SAASC,MAAMA,CAACnB,CAAC,EAAEE,CAAC,EAAEkB,IAAI,EAAE;EAC1B,IAAIC,GAAG,GAAGP,QAAQ;EAElB,IAAId,CAAC,GAAGoB,IAAI,CAACE,IAAI,EAAE;IACjBD,GAAG,IAAIN,MAAM;EACf,CAAC,MAAM,IAAIf,CAAC,GAAGoB,IAAI,CAACG,KAAK,EAAE;IACzBF,GAAG,IAAIL,OAAO;EAChB;EACA,IAAId,CAAC,GAAGkB,IAAI,CAACI,GAAG,EAAE;IAChBH,GAAG,IAAIH,KAAK;EACd,CAAC,MAAM,IAAIhB,CAAC,GAAGkB,IAAI,CAACK,MAAM,EAAE;IAC1BJ,GAAG,IAAIJ,QAAQ;EACjB;EAEA,OAAOI,GAAG;AACZ;AAEA,SAASK,OAAOA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC9B,IAAI7B,EAAE,GAAG4B,OAAO,CAAC5B,EAAE;EACnB,IAAIE,EAAE,GAAG0B,OAAO,CAAC1B,EAAE;EACnB,IAAI4B,EAAE,GAAGF,OAAO,CAACE,EAAE;EACnB,IAAIC,EAAE,GAAGH,OAAO,CAACG,EAAE;EACnB,IAAIC,EAAE,GAAGZ,MAAM,CAACpB,EAAE,EAAEE,EAAE,EAAE2B,IAAI,CAAC;EAC7B,IAAII,EAAE,GAAGb,MAAM,CAACU,EAAE,EAAEC,EAAE,EAAEF,IAAI,CAAC;EAC7B,IAAIK,CAAC,EAAEjC,CAAC,EAAEE,CAAC;;EAEX;EACA,OAAO,IAAI,EAAE;IACX,IAAI,EAAE6B,EAAE,GAAGC,EAAE,CAAC,IAAKD,EAAE,GAAGC,EAAG,EAAE;MAC3B;MACA;IACF;;IAEA;IACAC,CAAC,GAAGF,EAAE,IAAIC,EAAE;IAEZ,IAAIC,CAAC,GAAGf,KAAK,EAAE;MACblB,CAAC,GAAGD,EAAE,GAAG,CAAC8B,EAAE,GAAG9B,EAAE,KAAK6B,IAAI,CAACJ,GAAG,GAAGvB,EAAE,CAAC,IAAI6B,EAAE,GAAG7B,EAAE,CAAC;MAChDC,CAAC,GAAG0B,IAAI,CAACJ,GAAG;IACd,CAAC,MAAM,IAAIS,CAAC,GAAGhB,QAAQ,EAAE;MACvBjB,CAAC,GAAGD,EAAE,GAAG,CAAC8B,EAAE,GAAG9B,EAAE,KAAK6B,IAAI,CAACH,MAAM,GAAGxB,EAAE,CAAC,IAAI6B,EAAE,GAAG7B,EAAE,CAAC;MACnDC,CAAC,GAAG0B,IAAI,CAACH,MAAM;IACjB,CAAC,MAAM,IAAIQ,CAAC,GAAGjB,OAAO,EAAE;MACtBd,CAAC,GAAGD,EAAE,GAAG,CAAC6B,EAAE,GAAG7B,EAAE,KAAK2B,IAAI,CAACL,KAAK,GAAGxB,EAAE,CAAC,IAAI8B,EAAE,GAAG9B,EAAE,CAAC;MAClDC,CAAC,GAAG4B,IAAI,CAACL,KAAK;IAChB,CAAC,MAAM,IAAIU,CAAC,GAAGlB,MAAM,EAAE;MACrBb,CAAC,GAAGD,EAAE,GAAG,CAAC6B,EAAE,GAAG7B,EAAE,KAAK2B,IAAI,CAACN,IAAI,GAAGvB,EAAE,CAAC,IAAI8B,EAAE,GAAG9B,EAAE,CAAC;MACjDC,CAAC,GAAG4B,IAAI,CAACN,IAAI;IACf;IAEA,IAAIW,CAAC,KAAKF,EAAE,EAAE;MACZhC,EAAE,GAAGC,CAAC;MACNC,EAAE,GAAGC,CAAC;MACN6B,EAAE,GAAGZ,MAAM,CAACpB,EAAE,EAAEE,EAAE,EAAE2B,IAAI,CAAC;IAC3B,CAAC,MAAM;MACLC,EAAE,GAAG7B,CAAC;MACN8B,EAAE,GAAG5B,CAAC;MACN8B,EAAE,GAAGb,MAAM,CAACU,EAAE,EAAEC,EAAE,EAAEF,IAAI,CAAC;IAC3B;EACF;EAEA,OAAO;IACL7B,EAAE,EAAEA,EAAE;IACN8B,EAAE,EAAEA,EAAE;IACN5B,EAAE,EAAEA,EAAE;IACN6B,EAAE,EAAEA;EACN,CAAC;AACH;AAEA,SAASI,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAChC,IAAIC,MAAM,GAAGD,MAAM,CAACC,MAAM;EAC1B,IAAIV,OAAO,GAAGQ,KAAK;EACnB,IAAInC,CAAC,EAAEE,CAAC;EAER,IAAIkC,MAAM,CAACE,KAAK,EAAE;IAChBX,OAAO,GAAGD,OAAO,CAACC,OAAO,EAAES,MAAM,CAACR,IAAI,CAAC;EACzC;EAEA,IAAIS,MAAM,KAAK,OAAO,EAAE;IACtBrC,CAAC,GAAG2B,OAAO,CAAC5B,EAAE;IACdG,CAAC,GAAGyB,OAAO,CAAC1B,EAAE;EAChB,CAAC,MAAM,IAAIoC,MAAM,KAAK,KAAK,EAAE;IAC3BrC,CAAC,GAAG2B,OAAO,CAACE,EAAE;IACd3B,CAAC,GAAGyB,OAAO,CAACG,EAAE;EAChB,CAAC,MAAM;IACL9B,CAAC,GAAG,CAAC2B,OAAO,CAAC5B,EAAE,GAAG4B,OAAO,CAACE,EAAE,IAAI,CAAC;IACjC3B,CAAC,GAAG,CAACyB,OAAO,CAAC1B,EAAE,GAAG0B,OAAO,CAACG,EAAE,IAAI,CAAC;EACnC;EAEA,OAAOvB,OAAO,CAACP,CAAC,EAAEE,CAAC,EAAEiC,KAAK,CAAC3B,EAAE,EAAE2B,KAAK,CAAC1B,EAAE,EAAE2B,MAAM,CAAC1B,KAAK,CAAC;AACxD;AAEA,IAAI6B,WAAW,GAAG;EAChBC,GAAG,EAAE,SAAAA,CAASC,EAAE,EAAEL,MAAM,EAAE;IACxB,IAAIM,KAAK,GAAG,CAACD,EAAE,CAACE,UAAU,GAAGF,EAAE,CAACG,QAAQ,IAAI,CAAC;IAC7C,IAAIpC,EAAE,GAAG/B,IAAI,CAACmC,GAAG,CAAC8B,KAAK,CAAC;IACxB,IAAIjC,EAAE,GAAGhC,IAAI,CAACoC,GAAG,CAAC6B,KAAK,CAAC;IACxB,IAAIX,EAAE,GAAGU,EAAE,CAACI,WAAW;IACvB,IAAIb,EAAE,GAAGS,EAAE,CAACK,WAAW;IAEvB,OAAOZ,SAAS,CAAC;MACfnC,EAAE,EAAE0C,EAAE,CAACzC,CAAC,GAAGQ,EAAE,GAAGuB,EAAE;MAClB9B,EAAE,EAAEwC,EAAE,CAACvC,CAAC,GAAGO,EAAE,GAAGsB,EAAE;MAClBF,EAAE,EAAEY,EAAE,CAACzC,CAAC,GAAGQ,EAAE,GAAGwB,EAAE;MAClBF,EAAE,EAAEW,EAAE,CAACvC,CAAC,GAAGO,EAAE,GAAGuB,EAAE;MAClBxB,EAAE,EAAEA,EAAE;MACNC,EAAE,EAAEA;IACN,CAAC,EAAE2B,MAAM,CAAC;EACZ,CAAC;EAEDvC,KAAK,EAAE,SAAAA,CAAS4C,EAAE,EAAEL,MAAM,EAAE;IAC1B,IAAI7C,CAAC,GAAGK,MAAM,CAAC6C,EAAE,EAAEL,MAAM,CAACtC,MAAM,CAAC;IACjC,IAAIiD,EAAE,GAAGxD,CAAC,CAACS,CAAC,GAAGyC,EAAE,CAACO,OAAO,CAACC,MAAM;IAChC,IAAIC,EAAE,GAAG3D,CAAC,CAACW,CAAC,GAAGuC,EAAE,CAACO,OAAO,CAACC,MAAM;IAEhC,OAAOf,SAAS,CAAC;MACfnC,EAAE,EAAE0C,EAAE,CAACzC,CAAC,GAAG+C,EAAE;MACb9C,EAAE,EAAEwC,EAAE,CAACvC,CAAC,GAAGgD,EAAE;MACbrB,EAAE,EAAEY,EAAE,CAACzC,CAAC,GAAG+C,EAAE;MACbjB,EAAE,EAAEW,EAAE,CAACvC,CAAC,GAAGgD,EAAE;MACb1C,EAAE,EAAEjB,CAAC,CAACS,CAAC;MACPS,EAAE,EAAElB,CAAC,CAACW;IACR,CAAC,EAAEkC,MAAM,CAAC;EACZ,CAAC;EAEDe,GAAG,EAAE,SAAAA,CAASV,EAAE,EAAEL,MAAM,EAAE;IACxB,IAAI7C,CAAC,GAAGK,MAAM,CAAC6C,EAAE,EAAEL,MAAM,CAACtC,MAAM,CAAC;IACjC,IAAIE,CAAC,GAAGyC,EAAE,CAACzC,CAAC;IACZ,IAAIE,CAAC,GAAGuC,EAAE,CAACvC,CAAC;IACZ,IAAIkD,EAAE,GAAG,CAAC;IACV,IAAIC,EAAE,GAAG,CAAC;IAEV,IAAIZ,EAAE,CAACa,UAAU,EAAE;MACjBtD,CAAC,GAAGvB,IAAI,CAACM,GAAG,CAAC0D,EAAE,CAACzC,CAAC,EAAEyC,EAAE,CAACc,IAAI,CAAC;MAC3BH,EAAE,GAAG3E,IAAI,CAAC+E,GAAG,CAACf,EAAE,CAACc,IAAI,GAAGd,EAAE,CAACzC,CAAC,CAAC;IAC/B,CAAC,MAAM;MACLE,CAAC,GAAGzB,IAAI,CAACM,GAAG,CAAC0D,EAAE,CAACvC,CAAC,EAAEuC,EAAE,CAACc,IAAI,CAAC;MAC3BF,EAAE,GAAG5E,IAAI,CAAC+E,GAAG,CAACf,EAAE,CAACc,IAAI,GAAGd,EAAE,CAACvC,CAAC,CAAC;IAC/B;IAEA,OAAOgC,SAAS,CAAC;MACfnC,EAAE,EAAEC,CAAC;MACLC,EAAE,EAAEC,CAAC,GAAGmD,EAAE;MACVxB,EAAE,EAAE7B,CAAC,GAAGoD,EAAE;MACVtB,EAAE,EAAE5B,CAAC;MACLM,EAAE,EAAEjB,CAAC,CAACS,CAAC;MACPS,EAAE,EAAElB,CAAC,CAACW;IACR,CAAC,EAAEkC,MAAM,CAAC;EACZ,CAAC;EAEDqB,QAAQ,EAAE,SAAAA,CAAShB,EAAE,EAAEL,MAAM,EAAE;IAC7B,IAAI7C,CAAC,GAAGK,MAAM,CAAC6C,EAAE,EAAEL,MAAM,CAACtC,MAAM,CAAC;IAEjC,OAAOoC,SAAS,CAAC;MACfnC,EAAE,EAAE0C,EAAE,CAACzC,CAAC;MACRC,EAAE,EAAEwC,EAAE,CAACvC,CAAC;MACR2B,EAAE,EAAEY,EAAE,CAACzC,CAAC,IAAIyC,EAAE,CAACnE,KAAK,IAAI,CAAC,CAAC;MAC1BwD,EAAE,EAAEW,EAAE,CAACvC,CAAC,IAAIuC,EAAE,CAAC7D,MAAM,IAAI,CAAC,CAAC;MAC3B4B,EAAE,EAAEjB,CAAC,CAACS,CAAC;MACPS,EAAE,EAAElB,CAAC,CAACW;IACR,CAAC,EAAEkC,MAAM,CAAC;EACZ;AACF,CAAC;AAED,IAAI1C,SAAS,GAAGxC,KAAK,CAACwC,SAAS;AAE/B,SAASgE,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW,IAAI,CAAC;EACxC,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;EAC3B,IAAIC,EAAE,GAAGH,KAAK,CAACI,IAAI,CAACnF,MAAM;EAC1B,IAAIoF,EAAE,GAAGL,KAAK,CAACI,IAAI,CAACzF,KAAK;EACzB,IAAI2F,EAAE,GAAG,CAACD,EAAE,GAAG,CAAC;EAChB,IAAIE,EAAE,GAAG,CAACJ,EAAE,GAAG,CAAC;EAEhB,OAAO;IACLK,KAAK,EAAE;MACLnE,CAAC,EAAEiE,EAAE,GAAGJ,OAAO,CAACvC,IAAI,GAAGsC,WAAW;MAClC1D,CAAC,EAAEgE,EAAE,GAAGL,OAAO,CAACrC,GAAG,GAAGoC,WAAW;MACjCQ,CAAC,EAAEJ,EAAE,GAAGH,OAAO,CAACvF,KAAK,GAAGsF,WAAW,GAAG,CAAC;MACvCS,CAAC,EAAEP,EAAE,GAAGD,OAAO,CAACjF,MAAM,GAAGgF,WAAW,GAAG;IACzC,CAAC;IACDU,IAAI,EAAE;MACJtE,CAAC,EAAEiE,EAAE;MACL/D,CAAC,EAAEgE,EAAE;MACLE,CAAC,EAAEJ,EAAE;MACLK,CAAC,EAAEP;IACL;EACF,CAAC;AACH;AAEA,SAASS,cAAcA,CAAC9B,EAAE,EAAE+B,OAAO,EAAE;EACnC,IAAIC,KAAK,GAAGD,OAAO,CAACE,KAAK,CAACC,cAAc,CAACH,OAAO,CAACI,YAAY,CAAC,CAACC,MAAM;EAErE,IAAI,CAACJ,KAAK,EAAE;IACV,OAAO,IAAI;EACb;EAEA,IAAIA,KAAK,CAACK,OAAO,KAAKC,SAAS,IAAIN,KAAK,CAACO,OAAO,KAAKD,SAAS,EAAE;IAC9D,OAAO;MAAC/E,CAAC,EAAEyE,KAAK,CAACK,OAAO;MAAE5E,CAAC,EAAEuE,KAAK,CAACO;IAAO,CAAC;EAC7C;EAEA,IAAIC,KAAK,GAAGR,KAAK,CAACS,YAAY,CAAC,CAAC;EAChC,OAAOzC,EAAE,CAACa,UAAU,GAClB;IAACtD,CAAC,EAAEiF,KAAK;IAAE/E,CAAC,EAAE;EAAI,CAAC,GACnB;IAACF,CAAC,EAAE,IAAI;IAAEE,CAAC,EAAE+E;EAAK,CAAC;AACvB;AAEA,SAASE,aAAaA,CAAC1C,EAAE,EAAE;EACzB,IAAIA,EAAE,YAAY/F,UAAU,EAAE;IAC5B,OAAO6F,WAAW,CAACC,GAAG;EACxB;EACA,IAAIC,EAAE,YAAY9F,YAAY,EAAE;IAC9B,OAAO4F,WAAW,CAAC1C,KAAK;EAC1B;EACA,IAAI4C,EAAE,YAAY7F,UAAU,EAAE;IAC5B,OAAO2F,WAAW,CAACY,GAAG;EACxB;EACA,OAAOZ,WAAW,CAACkB,QAAQ;AAC7B;AAEA,SAAS2B,eAAeA,CAACnH,GAAG,EAAE+B,CAAC,EAAEE,CAAC,EAAEkE,CAAC,EAAEC,CAAC,EAAEpB,MAAM,EAAE;EAChD,IAAIoC,OAAO,GAAG5G,IAAI,CAACkC,EAAE,GAAG,CAAC;EAEzB,IAAIsC,MAAM,EAAE;IACV,IAAIhB,CAAC,GAAGxD,IAAI,CAACM,GAAG,CAACkE,MAAM,EAAEoB,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,CAAC;IACtC,IAAI9C,IAAI,GAAGtB,CAAC,GAAGiC,CAAC;IAChB,IAAIT,GAAG,GAAGtB,CAAC,GAAG+B,CAAC;IACf,IAAIV,KAAK,GAAGvB,CAAC,GAAGoE,CAAC,GAAGnC,CAAC;IACrB,IAAIR,MAAM,GAAGvB,CAAC,GAAGmE,CAAC,GAAGpC,CAAC;IAEtBhE,GAAG,CAACqH,MAAM,CAACtF,CAAC,EAAEwB,GAAG,CAAC;IAClB,IAAIF,IAAI,GAAGC,KAAK,IAAIC,GAAG,GAAGC,MAAM,EAAE;MAChCxD,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEE,GAAG,EAAES,CAAC,EAAE,CAACxD,IAAI,CAACkC,EAAE,EAAE,CAAC0E,OAAO,CAAC;MACzCpH,GAAG,CAACuE,GAAG,CAACjB,KAAK,EAAEC,GAAG,EAAES,CAAC,EAAE,CAACoD,OAAO,EAAE,CAAC,CAAC;MACnCpH,GAAG,CAACuE,GAAG,CAACjB,KAAK,EAAEE,MAAM,EAAEQ,CAAC,EAAE,CAAC,EAAEoD,OAAO,CAAC;MACrCpH,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEG,MAAM,EAAEQ,CAAC,EAAEoD,OAAO,EAAE5G,IAAI,CAACkC,EAAE,CAAC;IAC5C,CAAC,MAAM,IAAIW,IAAI,GAAGC,KAAK,EAAE;MACvBtD,GAAG,CAACqH,MAAM,CAAChE,IAAI,EAAEpB,CAAC,CAAC;MACnBjC,GAAG,CAACuE,GAAG,CAACjB,KAAK,EAAEC,GAAG,EAAES,CAAC,EAAE,CAACoD,OAAO,EAAEA,OAAO,CAAC;MACzCpH,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEE,GAAG,EAAES,CAAC,EAAEoD,OAAO,EAAE5G,IAAI,CAACkC,EAAE,GAAG0E,OAAO,CAAC;IACnD,CAAC,MAAM,IAAI7D,GAAG,GAAGC,MAAM,EAAE;MACvBxD,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEE,GAAG,EAAES,CAAC,EAAE,CAACxD,IAAI,CAACkC,EAAE,EAAE,CAAC,CAAC;MAClC1C,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEG,MAAM,EAAEQ,CAAC,EAAE,CAAC,EAAExD,IAAI,CAACkC,EAAE,CAAC;IACtC,CAAC,MAAM;MACL1C,GAAG,CAACuE,GAAG,CAAClB,IAAI,EAAEE,GAAG,EAAES,CAAC,EAAE,CAACxD,IAAI,CAACkC,EAAE,EAAElC,IAAI,CAACkC,EAAE,CAAC;IAC1C;IACA1C,GAAG,CAACsH,SAAS,CAAC,CAAC;IACftH,GAAG,CAACqH,MAAM,CAACtF,CAAC,EAAEE,CAAC,CAAC;EAClB,CAAC,MAAM;IACLjC,GAAG,CAACmD,IAAI,CAACpB,CAAC,EAAEE,CAAC,EAAEkE,CAAC,EAAEC,CAAC,CAAC;EACtB;AACF;AAEA,SAASmB,SAASA,CAACvH,GAAG,EAAEmD,IAAI,EAAEuC,KAAK,EAAE;EACnC,IAAI8B,OAAO,GAAG9B,KAAK,CAAC+B,eAAe;EACnC,IAAIC,WAAW,GAAGhC,KAAK,CAACgC,WAAW;EACnC,IAAI/B,WAAW,GAAGD,KAAK,CAACC,WAAW;EAEnC,IAAI,CAAC6B,OAAO,KAAK,CAACE,WAAW,IAAI,CAAC/B,WAAW,CAAC,EAAE;IAC9C;EACF;EAEA3F,GAAG,CAAC2H,SAAS,CAAC,CAAC;EAEfR,eAAe,CACbnH,GAAG,EACHyB,SAAS,CAAC0B,IAAI,CAACpB,CAAC,CAAC,GAAG4D,WAAW,GAAG,CAAC,EACnClE,SAAS,CAAC0B,IAAI,CAAClB,CAAC,CAAC,GAAG0D,WAAW,GAAG,CAAC,EACnClE,SAAS,CAAC0B,IAAI,CAACgD,CAAC,CAAC,GAAGR,WAAW,EAC/BlE,SAAS,CAAC0B,IAAI,CAACiD,CAAC,CAAC,GAAGT,WAAW,EAC/BD,KAAK,CAACkC,YAAY,CAAC;EAErB5H,GAAG,CAACsH,SAAS,CAAC,CAAC;EAEf,IAAIE,OAAO,EAAE;IACXxH,GAAG,CAAC6H,SAAS,GAAGL,OAAO;IACvBxH,GAAG,CAAC8H,IAAI,CAAC,CAAC;EACZ;EAEA,IAAIJ,WAAW,IAAI/B,WAAW,EAAE;IAC9B3F,GAAG,CAAC+H,WAAW,GAAGL,WAAW;IAC7B1H,GAAG,CAACgI,SAAS,GAAGrC,WAAW;IAC3B3F,GAAG,CAACiI,QAAQ,GAAG,OAAO;IACtBjI,GAAG,CAACkI,MAAM,CAAC,CAAC;EACd;AACF;AAEA,SAASC,YAAYA,CAAChF,IAAI,EAAEV,KAAK,EAAExC,IAAI,EAAE;EACvC,IAAImG,CAAC,GAAGnG,IAAI,CAACW,UAAU;EACvB,IAAIuF,CAAC,GAAGhD,IAAI,CAACgD,CAAC;EACd,IAAIpE,CAAC,GAAGoB,IAAI,CAACpB,CAAC;EACd,IAAIE,CAAC,GAAGkB,IAAI,CAAClB,CAAC,GAAGmE,CAAC,GAAG,CAAC;EAEtB,IAAI3D,KAAK,KAAK,QAAQ,EAAE;IACtBV,CAAC,IAAIoE,CAAC,GAAG,CAAC;EACZ,CAAC,MAAM,IAAI1D,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,OAAO,EAAE;IAC/CV,CAAC,IAAIoE,CAAC;EACR;EAEA,OAAO;IACLC,CAAC,EAAEA,CAAC;IACJD,CAAC,EAAEA,CAAC;IACJpE,CAAC,EAAEA,CAAC;IACJE,CAAC,EAAEA;EACL,CAAC;AACH;AAEA,SAASmG,YAAYA,CAACpI,GAAG,EAAEqG,IAAI,EAAEgC,GAAG,EAAE;EACpC,IAAIC,MAAM,GAAGtI,GAAG,CAACuI,UAAU;EAC3B,IAAIC,OAAO,GAAGH,GAAG,CAACG,OAAO;EACzB,IAAIzG,CAAC,GAAGN,SAAS,CAAC4G,GAAG,CAACtG,CAAC,CAAC;EACxB,IAAIE,CAAC,GAAGR,SAAS,CAAC4G,GAAG,CAACpG,CAAC,CAAC;EACxB,IAAIkE,CAAC,GAAG1E,SAAS,CAAC4G,GAAG,CAAClC,CAAC,CAAC;EAExB,IAAIqC,OAAO,EAAE;IACXxI,GAAG,CAACyI,UAAU,CAACpC,IAAI,EAAEtE,CAAC,EAAEE,CAAC,EAAEkE,CAAC,CAAC;EAC/B;EAEA,IAAIkC,GAAG,CAACK,MAAM,EAAE;IACd,IAAIJ,MAAM,IAAIE,OAAO,EAAE;MACrB;MACA;MACAxI,GAAG,CAACuI,UAAU,GAAG,CAAC;IACpB;IAEAvI,GAAG,CAAC2I,QAAQ,CAACtC,IAAI,EAAEtE,CAAC,EAAEE,CAAC,EAAEkE,CAAC,CAAC;IAE3B,IAAImC,MAAM,IAAIE,OAAO,EAAE;MACrBxI,GAAG,CAACuI,UAAU,GAAGD,MAAM;IACzB;EACF;AACF;AAEA,SAASM,QAAQA,CAAC5I,GAAG,EAAEZ,KAAK,EAAE+D,IAAI,EAAEuC,KAAK,EAAE;EACzC,IAAIjD,KAAK,GAAGiD,KAAK,CAACmD,SAAS;EAC3B,IAAIC,KAAK,GAAGpD,KAAK,CAACoD,KAAK;EACvB,IAAIJ,MAAM,GAAG,CAAC,CAACI,KAAK;EACpB,IAAI7I,IAAI,GAAGyF,KAAK,CAACzF,IAAI;EACrB,IAAIE,IAAI,GAAGf,KAAK,CAACG,MAAM;EACvB,IAAIwJ,WAAW,GAAGrD,KAAK,CAACsD,eAAe;EACvC,IAAIC,WAAW,GAAGvD,KAAK,CAACwD,eAAe;EACvC,IAAIV,OAAO,GAAGO,WAAW,IAAIE,WAAW;EACxC,IAAI3I,CAAC;EAEL,IAAI,CAACH,IAAI,IAAK,CAACuI,MAAM,IAAI,CAACF,OAAQ,EAAE;IAClC;EACF;;EAEA;EACArF,IAAI,GAAGgF,YAAY,CAAChF,IAAI,EAAEV,KAAK,EAAExC,IAAI,CAAC;EAEtCD,GAAG,CAACC,IAAI,GAAGA,IAAI,CAACM,MAAM;EACtBP,GAAG,CAAC6I,SAAS,GAAGpG,KAAK;EACrBzC,GAAG,CAACmJ,YAAY,GAAG,QAAQ;EAC3BnJ,GAAG,CAACuI,UAAU,GAAG7C,KAAK,CAAC0D,cAAc;EACrCpJ,GAAG,CAACqJ,WAAW,GAAG3D,KAAK,CAAC4D,eAAe;EAEvC,IAAIZ,MAAM,EAAE;IACV1I,GAAG,CAAC6H,SAAS,GAAGiB,KAAK;EACvB;EACA,IAAIN,OAAO,EAAE;IACXxI,GAAG,CAACiI,QAAQ,GAAG,OAAO;IACtBjI,GAAG,CAACgI,SAAS,GAAGiB,WAAW;IAC3BjJ,GAAG,CAAC+H,WAAW,GAAGgB,WAAW;EAC/B;EAEA,KAAKzI,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGf,KAAK,CAACG,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;IAC9C8H,YAAY,CAACpI,GAAG,EAAEZ,KAAK,CAACkB,CAAC,CAAC,EAAE;MAC1BkI,OAAO,EAAEA,OAAO;MAChBE,MAAM,EAAEA,MAAM;MACdvC,CAAC,EAAEhD,IAAI,CAACgD,CAAC;MACTpE,CAAC,EAAEoB,IAAI,CAACpB,CAAC;MACTE,CAAC,EAAEkB,IAAI,CAAClB,CAAC,GAAGkB,IAAI,CAACiD,CAAC,GAAG9F;IACvB,CAAC,CAAC;EACJ;AACF;AAEA,IAAIiJ,KAAK,GAAG,SAAAA,CAASpF,MAAM,EAAEnE,GAAG,EAAEwE,EAAE,EAAEgF,KAAK,EAAE;EAC3C,IAAIC,EAAE,GAAG,IAAI;EAEbA,EAAE,CAACC,OAAO,GAAGvF,MAAM;EACnBsF,EAAE,CAACE,MAAM,GAAGH,KAAK;EACjBC,EAAE,CAACG,MAAM,GAAG,IAAI;EAChBH,EAAE,CAACI,MAAM,GAAG,IAAI;EAChBJ,EAAE,CAACK,IAAI,GAAG9J,GAAG;EACbyJ,EAAE,CAACM,GAAG,GAAGvF,EAAE;AACb,CAAC;AAEDzG,KAAK,CAACwL,KAAK,CAACS,SAAS,EAAE;EACrB;AACF;AACA;EACEC,SAAS,EAAE,SAAAA,CAASC,OAAO,EAAE9K,KAAK,EAAE+E,MAAM,EAAEoC,OAAO,EAAE;IACnD,IAAIkD,EAAE,GAAG,IAAI;IACb,IAAID,KAAK,GAAGC,EAAE,CAACE,MAAM;IACrB,IAAI1J,IAAI,GAAGjC,MAAM,CAACC,OAAO,CAAC,CAACkG,MAAM,CAAClE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAEsG,OAAO,EAAEiD,KAAK,CAAC,CAAC;IAC7D,IAAIV,KAAK,GAAG7K,OAAO,CAAC,CAACkG,MAAM,CAAC2E,KAAK,EAAEtK,UAAU,CAACsK,KAAK,CAAC,EAAEvC,OAAO,EAAEiD,KAAK,CAAC;IAErE,OAAO;MACL/G,KAAK,EAAExE,OAAO,CAAC,CAACkG,MAAM,CAAC1B,KAAK,EAAE,QAAQ,CAAC,EAAE8D,OAAO,EAAEiD,KAAK,CAAC;MACxDpF,MAAM,EAAEnG,OAAO,CAAC,CAACkG,MAAM,CAACC,MAAM,EAAE,QAAQ,CAAC,EAAEmC,OAAO,EAAEiD,KAAK,CAAC;MAC1D7F,IAAI,EAAE4C,OAAO,CAACE,KAAK,CAAC0D,SAAS;MAC7B1C,eAAe,EAAExJ,OAAO,CAAC,CAACkG,MAAM,CAACsD,eAAe,EAAE,IAAI,CAAC,EAAElB,OAAO,EAAEiD,KAAK,CAAC;MACxE9B,WAAW,EAAEzJ,OAAO,CAAC,CAACkG,MAAM,CAACuD,WAAW,EAAE,IAAI,CAAC,EAAEnB,OAAO,EAAEiD,KAAK,CAAC;MAChE5B,YAAY,EAAE3J,OAAO,CAAC,CAACkG,MAAM,CAACyD,YAAY,EAAE,CAAC,CAAC,EAAErB,OAAO,EAAEiD,KAAK,CAAC;MAC/D7D,WAAW,EAAE1H,OAAO,CAAC,CAACkG,MAAM,CAACwB,WAAW,EAAE,CAAC,CAAC,EAAEY,OAAO,EAAEiD,KAAK,CAAC;MAC7DnF,KAAK,EAAEpG,OAAO,CAAC,CAACkG,MAAM,CAACE,KAAK,EAAE,KAAK,CAAC,EAAEkC,OAAO,EAAEiD,KAAK,CAAC;MACrDY,IAAI,EAAEnM,OAAO,CAAC,CAACkG,MAAM,CAACiG,IAAI,EAAE,KAAK,CAAC,EAAE7D,OAAO,EAAEiD,KAAK,CAAC;MACnDV,KAAK,EAAEA,KAAK;MACZoB,OAAO,EAAEA,OAAO;MAChBjK,IAAI,EAAEA,IAAI;MACVb,KAAK,EAAEA,KAAK;MACZiL,MAAM,EAAEpM,OAAO,CAAC,CAACkG,MAAM,CAACkG,MAAM,EAAE,CAAC,CAAC,EAAE9D,OAAO,EAAEiD,KAAK,CAAC;MACnDc,OAAO,EAAErM,OAAO,CAAC,CAACkG,MAAM,CAACmG,OAAO,EAAE,CAAC,CAAC,EAAE/D,OAAO,EAAEiD,KAAK,CAAC;MACrD3H,MAAM,EAAEyE,cAAc,CAACmD,EAAE,CAACM,GAAG,EAAExD,OAAO,CAAC;MACvCX,OAAO,EAAE1H,SAAS,CAACD,OAAO,CAAC,CAACkG,MAAM,CAACyB,OAAO,EAAE,CAAC,CAAC,EAAEW,OAAO,EAAEiD,KAAK,CAAC,CAAC;MAChEe,UAAU,EAAErD,aAAa,CAACuC,EAAE,CAACM,GAAG,CAAC;MACjCS,QAAQ,EAAEvM,OAAO,CAAC,CAACkG,MAAM,CAACqG,QAAQ,EAAE,CAAC,CAAC,EAAEjE,OAAO,EAAEiD,KAAK,CAAC,IAAIhJ,IAAI,CAACkC,EAAE,GAAG,GAAG,CAAC;MACzEoD,IAAI,EAAE7G,KAAK,CAACc,QAAQ,CAAC0J,EAAE,CAACK,IAAI,EAAE1K,KAAK,EAAEa,IAAI,CAAC;MAC1C4I,SAAS,EAAE5K,OAAO,CAAC,CAACkG,MAAM,CAAC0E,SAAS,EAAE,OAAO,CAAC,EAAEtC,OAAO,EAAEiD,KAAK,CAAC;MAC/DJ,cAAc,EAAEnL,OAAO,CAAC,CAACkG,MAAM,CAACiF,cAAc,EAAE,CAAC,CAAC,EAAE7C,OAAO,EAAEiD,KAAK,CAAC;MACnEF,eAAe,EAAErL,OAAO,CAAC,CAACkG,MAAM,CAACmF,eAAe,EAAER,KAAK,CAAC,EAAEvC,OAAO,EAAEiD,KAAK,CAAC;MACzER,eAAe,EAAE/K,OAAO,CAAC,CAACkG,MAAM,CAAC6E,eAAe,EAAEF,KAAK,CAAC,EAAEvC,OAAO,EAAEiD,KAAK,CAAC;MACzEN,eAAe,EAAEjL,OAAO,CAAC,CAACkG,MAAM,CAAC+E,eAAe,EAAE,CAAC,CAAC,EAAE3C,OAAO,EAAEiD,KAAK;IACtE,CAAC;EACH,CAAC;EAEDiB,MAAM,EAAE,SAAAA,CAASlE,OAAO,EAAE;IACxB,IAAIkD,EAAE,GAAG,IAAI;IACb,IAAI/D,KAAK,GAAG,IAAI;IAChB,IAAIgF,KAAK,GAAG,IAAI;IAChB,IAAIlB,KAAK,GAAGC,EAAE,CAACE,MAAM;IACrB,IAAIxF,MAAM,GAAGsF,EAAE,CAACC,OAAO;IACvB,IAAI3I,KAAK,EAAE4J,KAAK,EAAEvL,KAAK;;IAEvB;IACA;IACA,IAAI8K,OAAO,GAAGjM,OAAO,CAAC,CAACkG,MAAM,CAAC+F,OAAO,EAAE,IAAI,CAAC,EAAE3D,OAAO,EAAEiD,KAAK,CAAC;IAE7D,IAAIU,OAAO,EAAE;MACXnJ,KAAK,GAAGwF,OAAO,CAACqE,OAAO,CAACC,IAAI,CAACrB,KAAK,CAAC;MACnCmB,KAAK,GAAGxM,cAAc,CAACC,QAAQ,CAAC+F,MAAM,CAAC2G,SAAS,EAAE,CAAC/J,KAAK,EAAEwF,OAAO,CAAC,CAAC,EAAExF,KAAK,CAAC;MAC3E3B,KAAK,GAAGtB,aAAa,CAAC6M,KAAK,CAAC,GAAG,EAAE,GAAG1L,KAAK,CAACC,WAAW,CAACyL,KAAK,CAAC;MAE5D,IAAIvL,KAAK,CAACG,MAAM,EAAE;QAChBmG,KAAK,GAAG+D,EAAE,CAACQ,SAAS,CAACC,OAAO,EAAE9K,KAAK,EAAE+E,MAAM,EAAEoC,OAAO,CAAC;QACrDmE,KAAK,GAAGjF,aAAa,CAACC,KAAK,CAAC;MAC9B;IACF;IAEA+D,EAAE,CAACG,MAAM,GAAGlE,KAAK;IACjB+D,EAAE,CAACI,MAAM,GAAGa,KAAK;EACnB,CAAC;EAEDK,QAAQ,EAAE,SAAAA,CAAA,EAAW;IACnB,OAAO,IAAI,CAAClB,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC3D,KAAK,GAAG,CAAC,CAAC;EAC7C,CAAC;EAEDsE,QAAQ,EAAE,SAAAA,CAAA,EAAW;IACnB,OAAO,IAAI,CAACZ,MAAM,GAAG,IAAI,CAACA,MAAM,CAACY,QAAQ,GAAG,CAAC;EAC/C,CAAC;EAEDQ,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,OAAO,IAAI,CAACpB,MAAM,IAAI,IAAI,CAACA,MAAM,CAACU,OAAO;EAC3C,CAAC;EAED5E,KAAK,EAAE,SAAAA,CAAA,EAAW;IAChB,OAAO,IAAI,CAACkE,MAAM;EACpB,CAAC;EAEDqB,IAAI,EAAE,SAAAA,CAASxE,KAAK,EAAEyE,MAAM,EAAE;IAC5B,IAAIzB,EAAE,GAAG,IAAI;IACb,IAAIzJ,GAAG,GAAGyG,KAAK,CAACzG,GAAG;IACnB,IAAI0F,KAAK,GAAG+D,EAAE,CAACG,MAAM;IACrB,IAAIc,KAAK,GAAGjB,EAAE,CAACI,MAAM;IACrB,IAAIlG,IAAI;IAER,IAAI,CAAC,IAAI,CAACqH,OAAO,CAAC,CAAC,EAAE;MACnB;IACF;IAEAhL,GAAG,CAACmL,IAAI,CAAC,CAAC;IAEV,IAAIzF,KAAK,CAAC0E,IAAI,EAAE;MACdzG,IAAI,GAAG+B,KAAK,CAAC/B,IAAI;MACjB3D,GAAG,CAAC2H,SAAS,CAAC,CAAC;MACf3H,GAAG,CAACmD,IAAI,CACNQ,IAAI,CAACN,IAAI,EACTM,IAAI,CAACJ,GAAG,EACRI,IAAI,CAACL,KAAK,GAAGK,IAAI,CAACN,IAAI,EACtBM,IAAI,CAACH,MAAM,GAAGG,IAAI,CAACJ,GAAG,CAAC;MACzBvD,GAAG,CAACoK,IAAI,CAAC,CAAC;IACZ;IAEApK,GAAG,CAACoL,WAAW,GAAGnM,KAAK,CAAC4B,KAAK,CAAC,CAAC,EAAE6E,KAAK,CAAC4E,OAAO,EAAE,CAAC,CAAC;IAClDtK,GAAG,CAACqL,SAAS,CAAC5J,SAAS,CAACyJ,MAAM,CAACnJ,CAAC,CAAC,EAAEN,SAAS,CAACyJ,MAAM,CAACjJ,CAAC,CAAC,CAAC;IACvDjC,GAAG,CAACsL,MAAM,CAAC5F,KAAK,CAAC8E,QAAQ,CAAC;IAE1BjD,SAAS,CAACvH,GAAG,EAAE0K,KAAK,CAACxE,KAAK,EAAER,KAAK,CAAC;IAClCkD,QAAQ,CAAC5I,GAAG,EAAE0F,KAAK,CAACtG,KAAK,EAAEsL,KAAK,CAACrE,IAAI,EAAEX,KAAK,CAAC;IAE7C1F,GAAG,CAACuL,OAAO,CAAC,CAAC;EACf;AACF,CAAC,CAAC;AAEF,IAAIC,WAAW,GAAGC,MAAM,CAACC,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAChE,IAAIC,WAAW,GAAGF,MAAM,CAACG,gBAAgB,IAAI,gBAAgB,CAAC,CAAE;;AAEhE,SAASC,OAAOA,CAACjK,KAAK,EAAEsJ,MAAM,EAAEzG,KAAK,EAAE;EACrC,IAAI9B,GAAG,GAAGnC,IAAI,CAACmC,GAAG,CAAC8B,KAAK,CAAC;EACzB,IAAI7B,GAAG,GAAGpC,IAAI,CAACoC,GAAG,CAAC6B,KAAK,CAAC;EACzB,IAAIqH,EAAE,GAAGZ,MAAM,CAACnJ,CAAC;EACjB,IAAIgK,EAAE,GAAGb,MAAM,CAACjJ,CAAC;EAEjB,OAAO;IACLF,CAAC,EAAE+J,EAAE,GAAGnJ,GAAG,IAAIf,KAAK,CAACG,CAAC,GAAG+J,EAAE,CAAC,GAAGlJ,GAAG,IAAIhB,KAAK,CAACK,CAAC,GAAG8J,EAAE,CAAC;IACnD9J,CAAC,EAAE8J,EAAE,GAAGnJ,GAAG,IAAIhB,KAAK,CAACG,CAAC,GAAG+J,EAAE,CAAC,GAAGnJ,GAAG,IAAIf,KAAK,CAACK,CAAC,GAAG8J,EAAE;EACpD,CAAC;AACH;AAEA,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC/B,IAAIpL,GAAG,GAAG6K,WAAW;EACrB,IAAIlL,GAAG,GAAG+K,WAAW;EACrB,IAAI3J,MAAM,GAAGqK,IAAI,CAACrK,MAAM;EACxB,IAAIvB,CAAC,EAAE6L,EAAE,EAAE5J,EAAE,EAAEC,EAAE,EAAE4J,EAAE;EAErB,KAAK9L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2L,MAAM,CAAC1M,MAAM,EAAE,EAAEe,CAAC,EAAE;IAClC6L,EAAE,GAAGF,MAAM,CAAC3L,CAAC,CAAC;IACdiC,EAAE,GAAG4J,EAAE,CAACpK,CAAC,GAAGF,MAAM,CAACE,CAAC;IACpBS,EAAE,GAAG2J,EAAE,CAAClK,CAAC,GAAGJ,MAAM,CAACI,CAAC;IACpBmK,EAAE,GAAGF,IAAI,CAAC3J,EAAE,GAAGA,EAAE,GAAG2J,IAAI,CAAC1J,EAAE,GAAGA,EAAE;IAChC1B,GAAG,GAAGN,IAAI,CAACM,GAAG,CAACA,GAAG,EAAEsL,EAAE,CAAC;IACvB3L,GAAG,GAAGD,IAAI,CAACC,GAAG,CAACA,GAAG,EAAE2L,EAAE,CAAC;EACzB;EAEA,OAAO;IACLtL,GAAG,EAAEA,GAAG;IACRL,GAAG,EAAEA;EACP,CAAC;AACH;AAEA,SAAS4L,MAAMA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACtB,IAAIhK,EAAE,GAAGgK,EAAE,CAACxK,CAAC,GAAGuK,EAAE,CAACvK,CAAC;EACpB,IAAIS,EAAE,GAAG+J,EAAE,CAACtK,CAAC,GAAGqK,EAAE,CAACrK,CAAC;EACpB,IAAIG,EAAE,GAAG5B,IAAI,CAAC6B,IAAI,CAACE,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EAErC,OAAO;IACLD,EAAE,EAAE,CAACgK,EAAE,CAACxK,CAAC,GAAGuK,EAAE,CAACvK,CAAC,IAAIK,EAAE;IACtBI,EAAE,EAAE,CAAC+J,EAAE,CAACtK,CAAC,GAAGqK,EAAE,CAACrK,CAAC,IAAIG,EAAE;IACtBP,MAAM,EAAEyK,EAAE;IACVlK,EAAE,EAAEA;EACN,CAAC;AACH;AAEA,IAAIoK,MAAM,GAAG,SAAAA,CAAA,EAAW;EACtB,IAAI,CAACC,SAAS,GAAG,CAAC;EAClB,IAAI,CAACC,KAAK,GAAG;IACX3K,CAAC,EAAE,CAAC;IACJE,CAAC,EAAE,CAAC;IACJkE,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC;AACH,CAAC;AAEDrI,KAAK,CAACyO,MAAM,CAACxC,SAAS,EAAE;EACtBkB,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,IAAIlH,CAAC,GAAG,IAAI,CAAC0I,KAAK;IAClB,OAAO;MACL3K,CAAC,EAAEiC,CAAC,CAACjC,CAAC,GAAGiC,CAAC,CAACmC,CAAC,GAAG,CAAC;MAChBlE,CAAC,EAAE+B,CAAC,CAAC/B,CAAC,GAAG+B,CAAC,CAACoC,CAAC,GAAG;IACjB,CAAC;EACH,CAAC;EAEDqE,MAAM,EAAE,SAAAA,CAASS,MAAM,EAAE/H,IAAI,EAAEqH,QAAQ,EAAE;IACvC,IAAI,CAACiC,SAAS,GAAGjC,QAAQ;IACzB,IAAI,CAACkC,KAAK,GAAG;MACX3K,CAAC,EAAEoB,IAAI,CAACpB,CAAC,GAAGmJ,MAAM,CAACnJ,CAAC;MACpBE,CAAC,EAAEkB,IAAI,CAAClB,CAAC,GAAGiJ,MAAM,CAACjJ,CAAC;MACpBkE,CAAC,EAAEhD,IAAI,CAACgD,CAAC;MACTC,CAAC,EAAEjD,IAAI,CAACiD;IACV,CAAC;EACH,CAAC;EAEDuG,QAAQ,EAAE,SAAAA,CAAS/K,KAAK,EAAE;IACxB,IAAI6H,EAAE,GAAG,IAAI;IACb,IAAImD,MAAM,GAAG,CAAC;IACd,IAAIzJ,IAAI,GAAGsG,EAAE,CAACiD,KAAK;IAEnB9K,KAAK,GAAGiK,OAAO,CAACjK,KAAK,EAAE6H,EAAE,CAACyB,MAAM,CAAC,CAAC,EAAE,CAACzB,EAAE,CAACgD,SAAS,CAAC;IAElD,OAAO,EAAE7K,KAAK,CAACG,CAAC,GAAGoB,IAAI,CAACpB,CAAC,GAAG6K,MAAM,IAC7BhL,KAAK,CAACK,CAAC,GAAGkB,IAAI,CAAClB,CAAC,GAAG2K,MAAM,IACzBhL,KAAK,CAACG,CAAC,GAAGoB,IAAI,CAACpB,CAAC,GAAGoB,IAAI,CAACgD,CAAC,GAAGyG,MAAM,GAAG,CAAC,IACtChL,KAAK,CAACK,CAAC,GAAGkB,IAAI,CAAClB,CAAC,GAAGkB,IAAI,CAACiD,CAAC,GAAGwG,MAAM,GAAG,CAAC,CAAC;EAC9C,CAAC;EAED;EACA;EACAC,UAAU,EAAE,SAAAA,CAASC,KAAK,EAAE;IAC1B,IAAIhJ,EAAE,GAAG,IAAI,CAACiJ,OAAO,CAAC,CAAC;IACvB,IAAIhJ,EAAE,GAAG+I,KAAK,CAACC,OAAO,CAAC,CAAC;IACxB,IAAIC,IAAI,GAAG,CACTX,MAAM,CAACvI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EACpBuI,MAAM,CAACvI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,CACrB;IACD,IAAIxD,CAAC,EAAE2M,GAAG,EAAEC,GAAG;IAEf,IAAI,IAAI,CAACT,SAAS,KAAKK,KAAK,CAACL,SAAS,EAAE;MACtC;MACA;MACAO,IAAI,CAAClN,IAAI,CACPuM,MAAM,CAACtI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CAAC,EACpBsI,MAAM,CAACtI,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,CACrB,CAAC;IACH;IAEA,KAAKzD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0M,IAAI,CAACzN,MAAM,EAAE,EAAEe,CAAC,EAAE;MAChC2M,GAAG,GAAGjB,SAAS,CAAClI,EAAE,EAAEkJ,IAAI,CAAC1M,CAAC,CAAC,CAAC;MAC5B4M,GAAG,GAAGlB,SAAS,CAACjI,EAAE,EAAEiJ,IAAI,CAAC1M,CAAC,CAAC,CAAC;MAE5B,IAAI2M,GAAG,CAACxM,GAAG,GAAGyM,GAAG,CAACpM,GAAG,IAAIoM,GAAG,CAACzM,GAAG,GAAGwM,GAAG,CAACnM,GAAG,EAAE;QAC1C,OAAO,KAAK;MACd;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;EACEiM,OAAO,EAAE,SAAAA,CAAA,EAAW;IAClB,IAAItD,EAAE,GAAG,IAAI;IACb,IAAItG,IAAI,GAAGsG,EAAE,CAACiD,KAAK;IACnB,IAAIjI,KAAK,GAAGgF,EAAE,CAACgD,SAAS;IACxB,IAAIvB,MAAM,GAAGzB,EAAE,CAACyB,MAAM,CAAC,CAAC;IAExB,OAAO,CACLW,OAAO,CAAC;MAAC9J,CAAC,EAAEoB,IAAI,CAACpB,CAAC;MAAEE,CAAC,EAAEkB,IAAI,CAAClB;IAAC,CAAC,EAAEiJ,MAAM,EAAEzG,KAAK,CAAC,EAC9CoH,OAAO,CAAC;MAAC9J,CAAC,EAAEoB,IAAI,CAACpB,CAAC,GAAGoB,IAAI,CAACgD,CAAC;MAAElE,CAAC,EAAEkB,IAAI,CAAClB;IAAC,CAAC,EAAEiJ,MAAM,EAAEzG,KAAK,CAAC,EACvDoH,OAAO,CAAC;MAAC9J,CAAC,EAAEoB,IAAI,CAACpB,CAAC,GAAGoB,IAAI,CAACgD,CAAC;MAAElE,CAAC,EAAEkB,IAAI,CAAClB,CAAC,GAAGkB,IAAI,CAACiD;IAAC,CAAC,EAAE8E,MAAM,EAAEzG,KAAK,CAAC,EAChEoH,OAAO,CAAC;MAAC9J,CAAC,EAAEoB,IAAI,CAACpB,CAAC;MAAEE,CAAC,EAAEkB,IAAI,CAAClB,CAAC,GAAGkB,IAAI,CAACiD;IAAC,CAAC,EAAE8E,MAAM,EAAEzG,KAAK,CAAC,CACxD;EACH;AACF,CAAC,CAAC;AAEF,SAAS0I,WAAWA,CAAC3I,EAAE,EAAEkB,KAAK,EAAEqF,QAAQ,EAAE;EACxC,IAAInJ,KAAK,GAAG8D,KAAK,CAAC6E,UAAU,CAAC/F,EAAE,EAAEkB,KAAK,CAAC;EACvC,IAAInD,EAAE,GAAGX,KAAK,CAACW,EAAE;EACjB,IAAIC,EAAE,GAAGZ,KAAK,CAACY,EAAE;EAEjB,IAAI,CAACD,EAAE,IAAI,CAACC,EAAE,EAAE;IACd;IACA,OAAO;MAACT,CAAC,EAAEH,KAAK,CAACG,CAAC;MAAEE,CAAC,EAAEL,KAAK,CAACK;IAAC,CAAC;EACjC;EAEA,IAAIkE,CAAC,GAAG4E,QAAQ,CAAC5E,CAAC;EAClB,IAAIC,CAAC,GAAG2E,QAAQ,CAAC3E,CAAC;;EAElB;EACA,IAAIoE,QAAQ,GAAG9E,KAAK,CAAC8E,QAAQ;EAC7B,IAAItI,EAAE,GAAG1B,IAAI,CAAC+E,GAAG,CAACY,CAAC,GAAG,CAAC,GAAG3F,IAAI,CAACmC,GAAG,CAAC6H,QAAQ,CAAC,CAAC,GAAGhK,IAAI,CAAC+E,GAAG,CAACa,CAAC,GAAG,CAAC,GAAG5F,IAAI,CAACoC,GAAG,CAAC4H,QAAQ,CAAC,CAAC;EACpF,IAAIrI,EAAE,GAAG3B,IAAI,CAAC+E,GAAG,CAACY,CAAC,GAAG,CAAC,GAAG3F,IAAI,CAACoC,GAAG,CAAC4H,QAAQ,CAAC,CAAC,GAAGhK,IAAI,CAAC+E,GAAG,CAACa,CAAC,GAAG,CAAC,GAAG5F,IAAI,CAACmC,GAAG,CAAC6H,QAAQ,CAAC,CAAC;;EAEpF;EACA;EACA;EACA,IAAI4C,EAAE,GAAG,CAAC,GAAG5M,IAAI,CAACC,GAAG,CAACD,IAAI,CAAC+E,GAAG,CAAChD,EAAE,CAAC,EAAE/B,IAAI,CAAC+E,GAAG,CAAC/C,EAAE,CAAC,CAAC;EACjDN,EAAE,IAAIK,EAAE,GAAG6K,EAAE;EACbjL,EAAE,IAAIK,EAAE,GAAG4K,EAAE;;EAEb;EACAlL,EAAE,IAAIwD,KAAK,CAAC2E,MAAM,GAAG9H,EAAE;EACvBJ,EAAE,IAAIuD,KAAK,CAAC2E,MAAM,GAAG7H,EAAE;EAEvB,OAAO;IACLT,CAAC,EAAEH,KAAK,CAACG,CAAC,GAAGG,EAAE;IACfD,CAAC,EAAEL,KAAK,CAACK,CAAC,GAAGE;EACf,CAAC;AACH;AAEA,SAASkL,OAAOA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACjC,IAAIjN,CAAC,EAAEe,CAAC,EAAEmM,EAAE,EAAEC,EAAE;;EAEhB;EACA;EACA;;EAEA,KAAKnN,CAAC,GAAGgN,MAAM,CAAC/N,MAAM,GAAG,CAAC,EAAEe,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IACvCkN,EAAE,GAAGF,MAAM,CAAChN,CAAC,CAAC,CAACoN,OAAO;IAEtB,KAAKrM,CAAC,GAAGf,CAAC,GAAG,CAAC,EAAEe,CAAC,IAAI,CAAC,IAAImM,EAAE,CAACG,QAAQ,EAAE,EAAEtM,CAAC,EAAE;MAC1CoM,EAAE,GAAGH,MAAM,CAACjM,CAAC,CAAC,CAACqM,OAAO;MAEtB,IAAID,EAAE,CAACE,QAAQ,IAAIH,EAAE,CAACI,IAAI,CAACf,UAAU,CAACY,EAAE,CAACG,IAAI,CAAC,EAAE;QAC9CL,QAAQ,CAACC,EAAE,EAAEC,EAAE,CAAC;MAClB;IACF;EACF;EAEA,OAAOH,MAAM;AACf;AAEA,SAASO,OAAOA,CAACP,MAAM,EAAE;EACvB,IAAIhN,CAAC,EAAEH,IAAI,EAAEwK,KAAK,EAAEmD,KAAK,EAAE/C,QAAQ,EAAEG,MAAM,EAAE6C,KAAK;;EAElD;EACA,KAAKzN,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGmN,MAAM,CAAC/N,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;IAC/CqK,KAAK,GAAG2C,MAAM,CAAChN,CAAC,CAAC;IACjBwN,KAAK,GAAGnD,KAAK,CAAC+C,OAAO;IAErB,IAAII,KAAK,CAACH,QAAQ,EAAE;MAClB;MACA;MACA;MACA;MACA;MACAI,KAAK,GAAG,IAAIC,KAAK,CAACrD,KAAK,CAACZ,GAAG,EAAE;QAACkE,GAAG,EAAEA,CAACzJ,EAAE,EAAE0J,CAAC,KAAK1J,EAAE,CAAC2J,QAAQ,CAAC,CAACD,CAAC,CAAC,EAAE,IAAI,CAAC,CAACA,CAAC;MAAC,CAAC,CAAC;MAEzEnD,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,CAAC,CAAC;MAC3BG,MAAM,GAAGiC,WAAW,CAACY,KAAK,EAAEpD,KAAK,CAACjF,KAAK,CAAC,CAAC,EAAEqF,QAAQ,CAAC;MACpD+C,KAAK,CAACF,IAAI,CAACnD,MAAM,CAACS,MAAM,EAAEH,QAAQ,EAAEJ,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC;IACvD;EACF;;EAEA;EACA,OAAO6C,OAAO,CAACC,MAAM,EAAE,UAASE,EAAE,EAAEC,EAAE,EAAE;IACtC,IAAIW,EAAE,GAAGZ,EAAE,CAACa,QAAQ;IACpB,IAAIC,EAAE,GAAGb,EAAE,CAACY,QAAQ;IAEpB,IAAKD,EAAE,IAAIE,EAAE,IAAKA,EAAE,EAAE;MACpBb,EAAE,CAACE,QAAQ,GAAG,KAAK;IACrB,CAAC,MAAM,IAAIS,EAAE,EAAE;MACbZ,EAAE,CAACG,QAAQ,GAAG,KAAK;IACrB;EACF,CAAC,CAAC;AACJ;AAEA,IAAIY,MAAM,GAAG;EACXC,OAAO,EAAE,SAAAA,CAASC,QAAQ,EAAE;IAC1B,IAAInB,MAAM,GAAG,EAAE;IACf,IAAIhN,CAAC,EAAEe,CAAC,EAAElB,IAAI,EAAEuO,IAAI,EAAE/D,KAAK;IAE3B,KAAKrK,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGsO,QAAQ,CAAClP,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MACjD,KAAKe,CAAC,GAAG,CAAC,EAAEqN,IAAI,GAAGD,QAAQ,CAACnO,CAAC,CAAC,CAACf,MAAM,EAAE8B,CAAC,GAAGqN,IAAI,EAAE,EAAErN,CAAC,EAAE;QACpDsJ,KAAK,GAAG8D,QAAQ,CAACnO,CAAC,CAAC,CAACe,CAAC,CAAC;QACtBiM,MAAM,CAACxN,IAAI,CAAC6K,KAAK,CAAC;QAClBA,KAAK,CAAC+C,OAAO,GAAG;UACdE,IAAI,EAAE,IAAIpB,MAAM,CAAC,CAAC;UAClB6B,QAAQ,EAAE,KAAK;UACfV,QAAQ,EAAE,IAAI;UACdgB,IAAI,EAAErO,CAAC;UACPsO,IAAI,EAAEjE,KAAK,CAAChB;QACd,CAAC;MACH;IACF;;IAEA;IACA;IACA;IACA2D,MAAM,CAACuB,IAAI,CAAC,UAASC,CAAC,EAAEC,CAAC,EAAE;MACzB,IAAIC,EAAE,GAAGF,CAAC,CAACpB,OAAO;MAClB,IAAIuB,EAAE,GAAGF,CAAC,CAACrB,OAAO;MAElB,OAAOsB,EAAE,CAACJ,IAAI,KAAKK,EAAE,CAACL,IAAI,GACtBK,EAAE,CAACN,IAAI,GAAGK,EAAE,CAACL,IAAI,GACjBM,EAAE,CAACL,IAAI,GAAGI,EAAE,CAACJ,IAAI;IACvB,CAAC,CAAC;IAEF,IAAI,CAACnE,MAAM,CAAC6C,MAAM,CAAC;IAEnB,OAAOA,MAAM;EACf,CAAC;EAED7C,MAAM,EAAE,SAAAA,CAAS6C,MAAM,EAAE;IACvB,IAAI4B,KAAK,GAAG,KAAK;IACjB,IAAI5O,CAAC,EAAEH,IAAI,EAAEwK,KAAK,EAAEjF,KAAK,EAAEoI,KAAK;IAEhC,KAAKxN,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGmN,MAAM,CAAC/N,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MAC/CqK,KAAK,GAAG2C,MAAM,CAAChN,CAAC,CAAC;MACjBoF,KAAK,GAAGiF,KAAK,CAACjF,KAAK,CAAC,CAAC;MACrBoI,KAAK,GAAGnD,KAAK,CAAC+C,OAAO;MACrBI,KAAK,CAACO,QAAQ,GAAG3I,KAAK,IAAIA,KAAK,CAACwE,OAAO,KAAK,MAAM;MAClD4D,KAAK,CAACH,QAAQ,GAAGhD,KAAK,CAACK,OAAO,CAAC,CAAC;MAChCkE,KAAK,IAAIpB,KAAK,CAACO,QAAQ;IACzB;IAEA,IAAIa,KAAK,EAAE;MACTrB,OAAO,CAACP,MAAM,CAAC;IACjB;EACF,CAAC;EAED6B,MAAM,EAAE,SAAAA,CAAS7B,MAAM,EAAE1L,KAAK,EAAE;IAC9B,IAAItB,CAAC,EAAEwN,KAAK;;IAEZ;IACA;;IAEA,KAAKxN,CAAC,GAAGgN,MAAM,CAAC/N,MAAM,GAAG,CAAC,EAAEe,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACvCwN,KAAK,GAAGR,MAAM,CAAChN,CAAC,CAAC,CAACoN,OAAO;MAEzB,IAAII,KAAK,IAAIA,KAAK,CAACH,QAAQ,IAAIG,KAAK,CAACF,IAAI,CAACjB,QAAQ,CAAC/K,KAAK,CAAC,EAAE;QACzD,OAAO0L,MAAM,CAAChN,CAAC,CAAC;MAClB;IACF;IAEA,OAAO,IAAI;EACb,CAAC;EAED2K,IAAI,EAAE,SAAAA,CAASxE,KAAK,EAAE6G,MAAM,EAAE;IAC5B,IAAIhN,CAAC,EAAEH,IAAI,EAAEwK,KAAK,EAAEmD,KAAK,EAAE/C,QAAQ,EAAEG,MAAM;IAE3C,KAAK5K,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGmN,MAAM,CAAC/N,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MAC/CqK,KAAK,GAAG2C,MAAM,CAAChN,CAAC,CAAC;MACjBwN,KAAK,GAAGnD,KAAK,CAAC+C,OAAO;MAErB,IAAII,KAAK,CAACH,QAAQ,EAAE;QAClB5C,QAAQ,GAAGJ,KAAK,CAACI,QAAQ,CAAC,CAAC;QAC3BG,MAAM,GAAGiC,WAAW,CAACxC,KAAK,CAACZ,GAAG,EAAEY,KAAK,CAACjF,KAAK,CAAC,CAAC,EAAEqF,QAAQ,CAAC;QACxD+C,KAAK,CAACF,IAAI,CAACnD,MAAM,CAACS,MAAM,EAAEH,QAAQ,EAAEJ,KAAK,CAACH,QAAQ,CAAC,CAAC,CAAC;QACrDG,KAAK,CAACM,IAAI,CAACxE,KAAK,EAAEyE,MAAM,CAAC;MAC3B;IACF;EACF;AACF,CAAC;AAED,IAAIJ,SAAS,GAAG,SAAAA,CAAS/J,KAAK,EAAE;EAC9B,IAAIjD,aAAa,CAACiD,KAAK,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAI4J,KAAK,GAAG5J,KAAK;EACjB,IAAIqO,IAAI,EAAEC,IAAI,EAAEC,CAAC;EACjB,IAAIjR,QAAQ,CAAC0C,KAAK,CAAC,EAAE;IACnB,IAAI,CAACjD,aAAa,CAACiD,KAAK,CAAC4J,KAAK,CAAC,EAAE;MAC/BA,KAAK,GAAG5J,KAAK,CAAC4J,KAAK;IACrB,CAAC,MAAM,IAAI,CAAC7M,aAAa,CAACiD,KAAK,CAACiD,CAAC,CAAC,EAAE;MAClC2G,KAAK,GAAG5J,KAAK,CAACiD,CAAC;IACjB,CAAC,MAAM;MACL2G,KAAK,GAAG,EAAE;MACVyE,IAAI,GAAGG,MAAM,CAACH,IAAI,CAACrO,KAAK,CAAC;MACzB,KAAKuO,CAAC,GAAG,CAAC,EAAED,IAAI,GAAGD,IAAI,CAAC7P,MAAM,EAAE+P,CAAC,GAAGD,IAAI,EAAE,EAAEC,CAAC,EAAE;QAC7C3E,KAAK,IAAI,CAAC2E,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,IAAIF,IAAI,CAACE,CAAC,CAAC,GAAG,IAAI,GAAGvO,KAAK,CAACqO,IAAI,CAACE,CAAC,CAAC,CAAC;MAClE;IACF;EACF;EAEA,OAAO,EAAE,GAAG3E,KAAK;AACnB,CAAC;;AAED;AACA;AACA;AACA;;AAEA,IAAIpM,QAAQ,GAAG;EACbkE,KAAK,EAAE,QAAQ;EACf2B,MAAM,EAAE,QAAQ;EAChBqD,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBE,YAAY,EAAE,CAAC;EACfjC,WAAW,EAAE,CAAC;EACdtB,KAAK,EAAE,KAAK;EACZ+F,IAAI,EAAE,KAAK;EACXtB,KAAK,EAAEhC,SAAS;EAChBoD,OAAO,EAAE,IAAI;EACbjK,IAAI,EAAE;IACJuP,MAAM,EAAE1I,SAAS;IACjBlG,UAAU,EAAE,GAAG;IACfkF,IAAI,EAAEgB,SAAS;IACf2I,KAAK,EAAE3I,SAAS;IAChB4I,MAAM,EAAE;EACV,CAAC;EACD5E,SAAS,EAAEA,SAAS;EACpBwC,MAAM,EAAExG,SAAS;EACjB6I,SAAS,EAAE,CAAC,CAAC;EACbtF,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACV1E,OAAO,EAAE;IACPrC,GAAG,EAAE,CAAC;IACND,KAAK,EAAE,CAAC;IACRE,MAAM,EAAE,CAAC;IACTH,IAAI,EAAE;EACR,CAAC;EACDmH,QAAQ,EAAE,CAAC;EACX3B,SAAS,EAAE,OAAO;EAClBG,eAAe,EAAElC,SAAS;EAC1BoC,eAAe,EAAE,CAAC;EAClBE,cAAc,EAAE,CAAC;EACjBE,eAAe,EAAExC;AACnB,CAAC;;AAED;AACA;AACA;;AAEA,IAAI8I,WAAW,GAAG,aAAa;AAC/B,IAAIC,WAAW,GAAG,UAAU;AAE5B,SAASC,SAASA,CAAClF,OAAO,EAAE7F,OAAO,EAAE;EACnC,IAAIgL,QAAQ,GAAGnF,OAAO,CAACoF,UAAU;EACjC,IAAIL,SAAS,GAAG,CAAC,CAAC;EAClB,IAAIM,OAAO,GAAG,EAAE;EAChB,IAAI3C,MAAM,EAAE8B,IAAI;EAEhB,IAAIW,QAAQ,KAAK,KAAK,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIA,QAAQ,KAAK,IAAI,EAAE;IACrBA,QAAQ,GAAG,CAAC,CAAC;EACf;EAEAhL,OAAO,GAAGhH,KAAK,CAAC,CAAC,CAAC,EAAE,CAACgH,OAAO,EAAEgL,QAAQ,CAAC,CAAC;EACxCzC,MAAM,GAAGvI,OAAO,CAACuI,MAAM,IAAI,CAAC,CAAC;EAC7B8B,IAAI,GAAGG,MAAM,CAACH,IAAI,CAAC9B,MAAM,CAAC;EAC1B,OAAOvI,OAAO,CAACuI,MAAM;EAErB,IAAI8B,IAAI,CAAC7P,MAAM,EAAE;IACf6P,IAAI,CAACc,OAAO,CAAC,UAASC,GAAG,EAAE;MACzB,IAAI7C,MAAM,CAAC6C,GAAG,CAAC,EAAE;QACfF,OAAO,CAACnQ,IAAI,CAAC/B,KAAK,CAAC,CAAC,CAAC,EAAE,CACrBgH,OAAO,EACPuI,MAAM,CAAC6C,GAAG,CAAC,EACX;UAACC,IAAI,EAAED;QAAG,CAAC,CACZ,CAAC,CAAC;MACL;IACF,CAAC,CAAC;EACJ,CAAC,MAAM;IACL;IACAF,OAAO,CAACnQ,IAAI,CAACiF,OAAO,CAAC;EACvB;;EAEA;EACA4K,SAAS,GAAGM,OAAO,CAACI,MAAM,CAAC,UAASC,MAAM,EAAEnM,MAAM,EAAE;IAClD7F,IAAI,CAAC6F,MAAM,CAACwL,SAAS,IAAI,CAAC,CAAC,EAAE,UAASY,EAAE,EAAEC,KAAK,EAAE;MAC/CF,MAAM,CAACE,KAAK,CAAC,GAAGF,MAAM,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC;MACnCF,MAAM,CAACE,KAAK,CAAC,CAACrM,MAAM,CAACiM,IAAI,IAAIP,WAAW,CAAC,GAAGU,EAAE;IAChD,CAAC,CAAC;IAEF,OAAOpM,MAAM,CAACwL,SAAS;IACvB,OAAOW,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,OAAO;IACLhD,MAAM,EAAE2C,OAAO;IACfN,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASc,aAAaA,CAAChK,KAAK,EAAEkJ,SAAS,EAAEhF,KAAK,EAAE6F,KAAK,EAAE;EACrD,IAAI,CAACb,SAAS,EAAE;IACd;EACF;EAEA,IAAIpJ,OAAO,GAAGoE,KAAK,CAAC+F,QAAQ;EAC5B,IAAIC,MAAM,GAAGhG,KAAK,CAACiG,OAAO;EAC1B,IAAIC,UAAU;EAEd,IAAI,CAAClB,SAAS,CAACgB,MAAM,CAAChC,IAAI,CAAC,EAAE;IAC3B;EACF;EAEAkC,UAAU,GAAGlB,SAAS,CAACgB,MAAM,CAAChC,IAAI,CAAC,CAACgC,MAAM,CAACP,IAAI,CAAC;EAChD,IAAI,CAACS,UAAU,EAAE;IACf;EACF;EAEA,IAAIzS,QAAQ,CAACyS,UAAU,EAAE,CAACtK,OAAO,EAAEiK,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE;IACnD;IACA;IACA;IACA;IACA/J,KAAK,CAACmJ,WAAW,CAAC,CAACkB,MAAM,GAAG,IAAI;IAChCnG,KAAK,CAACF,MAAM,CAAClE,OAAO,CAAC;EACvB;AACF;AAEA,SAASwK,kBAAkBA,CAACtK,KAAK,EAAEkJ,SAAS,EAAEqB,QAAQ,EAAErG,KAAK,EAAE6F,KAAK,EAAE;EACpE,IAAIS,KAAK,EAAEC,KAAK;EAEhB,IAAI,CAACF,QAAQ,IAAI,CAACrG,KAAK,EAAE;IACvB;EACF;EAEA,IAAI,CAACqG,QAAQ,EAAE;IACbC,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAI,CAACtG,KAAK,EAAE;IACjBuG,KAAK,GAAG,IAAI;EACd,CAAC,MAAM,IAAIF,QAAQ,KAAKrG,KAAK,EAAE;IAC7BuG,KAAK,GAAGD,KAAK,GAAG,IAAI;EACtB;EAEA,IAAIC,KAAK,EAAE;IACTT,aAAa,CAAChK,KAAK,EAAEkJ,SAAS,CAACuB,KAAK,EAAEF,QAAQ,EAAER,KAAK,CAAC;EACxD;EACA,IAAIS,KAAK,EAAE;IACTR,aAAa,CAAChK,KAAK,EAAEkJ,SAAS,CAACsB,KAAK,EAAEtG,KAAK,EAAE6F,KAAK,CAAC;EACrD;AACF;AAEA,SAASW,gBAAgBA,CAAC1K,KAAK,EAAE+J,KAAK,EAAE;EACtC,IAAIY,OAAO,GAAG3K,KAAK,CAACmJ,WAAW,CAAC;EAChC,IAAID,SAAS,GAAGyB,OAAO,CAACC,UAAU;EAClC,IAAIL,QAAQ,EAAErG,KAAK;EAEnB,IAAI,CAACgF,SAAS,CAACsB,KAAK,IAAI,CAACtB,SAAS,CAACuB,KAAK,EAAE;IACxC;EACF;EAEA,IAAIV,KAAK,CAACc,IAAI,KAAK,WAAW,EAAE;IAC9B3G,KAAK,GAAG4D,MAAM,CAACY,MAAM,CAACiC,OAAO,CAACG,OAAO,EAAEf,KAAK,CAAC;EAC/C,CAAC,MAAM,IAAIA,KAAK,CAACc,IAAI,KAAK,UAAU,EAAE;IACpC;EACF;EAEAN,QAAQ,GAAGI,OAAO,CAACI,QAAQ;EAC3BJ,OAAO,CAACI,QAAQ,GAAG7G,KAAK;EACxBoG,kBAAkB,CAACtK,KAAK,EAAEkJ,SAAS,EAAEqB,QAAQ,EAAErG,KAAK,EAAE6F,KAAK,CAAC;AAC9D;AAEA,SAASiB,iBAAiBA,CAAChL,KAAK,EAAE+J,KAAK,EAAE;EACvC,IAAIY,OAAO,GAAG3K,KAAK,CAACmJ,WAAW,CAAC;EAChC,IAAI8B,QAAQ,GAAGN,OAAO,CAACC,UAAU,CAACM,KAAK;EACvC,IAAIhH,KAAK,GAAG+G,QAAQ,IAAInD,MAAM,CAACY,MAAM,CAACiC,OAAO,CAACG,OAAO,EAAEf,KAAK,CAAC;EAC7D,IAAI7F,KAAK,EAAE;IACT8F,aAAa,CAAChK,KAAK,EAAEiL,QAAQ,EAAE/G,KAAK,EAAE6F,KAAK,CAAC;EAC9C;AACF;AAEA,IAAIoB,MAAM,GAAG;EACXC,EAAE,EAAE,YAAY;EAEhBtT,QAAQ,EAAEA,QAAQ;EAElBuT,UAAU,EAAE,SAAAA,CAASrL,KAAK,EAAE;IAC1BA,KAAK,CAACmJ,WAAW,CAAC,GAAG;MACnBmC,QAAQ,EAAE;IACZ,CAAC;EACH,CAAC;EAEDC,YAAY,EAAE,SAAAA,CAASvL,KAAK,EAAE;IAC5B,IAAI2K,OAAO,GAAG3K,KAAK,CAACmJ,WAAW,CAAC;IAChCwB,OAAO,CAACa,SAAS,GAAG,KAAK;IACzBb,OAAO,CAACC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAK;IAC7BD,OAAO,CAACc,SAAS,GAAG,EAAE,CAAC,CAAM;IAC7Bd,OAAO,CAACG,OAAO,GAAG,EAAE,CAAC,CAAQ;EAC/B,CAAC;EAEDY,kBAAkB,EAAE,SAAAA,CAAS1L,KAAK,EAAE2L,IAAI,EAAErN,OAAO,EAAE;IACjD,IAAI4B,YAAY,GAAGyL,IAAI,CAAC5I,KAAK;IAC7B,IAAI4H,OAAO,GAAG3K,KAAK,CAACmJ,WAAW,CAAC;IAChC,IAAItC,MAAM,GAAG8D,OAAO,CAACc,SAAS,CAACvL,YAAY,CAAC,GAAG,EAAE;IACjD,IAAIqE,OAAO,GAAGvE,KAAK,CAAC4L,gBAAgB,CAAC1L,YAAY,CAAC;IAClD,IAAIiE,OAAO,GAAGnE,KAAK,CAACoE,IAAI,CAAC4D,QAAQ,CAAC9H,YAAY,CAAC;IAC/C,IAAIxC,MAAM,GAAG2L,SAAS,CAAClF,OAAO,EAAE7F,OAAO,CAAC;IACxC,IAAIuN,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAAC1H,IAAI,IAAI,EAAE;IACnC,IAAI7K,GAAG,GAAGyG,KAAK,CAACzG,GAAG;IACnB,IAAIM,CAAC,EAAEe,CAAC,EAAElB,IAAI,EAAEuO,IAAI,EAAErG,GAAG,EAAE8H,GAAG,EAAE3L,EAAE,EAAEmG,KAAK;IAEzC3K,GAAG,CAACmL,IAAI,CAAC,CAAC;IAEV,KAAK7K,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGmS,QAAQ,CAAC/S,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MACjDkE,EAAE,GAAG8N,QAAQ,CAAChS,CAAC,CAAC;MAChBkE,EAAE,CAACoL,WAAW,CAAC,GAAG,EAAE;MAEpB,IAAI5E,OAAO,IAAIxG,EAAE,IAAIiC,KAAK,CAAC+L,iBAAiB,CAAClS,CAAC,CAAC,IAAI,CAACkE,EAAE,CAACiO,IAAI,EAAE;QAC3D,KAAKpR,CAAC,GAAG,CAAC,EAAEqN,IAAI,GAAGvK,MAAM,CAACmJ,MAAM,CAAC/N,MAAM,EAAE8B,CAAC,GAAGqN,IAAI,EAAE,EAAErN,CAAC,EAAE;UACtDgH,GAAG,GAAGlE,MAAM,CAACmJ,MAAM,CAACjM,CAAC,CAAC;UACtB8O,GAAG,GAAG9H,GAAG,CAAC+H,IAAI;UAEdzF,KAAK,GAAG,IAAIpB,KAAK,CAAClB,GAAG,EAAErI,GAAG,EAAEwE,EAAE,EAAElE,CAAC,CAAC;UAClCqK,KAAK,CAACiG,OAAO,GAAG;YACdjC,IAAI,EAAEhI,YAAY;YAClByJ,IAAI,EAAED,GAAG,IAAIN;UACf,CAAC;UACDlF,KAAK,CAAC+F,QAAQ,GAAG;YACfgC,MAAM,EAAE,KAAK;YACbjM,KAAK,EAAEA,KAAK;YACZkM,SAAS,EAAErS,CAAC;YACZsK,OAAO,EAAEA,OAAO;YAChBjE,YAAY,EAAEA;UAChB,CAAC;UAEDgE,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC+F,QAAQ,CAAC;UAC5BlM,EAAE,CAACoL,WAAW,CAAC,CAAC9P,IAAI,CAAC6K,KAAK,CAAC;UAC3B2C,MAAM,CAACxN,IAAI,CAAC6K,KAAK,CAAC;QACpB;MACF;IACF;IAEA3K,GAAG,CAACuL,OAAO,CAAC,CAAC;;IAEb;IACA;IACAxN,KAAK,CAACqT,OAAO,CAACC,UAAU,EAAElN,MAAM,CAACwL,SAAS,EAAE;MAC1CiD,MAAM,EAAE,SAAAA,CAASpC,KAAK,EAAEF,MAAM,EAAEuC,MAAM,EAAE;QACtCvC,MAAM,CAACE,KAAK,CAAC,GAAGF,MAAM,CAACE,KAAK,CAAC,IAAI,CAAC,CAAC;QACnCF,MAAM,CAACE,KAAK,CAAC,CAAC4B,IAAI,CAAC5I,KAAK,CAAC,GAAGqJ,MAAM,CAACrC,KAAK,CAAC;QACzCY,OAAO,CAACa,SAAS,GAAG,IAAI;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAEDa,WAAW,EAAE,SAAAA,CAASrM,KAAK,EAAE;IAC3BA,KAAK,CAACmJ,WAAW,CAAC,CAAC2B,OAAO,GAAGhD,MAAM,CAACC,OAAO,CAAC/H,KAAK,CAACmJ,WAAW,CAAC,CAACsC,SAAS,CAAC;EAC3E,CAAC;EAED;EACA;EACA;EACAa,iBAAiB,EAAE,SAAAA,CAAStM,KAAK,EAAE;IACjC8H,MAAM,CAACtD,IAAI,CAACxE,KAAK,EAAEA,KAAK,CAACmJ,WAAW,CAAC,CAAC2B,OAAO,CAAC;EAChD,CAAC;EAEDyB,WAAW,EAAE,SAAAA,CAASvM,KAAK,EAAE2L,IAAI,EAAE;IACjC;IACA;IACA;IACA,IAAI3L,KAAK,CAACmJ,WAAW,CAAC,CAACqC,SAAS,EAAE;MAChC,IAAIzB,KAAK,GAAG4B,IAAI,CAAC5B,KAAK;MACtB,QAAQA,KAAK,CAACc,IAAI;QAClB,KAAK,WAAW;QAChB,KAAK,UAAU;UACbH,gBAAgB,CAAC1K,KAAK,EAAE+J,KAAK,CAAC;UAC9B;QACF,KAAK,OAAO;UACViB,iBAAiB,CAAChL,KAAK,EAAE+J,KAAK,CAAC;UAC/B;MACF;IACF;EACF,CAAC;EAEDyC,UAAU,EAAE,SAAAA,CAASxM,KAAK,EAAE;IAC1B,IAAI2K,OAAO,GAAG3K,KAAK,CAACmJ,WAAW,CAAC;IAChC,IAAIoB,QAAQ,GAAGI,OAAO,CAACW,QAAQ;IAC/B,IAAImB,OAAO,GAAG9B,OAAO,CAACW,QAAQ,GAAGtL,KAAK,CAAC0M,iBAAiB,CAAC,CAAC;IAC1D,IAAI/R,OAAO,GAAGnC,KAAK,CAAC+B,SAAS,CAACgQ,QAAQ,EAAEkC,OAAO,CAAC;IAChD,IAAI5S,CAAC,EAAEH,IAAI,EAAEkB,CAAC,EAAEqN,IAAI,EAAEjE,MAAM,EAAEE,KAAK,EAAE2C,MAAM;IAE3C,KAAKhN,CAAC,GAAG,CAAC,EAAEH,IAAI,GAAGiB,OAAO,CAAC7B,MAAM,EAAEe,CAAC,GAAGH,IAAI,EAAE,EAAEG,CAAC,EAAE;MAChDmK,MAAM,GAAGrJ,OAAO,CAACd,CAAC,CAAC;MACnB,IAAImK,MAAM,CAAC,CAAC,CAAC,EAAE;QACb6C,MAAM,GAAG7C,MAAM,CAAC,CAAC,CAAC,CAAC2I,OAAO,CAACxD,WAAW,CAAC,IAAI,EAAE;QAC7C,KAAKvO,CAAC,GAAG,CAAC,EAAEqN,IAAI,GAAGpB,MAAM,CAAC/N,MAAM,EAAE8B,CAAC,GAAGqN,IAAI,EAAE,EAAErN,CAAC,EAAE;UAC/CsJ,KAAK,GAAG2C,MAAM,CAACjM,CAAC,CAAC;UACjBsJ,KAAK,CAAC+F,QAAQ,CAACgC,MAAM,GAAIjI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAE;UACzCE,KAAK,CAACF,MAAM,CAACE,KAAK,CAAC+F,QAAQ,CAAC;QAC9B;MACF;IACF;IAEA,IAAIU,OAAO,CAACN,MAAM,IAAI1P,OAAO,CAAC7B,MAAM,EAAE;MACpCgP,MAAM,CAAC9D,MAAM,CAAC2G,OAAO,CAACG,OAAO,CAAC;MAC9B9K,KAAK,CAAC4M,MAAM,CAAC,CAAC;IAChB;IAEA,OAAOjC,OAAO,CAACN,MAAM;EACvB;AACF,CAAC;AAED,SAASc,MAAM,IAAI0B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}