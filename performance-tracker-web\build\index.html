<!doctype html><html lang="zh-CN"><head><meta charset="utf-8"/><link rel="icon" href="/favicon.ico"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#000000"/><meta name="description" content="智能驱动·质效双升——2025开发中心重点项目月度绩效跟踪报告"/><title>2025开发中心绩效跟踪系统</title><link rel="preconnect" href="https://fonts.googleapis.com"><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet"><style>*{margin:0;padding:0;box-sizing:border-box}body{margin:0;font-family:<PERSON><PERSON><PERSON>,'Microsoft YaHei',sans-serif;background:radial-gradient(ellipse at bottom,#1a1a2e 0,#16213e 50%,#0f0f23 100%);color:#fff;min-height:100vh;overflow-x:hidden}body::before{content:'';position:fixed;top:0;left:0;width:100%;height:100%;background:linear-gradient(0deg,transparent 24%,rgba(32,255,77,.05) 25%,rgba(32,255,77,.05) 26%,transparent 27%,transparent 74%,rgba(32,255,77,.05) 75%,rgba(32,255,77,.05) 76%,transparent 77%,transparent),linear-gradient(90deg,transparent 24%,rgba(32,255,77,.05) 25%,rgba(32,255,77,.05) 26%,transparent 27%,transparent 74%,rgba(32,255,77,.05) 75%,rgba(32,255,77,.05) 76%,transparent 77%,transparent);background-size:100px 100px;animation:grid-move 20s linear infinite;pointer-events:none;z-index:-1}@keyframes grid-move{0%{transform:translate(0,0)}100%{transform:translate(100px,100px)}}.loading-container{position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;z-index:1000}.loading-text{font-family:Orbitron,monospace;font-size:24px;color:#20ff4d;text-shadow:0 0 20px rgba(32,255,77,.8);animation:pulse 2s ease-in-out infinite}@keyframes pulse{0%,100%{opacity:.6}50%{opacity:1}}.loading-spinner{width:60px;height:60px;border:3px solid rgba(32,255,77,.3);border-top:3px solid #20ff4d;border-radius:50%;animation:spin 1s linear infinite;margin:20px auto}@keyframes spin{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}#root{position:relative;z-index:1}</style><script defer="defer" src="/static/js/main.39e1984c.js"></script><link href="/static/css/main.ddf0f13f.css" rel="stylesheet"></head><body><noscript>您需要启用JavaScript来运行此应用程序。</noscript><div class="loading-container" id="loading"><div class="loading-spinner"></div><div class="loading-text">智能系统初始化中...</div></div><div id="root"></div><script>window.addEventListener("load",(function(){setTimeout((function(){const t=document.getElementById("loading");t&&(t.style.opacity="0",t.style.transition="opacity 0.5s ease-out",setTimeout((()=>t.remove()),500))}),1e3)}))</script></body></html>