{"ast": null, "code": "var _jsxFileName = \"E:\\\\\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F\\\\\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10\\\\performance-tracker-web\\\\src\\\\auth\\\\pages\\\\PersonalSettings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport '../styles/PersonalSettings.css';\nimport '../../styles/themes.css';\nimport authService from '../services/authService';\nimport { t, setLanguage, getCurrentLanguage, initLanguage } from '../../utils/i18n';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PersonalSettings = ({\n  onNavigate,\n  currentUser,\n  onUserUpdate\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    email: '',\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n    role: '',\n    isActive: true,\n    lastLogin: '',\n    createdAt: ''\n  });\n\n  // 偏好设置状态\n  const [preferences, setPreferences] = useState({\n    theme: 'dark',\n    language: 'zh-CN',\n    emailNotifications: true,\n    desktopNotifications: false\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [activeTab, setActiveTab] = useState('profile'); // profile, security, preferences\n  const [currentLang, setCurrentLang] = useState('zh-CN'); // 当前语言状态\n  const [forceUpdate, setForceUpdate] = useState(0); // 强制重新渲染\n\n  useEffect(() => {\n    if (currentUser) {\n      setFormData({\n        username: currentUser.username || '',\n        email: currentUser.email || '',\n        currentPassword: '',\n        newPassword: '',\n        confirmPassword: '',\n        role: currentUser.role || '',\n        isActive: currentUser.isActive !== false,\n        lastLogin: currentUser.lastLogin || '',\n        createdAt: currentUser.createdAt || ''\n      });\n    }\n\n    // 初始化语言设置\n    initLanguage();\n    setCurrentLang(getCurrentLanguage());\n\n    // 加载偏好设置\n    loadPreferences();\n  }, [currentUser]);\n  const loadPreferences = () => {\n    try {\n      const savedPreferences = localStorage.getItem('userPreferences');\n      if (savedPreferences) {\n        const parsed = JSON.parse(savedPreferences);\n        const newPreferences = {\n          ...preferences,\n          ...parsed\n        };\n        setPreferences(newPreferences);\n\n        // 应用已保存的设置\n        applyTheme(newPreferences.theme);\n        applyLanguage(newPreferences.language);\n      }\n    } catch (error) {\n      console.error('加载偏好设置失败:', error);\n    }\n  };\n  const savePreferences = newPreferences => {\n    try {\n      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));\n      setPreferences(newPreferences);\n      setSuccess('偏好设置已保存');\n\n      // 应用主题设置\n      applyTheme(newPreferences.theme);\n\n      // 应用语言设置\n      applyLanguage(newPreferences.language);\n    } catch (error) {\n      console.error('保存偏好设置失败:', error);\n      setError('保存偏好设置失败');\n    }\n  };\n  const applyTheme = theme => {\n    const body = document.body;\n    body.classList.remove('theme-dark', 'theme-light', 'theme-auto');\n    if (theme === 'auto') {\n      // 跟随系统主题\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n      body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');\n    } else {\n      body.classList.add(`theme-${theme}`);\n    }\n  };\n  const applyLanguage = language => {\n    // 实现完整的语言切换逻辑\n    const success = setLanguage(language);\n    if (success) {\n      setCurrentLang(language);\n      console.log('语言设置已切换到:', language);\n      // 强制重新渲染组件以应用新语言\n      setForceUpdate(prev => prev + 1);\n      // 延迟显示成功消息，确保使用新语言\n      setTimeout(() => {\n        setSuccess(t('saveSuccess'));\n        setTimeout(() => setSuccess(''), 2000);\n      }, 100);\n    } else {\n      console.error('语言切换失败:', language);\n      setError('语言切换失败');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // 清除错误和成功消息\n    if (error) setError('');\n    if (success) setSuccess('');\n  };\n  const validateForm = () => {\n    if (!formData.username.trim()) {\n      setError('用户名不能为空');\n      return false;\n    }\n    if (!formData.email.trim()) {\n      setError('邮箱不能为空');\n      return false;\n    }\n\n    // 邮箱格式验证\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(formData.email)) {\n      setError('请输入有效的邮箱地址');\n      return false;\n    }\n    return true;\n  };\n  const validatePasswordForm = () => {\n    if (!formData.currentPassword) {\n      setError('请输入当前密码');\n      return false;\n    }\n    if (!formData.newPassword) {\n      setError('请输入新密码');\n      return false;\n    }\n    if (formData.newPassword.length < 6) {\n      setError('新密码长度至少6位');\n      return false;\n    }\n    if (formData.newPassword !== formData.confirmPassword) {\n      setError('新密码和确认密码不匹配');\n      return false;\n    }\n    return true;\n  };\n  const handleUpdateProfile = async e => {\n    e.preventDefault();\n    if (!validateForm()) return;\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${currentUser.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          username: formData.username,\n          email: formData.email\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setSuccess('个人信息更新成功');\n\n        // 更新当前用户信息\n        const updatedUser = {\n          ...currentUser,\n          ...data.user\n        };\n        authService.updateUserInfo(updatedUser);\n        if (onUserUpdate) {\n          onUserUpdate(updatedUser);\n        }\n      } else {\n        console.error('更新个人信息失败:', data);\n        setError(data.message || '更新个人信息失败，请稍后重试');\n      }\n    } catch (error) {\n      console.error('更新个人信息失败:', error);\n\n      // 根据错误类型提供更具体的错误信息\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\n        setError('网络连接失败，请检查网络连接后重试');\n      } else if (error.message.includes('401')) {\n        setError('登录已过期，请重新登录');\n      } else if (error.message.includes('403')) {\n        setError('权限不足，无法修改个人信息');\n      } else if (error.message.includes('404')) {\n        setError('用户不存在，请重新登录');\n      } else {\n        setError('更新个人信息失败，请稍后重试');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChangePassword = async e => {\n    e.preventDefault();\n    if (!validatePasswordForm()) return;\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    try {\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/change-password`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          currentPassword: formData.currentPassword,\n          newPassword: formData.newPassword\n        })\n      });\n      const data = await response.json();\n      if (data.success) {\n        setSuccess('密码修改成功');\n        setFormData(prev => ({\n          ...prev,\n          currentPassword: '',\n          newPassword: '',\n          confirmPassword: ''\n        }));\n      } else {\n        console.error('修改密码失败:', data);\n        setError(data.message || '修改密码失败，请稍后重试');\n      }\n    } catch (error) {\n      console.error('修改密码失败:', error);\n\n      // 根据错误类型提供更具体的错误信息\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\n        setError('网络连接失败，请检查网络连接后重试');\n      } else if (error.message.includes('401')) {\n        setError('当前密码错误，请重新输入');\n      } else if (error.message.includes('403')) {\n        setError('权限不足，无法修改密码');\n      } else if (error.message.includes('404')) {\n        setError('用户不存在，请重新登录');\n      } else {\n        setError('修改密码失败，请稍后重试');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const formatDate = dateString => {\n    if (!dateString) return '未知';\n    try {\n      return new Date(dateString).toLocaleString('zh-CN');\n    } catch {\n      return '未知';\n    }\n  };\n  const getRoleDisplayName = role => {\n    const roleNames = {\n      'super_admin': '超级管理员',\n      'manager': '管理员',\n      'director': '部长',\n      'user': '普通用户'\n    };\n    return roleNames[role] || role;\n  };\n\n  // 偏好设置处理函数\n  const handlePreferenceChange = (key, value) => {\n    const newPreferences = {\n      ...preferences,\n      [key]: value\n    };\n    savePreferences(newPreferences);\n  };\n  const handleThemeChange = e => {\n    handlePreferenceChange('theme', e.target.value);\n  };\n  const handleLanguageChange = e => {\n    handlePreferenceChange('language', e.target.value);\n  };\n  const handleNotificationChange = key => e => {\n    handlePreferenceChange(key, e.target.checked);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"personal-settings\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-content\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"title-icon\",\n            children: \"\\u2699\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            children: t('personalSettings')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-actions\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"action-button back\",\n          onClick: () => onNavigate('home'),\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\uD83C\\uDFE0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), t('back')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"settings-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-sidebar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-nav\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-item ${activeTab === 'profile' ? 'active' : ''}`,\n            onClick: () => setActiveTab('profile'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDC64\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: t('profile')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-item ${activeTab === 'security' ? 'active' : ''}`,\n            onClick: () => setActiveTab('security'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83D\\uDD12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: t('security')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-item ${activeTab === 'preferences' ? 'active' : ''}`,\n            onClick: () => setActiveTab('preferences'),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"nav-icon\",\n              children: \"\\uD83C\\uDFA8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: t('preferences')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"settings-main\",\n        children: [activeTab === 'profile' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-tab\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u4E2A\\u4EBA\\u4FE1\\u606F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u7BA1\\u7406\\u60A8\\u7684\\u57FA\\u672C\\u4FE1\\u606F\\u548C\\u8D26\\u6237\\u8BE6\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-error\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"alert-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 19\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 17\n          }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-success\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"alert-icon\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 406,\n              columnNumber: 19\n            }, this), success]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleUpdateProfile,\n            className: \"settings-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u57FA\\u672C\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 413,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"username\",\n                  children: \"\\u7528\\u6237\\u540D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  id: \"username\",\n                  name: \"username\",\n                  value: formData.username,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"email\",\n                  children: \"\\u90AE\\u7BB1\\u5730\\u5740\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 429,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  id: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 412,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u8D26\\u6237\\u4FE1\\u606F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"info-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"\\u7528\\u6237\\u89D2\\u8272\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-value role-badge\",\n                    children: getRoleDisplayName(formData.role)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 448,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"\\u8D26\\u6237\\u72B6\\u6001\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `info-value status-badge ${formData.isActive ? 'active' : 'inactive'}`,\n                    children: formData.isActive ? '正常' : '已禁用'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 455,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"\\u6700\\u540E\\u767B\\u5F55\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-value\",\n                    children: formatDate(formData.lastLogin)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 462,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"info-item\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"\\u6CE8\\u518C\\u65F6\\u95F4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"info-value\",\n                    children: formatDate(formData.createdAt)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn btn-primary\",\n                disabled: loading,\n                children: loading ? '保存中...' : '保存更改'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this), activeTab === 'security' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-tab\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: \"\\u5B89\\u5168\\u8BBE\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u7BA1\\u7406\\u60A8\\u7684\\u5BC6\\u7801\\u548C\\u5B89\\u5168\\u9009\\u9879\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 15\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-error\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"alert-icon\",\n              children: \"\\u26A0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 19\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 17\n          }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"alert alert-success\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"alert-icon\",\n              children: \"\\u2705\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 506,\n              columnNumber: 19\n            }, this), success]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleChangePassword,\n            className: \"settings-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\u4FEE\\u6539\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 513,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"currentPassword\",\n                  children: \"\\u5F53\\u524D\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  id: \"currentPassword\",\n                  name: \"currentPassword\",\n                  value: formData.currentPassword,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u5F53\\u524D\\u5BC6\\u7801\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 515,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"newPassword\",\n                  children: \"\\u65B0\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  id: \"newPassword\",\n                  name: \"newPassword\",\n                  value: formData.newPassword,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF09\",\n                  required: true,\n                  minLength: \"6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 530,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"form-group\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"confirmPassword\",\n                  children: \"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 543,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"password\",\n                  id: \"confirmPassword\",\n                  name: \"confirmPassword\",\n                  value: formData.confirmPassword,\n                  onChange: handleInputChange,\n                  placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 544,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 542,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-actions\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"submit\",\n                className: \"btn btn-primary\",\n                disabled: loading,\n                children: loading ? '修改中...' : '修改密码'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 491,\n          columnNumber: 13\n        }, this), activeTab === 'preferences' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-tab\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tab-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              children: t('preferencesSettings')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 573,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: t('preferencesSettingsDesc')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 572,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-form\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: t('interfaceSettings')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: t('themeMode')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 583,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: t('themeDesc')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 584,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-control\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-select\",\n                    value: preferences.theme,\n                    onChange: handleThemeChange,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"dark\",\n                      children: t('darkMode')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 592,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"light\",\n                      children: t('lightMode')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 593,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"auto\",\n                      children: t('autoMode')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 594,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 587,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 586,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 581,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: t('languageSettings')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: t('languageDesc')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 602,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 600,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-control\",\n                  children: /*#__PURE__*/_jsxDEV(\"select\", {\n                    className: \"form-select\",\n                    value: preferences.language,\n                    onChange: handleLanguageChange,\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"zh-CN\",\n                      children: t('simplifiedChinese')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 610,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"en-US\",\n                      children: t('english')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 611,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 605,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 604,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 599,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: t('notificationSettings')\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 618,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: t('emailNotifications')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 622,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: t('emailNotificationsDesc')\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 623,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-control\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: preferences.emailNotifications,\n                      onChange: handleNotificationChange('emailNotifications')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 632,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 626,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"preference-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u684C\\u9762\\u901A\\u77E5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 639,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"\\u5728\\u684C\\u9762\\u663E\\u793A\\u7CFB\\u7EDF\\u901A\\u77E5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 640,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"preference-control\",\n                  children: /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"switch\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: preferences.desktopNotifications,\n                      onChange: handleNotificationChange('desktopNotifications')\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 644,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"slider\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 649,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 617,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 571,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 337,\n    columnNumber: 5\n  }, this);\n};\n_s(PersonalSettings, \"igly9ryLSRyEBftZukX9UcIjA28=\");\n_c = PersonalSettings;\nexport default PersonalSettings;\nvar _c;\n$RefreshReg$(_c, \"PersonalSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "authService", "t", "setLanguage", "getCurrentLanguage", "initLanguage", "jsxDEV", "_jsxDEV", "PersonalSettings", "onNavigate", "currentUser", "onUserUpdate", "_s", "formData", "setFormData", "username", "email", "currentPassword", "newPassword", "confirmPassword", "role", "isActive", "lastLogin", "createdAt", "preferences", "setPreferences", "theme", "language", "emailNotifications", "desktopNotifications", "loading", "setLoading", "error", "setError", "success", "setSuccess", "activeTab", "setActiveTab", "currentLang", "setCurrentLang", "forceUpdate", "setForceUpdate", "loadPreferences", "savedPreferences", "localStorage", "getItem", "parsed", "JSON", "parse", "newPreferences", "applyTheme", "applyLanguage", "console", "savePreferences", "setItem", "stringify", "body", "document", "classList", "remove", "prefersDark", "window", "matchMedia", "matches", "add", "log", "prev", "setTimeout", "handleInputChange", "e", "name", "value", "type", "checked", "target", "validateForm", "trim", "emailRegex", "test", "validatePasswordForm", "length", "handleUpdateProfile", "preventDefault", "response", "authenticatedFetch", "id", "method", "headers", "data", "json", "updatedUser", "user", "updateUserInfo", "message", "includes", "handleChangePassword", "formatDate", "dateString", "Date", "toLocaleString", "getRoleDisplayName", "roleNames", "handlePreferenceChange", "key", "handleThemeChange", "handleLanguageChange", "handleNotificationChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "htmlFor", "onChange", "placeholder", "required", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/pages/PersonalSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/PersonalSettings.css';\r\nimport '../../styles/themes.css';\r\nimport authService from '../services/authService';\r\nimport { t, setLanguage, getCurrentLanguage, initLanguage } from '../../utils/i18n';\r\n\r\nconst PersonalSettings = ({ onNavigate, currentUser, onUserUpdate }) => {\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    email: '',\r\n    currentPassword: '',\r\n    newPassword: '',\r\n    confirmPassword: '',\r\n    role: '',\r\n    isActive: true,\r\n    lastLogin: '',\r\n    createdAt: ''\r\n  });\r\n\r\n  // 偏好设置状态\r\n  const [preferences, setPreferences] = useState({\r\n    theme: 'dark',\r\n    language: 'zh-CN',\r\n    emailNotifications: true,\r\n    desktopNotifications: false\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [activeTab, setActiveTab] = useState('profile'); // profile, security, preferences\r\n  const [currentLang, setCurrentLang] = useState('zh-CN'); // 当前语言状态\r\n  const [forceUpdate, setForceUpdate] = useState(0); // 强制重新渲染\r\n\r\n  useEffect(() => {\r\n    if (currentUser) {\r\n      setFormData({\r\n        username: currentUser.username || '',\r\n        email: currentUser.email || '',\r\n        currentPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        role: currentUser.role || '',\r\n        isActive: currentUser.isActive !== false,\r\n        lastLogin: currentUser.lastLogin || '',\r\n        createdAt: currentUser.createdAt || ''\r\n      });\r\n    }\r\n\r\n    // 初始化语言设置\r\n    initLanguage();\r\n    setCurrentLang(getCurrentLanguage());\r\n\r\n    // 加载偏好设置\r\n    loadPreferences();\r\n  }, [currentUser]);\r\n\r\n  const loadPreferences = () => {\r\n    try {\r\n      const savedPreferences = localStorage.getItem('userPreferences');\r\n      if (savedPreferences) {\r\n        const parsed = JSON.parse(savedPreferences);\r\n        const newPreferences = {\r\n          ...preferences,\r\n          ...parsed\r\n        };\r\n        setPreferences(newPreferences);\r\n\r\n        // 应用已保存的设置\r\n        applyTheme(newPreferences.theme);\r\n        applyLanguage(newPreferences.language);\r\n      }\r\n    } catch (error) {\r\n      console.error('加载偏好设置失败:', error);\r\n    }\r\n  };\r\n\r\n  const savePreferences = (newPreferences) => {\r\n    try {\r\n      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));\r\n      setPreferences(newPreferences);\r\n      setSuccess('偏好设置已保存');\r\n\r\n      // 应用主题设置\r\n      applyTheme(newPreferences.theme);\r\n\r\n      // 应用语言设置\r\n      applyLanguage(newPreferences.language);\r\n\r\n    } catch (error) {\r\n      console.error('保存偏好设置失败:', error);\r\n      setError('保存偏好设置失败');\r\n    }\r\n  };\r\n\r\n  const applyTheme = (theme) => {\r\n    const body = document.body;\r\n    body.classList.remove('theme-dark', 'theme-light', 'theme-auto');\r\n\r\n    if (theme === 'auto') {\r\n      // 跟随系统主题\r\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n      body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');\r\n    } else {\r\n      body.classList.add(`theme-${theme}`);\r\n    }\r\n  };\r\n\r\n  const applyLanguage = (language) => {\r\n    // 实现完整的语言切换逻辑\r\n    const success = setLanguage(language);\r\n    if (success) {\r\n      setCurrentLang(language);\r\n      console.log('语言设置已切换到:', language);\r\n      // 强制重新渲染组件以应用新语言\r\n      setForceUpdate(prev => prev + 1);\r\n      // 延迟显示成功消息，确保使用新语言\r\n      setTimeout(() => {\r\n        setSuccess(t('saveSuccess'));\r\n        setTimeout(() => setSuccess(''), 2000);\r\n      }, 100);\r\n    } else {\r\n      console.error('语言切换失败:', language);\r\n      setError('语言切换失败');\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n\r\n    // 清除错误和成功消息\r\n    if (error) setError('');\r\n    if (success) setSuccess('');\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!formData.username.trim()) {\r\n      setError('用户名不能为空');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.email.trim()) {\r\n      setError('邮箱不能为空');\r\n      return false;\r\n    }\r\n\r\n    // 邮箱格式验证\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(formData.email)) {\r\n      setError('请输入有效的邮箱地址');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const validatePasswordForm = () => {\r\n    if (!formData.currentPassword) {\r\n      setError('请输入当前密码');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.newPassword) {\r\n      setError('请输入新密码');\r\n      return false;\r\n    }\r\n\r\n    if (formData.newPassword.length < 6) {\r\n      setError('新密码长度至少6位');\r\n      return false;\r\n    }\r\n\r\n    if (formData.newPassword !== formData.confirmPassword) {\r\n      setError('新密码和确认密码不匹配');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleUpdateProfile = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) return;\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${currentUser.id}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          username: formData.username,\r\n          email: formData.email\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setSuccess('个人信息更新成功');\r\n        \r\n        // 更新当前用户信息\r\n        const updatedUser = { ...currentUser, ...data.user };\r\n        authService.updateUserInfo(updatedUser);\r\n        \r\n        if (onUserUpdate) {\r\n          onUserUpdate(updatedUser);\r\n        }\r\n      } else {\r\n        console.error('更新个人信息失败:', data);\r\n        setError(data.message || '更新个人信息失败，请稍后重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('更新个人信息失败:', error);\r\n      \r\n      // 根据错误类型提供更具体的错误信息\r\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n        setError('网络连接失败，请检查网络连接后重试');\r\n      } else if (error.message.includes('401')) {\r\n        setError('登录已过期，请重新登录');\r\n      } else if (error.message.includes('403')) {\r\n        setError('权限不足，无法修改个人信息');\r\n      } else if (error.message.includes('404')) {\r\n        setError('用户不存在，请重新登录');\r\n      } else {\r\n        setError('更新个人信息失败，请稍后重试');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChangePassword = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validatePasswordForm()) return;\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/change-password`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          currentPassword: formData.currentPassword,\r\n          newPassword: formData.newPassword\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setSuccess('密码修改成功');\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          currentPassword: '',\r\n          newPassword: '',\r\n          confirmPassword: ''\r\n        }));\r\n      } else {\r\n        console.error('修改密码失败:', data);\r\n        setError(data.message || '修改密码失败，请稍后重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('修改密码失败:', error);\r\n      \r\n      // 根据错误类型提供更具体的错误信息\r\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n        setError('网络连接失败，请检查网络连接后重试');\r\n      } else if (error.message.includes('401')) {\r\n        setError('当前密码错误，请重新输入');\r\n      } else if (error.message.includes('403')) {\r\n        setError('权限不足，无法修改密码');\r\n      } else if (error.message.includes('404')) {\r\n        setError('用户不存在，请重新登录');\r\n      } else {\r\n        setError('修改密码失败，请稍后重试');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return '未知';\r\n    try {\r\n      return new Date(dateString).toLocaleString('zh-CN');\r\n    } catch {\r\n      return '未知';\r\n    }\r\n  };\r\n\r\n  const getRoleDisplayName = (role) => {\r\n    const roleNames = {\r\n      'super_admin': '超级管理员',\r\n      'manager': '管理员',\r\n      'director': '部长',\r\n      'user': '普通用户'\r\n    };\r\n    return roleNames[role] || role;\r\n  };\r\n\r\n  // 偏好设置处理函数\r\n  const handlePreferenceChange = (key, value) => {\r\n    const newPreferences = {\r\n      ...preferences,\r\n      [key]: value\r\n    };\r\n    savePreferences(newPreferences);\r\n  };\r\n\r\n  const handleThemeChange = (e) => {\r\n    handlePreferenceChange('theme', e.target.value);\r\n  };\r\n\r\n  const handleLanguageChange = (e) => {\r\n    handlePreferenceChange('language', e.target.value);\r\n  };\r\n\r\n  const handleNotificationChange = (key) => (e) => {\r\n    handlePreferenceChange(key, e.target.checked);\r\n  };\r\n\r\n  return (\r\n    <div className=\"personal-settings\">\r\n      {/* 页面头部 */}\r\n      <div className=\"page-header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"header-title\">\r\n            <span className=\"title-icon\">⚙️</span>\r\n            <h1>{t('personalSettings')}</h1>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"header-actions\">\r\n          <button \r\n            className=\"action-button back\"\r\n            onClick={() => onNavigate('home')}\r\n          >\r\n            <span>🏠</span>\r\n            {t('back')}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 主要内容区域 */}\r\n      <div className=\"settings-content\">\r\n        {/* 侧边栏导航 */}\r\n        <div className=\"settings-sidebar\">\r\n          <div className=\"sidebar-nav\">\r\n            <button\r\n              className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('profile')}\r\n            >\r\n              <span className=\"nav-icon\">👤</span>\r\n              <span>{t('profile')}</span>\r\n            </button>\r\n            <button\r\n              className={`nav-item ${activeTab === 'security' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('security')}\r\n            >\r\n              <span className=\"nav-icon\">🔒</span>\r\n              <span>{t('security')}</span>\r\n            </button>\r\n            <button\r\n              className={`nav-item ${activeTab === 'preferences' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('preferences')}\r\n            >\r\n              <span className=\"nav-icon\">🎨</span>\r\n              <span>{t('preferences')}</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 设置内容区域 */}\r\n        <div className=\"settings-main\">\r\n          {/* 个人信息标签页 */}\r\n          {activeTab === 'profile' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>个人信息</h2>\r\n                <p>管理您的基本信息和账户详情</p>\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"alert alert-error\">\r\n                  <span className=\"alert-icon\">⚠️</span>\r\n                  {error}\r\n                </div>\r\n              )}\r\n\r\n              {success && (\r\n                <div className=\"alert alert-success\">\r\n                  <span className=\"alert-icon\">✅</span>\r\n                  {success}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleUpdateProfile} className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>基本信息</h3>\r\n                  \r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"username\">用户名</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"username\"\r\n                      name=\"username\"\r\n                      value={formData.username}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入用户名\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"email\">邮箱地址</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      id=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入邮箱地址\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h3>账户信息</h3>\r\n                  \r\n                  <div className=\"info-grid\">\r\n                    <div className=\"info-item\">\r\n                      <label>用户角色</label>\r\n                      <div className=\"info-value role-badge\">\r\n                        {getRoleDisplayName(formData.role)}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>账户状态</label>\r\n                      <div className={`info-value status-badge ${formData.isActive ? 'active' : 'inactive'}`}>\r\n                        {formData.isActive ? '正常' : '已禁用'}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>最后登录</label>\r\n                      <div className=\"info-value\">\r\n                        {formatDate(formData.lastLogin)}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>注册时间</label>\r\n                      <div className=\"info-value\">\r\n                        {formatDate(formData.createdAt)}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-actions\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"btn btn-primary\"\r\n                    disabled={loading}\r\n                  >\r\n                    {loading ? '保存中...' : '保存更改'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          )}\r\n\r\n          {/* 安全设置标签页 */}\r\n          {activeTab === 'security' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>安全设置</h2>\r\n                <p>管理您的密码和安全选项</p>\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"alert alert-error\">\r\n                  <span className=\"alert-icon\">⚠️</span>\r\n                  {error}\r\n                </div>\r\n              )}\r\n\r\n              {success && (\r\n                <div className=\"alert alert-success\">\r\n                  <span className=\"alert-icon\">✅</span>\r\n                  {success}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleChangePassword} className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>修改密码</h3>\r\n                  \r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"currentPassword\">当前密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"currentPassword\"\r\n                      name=\"currentPassword\"\r\n                      value={formData.currentPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入当前密码\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"newPassword\">新密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"newPassword\"\r\n                      name=\"newPassword\"\r\n                      value={formData.newPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入新密码（至少6位）\"\r\n                      required\r\n                      minLength=\"6\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"confirmPassword\">确认新密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"confirmPassword\"\r\n                      name=\"confirmPassword\"\r\n                      value={formData.confirmPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请再次输入新密码\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-actions\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"btn btn-primary\"\r\n                    disabled={loading}\r\n                  >\r\n                    {loading ? '修改中...' : '修改密码'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          )}\r\n\r\n          {/* 偏好设置标签页 */}\r\n          {activeTab === 'preferences' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>{t('preferencesSettings')}</h2>\r\n                <p>{t('preferencesSettingsDesc')}</p>\r\n              </div>\r\n\r\n              <div className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>{t('interfaceSettings')}</h3>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('themeMode')}</h4>\r\n                      <p>{t('themeDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <select\r\n                        className=\"form-select\"\r\n                        value={preferences.theme}\r\n                        onChange={handleThemeChange}\r\n                      >\r\n                        <option value=\"dark\">{t('darkMode')}</option>\r\n                        <option value=\"light\">{t('lightMode')}</option>\r\n                        <option value=\"auto\">{t('autoMode')}</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('languageSettings')}</h4>\r\n                      <p>{t('languageDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <select\r\n                        className=\"form-select\"\r\n                        value={preferences.language}\r\n                        onChange={handleLanguageChange}\r\n                      >\r\n                        <option value=\"zh-CN\">{t('simplifiedChinese')}</option>\r\n                        <option value=\"en-US\">{t('english')}</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h3>{t('notificationSettings')}</h3>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('emailNotifications')}</h4>\r\n                      <p>{t('emailNotificationsDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <label className=\"switch\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={preferences.emailNotifications}\r\n                          onChange={handleNotificationChange('emailNotifications')}\r\n                        />\r\n                        <span className=\"slider\"></span>\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>桌面通知</h4>\r\n                      <p>在桌面显示系统通知</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <label className=\"switch\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={preferences.desktopNotifications}\r\n                          onChange={handleNotificationChange('desktopNotifications')}\r\n                        />\r\n                        <span className=\"slider\"></span>\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonalSettings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,gCAAgC;AACvC,OAAO,yBAAyB;AAChC,OAAOC,WAAW,MAAM,yBAAyB;AACjD,SAASC,CAAC,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,YAAY,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpF,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,UAAU;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC;IACvCgB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE,EAAE;IACfC,eAAe,EAAE,EAAE;IACnBC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,EAAE;IACbC,SAAS,EAAE;EACb,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC;IAC7C2B,KAAK,EAAE,MAAM;IACbC,QAAQ,EAAE,OAAO;IACjBC,kBAAkB,EAAE,IAAI;IACxBC,oBAAoB,EAAE;EACxB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,KAAK,EAAEC,QAAQ,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACmC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;EACvD,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACzD,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;;EAEnDC,SAAS,CAAC,MAAM;IACd,IAAIU,WAAW,EAAE;MACfI,WAAW,CAAC;QACVC,QAAQ,EAAEL,WAAW,CAACK,QAAQ,IAAI,EAAE;QACpCC,KAAK,EAAEN,WAAW,CAACM,KAAK,IAAI,EAAE;QAC9BC,eAAe,EAAE,EAAE;QACnBC,WAAW,EAAE,EAAE;QACfC,eAAe,EAAE,EAAE;QACnBC,IAAI,EAAEV,WAAW,CAACU,IAAI,IAAI,EAAE;QAC5BC,QAAQ,EAAEX,WAAW,CAACW,QAAQ,KAAK,KAAK;QACxCC,SAAS,EAAEZ,WAAW,CAACY,SAAS,IAAI,EAAE;QACtCC,SAAS,EAAEb,WAAW,CAACa,SAAS,IAAI;MACtC,CAAC,CAAC;IACJ;;IAEA;IACAlB,YAAY,CAAC,CAAC;IACdkC,cAAc,CAACnC,kBAAkB,CAAC,CAAC,CAAC;;IAEpC;IACAsC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAMgC,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI;MACF,MAAMC,gBAAgB,GAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;MAChE,IAAIF,gBAAgB,EAAE;QACpB,MAAMG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACL,gBAAgB,CAAC;QAC3C,MAAMM,cAAc,GAAG;UACrB,GAAGzB,WAAW;UACd,GAAGsB;QACL,CAAC;QACDrB,cAAc,CAACwB,cAAc,CAAC;;QAE9B;QACAC,UAAU,CAACD,cAAc,CAACvB,KAAK,CAAC;QAChCyB,aAAa,CAACF,cAAc,CAACtB,QAAQ,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;EAED,MAAMqB,eAAe,GAAIJ,cAAc,IAAK;IAC1C,IAAI;MACFL,YAAY,CAACU,OAAO,CAAC,iBAAiB,EAAEP,IAAI,CAACQ,SAAS,CAACN,cAAc,CAAC,CAAC;MACvExB,cAAc,CAACwB,cAAc,CAAC;MAC9Bd,UAAU,CAAC,SAAS,CAAC;;MAErB;MACAe,UAAU,CAACD,cAAc,CAACvB,KAAK,CAAC;;MAEhC;MACAyB,aAAa,CAACF,cAAc,CAACtB,QAAQ,CAAC;IAExC,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCC,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;EAED,MAAMiB,UAAU,GAAIxB,KAAK,IAAK;IAC5B,MAAM8B,IAAI,GAAGC,QAAQ,CAACD,IAAI;IAC1BA,IAAI,CAACE,SAAS,CAACC,MAAM,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,CAAC;IAEhE,IAAIjC,KAAK,KAAK,MAAM,EAAE;MACpB;MACA,MAAMkC,WAAW,GAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAC7EP,IAAI,CAACE,SAAS,CAACM,GAAG,CAACJ,WAAW,GAAG,YAAY,GAAG,aAAa,CAAC;IAChE,CAAC,MAAM;MACLJ,IAAI,CAACE,SAAS,CAACM,GAAG,CAAC,SAAStC,KAAK,EAAE,CAAC;IACtC;EACF,CAAC;EAED,MAAMyB,aAAa,GAAIxB,QAAQ,IAAK;IAClC;IACA,MAAMO,OAAO,GAAG/B,WAAW,CAACwB,QAAQ,CAAC;IACrC,IAAIO,OAAO,EAAE;MACXK,cAAc,CAACZ,QAAQ,CAAC;MACxByB,OAAO,CAACa,GAAG,CAAC,WAAW,EAAEtC,QAAQ,CAAC;MAClC;MACAc,cAAc,CAACyB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAChC;MACAC,UAAU,CAAC,MAAM;QACfhC,UAAU,CAACjC,CAAC,CAAC,aAAa,CAAC,CAAC;QAC5BiE,UAAU,CAAC,MAAMhC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLiB,OAAO,CAACpB,KAAK,CAAC,SAAS,EAAEL,QAAQ,CAAC;MAClCM,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,MAAMmC,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/C5D,WAAW,CAACoD,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACI,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIvC,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;IACvB,IAAIC,OAAO,EAAEC,UAAU,CAAC,EAAE,CAAC;EAC7B,CAAC;EAED,MAAMwC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAC9D,QAAQ,CAACE,QAAQ,CAAC6D,IAAI,CAAC,CAAC,EAAE;MAC7B3C,QAAQ,CAAC,SAAS,CAAC;MACnB,OAAO,KAAK;IACd;IAEA,IAAI,CAACpB,QAAQ,CAACG,KAAK,CAAC4D,IAAI,CAAC,CAAC,EAAE;MAC1B3C,QAAQ,CAAC,QAAQ,CAAC;MAClB,OAAO,KAAK;IACd;;IAEA;IACA,MAAM4C,UAAU,GAAG,4BAA4B;IAC/C,IAAI,CAACA,UAAU,CAACC,IAAI,CAACjE,QAAQ,CAACG,KAAK,CAAC,EAAE;MACpCiB,QAAQ,CAAC,YAAY,CAAC;MACtB,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAM8C,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAI,CAAClE,QAAQ,CAACI,eAAe,EAAE;MAC7BgB,QAAQ,CAAC,SAAS,CAAC;MACnB,OAAO,KAAK;IACd;IAEA,IAAI,CAACpB,QAAQ,CAACK,WAAW,EAAE;MACzBe,QAAQ,CAAC,QAAQ,CAAC;MAClB,OAAO,KAAK;IACd;IAEA,IAAIpB,QAAQ,CAACK,WAAW,CAAC8D,MAAM,GAAG,CAAC,EAAE;MACnC/C,QAAQ,CAAC,WAAW,CAAC;MACrB,OAAO,KAAK;IACd;IAEA,IAAIpB,QAAQ,CAACK,WAAW,KAAKL,QAAQ,CAACM,eAAe,EAAE;MACrDc,QAAQ,CAAC,aAAa,CAAC;MACvB,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;EAED,MAAMgD,mBAAmB,GAAG,MAAOZ,CAAC,IAAK;IACvCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;IAErB5C,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAMlF,WAAW,CAACmF,kBAAkB,CAAC,wCAAwC1E,WAAW,CAAC2E,EAAE,EAAE,EAAE;QAC9GC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD/B,IAAI,EAAET,IAAI,CAACQ,SAAS,CAAC;UACnBxC,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;UAC3BC,KAAK,EAAEH,QAAQ,CAACG;QAClB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMwE,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACtD,OAAO,EAAE;QAChBC,UAAU,CAAC,UAAU,CAAC;;QAEtB;QACA,MAAMuD,WAAW,GAAG;UAAE,GAAGhF,WAAW;UAAE,GAAG8E,IAAI,CAACG;QAAK,CAAC;QACpD1F,WAAW,CAAC2F,cAAc,CAACF,WAAW,CAAC;QAEvC,IAAI/E,YAAY,EAAE;UAChBA,YAAY,CAAC+E,WAAW,CAAC;QAC3B;MACF,CAAC,MAAM;QACLtC,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEwD,IAAI,CAAC;QAChCvD,QAAQ,CAACuD,IAAI,CAACK,OAAO,IAAI,gBAAgB,CAAC;MAC5C;IACF,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;;MAEjC;MACA,IAAIA,KAAK,CAACsC,IAAI,KAAK,WAAW,IAAItC,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACjE7D,QAAQ,CAAC,mBAAmB,CAAC;MAC/B,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,aAAa,CAAC;MACzB,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,eAAe,CAAC;MAC3B,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,aAAa,CAAC;MACzB,CAAC,MAAM;QACLA,QAAQ,CAAC,gBAAgB,CAAC;MAC5B;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,oBAAoB,GAAG,MAAO1B,CAAC,IAAK;IACxCA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAI,CAACH,oBAAoB,CAAC,CAAC,EAAE;IAE7BhD,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACF,MAAMgD,QAAQ,GAAG,MAAMlF,WAAW,CAACmF,kBAAkB,CAAC,gDAAgD,EAAE;QACtGE,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD/B,IAAI,EAAET,IAAI,CAACQ,SAAS,CAAC;UACnBtC,eAAe,EAAEJ,QAAQ,CAACI,eAAe;UACzCC,WAAW,EAAEL,QAAQ,CAACK;QACxB,CAAC;MACH,CAAC,CAAC;MAEF,MAAMsE,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACtD,OAAO,EAAE;QAChBC,UAAU,CAAC,QAAQ,CAAC;QACpBrB,WAAW,CAACoD,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPjD,eAAe,EAAE,EAAE;UACnBC,WAAW,EAAE,EAAE;UACfC,eAAe,EAAE;QACnB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLiC,OAAO,CAACpB,KAAK,CAAC,SAAS,EAAEwD,IAAI,CAAC;QAC9BvD,QAAQ,CAACuD,IAAI,CAACK,OAAO,IAAI,cAAc,CAAC;MAC1C;IACF,CAAC,CAAC,OAAO7D,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;;MAE/B;MACA,IAAIA,KAAK,CAACsC,IAAI,KAAK,WAAW,IAAItC,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACjE7D,QAAQ,CAAC,mBAAmB,CAAC;MAC/B,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,cAAc,CAAC;MAC1B,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,aAAa,CAAC;MACzB,CAAC,MAAM,IAAID,KAAK,CAAC6D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;QACxC7D,QAAQ,CAAC,aAAa,CAAC;MACzB,CAAC,MAAM;QACLA,QAAQ,CAAC,cAAc,CAAC;MAC1B;IACF,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiE,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE,OAAO,IAAI;IAC5B,IAAI;MACF,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;IACrD,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIhF,IAAI,IAAK;IACnC,MAAMiF,SAAS,GAAG;MAChB,aAAa,EAAE,OAAO;MACtB,SAAS,EAAE,KAAK;MAChB,UAAU,EAAE,IAAI;MAChB,MAAM,EAAE;IACV,CAAC;IACD,OAAOA,SAAS,CAACjF,IAAI,CAAC,IAAIA,IAAI;EAChC,CAAC;;EAED;EACA,MAAMkF,sBAAsB,GAAGA,CAACC,GAAG,EAAEhC,KAAK,KAAK;IAC7C,MAAMtB,cAAc,GAAG;MACrB,GAAGzB,WAAW;MACd,CAAC+E,GAAG,GAAGhC;IACT,CAAC;IACDlB,eAAe,CAACJ,cAAc,CAAC;EACjC,CAAC;EAED,MAAMuD,iBAAiB,GAAInC,CAAC,IAAK;IAC/BiC,sBAAsB,CAAC,OAAO,EAAEjC,CAAC,CAACK,MAAM,CAACH,KAAK,CAAC;EACjD,CAAC;EAED,MAAMkC,oBAAoB,GAAIpC,CAAC,IAAK;IAClCiC,sBAAsB,CAAC,UAAU,EAAEjC,CAAC,CAACK,MAAM,CAACH,KAAK,CAAC;EACpD,CAAC;EAED,MAAMmC,wBAAwB,GAAIH,GAAG,IAAMlC,CAAC,IAAK;IAC/CiC,sBAAsB,CAACC,GAAG,EAAElC,CAAC,CAACK,MAAM,CAACD,OAAO,CAAC;EAC/C,CAAC;EAED,oBACElE,OAAA;IAAKoG,SAAS,EAAC,mBAAmB;IAAAC,QAAA,gBAEhCrG,OAAA;MAAKoG,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BrG,OAAA;QAAKoG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BrG,OAAA;UAAKoG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrG,OAAA;YAAMoG,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACtCzG,OAAA;YAAAqG,QAAA,EAAK1G,CAAC,CAAC,kBAAkB;UAAC;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENzG,OAAA;QAAKoG,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BrG,OAAA;UACEoG,SAAS,EAAC,oBAAoB;UAC9BM,OAAO,EAAEA,CAAA,KAAMxG,UAAU,CAAC,MAAM,CAAE;UAAAmG,QAAA,gBAElCrG,OAAA;YAAAqG,QAAA,EAAM;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EACd9G,CAAC,CAAC,MAAM,CAAC;QAAA;UAAA2G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzG,OAAA;MAAKoG,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BrG,OAAA;QAAKoG,SAAS,EAAC,kBAAkB;QAAAC,QAAA,eAC/BrG,OAAA;UAAKoG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BrG,OAAA;YACEoG,SAAS,EAAE,YAAYvE,SAAS,KAAK,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;YACjE6E,OAAO,EAAEA,CAAA,KAAM5E,YAAY,CAAC,SAAS,CAAE;YAAAuE,QAAA,gBAEvCrG,OAAA;cAAMoG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpCzG,OAAA;cAAAqG,QAAA,EAAO1G,CAAC,CAAC,SAAS;YAAC;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACTzG,OAAA;YACEoG,SAAS,EAAE,YAAYvE,SAAS,KAAK,UAAU,GAAG,QAAQ,GAAG,EAAE,EAAG;YAClE6E,OAAO,EAAEA,CAAA,KAAM5E,YAAY,CAAC,UAAU,CAAE;YAAAuE,QAAA,gBAExCrG,OAAA;cAAMoG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpCzG,OAAA;cAAAqG,QAAA,EAAO1G,CAAC,CAAC,UAAU;YAAC;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACTzG,OAAA;YACEoG,SAAS,EAAE,YAAYvE,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrE6E,OAAO,EAAEA,CAAA,KAAM5E,YAAY,CAAC,aAAa,CAAE;YAAAuE,QAAA,gBAE3CrG,OAAA;cAAMoG,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpCzG,OAAA;cAAAqG,QAAA,EAAO1G,CAAC,CAAC,aAAa;YAAC;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzG,OAAA;QAAKoG,SAAS,EAAC,eAAe;QAAAC,QAAA,GAE3BxE,SAAS,KAAK,SAAS,iBACtB7B,OAAA;UAAKoG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAAqG,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbzG,OAAA;cAAAqG,QAAA,EAAG;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,EAELhF,KAAK,iBACJzB,OAAA;YAAKoG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrG,OAAA;cAAMoG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrChF,KAAK;UAAA;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA9E,OAAO,iBACN3B,OAAA;YAAKoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrG,OAAA;cAAMoG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACpC9E,OAAO;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eAEDzG,OAAA;YAAM2G,QAAQ,EAAEjC,mBAAoB;YAAC0B,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5DrG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAAqG,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEbzG,OAAA;gBAAKoG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrG,OAAA;kBAAO4G,OAAO,EAAC,UAAU;kBAAAP,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACrCzG,OAAA;kBACEiE,IAAI,EAAC,MAAM;kBACXa,EAAE,EAAC,UAAU;kBACbf,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAE1D,QAAQ,CAACE,QAAS;kBACzBqG,QAAQ,EAAEhD,iBAAkB;kBAC5BiD,WAAW,EAAC,sCAAQ;kBACpBC,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzG,OAAA;gBAAKoG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrG,OAAA;kBAAO4G,OAAO,EAAC,OAAO;kBAAAP,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnCzG,OAAA;kBACEiE,IAAI,EAAC,OAAO;kBACZa,EAAE,EAAC,OAAO;kBACVf,IAAI,EAAC,OAAO;kBACZC,KAAK,EAAE1D,QAAQ,CAACG,KAAM;kBACtBoG,QAAQ,EAAEhD,iBAAkB;kBAC5BiD,WAAW,EAAC,4CAAS;kBACrBC,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAAqG,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEbzG,OAAA;gBAAKoG,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBrG,OAAA;kBAAKoG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBrG,OAAA;oBAAAqG,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBzG,OAAA;oBAAKoG,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EACnCR,kBAAkB,CAACvF,QAAQ,CAACO,IAAI;kBAAC;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzG,OAAA;kBAAKoG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBrG,OAAA;oBAAAqG,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBzG,OAAA;oBAAKoG,SAAS,EAAE,2BAA2B9F,QAAQ,CAACQ,QAAQ,GAAG,QAAQ,GAAG,UAAU,EAAG;oBAAAuF,QAAA,EACpF/F,QAAQ,CAACQ,QAAQ,GAAG,IAAI,GAAG;kBAAK;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzG,OAAA;kBAAKoG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBrG,OAAA;oBAAAqG,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBzG,OAAA;oBAAKoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACxBZ,UAAU,CAACnF,QAAQ,CAACS,SAAS;kBAAC;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENzG,OAAA;kBAAKoG,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBrG,OAAA;oBAAAqG,QAAA,EAAO;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACnBzG,OAAA;oBAAKoG,SAAS,EAAC,YAAY;oBAAAC,QAAA,EACxBZ,UAAU,CAACnF,QAAQ,CAACU,SAAS;kBAAC;oBAAAsF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrG,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbmC,SAAS,EAAC,iBAAiB;gBAC3BY,QAAQ,EAAEzF,OAAQ;gBAAA8E,QAAA,EAEjB9E,OAAO,GAAG,QAAQ,GAAG;cAAM;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAGA5E,SAAS,KAAK,UAAU,iBACvB7B,OAAA;UAAKoG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAAqG,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACbzG,OAAA;cAAAqG,QAAA,EAAG;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,EAELhF,KAAK,iBACJzB,OAAA;YAAKoG,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCrG,OAAA;cAAMoG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACrChF,KAAK;UAAA;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA9E,OAAO,iBACN3B,OAAA;YAAKoG,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClCrG,OAAA;cAAMoG,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACpC9E,OAAO;UAAA;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CACN,eAEDzG,OAAA;YAAM2G,QAAQ,EAAEnB,oBAAqB;YAACY,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC7DrG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAAqG,QAAA,EAAI;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAEbzG,OAAA;gBAAKoG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrG,OAAA;kBAAO4G,OAAO,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7CzG,OAAA;kBACEiE,IAAI,EAAC,UAAU;kBACfa,EAAE,EAAC,iBAAiB;kBACpBf,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAE1D,QAAQ,CAACI,eAAgB;kBAChCmG,QAAQ,EAAEhD,iBAAkB;kBAC5BiD,WAAW,EAAC,4CAAS;kBACrBC,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzG,OAAA;gBAAKoG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrG,OAAA;kBAAO4G,OAAO,EAAC,aAAa;kBAAAP,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACxCzG,OAAA;kBACEiE,IAAI,EAAC,UAAU;kBACfa,EAAE,EAAC,aAAa;kBAChBf,IAAI,EAAC,aAAa;kBAClBC,KAAK,EAAE1D,QAAQ,CAACK,WAAY;kBAC5BkG,QAAQ,EAAEhD,iBAAkB;kBAC5BiD,WAAW,EAAC,qEAAc;kBAC1BC,QAAQ;kBACRE,SAAS,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAENzG,OAAA;gBAAKoG,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBrG,OAAA;kBAAO4G,OAAO,EAAC,iBAAiB;kBAAAP,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC9CzG,OAAA;kBACEiE,IAAI,EAAC,UAAU;kBACfa,EAAE,EAAC,iBAAiB;kBACpBf,IAAI,EAAC,iBAAiB;kBACtBC,KAAK,EAAE1D,QAAQ,CAACM,eAAgB;kBAChCiG,QAAQ,EAAEhD,iBAAkB;kBAC5BiD,WAAW,EAAC,kDAAU;kBACtBC,QAAQ;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3BrG,OAAA;gBACEiE,IAAI,EAAC,QAAQ;gBACbmC,SAAS,EAAC,iBAAiB;gBAC3BY,QAAQ,EAAEzF,OAAQ;gBAAA8E,QAAA,EAEjB9E,OAAO,GAAG,QAAQ,GAAG;cAAM;gBAAA+E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN,EAGA5E,SAAS,KAAK,aAAa,iBAC1B7B,OAAA;UAAKoG,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrG,OAAA;YAAKoG,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBrG,OAAA;cAAAqG,QAAA,EAAK1G,CAAC,CAAC,qBAAqB;YAAC;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCzG,OAAA;cAAAqG,QAAA,EAAI1G,CAAC,CAAC,yBAAyB;YAAC;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eAENzG,OAAA;YAAKoG,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAAqG,QAAA,EAAK1G,CAAC,CAAC,mBAAmB;cAAC;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEjCzG,OAAA;gBAAKoG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrG,OAAA;kBAAKoG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrG,OAAA;oBAAAqG,QAAA,EAAK1G,CAAC,CAAC,WAAW;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACzBzG,OAAA;oBAAAqG,QAAA,EAAI1G,CAAC,CAAC,WAAW;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACNzG,OAAA;kBAAKoG,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCrG,OAAA;oBACEoG,SAAS,EAAC,aAAa;oBACvBpC,KAAK,EAAE/C,WAAW,CAACE,KAAM;oBACzB0F,QAAQ,EAAEZ,iBAAkB;oBAAAI,QAAA,gBAE5BrG,OAAA;sBAAQgE,KAAK,EAAC,MAAM;sBAAAqC,QAAA,EAAE1G,CAAC,CAAC,UAAU;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC7CzG,OAAA;sBAAQgE,KAAK,EAAC,OAAO;sBAAAqC,QAAA,EAAE1G,CAAC,CAAC,WAAW;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC/CzG,OAAA;sBAAQgE,KAAK,EAAC,MAAM;sBAAAqC,QAAA,EAAE1G,CAAC,CAAC,UAAU;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzG,OAAA;gBAAKoG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrG,OAAA;kBAAKoG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrG,OAAA;oBAAAqG,QAAA,EAAK1G,CAAC,CAAC,kBAAkB;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAChCzG,OAAA;oBAAAqG,QAAA,EAAI1G,CAAC,CAAC,cAAc;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNzG,OAAA;kBAAKoG,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCrG,OAAA;oBACEoG,SAAS,EAAC,aAAa;oBACvBpC,KAAK,EAAE/C,WAAW,CAACG,QAAS;oBAC5ByF,QAAQ,EAAEX,oBAAqB;oBAAAG,QAAA,gBAE/BrG,OAAA;sBAAQgE,KAAK,EAAC,OAAO;sBAAAqC,QAAA,EAAE1G,CAAC,CAAC,mBAAmB;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eACvDzG,OAAA;sBAAQgE,KAAK,EAAC,OAAO;sBAAAqC,QAAA,EAAE1G,CAAC,CAAC,SAAS;oBAAC;sBAAA2G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzG,OAAA;cAAKoG,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrG,OAAA;gBAAAqG,QAAA,EAAK1G,CAAC,CAAC,sBAAsB;cAAC;gBAAA2G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEpCzG,OAAA;gBAAKoG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrG,OAAA;kBAAKoG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrG,OAAA;oBAAAqG,QAAA,EAAK1G,CAAC,CAAC,oBAAoB;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAClCzG,OAAA;oBAAAqG,QAAA,EAAI1G,CAAC,CAAC,wBAAwB;kBAAC;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACNzG,OAAA;kBAAKoG,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCrG,OAAA;oBAAOoG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACvBrG,OAAA;sBACEiE,IAAI,EAAC,UAAU;sBACfC,OAAO,EAAEjD,WAAW,CAACI,kBAAmB;sBACxCwF,QAAQ,EAAEV,wBAAwB,CAAC,oBAAoB;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1D,CAAC,eACFzG,OAAA;sBAAMoG,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENzG,OAAA;gBAAKoG,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrG,OAAA;kBAAKoG,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9BrG,OAAA;oBAAAqG,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACbzG,OAAA;oBAAAqG,QAAA,EAAG;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACNzG,OAAA;kBAAKoG,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCrG,OAAA;oBAAOoG,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACvBrG,OAAA;sBACEiE,IAAI,EAAC,UAAU;sBACfC,OAAO,EAAEjD,WAAW,CAACK,oBAAqB;sBAC1CuF,QAAQ,EAAEV,wBAAwB,CAAC,sBAAsB;oBAAE;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eACFzG,OAAA;sBAAMoG,SAAS,EAAC;oBAAQ;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpG,EAAA,CA9oBIJ,gBAAgB;AAAAiH,EAAA,GAAhBjH,gBAAgB;AAgpBtB,eAAeA,gBAAgB;AAAC,IAAAiH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}