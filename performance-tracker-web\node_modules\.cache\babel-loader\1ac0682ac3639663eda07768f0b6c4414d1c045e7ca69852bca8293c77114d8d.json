{"ast": null, "code": "import React,{useState,useEffect}from'react';import'../styles/ModuleSix.css';import'../styles/KPITable.css';// 确保KPITable样式优先级更高\nimport KPITable from'../components/KPITable';import ExportModal from'../components/ExportModal';import DataVisualization from'../components/DataVisualization';import moduleSixService from'../services/moduleSixService';import module6_dataVisualizationService from'../services/dataVisualizationService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ModuleSix=_ref=>{let{onNavigate}=_ref;const[activeTable,setActiveTable]=useState('金属橡胶件');const[currentView,setCurrentView]=useState('keyIndicators');// keyIndicators, keyWork 或 dataVisualization\n// 智能月份显示功能 - 根据当前系统日期自动计算显示的月份\nconst[currentMonthPair,setCurrentMonthPair]=useState(()=>{const currentMonth=new Date().getMonth()+1;// getMonth()返回0-11，加1得到1-12\nconst availableMonths=[2,3,4,5,6,7,8,9,10,11,12];// 模块六可用的月份数据\n// 根据当前月份智能计算显示的月份对 - 显示前一个月和当前月\nif(currentMonth===1){// 1月时显示2月和3月（因为没有1月数据，且前一月12月也要跨年处理）\nreturn[2,3];}else if(currentMonth===2){// 2月时显示2月和3月（因为前一月1月没有数据）\nreturn[2,3];}else if(currentMonth>=3&&currentMonth<=12){// 3-12月时显示前一个月和当前月\nreturn[currentMonth-1,currentMonth];}else{// 其他情况默认显示2月和3月\nreturn[2,3];}});const[data,setData]=useState({keyIndicators:[],keyWork:[]});const[loading,setLoading]=useState(true);const[syncStatus,setSyncStatus]=useState('已同步');const[isRefreshing,setIsRefreshing]=useState(false);const[showExportModal,setShowExportModal]=useState(false);const[exportLoading,setExportLoading]=useState(false);const[allDepartmentData,setAllDepartmentData]=useState({});const[visualizationLoading,setVisualizationLoading]=useState(false);const[showVisualizationModal,setShowVisualizationModal]=useState(false);// 11个表的选择项\nconst tableOptions=['金属橡胶件','空簧','系统','客户技术','工艺模具','仿真','特装','技术研究与发展','车端','属地化','车体新材料'];useEffect(()=>{loadData();},[activeTable]);useEffect(()=>{// 当切换到数据可视化视图时，加载所有部门数据\nif(currentView==='dataVisualization'){loadAllDepartmentData();}},[currentView]);const loadData=async()=>{setLoading(true);try{const tableData=await moduleSixService.module6_loadTableData(activeTable);setData(tableData);setSyncStatus('数据加载成功');}catch(error){console.error('数据加载失败:',error);setSyncStatus('数据加载失败');}setLoading(false);};// 刷新数据功能\nconst handleRefresh=async()=>{if(isRefreshing)return;// 防止重复点击\nsetIsRefreshing(true);setSyncStatus('刷新中...');try{await loadData();setSyncStatus('数据加载成功');}catch(error){console.error('刷新失败:',error);setSyncStatus('刷新失败');}finally{setIsRefreshing(false);}};// 加载所有部门数据（用于数据可视化）\nconst loadAllDepartmentData=async()=>{try{setVisualizationLoading(true);setSyncStatus('加载可视化数据中...');const allData=await module6_dataVisualizationService.module6_getAllDepartmentData();setAllDepartmentData(allData);setSyncStatus('可视化数据加载成功');}catch(error){console.error('加载所有部门数据失败:',error);setSyncStatus('可视化数据加载失败');}finally{setVisualizationLoading(false);}};const handleTableChange=tableName=>{setActiveTable(tableName);};// 处理数据可视化弹窗\nconst handleOpenVisualization=async()=>{setShowVisualizationModal(true);if(Object.keys(allDepartmentData).length===0){await loadAllDepartmentData();}};const handleCloseVisualization=()=>{setShowVisualizationModal(false);};// 处理ESC键关闭弹窗\nuseEffect(()=>{const handleKeyDown=event=>{if(event.key==='Escape'&&showVisualizationModal){handleCloseVisualization();}};if(showVisualizationModal){document.addEventListener('keydown',handleKeyDown);// 防止背景滚动\ndocument.body.style.overflow='hidden';}return()=>{document.removeEventListener('keydown',handleKeyDown);document.body.style.overflow='unset';};},[showVisualizationModal]);const handleViewChange=view=>{setCurrentView(view);};// 月份切换处理\nconst handleMonthChange=direction=>{setCurrentMonthPair(prev=>{const[first,second]=prev;if(direction==='prev'){if(first===2)return[11,12];// 特殊处理：2-3月的前一个是11-12月\nconst newFirst=first===1?12:first-1;const newSecond=first;return[newFirst,newSecond];}else{if(second===12)return[2,3];// 特殊处理：11-12月的下一个是2-3月\nconst newFirst=second;const newSecond=second===12?1:second+1;return[newFirst,newSecond];}});};const handleSyncStatus=status=>{setSyncStatus(status);};// 处理导出\nconst handleExport=async exportConfig=>{setExportLoading(true);try{await moduleSixService.module6_exportData(activeTable,currentView,exportConfig);setSyncStatus('导出成功');}catch(error){console.error('导出失败:',error);setSyncStatus('导出失败');}setExportLoading(false);setShowExportModal(false);};return/*#__PURE__*/_jsxs(\"div\",{className:\"module6_module-six\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_module-six-background\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"module6_particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_grid-lines\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_module-six-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"module6_header-left\",children:/*#__PURE__*/_jsx(\"button\",{className:\"module6_back-button\",onClick:()=>onNavigate('home'),children:\"\\u2190 \\u8FD4\\u56DE\\u9996\\u9875\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_header-center\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"module6_module-title\",children:\"\\u5F00\\u53D1\\u4E2D\\u5FC3\\u4E8C\\u7EA7\\u90E8\\u95E8\\u5DE5\\u4F5C\\u76EE\\u6807\\u7BA1\\u7406\\u8D23\\u4EFB\\u4E66\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_header-right\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"module6_status-button \".concat(syncStatus.includes('成功')?'success':syncStatus.includes('失败')?'error':'syncing',\" \").concat(isRefreshing?'refreshing':''),onClick:handleRefresh,style:{cursor:'pointer'},title:\"\\u70B9\\u51FB\\u5237\\u65B0\\u6570\\u636E\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"module6_status-indicator \".concat(syncStatus.includes('成功')?'success':syncStatus.includes('失败')?'error':'syncing')}),syncStatus,syncStatus.includes('成功')&&/*#__PURE__*/_jsx(\"span\",{className:\"module6_refresh-icon\",title:\"\\u70B9\\u51FB\\u5237\\u65B0\",children:\"\\uD83D\\uDD04\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_table-selector\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_table-select-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"table-select\",children:\"\\u9009\\u62E9\\u90E8\\u95E8\\u8868\\uFF1A\"}),/*#__PURE__*/_jsx(\"select\",{id:\"table-select\",value:activeTable,onChange:e=>handleTableChange(e.target.value),className:\"module6_table-dropdown\",children:tableOptions.map(table=>/*#__PURE__*/_jsx(\"option\",{value:table,children:table},table))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"module6_export-btn\",onClick:()=>setShowExportModal(true),children:\"\\u5BFC\\u51FA\\u6570\\u636E\"}),/*#__PURE__*/_jsxs(\"button\",{onClick:handleOpenVisualization,className:\"module6_visualization-btn\",disabled:visualizationLoading,children:[/*#__PURE__*/_jsx(\"span\",{className:\"module6_btn-icon\",children:\"\\uD83D\\uDCCA\"}),visualizationLoading?'加载中...':'绩效评分对比']})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_module-six-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_unified-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_header-section \".concat(currentView==='keyIndicators'?'active':''),onClick:()=>handleViewChange('keyIndicators'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"module6_section-icon\",children:\"\\uD83C\\uDFAF\"}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_section-title\",children:\"\\u5173\\u952E\\u6307\\u6807\\uFF0840\\u5206\\uFF09\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_month-selector\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"module6_month-nav-btn\",onClick:()=>handleMonthChange('prev'),children:\"\\u2190\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"module6_month-display\",children:[currentMonthPair[0],\"\\u6708-\",currentMonthPair[1],\"\\u6708\"]}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_month-nav-btn\",onClick:()=>handleMonthChange('next'),children:\"\\u2192\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_header-section \".concat(currentView==='keyWork'?'active':''),onClick:()=>handleViewChange('keyWork'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"module6_section-icon\",children:\"\\uD83D\\uDE80\"}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_section-title\",children:\"\\u91CD\\u70B9\\u5DE5\\u4F5C\\uFF0860\\u5206\\uFF09\"})]})]}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"module6_loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"module6_loading-spinner\"}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_loading-text\",children:\"\\u6570\\u636E\\u52A0\\u8F7D\\u4E2D...\"})]}):/*#__PURE__*/_jsx(KPITable,{data:data[currentView],tableName:activeTable,viewType:currentView,currentMonthPair:currentMonthPair,onSyncStatus:handleSyncStatus})]}),showExportModal&&/*#__PURE__*/_jsx(ExportModal,{onClose:()=>setShowExportModal(false),onExport:handleExport,loading:exportLoading,data:data[currentView]}),showVisualizationModal&&/*#__PURE__*/_jsx(\"div\",{className:\"module6_visualization-modal-overlay\",onClick:handleCloseVisualization,role:\"dialog\",\"aria-modal\":\"true\",\"aria-labelledby\":\"modal-title\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"module6_visualization-modal\",onClick:e=>e.stopPropagation(),tabIndex:\"-1\",ref:el=>el&&el.focus(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{id:\"modal-title\",className:\"module6_modal-title\",children:\"\\u5404\\u90E8\\u95E8\\u7EE9\\u6548\\u8BC4\\u5206\\u5BF9\\u6BD4\"}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_modal-close-btn\",onClick:handleCloseVisualization,\"aria-label\":\"\\u5173\\u95ED\\u5F39\\u7A97\",title:\"\\u5173\\u95ED\\u5F39\\u7A97 (ESC)\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_modal-content\",children:visualizationLoading?/*#__PURE__*/_jsxs(\"div\",{className:\"module6_loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"module6_loading-spinner\"}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_loading-text\",children:\"\\u52A0\\u8F7D\\u53EF\\u89C6\\u5316\\u6570\\u636E\\u4E2D...\"})]}):/*#__PURE__*/_jsx(DataVisualization,{allDepartmentData:allDepartmentData})})]})})]});};export default ModuleSix;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "KPITable", "ExportModal", "DataVisualization", "moduleSixService", "module6_dataVisualizationService", "jsx", "_jsx", "jsxs", "_jsxs", "ModuleSix", "_ref", "onNavigate", "activeTable", "setActiveTable", "current<PERSON>iew", "set<PERSON><PERSON><PERSON>View", "currentMonthPair", "setCurrentMonthPair", "currentMonth", "Date", "getMonth", "availableMonths", "data", "setData", "keyIndicators", "keyWork", "loading", "setLoading", "syncStatus", "setSyncStatus", "isRefreshing", "setIsRefreshing", "showExportModal", "setShowExportModal", "exportLoading", "setExportLoading", "allDepartmentData", "setAllDepartmentData", "visualizationLoading", "setVisualizationLoading", "showVisualizationModal", "setShowVisualizationModal", "tableOptions", "loadData", "loadAllDepartmentData", "tableData", "module6_loadTableData", "error", "console", "handleRefresh", "allData", "module6_getAllDepartmentData", "handleTableChange", "tableName", "handleOpenVisualization", "Object", "keys", "length", "handleCloseVisualization", "handleKeyDown", "event", "key", "document", "addEventListener", "body", "style", "overflow", "removeEventListener", "handleViewChange", "view", "handleMonthChange", "direction", "prev", "first", "second", "newFirst", "newSecond", "handleSyncStatus", "status", "handleExport", "exportConfig", "module6_exportData", "className", "children", "onClick", "concat", "includes", "cursor", "title", "htmlFor", "id", "value", "onChange", "e", "target", "map", "table", "disabled", "viewType", "onSyncStatus", "onClose", "onExport", "role", "stopPropagation", "tabIndex", "ref", "el", "focus"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块六/pages/ModuleSix.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport '../styles/ModuleSix.css';\nimport '../styles/KPITable.css'; // 确保KPITable样式优先级更高\nimport KPITable from '../components/KPITable';\nimport ExportModal from '../components/ExportModal';\nimport DataVisualization from '../components/DataVisualization';\nimport moduleSixService from '../services/moduleSixService';\nimport module6_dataVisualizationService from '../services/dataVisualizationService';\n\nconst ModuleSix = ({ onNavigate }) => {\n  const [activeTable, setActiveTable] = useState('金属橡胶件');\n  const [currentView, setCurrentView] = useState('keyIndicators'); // keyIndicators, keyWork 或 dataVisualization\n\n  // 智能月份显示功能 - 根据当前系统日期自动计算显示的月份\n  const [currentMonthPair, setCurrentMonthPair] = useState(() => {\n    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12\n    const availableMonths = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]; // 模块六可用的月份数据\n\n    // 根据当前月份智能计算显示的月份对 - 显示前一个月和当前月\n    if (currentMonth === 1) {\n      // 1月时显示2月和3月（因为没有1月数据，且前一月12月也要跨年处理）\n      return [2, 3];\n    } else if (currentMonth === 2) {\n      // 2月时显示2月和3月（因为前一月1月没有数据）\n      return [2, 3];\n    } else if (currentMonth >= 3 && currentMonth <= 12) {\n      // 3-12月时显示前一个月和当前月\n      return [currentMonth - 1, currentMonth];\n    } else {\n      // 其他情况默认显示2月和3月\n      return [2, 3];\n    }\n  });\n  const [data, setData] = useState({\n    keyIndicators: [],\n    keyWork: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [syncStatus, setSyncStatus] = useState('已同步');\n  const [isRefreshing, setIsRefreshing] = useState(false);\n  const [showExportModal, setShowExportModal] = useState(false);\n  const [exportLoading, setExportLoading] = useState(false);\n  const [allDepartmentData, setAllDepartmentData] = useState({});\n  const [visualizationLoading, setVisualizationLoading] = useState(false);\n  const [showVisualizationModal, setShowVisualizationModal] = useState(false);\n\n  // 11个表的选择项\n  const tableOptions = [\n    '金属橡胶件',\n    '空簧',\n    '系统',\n    '客户技术',\n    '工艺模具',\n    '仿真',\n    '特装',\n    '技术研究与发展',\n    '车端',\n    '属地化',\n    '车体新材料'\n  ];\n\n  useEffect(() => {\n    loadData();\n  }, [activeTable]);\n\n  useEffect(() => {\n    // 当切换到数据可视化视图时，加载所有部门数据\n    if (currentView === 'dataVisualization') {\n      loadAllDepartmentData();\n    }\n  }, [currentView]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      const tableData = await moduleSixService.module6_loadTableData(activeTable);\n      setData(tableData);\n      setSyncStatus('数据加载成功');\n    } catch (error) {\n      console.error('数据加载失败:', error);\n      setSyncStatus('数据加载失败');\n    }\n    setLoading(false);\n  };\n\n  // 刷新数据功能\n  const handleRefresh = async () => {\n    if (isRefreshing) return; // 防止重复点击\n    \n    setIsRefreshing(true);\n    setSyncStatus('刷新中...');\n    \n    try {\n      await loadData();\n      setSyncStatus('数据加载成功');\n    } catch (error) {\n      console.error('刷新失败:', error);\n      setSyncStatus('刷新失败');\n    } finally {\n      setIsRefreshing(false);\n    }\n  };\n\n  // 加载所有部门数据（用于数据可视化）\n  const loadAllDepartmentData = async () => {\n    try {\n      setVisualizationLoading(true);\n      setSyncStatus('加载可视化数据中...');\n      const allData = await module6_dataVisualizationService.module6_getAllDepartmentData();\n      setAllDepartmentData(allData);\n      setSyncStatus('可视化数据加载成功');\n    } catch (error) {\n      console.error('加载所有部门数据失败:', error);\n      setSyncStatus('可视化数据加载失败');\n    } finally {\n      setVisualizationLoading(false);\n    }\n  };\n\n  const handleTableChange = (tableName) => {\n    setActiveTable(tableName);\n  };\n\n  // 处理数据可视化弹窗\n  const handleOpenVisualization = async () => {\n    setShowVisualizationModal(true);\n    if (Object.keys(allDepartmentData).length === 0) {\n      await loadAllDepartmentData();\n    }\n  };\n\n  const handleCloseVisualization = () => {\n    setShowVisualizationModal(false);\n  };\n\n  // 处理ESC键关闭弹窗\n  useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === 'Escape' && showVisualizationModal) {\n        handleCloseVisualization();\n      }\n    };\n\n    if (showVisualizationModal) {\n      document.addEventListener('keydown', handleKeyDown);\n      // 防止背景滚动\n      document.body.style.overflow = 'hidden';\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleKeyDown);\n      document.body.style.overflow = 'unset';\n    };\n  }, [showVisualizationModal]);\n\n  const handleViewChange = (view) => {\n    setCurrentView(view);\n  };\n\n  // 月份切换处理\n  const handleMonthChange = (direction) => {\n    setCurrentMonthPair(prev => {\n      const [first, second] = prev;\n      if (direction === 'prev') {\n        if (first === 2) return [11, 12]; // 特殊处理：2-3月的前一个是11-12月\n        const newFirst = first === 1 ? 12 : first - 1;\n        const newSecond = first;\n        return [newFirst, newSecond];\n      } else {\n        if (second === 12) return [2, 3]; // 特殊处理：11-12月的下一个是2-3月\n        const newFirst = second;\n        const newSecond = second === 12 ? 1 : second + 1;\n        return [newFirst, newSecond];\n      }\n    });\n  };\n\n  const handleSyncStatus = (status) => {\n    setSyncStatus(status);\n  };\n\n  // 处理导出\n  const handleExport = async (exportConfig) => {\n    setExportLoading(true);\n    try {\n      await moduleSixService.module6_exportData(activeTable, currentView, exportConfig);\n      setSyncStatus('导出成功');\n    } catch (error) {\n      console.error('导出失败:', error);\n      setSyncStatus('导出失败');\n    }\n    setExportLoading(false);\n    setShowExportModal(false);\n  };\n\n  return (\n    <div className=\"module6_module-six\">\n      {/* 背景动画 */}\n      <div className=\"module6_module-six-background\">\n        <div className=\"module6_particle\"></div>\n        <div className=\"module6_particle\"></div>\n        <div className=\"module6_particle\"></div>\n        <div className=\"module6_grid-lines\"></div>\n      </div>\n\n      {/* 顶部导航栏 */}\n      <div className=\"module6_module-six-header\">\n        {/* 左侧：返回首页按钮 */}\n        <div className=\"module6_header-left\">\n          <button \n            className=\"module6_back-button\"\n            onClick={() => onNavigate('home')}\n          >\n            ← 返回首页\n          </button>\n        </div>\n        \n        {/* 中间：标题居中显示 */}\n        <div className=\"module6_header-center\">\n          <h1 className=\"module6_module-title\">开发中心二级部门工作目标管理责任书</h1>\n        </div>\n        \n        {/* 右侧：状态指示器（重新设计为按钮样式） */}\n        <div className=\"module6_header-right\">\n          <div \n            className={`module6_status-button ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'syncing'} ${isRefreshing ? 'refreshing' : ''}`}\n            onClick={handleRefresh}\n            style={{ cursor: 'pointer' }}\n            title=\"点击刷新数据\"\n          >\n            <span className={`module6_status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'syncing'}`}></span>\n            {syncStatus}\n            {syncStatus.includes('成功') && (\n              <span className=\"module6_refresh-icon\" title=\"点击刷新\">\n                🔄\n              </span>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* 表选择器和操作按钮 */}\n      <div className=\"module6_table-selector\">\n        <div className=\"module6_table-select-group\">\n          <label htmlFor=\"table-select\">选择部门表：</label>\n          <select\n            id=\"table-select\"\n            value={activeTable}\n            onChange={(e) => handleTableChange(e.target.value)}\n            className=\"module6_table-dropdown\"\n          >\n            {tableOptions.map((table) => (\n              <option key={table} value={table}>{table}</option>\n            ))}\n          </select>\n        </div>\n\n        <div className=\"module6_action-buttons\">\n          <button\n            className=\"module6_export-btn\"\n            onClick={() => setShowExportModal(true)}\n          >\n            导出数据\n          </button>\n\n          <button\n            onClick={handleOpenVisualization}\n            className=\"module6_visualization-btn\"\n            disabled={visualizationLoading}\n          >\n            <span className=\"module6_btn-icon\">📊</span>\n            {visualizationLoading ? '加载中...' : '绩效评分对比'}\n          </button>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"module6_module-six-content\">\n        {/* 合并到一行的布局：关键指标 | 月份 | 重点工作 */}\n        <div className=\"module6_unified-header\">\n          {/* 左侧：关键指标 */}\n          <div\n            className={`module6_header-section ${currentView === 'keyIndicators' ? 'active' : ''}`}\n            onClick={() => handleViewChange('keyIndicators')}\n          >\n            <span className=\"module6_section-icon\">🎯</span>\n            <span className=\"module6_section-title\">关键指标（40分）</span>\n          </div>\n\n          {/* 中间：月份选择器（从KPITable移动过来） */}\n          <div className=\"module6_month-selector\">\n            <button\n              className=\"module6_month-nav-btn\"\n              onClick={() => handleMonthChange('prev')}\n            >\n              ←\n            </button>\n            <span className=\"module6_month-display\">\n              {currentMonthPair[0]}月-{currentMonthPair[1]}月\n            </span>\n            <button\n              className=\"module6_month-nav-btn\"\n              onClick={() => handleMonthChange('next')}\n            >\n              →\n            </button>\n          </div>\n\n          {/* 右侧：重点工作 */}\n          <div\n            className={`module6_header-section ${currentView === 'keyWork' ? 'active' : ''}`}\n            onClick={() => handleViewChange('keyWork')}\n          >\n            <span className=\"module6_section-icon\">🚀</span>\n            <span className=\"module6_section-title\">重点工作（60分）</span>\n          </div>\n        </div>\n\n        {/* 表格内容区域 */}\n        {loading ? (\n          <div className=\"module6_loading-container\">\n            <div className=\"module6_loading-spinner\"></div>\n            <div className=\"module6_loading-text\">数据加载中...</div>\n          </div>\n        ) : (\n          <KPITable\n            data={data[currentView]}\n            tableName={activeTable}\n            viewType={currentView}\n            currentMonthPair={currentMonthPair}\n            onSyncStatus={handleSyncStatus}\n          />\n        )}\n      </div>\n\n      {/* 导出模态框 */}\n      {showExportModal && (\n        <ExportModal\n          onClose={() => setShowExportModal(false)}\n          onExport={handleExport}\n          loading={exportLoading}\n          data={data[currentView]}\n        />\n      )}\n\n      {/* 数据可视化弹窗模态框 */}\n      {showVisualizationModal && (\n        <div\n          className=\"module6_visualization-modal-overlay\"\n          onClick={handleCloseVisualization}\n          role=\"dialog\"\n          aria-modal=\"true\"\n          aria-labelledby=\"modal-title\"\n        >\n          <div\n            className=\"module6_visualization-modal\"\n            onClick={(e) => e.stopPropagation()}\n            tabIndex=\"-1\"\n            ref={(el) => el && el.focus()}\n          >\n            <div className=\"module6_modal-header\">\n              <h2 id=\"modal-title\" className=\"module6_modal-title\">各部门绩效评分对比</h2>\n              <button\n                className=\"module6_modal-close-btn\"\n                onClick={handleCloseVisualization}\n                aria-label=\"关闭弹窗\"\n                title=\"关闭弹窗 (ESC)\"\n              >\n                ×\n              </button>\n            </div>\n            <div className=\"module6_modal-content\">\n              {visualizationLoading ? (\n                <div className=\"module6_loading-container\">\n                  <div className=\"module6_loading-spinner\"></div>\n                  <div className=\"module6_loading-text\">加载可视化数据中...</div>\n                </div>\n              ) : (\n                <DataVisualization allDepartmentData={allDepartmentData} />\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ModuleSix;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,yBAAyB,CAChC,MAAO,wBAAwB,CAAE;AACjC,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,MAAO,CAAAC,iBAAiB,KAAM,iCAAiC,CAC/D,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAC3D,MAAO,CAAAC,gCAAgC,KAAM,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpF,KAAM,CAAAC,SAAS,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAC/B,KAAM,CAACE,WAAW,CAAEC,cAAc,CAAC,CAAGf,QAAQ,CAAC,OAAO,CAAC,CACvD,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGjB,QAAQ,CAAC,eAAe,CAAC,CAAE;AAEjE;AACA,KAAM,CAACkB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnB,QAAQ,CAAC,IAAM,CAC7D,KAAM,CAAAoB,YAAY,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAE;AAChD,KAAM,CAAAC,eAAe,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAE;AAE9D;AACA,GAAIH,YAAY,GAAK,CAAC,CAAE,CACtB;AACA,MAAO,CAAC,CAAC,CAAE,CAAC,CAAC,CACf,CAAC,IAAM,IAAIA,YAAY,GAAK,CAAC,CAAE,CAC7B;AACA,MAAO,CAAC,CAAC,CAAE,CAAC,CAAC,CACf,CAAC,IAAM,IAAIA,YAAY,EAAI,CAAC,EAAIA,YAAY,EAAI,EAAE,CAAE,CAClD;AACA,MAAO,CAACA,YAAY,CAAG,CAAC,CAAEA,YAAY,CAAC,CACzC,CAAC,IAAM,CACL;AACA,MAAO,CAAC,CAAC,CAAE,CAAC,CAAC,CACf,CACF,CAAC,CAAC,CACF,KAAM,CAACI,IAAI,CAAEC,OAAO,CAAC,CAAGzB,QAAQ,CAAC,CAC/B0B,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG7B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC8B,UAAU,CAAEC,aAAa,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgC,YAAY,CAAEC,eAAe,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACkC,eAAe,CAAEC,kBAAkB,CAAC,CAAGnC,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACoC,aAAa,CAAEC,gBAAgB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACsC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9D,KAAM,CAACwC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAAC0C,sBAAsB,CAAEC,yBAAyB,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAE3E;AACA,KAAM,CAAA4C,YAAY,CAAG,CACnB,OAAO,CACP,IAAI,CACJ,IAAI,CACJ,MAAM,CACN,MAAM,CACN,IAAI,CACJ,IAAI,CACJ,SAAS,CACT,IAAI,CACJ,KAAK,CACL,OAAO,CACR,CAED3C,SAAS,CAAC,IAAM,CACd4C,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,CAAC/B,WAAW,CAAC,CAAC,CAEjBb,SAAS,CAAC,IAAM,CACd;AACA,GAAIe,WAAW,GAAK,mBAAmB,CAAE,CACvC8B,qBAAqB,CAAC,CAAC,CACzB,CACF,CAAC,CAAE,CAAC9B,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAA6B,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BhB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAkB,SAAS,CAAG,KAAM,CAAA1C,gBAAgB,CAAC2C,qBAAqB,CAAClC,WAAW,CAAC,CAC3EW,OAAO,CAACsB,SAAS,CAAC,CAClBhB,aAAa,CAAC,QAAQ,CAAC,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BlB,aAAa,CAAC,QAAQ,CAAC,CACzB,CACAF,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAsB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAInB,YAAY,CAAE,OAAQ;AAE1BC,eAAe,CAAC,IAAI,CAAC,CACrBF,aAAa,CAAC,QAAQ,CAAC,CAEvB,GAAI,CACF,KAAM,CAAAc,QAAQ,CAAC,CAAC,CAChBd,aAAa,CAAC,QAAQ,CAAC,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BlB,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,OAAS,CACRE,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CAAC,CAED;AACA,KAAM,CAAAa,qBAAqB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACFL,uBAAuB,CAAC,IAAI,CAAC,CAC7BV,aAAa,CAAC,aAAa,CAAC,CAC5B,KAAM,CAAAqB,OAAO,CAAG,KAAM,CAAA9C,gCAAgC,CAAC+C,4BAA4B,CAAC,CAAC,CACrFd,oBAAoB,CAACa,OAAO,CAAC,CAC7BrB,aAAa,CAAC,WAAW,CAAC,CAC5B,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnClB,aAAa,CAAC,WAAW,CAAC,CAC5B,CAAC,OAAS,CACRU,uBAAuB,CAAC,KAAK,CAAC,CAChC,CACF,CAAC,CAED,KAAM,CAAAa,iBAAiB,CAAIC,SAAS,EAAK,CACvCxC,cAAc,CAACwC,SAAS,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAC,uBAAuB,CAAG,KAAAA,CAAA,GAAY,CAC1Cb,yBAAyB,CAAC,IAAI,CAAC,CAC/B,GAAIc,MAAM,CAACC,IAAI,CAACpB,iBAAiB,CAAC,CAACqB,MAAM,GAAK,CAAC,CAAE,CAC/C,KAAM,CAAAb,qBAAqB,CAAC,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAAc,wBAAwB,CAAGA,CAAA,GAAM,CACrCjB,yBAAyB,CAAC,KAAK,CAAC,CAClC,CAAC,CAED;AACA1C,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4D,aAAa,CAAIC,KAAK,EAAK,CAC/B,GAAIA,KAAK,CAACC,GAAG,GAAK,QAAQ,EAAIrB,sBAAsB,CAAE,CACpDkB,wBAAwB,CAAC,CAAC,CAC5B,CACF,CAAC,CAED,GAAIlB,sBAAsB,CAAE,CAC1BsB,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEJ,aAAa,CAAC,CACnD;AACAG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,QAAQ,CACzC,CAEA,MAAO,IAAM,CACXJ,QAAQ,CAACK,mBAAmB,CAAC,SAAS,CAAER,aAAa,CAAC,CACtDG,QAAQ,CAACE,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAG,OAAO,CACxC,CAAC,CACH,CAAC,CAAE,CAAC1B,sBAAsB,CAAC,CAAC,CAE5B,KAAM,CAAA4B,gBAAgB,CAAIC,IAAI,EAAK,CACjCtD,cAAc,CAACsD,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIC,SAAS,EAAK,CACvCtD,mBAAmB,CAACuD,IAAI,EAAI,CAC1B,KAAM,CAACC,KAAK,CAAEC,MAAM,CAAC,CAAGF,IAAI,CAC5B,GAAID,SAAS,GAAK,MAAM,CAAE,CACxB,GAAIE,KAAK,GAAK,CAAC,CAAE,MAAO,CAAC,EAAE,CAAE,EAAE,CAAC,CAAE;AAClC,KAAM,CAAAE,QAAQ,CAAGF,KAAK,GAAK,CAAC,CAAG,EAAE,CAAGA,KAAK,CAAG,CAAC,CAC7C,KAAM,CAAAG,SAAS,CAAGH,KAAK,CACvB,MAAO,CAACE,QAAQ,CAAEC,SAAS,CAAC,CAC9B,CAAC,IAAM,CACL,GAAIF,MAAM,GAAK,EAAE,CAAE,MAAO,CAAC,CAAC,CAAE,CAAC,CAAC,CAAE;AAClC,KAAM,CAAAC,QAAQ,CAAGD,MAAM,CACvB,KAAM,CAAAE,SAAS,CAAGF,MAAM,GAAK,EAAE,CAAG,CAAC,CAAGA,MAAM,CAAG,CAAC,CAChD,MAAO,CAACC,QAAQ,CAAEC,SAAS,CAAC,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAC,gBAAgB,CAAIC,MAAM,EAAK,CACnCjD,aAAa,CAACiD,MAAM,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,YAAY,EAAK,CAC3C7C,gBAAgB,CAAC,IAAI,CAAC,CACtB,GAAI,CACF,KAAM,CAAAhC,gBAAgB,CAAC8E,kBAAkB,CAACrE,WAAW,CAAEE,WAAW,CAAEkE,YAAY,CAAC,CACjFnD,aAAa,CAAC,MAAM,CAAC,CACvB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BlB,aAAa,CAAC,MAAM,CAAC,CACvB,CACAM,gBAAgB,CAAC,KAAK,CAAC,CACvBF,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAED,mBACEzB,KAAA,QAAK0E,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAEjC3E,KAAA,QAAK0E,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5C7E,IAAA,QAAK4E,SAAS,CAAC,kBAAkB,CAAM,CAAC,cACxC5E,IAAA,QAAK4E,SAAS,CAAC,kBAAkB,CAAM,CAAC,cACxC5E,IAAA,QAAK4E,SAAS,CAAC,kBAAkB,CAAM,CAAC,cACxC5E,IAAA,QAAK4E,SAAS,CAAC,oBAAoB,CAAM,CAAC,EACvC,CAAC,cAGN1E,KAAA,QAAK0E,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eAExC7E,IAAA,QAAK4E,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cAClC7E,IAAA,WACE4E,SAAS,CAAC,qBAAqB,CAC/BE,OAAO,CAAEA,CAAA,GAAMzE,UAAU,CAAC,MAAM,CAAE,CAAAwE,QAAA,CACnC,iCAED,CAAQ,CAAC,CACN,CAAC,cAGN7E,IAAA,QAAK4E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACpC7E,IAAA,OAAI4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,wGAAiB,CAAI,CAAC,CACxD,CAAC,cAGN7E,IAAA,QAAK4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,cACnC3E,KAAA,QACE0E,SAAS,0BAAAG,MAAA,CAA2BzD,UAAU,CAAC0D,QAAQ,CAAC,IAAI,CAAC,CAAG,SAAS,CAAG1D,UAAU,CAAC0D,QAAQ,CAAC,IAAI,CAAC,CAAG,OAAO,CAAG,SAAS,MAAAD,MAAA,CAAIvD,YAAY,CAAG,YAAY,CAAG,EAAE,CAAG,CAClKsD,OAAO,CAAEnC,aAAc,CACvBgB,KAAK,CAAE,CAAEsB,MAAM,CAAE,SAAU,CAAE,CAC7BC,KAAK,CAAC,sCAAQ,CAAAL,QAAA,eAEd7E,IAAA,SAAM4E,SAAS,6BAAAG,MAAA,CAA8BzD,UAAU,CAAC0D,QAAQ,CAAC,IAAI,CAAC,CAAG,SAAS,CAAG1D,UAAU,CAAC0D,QAAQ,CAAC,IAAI,CAAC,CAAG,OAAO,CAAG,SAAS,CAAG,CAAO,CAAC,CAC9I1D,UAAU,CACVA,UAAU,CAAC0D,QAAQ,CAAC,IAAI,CAAC,eACxBhF,IAAA,SAAM4E,SAAS,CAAC,sBAAsB,CAACM,KAAK,CAAC,0BAAM,CAAAL,QAAA,CAAC,cAEpD,CAAM,CACP,EACE,CAAC,CACH,CAAC,EACH,CAAC,cAGN3E,KAAA,QAAK0E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC3E,KAAA,QAAK0E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzC7E,IAAA,UAAOmF,OAAO,CAAC,cAAc,CAAAN,QAAA,CAAC,sCAAM,CAAO,CAAC,cAC5C7E,IAAA,WACEoF,EAAE,CAAC,cAAc,CACjBC,KAAK,CAAE/E,WAAY,CACnBgF,QAAQ,CAAGC,CAAC,EAAKzC,iBAAiB,CAACyC,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACnDT,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAEjCzC,YAAY,CAACqD,GAAG,CAAEC,KAAK,eACtB1F,IAAA,WAAoBqF,KAAK,CAAEK,KAAM,CAAAb,QAAA,CAAEa,KAAK,EAA3BA,KAAoC,CAClD,CAAC,CACI,CAAC,EACN,CAAC,cAENxF,KAAA,QAAK0E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC7E,IAAA,WACE4E,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAMnD,kBAAkB,CAAC,IAAI,CAAE,CAAAkD,QAAA,CACzC,0BAED,CAAQ,CAAC,cAET3E,KAAA,WACE4E,OAAO,CAAE9B,uBAAwB,CACjC4B,SAAS,CAAC,2BAA2B,CACrCe,QAAQ,CAAE3D,oBAAqB,CAAA6C,QAAA,eAE/B7E,IAAA,SAAM4E,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CAC3C7C,oBAAoB,CAAG,QAAQ,CAAG,QAAQ,EACrC,CAAC,EACN,CAAC,EACH,CAAC,cAGN9B,KAAA,QAAK0E,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eAEzC3E,KAAA,QAAK0E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErC3E,KAAA,QACE0E,SAAS,2BAAAG,MAAA,CAA4BvE,WAAW,GAAK,eAAe,CAAG,QAAQ,CAAG,EAAE,CAAG,CACvFsE,OAAO,CAAEA,CAAA,GAAMhB,gBAAgB,CAAC,eAAe,CAAE,CAAAe,QAAA,eAEjD7E,IAAA,SAAM4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cAChD7E,IAAA,SAAM4E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8CAAS,CAAM,CAAC,EACrD,CAAC,cAGN3E,KAAA,QAAK0E,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC7E,IAAA,WACE4E,SAAS,CAAC,uBAAuB,CACjCE,OAAO,CAAEA,CAAA,GAAMd,iBAAiB,CAAC,MAAM,CAAE,CAAAa,QAAA,CAC1C,QAED,CAAQ,CAAC,cACT3E,KAAA,SAAM0E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACpCnE,gBAAgB,CAAC,CAAC,CAAC,CAAC,SAAE,CAACA,gBAAgB,CAAC,CAAC,CAAC,CAAC,QAC9C,EAAM,CAAC,cACPV,IAAA,WACE4E,SAAS,CAAC,uBAAuB,CACjCE,OAAO,CAAEA,CAAA,GAAMd,iBAAiB,CAAC,MAAM,CAAE,CAAAa,QAAA,CAC1C,QAED,CAAQ,CAAC,EACN,CAAC,cAGN3E,KAAA,QACE0E,SAAS,2BAAAG,MAAA,CAA4BvE,WAAW,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CACjFsE,OAAO,CAAEA,CAAA,GAAMhB,gBAAgB,CAAC,SAAS,CAAE,CAAAe,QAAA,eAE3C7E,IAAA,SAAM4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cAChD7E,IAAA,SAAM4E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8CAAS,CAAM,CAAC,EACrD,CAAC,EACH,CAAC,CAGLzD,OAAO,cACNlB,KAAA,QAAK0E,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC7E,IAAA,QAAK4E,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/C5E,IAAA,QAAK4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,mCAAQ,CAAK,CAAC,EACjD,CAAC,cAEN7E,IAAA,CAACN,QAAQ,EACPsB,IAAI,CAAEA,IAAI,CAACR,WAAW,CAAE,CACxBuC,SAAS,CAAEzC,WAAY,CACvBsF,QAAQ,CAAEpF,WAAY,CACtBE,gBAAgB,CAAEA,gBAAiB,CACnCmF,YAAY,CAAEtB,gBAAiB,CAChC,CACF,EACE,CAAC,CAGL7C,eAAe,eACd1B,IAAA,CAACL,WAAW,EACVmG,OAAO,CAAEA,CAAA,GAAMnE,kBAAkB,CAAC,KAAK,CAAE,CACzCoE,QAAQ,CAAEtB,YAAa,CACvBrD,OAAO,CAAEQ,aAAc,CACvBZ,IAAI,CAAEA,IAAI,CAACR,WAAW,CAAE,CACzB,CACF,CAGA0B,sBAAsB,eACrBlC,IAAA,QACE4E,SAAS,CAAC,qCAAqC,CAC/CE,OAAO,CAAE1B,wBAAyB,CAClC4C,IAAI,CAAC,QAAQ,CACb,aAAW,MAAM,CACjB,kBAAgB,aAAa,CAAAnB,QAAA,cAE7B3E,KAAA,QACE0E,SAAS,CAAC,6BAA6B,CACvCE,OAAO,CAAGS,CAAC,EAAKA,CAAC,CAACU,eAAe,CAAC,CAAE,CACpCC,QAAQ,CAAC,IAAI,CACbC,GAAG,CAAGC,EAAE,EAAKA,EAAE,EAAIA,EAAE,CAACC,KAAK,CAAC,CAAE,CAAAxB,QAAA,eAE9B3E,KAAA,QAAK0E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC7E,IAAA,OAAIoF,EAAE,CAAC,aAAa,CAACR,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,wDAAS,CAAI,CAAC,cACnE7E,IAAA,WACE4E,SAAS,CAAC,yBAAyB,CACnCE,OAAO,CAAE1B,wBAAyB,CAClC,aAAW,0BAAM,CACjB8B,KAAK,CAAC,gCAAY,CAAAL,QAAA,CACnB,MAED,CAAQ,CAAC,EACN,CAAC,cACN7E,IAAA,QAAK4E,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnC7C,oBAAoB,cACnB9B,KAAA,QAAK0E,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxC7E,IAAA,QAAK4E,SAAS,CAAC,yBAAyB,CAAM,CAAC,cAC/C5E,IAAA,QAAK4E,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,qDAAW,CAAK,CAAC,EACpD,CAAC,cAEN7E,IAAA,CAACJ,iBAAiB,EAACkC,iBAAiB,CAAEA,iBAAkB,CAAE,CAC3D,CACE,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAA3B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}