import * as XLSX from 'xlsx';
import kpiService from './kpiService';

class KPIDownloadService {
  constructor() {
    this.downloadHistory = [];
  }

  // 处理选择性下载（基于指标选择）
  async handleSelectiveDownload(selectionData) {
    try {
      console.log('开始处理KPI选择性下载:', selectionData);

      const { selectedData, selectedIndicators, format, includeAllMonths, statistics } = selectionData;

      // 验证选择数据
      if (!selectedData || selectedData.length === 0) {
        throw new Error('没有选择任何数据');
      }

      if (!selectedIndicators || selectedIndicators.length === 0) {
        throw new Error('没有选择任何指标');
      }

      // 根据格式生成文件
      const result = await this.generateSelectiveFile(selectedData, selectionData);

      // 记录下载历史
      this.recordSelectiveDownloadHistory(selectionData, result);

      return {
        success: true,
        message: `成功下载 ${selectedIndicators.length} 个指标的数据`,
        filename: result.filename,
        downloadUrl: result.downloadUrl
      };

    } catch (error) {
      console.error('KPI选择性下载处理失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // 处理KPI数据下载
  async handleKPIDownload(selectionData) {
    try {
      console.log('开始处理KPI下载:', selectionData);
      
      // 验证选择数据
      const validation = this.validateSelection(selectionData);
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 根据选择条件过滤数据
      const filteredData = this.filterDataBySelection(selectionData);
      
      if (filteredData.length === 0) {
        throw new Error('没有符合条件的数据可供下载');
      }

      // 根据格式生成文件
      const result = await this.generateFile(filteredData, selectionData);
      
      // 记录下载历史
      this.recordDownloadHistory(selectionData, result);
      
      return {
        success: true,
        message: `成功下载 ${filteredData.length} 项KPI数据`,
        filename: result.filename,
        downloadUrl: result.downloadUrl
      };
      
    } catch (error) {
      console.error('KPI下载处理失败:', error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  // 验证选择数据
  validateSelection(selectionData) {
    if (!selectionData) {
      return { valid: false, message: '选择数据不能为空' };
    }

    if (!selectionData.items || selectionData.items.length === 0) {
      return { valid: false, message: '请至少选择一项KPI指标' };
    }

    if (!selectionData.monthRange || selectionData.monthRange.length !== 2) {
      return { valid: false, message: '请选择有效的月份范围' };
    }

    const [firstMonth, secondMonth] = selectionData.monthRange;
    if (firstMonth < 1 || firstMonth > 12 || secondMonth < 1 || secondMonth > 12) {
      return { valid: false, message: '月份范围无效' };
    }

    if (secondMonth <= firstMonth) {
      return { valid: false, message: '结束月份必须大于开始月份' };
    }

    return { valid: true };
  }

  // 根据选择条件过滤数据
  filterDataBySelection(selectionData) {
    const { items, workTypes, responsibles, monthRange, data } = selectionData;
    
    // 基础过滤：选中的项目索引
    let filteredData = data.filter((_, index) => items.includes(index));

    // 按工作类型过滤
    if (workTypes && workTypes.length > 0) {
      filteredData = filteredData.filter(item => {
        return workTypes.some(type => this.matchWorkType(item, type));
      });
    }

    // 按负责人过滤
    if (responsibles && responsibles.length > 0) {
      filteredData = filteredData.filter(item => {
        return responsibles.includes(item['统计方式&口径']) || responsibles.includes(item.统计方式);
      });
    }

    // 转换数据格式，只包含选定的月份
    return this.transformDataForDownload(filteredData, monthRange);
  }

  // 判断工作项是否匹配工作类型
  matchWorkType(item, type) {
    const indicator = item.指标 || '';
    
    switch (type) {
      case '开发相关':
        return indicator.includes('产品线') || indicator.includes('开发') || 
               indicator.includes('降本') || indicator.includes('新产品');
      case '管理相关':
        return indicator.includes('管理') || indicator.includes('归口') || 
               indicator.includes('预算') || indicator.includes('编制');
      case '质量相关':
        return indicator.includes('质量') || indicator.includes('及时率') || 
               indicator.includes('命中率') || indicator.includes('周期');
      case '其他':
        return !this.matchWorkType(item, '开发相关') && 
               !this.matchWorkType(item, '管理相关') && 
               !this.matchWorkType(item, '质量相关');
      default:
        return true;
    }
  }

  // 转换数据用于下载
  transformDataForDownload(data, monthRange) {
    const [firstMonth, secondMonth] = monthRange;
    
    return data.map(row => {
      // 过滤掉内部属性
      const { rowSpan, isMergedStart, isMergedCell, ...cleanRow } = row;
      
      // 构建下载数据结构
      const result = {
        序号: cleanRow.序号,
        指标: cleanRow.指标,
        目标值: cleanRow.目标值,
        分值: cleanRow.分值,
        统计方式: cleanRow.统计方式 || cleanRow['统计方式&口径'],
        考核标准: cleanRow.考核标准
      };
      
      // 添加指定月份的数据
      result[`${firstMonth}月`] = cleanRow[`${firstMonth}月`] || `${firstMonth}月`;
      result[`${firstMonth}月完成情况`] = cleanRow[`${firstMonth}月完成情况`] || '';
      result[`${firstMonth}月得分`] = cleanRow[`${firstMonth}月得分`] || '';
      result[`${secondMonth}月`] = cleanRow[`${secondMonth}月`] || `${secondMonth}月`;
      result[`${secondMonth}月完成情况`] = cleanRow[`${secondMonth}月完成情况`] || '';
      result[`${secondMonth}月得分`] = cleanRow[`${secondMonth}月得分`] || '';
      
      return result;
    });
  }

  // 生成文件
  async generateFile(data, selectionData) {
    const { format, monthRange } = selectionData;
    const [firstMonth, secondMonth] = monthRange;
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    
    if (format === 'excel') {
      return await this.generateExcelFile(data, firstMonth, secondMonth, timestamp);
    } else {
      return await this.generateCSVFile(data, firstMonth, secondMonth, timestamp);
    }
  }

  // 生成Excel文件
  async generateExcelFile(data, firstMonth, secondMonth, timestamp) {
    try {
      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      
      // 准备数据表头
      const headers = [
        '序号', '指标', '目标值', '分值(30)', '统计方式&口径', '考核标准',
        `${firstMonth}月`, `${firstMonth}月完成情况`, `${firstMonth}月得分`,
        `${secondMonth}月`, `${secondMonth}月完成情况`, `${secondMonth}月得分`
      ];
      
      // 准备工作表数据
      const wsData = [headers];
      
      data.forEach(row => {
        const rowData = [
          row.序号,
          row.指标,
          row.目标值,
          row.分值,
          row.统计方式,
          row.考核标准,
          row[`${firstMonth}月`],
          row[`${firstMonth}月完成情况`],
          row[`${firstMonth}月得分`],
          row[`${secondMonth}月`],
          row[`${secondMonth}月完成情况`],
          row[`${secondMonth}月得分`]
        ];
        wsData.push(rowData);
      });
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(wsData);
      
      // 设置列宽
      const colWidths = [
        { wch: 8 },   // 序号
        { wch: 25 },  // 指标
        { wch: 40 },  // 目标值
        { wch: 12 },  // 分值
        { wch: 20 },  // 统计方式
        { wch: 30 },  // 考核标准
        { wch: 10 },  // 月份1
        { wch: 25 },  // 完成情况1
        { wch: 10 },  // 得分1
        { wch: 10 },  // 月份2
        { wch: 25 },  // 完成情况2
        { wch: 10 }   // 得分2
      ];
      worksheet['!cols'] = colWidths;
      
      // 设置行高
      const rowHeights = wsData.map(() => ({ hpt: 20 }));
      worksheet['!rows'] = rowHeights;

      // 设置表头样式
      this.setHeaderStyle(worksheet, headers.length);

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, `KPI数据_${firstMonth}-${secondMonth}月`);

      // 创建汇总页
      this.createSummarySheet(workbook, data, firstMonth, secondMonth);
      
      // 生成文件名
      const filename = `KPI跟踪数据_${firstMonth}-${secondMonth}月_${timestamp}.xlsx`;
      
      // 写入文件
      const excelBuffer = XLSX.write(workbook, { 
        bookType: 'xlsx', 
        type: 'array',
        compression: true
      });
      
      // 创建下载
      this.downloadFile(excelBuffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      
      return {
        filename: filename,
        downloadUrl: `data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,${this.arrayBufferToBase64(excelBuffer)}`
      };
      
    } catch (error) {
      console.error('生成Excel文件失败:', error);
      throw new Error('Excel文件生成失败: ' + error.message);
    }
  }

  // 生成CSV文件
  async generateCSVFile(data, firstMonth, secondMonth, timestamp) {
    try {
      // 准备CSV头部
      const headers = [
        '序号', '指标', '目标值', '分值', '统计方式', '考核标准',
        `${firstMonth}月`, `${firstMonth}月完成情况`, `${firstMonth}月得分`,
        `${secondMonth}月`, `${secondMonth}月完成情况`, `${secondMonth}月得分`
      ];
      
      // 构建CSV内容
      let csvContent = headers.join(',') + '\n';
      
      data.forEach(row => {
        const rowData = [
          this.escapeCsvValue(row.序号),
          this.escapeCsvValue(row.指标),
          this.escapeCsvValue(row.目标值),
          this.escapeCsvValue(row.分值),
          this.escapeCsvValue(row.统计方式),
          this.escapeCsvValue(row.考核标准),
          this.escapeCsvValue(row[`${firstMonth}月`]),
          this.escapeCsvValue(row[`${firstMonth}月完成情况`]),
          this.escapeCsvValue(row[`${firstMonth}月得分`]),
          this.escapeCsvValue(row[`${secondMonth}月`]),
          this.escapeCsvValue(row[`${secondMonth}月完成情况`]),
          this.escapeCsvValue(row[`${secondMonth}月得分`])
        ];
        csvContent += rowData.join(',') + '\n';
      });
      
      // 添加BOM以支持中文
      const BOM = '\uFEFF';
      const finalContent = BOM + csvContent;
      
      // 生成文件名
      const filename = `KPI跟踪数据_${firstMonth}-${secondMonth}月_${timestamp}.csv`;
      
      // 创建下载
      this.downloadFile(finalContent, filename, 'text/csv;charset=utf-8');
      
      return {
        filename: filename,
        downloadUrl: `data:text/csv;charset=utf-8,${encodeURIComponent(finalContent)}`
      };
      
    } catch (error) {
      console.error('生成CSV文件失败:', error);
      throw new Error('CSV文件生成失败: ' + error.message);
    }
  }

  // 转义CSV值
  escapeCsvValue(value) {
    if (value === null || value === undefined) {
      return '';
    }
    
    const stringValue = String(value);
    
    // 如果包含逗号、引号或换行符，需要用引号包围并转义引号
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      return '"' + stringValue.replace(/"/g, '""') + '"';
    }
    
    return stringValue;
  }

  // 下载文件
  downloadFile(content, filename, mimeType) {
    try {
      let blob;
      
      if (content instanceof ArrayBuffer) {
        blob = new Blob([content], { type: mimeType });
      } else {
        blob = new Blob([content], { type: mimeType });
      }
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      link.style.display = 'none';
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 释放URL对象
      setTimeout(() => window.URL.revokeObjectURL(url), 100);
      
    } catch (error) {
      console.error('文件下载失败:', error);
      throw new Error('文件下载失败: ' + error.message);
    }
  }

  // ArrayBuffer转Base64
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const len = bytes.byteLength;
    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
  }

  // 记录下载历史
  recordDownloadHistory(selectionData, result) {
    const historyRecord = {
      timestamp: new Date().toISOString(),
      filename: result.filename,
      itemCount: selectionData.items.length,
      monthRange: selectionData.monthRange,
      format: selectionData.format,
      workTypes: selectionData.workTypes,
      responsibles: selectionData.responsibles
    };
    
    this.downloadHistory.push(historyRecord);
    
    // 只保留最近100条记录
    if (this.downloadHistory.length > 100) {
      this.downloadHistory = this.downloadHistory.slice(-100);
    }
    
    console.log('下载历史记录已保存:', historyRecord);
  }

  // 获取下载历史
  getDownloadHistory() {
    return [...this.downloadHistory].reverse(); // 返回副本，最新的在前
  }

  // 清除下载历史
  clearDownloadHistory() {
    this.downloadHistory = [];
    console.log('下载历史已清除');
  }

  // 获取下载统计
  getDownloadStats() {
    const stats = {
      totalDownloads: this.downloadHistory.length,
      excelDownloads: this.downloadHistory.filter(record => record.format === 'excel').length,
      csvDownloads: this.downloadHistory.filter(record => record.format === 'csv').length,
      totalItems: this.downloadHistory.reduce((sum, record) => sum + record.itemCount, 0)
    };
    
    if (this.downloadHistory.length > 0) {
      stats.lastDownload = this.downloadHistory[this.downloadHistory.length - 1].timestamp;
      stats.mostUsedFormat = stats.excelDownloads >= stats.csvDownloads ? 'excel' : 'csv';
    }
    
    return stats;
  }

  // 设置表头样式
  setHeaderStyle(worksheet, colCount) {
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (!worksheet[cellRef]) continue;

      worksheet[cellRef].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "20FF4D" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }
  }

  // 创建汇总页
  createSummarySheet(workbook, data, firstMonth, secondMonth) {
    const summaryData = [
      ['KPI跟踪数据汇总'],
      [''],
      ['导出时间', this.formatDateTime(new Date())],
      ['月份范围', `${firstMonth}月 - ${secondMonth}月`],
      ['总KPI项数', data.length],
      [''],
      ['统计信息'],
      ['项目', '数量', '占比'],
    ];

    // 计算统计信息
    const totalItems = data.length;
    const itemsWithFirstMonth = data.filter(item => item[`${firstMonth}月`] && item[`${firstMonth}月`] !== '').length;
    const itemsWithSecondMonth = data.filter(item => item[`${secondMonth}月`] && item[`${secondMonth}月`] !== '').length;
    const itemsWithFirstScore = data.filter(item => item[`${firstMonth}月得分`] && item[`${firstMonth}月得分`] !== '').length;
    const itemsWithSecondScore = data.filter(item => item[`${secondMonth}月得分`] && item[`${secondMonth}月得分`] !== '').length;

    summaryData.push(
      [`${firstMonth}月有数据`, itemsWithFirstMonth, `${((itemsWithFirstMonth / totalItems) * 100).toFixed(1)}%`],
      [`${secondMonth}月有数据`, itemsWithSecondMonth, `${((itemsWithSecondMonth / totalItems) * 100).toFixed(1)}%`],
      [`${firstMonth}月有得分`, itemsWithFirstScore, `${((itemsWithFirstScore / totalItems) * 100).toFixed(1)}%`],
      [`${secondMonth}月有得分`, itemsWithSecondScore, `${((itemsWithSecondScore / totalItems) * 100).toFixed(1)}%`]
    );

    // 创建汇总工作表
    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);

    // 设置列宽
    summaryWorksheet['!cols'] = [
      { wch: 20 },
      { wch: 15 },
      { wch: 10 }
    ];

    // 设置标题样式
    const titleCellRef = XLSX.utils.encode_cell({ r: 0, c: 0 });
    if (summaryWorksheet[titleCellRef]) {
      summaryWorksheet[titleCellRef].s = {
        font: { bold: true, size: 16, color: { rgb: "000000" } },
        fill: { fgColor: { rgb: "FFE066" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }

    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '汇总信息');
  }

  // 格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // 格式化日期（用于文件名）
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}${month}${day}_${hours}${minutes}`;
  }

  // 生成选择性文件
  async generateSelectiveFile(selectedData, selectionData) {
    const { format } = selectionData;

    switch (format) {
      case 'excel':
        return await this.generateSelectiveExcelFile(selectedData, selectionData);
      case 'csv':
        return await this.generateSelectiveCSVFile(selectedData, selectionData);
      default:
        throw new Error(`不支持的格式: ${format}`);
    }
  }

  // 生成选择性Excel文件（包含全年数据）
  async generateSelectiveExcelFile(selectedData, selectionData) {
    try {
      const { selectedIndicators, statistics } = selectionData;

      // 创建工作簿
      const workbook = XLSX.utils.book_new();

      // 准备全年数据（2月-12月）
      const fullYearData = this.prepareFullYearData(selectedData);

      // 创建主数据表
      const mainSheet = this.createSelectiveMainSheet(fullYearData);
      XLSX.utils.book_append_sheet(workbook, mainSheet, 'KPI数据');

      // 创建汇总表
      const summarySheet = this.createSelectiveSummarySheet(selectedData, selectionData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, '下载汇总');

      // 生成文件
      const filename = `KPI选择性下载_${selectedIndicators.length}个指标_${this.formatDate(new Date())}.xlsx`;
      const buffer = XLSX.write(workbook, { type: 'array', bookType: 'xlsx' });

      // 下载文件
      this.downloadFile(buffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

      return {
        success: true,
        filename: filename,
        downloadUrl: null
      };

    } catch (error) {
      console.error('生成选择性Excel文件失败:', error);
      throw error;
    }
  }

  // 生成选择性CSV文件（包含全年数据）
  async generateSelectiveCSVFile(selectedData, selectionData) {
    try {
      const { selectedIndicators } = selectionData;

      // 准备全年数据（2月-12月）
      const fullYearData = this.prepareFullYearData(selectedData);

      // 构建CSV头部
      const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      const headers = [
        '序号', '指标', '目标值', '分值', '统计方式&口径', '考核标准'
      ];

      // 添加月份列
      months.forEach(month => {
        headers.push(`${month}月份`);
        headers.push(`${month}月份完成情况`);
        headers.push(`${month}月得分`);
      });

      // 构建CSV内容
      let csvContent = '\ufeff'; // BOM for UTF-8
      csvContent += headers.map(header => `"${header}"`).join(',') + '\n';

      // 添加数据行
      fullYearData.forEach(item => {
        const row = [
          item.序号 || '',
          item.指标 || '',
          item.目标值 || '',
          item.分值 || '',
          item['统计方式&口径'] || '',
          item.考核标准 || ''
        ];

        // 添加月份数据
        months.forEach(month => {
          row.push(item[`${month}月份`] || '');
          row.push(item[`${month}月份完成情况`] || '');
          row.push(item[`${month}月得分`] || '');
        });

        csvContent += row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',') + '\n';
      });

      // 生成文件名
      const filename = `KPI选择性下载_${selectedIndicators.length}个指标_${this.formatDate(new Date())}.csv`;

      // 下载文件
      this.downloadFile(csvContent, filename, 'text/csv;charset=utf-8');

      return {
        success: true,
        filename: filename,
        downloadUrl: null
      };

    } catch (error) {
      console.error('生成选择性CSV文件失败:', error);
      throw error;
    }
  }

  // 准备全年数据（2月-12月）
  prepareFullYearData(selectedData) {
    const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    return selectedData.map(item => {
      const fullYearItem = {
        序号: item.序号 || '',
        指标: item.指标 || '',
        目标值: item.目标值 || '',
        分值: item.分值 || '',
        '统计方式&口径': item['统计方式&口径'] || item.统计方式 || '',
        考核标准: item.考核标准 || ''
      };

      // 添加所有月份的数据
      months.forEach(month => {
        fullYearItem[`${month}月份`] = item[`${month}月份`] || '';
        fullYearItem[`${month}月份完成情况`] = item[`${month}月份完成情况`] || '';
        fullYearItem[`${month}月得分`] = item[`${month}月得分`] || '';
      });

      return fullYearItem;
    });
  }

  // 创建选择性主数据表
  createSelectiveMainSheet(fullYearData) {
    const months = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    // 构建表头
    const headers = [
      '序号', '指标', '目标值', '分值', '统计方式&口径', '考核标准'
    ];

    // 添加月份列
    months.forEach(month => {
      headers.push(`${month}月份`);
      headers.push(`${month}月份完成情况`);
      headers.push(`${month}月得分`);
    });

    // 构建数据行
    const rows = [headers];
    fullYearData.forEach(item => {
      const row = [
        item.序号, item.指标, item.目标值, item.分值,
        item['统计方式&口径'], item.考核标准
      ];

      // 添加月份数据
      months.forEach(month => {
        row.push(item[`${month}月份`]);
        row.push(item[`${month}月份完成情况`]);
        row.push(item[`${month}月得分`]);
      });

      rows.push(row);
    });

    return XLSX.utils.aoa_to_sheet(rows);
  }

  // 创建选择性汇总表
  createSelectiveSummarySheet(selectedData, selectionData) {
    const { selectedIndicators, statistics } = selectionData;

    const summaryData = [
      ['KPI选择性下载汇总'],
      [''],
      ['下载时间', this.formatDate(new Date())],
      ['选择指标数量', selectedIndicators.length],
      ['总指标数量', statistics.totalItems],
      ['选择比例', `${((selectedIndicators.length / statistics.totalItems) * 100).toFixed(1)}%`],
      [''],
      ['选择的指标列表'],
      ...selectedIndicators.map((indicator, index) => [`${index + 1}. ${indicator}`]),
      [''],
      ['数据范围说明'],
      ['包含月份', '2月 - 12月（全年数据）'],
      ['数据列', '序号、指标、目标值、分值、统计方式&口径、考核标准'],
      ['月份列', '每月的计划、完成情况、得分'],
      [''],
      ['注意事项'],
      ['1. 只包含选中指标的数据行'],
      ['2. 包含完整的2月-12月全年数据'],
      ['3. 完成情况列为只读数据，请勿修改']
    ];

    return XLSX.utils.aoa_to_sheet(summaryData);
  }

  // 记录选择性下载历史
  recordSelectiveDownloadHistory(selectionData, result) {
    const { selectedIndicators, format, statistics } = selectionData;

    this.downloadHistory.push({
      type: 'selective',
      timestamp: new Date(),
      selectedIndicators: selectedIndicators,
      indicatorCount: selectedIndicators.length,
      totalIndicators: statistics.totalItems,
      format: format,
      filename: result.filename,
      success: result.success
    });

    // 保持历史记录不超过100条
    if (this.downloadHistory.length > 100) {
      this.downloadHistory = this.downloadHistory.slice(-100);
    }
  }
}

const kpiDownloadService = new KPIDownloadService();
export default kpiDownloadService;