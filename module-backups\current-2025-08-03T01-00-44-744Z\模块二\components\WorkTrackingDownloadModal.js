import React, { useState, useEffect } from 'react';
import './WorkTrackingDownloadModal.css';

const WorkTrackingDownloadModal = ({ 
  isOpen, 
  onClose, 
  data, 
  filteredData,
  selectedResponsiblePersons,
  currentMonthPair,
  monthPairs,
  onDownload 
}) => {
  const [selectionState, setSelectionState] = useState({
    selectedWorkTypes: new Set(),
    selectedWorkItems: new Set(),
    downloadFormat: 'excel',
    monthRange: { start: 0, end: monthPairs.length - 1 }, // 月份对索引范围
    includeResponsibleFilter: true
  });

  const [statistics, setStatistics] = useState({
    totalItems: 0,
    workTypeCount: 0,
    monthsCovered: ''
  });

  // 获取当前使用的数据（筛选后或原始数据）
  const getCurrentData = () => {
    // 优先使用原始数据，如果有筛选条件且筛选数据不为空，则使用筛选数据
    if (selectedResponsiblePersons.length > 0 && filteredData && filteredData.length > 0) {
      return filteredData;
    } else if (data && data.length > 0) {
      return data;
    } else {
      return [];
    }
  };

  // 按工作类型分组数据
  const getGroupedData = () => {
    const dataToUse = getCurrentData();
    if (!dataToUse || dataToUse.length === 0) {
      return {};
    }
    
    const grouped = {};
    dataToUse.forEach(item => {
      const type = item.重点工作类型 || '其他';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(item);
    });
    
    return grouped;
  };

  // 获取所有工作类型
  const getWorkTypes = () => {
    const grouped = getGroupedData();
    return Object.keys(grouped);
  };

  useEffect(() => {
    updateStatistics();
  }, [selectionState.selectedWorkItems, selectionState.monthRange, data, filteredData]);

  // 计算统计信息
  const updateStatistics = () => {
    let totalItems = 0;
    const workTypes = new Set();

    const dataToUse = getCurrentData();
    dataToUse.forEach((item, index) => {
      const itemKey = `item-${index}`;
      if (selectionState.selectedWorkItems.has(itemKey)) {
        totalItems++;
        if (item.重点工作类型) {
          workTypes.add(item.重点工作类型);
        }
      }
    });

    // 计算覆盖的月份范围
    const startMonth = monthPairs[selectionState.monthRange.start]?.[0] || '';
    const endMonth = monthPairs[selectionState.monthRange.end]?.[1] || '';
    const monthsCovered = startMonth && endMonth ? `${startMonth} 至 ${endMonth}` : '';

    setStatistics({
      totalItems,
      workTypeCount: workTypes.size,
      monthsCovered
    });
  };

  // 处理工作类型选择
  const handleWorkTypeSelect = (workType) => {
    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);
    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    if (newSelectedWorkTypes.has(workType)) {
      // 取消选择工作类型，移除该类型下的所有项目
      newSelectedWorkTypes.delete(workType);
      grouped[workType]?.forEach(item => {
        const itemIndex = dataToUse.findIndex(d => d === item);
        if (itemIndex >= 0) {
          newSelectedWorkItems.delete(`item-${itemIndex}`);
        }
      });
    } else {
      // 选择工作类型，添加该类型下的所有项目
      newSelectedWorkTypes.add(workType);
      grouped[workType]?.forEach(item => {
        const itemIndex = dataToUse.findIndex(d => d === item);
        if (itemIndex >= 0) {
          newSelectedWorkItems.add(`item-${itemIndex}`);
        }
      });
    }

    setSelectionState({
      ...selectionState,
      selectedWorkTypes: newSelectedWorkTypes,
      selectedWorkItems: newSelectedWorkItems
    });
  };

  // 处理单项选择
  const handleWorkItemSelect = (itemIndex) => {
    const itemKey = `item-${itemIndex}`;
    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);
    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);

    const dataToUse = getCurrentData();
    const item = dataToUse[itemIndex];
    const workType = item?.重点工作类型 || '其他';

    if (newSelectedWorkItems.has(itemKey)) {
      newSelectedWorkItems.delete(itemKey);
      // 检查是否需要取消工作类型选择
      const grouped = getGroupedData();
      const typeItems = grouped[workType] || [];
      const typeItemsSelected = typeItems.some(typeItem => {
        const typeItemIndex = dataToUse.findIndex(d => d === typeItem);
        return typeItemIndex >= 0 && newSelectedWorkItems.has(`item-${typeItemIndex}`);
      });
      if (!typeItemsSelected) {
        newSelectedWorkTypes.delete(workType);
      }
    } else {
      newSelectedWorkItems.add(itemKey);
      // 检查是否需要添加工作类型选择
      const grouped = getGroupedData();
      const typeItems = grouped[workType] || [];
      const allTypeItemsSelected = typeItems.every(typeItem => {
        const typeItemIndex = dataToUse.findIndex(d => d === typeItem);
        return typeItemIndex >= 0 && (newSelectedWorkItems.has(`item-${typeItemIndex}`) || typeItemIndex === itemIndex);
      });
      if (allTypeItemsSelected) {
        newSelectedWorkTypes.add(workType);
      }
    }

    setSelectionState({
      ...selectionState,
      selectedWorkTypes: newSelectedWorkTypes,
      selectedWorkItems: newSelectedWorkItems
    });
  };

  // 工作类型全选
  const handleWorkTypeSelectAll = (workType) => {
    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);
    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    grouped[workType]?.forEach(item => {
      const itemIndex = dataToUse.findIndex(d => d === item);
      if (itemIndex >= 0) {
        newSelectedWorkItems.add(`item-${itemIndex}`);
      }
    });
    newSelectedWorkTypes.add(workType);

    setSelectionState({
      ...selectionState,
      selectedWorkTypes: newSelectedWorkTypes,
      selectedWorkItems: newSelectedWorkItems
    });
  };

  // 工作类型反选
  const handleWorkTypeUnselectAll = (workType) => {
    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);
    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);
    
    const grouped = getGroupedData();
    const dataToUse = getCurrentData();
    
    grouped[workType]?.forEach(item => {
      const itemIndex = dataToUse.findIndex(d => d === item);
      if (itemIndex >= 0) {
        newSelectedWorkItems.delete(`item-${itemIndex}`);
      }
    });
    newSelectedWorkTypes.delete(workType);

    setSelectionState({
      ...selectionState,
      selectedWorkTypes: newSelectedWorkTypes,
      selectedWorkItems: newSelectedWorkItems
    });
  };

  // 处理下载
  const handleDownload = () => {
    const selectedData = [];
    const dataToUse = getCurrentData();
    
    // 收集选中的数据
    dataToUse.forEach((item, index) => {
      const itemKey = `item-${index}`;
      if (selectionState.selectedWorkItems.has(itemKey)) {
        selectedData.push({
          index,
          data: item
        });
      }
    });

    onDownload({
      selectedData,
      format: selectionState.downloadFormat,
      monthRange: selectionState.monthRange,
      responsibleFilter: selectionState.includeResponsibleFilter ? selectedResponsiblePersons : [],
      statistics,
      monthPairs
    });
  };

  // 格式化显示值
  const formatDisplayValue = (value) => {
    if (value === null || value === undefined || value === '') return '';
    if (typeof value === 'object') {
      if (value.hasOwnProperty('v')) return value.v;
      if (value.hasOwnProperty('w')) return value.w;
      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    return String(value);
  };

  if (!isOpen) return null;

  const groupedData = getGroupedData();
  const workTypes = getWorkTypes();

  return (
    <div className="work-tracking-download-overlay">
      <div className="work-tracking-download-modal">
        <div className="modal-header">
          <h2>📈 选择要下载的跟踪数据</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          {/* 筛选条件显示 */}
          <div className="current-filters">
            <div className="filter-item">
              <span className="filter-label">📅 月份范围:</span>
              <select
                value={selectionState.monthRange.start}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  monthRange: { ...selectionState.monthRange, start: Number(e.target.value) }
                })}
              >
                {monthPairs.map((pair, index) => (
                  <option key={index} value={index}>{pair[0]}</option>
                ))}
              </select>
              <span> 至 </span>
              <select
                value={selectionState.monthRange.end}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  monthRange: { ...selectionState.monthRange, end: Number(e.target.value) }
                })}
              >
                {monthPairs.map((pair, index) => (
                  <option key={index} value={index}>{pair[1]}</option>
                ))}
              </select>
            </div>
            
            {selectedResponsiblePersons.length > 0 && (
              <div className="filter-item">
                <span className="filter-label">👥 当前筛选:</span>
                <span className="filter-value">{selectedResponsiblePersons.join('、')}</span>
                <label className="include-filter-checkbox">
                  <input
                    type="checkbox"
                    checked={selectionState.includeResponsibleFilter}
                    onChange={(e) => setSelectionState({
                      ...selectionState,
                      includeResponsibleFilter: e.target.checked
                    })}
                  />
                  应用到下载
                </label>
              </div>
            )}
          </div>

          {/* 工作类型选择 */}
          <div className="work-types-section">
            {workTypes.map(workType => {
              const typeData = groupedData[workType] || [];
              const isTypeSelected = selectionState.selectedWorkTypes.has(workType);
              
              return (
                <div key={workType} className="work-type-section">
                  <div className="work-type-header">
                    <label className="work-type-checkbox">
                      <input
                        type="checkbox"
                        checked={isTypeSelected}
                        onChange={() => handleWorkTypeSelect(workType)}
                      />
                      <span className="work-type-title">
                        {workType} ({typeData.length}项)
                      </span>
                    </label>
                    <div className="work-type-actions">
                      <button 
                        className="action-btn"
                        onClick={() => handleWorkTypeSelectAll(workType)}
                      >
                        全选
                      </button>
                      <button 
                        className="action-btn"
                        onClick={() => handleWorkTypeUnselectAll(workType)}
                      >
                        反选
                      </button>
                    </div>
                  </div>

                  <div className="work-items-list">
                    {typeData.map((item, typeIndex) => {
                      const dataToUse = getCurrentData();
                      const globalIndex = dataToUse.findIndex(d => d === item);
                      const itemKey = `item-${globalIndex}`;
                      const isItemSelected = selectionState.selectedWorkItems.has(itemKey);
                      
                      // 尝试多个可能的字段名获取工作名称
                      const possibleNameFields = [
                        '重点工作名称', '工作名称', '指标', '重点工作', '名称', '项目名称', '工作内容'
                      ];
                      let workName = '';
                      for (const field of possibleNameFields) {
                        if (item[field]) {
                          workName = formatDisplayValue(item[field]);
                          break;
                        }
                      }
                      
                      // 如果没有找到名称字段，使用第一个非空字段作为标识
                      if (!workName) {
                        const allFields = Object.keys(item);
                        for (const field of allFields) {
                          if (field !== '重点工作类型' && field !== '序号' && item[field]) {
                            workName = `${field}: ${formatDisplayValue(item[field])}`;
                            break;
                          }
                        }
                      }
                      
                      const responsible = formatDisplayValue(item.责任人);

                      return (
                        <div key={typeIndex} className="work-item-row">
                          <label className="work-item-checkbox">
                            <input
                              type="checkbox"
                              checked={isItemSelected}
                              onChange={() => handleWorkItemSelect(globalIndex)}
                            />
                            <span className="work-item-content">
                              <span className="work-item-name">{workName}</span>
                              {responsible && (
                                <span className="work-item-responsible">负责人: {responsible}</span>
                              )}
                            </span>
                          </label>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <div className="modal-footer">
          <div className="statistics">
            <span>已选择: {statistics.totalItems}项工作</span>
            <span>涵盖类型: {statistics.workTypeCount}种</span>
            {statistics.monthsCovered && (
              <span>月份: {statistics.monthsCovered}</span>
            )}
          </div>

          <div className="format-selection">
            <label>
              下载格式:
              <select 
                value={selectionState.downloadFormat}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  downloadFormat: e.target.value
                })}
              >
                <option value="excel">Excel格式</option>
                <option value="pdf">PDF格式</option>
                <option value="csv">CSV格式</option>
              </select>
            </label>
          </div>

          <div className="action-buttons">
            <button className="cancel-btn" onClick={onClose}>
              取消
            </button>
            <button 
              className="download-btn"
              onClick={handleDownload}
              disabled={statistics.totalItems === 0}
            >
              {selectionState.downloadFormat.toUpperCase()}下载
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkTrackingDownloadModal; 