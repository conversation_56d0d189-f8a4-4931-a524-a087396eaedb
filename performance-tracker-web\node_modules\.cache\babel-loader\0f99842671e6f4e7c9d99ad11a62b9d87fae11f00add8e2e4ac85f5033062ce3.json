{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// 认证服务 - 处理用户登录、登出、会话管理等功能\nconst API_BASE_URL='http://localhost:3001/api';class AuthService{constructor(){this.currentUser=null;this.isAuthenticated=false;this.sessionTimeout=8*60*60*1000;// 8小时\nthis.sessionTimer=null;}// 用户登录\nasync login(username,password){let rememberMe=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{const response=await fetch(\"\".concat(API_BASE_URL,\"/auth/login\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({username:username.trim(),password:password,rememberMe})});const data=await response.json();if(data.success){this.currentUser=data.user;this.isAuthenticated=true;// 存储登录状态\nif(rememberMe){localStorage.setItem('auth_user',JSON.stringify(data.user));localStorage.setItem('auth_token',data.token);localStorage.setItem('auth_remember','true');}else{sessionStorage.setItem('auth_user',JSON.stringify(data.user));sessionStorage.setItem('auth_token',data.token);}// 设置会话超时\nthis.startSessionTimer();return{success:true,user:data.user,message:'登录成功'};}else{return{success:false,message:data.message||'登录失败'};}}catch(error){console.error('登录错误:',error);return{success:false,message:'网络连接错误，请检查服务器状态'};}}// 用户登出\nasync logout(){try{// 清除本地存储\nlocalStorage.removeItem('auth_user');localStorage.removeItem('auth_token');localStorage.removeItem('auth_remember');sessionStorage.removeItem('auth_user');sessionStorage.removeItem('auth_token');// 清除会话定时器\nif(this.sessionTimer){clearTimeout(this.sessionTimer);this.sessionTimer=null;}// 重置状态\nthis.currentUser=null;this.isAuthenticated=false;// 通知服务器登出（可选）\nconst token=this.getToken();if(token){await fetch(\"\".concat(API_BASE_URL,\"/auth/logout\"),{method:'POST',headers:{'Authorization':\"Bearer \".concat(token),'Content-Type':'application/json'}});}return{success:true,message:'已安全登出'};}catch(error){console.error('登出错误:',error);return{success:false,message:'登出过程中发生错误'};}}// 检查登录状态\ncheckAuthStatus(){try{// 优先检查localStorage（记住我）\nlet userData=localStorage.getItem('auth_user');let token=localStorage.getItem('auth_token');let isRemembered=localStorage.getItem('auth_remember')==='true';// 如果没有记住我的数据，检查sessionStorage\nif(!userData||!token){userData=sessionStorage.getItem('auth_user');token=sessionStorage.getItem('auth_token');isRemembered=false;}if(userData&&token){this.currentUser=JSON.parse(userData);this.isAuthenticated=true;// 如果是记住我的登录，重新设置会话定时器\nif(isRemembered){this.startSessionTimer();}return true;}return false;}catch(error){console.error('检查认证状态错误:',error);this.clearAuthData();return false;}}// 获取当前用户信息\ngetCurrentUser(){return this.currentUser;}// 获取用户角色\ngetUserRole(){var _this$currentUser;return((_this$currentUser=this.currentUser)===null||_this$currentUser===void 0?void 0:_this$currentUser.role)||null;}// 获取认证token\ngetToken(){return localStorage.getItem('auth_token')||sessionStorage.getItem('auth_token');}// 检查是否已登录\nisLoggedIn(){return this.isAuthenticated&&this.currentUser!==null;}// 开始会话定时器\nstartSessionTimer(){// 清除现有定时器\nif(this.sessionTimer){clearTimeout(this.sessionTimer);}// 设置新的定时器\nthis.sessionTimer=setTimeout(()=>{this.handleSessionTimeout();},this.sessionTimeout);}// 处理会话超时\nhandleSessionTimeout(){console.log('会话已超时，自动登出');this.logout();// 触发全局事件\nwindow.dispatchEvent(new CustomEvent('tokenExpired'));// 可以在这里添加超时提示\nif(window.showNotification){window.showNotification('会话已超时，请重新登录','warning');}}// 刷新会话\nrefreshSession(){if(this.isAuthenticated){this.startSessionTimer();}}// 清除认证数据\nclearAuthData(){localStorage.removeItem('auth_user');localStorage.removeItem('auth_token');localStorage.removeItem('auth_remember');sessionStorage.removeItem('auth_user');sessionStorage.removeItem('auth_token');this.currentUser=null;this.isAuthenticated=false;if(this.sessionTimer){clearTimeout(this.sessionTimer);this.sessionTimer=null;}}// 验证token有效性\nasync validateToken(){try{const token=this.getToken();if(!token)return false;const response=await fetch(\"\".concat(API_BASE_URL,\"/auth/validate\"),{method:'POST',headers:{'Authorization':\"Bearer \".concat(token),'Content-Type':'application/json'}});const data=await response.json();if(data.success){// 更新用户信息\nthis.currentUser=data.user;this.isAuthenticated=true;return true;}else{// Token无效，尝试刷新\nreturn await this.refreshToken();}}catch(error){console.error('Token验证错误:',error);// 网络错误时也尝试刷新\nreturn await this.refreshToken();}}// 刷新token\nasync refreshToken(){try{const token=this.getToken();if(!token)return false;const response=await fetch(\"\".concat(API_BASE_URL,\"/auth/refresh\"),{method:'POST',headers:{'Authorization':\"Bearer \".concat(token),'Content-Type':'application/json'}});const data=await response.json();if(data.success){// 更新token和用户信息\nthis.currentUser=data.user;this.isAuthenticated=true;// 更新存储的token\nif(localStorage.getItem('auth_token')){localStorage.setItem('auth_token',data.token);}if(sessionStorage.getItem('auth_token')){sessionStorage.setItem('auth_token',data.token);}// 重新开始会话定时器\nthis.startSessionTimer();// 触发全局事件\nwindow.dispatchEvent(new CustomEvent('tokenRefreshed'));console.log('Token自动刷新成功');return true;}else{this.clearAuthData();return false;}}catch(error){console.error('Token刷新错误:',error);this.clearAuthData();return false;}}// 更新用户信息\nupdateUserInfo(updatedUser){if(this.currentUser&&updatedUser){this.currentUser=_objectSpread(_objectSpread({},this.currentUser),updatedUser);// 更新存储的用户信息\nconst userJson=JSON.stringify(this.currentUser);if(localStorage.getItem('auth_user')){localStorage.setItem('auth_user',userJson);}if(sessionStorage.getItem('auth_user')){sessionStorage.setItem('auth_user',userJson);}}}// 带自动刷新token的fetch请求\nasync authenticatedFetch(url){let options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{// 添加认证头\nconst token=this.getToken();if(token){options.headers=_objectSpread(_objectSpread({},options.headers),{},{'Authorization':\"Bearer \".concat(token)});}const response=await fetch(url,options);// 检查是否是401未授权错误\nif(response.status===401){console.log('检测到401错误，尝试刷新token...');// 尝试刷新token\nconst refreshSuccess=await this.refreshToken();if(refreshSuccess){// 刷新成功，重新发送请求\nconst newToken=this.getToken();options.headers=_objectSpread(_objectSpread({},options.headers),{},{'Authorization':\"Bearer \".concat(newToken)});return await fetch(url,options);}else{// 刷新失败，返回原始响应\nreturn response;}}return response;}catch(error){console.error('认证请求错误:',error);throw error;}}}// 创建单例实例\nconst authService=new AuthService();export default authService;", "map": {"version": 3, "names": ["API_BASE_URL", "AuthService", "constructor", "currentUser", "isAuthenticated", "sessionTimeout", "sessionTimer", "login", "username", "password", "rememberMe", "arguments", "length", "undefined", "response", "fetch", "concat", "method", "headers", "body", "JSON", "stringify", "trim", "data", "json", "success", "user", "localStorage", "setItem", "token", "sessionStorage", "startSessionTimer", "message", "error", "console", "logout", "removeItem", "clearTimeout", "getToken", "checkAuthStatus", "userData", "getItem", "isRemembered", "parse", "clearAuthData", "getCurrentUser", "getUserRole", "_this$currentUser", "role", "isLoggedIn", "setTimeout", "handleSessionTimeout", "log", "window", "dispatchEvent", "CustomEvent", "showNotification", "refreshSession", "validateToken", "refreshToken", "updateUserInfo", "updatedUser", "_objectSpread", "userJson", "authenticatedFetch", "url", "options", "status", "refreshSuccess", "newToken", "authService"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/services/authService.js"], "sourcesContent": ["// 认证服务 - 处理用户登录、登出、会话管理等功能\n\nconst API_BASE_URL = 'http://localhost:3001/api';\n\nclass AuthService {\n  constructor() {\n    this.currentUser = null;\n    this.isAuthenticated = false;\n    this.sessionTimeout = 8 * 60 * 60 * 1000; // 8小时\n    this.sessionTimer = null;\n  }\n\n  // 用户登录\n  async login(username, password, rememberMe = false) {\n    try {\n      const response = await fetch(`${API_BASE_URL}/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          username: username.trim(),\n          password: password,\n          rememberMe\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        this.currentUser = data.user;\n        this.isAuthenticated = true;\n        \n        // 存储登录状态\n        if (rememberMe) {\n          localStorage.setItem('auth_user', JSON.stringify(data.user));\n          localStorage.setItem('auth_token', data.token);\n          localStorage.setItem('auth_remember', 'true');\n        } else {\n          sessionStorage.setItem('auth_user', JSON.stringify(data.user));\n          sessionStorage.setItem('auth_token', data.token);\n        }\n\n        // 设置会话超时\n        this.startSessionTimer();\n\n        return {\n          success: true,\n          user: data.user,\n          message: '登录成功'\n        };\n      } else {\n        return {\n          success: false,\n          message: data.message || '登录失败'\n        };\n      }\n    } catch (error) {\n      console.error('登录错误:', error);\n      return {\n        success: false,\n        message: '网络连接错误，请检查服务器状态'\n      };\n    }\n  }\n\n  // 用户登出\n  async logout() {\n    try {\n      // 清除本地存储\n      localStorage.removeItem('auth_user');\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('auth_remember');\n      sessionStorage.removeItem('auth_user');\n      sessionStorage.removeItem('auth_token');\n\n      // 清除会话定时器\n      if (this.sessionTimer) {\n        clearTimeout(this.sessionTimer);\n        this.sessionTimer = null;\n      }\n\n      // 重置状态\n      this.currentUser = null;\n      this.isAuthenticated = false;\n\n      // 通知服务器登出（可选）\n      const token = this.getToken();\n      if (token) {\n        await fetch(`${API_BASE_URL}/auth/logout`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n            'Content-Type': 'application/json',\n          },\n        });\n      }\n\n      return { success: true, message: '已安全登出' };\n    } catch (error) {\n      console.error('登出错误:', error);\n      return { success: false, message: '登出过程中发生错误' };\n    }\n  }\n\n  // 检查登录状态\n  checkAuthStatus() {\n    try {\n      // 优先检查localStorage（记住我）\n      let userData = localStorage.getItem('auth_user');\n      let token = localStorage.getItem('auth_token');\n      let isRemembered = localStorage.getItem('auth_remember') === 'true';\n\n      // 如果没有记住我的数据，检查sessionStorage\n      if (!userData || !token) {\n        userData = sessionStorage.getItem('auth_user');\n        token = sessionStorage.getItem('auth_token');\n        isRemembered = false;\n      }\n\n      if (userData && token) {\n        this.currentUser = JSON.parse(userData);\n        this.isAuthenticated = true;\n        \n        // 如果是记住我的登录，重新设置会话定时器\n        if (isRemembered) {\n          this.startSessionTimer();\n        }\n\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      console.error('检查认证状态错误:', error);\n      this.clearAuthData();\n      return false;\n    }\n  }\n\n  // 获取当前用户信息\n  getCurrentUser() {\n    return this.currentUser;\n  }\n\n  // 获取用户角色\n  getUserRole() {\n    return this.currentUser?.role || null;\n  }\n\n  // 获取认证token\n  getToken() {\n    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\n  }\n\n  // 检查是否已登录\n  isLoggedIn() {\n    return this.isAuthenticated && this.currentUser !== null;\n  }\n\n  // 开始会话定时器\n  startSessionTimer() {\n    // 清除现有定时器\n    if (this.sessionTimer) {\n      clearTimeout(this.sessionTimer);\n    }\n\n    // 设置新的定时器\n    this.sessionTimer = setTimeout(() => {\n      this.handleSessionTimeout();\n    }, this.sessionTimeout);\n  }\n\n  // 处理会话超时\n  handleSessionTimeout() {\n    console.log('会话已超时，自动登出');\n    this.logout();\n    \n    // 触发全局事件\n    window.dispatchEvent(new CustomEvent('tokenExpired'));\n    \n    // 可以在这里添加超时提示\n    if (window.showNotification) {\n      window.showNotification('会话已超时，请重新登录', 'warning');\n    }\n  }\n\n  // 刷新会话\n  refreshSession() {\n    if (this.isAuthenticated) {\n      this.startSessionTimer();\n    }\n  }\n\n  // 清除认证数据\n  clearAuthData() {\n    localStorage.removeItem('auth_user');\n    localStorage.removeItem('auth_token');\n    localStorage.removeItem('auth_remember');\n    sessionStorage.removeItem('auth_user');\n    sessionStorage.removeItem('auth_token');\n    \n    this.currentUser = null;\n    this.isAuthenticated = false;\n    \n    if (this.sessionTimer) {\n      clearTimeout(this.sessionTimer);\n      this.sessionTimer = null;\n    }\n  }\n\n  // 验证token有效性\n  async validateToken() {\n    try {\n      const token = this.getToken();\n      if (!token) return false;\n\n      const response = await fetch(`${API_BASE_URL}/auth/validate`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n      \n      if (data.success) {\n        // 更新用户信息\n        this.currentUser = data.user;\n        this.isAuthenticated = true;\n        return true;\n      } else {\n        // Token无效，尝试刷新\n        return await this.refreshToken();\n      }\n    } catch (error) {\n      console.error('Token验证错误:', error);\n      // 网络错误时也尝试刷新\n      return await this.refreshToken();\n    }\n  }\n\n  // 刷新token\n  async refreshToken() {\n    try {\n      const token = this.getToken();\n      if (!token) return false;\n\n      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`,\n          'Content-Type': 'application/json',\n        },\n      });\n\n      const data = await response.json();\n      \n      if (data.success) {\n        // 更新token和用户信息\n        this.currentUser = data.user;\n        this.isAuthenticated = true;\n        \n        // 更新存储的token\n        if (localStorage.getItem('auth_token')) {\n          localStorage.setItem('auth_token', data.token);\n        }\n        if (sessionStorage.getItem('auth_token')) {\n          sessionStorage.setItem('auth_token', data.token);\n        }\n        \n        // 重新开始会话定时器\n        this.startSessionTimer();\n        \n        // 触发全局事件\n        window.dispatchEvent(new CustomEvent('tokenRefreshed'));\n        \n        console.log('Token自动刷新成功');\n        return true;\n      } else {\n        this.clearAuthData();\n        return false;\n      }\n    } catch (error) {\n      console.error('Token刷新错误:', error);\n      this.clearAuthData();\n      return false;\n    }\n  }\n\n  // 更新用户信息\n  updateUserInfo(updatedUser) {\n    if (this.currentUser && updatedUser) {\n      this.currentUser = { ...this.currentUser, ...updatedUser };\n      \n      // 更新存储的用户信息\n      const userJson = JSON.stringify(this.currentUser);\n      if (localStorage.getItem('auth_user')) {\n        localStorage.setItem('auth_user', userJson);\n      }\n      if (sessionStorage.getItem('auth_user')) {\n        sessionStorage.setItem('auth_user', userJson);\n      }\n    }\n  }\n\n  // 带自动刷新token的fetch请求\n  async authenticatedFetch(url, options = {}) {\n    try {\n      // 添加认证头\n      const token = this.getToken();\n      if (token) {\n        options.headers = {\n          ...options.headers,\n          'Authorization': `Bearer ${token}`,\n        };\n      }\n\n      const response = await fetch(url, options);\n      \n      // 检查是否是401未授权错误\n      if (response.status === 401) {\n        console.log('检测到401错误，尝试刷新token...');\n        \n        // 尝试刷新token\n        const refreshSuccess = await this.refreshToken();\n        \n        if (refreshSuccess) {\n          // 刷新成功，重新发送请求\n          const newToken = this.getToken();\n          options.headers = {\n            ...options.headers,\n            'Authorization': `Bearer ${newToken}`,\n          };\n          return await fetch(url, options);\n        } else {\n          // 刷新失败，返回原始响应\n          return response;\n        }\n      }\n      \n      return response;\n    } catch (error) {\n      console.error('认证请求错误:', error);\n      throw error;\n    }\n  }\n}\n\n// 创建单例实例\nconst authService = new AuthService();\n\nexport default authService;\n"], "mappings": "+MAAA;AAEA,KAAM,CAAAA,YAAY,CAAG,2BAA2B,CAEhD,KAAM,CAAAC,WAAY,CAChBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,WAAW,CAAG,IAAI,CACvB,IAAI,CAACC,eAAe,CAAG,KAAK,CAC5B,IAAI,CAACC,cAAc,CAAG,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAE;AAC1C,IAAI,CAACC,YAAY,CAAG,IAAI,CAC1B,CAEA;AACA,KAAM,CAAAC,KAAKA,CAACC,QAAQ,CAAEC,QAAQ,CAAsB,IAApB,CAAAC,UAAU,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAChD,GAAI,CACF,KAAM,CAAAG,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIhB,YAAY,gBAAe,CACzDiB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBb,QAAQ,CAAEA,QAAQ,CAACc,IAAI,CAAC,CAAC,CACzBb,QAAQ,CAAEA,QAAQ,CAClBC,UACF,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAa,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACU,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChB,IAAI,CAACtB,WAAW,CAAGoB,IAAI,CAACG,IAAI,CAC5B,IAAI,CAACtB,eAAe,CAAG,IAAI,CAE3B;AACA,GAAIM,UAAU,CAAE,CACdiB,YAAY,CAACC,OAAO,CAAC,WAAW,CAAER,IAAI,CAACC,SAAS,CAACE,IAAI,CAACG,IAAI,CAAC,CAAC,CAC5DC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEL,IAAI,CAACM,KAAK,CAAC,CAC9CF,YAAY,CAACC,OAAO,CAAC,eAAe,CAAE,MAAM,CAAC,CAC/C,CAAC,IAAM,CACLE,cAAc,CAACF,OAAO,CAAC,WAAW,CAAER,IAAI,CAACC,SAAS,CAACE,IAAI,CAACG,IAAI,CAAC,CAAC,CAC9DI,cAAc,CAACF,OAAO,CAAC,YAAY,CAAEL,IAAI,CAACM,KAAK,CAAC,CAClD,CAEA;AACA,IAAI,CAACE,iBAAiB,CAAC,CAAC,CAExB,MAAO,CACLN,OAAO,CAAE,IAAI,CACbC,IAAI,CAAEH,IAAI,CAACG,IAAI,CACfM,OAAO,CAAE,MACX,CAAC,CACH,CAAC,IAAM,CACL,MAAO,CACLP,OAAO,CAAE,KAAK,CACdO,OAAO,CAAET,IAAI,CAACS,OAAO,EAAI,MAC3B,CAAC,CACH,CACF,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,MAAO,CACLR,OAAO,CAAE,KAAK,CACdO,OAAO,CAAE,iBACX,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAG,MAAMA,CAAA,CAAG,CACb,GAAI,CACF;AACAR,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC,CACpCT,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC,CACrCT,YAAY,CAACS,UAAU,CAAC,eAAe,CAAC,CACxCN,cAAc,CAACM,UAAU,CAAC,WAAW,CAAC,CACtCN,cAAc,CAACM,UAAU,CAAC,YAAY,CAAC,CAEvC;AACA,GAAI,IAAI,CAAC9B,YAAY,CAAE,CACrB+B,YAAY,CAAC,IAAI,CAAC/B,YAAY,CAAC,CAC/B,IAAI,CAACA,YAAY,CAAG,IAAI,CAC1B,CAEA;AACA,IAAI,CAACH,WAAW,CAAG,IAAI,CACvB,IAAI,CAACC,eAAe,CAAG,KAAK,CAE5B;AACA,KAAM,CAAAyB,KAAK,CAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC7B,GAAIT,KAAK,CAAE,CACT,KAAM,CAAAd,KAAK,IAAAC,MAAA,CAAIhB,YAAY,iBAAgB,CACzCiB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,eAAe,WAAAF,MAAA,CAAYa,KAAK,CAAE,CAClC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CACJ,CAEA,MAAO,CAAEJ,OAAO,CAAE,IAAI,CAAEO,OAAO,CAAE,OAAQ,CAAC,CAC5C,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,MAAO,CAAER,OAAO,CAAE,KAAK,CAAEO,OAAO,CAAE,WAAY,CAAC,CACjD,CACF,CAEA;AACAO,eAAeA,CAAA,CAAG,CAChB,GAAI,CACF;AACA,GAAI,CAAAC,QAAQ,CAAGb,YAAY,CAACc,OAAO,CAAC,WAAW,CAAC,CAChD,GAAI,CAAAZ,KAAK,CAAGF,YAAY,CAACc,OAAO,CAAC,YAAY,CAAC,CAC9C,GAAI,CAAAC,YAAY,CAAGf,YAAY,CAACc,OAAO,CAAC,eAAe,CAAC,GAAK,MAAM,CAEnE;AACA,GAAI,CAACD,QAAQ,EAAI,CAACX,KAAK,CAAE,CACvBW,QAAQ,CAAGV,cAAc,CAACW,OAAO,CAAC,WAAW,CAAC,CAC9CZ,KAAK,CAAGC,cAAc,CAACW,OAAO,CAAC,YAAY,CAAC,CAC5CC,YAAY,CAAG,KAAK,CACtB,CAEA,GAAIF,QAAQ,EAAIX,KAAK,CAAE,CACrB,IAAI,CAAC1B,WAAW,CAAGiB,IAAI,CAACuB,KAAK,CAACH,QAAQ,CAAC,CACvC,IAAI,CAACpC,eAAe,CAAG,IAAI,CAE3B;AACA,GAAIsC,YAAY,CAAE,CAChB,IAAI,CAACX,iBAAiB,CAAC,CAAC,CAC1B,CAEA,MAAO,KAAI,CACb,CAEA,MAAO,MAAK,CACd,CAAE,MAAOE,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,IAAI,CAACW,aAAa,CAAC,CAAC,CACpB,MAAO,MAAK,CACd,CACF,CAEA;AACAC,cAAcA,CAAA,CAAG,CACf,MAAO,KAAI,CAAC1C,WAAW,CACzB,CAEA;AACA2C,WAAWA,CAAA,CAAG,KAAAC,iBAAA,CACZ,MAAO,EAAAA,iBAAA,KAAI,CAAC5C,WAAW,UAAA4C,iBAAA,iBAAhBA,iBAAA,CAAkBC,IAAI,GAAI,IAAI,CACvC,CAEA;AACAV,QAAQA,CAAA,CAAG,CACT,MAAO,CAAAX,YAAY,CAACc,OAAO,CAAC,YAAY,CAAC,EAAIX,cAAc,CAACW,OAAO,CAAC,YAAY,CAAC,CACnF,CAEA;AACAQ,UAAUA,CAAA,CAAG,CACX,MAAO,KAAI,CAAC7C,eAAe,EAAI,IAAI,CAACD,WAAW,GAAK,IAAI,CAC1D,CAEA;AACA4B,iBAAiBA,CAAA,CAAG,CAClB;AACA,GAAI,IAAI,CAACzB,YAAY,CAAE,CACrB+B,YAAY,CAAC,IAAI,CAAC/B,YAAY,CAAC,CACjC,CAEA;AACA,IAAI,CAACA,YAAY,CAAG4C,UAAU,CAAC,IAAM,CACnC,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAC7B,CAAC,CAAE,IAAI,CAAC9C,cAAc,CAAC,CACzB,CAEA;AACA8C,oBAAoBA,CAAA,CAAG,CACrBjB,OAAO,CAACkB,GAAG,CAAC,YAAY,CAAC,CACzB,IAAI,CAACjB,MAAM,CAAC,CAAC,CAEb;AACAkB,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAErD;AACA,GAAIF,MAAM,CAACG,gBAAgB,CAAE,CAC3BH,MAAM,CAACG,gBAAgB,CAAC,aAAa,CAAE,SAAS,CAAC,CACnD,CACF,CAEA;AACAC,cAAcA,CAAA,CAAG,CACf,GAAI,IAAI,CAACrD,eAAe,CAAE,CACxB,IAAI,CAAC2B,iBAAiB,CAAC,CAAC,CAC1B,CACF,CAEA;AACAa,aAAaA,CAAA,CAAG,CACdjB,YAAY,CAACS,UAAU,CAAC,WAAW,CAAC,CACpCT,YAAY,CAACS,UAAU,CAAC,YAAY,CAAC,CACrCT,YAAY,CAACS,UAAU,CAAC,eAAe,CAAC,CACxCN,cAAc,CAACM,UAAU,CAAC,WAAW,CAAC,CACtCN,cAAc,CAACM,UAAU,CAAC,YAAY,CAAC,CAEvC,IAAI,CAACjC,WAAW,CAAG,IAAI,CACvB,IAAI,CAACC,eAAe,CAAG,KAAK,CAE5B,GAAI,IAAI,CAACE,YAAY,CAAE,CACrB+B,YAAY,CAAC,IAAI,CAAC/B,YAAY,CAAC,CAC/B,IAAI,CAACA,YAAY,CAAG,IAAI,CAC1B,CACF,CAEA;AACA,KAAM,CAAAoD,aAAaA,CAAA,CAAG,CACpB,GAAI,CACF,KAAM,CAAA7B,KAAK,CAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC7B,GAAI,CAACT,KAAK,CAAE,MAAO,MAAK,CAExB,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIhB,YAAY,mBAAkB,CAC5DiB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,eAAe,WAAAF,MAAA,CAAYa,KAAK,CAAE,CAClC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAN,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACU,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChB;AACA,IAAI,CAACtB,WAAW,CAAGoB,IAAI,CAACG,IAAI,CAC5B,IAAI,CAACtB,eAAe,CAAG,IAAI,CAC3B,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,MAAO,MAAM,KAAI,CAACuD,YAAY,CAAC,CAAC,CAClC,CACF,CAAE,MAAO1B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC;AACA,MAAO,MAAM,KAAI,CAAC0B,YAAY,CAAC,CAAC,CAClC,CACF,CAEA;AACA,KAAM,CAAAA,YAAYA,CAAA,CAAG,CACnB,GAAI,CACF,KAAM,CAAA9B,KAAK,CAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC7B,GAAI,CAACT,KAAK,CAAE,MAAO,MAAK,CAExB,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAIhB,YAAY,kBAAiB,CAC3DiB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,eAAe,WAAAF,MAAA,CAAYa,KAAK,CAAE,CAClC,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAN,IAAI,CAAG,KAAM,CAAAT,QAAQ,CAACU,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChB;AACA,IAAI,CAACtB,WAAW,CAAGoB,IAAI,CAACG,IAAI,CAC5B,IAAI,CAACtB,eAAe,CAAG,IAAI,CAE3B;AACA,GAAIuB,YAAY,CAACc,OAAO,CAAC,YAAY,CAAC,CAAE,CACtCd,YAAY,CAACC,OAAO,CAAC,YAAY,CAAEL,IAAI,CAACM,KAAK,CAAC,CAChD,CACA,GAAIC,cAAc,CAACW,OAAO,CAAC,YAAY,CAAC,CAAE,CACxCX,cAAc,CAACF,OAAO,CAAC,YAAY,CAAEL,IAAI,CAACM,KAAK,CAAC,CAClD,CAEA;AACA,IAAI,CAACE,iBAAiB,CAAC,CAAC,CAExB;AACAsB,MAAM,CAACC,aAAa,CAAC,GAAI,CAAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC,CAEvDrB,OAAO,CAACkB,GAAG,CAAC,aAAa,CAAC,CAC1B,MAAO,KAAI,CACb,CAAC,IAAM,CACL,IAAI,CAACR,aAAa,CAAC,CAAC,CACpB,MAAO,MAAK,CACd,CACF,CAAE,MAAOX,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,IAAI,CAACW,aAAa,CAAC,CAAC,CACpB,MAAO,MAAK,CACd,CACF,CAEA;AACAgB,cAAcA,CAACC,WAAW,CAAE,CAC1B,GAAI,IAAI,CAAC1D,WAAW,EAAI0D,WAAW,CAAE,CACnC,IAAI,CAAC1D,WAAW,CAAA2D,aAAA,CAAAA,aAAA,IAAQ,IAAI,CAAC3D,WAAW,EAAK0D,WAAW,CAAE,CAE1D;AACA,KAAM,CAAAE,QAAQ,CAAG3C,IAAI,CAACC,SAAS,CAAC,IAAI,CAAClB,WAAW,CAAC,CACjD,GAAIwB,YAAY,CAACc,OAAO,CAAC,WAAW,CAAC,CAAE,CACrCd,YAAY,CAACC,OAAO,CAAC,WAAW,CAAEmC,QAAQ,CAAC,CAC7C,CACA,GAAIjC,cAAc,CAACW,OAAO,CAAC,WAAW,CAAC,CAAE,CACvCX,cAAc,CAACF,OAAO,CAAC,WAAW,CAAEmC,QAAQ,CAAC,CAC/C,CACF,CACF,CAEA;AACA,KAAM,CAAAC,kBAAkBA,CAACC,GAAG,CAAgB,IAAd,CAAAC,OAAO,CAAAvD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACxC,GAAI,CACF;AACA,KAAM,CAAAkB,KAAK,CAAG,IAAI,CAACS,QAAQ,CAAC,CAAC,CAC7B,GAAIT,KAAK,CAAE,CACTqC,OAAO,CAAChD,OAAO,CAAA4C,aAAA,CAAAA,aAAA,IACVI,OAAO,CAAChD,OAAO,MAClB,eAAe,WAAAF,MAAA,CAAYa,KAAK,CAAE,EACnC,CACH,CAEA,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACkD,GAAG,CAAEC,OAAO,CAAC,CAE1C;AACA,GAAIpD,QAAQ,CAACqD,MAAM,GAAK,GAAG,CAAE,CAC3BjC,OAAO,CAACkB,GAAG,CAAC,uBAAuB,CAAC,CAEpC;AACA,KAAM,CAAAgB,cAAc,CAAG,KAAM,KAAI,CAACT,YAAY,CAAC,CAAC,CAEhD,GAAIS,cAAc,CAAE,CAClB;AACA,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAAC/B,QAAQ,CAAC,CAAC,CAChC4B,OAAO,CAAChD,OAAO,CAAA4C,aAAA,CAAAA,aAAA,IACVI,OAAO,CAAChD,OAAO,MAClB,eAAe,WAAAF,MAAA,CAAYqD,QAAQ,CAAE,EACtC,CACD,MAAO,MAAM,CAAAtD,KAAK,CAACkD,GAAG,CAAEC,OAAO,CAAC,CAClC,CAAC,IAAM,CACL;AACA,MAAO,CAAApD,QAAQ,CACjB,CACF,CAEA,MAAO,CAAAA,QAAQ,CACjB,CAAE,MAAOmB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,CAAAA,KAAK,CACb,CACF,CACF,CAEA;AACA,KAAM,CAAAqC,WAAW,CAAG,GAAI,CAAArE,WAAW,CAAC,CAAC,CAErC,cAAe,CAAAqE,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}