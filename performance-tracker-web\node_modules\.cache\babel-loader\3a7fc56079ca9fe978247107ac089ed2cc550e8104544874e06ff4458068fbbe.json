{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'../styles/UserManagement.css';import authService from'../services/authService';import{ROLES,ROLE_DISPLAY_NAMES,ROLE_DESCRIPTIONS}from'../utils/rolePermissions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const UserManagement=_ref=>{let{onNavigate,isAuthenticated,currentUser,userRole}=_ref;const[users,setUsers]=useState([]);const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[searchTerm,setSearchTerm]=useState('');const[roleFilter,setRoleFilter]=useState('all');const[statusFilter,setStatusFilter]=useState('all');const[showCreateModal,setShowCreateModal]=useState(false);const[showEditModal,setShowEditModal]=useState(false);const[showViewModal,setShowViewModal]=useState(false);const[selectedUser,setSelectedUser]=useState(null);useEffect(()=>{if(userRole==='super_admin'||userRole==='manager'){loadUsers();}},[userRole]);const loadUsers=async()=>{try{setLoading(true);const response=await authService.authenticatedFetch('http://localhost:3001/api/auth/users',{method:'GET',headers:{'Content-Type':'application/json'}});const data=await response.json();if(data.success){setUsers(data.users);}else{setError(data.message);}}catch(error){console.error('加载用户列表失败:',error);setError('加载用户列表失败，请检查网络连接');}finally{setLoading(false);}};const handleCreateUser=()=>{setSelectedUser(null);setShowCreateModal(true);};const handleViewUser=user=>{setSelectedUser(user);setShowViewModal(true);};const handleEditUser=user=>{setSelectedUser(user);setShowEditModal(true);};const handleDeleteUser=async userId=>{// 只有超级管理员可以删除用户\nif(userRole!=='super_admin'){showNotification('只有超级管理员可以删除用户','error');return;}if(!window.confirm('确定要删除这个用户吗？此操作不可撤销。')){return;}try{const response=await authService.authenticatedFetch(\"http://localhost:3001/api/auth/users/\".concat(userId),{method:'DELETE',headers:{'Content-Type':'application/json'}});const data=await response.json();if(data.success){showNotification('用户删除成功','success');loadUsers();// 重新加载用户列表\n}else{showNotification(data.message,'error');}}catch(error){console.error('删除用户失败:',error);showNotification('删除用户失败，请稍后重试','error');}};const handleToggleUserStatus=async(userId,currentStatus)=>{try{const response=await authService.authenticatedFetch(\"http://localhost:3001/api/auth/users/\".concat(userId),{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({isActive:!currentStatus})});const data=await response.json();if(data.success){showNotification(\"\\u7528\\u6237\\u5DF2\".concat(!currentStatus?'启用':'禁用'),'success');loadUsers();// 重新加载用户列表\n}else{showNotification(data.message,'error');}}catch(error){console.error('更新用户状态失败:',error);showNotification('更新用户状态失败，请稍后重试','error');}};const showNotification=(message,type)=>{const notification=document.createElement('div');notification.className=\"notification \".concat(type);notification.innerHTML=\"\\n      <span class=\\\"notification-icon\\\">\".concat(type==='success'?'✅':'❌',\"</span>\\n      <span class=\\\"notification-text\\\">\").concat(message,\"</span>\\n    \");document.body.appendChild(notification);setTimeout(()=>{if(notification.parentNode){notification.parentNode.removeChild(notification);}},3000);};// 过滤用户\nconst filteredUsers=users.filter(user=>{const matchesSearch=user.username.toLowerCase().includes(searchTerm.toLowerCase())||user.email.toLowerCase().includes(searchTerm.toLowerCase());const matchesRole=roleFilter==='all'||user.role===roleFilter;const matchesStatus=statusFilter==='all'||statusFilter==='active'&&user.isActive||statusFilter==='inactive'&&!user.isActive;return matchesSearch&&matchesRole&&matchesStatus;});// 权限检查\nif(userRole!=='super_admin'&&userRole!=='manager'){return/*#__PURE__*/_jsx(\"div\",{className:\"user-management\",children:/*#__PURE__*/_jsx(\"div\",{className:\"page-access-denied\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"access-denied-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"access-denied-icon\",children:\"\\uD83D\\uDEAB\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"access-denied-title\",children:\"\\u8BBF\\u95EE\\u88AB\\u62D2\\u7EDD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"access-denied-message\",children:\"\\u53EA\\u6709\\u8D85\\u7EA7\\u7BA1\\u7406\\u5458\\u548C\\u7BA1\\u7406\\u5458\\u53EF\\u4EE5\\u8BBF\\u95EE\\u7528\\u6237\\u7BA1\\u7406\\u9875\\u9762\"}),/*#__PURE__*/_jsx(\"button\",{className:\"back-button\",onClick:()=>onNavigate('home'),children:\"\\u8FD4\\u56DE\\u9996\\u9875\"})]})})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"user-management\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header-title\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"title-icon\",children:\"\\uD83D\\uDC65\"}),/*#__PURE__*/_jsx(\"h1\",{children:\"\\u7528\\u6237\\u7BA1\\u7406\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"user-count\",children:[\"\\u5171 \",users.length,\" \\u4E2A\\u7528\\u6237\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"action-button back\",onClick:()=>onNavigate('home'),children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFE0\"}),\"\\u8FD4\\u56DE\\u9996\\u9875\"]}),/*#__PURE__*/_jsxs(\"button\",{className:\"action-button create\",onClick:handleCreateUser,children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2795\"}),\"\\u521B\\u5EFA\\u7528\\u6237\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filters-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"search-box\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"search-icon\",children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",className:\"search-input\",placeholder:\"\\u641C\\u7D22\\u7528\\u6237\\u540D\\u6216\\u90AE\\u7BB1...\",value:searchTerm,onChange:e=>setSearchTerm(e.target.value)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-controls\",children:[/*#__PURE__*/_jsxs(\"select\",{className:\"filter-select\",value:roleFilter,onChange:e=>setRoleFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u6240\\u6709\\u89D2\\u8272\"}),Object.entries(ROLE_DISPLAY_NAMES).map(_ref2=>{let[role,name]=_ref2;return/*#__PURE__*/_jsx(\"option\",{value:role,children:name},role);})]}),/*#__PURE__*/_jsxs(\"select\",{className:\"filter-select\",value:statusFilter,onChange:e=>setStatusFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u6240\\u6709\\u72B6\\u6001\"}),/*#__PURE__*/_jsx(\"option\",{value:\"active\",children:\"\\u5DF2\\u542F\\u7528\"}),/*#__PURE__*/_jsx(\"option\",{value:\"inactive\",children:\"\\u5DF2\\u7981\\u7528\"})]})]})]})]}),loading?/*#__PURE__*/_jsxs(\"div\",{className:\"loading-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u52A0\\u8F7D\\u7528\\u6237\\u5217\\u8868\\u4E2D...\"})]}):error?/*#__PURE__*/_jsxs(\"div\",{className:\"error-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"error-icon\",children:\"\\u274C\"}),/*#__PURE__*/_jsx(\"div\",{className:\"error-message\",children:error}),/*#__PURE__*/_jsx(\"button\",{className:\"retry-button\",onClick:loadUsers,children:\"\\u91CD\\u8BD5\"})]}):/*#__PURE__*/_jsx(\"div\",{className:\"users-grid\",children:filteredUsers.length===0?/*#__PURE__*/_jsxs(\"div\",{className:\"empty-state\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"empty-icon\",children:\"\\uD83D\\uDC64\"}),/*#__PURE__*/_jsx(\"div\",{children:\"\\u6CA1\\u6709\\u627E\\u5230\\u5339\\u914D\\u7684\\u7528\\u6237\"})]}):filteredUsers.map(user=>/*#__PURE__*/_jsx(UserCard,{user:user,currentUserId:currentUser===null||currentUser===void 0?void 0:currentUser.id,currentUserRole:userRole,onView:handleViewUser,onEdit:handleEditUser,onDelete:handleDeleteUser,onToggleStatus:handleToggleUserStatus},user.id))}),showCreateModal&&/*#__PURE__*/_jsx(UserModal,{isEdit:false,user:null,currentUserRole:userRole,onClose:()=>setShowCreateModal(false),onSuccess:()=>{setShowCreateModal(false);loadUsers();}}),showEditModal&&selectedUser&&/*#__PURE__*/_jsx(UserModal,{isEdit:true,user:selectedUser,currentUserRole:userRole,onClose:()=>setShowEditModal(false),onSuccess:()=>{setShowEditModal(false);loadUsers();}}),showViewModal&&selectedUser&&/*#__PURE__*/_jsx(UserModal,{isEdit:false,isView:true,user:selectedUser,currentUserRole:userRole,onClose:()=>setShowViewModal(false),onSuccess:()=>{setShowViewModal(false);}})]});};// 用户卡片组件\nconst UserCard=_ref3=>{let{user,currentUserId,currentUserRole,onView,onEdit,onDelete,onToggleStatus}=_ref3;const isCurrentUser=user.id===currentUserId;const canDelete=currentUserRole==='super_admin'&&!isCurrentUser;return/*#__PURE__*/_jsxs(\"div\",{className:\"user-card \".concat(!user.isActive?'inactive':''),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"user-avatar-section\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"user-avatar\",children:user.username.charAt(0).toUpperCase()}),/*#__PURE__*/_jsx(\"div\",{className:\"status-indicator\",children:/*#__PURE__*/_jsx(\"div\",{className:\"status-dot \".concat(user.isActive?'active':'inactive')})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"user-info-center\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"username\",children:user.username}),/*#__PURE__*/_jsxs(\"p\",{className:\"user-email\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"email-icon\",children:\"\\uD83D\\uDCE7\"}),user.email]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"role-badge-container\",children:/*#__PURE__*/_jsx(\"span\",{className:\"role-badge role-\".concat(user.role),children:ROLE_DISPLAY_NAMES[user.role]})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"info-unified-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"info-item-left\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"info-icon\",children:\"\\uD83D\\uDCC5\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"info-label\",children:\"\\u521B\\u5EFA\\u65F6\\u95F4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value\",children:new Date(user.createdAt).toLocaleDateString('zh-CN')})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item-right\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"info-icon\",children:\"\\uD83D\\uDD50\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"info-label\",children:\"\\u6700\\u540E\\u767B\\u5F55\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value\",children:user.lastLogin?new Date(user.lastLogin).toLocaleDateString('zh-CN'):'从未登录'})]})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-actions\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"action-group primary\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn view\",onClick:()=>onView(user),title:\"\\u67E5\\u770B\\u8BE6\\u60C5\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDC41\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u67E5\\u770B\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn edit\",onClick:()=>onEdit(user),title:\"\\u7F16\\u8F91\\u7528\\u6237\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u270F\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7F16\\u8F91\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"action-group secondary\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn toggle\",onClick:()=>onToggleStatus(user.id,user.isActive),title:user.isActive?'禁用用户':'启用用户',children:[/*#__PURE__*/_jsx(\"span\",{children:user.isActive?'🔒':'🔓'}),/*#__PURE__*/_jsx(\"span\",{children:user.isActive?'禁用':'启用'})]})}),canDelete&&/*#__PURE__*/_jsx(\"div\",{className:\"action-group danger\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"action-btn delete\",onClick:()=>onDelete(user.id),title:\"\\u5220\\u9664\\u7528\\u6237\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDDD1\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u5220\\u9664\"})]})})]})]});};// 用户创建/编辑模态框组件\nconst UserModal=_ref4=>{let{isEdit,isView=false,user,currentUserRole,onClose,onSuccess}=_ref4;const[formData,setFormData]=useState({username:(user===null||user===void 0?void 0:user.username)||'',password:'',email:(user===null||user===void 0?void 0:user.email)||'',role:(user===null||user===void 0?void 0:user.role)||'user',isActive:(user===null||user===void 0?void 0:user.isActive)!==false});const[loading,setLoading]=useState(false);const[error,setError]=useState('');// 根据当前用户角色获取可选择的角色\nconst getAvailableRoles=()=>{if(currentUserRole==='super_admin'){// 超级管理员可以创建所有角色\nreturn Object.entries(ROLE_DISPLAY_NAMES);}else if(currentUserRole==='manager'){// 管理员只能创建部长以下级别的用户\nreturn Object.entries(ROLE_DISPLAY_NAMES).filter(_ref5=>{let[role]=_ref5;return role!=='super_admin'&&role!=='manager';});}return[];};const handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));if(error)setError('');};const handleSubmit=async e=>{e.preventDefault();// 查看模式下不允许提交\nif(isView){return;}// 表单验证\nif(!formData.username.trim()){setError('用户名不能为空');return;}if(!isEdit&&!formData.password){setError('密码不能为空');return;}if(!formData.email.trim()){setError('邮箱不能为空');return;}setLoading(true);setError('');try{const url=isEdit?\"http://localhost:3001/api/auth/users/\".concat(user.id):'http://localhost:3001/api/auth/users';const method=isEdit?'PUT':'POST';const body=_objectSpread({},formData);if(isEdit&&!formData.password){delete body.password;// 如果编辑时密码为空，不更新密码\n}const response=await authService.authenticatedFetch(url,{method,headers:{'Content-Type':'application/json'},body:JSON.stringify(body)});const data=await response.json();if(data.success){onSuccess();}else{setError(data.message);}}catch(error){console.error('保存用户失败:',error);setError('保存用户失败，请稍后重试');}finally{setLoading(false);}};return/*#__PURE__*/_jsx(\"div\",{className:\"modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:isView?'查看用户':isEdit?'编辑用户':'创建用户'}),/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:onClose,children:\"\\u2715\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error-message\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"error-icon\",children:\"\\u26A0\\uFE0F\"}),error]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,className:\"compact-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group-half\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u7528\\u6237\\u540D\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",name:\"username\",value:formData.username,onChange:handleInputChange,className:\"form-input\",placeholder:\"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",disabled:loading||isView,readOnly:isView,required:!isView})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group-half\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u90AE\\u7BB1\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,className:\"form-input\",placeholder:\"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",disabled:loading||isView,readOnly:isView,required:!isView})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-row\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-group-half\",children:[/*#__PURE__*/_jsx(\"label\",{children:isView?'密码':isEdit?'新密码（留空则不修改）':'密码'}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",name:\"password\",value:isView?'••••••••':formData.password,onChange:handleInputChange,className:\"form-input\",placeholder:isView?\"密码已隐藏\":isEdit?\"留空则不修改密码\":\"请输入密码\",disabled:loading||isView,readOnly:isView,required:!isEdit&&!isView})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group-half\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u89D2\\u8272\"}),/*#__PURE__*/_jsx(\"select\",{name:\"role\",value:formData.role,onChange:handleInputChange,className:\"form-select role-select\",disabled:loading||isView,required:!isView,children:getAvailableRoles().map(_ref6=>{let[role,name]=_ref6;return/*#__PURE__*/_jsxs(\"option\",{value:role,children:[name,\" - \",ROLE_DESCRIPTIONS[role]]},role);})})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-group\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",name:\"isActive\",checked:formData.isActive,onChange:handleInputChange,disabled:loading||isView}),\"\\u542F\\u7528\\u7528\\u6237\"]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-actions\",children:[/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"cancel-button\",onClick:onClose,disabled:loading,children:isView?'关闭':'取消'}),!isView&&/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"submit-button\",disabled:loading,children:loading?'保存中...':isEdit?'更新':'创建'})]})]})]})});};export default UserManagement;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "authService", "ROLES", "ROLE_DISPLAY_NAMES", "ROLE_DESCRIPTIONS", "jsx", "_jsx", "jsxs", "_jsxs", "UserManagement", "_ref", "onNavigate", "isAuthenticated", "currentUser", "userRole", "users", "setUsers", "loading", "setLoading", "error", "setError", "searchTerm", "setSearchTerm", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "statusFilter", "setStatus<PERSON>ilter", "showCreateModal", "setShowCreateModal", "showEditModal", "setShowEditModal", "showViewModal", "setShowViewModal", "selected<PERSON>ser", "setSelectedUser", "loadUsers", "response", "authenticatedFetch", "method", "headers", "data", "json", "success", "message", "console", "handleCreateUser", "handleViewUser", "user", "handleEditUser", "handleDeleteUser", "userId", "showNotification", "window", "confirm", "concat", "handleToggleUserStatus", "currentStatus", "body", "JSON", "stringify", "isActive", "type", "notification", "document", "createElement", "className", "innerHTML", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "filteredUsers", "filter", "matchesSearch", "username", "toLowerCase", "includes", "email", "matchesRole", "role", "matchesStatus", "children", "onClick", "length", "placeholder", "value", "onChange", "e", "target", "Object", "entries", "map", "_ref2", "name", "UserCard", "currentUserId", "id", "currentUserRole", "onView", "onEdit", "onDelete", "onToggleStatus", "UserModal", "isEdit", "onClose", "onSuccess", "<PERSON><PERSON><PERSON><PERSON>", "_ref3", "isCurrentUser", "canDelete", "char<PERSON>t", "toUpperCase", "Date", "createdAt", "toLocaleDateString", "lastLogin", "title", "_ref4", "formData", "setFormData", "password", "getAvailableRoles", "_ref5", "handleInputChange", "checked", "prev", "_objectSpread", "handleSubmit", "preventDefault", "trim", "url", "onSubmit", "disabled", "readOnly", "required", "_ref6"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/pages/UserManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/UserManagement.css';\r\nimport authService from '../services/authService';\r\nimport { ROLES, ROLE_DISPLAY_NAMES, ROLE_DESCRIPTIONS } from '../utils/rolePermissions';\r\n\r\nconst UserManagement = ({ onNavigate, isAuthenticated, currentUser, userRole }) => {\r\n  const [users, setUsers] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState('');\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [roleFilter, setRoleFilter] = useState('all');\r\n  const [statusFilter, setStatusFilter] = useState('all');\r\n  const [showCreateModal, setShowCreateModal] = useState(false);\r\n  const [showEditModal, setShowEditModal] = useState(false);\r\n  const [showViewModal, setShowViewModal] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (userRole === 'super_admin' || userRole === 'manager') {\r\n      loadUsers();\r\n    }\r\n  }, [userRole]);\r\n\r\n  const loadUsers = async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await authService.authenticatedFetch('http://localhost:3001/api/auth/users', {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setUsers(data.users);\r\n      } else {\r\n        setError(data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('加载用户列表失败:', error);\r\n      setError('加载用户列表失败，请检查网络连接');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleCreateUser = () => {\r\n    setSelectedUser(null);\r\n    setShowCreateModal(true);\r\n  };\r\n\r\n  const handleViewUser = (user) => {\r\n    setSelectedUser(user);\r\n    setShowViewModal(true);\r\n  };\r\n\r\n  const handleEditUser = (user) => {\r\n    setSelectedUser(user);\r\n    setShowEditModal(true);\r\n  };\r\n\r\n  const handleDeleteUser = async (userId) => {\r\n    // 只有超级管理员可以删除用户\r\n    if (userRole !== 'super_admin') {\r\n      showNotification('只有超级管理员可以删除用户', 'error');\r\n      return;\r\n    }\r\n\r\n    if (!window.confirm('确定要删除这个用户吗？此操作不可撤销。')) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${userId}`, {\r\n        method: 'DELETE',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        showNotification('用户删除成功', 'success');\r\n        loadUsers(); // 重新加载用户列表\r\n      } else {\r\n        showNotification(data.message, 'error');\r\n      }\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error);\r\n      showNotification('删除用户失败，请稍后重试', 'error');\r\n    }\r\n  };\r\n\r\n  const handleToggleUserStatus = async (userId, currentStatus) => {\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${userId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          isActive: !currentStatus\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        showNotification(`用户已${!currentStatus ? '启用' : '禁用'}`, 'success');\r\n        loadUsers(); // 重新加载用户列表\r\n      } else {\r\n        showNotification(data.message, 'error');\r\n      }\r\n    } catch (error) {\r\n      console.error('更新用户状态失败:', error);\r\n      showNotification('更新用户状态失败，请稍后重试', 'error');\r\n    }\r\n  };\r\n\r\n  const showNotification = (message, type) => {\r\n    const notification = document.createElement('div');\r\n    notification.className = `notification ${type}`;\r\n    notification.innerHTML = `\r\n      <span class=\"notification-icon\">${type === 'success' ? '✅' : '❌'}</span>\r\n      <span class=\"notification-text\">${message}</span>\r\n    `;\r\n    \r\n    document.body.appendChild(notification);\r\n    \r\n    setTimeout(() => {\r\n      if (notification.parentNode) {\r\n        notification.parentNode.removeChild(notification);\r\n      }\r\n    }, 3000);\r\n  };\r\n\r\n  // 过滤用户\r\n  const filteredUsers = users.filter(user => {\r\n    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||\r\n                         user.email.toLowerCase().includes(searchTerm.toLowerCase());\r\n    const matchesRole = roleFilter === 'all' || user.role === roleFilter;\r\n    const matchesStatus = statusFilter === 'all' || \r\n                         (statusFilter === 'active' && user.isActive) ||\r\n                         (statusFilter === 'inactive' && !user.isActive);\r\n    \r\n    return matchesSearch && matchesRole && matchesStatus;\r\n  });\r\n\r\n  // 权限检查\r\n  if (userRole !== 'super_admin' && userRole !== 'manager') {\r\n    return (\r\n      <div className=\"user-management\">\r\n        <div className=\"page-access-denied\">\r\n          <div className=\"access-denied-content\">\r\n            <div className=\"access-denied-icon\">🚫</div>\r\n            <h2 className=\"access-denied-title\">访问被拒绝</h2>\r\n            <p className=\"access-denied-message\">只有超级管理员和管理员可以访问用户管理页面</p>\r\n            <button\r\n              className=\"back-button\"\r\n              onClick={() => onNavigate('home')}\r\n            >\r\n              返回首页\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"user-management\">\r\n      {/* 页面头部 */}\r\n      <div className=\"page-header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"header-title\">\r\n            <span className=\"title-icon\">👥</span>\r\n            <h1>用户管理</h1>\r\n          </div>\r\n          <div className=\"user-count\">\r\n            共 {users.length} 个用户\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"header-actions\">\r\n          <button \r\n            className=\"action-button back\"\r\n            onClick={() => onNavigate('home')}\r\n          >\r\n            <span>🏠</span>\r\n            返回首页\r\n          </button>\r\n          <button \r\n            className=\"action-button create\"\r\n            onClick={handleCreateUser}\r\n          >\r\n            <span>➕</span>\r\n            创建用户\r\n          </button>\r\n        </div>\r\n\r\n        {/* 搜索和过滤 */}\r\n        <div className=\"filters-section\">\r\n          <div className=\"search-box\">\r\n            <span className=\"search-icon\">🔍</span>\r\n            <input\r\n              type=\"text\"\r\n              className=\"search-input\"\r\n              placeholder=\"搜索用户名或邮箱...\"\r\n              value={searchTerm}\r\n              onChange={(e) => setSearchTerm(e.target.value)}\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"filter-controls\">\r\n            <select\r\n              className=\"filter-select\"\r\n              value={roleFilter}\r\n              onChange={(e) => setRoleFilter(e.target.value)}\r\n            >\r\n              <option value=\"all\">所有角色</option>\r\n              {Object.entries(ROLE_DISPLAY_NAMES).map(([role, name]) => (\r\n                <option key={role} value={role}>{name}</option>\r\n              ))}\r\n            </select>\r\n            \r\n            <select\r\n              className=\"filter-select\"\r\n              value={statusFilter}\r\n              onChange={(e) => setStatusFilter(e.target.value)}\r\n            >\r\n              <option value=\"all\">所有状态</option>\r\n              <option value=\"active\">已启用</option>\r\n              <option value=\"inactive\">已禁用</option>\r\n            </select>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 用户列表 */}\r\n      {loading ? (\r\n        <div className=\"loading-container\">\r\n          <div className=\"loading-spinner\"></div>\r\n          <div>加载用户列表中...</div>\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"error-container\">\r\n          <div className=\"error-icon\">❌</div>\r\n          <div className=\"error-message\">{error}</div>\r\n          <button className=\"retry-button\" onClick={loadUsers}>\r\n            重试\r\n          </button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"users-grid\">\r\n          {filteredUsers.length === 0 ? (\r\n            <div className=\"empty-state\">\r\n              <div className=\"empty-icon\">👤</div>\r\n              <div>没有找到匹配的用户</div>\r\n            </div>\r\n          ) : (\r\n            filteredUsers.map(user => (\r\n              <UserCard\r\n                key={user.id}\r\n                user={user}\r\n                currentUserId={currentUser?.id}\r\n                currentUserRole={userRole}\r\n                onView={handleViewUser}\r\n                onEdit={handleEditUser}\r\n                onDelete={handleDeleteUser}\r\n                onToggleStatus={handleToggleUserStatus}\r\n              />\r\n            ))\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* 创建用户模态框 */}\r\n      {showCreateModal && (\r\n        <UserModal\r\n          isEdit={false}\r\n          user={null}\r\n          currentUserRole={userRole}\r\n          onClose={() => setShowCreateModal(false)}\r\n          onSuccess={() => {\r\n            setShowCreateModal(false);\r\n            loadUsers();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* 编辑用户模态框 */}\r\n      {showEditModal && selectedUser && (\r\n        <UserModal\r\n          isEdit={true}\r\n          user={selectedUser}\r\n          currentUserRole={userRole}\r\n          onClose={() => setShowEditModal(false)}\r\n          onSuccess={() => {\r\n            setShowEditModal(false);\r\n            loadUsers();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* 查看用户模态框 */}\r\n      {showViewModal && selectedUser && (\r\n        <UserModal\r\n          isEdit={false}\r\n          isView={true}\r\n          user={selectedUser}\r\n          currentUserRole={userRole}\r\n          onClose={() => setShowViewModal(false)}\r\n          onSuccess={() => {\r\n            setShowViewModal(false);\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n// 用户卡片组件\r\nconst UserCard = ({ user, currentUserId, currentUserRole, onView, onEdit, onDelete, onToggleStatus }) => {\r\n  const isCurrentUser = user.id === currentUserId;\r\n  const canDelete = currentUserRole === 'super_admin' && !isCurrentUser;\r\n\r\n  return (\r\n    <div className={`user-card ${!user.isActive ? 'inactive' : ''}`}>\r\n      <div className=\"card-header\">\r\n        <div className=\"user-avatar-section\">\r\n          <div className=\"user-avatar\">\r\n            {user.username.charAt(0).toUpperCase()}\r\n          </div>\r\n          <div className=\"status-indicator\">\r\n            <div className={`status-dot ${user.isActive ? 'active' : 'inactive'}`}></div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"user-info-center\">\r\n          <h3 className=\"username\">{user.username}</h3>\r\n          <p className=\"user-email\">\r\n            <span className=\"email-icon\">📧</span>\r\n            {user.email}\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"role-badge-container\">\r\n          <span className={`role-badge role-${user.role}`}>\r\n            {ROLE_DISPLAY_NAMES[user.role]}\r\n          </span>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card-content\">\r\n        <div className=\"info-unified-container\">\r\n          <div className=\"info-item-left\">\r\n            <span className=\"info-icon\">📅</span>\r\n            <div className=\"info-content\">\r\n              <div className=\"info-label\">创建时间</div>\r\n              <div className=\"info-value\">\r\n                {new Date(user.createdAt).toLocaleDateString('zh-CN')}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"info-item-right\">\r\n            <span className=\"info-icon\">🕐</span>\r\n            <div className=\"info-content\">\r\n              <div className=\"info-label\">最后登录</div>\r\n              <div className=\"info-value\">\r\n                {user.lastLogin ?\r\n                  new Date(user.lastLogin).toLocaleDateString('zh-CN') :\r\n                  '从未登录'\r\n                }\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"card-actions\">\r\n        <div className=\"action-group primary\">\r\n          <button\r\n            className=\"action-btn view\"\r\n            onClick={() => onView(user)}\r\n            title=\"查看详情\"\r\n          >\r\n            <span>👁️</span>\r\n            <span>查看</span>\r\n          </button>\r\n\r\n          <button\r\n            className=\"action-btn edit\"\r\n            onClick={() => onEdit(user)}\r\n            title=\"编辑用户\"\r\n          >\r\n            <span>✏️</span>\r\n            <span>编辑</span>\r\n          </button>\r\n        </div>\r\n\r\n        <div className=\"action-group secondary\">\r\n          <button\r\n            className=\"action-btn toggle\"\r\n            onClick={() => onToggleStatus(user.id, user.isActive)}\r\n            title={user.isActive ? '禁用用户' : '启用用户'}\r\n          >\r\n            <span>{user.isActive ? '🔒' : '🔓'}</span>\r\n            <span>{user.isActive ? '禁用' : '启用'}</span>\r\n          </button>\r\n        </div>\r\n\r\n        {canDelete && (\r\n          <div className=\"action-group danger\">\r\n            <button\r\n              className=\"action-btn delete\"\r\n              onClick={() => onDelete(user.id)}\r\n              title=\"删除用户\"\r\n            >\r\n              <span>🗑️</span>\r\n              <span>删除</span>\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// 用户创建/编辑模态框组件\r\nconst UserModal = ({ isEdit, isView = false, user, currentUserRole, onClose, onSuccess }) => {\r\n  const [formData, setFormData] = useState({\r\n    username: user?.username || '',\r\n    password: '',\r\n    email: user?.email || '',\r\n    role: user?.role || 'user',\r\n    isActive: user?.isActive !== false\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n\r\n  // 根据当前用户角色获取可选择的角色\r\n  const getAvailableRoles = () => {\r\n    if (currentUserRole === 'super_admin') {\r\n      // 超级管理员可以创建所有角色\r\n      return Object.entries(ROLE_DISPLAY_NAMES);\r\n    } else if (currentUserRole === 'manager') {\r\n      // 管理员只能创建部长以下级别的用户\r\n      return Object.entries(ROLE_DISPLAY_NAMES).filter(([role]) =>\r\n        role !== 'super_admin' && role !== 'manager'\r\n      );\r\n    }\r\n    return [];\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n\r\n    if (error) setError('');\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // 查看模式下不允许提交\r\n    if (isView) {\r\n      return;\r\n    }\r\n\r\n    // 表单验证\r\n    if (!formData.username.trim()) {\r\n      setError('用户名不能为空');\r\n      return;\r\n    }\r\n\r\n    if (!isEdit && !formData.password) {\r\n      setError('密码不能为空');\r\n      return;\r\n    }\r\n\r\n    if (!formData.email.trim()) {\r\n      setError('邮箱不能为空');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError('');\r\n\r\n    try {\r\n      const url = isEdit\r\n        ? `http://localhost:3001/api/auth/users/${user.id}`\r\n        : 'http://localhost:3001/api/auth/users';\r\n\r\n      const method = isEdit ? 'PUT' : 'POST';\r\n\r\n      const body = { ...formData };\r\n      if (isEdit && !formData.password) {\r\n        delete body.password; // 如果编辑时密码为空，不更新密码\r\n      }\r\n\r\n      const response = await authService.authenticatedFetch(url, {\r\n        method,\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(body),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        onSuccess();\r\n      } else {\r\n        setError(data.message);\r\n      }\r\n    } catch (error) {\r\n      console.error('保存用户失败:', error);\r\n      setError('保存用户失败，请稍后重试');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"modal-overlay\">\r\n      <div className=\"modal-content\">\r\n        <div className=\"modal-header\">\r\n          <h2>{isView ? '查看用户' : (isEdit ? '编辑用户' : '创建用户')}</h2>\r\n          <button className=\"close-button\" onClick={onClose}>✕</button>\r\n        </div>\r\n\r\n        {error && (\r\n          <div className=\"error-message\">\r\n            <span className=\"error-icon\">⚠️</span>\r\n            {error}\r\n          </div>\r\n        )}\r\n\r\n        <form onSubmit={handleSubmit} className=\"compact-form\">\r\n          <div className=\"form-row\">\r\n            <div className=\"form-group-half\">\r\n              <label>用户名</label>\r\n              <input\r\n                type=\"text\"\r\n                name=\"username\"\r\n                value={formData.username}\r\n                onChange={handleInputChange}\r\n                className=\"form-input\"\r\n                placeholder=\"请输入用户名\"\r\n                disabled={loading || isView}\r\n                readOnly={isView}\r\n                required={!isView}\r\n              />\r\n            </div>\r\n            <div className=\"form-group-half\">\r\n              <label>邮箱</label>\r\n              <input\r\n                type=\"email\"\r\n                name=\"email\"\r\n                value={formData.email}\r\n                onChange={handleInputChange}\r\n                className=\"form-input\"\r\n                placeholder=\"请输入邮箱地址\"\r\n                disabled={loading || isView}\r\n                readOnly={isView}\r\n                required={!isView}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"form-row\">\r\n            <div className=\"form-group-half\">\r\n              <label>{isView ? '密码' : (isEdit ? '新密码（留空则不修改）' : '密码')}</label>\r\n              <input\r\n                type=\"password\"\r\n                name=\"password\"\r\n                value={isView ? '••••••••' : formData.password}\r\n                onChange={handleInputChange}\r\n                className=\"form-input\"\r\n                placeholder={isView ? \"密码已隐藏\" : (isEdit ? \"留空则不修改密码\" : \"请输入密码\")}\r\n                disabled={loading || isView}\r\n                readOnly={isView}\r\n                required={!isEdit && !isView}\r\n              />\r\n            </div>\r\n            <div className=\"form-group-half\">\r\n              <label>角色</label>\r\n              <select\r\n                name=\"role\"\r\n                value={formData.role}\r\n                onChange={handleInputChange}\r\n                className=\"form-select role-select\"\r\n                disabled={loading || isView}\r\n                required={!isView}\r\n              >\r\n                {getAvailableRoles().map(([role, name]) => (\r\n                  <option key={role} value={role}>\r\n                    {name} - {ROLE_DESCRIPTIONS[role]}\r\n                  </option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"form-group\">\r\n            <label className=\"checkbox-label\">\r\n              <input\r\n                type=\"checkbox\"\r\n                name=\"isActive\"\r\n                checked={formData.isActive}\r\n                onChange={handleInputChange}\r\n                disabled={loading || isView}\r\n              />\r\n              启用用户\r\n            </label>\r\n          </div>\r\n\r\n          <div className=\"modal-actions\">\r\n            <button\r\n              type=\"button\"\r\n              className=\"cancel-button\"\r\n              onClick={onClose}\r\n              disabled={loading}\r\n            >\r\n              {isView ? '关闭' : '取消'}\r\n            </button>\r\n            {!isView && (\r\n              <button\r\n                type=\"submit\"\r\n                className=\"submit-button\"\r\n                disabled={loading}\r\n              >\r\n                {loading ? '保存中...' : (isEdit ? '更新' : '创建')}\r\n              </button>\r\n            )}\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserManagement;\r\n"], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,8BAA8B,CACrC,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,OAASC,KAAK,CAAEC,kBAAkB,CAAEC,iBAAiB,KAAQ,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExF,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAA4D,IAA3D,CAAEC,UAAU,CAAEC,eAAe,CAAEC,WAAW,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CAC5E,KAAM,CAACK,KAAK,CAAEC,QAAQ,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC0B,YAAY,CAAEC,eAAe,CAAC,CAAG3B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC4B,eAAe,CAAEC,kBAAkB,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC8B,aAAa,CAAEC,gBAAgB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACgC,aAAa,CAAEC,gBAAgB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACkC,YAAY,CAAEC,eAAe,CAAC,CAAGnC,QAAQ,CAAC,IAAI,CAAC,CAEtDC,SAAS,CAAC,IAAM,CACd,GAAIc,QAAQ,GAAK,aAAa,EAAIA,QAAQ,GAAK,SAAS,CAAE,CACxDqB,SAAS,CAAC,CAAC,CACb,CACF,CAAC,CAAE,CAACrB,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAqB,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACFjB,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAkB,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACoC,kBAAkB,CAAC,sCAAsC,CAAE,CAC5FC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChB1B,QAAQ,CAACwB,IAAI,CAACzB,KAAK,CAAC,CACtB,CAAC,IAAM,CACLK,QAAQ,CAACoB,IAAI,CAACG,OAAO,CAAC,CACxB,CACF,CAAE,MAAOxB,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCC,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA2B,gBAAgB,CAAGA,CAAA,GAAM,CAC7BX,eAAe,CAAC,IAAI,CAAC,CACrBN,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAED,KAAM,CAAAkB,cAAc,CAAIC,IAAI,EAAK,CAC/Bb,eAAe,CAACa,IAAI,CAAC,CACrBf,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAgB,cAAc,CAAID,IAAI,EAAK,CAC/Bb,eAAe,CAACa,IAAI,CAAC,CACrBjB,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC,CAED,KAAM,CAAAmB,gBAAgB,CAAG,KAAO,CAAAC,MAAM,EAAK,CACzC;AACA,GAAIpC,QAAQ,GAAK,aAAa,CAAE,CAC9BqC,gBAAgB,CAAC,eAAe,CAAE,OAAO,CAAC,CAC1C,OACF,CAEA,GAAI,CAACC,MAAM,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAAE,CAC1C,OACF,CAEA,GAAI,CACF,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACoC,kBAAkB,yCAAAiB,MAAA,CAAyCJ,MAAM,EAAI,CACtGZ,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChBS,gBAAgB,CAAC,QAAQ,CAAE,SAAS,CAAC,CACrChB,SAAS,CAAC,CAAC,CAAE;AACf,CAAC,IAAM,CACLgB,gBAAgB,CAACX,IAAI,CAACG,OAAO,CAAE,OAAO,CAAC,CACzC,CACF,CAAE,MAAOxB,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BgC,gBAAgB,CAAC,cAAc,CAAE,OAAO,CAAC,CAC3C,CACF,CAAC,CAED,KAAM,CAAAI,sBAAsB,CAAG,KAAAA,CAAOL,MAAM,CAAEM,aAAa,GAAK,CAC9D,GAAI,CACF,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACoC,kBAAkB,yCAAAiB,MAAA,CAAyCJ,MAAM,EAAI,CACtGZ,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDkB,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,QAAQ,CAAE,CAACJ,aACb,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAhB,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChBS,gBAAgB,sBAAAG,MAAA,CAAO,CAACE,aAAa,CAAG,IAAI,CAAG,IAAI,EAAI,SAAS,CAAC,CACjErB,SAAS,CAAC,CAAC,CAAE;AACf,CAAC,IAAM,CACLgB,gBAAgB,CAACX,IAAI,CAACG,OAAO,CAAE,OAAO,CAAC,CACzC,CACF,CAAE,MAAOxB,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCgC,gBAAgB,CAAC,gBAAgB,CAAE,OAAO,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAA,gBAAgB,CAAGA,CAACR,OAAO,CAAEkB,IAAI,GAAK,CAC1C,KAAM,CAAAC,YAAY,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAClDF,YAAY,CAACG,SAAS,iBAAAX,MAAA,CAAmBO,IAAI,CAAE,CAC/CC,YAAY,CAACI,SAAS,8CAAAZ,MAAA,CACcO,IAAI,GAAK,SAAS,CAAG,GAAG,CAAG,GAAG,sDAAAP,MAAA,CAC9BX,OAAO,iBAC1C,CAEDoB,QAAQ,CAACN,IAAI,CAACU,WAAW,CAACL,YAAY,CAAC,CAEvCM,UAAU,CAAC,IAAM,CACf,GAAIN,YAAY,CAACO,UAAU,CAAE,CAC3BP,YAAY,CAACO,UAAU,CAACC,WAAW,CAACR,YAAY,CAAC,CACnD,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA,KAAM,CAAAS,aAAa,CAAGxD,KAAK,CAACyD,MAAM,CAACzB,IAAI,EAAI,CACzC,KAAM,CAAA0B,aAAa,CAAG1B,IAAI,CAAC2B,QAAQ,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvD,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,EAC/D5B,IAAI,CAAC8B,KAAK,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACvD,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC,CAChF,KAAM,CAAAG,WAAW,CAAGvD,UAAU,GAAK,KAAK,EAAIwB,IAAI,CAACgC,IAAI,GAAKxD,UAAU,CACpE,KAAM,CAAAyD,aAAa,CAAGvD,YAAY,GAAK,KAAK,EACtBA,YAAY,GAAK,QAAQ,EAAIsB,IAAI,CAACa,QAAS,EAC3CnC,YAAY,GAAK,UAAU,EAAI,CAACsB,IAAI,CAACa,QAAS,CAEpE,MAAO,CAAAa,aAAa,EAAIK,WAAW,EAAIE,aAAa,CACtD,CAAC,CAAC,CAEF;AACA,GAAIlE,QAAQ,GAAK,aAAa,EAAIA,QAAQ,GAAK,SAAS,CAAE,CACxD,mBACER,IAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,cAC9B3E,IAAA,QAAK2D,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,cACjCzE,KAAA,QAAKyD,SAAS,CAAC,uBAAuB,CAAAgB,QAAA,eACpC3E,IAAA,QAAK2D,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,CAAC,cAAE,CAAK,CAAC,cAC5C3E,IAAA,OAAI2D,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,CAAC,gCAAK,CAAI,CAAC,cAC9C3E,IAAA,MAAG2D,SAAS,CAAC,uBAAuB,CAAAgB,QAAA,CAAC,gIAAqB,CAAG,CAAC,cAC9D3E,IAAA,WACE2D,SAAS,CAAC,aAAa,CACvBiB,OAAO,CAAEA,CAAA,GAAMvE,UAAU,CAAC,MAAM,CAAE,CAAAsE,QAAA,CACnC,0BAED,CAAQ,CAAC,EACN,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACEzE,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAE9BzE,KAAA,QAAKyD,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BzE,KAAA,QAAKyD,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,eAC7BzE,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3B3E,IAAA,SAAM2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,cACtC3E,IAAA,OAAA2E,QAAA,CAAI,0BAAI,CAAI,CAAC,EACV,CAAC,cACNzE,KAAA,QAAKyD,SAAS,CAAC,YAAY,CAAAgB,QAAA,EAAC,SACxB,CAAClE,KAAK,CAACoE,MAAM,CAAC,qBAClB,EAAK,CAAC,EACH,CAAC,cAEN3E,KAAA,QAAKyD,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,eAC7BzE,KAAA,WACEyD,SAAS,CAAC,oBAAoB,CAC9BiB,OAAO,CAAEA,CAAA,GAAMvE,UAAU,CAAC,MAAM,CAAE,CAAAsE,QAAA,eAElC3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,2BAEjB,EAAQ,CAAC,cACTzE,KAAA,WACEyD,SAAS,CAAC,sBAAsB,CAChCiB,OAAO,CAAErC,gBAAiB,CAAAoC,QAAA,eAE1B3E,IAAA,SAAA2E,QAAA,CAAM,QAAC,CAAM,CAAC,2BAEhB,EAAQ,CAAC,EACN,CAAC,cAGNzE,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9BzE,KAAA,QAAKyD,SAAS,CAAC,YAAY,CAAAgB,QAAA,eACzB3E,IAAA,SAAM2D,SAAS,CAAC,aAAa,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,cACvC3E,IAAA,UACEuD,IAAI,CAAC,MAAM,CACXI,SAAS,CAAC,cAAc,CACxBmB,WAAW,CAAC,qDAAa,CACzBC,KAAK,CAAEhE,UAAW,CAClBiE,QAAQ,CAAGC,CAAC,EAAKjE,aAAa,CAACiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAChD,CAAC,EACC,CAAC,cAEN7E,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9BzE,KAAA,WACEyD,SAAS,CAAC,eAAe,CACzBoB,KAAK,CAAE9D,UAAW,CAClB+D,QAAQ,CAAGC,CAAC,EAAK/D,aAAa,CAAC+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAE/C3E,IAAA,WAAQ+E,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,0BAAI,CAAQ,CAAC,CAChCQ,MAAM,CAACC,OAAO,CAACvF,kBAAkB,CAAC,CAACwF,GAAG,CAACC,KAAA,MAAC,CAACb,IAAI,CAAEc,IAAI,CAAC,CAAAD,KAAA,oBACnDtF,IAAA,WAAmB+E,KAAK,CAAEN,IAAK,CAAAE,QAAA,CAAEY,IAAI,EAAxBd,IAAiC,CAAC,EAChD,CAAC,EACI,CAAC,cAETvE,KAAA,WACEyD,SAAS,CAAC,eAAe,CACzBoB,KAAK,CAAE5D,YAAa,CACpB6D,QAAQ,CAAGC,CAAC,EAAK7D,eAAe,CAAC6D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAAJ,QAAA,eAEjD3E,IAAA,WAAQ+E,KAAK,CAAC,KAAK,CAAAJ,QAAA,CAAC,0BAAI,CAAQ,CAAC,cACjC3E,IAAA,WAAQ+E,KAAK,CAAC,QAAQ,CAAAJ,QAAA,CAAC,oBAAG,CAAQ,CAAC,cACnC3E,IAAA,WAAQ+E,KAAK,CAAC,UAAU,CAAAJ,QAAA,CAAC,oBAAG,CAAQ,CAAC,EAC/B,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAGLhE,OAAO,cACNT,KAAA,QAAKyD,SAAS,CAAC,mBAAmB,CAAAgB,QAAA,eAChC3E,IAAA,QAAK2D,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC3D,IAAA,QAAA2E,QAAA,CAAK,+CAAU,CAAK,CAAC,EAClB,CAAC,CACJ9D,KAAK,cACPX,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,QAAC,CAAK,CAAC,cACnC3E,IAAA,QAAK2D,SAAS,CAAC,eAAe,CAAAgB,QAAA,CAAE9D,KAAK,CAAM,CAAC,cAC5Cb,IAAA,WAAQ2D,SAAS,CAAC,cAAc,CAACiB,OAAO,CAAE/C,SAAU,CAAA8C,QAAA,CAAC,cAErD,CAAQ,CAAC,EACN,CAAC,cAEN3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CACxBV,aAAa,CAACY,MAAM,GAAK,CAAC,cACzB3E,KAAA,QAAKyD,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1B3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,cAAE,CAAK,CAAC,cACpC3E,IAAA,QAAA2E,QAAA,CAAK,wDAAS,CAAK,CAAC,EACjB,CAAC,CAENV,aAAa,CAACoB,GAAG,CAAC5C,IAAI,eACpBzC,IAAA,CAACwF,QAAQ,EAEP/C,IAAI,CAAEA,IAAK,CACXgD,aAAa,CAAElF,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmF,EAAG,CAC/BC,eAAe,CAAEnF,QAAS,CAC1BoF,MAAM,CAAEpD,cAAe,CACvBqD,MAAM,CAAEnD,cAAe,CACvBoD,QAAQ,CAAEnD,gBAAiB,CAC3BoD,cAAc,CAAE9C,sBAAuB,EAPlCR,IAAI,CAACiD,EAQX,CACF,CACF,CACE,CACN,CAGArE,eAAe,eACdrB,IAAA,CAACgG,SAAS,EACRC,MAAM,CAAE,KAAM,CACdxD,IAAI,CAAE,IAAK,CACXkD,eAAe,CAAEnF,QAAS,CAC1B0F,OAAO,CAAEA,CAAA,GAAM5E,kBAAkB,CAAC,KAAK,CAAE,CACzC6E,SAAS,CAAEA,CAAA,GAAM,CACf7E,kBAAkB,CAAC,KAAK,CAAC,CACzBO,SAAS,CAAC,CAAC,CACb,CAAE,CACH,CACF,CAGAN,aAAa,EAAII,YAAY,eAC5B3B,IAAA,CAACgG,SAAS,EACRC,MAAM,CAAE,IAAK,CACbxD,IAAI,CAAEd,YAAa,CACnBgE,eAAe,CAAEnF,QAAS,CAC1B0F,OAAO,CAAEA,CAAA,GAAM1E,gBAAgB,CAAC,KAAK,CAAE,CACvC2E,SAAS,CAAEA,CAAA,GAAM,CACf3E,gBAAgB,CAAC,KAAK,CAAC,CACvBK,SAAS,CAAC,CAAC,CACb,CAAE,CACH,CACF,CAGAJ,aAAa,EAAIE,YAAY,eAC5B3B,IAAA,CAACgG,SAAS,EACRC,MAAM,CAAE,KAAM,CACdG,MAAM,CAAE,IAAK,CACb3D,IAAI,CAAEd,YAAa,CACnBgE,eAAe,CAAEnF,QAAS,CAC1B0F,OAAO,CAAEA,CAAA,GAAMxE,gBAAgB,CAAC,KAAK,CAAE,CACvCyE,SAAS,CAAEA,CAAA,GAAM,CACfzE,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAE,CACH,CACF,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA8D,QAAQ,CAAGa,KAAA,EAAwF,IAAvF,CAAE5D,IAAI,CAAEgD,aAAa,CAAEE,eAAe,CAAEC,MAAM,CAAEC,MAAM,CAAEC,QAAQ,CAAEC,cAAe,CAAC,CAAAM,KAAA,CAClG,KAAM,CAAAC,aAAa,CAAG7D,IAAI,CAACiD,EAAE,GAAKD,aAAa,CAC/C,KAAM,CAAAc,SAAS,CAAGZ,eAAe,GAAK,aAAa,EAAI,CAACW,aAAa,CAErE,mBACEpG,KAAA,QAAKyD,SAAS,cAAAX,MAAA,CAAe,CAACP,IAAI,CAACa,QAAQ,CAAG,UAAU,CAAG,EAAE,CAAG,CAAAqB,QAAA,eAC9DzE,KAAA,QAAKyD,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BzE,KAAA,QAAKyD,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,eAClC3E,IAAA,QAAK2D,SAAS,CAAC,aAAa,CAAAgB,QAAA,CACzBlC,IAAI,CAAC2B,QAAQ,CAACoC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnC,CAAC,cACNzG,IAAA,QAAK2D,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,cAC/B3E,IAAA,QAAK2D,SAAS,eAAAX,MAAA,CAAgBP,IAAI,CAACa,QAAQ,CAAG,QAAQ,CAAG,UAAU,CAAG,CAAM,CAAC,CAC1E,CAAC,EACH,CAAC,cAENpD,KAAA,QAAKyD,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,eAC/B3E,IAAA,OAAI2D,SAAS,CAAC,UAAU,CAAAgB,QAAA,CAAElC,IAAI,CAAC2B,QAAQ,CAAK,CAAC,cAC7ClE,KAAA,MAAGyD,SAAS,CAAC,YAAY,CAAAgB,QAAA,eACvB3E,IAAA,SAAM2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,CACrClC,IAAI,CAAC8B,KAAK,EACV,CAAC,EACD,CAAC,cAENvE,IAAA,QAAK2D,SAAS,CAAC,sBAAsB,CAAAgB,QAAA,cACnC3E,IAAA,SAAM2D,SAAS,oBAAAX,MAAA,CAAqBP,IAAI,CAACgC,IAAI,CAAG,CAAAE,QAAA,CAC7C9E,kBAAkB,CAAC4C,IAAI,CAACgC,IAAI,CAAC,CAC1B,CAAC,CACJ,CAAC,EACH,CAAC,cAENzE,IAAA,QAAK2D,SAAS,CAAC,cAAc,CAAAgB,QAAA,cAC3BzE,KAAA,QAAKyD,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,eACrCzE,KAAA,QAAKyD,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,eAC7B3E,IAAA,SAAM2D,SAAS,CAAC,WAAW,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,cACrCzE,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3B3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,0BAAI,CAAK,CAAC,cACtC3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CACxB,GAAI,CAAA+B,IAAI,CAACjE,IAAI,CAACkE,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,CAClD,CAAC,EACH,CAAC,EACH,CAAC,cAEN1G,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,SAAM2D,SAAS,CAAC,WAAW,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,cACrCzE,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3B3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,0BAAI,CAAK,CAAC,cACtC3E,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CACxBlC,IAAI,CAACoE,SAAS,CACb,GAAI,CAAAH,IAAI,CAACjE,IAAI,CAACoE,SAAS,CAAC,CAACD,kBAAkB,CAAC,OAAO,CAAC,CACpD,MAAM,CAEL,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,cAEN1G,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3BzE,KAAA,QAAKyD,SAAS,CAAC,sBAAsB,CAAAgB,QAAA,eACnCzE,KAAA,WACEyD,SAAS,CAAC,iBAAiB,CAC3BiB,OAAO,CAAEA,CAAA,GAAMgB,MAAM,CAACnD,IAAI,CAAE,CAC5BqE,KAAK,CAAC,0BAAM,CAAAnC,QAAA,eAEZ3E,IAAA,SAAA2E,QAAA,CAAM,oBAAG,CAAM,CAAC,cAChB3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,EACT,CAAC,cAETzE,KAAA,WACEyD,SAAS,CAAC,iBAAiB,CAC3BiB,OAAO,CAAEA,CAAA,GAAMiB,MAAM,CAACpD,IAAI,CAAE,CAC5BqE,KAAK,CAAC,0BAAM,CAAAnC,QAAA,eAEZ3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,cACf3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,EACT,CAAC,EACN,CAAC,cAEN3E,IAAA,QAAK2D,SAAS,CAAC,wBAAwB,CAAAgB,QAAA,cACrCzE,KAAA,WACEyD,SAAS,CAAC,mBAAmB,CAC7BiB,OAAO,CAAEA,CAAA,GAAMmB,cAAc,CAACtD,IAAI,CAACiD,EAAE,CAAEjD,IAAI,CAACa,QAAQ,CAAE,CACtDwD,KAAK,CAAErE,IAAI,CAACa,QAAQ,CAAG,MAAM,CAAG,MAAO,CAAAqB,QAAA,eAEvC3E,IAAA,SAAA2E,QAAA,CAAOlC,IAAI,CAACa,QAAQ,CAAG,IAAI,CAAG,IAAI,CAAO,CAAC,cAC1CtD,IAAA,SAAA2E,QAAA,CAAOlC,IAAI,CAACa,QAAQ,CAAG,IAAI,CAAG,IAAI,CAAO,CAAC,EACpC,CAAC,CACN,CAAC,CAELiD,SAAS,eACRvG,IAAA,QAAK2D,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,cAClCzE,KAAA,WACEyD,SAAS,CAAC,mBAAmB,CAC7BiB,OAAO,CAAEA,CAAA,GAAMkB,QAAQ,CAACrD,IAAI,CAACiD,EAAE,CAAE,CACjCoB,KAAK,CAAC,0BAAM,CAAAnC,QAAA,eAEZ3E,IAAA,SAAA2E,QAAA,CAAM,oBAAG,CAAM,CAAC,cAChB3E,IAAA,SAAA2E,QAAA,CAAM,cAAE,CAAM,CAAC,EACT,CAAC,CACN,CACN,EACE,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAqB,SAAS,CAAGe,KAAA,EAA2E,IAA1E,CAAEd,MAAM,CAAEG,MAAM,CAAG,KAAK,CAAE3D,IAAI,CAAEkD,eAAe,CAAEO,OAAO,CAAEC,SAAU,CAAC,CAAAY,KAAA,CACtF,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGxH,QAAQ,CAAC,CACvC2E,QAAQ,CAAE,CAAA3B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2B,QAAQ,GAAI,EAAE,CAC9B8C,QAAQ,CAAE,EAAE,CACZ3C,KAAK,CAAE,CAAA9B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE8B,KAAK,GAAI,EAAE,CACxBE,IAAI,CAAE,CAAAhC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEgC,IAAI,GAAI,MAAM,CAC1BnB,QAAQ,CAAE,CAAAb,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEa,QAAQ,IAAK,KAC/B,CAAC,CAAC,CACF,KAAM,CAAC3C,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACoB,KAAK,CAAEC,QAAQ,CAAC,CAAGrB,QAAQ,CAAC,EAAE,CAAC,CAEtC;AACA,KAAM,CAAA0H,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAIxB,eAAe,GAAK,aAAa,CAAE,CACrC;AACA,MAAO,CAAAR,MAAM,CAACC,OAAO,CAACvF,kBAAkB,CAAC,CAC3C,CAAC,IAAM,IAAI8F,eAAe,GAAK,SAAS,CAAE,CACxC;AACA,MAAO,CAAAR,MAAM,CAACC,OAAO,CAACvF,kBAAkB,CAAC,CAACqE,MAAM,CAACkD,KAAA,MAAC,CAAC3C,IAAI,CAAC,CAAA2C,KAAA,OACtD,CAAA3C,IAAI,GAAK,aAAa,EAAIA,IAAI,GAAK,SAAS,EAC9C,CAAC,CACH,CACA,MAAO,EAAE,CACX,CAAC,CAED,KAAM,CAAA4C,iBAAiB,CAAIpC,CAAC,EAAK,CAC/B,KAAM,CAAEM,IAAI,CAAER,KAAK,CAAExB,IAAI,CAAE+D,OAAQ,CAAC,CAAGrC,CAAC,CAACC,MAAM,CAC/C+B,WAAW,CAACM,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAAChC,IAAI,EAAGhC,IAAI,GAAK,UAAU,CAAG+D,OAAO,CAAGvC,KAAK,EAC7C,CAAC,CAEH,GAAIlE,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,CACzB,CAAC,CAED,KAAM,CAAA2G,YAAY,CAAG,KAAO,CAAAxC,CAAC,EAAK,CAChCA,CAAC,CAACyC,cAAc,CAAC,CAAC,CAElB;AACA,GAAItB,MAAM,CAAE,CACV,OACF,CAEA;AACA,GAAI,CAACY,QAAQ,CAAC5C,QAAQ,CAACuD,IAAI,CAAC,CAAC,CAAE,CAC7B7G,QAAQ,CAAC,SAAS,CAAC,CACnB,OACF,CAEA,GAAI,CAACmF,MAAM,EAAI,CAACe,QAAQ,CAACE,QAAQ,CAAE,CACjCpG,QAAQ,CAAC,QAAQ,CAAC,CAClB,OACF,CAEA,GAAI,CAACkG,QAAQ,CAACzC,KAAK,CAACoD,IAAI,CAAC,CAAC,CAAE,CAC1B7G,QAAQ,CAAC,QAAQ,CAAC,CAClB,OACF,CAEAF,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAA8G,GAAG,CAAG3B,MAAM,yCAAAjD,MAAA,CAC0BP,IAAI,CAACiD,EAAE,EAC/C,sCAAsC,CAE1C,KAAM,CAAA1D,MAAM,CAAGiE,MAAM,CAAG,KAAK,CAAG,MAAM,CAEtC,KAAM,CAAA9C,IAAI,CAAAqE,aAAA,IAAQR,QAAQ,CAAE,CAC5B,GAAIf,MAAM,EAAI,CAACe,QAAQ,CAACE,QAAQ,CAAE,CAChC,MAAO,CAAA/D,IAAI,CAAC+D,QAAQ,CAAE;AACxB,CAEA,KAAM,CAAApF,QAAQ,CAAG,KAAM,CAAAnC,WAAW,CAACoC,kBAAkB,CAAC6F,GAAG,CAAE,CACzD5F,MAAM,CACNC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDkB,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACF,IAAI,CAC3B,CAAC,CAAC,CAEF,KAAM,CAAAjB,IAAI,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,CAAE,CAChB+D,SAAS,CAAC,CAAC,CACb,CAAC,IAAM,CACLrF,QAAQ,CAACoB,IAAI,CAACG,OAAO,CAAC,CACxB,CACF,CAAE,MAAOxB,KAAK,CAAE,CACdyB,OAAO,CAACzB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BC,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,mBACEZ,IAAA,QAAK2D,SAAS,CAAC,eAAe,CAAAgB,QAAA,cAC5BzE,KAAA,QAAKyD,SAAS,CAAC,eAAe,CAAAgB,QAAA,eAC5BzE,KAAA,QAAKyD,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3B3E,IAAA,OAAA2E,QAAA,CAAKyB,MAAM,CAAG,MAAM,CAAIH,MAAM,CAAG,MAAM,CAAG,MAAO,CAAK,CAAC,cACvDjG,IAAA,WAAQ2D,SAAS,CAAC,cAAc,CAACiB,OAAO,CAAEsB,OAAQ,CAAAvB,QAAA,CAAC,QAAC,CAAQ,CAAC,EAC1D,CAAC,CAEL9D,KAAK,eACJX,KAAA,QAAKyD,SAAS,CAAC,eAAe,CAAAgB,QAAA,eAC5B3E,IAAA,SAAM2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,CACrC9D,KAAK,EACH,CACN,cAEDX,KAAA,SAAM2H,QAAQ,CAAEJ,YAAa,CAAC9D,SAAS,CAAC,cAAc,CAAAgB,QAAA,eACpDzE,KAAA,QAAKyD,SAAS,CAAC,UAAU,CAAAgB,QAAA,eACvBzE,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,UAAA2E,QAAA,CAAO,oBAAG,CAAO,CAAC,cAClB3E,IAAA,UACEuD,IAAI,CAAC,MAAM,CACXgC,IAAI,CAAC,UAAU,CACfR,KAAK,CAAEiC,QAAQ,CAAC5C,QAAS,CACzBY,QAAQ,CAAEqC,iBAAkB,CAC5B1D,SAAS,CAAC,YAAY,CACtBmB,WAAW,CAAC,sCAAQ,CACpBgD,QAAQ,CAAEnH,OAAO,EAAIyF,MAAO,CAC5B2B,QAAQ,CAAE3B,MAAO,CACjB4B,QAAQ,CAAE,CAAC5B,MAAO,CACnB,CAAC,EACC,CAAC,cACNlG,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,UAAA2E,QAAA,CAAO,cAAE,CAAO,CAAC,cACjB3E,IAAA,UACEuD,IAAI,CAAC,OAAO,CACZgC,IAAI,CAAC,OAAO,CACZR,KAAK,CAAEiC,QAAQ,CAACzC,KAAM,CACtBS,QAAQ,CAAEqC,iBAAkB,CAC5B1D,SAAS,CAAC,YAAY,CACtBmB,WAAW,CAAC,4CAAS,CACrBgD,QAAQ,CAAEnH,OAAO,EAAIyF,MAAO,CAC5B2B,QAAQ,CAAE3B,MAAO,CACjB4B,QAAQ,CAAE,CAAC5B,MAAO,CACnB,CAAC,EACC,CAAC,EACH,CAAC,cAENlG,KAAA,QAAKyD,SAAS,CAAC,UAAU,CAAAgB,QAAA,eACvBzE,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,UAAA2E,QAAA,CAAQyB,MAAM,CAAG,IAAI,CAAIH,MAAM,CAAG,aAAa,CAAG,IAAK,CAAQ,CAAC,cAChEjG,IAAA,UACEuD,IAAI,CAAC,UAAU,CACfgC,IAAI,CAAC,UAAU,CACfR,KAAK,CAAEqB,MAAM,CAAG,UAAU,CAAGY,QAAQ,CAACE,QAAS,CAC/ClC,QAAQ,CAAEqC,iBAAkB,CAC5B1D,SAAS,CAAC,YAAY,CACtBmB,WAAW,CAAEsB,MAAM,CAAG,OAAO,CAAIH,MAAM,CAAG,UAAU,CAAG,OAAS,CAChE6B,QAAQ,CAAEnH,OAAO,EAAIyF,MAAO,CAC5B2B,QAAQ,CAAE3B,MAAO,CACjB4B,QAAQ,CAAE,CAAC/B,MAAM,EAAI,CAACG,MAAO,CAC9B,CAAC,EACC,CAAC,cACNlG,KAAA,QAAKyD,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9B3E,IAAA,UAAA2E,QAAA,CAAO,cAAE,CAAO,CAAC,cACjB3E,IAAA,WACEuF,IAAI,CAAC,MAAM,CACXR,KAAK,CAAEiC,QAAQ,CAACvC,IAAK,CACrBO,QAAQ,CAAEqC,iBAAkB,CAC5B1D,SAAS,CAAC,yBAAyB,CACnCmE,QAAQ,CAAEnH,OAAO,EAAIyF,MAAO,CAC5B4B,QAAQ,CAAE,CAAC5B,MAAO,CAAAzB,QAAA,CAEjBwC,iBAAiB,CAAC,CAAC,CAAC9B,GAAG,CAAC4C,KAAA,MAAC,CAACxD,IAAI,CAAEc,IAAI,CAAC,CAAA0C,KAAA,oBACpC/H,KAAA,WAAmB6E,KAAK,CAAEN,IAAK,CAAAE,QAAA,EAC5BY,IAAI,CAAC,KAAG,CAACzF,iBAAiB,CAAC2E,IAAI,CAAC,GADtBA,IAEL,CAAC,EACV,CAAC,CACI,CAAC,EACN,CAAC,EACH,CAAC,cAENzE,IAAA,QAAK2D,SAAS,CAAC,YAAY,CAAAgB,QAAA,cACzBzE,KAAA,UAAOyD,SAAS,CAAC,gBAAgB,CAAAgB,QAAA,eAC/B3E,IAAA,UACEuD,IAAI,CAAC,UAAU,CACfgC,IAAI,CAAC,UAAU,CACf+B,OAAO,CAAEN,QAAQ,CAAC1D,QAAS,CAC3B0B,QAAQ,CAAEqC,iBAAkB,CAC5BS,QAAQ,CAAEnH,OAAO,EAAIyF,MAAO,CAC7B,CAAC,2BAEJ,EAAO,CAAC,CACL,CAAC,cAENlG,KAAA,QAAKyD,SAAS,CAAC,eAAe,CAAAgB,QAAA,eAC5B3E,IAAA,WACEuD,IAAI,CAAC,QAAQ,CACbI,SAAS,CAAC,eAAe,CACzBiB,OAAO,CAAEsB,OAAQ,CACjB4B,QAAQ,CAAEnH,OAAQ,CAAAgE,QAAA,CAEjByB,MAAM,CAAG,IAAI,CAAG,IAAI,CACf,CAAC,CACR,CAACA,MAAM,eACNpG,IAAA,WACEuD,IAAI,CAAC,QAAQ,CACbI,SAAS,CAAC,eAAe,CACzBmE,QAAQ,CAAEnH,OAAQ,CAAAgE,QAAA,CAEjBhE,OAAO,CAAG,QAAQ,CAAIsF,MAAM,CAAG,IAAI,CAAG,IAAK,CACtC,CACT,EACE,CAAC,EACF,CAAC,EACJ,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9F,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}