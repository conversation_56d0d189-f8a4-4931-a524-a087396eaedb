{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./WorkTrackingDownloadModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WorkTrackingDownloadModal=_ref=>{let{isOpen,onClose,data,filteredData,selectedResponsiblePersons,currentMonthPair,monthPairs,onDownload}=_ref;const[selectionState,setSelectionState]=useState({selectedWorkTypes:new Set(),selectedWorkItems:new Set(),downloadFormat:'excel',monthRange:{start:0,end:monthPairs.length-1},// 月份对索引范围\nincludeResponsibleFilter:true});const[statistics,setStatistics]=useState({totalItems:0,workTypeCount:0,monthsCovered:''});// 获取当前使用的数据（筛选后或原始数据）\nconst getCurrentData=()=>{// 优先使用原始数据，如果有筛选条件且筛选数据不为空，则使用筛选数据\nif(selectedResponsiblePersons.length>0&&filteredData&&filteredData.length>0){return filteredData;}else if(data&&data.length>0){return data;}else{return[];}};// 按工作类型分组数据\nconst getGroupedData=()=>{const dataToUse=getCurrentData();if(!dataToUse||dataToUse.length===0){return{};}const grouped={};dataToUse.forEach(item=>{const type=item.重点工作类型||'其他';if(!grouped[type]){grouped[type]=[];}grouped[type].push(item);});return grouped;};// 获取所有工作类型\nconst getWorkTypes=()=>{const grouped=getGroupedData();return Object.keys(grouped);};useEffect(()=>{updateStatistics();},[selectionState.selectedWorkItems,selectionState.monthRange,data,filteredData]);// 计算统计信息\nconst updateStatistics=()=>{var _monthPairs$selection,_monthPairs$selection2;let totalItems=0;const workTypes=new Set();const dataToUse=getCurrentData();dataToUse.forEach((item,index)=>{const itemKey=\"item-\".concat(index);if(selectionState.selectedWorkItems.has(itemKey)){totalItems++;if(item.重点工作类型){workTypes.add(item.重点工作类型);}}});// 计算覆盖的月份范围\nconst startMonth=((_monthPairs$selection=monthPairs[selectionState.monthRange.start])===null||_monthPairs$selection===void 0?void 0:_monthPairs$selection[0])||'';const endMonth=((_monthPairs$selection2=monthPairs[selectionState.monthRange.end])===null||_monthPairs$selection2===void 0?void 0:_monthPairs$selection2[1])||'';const monthsCovered=startMonth&&endMonth?\"\".concat(startMonth,\" \\u81F3 \").concat(endMonth):'';setStatistics({totalItems,workTypeCount:workTypes.size,monthsCovered});};// 处理工作类型选择\nconst handleWorkTypeSelect=workType=>{const newSelectedWorkTypes=new Set(selectionState.selectedWorkTypes);const newSelectedWorkItems=new Set(selectionState.selectedWorkItems);const grouped=getGroupedData();const dataToUse=getCurrentData();if(newSelectedWorkTypes.has(workType)){var _grouped$workType;// 取消选择工作类型，移除该类型下的所有项目\nnewSelectedWorkTypes.delete(workType);(_grouped$workType=grouped[workType])===null||_grouped$workType===void 0?void 0:_grouped$workType.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedWorkItems.delete(\"item-\".concat(itemIndex));}});}else{var _grouped$workType2;// 选择工作类型，添加该类型下的所有项目\nnewSelectedWorkTypes.add(workType);(_grouped$workType2=grouped[workType])===null||_grouped$workType2===void 0?void 0:_grouped$workType2.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedWorkItems.add(\"item-\".concat(itemIndex));}});}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedWorkTypes:newSelectedWorkTypes,selectedWorkItems:newSelectedWorkItems}));};// 处理单项选择\nconst handleWorkItemSelect=itemIndex=>{const itemKey=\"item-\".concat(itemIndex);const newSelectedWorkItems=new Set(selectionState.selectedWorkItems);const newSelectedWorkTypes=new Set(selectionState.selectedWorkTypes);const dataToUse=getCurrentData();const item=dataToUse[itemIndex];const workType=(item===null||item===void 0?void 0:item.重点工作类型)||'其他';if(newSelectedWorkItems.has(itemKey)){newSelectedWorkItems.delete(itemKey);// 检查是否需要取消工作类型选择\nconst grouped=getGroupedData();const typeItems=grouped[workType]||[];const typeItemsSelected=typeItems.some(typeItem=>{const typeItemIndex=dataToUse.findIndex(d=>d===typeItem);return typeItemIndex>=0&&newSelectedWorkItems.has(\"item-\".concat(typeItemIndex));});if(!typeItemsSelected){newSelectedWorkTypes.delete(workType);}}else{newSelectedWorkItems.add(itemKey);// 检查是否需要添加工作类型选择\nconst grouped=getGroupedData();const typeItems=grouped[workType]||[];const allTypeItemsSelected=typeItems.every(typeItem=>{const typeItemIndex=dataToUse.findIndex(d=>d===typeItem);return typeItemIndex>=0&&(newSelectedWorkItems.has(\"item-\".concat(typeItemIndex))||typeItemIndex===itemIndex);});if(allTypeItemsSelected){newSelectedWorkTypes.add(workType);}}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedWorkTypes:newSelectedWorkTypes,selectedWorkItems:newSelectedWorkItems}));};// 工作类型全选\nconst handleWorkTypeSelectAll=workType=>{var _grouped$workType3;const newSelectedWorkItems=new Set(selectionState.selectedWorkItems);const newSelectedWorkTypes=new Set(selectionState.selectedWorkTypes);const grouped=getGroupedData();const dataToUse=getCurrentData();(_grouped$workType3=grouped[workType])===null||_grouped$workType3===void 0?void 0:_grouped$workType3.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedWorkItems.add(\"item-\".concat(itemIndex));}});newSelectedWorkTypes.add(workType);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedWorkTypes:newSelectedWorkTypes,selectedWorkItems:newSelectedWorkItems}));};// 工作类型反选\nconst handleWorkTypeUnselectAll=workType=>{var _grouped$workType4;const newSelectedWorkItems=new Set(selectionState.selectedWorkItems);const newSelectedWorkTypes=new Set(selectionState.selectedWorkTypes);const grouped=getGroupedData();const dataToUse=getCurrentData();(_grouped$workType4=grouped[workType])===null||_grouped$workType4===void 0?void 0:_grouped$workType4.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedWorkItems.delete(\"item-\".concat(itemIndex));}});newSelectedWorkTypes.delete(workType);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedWorkTypes:newSelectedWorkTypes,selectedWorkItems:newSelectedWorkItems}));};// 处理下载\nconst handleDownload=()=>{const selectedData=[];const dataToUse=getCurrentData();// 收集选中的数据\ndataToUse.forEach((item,index)=>{const itemKey=\"item-\".concat(index);if(selectionState.selectedWorkItems.has(itemKey)){selectedData.push({index,data:item});}});onDownload({selectedData,format:selectionState.downloadFormat,monthRange:selectionState.monthRange,responsibleFilter:selectionState.includeResponsibleFilter?selectedResponsiblePersons:[],statistics,monthPairs});};// 格式化显示值\nconst formatDisplayValue=value=>{if(value===null||value===undefined||value==='')return'';if(typeof value==='object'){if(value.hasOwnProperty('v'))return value.v;if(value.hasOwnProperty('w'))return value.w;if(value.hasOwnProperty('t')&&value.hasOwnProperty('v'))return value.v;if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);};if(!isOpen)return null;const groupedData=getGroupedData();const workTypes=getWorkTypes();return/*#__PURE__*/_jsx(\"div\",{className:\"work-tracking-download-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"work-tracking-download-modal\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83D\\uDCC8 \\u9009\\u62E9\\u8981\\u4E0B\\u8F7D\\u7684\\u8DDF\\u8E2A\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"current-filters\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-label\",children:\"\\uD83D\\uDCC5 \\u6708\\u4EFD\\u8303\\u56F4:\"}),/*#__PURE__*/_jsx(\"select\",{value:selectionState.monthRange.start,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{monthRange:_objectSpread(_objectSpread({},selectionState.monthRange),{},{start:Number(e.target.value)})})),children:monthPairs.map((pair,index)=>/*#__PURE__*/_jsx(\"option\",{value:index,children:pair[0]},index))}),/*#__PURE__*/_jsx(\"span\",{children:\" \\u81F3 \"}),/*#__PURE__*/_jsx(\"select\",{value:selectionState.monthRange.end,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{monthRange:_objectSpread(_objectSpread({},selectionState.monthRange),{},{end:Number(e.target.value)})})),children:monthPairs.map((pair,index)=>/*#__PURE__*/_jsx(\"option\",{value:index,children:pair[1]},index))})]}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"filter-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-label\",children:\"\\uD83D\\uDC65 \\u5F53\\u524D\\u7B5B\\u9009:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"filter-value\",children:selectedResponsiblePersons.join('、')}),/*#__PURE__*/_jsxs(\"label\",{className:\"include-filter-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectionState.includeResponsibleFilter,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{includeResponsibleFilter:e.target.checked}))}),\"\\u5E94\\u7528\\u5230\\u4E0B\\u8F7D\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"work-types-section\",children:workTypes.map(workType=>{const typeData=groupedData[workType]||[];const isTypeSelected=selectionState.selectedWorkTypes.has(workType);return/*#__PURE__*/_jsxs(\"div\",{className:\"work-type-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"work-type-header\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"work-type-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isTypeSelected,onChange:()=>handleWorkTypeSelect(workType)}),/*#__PURE__*/_jsxs(\"span\",{className:\"work-type-title\",children:[workType,\" (\",typeData.length,\"\\u9879)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"work-type-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleWorkTypeSelectAll(workType),children:\"\\u5168\\u9009\"}),/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleWorkTypeUnselectAll(workType),children:\"\\u53CD\\u9009\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"work-items-list\",children:typeData.map((item,typeIndex)=>{const dataToUse=getCurrentData();const globalIndex=dataToUse.findIndex(d=>d===item);const itemKey=\"item-\".concat(globalIndex);const isItemSelected=selectionState.selectedWorkItems.has(itemKey);// 尝试多个可能的字段名获取工作名称\nconst possibleNameFields=['重点工作名称','工作名称','指标','重点工作','名称','项目名称','工作内容'];let workName='';for(const field of possibleNameFields){if(item[field]){workName=formatDisplayValue(item[field]);break;}}// 如果没有找到名称字段，使用第一个非空字段作为标识\nif(!workName){const allFields=Object.keys(item);for(const field of allFields){if(field!=='重点工作类型'&&field!=='序号'&&item[field]){workName=\"\".concat(field,\": \").concat(formatDisplayValue(item[field]));break;}}}const responsible=formatDisplayValue(item.责任人);return/*#__PURE__*/_jsx(\"div\",{className:\"work-item-row\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"work-item-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isItemSelected,onChange:()=>handleWorkItemSelect(globalIndex)}),/*#__PURE__*/_jsxs(\"span\",{className:\"work-item-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"work-item-name\",children:workName}),responsible&&/*#__PURE__*/_jsxs(\"span\",{className:\"work-item-responsible\",children:[\"\\u8D1F\\u8D23\\u4EBA: \",responsible]})]})]})},typeIndex);})})]},workType);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"statistics\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5DF2\\u9009\\u62E9: \",statistics.totalItems,\"\\u9879\\u5DE5\\u4F5C\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u6DB5\\u76D6\\u7C7B\\u578B: \",statistics.workTypeCount,\"\\u79CD\"]}),statistics.monthsCovered&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u6708\\u4EFD: \",statistics.monthsCovered]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"format-selection\",children:/*#__PURE__*/_jsxs(\"label\",{children:[\"\\u4E0B\\u8F7D\\u683C\\u5F0F:\",/*#__PURE__*/_jsxs(\"select\",{value:selectionState.downloadFormat,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{downloadFormat:e.target.value})),children:[/*#__PURE__*/_jsx(\"option\",{value:\"excel\",children:\"Excel\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pdf\",children:\"PDF\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"csv\",children:\"CSV\\u683C\\u5F0F\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-btn\",onClick:onClose,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"download-btn\",onClick:handleDownload,disabled:statistics.totalItems===0,children:[selectionState.downloadFormat.toUpperCase(),\"\\u4E0B\\u8F7D\"]})]})]})]})});};export default WorkTrackingDownloadModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "WorkTrackingDownloadModal", "_ref", "isOpen", "onClose", "data", "filteredData", "selected<PERSON>espons<PERSON><PERSON><PERSON><PERSON>", "currentMonthPair", "monthPairs", "onDownload", "selectionState", "setSelectionState", "selectedWorkTypes", "Set", "selectedWorkItems", "downloadFormat", "<PERSON><PERSON><PERSON><PERSON>", "start", "end", "length", "includeResponsibleFilter", "statistics", "setStatistics", "totalItems", "workTypeCount", "monthsCovered", "getCurrentData", "getGroupedData", "dataToUse", "grouped", "for<PERSON>ach", "item", "type", "重点工作类型", "push", "getWorkTypes", "Object", "keys", "updateStatistics", "_monthPairs$selection", "_monthPairs$selection2", "workTypes", "index", "itemKey", "concat", "has", "add", "startMonth", "endMonth", "size", "handleWorkTypeSelect", "workType", "newSelectedWorkTypes", "newSelectedWorkItems", "_grouped$workType", "delete", "itemIndex", "findIndex", "d", "_grouped$workType2", "_objectSpread", "handleWorkItemSelect", "typeItems", "typeItemsSelected", "some", "typeItem", "typeItemIndex", "allTypeItemsSelected", "every", "handleWorkTypeSelectAll", "_grouped$workType3", "handleWorkTypeUnselectAll", "_grouped$workType4", "handleDownload", "selectedData", "format", "responsibleFilter", "formatDisplayValue", "value", "undefined", "hasOwnProperty", "v", "w", "text", "richText", "String", "groupedData", "className", "children", "onClick", "onChange", "e", "Number", "target", "map", "pair", "join", "checked", "typeData", "isTypeSelected", "typeIndex", "globalIndex", "isItemSelected", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "workName", "field", "allFields", "responsible", "责任人", "disabled", "toUpperCase"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块二/components/WorkTrackingDownloadModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './WorkTrackingDownloadModal.css';\r\n\r\nconst WorkTrackingDownloadModal = ({ \r\n  isOpen, \r\n  onClose, \r\n  data, \r\n  filteredData,\r\n  selectedResponsiblePersons,\r\n  currentMonthPair,\r\n  monthPairs,\r\n  onDownload \r\n}) => {\r\n  const [selectionState, setSelectionState] = useState({\r\n    selectedWorkTypes: new Set(),\r\n    selectedWorkItems: new Set(),\r\n    downloadFormat: 'excel',\r\n    monthRange: { start: 0, end: monthPairs.length - 1 }, // 月份对索引范围\r\n    includeResponsibleFilter: true\r\n  });\r\n\r\n  const [statistics, setStatistics] = useState({\r\n    totalItems: 0,\r\n    workTypeCount: 0,\r\n    monthsCovered: ''\r\n  });\r\n\r\n  // 获取当前使用的数据（筛选后或原始数据）\r\n  const getCurrentData = () => {\r\n    // 优先使用原始数据，如果有筛选条件且筛选数据不为空，则使用筛选数据\r\n    if (selectedResponsiblePersons.length > 0 && filteredData && filteredData.length > 0) {\r\n      return filteredData;\r\n    } else if (data && data.length > 0) {\r\n      return data;\r\n    } else {\r\n      return [];\r\n    }\r\n  };\r\n\r\n  // 按工作类型分组数据\r\n  const getGroupedData = () => {\r\n    const dataToUse = getCurrentData();\r\n    if (!dataToUse || dataToUse.length === 0) {\r\n      return {};\r\n    }\r\n    \r\n    const grouped = {};\r\n    dataToUse.forEach(item => {\r\n      const type = item.重点工作类型 || '其他';\r\n      if (!grouped[type]) {\r\n        grouped[type] = [];\r\n      }\r\n      grouped[type].push(item);\r\n    });\r\n    \r\n    return grouped;\r\n  };\r\n\r\n  // 获取所有工作类型\r\n  const getWorkTypes = () => {\r\n    const grouped = getGroupedData();\r\n    return Object.keys(grouped);\r\n  };\r\n\r\n  useEffect(() => {\r\n    updateStatistics();\r\n  }, [selectionState.selectedWorkItems, selectionState.monthRange, data, filteredData]);\r\n\r\n  // 计算统计信息\r\n  const updateStatistics = () => {\r\n    let totalItems = 0;\r\n    const workTypes = new Set();\r\n\r\n    const dataToUse = getCurrentData();\r\n    dataToUse.forEach((item, index) => {\r\n      const itemKey = `item-${index}`;\r\n      if (selectionState.selectedWorkItems.has(itemKey)) {\r\n        totalItems++;\r\n        if (item.重点工作类型) {\r\n          workTypes.add(item.重点工作类型);\r\n        }\r\n      }\r\n    });\r\n\r\n    // 计算覆盖的月份范围\r\n    const startMonth = monthPairs[selectionState.monthRange.start]?.[0] || '';\r\n    const endMonth = monthPairs[selectionState.monthRange.end]?.[1] || '';\r\n    const monthsCovered = startMonth && endMonth ? `${startMonth} 至 ${endMonth}` : '';\r\n\r\n    setStatistics({\r\n      totalItems,\r\n      workTypeCount: workTypes.size,\r\n      monthsCovered\r\n    });\r\n  };\r\n\r\n  // 处理工作类型选择\r\n  const handleWorkTypeSelect = (workType) => {\r\n    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);\r\n    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    if (newSelectedWorkTypes.has(workType)) {\r\n      // 取消选择工作类型，移除该类型下的所有项目\r\n      newSelectedWorkTypes.delete(workType);\r\n      grouped[workType]?.forEach(item => {\r\n        const itemIndex = dataToUse.findIndex(d => d === item);\r\n        if (itemIndex >= 0) {\r\n          newSelectedWorkItems.delete(`item-${itemIndex}`);\r\n        }\r\n      });\r\n    } else {\r\n      // 选择工作类型，添加该类型下的所有项目\r\n      newSelectedWorkTypes.add(workType);\r\n      grouped[workType]?.forEach(item => {\r\n        const itemIndex = dataToUse.findIndex(d => d === item);\r\n        if (itemIndex >= 0) {\r\n          newSelectedWorkItems.add(`item-${itemIndex}`);\r\n        }\r\n      });\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedWorkTypes: newSelectedWorkTypes,\r\n      selectedWorkItems: newSelectedWorkItems\r\n    });\r\n  };\r\n\r\n  // 处理单项选择\r\n  const handleWorkItemSelect = (itemIndex) => {\r\n    const itemKey = `item-${itemIndex}`;\r\n    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);\r\n    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);\r\n\r\n    const dataToUse = getCurrentData();\r\n    const item = dataToUse[itemIndex];\r\n    const workType = item?.重点工作类型 || '其他';\r\n\r\n    if (newSelectedWorkItems.has(itemKey)) {\r\n      newSelectedWorkItems.delete(itemKey);\r\n      // 检查是否需要取消工作类型选择\r\n      const grouped = getGroupedData();\r\n      const typeItems = grouped[workType] || [];\r\n      const typeItemsSelected = typeItems.some(typeItem => {\r\n        const typeItemIndex = dataToUse.findIndex(d => d === typeItem);\r\n        return typeItemIndex >= 0 && newSelectedWorkItems.has(`item-${typeItemIndex}`);\r\n      });\r\n      if (!typeItemsSelected) {\r\n        newSelectedWorkTypes.delete(workType);\r\n      }\r\n    } else {\r\n      newSelectedWorkItems.add(itemKey);\r\n      // 检查是否需要添加工作类型选择\r\n      const grouped = getGroupedData();\r\n      const typeItems = grouped[workType] || [];\r\n      const allTypeItemsSelected = typeItems.every(typeItem => {\r\n        const typeItemIndex = dataToUse.findIndex(d => d === typeItem);\r\n        return typeItemIndex >= 0 && (newSelectedWorkItems.has(`item-${typeItemIndex}`) || typeItemIndex === itemIndex);\r\n      });\r\n      if (allTypeItemsSelected) {\r\n        newSelectedWorkTypes.add(workType);\r\n      }\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedWorkTypes: newSelectedWorkTypes,\r\n      selectedWorkItems: newSelectedWorkItems\r\n    });\r\n  };\r\n\r\n  // 工作类型全选\r\n  const handleWorkTypeSelectAll = (workType) => {\r\n    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);\r\n    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    grouped[workType]?.forEach(item => {\r\n      const itemIndex = dataToUse.findIndex(d => d === item);\r\n      if (itemIndex >= 0) {\r\n        newSelectedWorkItems.add(`item-${itemIndex}`);\r\n      }\r\n    });\r\n    newSelectedWorkTypes.add(workType);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedWorkTypes: newSelectedWorkTypes,\r\n      selectedWorkItems: newSelectedWorkItems\r\n    });\r\n  };\r\n\r\n  // 工作类型反选\r\n  const handleWorkTypeUnselectAll = (workType) => {\r\n    const newSelectedWorkItems = new Set(selectionState.selectedWorkItems);\r\n    const newSelectedWorkTypes = new Set(selectionState.selectedWorkTypes);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    grouped[workType]?.forEach(item => {\r\n      const itemIndex = dataToUse.findIndex(d => d === item);\r\n      if (itemIndex >= 0) {\r\n        newSelectedWorkItems.delete(`item-${itemIndex}`);\r\n      }\r\n    });\r\n    newSelectedWorkTypes.delete(workType);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedWorkTypes: newSelectedWorkTypes,\r\n      selectedWorkItems: newSelectedWorkItems\r\n    });\r\n  };\r\n\r\n  // 处理下载\r\n  const handleDownload = () => {\r\n    const selectedData = [];\r\n    const dataToUse = getCurrentData();\r\n    \r\n    // 收集选中的数据\r\n    dataToUse.forEach((item, index) => {\r\n      const itemKey = `item-${index}`;\r\n      if (selectionState.selectedWorkItems.has(itemKey)) {\r\n        selectedData.push({\r\n          index,\r\n          data: item\r\n        });\r\n      }\r\n    });\r\n\r\n    onDownload({\r\n      selectedData,\r\n      format: selectionState.downloadFormat,\r\n      monthRange: selectionState.monthRange,\r\n      responsibleFilter: selectionState.includeResponsibleFilter ? selectedResponsiblePersons : [],\r\n      statistics,\r\n      monthPairs\r\n    });\r\n  };\r\n\r\n  // 格式化显示值\r\n  const formatDisplayValue = (value) => {\r\n    if (value === null || value === undefined || value === '') return '';\r\n    if (typeof value === 'object') {\r\n      if (value.hasOwnProperty('v')) return value.v;\r\n      if (value.hasOwnProperty('w')) return value.w;\r\n      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;\r\n      if (value.text !== undefined) return value.text;\r\n      if (value.richText !== undefined) return value.richText;\r\n      if (value.value !== undefined) return value.value;\r\n      return String(value);\r\n    }\r\n    return String(value);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const groupedData = getGroupedData();\r\n  const workTypes = getWorkTypes();\r\n\r\n  return (\r\n    <div className=\"work-tracking-download-overlay\">\r\n      <div className=\"work-tracking-download-modal\">\r\n        <div className=\"modal-header\">\r\n          <h2>📈 选择要下载的跟踪数据</h2>\r\n          <button className=\"close-btn\" onClick={onClose}>×</button>\r\n        </div>\r\n\r\n        <div className=\"modal-content\">\r\n          {/* 筛选条件显示 */}\r\n          <div className=\"current-filters\">\r\n            <div className=\"filter-item\">\r\n              <span className=\"filter-label\">📅 月份范围:</span>\r\n              <select\r\n                value={selectionState.monthRange.start}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  monthRange: { ...selectionState.monthRange, start: Number(e.target.value) }\r\n                })}\r\n              >\r\n                {monthPairs.map((pair, index) => (\r\n                  <option key={index} value={index}>{pair[0]}</option>\r\n                ))}\r\n              </select>\r\n              <span> 至 </span>\r\n              <select\r\n                value={selectionState.monthRange.end}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  monthRange: { ...selectionState.monthRange, end: Number(e.target.value) }\r\n                })}\r\n              >\r\n                {monthPairs.map((pair, index) => (\r\n                  <option key={index} value={index}>{pair[1]}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n            \r\n            {selectedResponsiblePersons.length > 0 && (\r\n              <div className=\"filter-item\">\r\n                <span className=\"filter-label\">👥 当前筛选:</span>\r\n                <span className=\"filter-value\">{selectedResponsiblePersons.join('、')}</span>\r\n                <label className=\"include-filter-checkbox\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectionState.includeResponsibleFilter}\r\n                    onChange={(e) => setSelectionState({\r\n                      ...selectionState,\r\n                      includeResponsibleFilter: e.target.checked\r\n                    })}\r\n                  />\r\n                  应用到下载\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 工作类型选择 */}\r\n          <div className=\"work-types-section\">\r\n            {workTypes.map(workType => {\r\n              const typeData = groupedData[workType] || [];\r\n              const isTypeSelected = selectionState.selectedWorkTypes.has(workType);\r\n              \r\n              return (\r\n                <div key={workType} className=\"work-type-section\">\r\n                  <div className=\"work-type-header\">\r\n                    <label className=\"work-type-checkbox\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={isTypeSelected}\r\n                        onChange={() => handleWorkTypeSelect(workType)}\r\n                      />\r\n                      <span className=\"work-type-title\">\r\n                        {workType} ({typeData.length}项)\r\n                      </span>\r\n                    </label>\r\n                    <div className=\"work-type-actions\">\r\n                      <button \r\n                        className=\"action-btn\"\r\n                        onClick={() => handleWorkTypeSelectAll(workType)}\r\n                      >\r\n                        全选\r\n                      </button>\r\n                      <button \r\n                        className=\"action-btn\"\r\n                        onClick={() => handleWorkTypeUnselectAll(workType)}\r\n                      >\r\n                        反选\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"work-items-list\">\r\n                    {typeData.map((item, typeIndex) => {\r\n                      const dataToUse = getCurrentData();\r\n                      const globalIndex = dataToUse.findIndex(d => d === item);\r\n                      const itemKey = `item-${globalIndex}`;\r\n                      const isItemSelected = selectionState.selectedWorkItems.has(itemKey);\r\n                      \r\n                      // 尝试多个可能的字段名获取工作名称\r\n                      const possibleNameFields = [\r\n                        '重点工作名称', '工作名称', '指标', '重点工作', '名称', '项目名称', '工作内容'\r\n                      ];\r\n                      let workName = '';\r\n                      for (const field of possibleNameFields) {\r\n                        if (item[field]) {\r\n                          workName = formatDisplayValue(item[field]);\r\n                          break;\r\n                        }\r\n                      }\r\n                      \r\n                      // 如果没有找到名称字段，使用第一个非空字段作为标识\r\n                      if (!workName) {\r\n                        const allFields = Object.keys(item);\r\n                        for (const field of allFields) {\r\n                          if (field !== '重点工作类型' && field !== '序号' && item[field]) {\r\n                            workName = `${field}: ${formatDisplayValue(item[field])}`;\r\n                            break;\r\n                          }\r\n                        }\r\n                      }\r\n                      \r\n                      const responsible = formatDisplayValue(item.责任人);\r\n\r\n                      return (\r\n                        <div key={typeIndex} className=\"work-item-row\">\r\n                          <label className=\"work-item-checkbox\">\r\n                            <input\r\n                              type=\"checkbox\"\r\n                              checked={isItemSelected}\r\n                              onChange={() => handleWorkItemSelect(globalIndex)}\r\n                            />\r\n                            <span className=\"work-item-content\">\r\n                              <span className=\"work-item-name\">{workName}</span>\r\n                              {responsible && (\r\n                                <span className=\"work-item-responsible\">负责人: {responsible}</span>\r\n                              )}\r\n                            </span>\r\n                          </label>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"modal-footer\">\r\n          <div className=\"statistics\">\r\n            <span>已选择: {statistics.totalItems}项工作</span>\r\n            <span>涵盖类型: {statistics.workTypeCount}种</span>\r\n            {statistics.monthsCovered && (\r\n              <span>月份: {statistics.monthsCovered}</span>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"format-selection\">\r\n            <label>\r\n              下载格式:\r\n              <select \r\n                value={selectionState.downloadFormat}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  downloadFormat: e.target.value\r\n                })}\r\n              >\r\n                <option value=\"excel\">Excel格式</option>\r\n                <option value=\"pdf\">PDF格式</option>\r\n                <option value=\"csv\">CSV格式</option>\r\n              </select>\r\n            </label>\r\n          </div>\r\n\r\n          <div className=\"action-buttons\">\r\n            <button className=\"cancel-btn\" onClick={onClose}>\r\n              取消\r\n            </button>\r\n            <button \r\n              className=\"download-btn\"\r\n              onClick={handleDownload}\r\n              disabled={statistics.totalItems === 0}\r\n            >\r\n              {selectionState.downloadFormat.toUpperCase()}下载\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WorkTrackingDownloadModal; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,yBAAyB,CAAGC,IAAA,EAS5B,IAT6B,CACjCC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,YAAY,CACZC,0BAA0B,CAC1BC,gBAAgB,CAChBC,UAAU,CACVC,UACF,CAAC,CAAAR,IAAA,CACC,KAAM,CAACS,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,CACnDkB,iBAAiB,CAAE,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC5BC,iBAAiB,CAAE,GAAI,CAAAD,GAAG,CAAC,CAAC,CAC5BE,cAAc,CAAE,OAAO,CACvBC,UAAU,CAAE,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAEV,UAAU,CAACW,MAAM,CAAG,CAAE,CAAC,CAAE;AACtDC,wBAAwB,CAAE,IAC5B,CAAC,CAAC,CAEF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,CAC3C6B,UAAU,CAAE,CAAC,CACbC,aAAa,CAAE,CAAC,CAChBC,aAAa,CAAE,EACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B;AACA,GAAIpB,0BAA0B,CAACa,MAAM,CAAG,CAAC,EAAId,YAAY,EAAIA,YAAY,CAACc,MAAM,CAAG,CAAC,CAAE,CACpF,MAAO,CAAAd,YAAY,CACrB,CAAC,IAAM,IAAID,IAAI,EAAIA,IAAI,CAACe,MAAM,CAAG,CAAC,CAAE,CAClC,MAAO,CAAAf,IAAI,CACb,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,SAAS,CAAGF,cAAc,CAAC,CAAC,CAClC,GAAI,CAACE,SAAS,EAAIA,SAAS,CAACT,MAAM,GAAK,CAAC,CAAE,CACxC,MAAO,CAAC,CAAC,CACX,CAEA,KAAM,CAAAU,OAAO,CAAG,CAAC,CAAC,CAClBD,SAAS,CAACE,OAAO,CAACC,IAAI,EAAI,CACxB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,MAAM,EAAI,IAAI,CAChC,GAAI,CAACJ,OAAO,CAACG,IAAI,CAAC,CAAE,CAClBH,OAAO,CAACG,IAAI,CAAC,CAAG,EAAE,CACpB,CACAH,OAAO,CAACG,IAAI,CAAC,CAACE,IAAI,CAACH,IAAI,CAAC,CAC1B,CAAC,CAAC,CAEF,MAAO,CAAAF,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAM,YAAY,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAN,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,MAAO,CAAAS,MAAM,CAACC,IAAI,CAACR,OAAO,CAAC,CAC7B,CAAC,CAEDlC,SAAS,CAAC,IAAM,CACd2C,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAAC5B,cAAc,CAACI,iBAAiB,CAAEJ,cAAc,CAACM,UAAU,CAAEZ,IAAI,CAAEC,YAAY,CAAC,CAAC,CAErF;AACA,KAAM,CAAAiC,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,GAAI,CAAAjB,UAAU,CAAG,CAAC,CAClB,KAAM,CAAAkB,SAAS,CAAG,GAAI,CAAA5B,GAAG,CAAC,CAAC,CAE3B,KAAM,CAAAe,SAAS,CAAGF,cAAc,CAAC,CAAC,CAClCE,SAAS,CAACE,OAAO,CAAC,CAACC,IAAI,CAAEW,KAAK,GAAK,CACjC,KAAM,CAAAC,OAAO,SAAAC,MAAA,CAAWF,KAAK,CAAE,CAC/B,GAAIhC,cAAc,CAACI,iBAAiB,CAAC+B,GAAG,CAACF,OAAO,CAAC,CAAE,CACjDpB,UAAU,EAAE,CACZ,GAAIQ,IAAI,CAACE,MAAM,CAAE,CACfQ,SAAS,CAACK,GAAG,CAACf,IAAI,CAACE,MAAM,CAAC,CAC5B,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAc,UAAU,CAAG,EAAAR,qBAAA,CAAA/B,UAAU,CAACE,cAAc,CAACM,UAAU,CAACC,KAAK,CAAC,UAAAsB,qBAAA,iBAA3CA,qBAAA,CAA8C,CAAC,CAAC,GAAI,EAAE,CACzE,KAAM,CAAAS,QAAQ,CAAG,EAAAR,sBAAA,CAAAhC,UAAU,CAACE,cAAc,CAACM,UAAU,CAACE,GAAG,CAAC,UAAAsB,sBAAA,iBAAzCA,sBAAA,CAA4C,CAAC,CAAC,GAAI,EAAE,CACrE,KAAM,CAAAf,aAAa,CAAGsB,UAAU,EAAIC,QAAQ,IAAAJ,MAAA,CAAMG,UAAU,aAAAH,MAAA,CAAMI,QAAQ,EAAK,EAAE,CAEjF1B,aAAa,CAAC,CACZC,UAAU,CACVC,aAAa,CAAEiB,SAAS,CAACQ,IAAI,CAC7BxB,aACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAyB,oBAAoB,CAAIC,QAAQ,EAAK,CACzC,KAAM,CAAAC,oBAAoB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,iBAAiB,CAAC,CACtE,KAAM,CAAAyC,oBAAoB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,iBAAiB,CAAC,CAEtE,KAAM,CAAAe,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGF,cAAc,CAAC,CAAC,CAElC,GAAI0B,oBAAoB,CAACP,GAAG,CAACM,QAAQ,CAAC,CAAE,KAAAG,iBAAA,CACtC;AACAF,oBAAoB,CAACG,MAAM,CAACJ,QAAQ,CAAC,CACrC,CAAAG,iBAAA,CAAAzB,OAAO,CAACsB,QAAQ,CAAC,UAAAG,iBAAA,iBAAjBA,iBAAA,CAAmBxB,OAAO,CAACC,IAAI,EAAI,CACjC,KAAM,CAAAyB,SAAS,CAAG5B,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAK3B,IAAI,CAAC,CACtD,GAAIyB,SAAS,EAAI,CAAC,CAAE,CAClBH,oBAAoB,CAACE,MAAM,SAAAX,MAAA,CAASY,SAAS,CAAE,CAAC,CAClD,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,KAAAG,kBAAA,CACL;AACAP,oBAAoB,CAACN,GAAG,CAACK,QAAQ,CAAC,CAClC,CAAAQ,kBAAA,CAAA9B,OAAO,CAACsB,QAAQ,CAAC,UAAAQ,kBAAA,iBAAjBA,kBAAA,CAAmB7B,OAAO,CAACC,IAAI,EAAI,CACjC,KAAM,CAAAyB,SAAS,CAAG5B,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAK3B,IAAI,CAAC,CACtD,GAAIyB,SAAS,EAAI,CAAC,CAAE,CAClBH,oBAAoB,CAACP,GAAG,SAAAF,MAAA,CAASY,SAAS,CAAE,CAAC,CAC/C,CACF,CAAC,CAAC,CACJ,CAEA7C,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,iBAAiB,CAAEwC,oBAAoB,CACvCtC,iBAAiB,CAAEuC,oBAAoB,EACxC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAQ,oBAAoB,CAAIL,SAAS,EAAK,CAC1C,KAAM,CAAAb,OAAO,SAAAC,MAAA,CAAWY,SAAS,CAAE,CACnC,KAAM,CAAAH,oBAAoB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,iBAAiB,CAAC,CACtE,KAAM,CAAAsC,oBAAoB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,iBAAiB,CAAC,CAEtE,KAAM,CAAAgB,SAAS,CAAGF,cAAc,CAAC,CAAC,CAClC,KAAM,CAAAK,IAAI,CAAGH,SAAS,CAAC4B,SAAS,CAAC,CACjC,KAAM,CAAAL,QAAQ,CAAG,CAAApB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEE,MAAM,GAAI,IAAI,CAErC,GAAIoB,oBAAoB,CAACR,GAAG,CAACF,OAAO,CAAC,CAAE,CACrCU,oBAAoB,CAACE,MAAM,CAACZ,OAAO,CAAC,CACpC;AACA,KAAM,CAAAd,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAmC,SAAS,CAAGjC,OAAO,CAACsB,QAAQ,CAAC,EAAI,EAAE,CACzC,KAAM,CAAAY,iBAAiB,CAAGD,SAAS,CAACE,IAAI,CAACC,QAAQ,EAAI,CACnD,KAAM,CAAAC,aAAa,CAAGtC,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKO,QAAQ,CAAC,CAC9D,MAAO,CAAAC,aAAa,EAAI,CAAC,EAAIb,oBAAoB,CAACR,GAAG,SAAAD,MAAA,CAASsB,aAAa,CAAE,CAAC,CAChF,CAAC,CAAC,CACF,GAAI,CAACH,iBAAiB,CAAE,CACtBX,oBAAoB,CAACG,MAAM,CAACJ,QAAQ,CAAC,CACvC,CACF,CAAC,IAAM,CACLE,oBAAoB,CAACP,GAAG,CAACH,OAAO,CAAC,CACjC;AACA,KAAM,CAAAd,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAmC,SAAS,CAAGjC,OAAO,CAACsB,QAAQ,CAAC,EAAI,EAAE,CACzC,KAAM,CAAAgB,oBAAoB,CAAGL,SAAS,CAACM,KAAK,CAACH,QAAQ,EAAI,CACvD,KAAM,CAAAC,aAAa,CAAGtC,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKO,QAAQ,CAAC,CAC9D,MAAO,CAAAC,aAAa,EAAI,CAAC,GAAKb,oBAAoB,CAACR,GAAG,SAAAD,MAAA,CAASsB,aAAa,CAAE,CAAC,EAAIA,aAAa,GAAKV,SAAS,CAAC,CACjH,CAAC,CAAC,CACF,GAAIW,oBAAoB,CAAE,CACxBf,oBAAoB,CAACN,GAAG,CAACK,QAAQ,CAAC,CACpC,CACF,CAEAxC,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,iBAAiB,CAAEwC,oBAAoB,CACvCtC,iBAAiB,CAAEuC,oBAAoB,EACxC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgB,uBAAuB,CAAIlB,QAAQ,EAAK,KAAAmB,kBAAA,CAC5C,KAAM,CAAAjB,oBAAoB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,iBAAiB,CAAC,CACtE,KAAM,CAAAsC,oBAAoB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,iBAAiB,CAAC,CAEtE,KAAM,CAAAiB,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGF,cAAc,CAAC,CAAC,CAElC,CAAA4C,kBAAA,CAAAzC,OAAO,CAACsB,QAAQ,CAAC,UAAAmB,kBAAA,iBAAjBA,kBAAA,CAAmBxC,OAAO,CAACC,IAAI,EAAI,CACjC,KAAM,CAAAyB,SAAS,CAAG5B,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAK3B,IAAI,CAAC,CACtD,GAAIyB,SAAS,EAAI,CAAC,CAAE,CAClBH,oBAAoB,CAACP,GAAG,SAAAF,MAAA,CAASY,SAAS,CAAE,CAAC,CAC/C,CACF,CAAC,CAAC,CACFJ,oBAAoB,CAACN,GAAG,CAACK,QAAQ,CAAC,CAElCxC,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,iBAAiB,CAAEwC,oBAAoB,CACvCtC,iBAAiB,CAAEuC,oBAAoB,EACxC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAkB,yBAAyB,CAAIpB,QAAQ,EAAK,KAAAqB,kBAAA,CAC9C,KAAM,CAAAnB,oBAAoB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,iBAAiB,CAAC,CACtE,KAAM,CAAAsC,oBAAoB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,iBAAiB,CAAC,CAEtE,KAAM,CAAAiB,OAAO,CAAGF,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGF,cAAc,CAAC,CAAC,CAElC,CAAA8C,kBAAA,CAAA3C,OAAO,CAACsB,QAAQ,CAAC,UAAAqB,kBAAA,iBAAjBA,kBAAA,CAAmB1C,OAAO,CAACC,IAAI,EAAI,CACjC,KAAM,CAAAyB,SAAS,CAAG5B,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAK3B,IAAI,CAAC,CACtD,GAAIyB,SAAS,EAAI,CAAC,CAAE,CAClBH,oBAAoB,CAACE,MAAM,SAAAX,MAAA,CAASY,SAAS,CAAE,CAAC,CAClD,CACF,CAAC,CAAC,CACFJ,oBAAoB,CAACG,MAAM,CAACJ,QAAQ,CAAC,CAErCxC,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,iBAAiB,CAAEwC,oBAAoB,CACvCtC,iBAAiB,CAAEuC,oBAAoB,EACxC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAoB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAA9C,SAAS,CAAGF,cAAc,CAAC,CAAC,CAElC;AACAE,SAAS,CAACE,OAAO,CAAC,CAACC,IAAI,CAAEW,KAAK,GAAK,CACjC,KAAM,CAAAC,OAAO,SAAAC,MAAA,CAAWF,KAAK,CAAE,CAC/B,GAAIhC,cAAc,CAACI,iBAAiB,CAAC+B,GAAG,CAACF,OAAO,CAAC,CAAE,CACjD+B,YAAY,CAACxC,IAAI,CAAC,CAChBQ,KAAK,CACLtC,IAAI,CAAE2B,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFtB,UAAU,CAAC,CACTiE,YAAY,CACZC,MAAM,CAAEjE,cAAc,CAACK,cAAc,CACrCC,UAAU,CAAEN,cAAc,CAACM,UAAU,CACrC4D,iBAAiB,CAAElE,cAAc,CAACU,wBAAwB,CAAGd,0BAA0B,CAAG,EAAE,CAC5Fe,UAAU,CACVb,UACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAqE,kBAAkB,CAAIC,KAAK,EAAK,CACpC,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,MAAO,EAAE,CACpE,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACG,CAAC,CAC7C,GAAIH,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACI,CAAC,CAC7C,GAAIJ,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,EAAIF,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACG,CAAC,CAC1E,GAAIH,KAAK,CAACK,IAAI,GAAKJ,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACK,IAAI,CAC/C,GAAIL,KAAK,CAACM,QAAQ,GAAKL,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACM,QAAQ,CACvD,GAAIN,KAAK,CAACA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAO,MAAM,CAACP,KAAK,CAAC,CACtB,CACA,MAAO,CAAAO,MAAM,CAACP,KAAK,CAAC,CACtB,CAAC,CAED,GAAI,CAAC5E,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAAoF,WAAW,CAAG3D,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAc,SAAS,CAAGN,YAAY,CAAC,CAAC,CAEhC,mBACEtC,IAAA,QAAK0F,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CzF,KAAA,QAAKwF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CzF,KAAA,QAAKwF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B3F,IAAA,OAAA2F,QAAA,CAAI,2EAAa,CAAI,CAAC,cACtB3F,IAAA,WAAQ0F,SAAS,CAAC,WAAW,CAACE,OAAO,CAAEtF,OAAQ,CAAAqF,QAAA,CAAC,MAAC,CAAQ,CAAC,EACvD,CAAC,cAENzF,KAAA,QAAKwF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BzF,KAAA,QAAKwF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BzF,KAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3F,IAAA,SAAM0F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wCAAQ,CAAM,CAAC,cAC9C3F,IAAA,WACEiF,KAAK,CAAEpE,cAAc,CAACM,UAAU,CAACC,KAAM,CACvCyE,QAAQ,CAAGC,CAAC,EAAKhF,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBM,UAAU,CAAA4C,aAAA,CAAAA,aAAA,IAAOlD,cAAc,CAACM,UAAU,MAAEC,KAAK,CAAE2E,MAAM,CAACD,CAAC,CAACE,MAAM,CAACf,KAAK,CAAC,EAAE,EAC5E,CAAE,CAAAU,QAAA,CAEFhF,UAAU,CAACsF,GAAG,CAAC,CAACC,IAAI,CAAErD,KAAK,gBAC1B7C,IAAA,WAAoBiF,KAAK,CAAEpC,KAAM,CAAA8C,QAAA,CAAEO,IAAI,CAAC,CAAC,CAAC,EAA7BrD,KAAsC,CACpD,CAAC,CACI,CAAC,cACT7C,IAAA,SAAA2F,QAAA,CAAM,UAAG,CAAM,CAAC,cAChB3F,IAAA,WACEiF,KAAK,CAAEpE,cAAc,CAACM,UAAU,CAACE,GAAI,CACrCwE,QAAQ,CAAGC,CAAC,EAAKhF,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBM,UAAU,CAAA4C,aAAA,CAAAA,aAAA,IAAOlD,cAAc,CAACM,UAAU,MAAEE,GAAG,CAAE0E,MAAM,CAACD,CAAC,CAACE,MAAM,CAACf,KAAK,CAAC,EAAE,EAC1E,CAAE,CAAAU,QAAA,CAEFhF,UAAU,CAACsF,GAAG,CAAC,CAACC,IAAI,CAAErD,KAAK,gBAC1B7C,IAAA,WAAoBiF,KAAK,CAAEpC,KAAM,CAAA8C,QAAA,CAAEO,IAAI,CAAC,CAAC,CAAC,EAA7BrD,KAAsC,CACpD,CAAC,CACI,CAAC,EACN,CAAC,CAELpC,0BAA0B,CAACa,MAAM,CAAG,CAAC,eACpCpB,KAAA,QAAKwF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B3F,IAAA,SAAM0F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wCAAQ,CAAM,CAAC,cAC9C3F,IAAA,SAAM0F,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAElF,0BAA0B,CAAC0F,IAAI,CAAC,GAAG,CAAC,CAAO,CAAC,cAC5EjG,KAAA,UAAOwF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACxC3F,IAAA,UACEmC,IAAI,CAAC,UAAU,CACfiE,OAAO,CAAEvF,cAAc,CAACU,wBAAyB,CACjDsE,QAAQ,CAAGC,CAAC,EAAKhF,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBU,wBAAwB,CAAEuE,CAAC,CAACE,MAAM,CAACI,OAAO,EAC3C,CAAE,CACJ,CAAC,iCAEJ,EAAO,CAAC,EACL,CACN,EACE,CAAC,cAGNpG,IAAA,QAAK0F,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAChC/C,SAAS,CAACqD,GAAG,CAAC3C,QAAQ,EAAI,CACzB,KAAM,CAAA+C,QAAQ,CAAGZ,WAAW,CAACnC,QAAQ,CAAC,EAAI,EAAE,CAC5C,KAAM,CAAAgD,cAAc,CAAGzF,cAAc,CAACE,iBAAiB,CAACiC,GAAG,CAACM,QAAQ,CAAC,CAErE,mBACEpD,KAAA,QAAoBwF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/CzF,KAAA,QAAKwF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/BzF,KAAA,UAAOwF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC3F,IAAA,UACEmC,IAAI,CAAC,UAAU,CACfiE,OAAO,CAAEE,cAAe,CACxBT,QAAQ,CAAEA,CAAA,GAAMxC,oBAAoB,CAACC,QAAQ,CAAE,CAChD,CAAC,cACFpD,KAAA,SAAMwF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAC9BrC,QAAQ,CAAC,IAAE,CAAC+C,QAAQ,CAAC/E,MAAM,CAAC,SAC/B,EAAM,CAAC,EACF,CAAC,cACRpB,KAAA,QAAKwF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC3F,IAAA,WACE0F,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMpB,uBAAuB,CAAClB,QAAQ,CAAE,CAAAqC,QAAA,CAClD,cAED,CAAQ,CAAC,cACT3F,IAAA,WACE0F,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMlB,yBAAyB,CAACpB,QAAQ,CAAE,CAAAqC,QAAA,CACpD,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAEN3F,IAAA,QAAK0F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7BU,QAAQ,CAACJ,GAAG,CAAC,CAAC/D,IAAI,CAAEqE,SAAS,GAAK,CACjC,KAAM,CAAAxE,SAAS,CAAGF,cAAc,CAAC,CAAC,CAClC,KAAM,CAAA2E,WAAW,CAAGzE,SAAS,CAAC6B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAK3B,IAAI,CAAC,CACxD,KAAM,CAAAY,OAAO,SAAAC,MAAA,CAAWyD,WAAW,CAAE,CACrC,KAAM,CAAAC,cAAc,CAAG5F,cAAc,CAACI,iBAAiB,CAAC+B,GAAG,CAACF,OAAO,CAAC,CAEpE;AACA,KAAM,CAAA4D,kBAAkB,CAAG,CACzB,QAAQ,CAAE,MAAM,CAAE,IAAI,CAAE,MAAM,CAAE,IAAI,CAAE,MAAM,CAAE,MAAM,CACrD,CACD,GAAI,CAAAC,QAAQ,CAAG,EAAE,CACjB,IAAK,KAAM,CAAAC,KAAK,GAAI,CAAAF,kBAAkB,CAAE,CACtC,GAAIxE,IAAI,CAAC0E,KAAK,CAAC,CAAE,CACfD,QAAQ,CAAG3B,kBAAkB,CAAC9C,IAAI,CAAC0E,KAAK,CAAC,CAAC,CAC1C,MACF,CACF,CAEA;AACA,GAAI,CAACD,QAAQ,CAAE,CACb,KAAM,CAAAE,SAAS,CAAGtE,MAAM,CAACC,IAAI,CAACN,IAAI,CAAC,CACnC,IAAK,KAAM,CAAA0E,KAAK,GAAI,CAAAC,SAAS,CAAE,CAC7B,GAAID,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,EAAI1E,IAAI,CAAC0E,KAAK,CAAC,CAAE,CACvDD,QAAQ,IAAA5D,MAAA,CAAM6D,KAAK,OAAA7D,MAAA,CAAKiC,kBAAkB,CAAC9C,IAAI,CAAC0E,KAAK,CAAC,CAAC,CAAE,CACzD,MACF,CACF,CACF,CAEA,KAAM,CAAAE,WAAW,CAAG9B,kBAAkB,CAAC9C,IAAI,CAAC6E,GAAG,CAAC,CAEhD,mBACE/G,IAAA,QAAqB0F,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5CzF,KAAA,UAAOwF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eACnC3F,IAAA,UACEmC,IAAI,CAAC,UAAU,CACfiE,OAAO,CAAEK,cAAe,CACxBZ,QAAQ,CAAEA,CAAA,GAAM7B,oBAAoB,CAACwC,WAAW,CAAE,CACnD,CAAC,cACFtG,KAAA,SAAMwF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACjC3F,IAAA,SAAM0F,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAEgB,QAAQ,CAAO,CAAC,CACjDG,WAAW,eACV5G,KAAA,SAAMwF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,sBAAK,CAACmB,WAAW,EAAO,CACjE,EACG,CAAC,EACF,CAAC,EAbAP,SAcL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,GA9EEjD,QA+EL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAENpD,KAAA,QAAKwF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BzF,KAAA,QAAKwF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzF,KAAA,SAAAyF,QAAA,EAAM,sBAAK,CAACnE,UAAU,CAACE,UAAU,CAAC,oBAAG,EAAM,CAAC,cAC5CxB,KAAA,SAAAyF,QAAA,EAAM,4BAAM,CAACnE,UAAU,CAACG,aAAa,CAAC,QAAC,EAAM,CAAC,CAC7CH,UAAU,CAACI,aAAa,eACvB1B,KAAA,SAAAyF,QAAA,EAAM,gBAAI,CAACnE,UAAU,CAACI,aAAa,EAAO,CAC3C,EACE,CAAC,cAEN5B,IAAA,QAAK0F,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BzF,KAAA,UAAAyF,QAAA,EAAO,2BAEL,cAAAzF,KAAA,WACE+E,KAAK,CAAEpE,cAAc,CAACK,cAAe,CACrC2E,QAAQ,CAAGC,CAAC,EAAKhF,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBK,cAAc,CAAE4E,CAAC,CAACE,MAAM,CAACf,KAAK,EAC/B,CAAE,CAAAU,QAAA,eAEH3F,IAAA,WAAQiF,KAAK,CAAC,OAAO,CAAAU,QAAA,CAAC,mBAAO,CAAQ,CAAC,cACtC3F,IAAA,WAAQiF,KAAK,CAAC,KAAK,CAAAU,QAAA,CAAC,iBAAK,CAAQ,CAAC,cAClC3F,IAAA,WAAQiF,KAAK,CAAC,KAAK,CAAAU,QAAA,CAAC,iBAAK,CAAQ,CAAC,EAC5B,CAAC,EACJ,CAAC,CACL,CAAC,cAENzF,KAAA,QAAKwF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B3F,IAAA,WAAQ0F,SAAS,CAAC,YAAY,CAACE,OAAO,CAAEtF,OAAQ,CAAAqF,QAAA,CAAC,cAEjD,CAAQ,CAAC,cACTzF,KAAA,WACEwF,SAAS,CAAC,cAAc,CACxBE,OAAO,CAAEhB,cAAe,CACxBoC,QAAQ,CAAExF,UAAU,CAACE,UAAU,GAAK,CAAE,CAAAiE,QAAA,EAErC9E,cAAc,CAACK,cAAc,CAAC+F,WAAW,CAAC,CAAC,CAAC,cAC/C,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA9G,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}