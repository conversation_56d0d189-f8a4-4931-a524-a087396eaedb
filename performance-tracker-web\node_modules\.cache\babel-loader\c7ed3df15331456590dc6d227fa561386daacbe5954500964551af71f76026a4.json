{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'../styles/WorkTarget.css';import excelService from'../services/excelService';import SelectiveDownloadModal from'../components/SelectiveDownloadModal';import downloadService from'../services/downloadService';import{ButtonGuard}from'../../auth/components/PermissionGuard';import routeGuard from'../../auth/utils/routeGuards';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WorkTarget=_ref=>{var _dataStats$totalWeigh,_dataStats$totalWeigh2,_dataStats$totalWeigh3,_sections$find;let{onNavigate,isAuthenticated,currentUser,userRole}=_ref;const[activeSection,setActiveSection]=useState(null);const[data,setData]=useState({keyIndicators:[],qualityIndicators:[],keyWork:[]});const[loading,setLoading]=useState(true);const[editingCell,setEditingCell]=useState(null);const[dataStats,setDataStats]=useState({});const[syncStatus,setSyncStatus]=useState('已同步');// 选择性下载相关状态\nconst[showDownloadModal,setShowDownloadModal]=useState(false);const[downloadLoading,setDownloadLoading]=useState(false);// 数据输入优化相关状态\nconst[tempValues,setTempValues]=useState({});// 临时存储编辑中的值\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);useEffect(()=>{// 初始化数据加载\nloadData();// 设置同步回调\nexcelService.setSyncCallbacks(()=>setSyncStatus('同步成功'),error=>setSyncStatus('同步失败'));// 清理函数\nreturn()=>{if(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}if(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}};},[]);// 强制刷新数据的方法\nconst forceRefresh=async()=>{console.log('强制刷新数据...');setSyncStatus('刷新中...');await loadData();setSyncStatus('刷新完成');};const loadData=async()=>{setLoading(true);try{// 强制重新加载数据\nconsole.log('开始加载数据...');const excelData=await excelService.loadExcel();console.log('加载的数据:',excelData);if(excelData){var _excelData$keyWork;setData(excelData);setDataStats(excelService.getDataStats());console.log('数据设置完成 - keyWork:',((_excelData$keyWork=excelData.keyWork)===null||_excelData$keyWork===void 0?void 0:_excelData$keyWork.length)||0,'项');}else{console.error('未获取到数据');}}catch(error){console.error('数据加载失败:',error);}setLoading(false);};const sections=[{key:'keyIndicators',title:'关键指标（40分）',color:'#20ff4d',icon:'🎯'},{key:'qualityIndicators',title:'质量指标（20分）',color:'#00d4aa',icon:'⭐'},{key:'keyWork',title:'重点工作（40分）',color:'#4dd0ff',icon:'🚀'}];const handleSectionClick=sectionKey=>{setActiveSection(activeSection===sectionKey?null:sectionKey);};// 防抖保存函数\nconst debouncedSave=useCallback(async(section,rowIndex,field,value)=>{try{setSyncStatus('同步中...');// 取消之前的保存操作\nif(pendingSaveRef.current){var _pendingSaveRef$curre3,_pendingSaveRef$curre4;(_pendingSaveRef$curre3=(_pendingSaveRef$curre4=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre3===void 0?void 0:_pendingSaveRef$curre3.call(_pendingSaveRef$curre4);}// 创建新的保存操作\nconst controller=new AbortController();pendingSaveRef.current=controller;// 调用双向同步\nawait excelService.updateData(section,rowIndex,field,value);// 如果没有被取消，更新状态\nif(!controller.signal.aborted){setSyncStatus('同步成功');setTimeout(()=>setSyncStatus('已同步'),1000);pendingSaveRef.current=null;}}catch(error){if(!error.name==='AbortError'){console.error('保存失败:',error);setSyncStatus('同步失败');}}},[]);// 处理输入变化（实时更新UI，延迟保存）\nconst handleInputChange=(section,rowIndex,field,value)=>{// 立即更新UI显示\nconst newData=_objectSpread({},data);newData[section][rowIndex][field]=value;setData(newData);// 存储临时值\nconst key=\"\".concat(section,\"-\").concat(rowIndex,\"-\").concat(field);setTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));// 清除之前的定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的防抖定时器\nsaveTimeoutRef.current=setTimeout(()=>{debouncedSave(section,rowIndex,field,value);},800);// 800ms防抖延迟\n};// 处理失焦保存（立即保存）\nconst handleBlurSave=async(section,rowIndex,field)=>{const key=\"\".concat(section,\"-\").concat(rowIndex,\"-\").concat(field);const value=tempValues[key];if(value!==undefined){// 清除防抖定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);saveTimeoutRef.current=null;}// 立即保存\nawait debouncedSave(section,rowIndex,field,value);// 清除临时值\nsetTempValues(prev=>{const newTemp=_objectSpread({},prev);delete newTemp[key];return newTemp;});}};// 兼容原有的handleCellEdit接口\nconst handleCellEdit=async(section,rowIndex,field,value)=>{handleInputChange(section,rowIndex,field,value);};const startEdit=(section,rowIndex,field)=>{setEditingCell(\"\".concat(section,\"-\").concat(rowIndex,\"-\").concat(field));};const finishEdit=()=>{setEditingCell(null);};// 处理选择性下载\nconst handleDownload=async selectionData=>{setDownloadLoading(true);try{const result=await downloadService.handleSelectiveDownload(selectionData);if(result.success){console.log('下载成功:',result.message);// 可以在这里添加成功提示\nalert(result.message);}}catch(error){console.error('下载失败:',error);alert('下载失败: '+error.message);}finally{setDownloadLoading(false);setShowDownloadModal(false);}};// 打开下载模态框\nconst openDownloadModal=()=>{setShowDownloadModal(true);};// 关闭下载模态框\nconst closeDownloadModal=()=>{setShowDownloadModal(false);};const renderTable=sectionKey=>{const sectionData=data[sectionKey]||[];console.log(\"\\u6E32\\u67D3\".concat(sectionKey,\"\\u6570\\u636E:\"),sectionData);if(sectionData.length===0){return/*#__PURE__*/_jsxs(\"div\",{className:\"no-data\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B64\\u90E8\\u5206\\u6682\\u65E0\\u6570\\u636E\"}),/*#__PURE__*/_jsxs(\"p\",{style:{fontSize:'0.8rem',color:'#666',marginTop:'10px'},children:[\"\\u8C03\\u8BD5\\u4FE1\\u606F: \",sectionKey,\" \\u6570\\u636E\\u957F\\u5EA6\\u4E3A \",sectionData.length]})]});}const totalWeight=sectionData.reduce((sum,item)=>{const weight=item.权重;// 处理空字符串权重的情况\nif(weight===''||weight===null||weight===undefined)return sum;return sum+(typeof weight==='number'?weight:weight?Number(weight)||0:0);},0);return/*#__PURE__*/_jsxs(\"div\",{className:\"data-table-container\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"table-summary\",children:[/*#__PURE__*/_jsx(\"span\",{children:sectionKey==='keyWork'?\"\\u5DE5\\u4F5C\\u9879\\u76EE: \".concat(sectionData.length,\" \\u9879\"):\"\\u6307\\u6807\\u6570\\u91CF: \".concat(sectionData.filter(row=>!row.isMergedCell||row.isMergedCell&&row.目标值).length,\" \\u9879\")}),sectionKey==='keyWork'&&/*#__PURE__*/_jsx(\"span\",{className:\"key-work-notice-inline\",style:{fontSize:'16px'},children:\"\\u6DE1\\u6A59\\u8272\\u6807\\u8BC6\\u4E3A\\u65B0\\u6750\\u7EA7\\u91CD\\u70B9\\u5DE5\\u4F5C\"}),totalWeight>0&&/*#__PURE__*/_jsxs(\"span\",{style:{fontSize:'16px'},children:[\"\\u6743\\u91CD: \",totalWeight,\" \\u5206\"]})]}),/*#__PURE__*/_jsxs(\"table\",{className:\"data-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"col-number\",style:{fontSize:'15px'},children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-indicator\",style:{fontSize:'15px'},children:\"\\u6307\\u6807\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-target\",style:{fontSize:'15px'},children:\"\\u76EE\\u6807\\u503C\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-weight\",style:{fontSize:'15px'},children:\"\\u6743\\u91CD\\uFF08\\u5206\\uFF09\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-standard\",style:{fontSize:'15px'},children:\"\\u8003\\u6838\\u6807\\u51C6\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-category\",style:{fontSize:'15px'},children:\"\\u6307\\u6807\\u5206\\u7C7B\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-responsible\",style:{fontSize:'15px'},children:\"\\u8D23\\u4EFB\\u4EBA\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:sectionData.map((row,index)=>{const isMergedStart=row.isMergedStart;const isMergedCell=row.isMergedCell;const rowSpan=row.rowSpan||1;// 判断是否为新材级重点工作（基于后端返回的颜色标识）\nconst isHighlightWork=row.isHighlighted&&row.highlightType==='新材级重点工作';return/*#__PURE__*/_jsx(\"tr\",{className:\"data-row \".concat(isMergedCell?'merged-row':'',\" \").concat(isMergedStart?'merged-start-row':'',\" \").concat(isHighlightWork?'highlight-work':''),children:Object.entries(row).filter(_ref2=>{let[field]=_ref2;return!['rowSpan','isMergedStart','isMergedCell','isHighlighted','highlightType'].includes(field);}).map(_ref3=>{let[field,value]=_ref3;const cellKey=\"\".concat(sectionKey,\"-\").concat(index,\"-\").concat(field);const isEditing=editingCell===cellKey;const isEditable=field!=='序号';// 处理合并单元格\nif(isMergedCell&&(field==='序号'||field==='指标')){return null;}let cellProps={key:field,className:\"data-cell col-\".concat(field.toLowerCase(),\" \").concat(isEditable?'editable':'',\" \").concat(isHighlightWork?'highlight-cell':'')};if(isMergedStart&&(field==='序号'||field==='指标')){cellProps.rowSpan=rowSpan;cellProps.className+=' merged-cell-content';if(isHighlightWork){cellProps.className+=' highlight-merged-cell';}}// 安全地提取和处理内容 - 处理可能的对象值\nlet contentToRender=value;// 处理对象类型的值（如 {font, text} 或 {richText}）\nif(typeof value==='object'&&value!==null){if(value.text!==undefined){contentToRender=value.text;}else if(value.richText!==undefined){contentToRender=value.richText;}else if(value.value!==undefined){contentToRender=value.value;}else{contentToRender='';}}// 确保 contentToRender 是字符串\ncontentToRender=String(contentToRender||'');// 处理包含换行符的文本\nif(contentToRender.includes('\\n')){contentToRender=contentToRender.split('\\n').map((line,i,arr)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[line,i<arr.length-1&&/*#__PURE__*/_jsx(\"br\",{})]},i));}else{// 对于权重字段，只有当值不为空且不是空字符串时才添加单位\nif(field==='权重'&&contentToRender&&contentToRender.trim()!==''){contentToRender=\"\".concat(contentToRender,\" \\u5206\");}// 移除新材级重点工作文字标识（如果存在）\nif(field==='责任人'&&isHighlightWork&&contentToRender&&contentToRender.includes('新材级重点工作')){contentToRender=contentToRender.replace(/新材级重点工作/g,'').trim();}}return/*#__PURE__*/_jsx(\"td\",_objectSpread(_objectSpread({},cellProps),{},{children:isEditing?/*#__PURE__*/_jsx(\"textarea\",{value:typeof value==='object'&&value!==null?value.text||value.richText||value.value||'':value||'',onChange:e=>handleInputChange(sectionKey,index,field,e.target.value),onBlur:()=>{handleBlurSave(sectionKey,index,field);finishEdit();},onKeyDown:e=>{if(e.key==='Enter'&&e.ctrlKey){handleBlurSave(sectionKey,index,field);finishEdit();}if(e.key==='Escape'){finishEdit();}},className:\"cell-input\",rows:typeof value==='string'&&value.includes('\\n')?value.split('\\n').length+1:2}):/*#__PURE__*/_jsx(\"span\",{onClick:()=>isEditable&&startEdit(sectionKey,index,field),className:\"cell-content \".concat(isEditable?'editable-cell':''),title:isEditable?'点击编辑':'',children:contentToRender})}));})},index);})})]})]});};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"work-target-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u6570\\u636E...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"work-target\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"back-btn-top\",onClick:()=>onNavigate('home'),style:{fontSize:'18px'},children:\"\\u8FD4\\u56DE\\u9996\\u9875\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"page-title\",children:\"\\u5F00\\u53D1\\u4E2D\\u5FC32025\\u5E74\\u5DE5\\u4F5C\\u76EE\\u6807\\u7BA1\\u7406\\u8D23\\u4EFB\\u4E66\"}),/*#__PURE__*/_jsx(\"p\",{className:\"page-subtitle\",children:\"\\u5173\\u952E\\u6307\\u6807\\xB7\\u8D28\\u91CF\\u6307\\u6807\\xB7\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7BA1\\u7406\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-status\",style:{position:'absolute',top:'calc(50% + 10px)',right:'15px',transform:'translateY(-50%)',display:'flex',alignItems:'center',gap:'6px',fontSize:'18px',zIndex:10,height:'40px'},children:/*#__PURE__*/_jsx(\"span\",{className:\"status-indicator\",style:{padding:'5px 12px',borderRadius:'6px',fontSize:'18px',fontWeight:'600',transition:'all 0.3s ease',whiteSpace:'nowrap',height:'40px',display:'flex',alignItems:'center',background:'linear-gradient(45deg, #00d4aa, #20ff4d)',color:'#000',border:'1px solid rgba(0, 212, 170, 0.6)',boxShadow:'0 0 8px rgba(0, 212, 170, 0.4)'},children:syncStatus})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"data-overview\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"overview-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"overview-number\",style:{fontSize:'32px'},children:data.keyIndicators?Math.max(...data.keyIndicators.filter(item=>item.序号&&!isNaN(item.序号)).map(item=>Number(item.序号)))||0:0}),/*#__PURE__*/_jsx(\"span\",{className:\"overview-label\",style:{fontSize:'16px'},children:\"\\u5173\\u952E\\u6307\\u6807\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"overview-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"overview-number\",style:{fontSize:'32px'},children:data.qualityIndicators?Math.max(...data.qualityIndicators.filter(item=>item.序号&&!isNaN(item.序号)).map(item=>Number(item.序号)))||0:0}),/*#__PURE__*/_jsx(\"span\",{className:\"overview-label\",style:{fontSize:'16px'},children:\"\\u8D28\\u91CF\\u6307\\u6807\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"overview-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"overview-number\",style:{fontSize:'32px'},children:data.keyWork?Math.max(...data.keyWork.filter(item=>item.序号&&!isNaN(item.序号)).map(item=>Number(item.序号)))||0:0}),/*#__PURE__*/_jsx(\"span\",{className:\"overview-label\",style:{fontSize:'16px'},children:\"\\u91CD\\u70B9\\u5DE5\\u4F5C\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"overview-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"overview-number\",style:{fontSize:'32px'},children:(((_dataStats$totalWeigh=dataStats.totalWeight)===null||_dataStats$totalWeigh===void 0?void 0:_dataStats$totalWeigh.keyIndicators)||0)+(((_dataStats$totalWeigh2=dataStats.totalWeight)===null||_dataStats$totalWeigh2===void 0?void 0:_dataStats$totalWeigh2.qualityIndicators)||0)+(((_dataStats$totalWeigh3=dataStats.totalWeight)===null||_dataStats$totalWeigh3===void 0?void 0:_dataStats$totalWeigh3.keyWork)||0)}),/*#__PURE__*/_jsx(\"span\",{className:\"overview-label\",style:{fontSize:'16px'},children:\"\\u603B\\u6743\\u91CD\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-area\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"section-tabs\",children:sections.map(section=>/*#__PURE__*/_jsxs(\"div\",{className:\"section-tab \".concat(activeSection===section.key?'active':''),style:{'--section-color':section.color},onClick:()=>handleSectionClick(section.key),children:[/*#__PURE__*/_jsx(\"div\",{className:\"tab-icon\",style:{fontSize:'40px'},children:section.icon}),/*#__PURE__*/_jsxs(\"div\",{className:\"tab-content\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"tab-title\",style:{fontSize:'20px'},children:section.title}),/*#__PURE__*/_jsxs(\"div\",{className:\"tab-count\",style:{fontSize:'15px'},children:[data[section.key]?Math.max(...data[section.key].filter(item=>item.序号&&!isNaN(item.序号)).map(item=>Number(item.序号)))||0:0,\" \\u9879\\u6307\\u6807\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"tab-arrow\",children:activeSection===section.key?'▼':'▶'})]},section.key))}),activeSection&&/*#__PURE__*/_jsxs(\"div\",{className:\"table-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"table-header\",children:[/*#__PURE__*/_jsxs(\"h3\",{children:[(_sections$find=sections.find(s=>s.key===activeSection))===null||_sections$find===void 0?void 0:_sections$find.title,\" - \\u8BE6\\u7EC6\\u6570\\u636E\"]}),/*#__PURE__*/_jsxs(\"div\",{className:\"table-actions\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"edit-hint\",style:{fontSize:'15px'},children:\"\\uD83D\\uDCA1 \\u70B9\\u51FB\\u5355\\u5143\\u683C\\u5373\\u53EF\\u7F16\\u8F91\\uFF0C\\u81EA\\u52A8\\u540C\\u6B65Excel\"}),/*#__PURE__*/_jsx(\"button\",{className:\"refresh-btn\",onClick:forceRefresh,children:\"\\uD83D\\uDD04 \\u5237\\u65B0\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"download-btn\",onClick:openDownloadModal,style:{marginLeft:'10px'},children:\"\\uD83D\\uDCCA \\u9009\\u62E9\\u6027\\u4E0B\\u8F7D\"})]})]}),renderTable(activeSection)]}),!activeSection&&/*#__PURE__*/_jsxs(\"div\",{className:\"welcome-message\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u8BF7\\u9009\\u62E9\\u8981\\u67E5\\u770B\\u7684\\u6307\\u6807\\u7C7B\\u578B\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u70B9\\u51FB\\u4E0A\\u65B9\\u4EFB\\u610F\\u6807\\u9898\\u67E5\\u770B\\u5BF9\\u5E94\\u7684\\u8BE6\\u7EC6\\u6570\\u636E\"})]})]}),/*#__PURE__*/_jsx(SelectiveDownloadModal,{isOpen:showDownloadModal,onClose:closeDownloadModal,data:data,onDownload:handleDownload})]});};export default WorkTarget;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "excelService", "SelectiveDownloadModal", "downloadService", "<PERSON><PERSON><PERSON><PERSON>", "routeGuard", "jsx", "_jsx", "jsxs", "_jsxs", "WorkTarget", "_ref", "_dataStats$totalWeigh", "_dataStats$totalWeigh2", "_dataStats$totalWeigh3", "_sections$find", "onNavigate", "isAuthenticated", "currentUser", "userRole", "activeSection", "setActiveSection", "data", "setData", "keyIndicators", "qualityIndicators", "keyWork", "loading", "setLoading", "editingCell", "setEditingCell", "dataStats", "setDataStats", "syncStatus", "setSyncStatus", "showDownloadModal", "setShowDownloadModal", "downloadLoading", "setDownloadLoading", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "saveTimeoutRef", "pendingSaveRef", "loadData", "setSyncCallbacks", "error", "current", "clearTimeout", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "forceRefresh", "console", "log", "excelData", "loadExcel", "_excelData$keyWork", "getDataStats", "length", "sections", "key", "title", "color", "icon", "handleSectionClick", "sectionKey", "debouncedSave", "section", "rowIndex", "field", "value", "_pendingSaveRef$curre3", "_pendingSaveRef$curre4", "controller", "AbortController", "updateData", "signal", "aborted", "setTimeout", "name", "handleInputChange", "newData", "_objectSpread", "concat", "prev", "handleBlurSave", "undefined", "newTemp", "handleCellEdit", "startEdit", "finishEdit", "handleDownload", "selectionData", "result", "handleSelectiveDownload", "success", "message", "alert", "openDownloadModal", "closeDownloadModal", "renderTable", "sectionData", "className", "children", "style", "fontSize", "marginTop", "totalWeight", "reduce", "sum", "item", "weight", "权重", "Number", "filter", "row", "isMergedCell", "目标值", "map", "index", "isMergedStart", "rowSpan", "isHighlightWork", "isHighlighted", "highlightType", "Object", "entries", "_ref2", "includes", "_ref3", "cellKey", "isEditing", "isEditable", "cellProps", "toLowerCase", "contentToRender", "text", "richText", "String", "split", "line", "i", "arr", "Fragment", "trim", "replace", "onChange", "e", "target", "onBlur", "onKeyDown", "ctrl<PERSON>ey", "rows", "onClick", "position", "top", "right", "transform", "display", "alignItems", "gap", "zIndex", "height", "padding", "borderRadius", "fontWeight", "transition", "whiteSpace", "background", "border", "boxShadow", "Math", "max", "序号", "isNaN", "find", "s", "marginLeft", "isOpen", "onClose", "onDownload"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块一/pages/WorkTarget.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/WorkTarget.css';\nimport excelService from '../services/excelService';\nimport SelectiveDownloadModal from '../components/SelectiveDownloadModal';\nimport downloadService from '../services/downloadService';\nimport { ButtonGuard } from '../../auth/components/PermissionGuard';\nimport routeGuard from '../../auth/utils/routeGuards';\n\nconst WorkTarget = ({ onNavigate, isAuthenticated, currentUser, userRole }) => {\n  const [activeSection, setActiveSection] = useState(null);\n  const [data, setData] = useState({\n    keyIndicators: [],\n    qualityIndicators: [],\n    keyWork: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [editingCell, setEditingCell] = useState(null);\n  const [dataStats, setDataStats] = useState({});\n  const [syncStatus, setSyncStatus] = useState('已同步');\n\n  // 选择性下载相关状态\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n\n  // 数据输入优化相关状态\n  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n\n  useEffect(() => {\n    // 初始化数据加载\n    loadData();\n\n    // 设置同步回调\n    excelService.setSyncCallbacks(\n      () => setSyncStatus('同步成功'),\n      (error) => setSyncStatus('同步失败')\n    );\n\n    // 清理函数\n    return () => {\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n      }\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n    };\n  }, []);\n\n  // 强制刷新数据的方法\n  const forceRefresh = async () => {\n    console.log('强制刷新数据...');\n    setSyncStatus('刷新中...');\n    await loadData();\n    setSyncStatus('刷新完成');\n  };\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      // 强制重新加载数据\n      console.log('开始加载数据...');\n      const excelData = await excelService.loadExcel();\n      console.log('加载的数据:', excelData);\n      \n      if (excelData) {\n        setData(excelData);\n        setDataStats(excelService.getDataStats());\n        console.log('数据设置完成 - keyWork:', excelData.keyWork?.length || 0, '项');\n      } else {\n        console.error('未获取到数据');\n      }\n    } catch (error) {\n      console.error('数据加载失败:', error);\n    }\n    setLoading(false);\n  };\n\n  const sections = [\n    {\n      key: 'keyIndicators',\n      title: '关键指标（40分）',\n      color: '#20ff4d',\n      icon: '🎯'\n    },\n    {\n      key: 'qualityIndicators',\n      title: '质量指标（20分）',\n      color: '#00d4aa',\n      icon: '⭐'\n    },\n    {\n      key: 'keyWork',\n      title: '重点工作（40分）',\n      color: '#4dd0ff',\n      icon: '🚀'\n    }\n  ];\n\n  const handleSectionClick = (sectionKey) => {\n    setActiveSection(activeSection === sectionKey ? null : sectionKey);\n  };\n\n  // 防抖保存函数\n  const debouncedSave = useCallback(async (section, rowIndex, field, value) => {\n    try {\n      setSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用双向同步\n      await excelService.updateData(section, rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        setSyncStatus('同步成功');\n        setTimeout(() => setSyncStatus('已同步'), 1000);\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (!error.name === 'AbortError') {\n        console.error('保存失败:', error);\n        setSyncStatus('同步失败');\n      }\n    }\n  }, []);\n\n  // 处理输入变化（实时更新UI，延迟保存）\n  const handleInputChange = (section, rowIndex, field, value) => {\n    // 立即更新UI显示\n    const newData = { ...data };\n    newData[section][rowIndex][field] = value;\n    setData(newData);\n\n    // 存储临时值\n    const key = `${section}-${rowIndex}-${field}`;\n    setTempValues(prev => ({ ...prev, [key]: value }));\n\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(section, rowIndex, field, value);\n    }, 800); // 800ms防抖延迟\n  };\n\n  // 处理失焦保存（立即保存）\n  const handleBlurSave = async (section, rowIndex, field) => {\n    const key = `${section}-${rowIndex}-${field}`;\n    const value = tempValues[key];\n\n    if (value !== undefined) {\n      // 清除防抖定时器\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n        saveTimeoutRef.current = null;\n      }\n\n      // 立即保存\n      await debouncedSave(section, rowIndex, field, value);\n\n      // 清除临时值\n      setTempValues(prev => {\n        const newTemp = { ...prev };\n        delete newTemp[key];\n        return newTemp;\n      });\n    }\n  };\n\n  // 兼容原有的handleCellEdit接口\n  const handleCellEdit = async (section, rowIndex, field, value) => {\n    handleInputChange(section, rowIndex, field, value);\n  };\n\n  const startEdit = (section, rowIndex, field) => {\n    setEditingCell(`${section}-${rowIndex}-${field}`);\n  };\n\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 处理选择性下载\n  const handleDownload = async (selectionData) => {\n    setDownloadLoading(true);\n    try {\n      const result = await downloadService.handleSelectiveDownload(selectionData);\n      if (result.success) {\n        console.log('下载成功:', result.message);\n        // 可以在这里添加成功提示\n        alert(result.message);\n      }\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败: ' + error.message);\n    } finally {\n      setDownloadLoading(false);\n      setShowDownloadModal(false);\n    }\n  };\n\n  // 打开下载模态框\n  const openDownloadModal = () => {\n    setShowDownloadModal(true);\n  };\n\n  // 关闭下载模态框\n  const closeDownloadModal = () => {\n    setShowDownloadModal(false);\n  };\n\n  const renderTable = (sectionKey) => {\n    const sectionData = data[sectionKey] || [];\n    console.log(`渲染${sectionKey}数据:`, sectionData);\n    \n    if (sectionData.length === 0) {\n      return (\n        <div className=\"no-data\">\n          <p>此部分暂无数据</p>\n          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '10px' }}>\n            调试信息: {sectionKey} 数据长度为 {sectionData.length}\n          </p>\n        </div>\n      );\n    }\n\n    const totalWeight = sectionData.reduce((sum, item) => {\n      const weight = item.权重;\n      // 处理空字符串权重的情况\n      if (weight === '' || weight === null || weight === undefined) return sum;\n      return sum + (typeof weight === 'number' ? weight : (weight ? Number(weight) || 0 : 0));\n    }, 0);\n\n    return (\n      <div className=\"data-table-container\">\n        <div className=\"table-summary\">\n          <span>\n            {sectionKey === 'keyWork' ? \n              `工作项目: ${sectionData.length} 项` : \n              `指标数量: ${sectionData.filter(row => !row.isMergedCell || (row.isMergedCell && row.目标值)).length} 项`\n            }\n          </span>\n          \n          {/* 重点工作说明 - 放在中间位置 */}\n          {sectionKey === 'keyWork' && (\n            <span \n              className=\"key-work-notice-inline\"\n              style={{ fontSize: '16px' }}\n            >\n              淡橙色标识为新材级重点工作\n            </span>\n          )}\n          \n          {totalWeight > 0 && <span style={{ fontSize: '16px' }}>权重: {totalWeight} 分</span>}\n        </div>\n        \n        <table className=\"data-table\">\n          <thead>\n            <tr>\n              <th className=\"col-number\" style={{ fontSize: '15px' }}>序号</th>\n              <th className=\"col-indicator\" style={{ fontSize: '15px' }}>指标</th>\n              <th className=\"col-target\" style={{ fontSize: '15px' }}>目标值</th>\n              <th className=\"col-weight\" style={{ fontSize: '15px' }}>权重（分）</th>\n              <th className=\"col-standard\" style={{ fontSize: '15px' }}>考核标准</th>\n              <th className=\"col-category\" style={{ fontSize: '15px' }}>指标分类</th>\n              <th className=\"col-responsible\" style={{ fontSize: '15px' }}>责任人</th>\n            </tr>\n          </thead>\n          <tbody>\n            {sectionData.map((row, index) => {\n              const isMergedStart = row.isMergedStart;\n              const isMergedCell = row.isMergedCell;\n              const rowSpan = row.rowSpan || 1;\n              \n              // 判断是否为新材级重点工作（基于后端返回的颜色标识）\n              const isHighlightWork = row.isHighlighted && row.highlightType === '新材级重点工作';\n              \n              return (\n                <tr key={index} className={`data-row ${isMergedCell ? 'merged-row' : ''} ${isMergedStart ? 'merged-start-row' : ''} ${isHighlightWork ? 'highlight-work' : ''}`}>\n                  {Object.entries(row).filter(([field]) => !['rowSpan', 'isMergedStart', 'isMergedCell', 'isHighlighted', 'highlightType'].includes(field)).map(([field, value]) => {\n                    const cellKey = `${sectionKey}-${index}-${field}`;\n                    const isEditing = editingCell === cellKey;\n                    const isEditable = field !== '序号';\n\n                    // 处理合并单元格\n                    if (isMergedCell && (field === '序号' || field === '指标')) {\n                      return null; \n                    }\n\n                    let cellProps = {\n                      key: field,\n                      className: `data-cell col-${field.toLowerCase()} ${isEditable ? 'editable' : ''} ${isHighlightWork ? 'highlight-cell' : ''}`\n                    };\n\n                    if (isMergedStart && (field === '序号' || field === '指标')) {\n                      cellProps.rowSpan = rowSpan;\n                      cellProps.className += ' merged-cell-content';\n                      if (isHighlightWork) {\n                        cellProps.className += ' highlight-merged-cell';\n                      }\n                    }\n                    \n                    // 安全地提取和处理内容 - 处理可能的对象值\n                    let contentToRender = value;\n                    \n                    // 处理对象类型的值（如 {font, text} 或 {richText}）\n                    if (typeof value === 'object' && value !== null) {\n                      if (value.text !== undefined) {\n                        contentToRender = value.text;\n                      } else if (value.richText !== undefined) {\n                        contentToRender = value.richText;\n                      } else if (value.value !== undefined) {\n                        contentToRender = value.value;\n                      } else {\n                        contentToRender = '';\n                      }\n                    }\n                    \n                    // 确保 contentToRender 是字符串\n                    contentToRender = String(contentToRender || '');\n                    \n                    // 处理包含换行符的文本\n                    if (contentToRender.includes('\\n')) {\n                        contentToRender = contentToRender.split('\\n').map((line, i, arr) => (\n                            <React.Fragment key={i}>\n                                {line}\n                                {i < arr.length - 1 && <br />}\n                            </React.Fragment>\n                        ));\n                    } else {\n                        // 对于权重字段，只有当值不为空且不是空字符串时才添加单位\n                        if (field === '权重' && contentToRender && contentToRender.trim() !== '') {\n                            contentToRender = `${contentToRender} 分`;\n                        }\n\n                        // 移除新材级重点工作文字标识（如果存在）\n                        if (field === '责任人' && isHighlightWork && contentToRender && contentToRender.includes('新材级重点工作')) {\n                            contentToRender = contentToRender.replace(/新材级重点工作/g, '').trim();\n                        }\n                    }\n\n                    return (\n                      <td {...cellProps}>\n                        {isEditing ? (\n                          <textarea\n                            value={typeof value === 'object' && value !== null ?\n                              (value.text || value.richText || value.value || '') :\n                              (value || '')}\n                            onChange={(e) => handleInputChange(sectionKey, index, field, e.target.value)}\n                            onBlur={() => {\n                              handleBlurSave(sectionKey, index, field);\n                              finishEdit();\n                            }}\n                            onKeyDown={(e) => {\n                              if (e.key === 'Enter' && e.ctrlKey) {\n                                handleBlurSave(sectionKey, index, field);\n                                finishEdit();\n                              }\n                              if (e.key === 'Escape') {\n                                finishEdit();\n                              }\n                            }}\n                            className=\"cell-input\"\n                            rows={typeof value === 'string' && value.includes('\\n') ? value.split('\\n').length + 1 : 2}\n                          />\n                        ) : (\n                          <span\n                            onClick={() => isEditable && startEdit(sectionKey, index, field)}\n                            className={`cell-content ${isEditable ? 'editable-cell' : ''}`}\n                            title={isEditable ? '点击编辑' : ''}\n                          >\n                            {contentToRender}\n                          </span>\n                        )}\n                      </td>\n                    );\n                  })}\n\n                  {/* 移除可能存在的额外\"新材级重点工作\"标识列 */}\n                  {/* 如果之前有额外的单元格显示\"新材级重点工作\"，这里将其移除 */}\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"work-target-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>正在加载数据...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"work-target\">\n      {/* 页面头部 */}\n      <div className=\"page-header\">\n        <button \n          className=\"back-btn-top\"\n          onClick={() => onNavigate('home')}\n          style={{ fontSize: '18px' }}\n        >\n          返回首页\n        </button>\n        \n        <div className=\"header-center\">\n          <h1 className=\"page-title\">开发中心2025年工作目标管理责任书</h1>\n          <p className=\"page-subtitle\">关键指标·质量指标·重点工作管理</p>\n        </div>\n        \n        <div className=\"sync-status\" style={{\n          position: 'absolute',\n          top: 'calc(50% + 10px)',\n          right: '15px',\n          transform: 'translateY(-50%)',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '6px',\n          fontSize: '18px',\n          zIndex: 10,\n          height: '40px'\n        }}>\n          <span\n            className=\"status-indicator\"\n            style={{\n              padding: '5px 12px',\n              borderRadius: '6px',\n              fontSize: '18px',\n              fontWeight: '600',\n              transition: 'all 0.3s ease',\n              whiteSpace: 'nowrap',\n              height: '40px',\n              display: 'flex',\n              alignItems: 'center',\n              background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',\n              color: '#000',\n              border: '1px solid rgba(0, 212, 170, 0.6)',\n              boxShadow: '0 0 8px rgba(0, 212, 170, 0.4)'\n            }}\n          >\n            {syncStatus}\n          </span>\n        </div>\n      </div>\n\n      {/* 数据概览 */}\n      <div className=\"data-overview\">\n        <div className=\"overview-item\">\n          <span \n            className=\"overview-number\"\n            style={{ fontSize: '32px' }}\n          >\n            {data.keyIndicators ? \n              Math.max(...data.keyIndicators.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 \n              : 0\n            }\n          </span>\n          <span \n            className=\"overview-label\"\n            style={{ fontSize: '16px' }}\n          >\n            关键指标\n          </span>\n        </div>\n        <div className=\"overview-item\">\n          <span \n            className=\"overview-number\"\n            style={{ fontSize: '32px' }}\n          >\n            {data.qualityIndicators ? \n              Math.max(...data.qualityIndicators.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 \n              : 0\n            }\n          </span>\n          <span \n            className=\"overview-label\"\n            style={{ fontSize: '16px' }}\n          >\n            质量指标\n          </span>\n        </div>\n        <div className=\"overview-item\">\n          <span \n            className=\"overview-number\"\n            style={{ fontSize: '32px' }}\n          >\n            {data.keyWork ? \n              Math.max(...data.keyWork.filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 \n              : 0\n            }\n          </span>\n          <span \n            className=\"overview-label\"\n            style={{ fontSize: '16px' }}\n          >\n            重点工作\n          </span>\n        </div>\n        <div className=\"overview-item\">\n          <span \n            className=\"overview-number\"\n            style={{ fontSize: '32px' }}\n          >\n            {(dataStats.totalWeight?.keyIndicators || 0) + \n             (dataStats.totalWeight?.qualityIndicators || 0) + \n             (dataStats.totalWeight?.keyWork || 0)}\n          </span>\n          <span \n            className=\"overview-label\"\n            style={{ fontSize: '16px' }}\n          >\n            总权重\n          </span>\n        </div>\n      </div>\n\n      {/* 主要内容区域 */}\n      <div className=\"content-area\">\n        {/* 三个大标题 */}\n        <div className=\"section-tabs\">\n          {sections.map((section) => (\n            <div\n              key={section.key}\n              className={`section-tab ${activeSection === section.key ? 'active' : ''}`}\n              style={{ '--section-color': section.color }}\n              onClick={() => handleSectionClick(section.key)}\n            >\n              <div \n                className=\"tab-icon\"\n                style={{ fontSize: '40px' }}\n              >\n                {section.icon}\n              </div>\n              <div className=\"tab-content\">\n                <h2 \n                  className=\"tab-title\"\n                  style={{ fontSize: '20px' }}\n                >\n                  {section.title}\n                </h2>\n                <div \n                  className=\"tab-count\"\n                  style={{ fontSize: '15px' }}\n                >\n                  {data[section.key] ? \n                    Math.max(...data[section.key].filter(item => item.序号 && !isNaN(item.序号)).map(item => Number(item.序号))) || 0 \n                    : 0\n                  } 项指标\n                </div>\n              </div>\n              <div className=\"tab-arrow\">\n                {activeSection === section.key ? '▼' : '▶'}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* 数据表格展示区域 */}\n        {activeSection && (\n          <div className=\"table-section\">\n            <div className=\"table-header\">\n              <h3>\n                {sections.find(s => s.key === activeSection)?.title} - 详细数据\n              </h3>\n              <div className=\"table-actions\">\n                <span \n                  className=\"edit-hint\"\n                  style={{ fontSize: '15px' }}\n                >\n                  💡 点击单元格即可编辑，自动同步Excel\n                </span>\n                <button className=\"refresh-btn\" onClick={forceRefresh}>\n                  🔄 刷新数据\n                </button>\n                <button \n                  className=\"download-btn\" \n                  onClick={openDownloadModal}\n                  style={{ marginLeft: '10px' }}\n                >\n                  📊 选择性下载\n                </button>\n              </div>\n            </div>\n            {renderTable(activeSection)}\n          </div>\n        )}\n\n        {!activeSection && (\n          <div className=\"welcome-message\">\n            <h3>请选择要查看的指标类型</h3>\n            <p>点击上方任意标题查看对应的详细数据</p>\n          </div>\n        )}\n      </div>\n\n      {/* 选择性下载模态框 */}\n      <SelectiveDownloadModal\n        isOpen={showDownloadModal}\n        onClose={closeDownloadModal}\n        data={data}\n        onDownload={handleDownload}\n      />\n    </div>\n  );\n};\n\nexport default WorkTarget; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,sBAAsB,KAAM,sCAAsC,CACzE,MAAO,CAAAC,eAAe,KAAM,6BAA6B,CACzD,OAASC,WAAW,KAAQ,uCAAuC,CACnE,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEtD,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAA4D,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,cAAA,IAA3D,CAAEC,UAAU,CAAEC,eAAe,CAAEC,WAAW,CAAEC,QAAS,CAAC,CAAAR,IAAA,CACxE,KAAM,CAACS,aAAa,CAAEC,gBAAgB,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CACxD,KAAM,CAACyB,IAAI,CAAEC,OAAO,CAAC,CAAG1B,QAAQ,CAAC,CAC/B2B,aAAa,CAAE,EAAE,CACjBC,iBAAiB,CAAE,EAAE,CACrBC,OAAO,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACkC,SAAS,CAAEC,YAAY,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC9C,KAAM,CAACoC,UAAU,CAAEC,aAAa,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAACsC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACwC,eAAe,CAAEC,kBAAkB,CAAC,CAAGzC,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAAC0C,UAAU,CAAEC,aAAa,CAAC,CAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE;AAClD,KAAM,CAAA4C,cAAc,CAAG1C,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA2C,cAAc,CAAG3C,MAAM,CAAC,IAAI,CAAC,CAEnCD,SAAS,CAAC,IAAM,CACd;AACA6C,QAAQ,CAAC,CAAC,CAEV;AACA1C,YAAY,CAAC2C,gBAAgB,CAC3B,IAAMV,aAAa,CAAC,MAAM,CAAC,CAC1BW,KAAK,EAAKX,aAAa,CAAC,MAAM,CACjC,CAAC,CAED;AACA,MAAO,IAAM,CACX,GAAIO,cAAc,CAACK,OAAO,CAAE,CAC1BC,YAAY,CAACN,cAAc,CAACK,OAAO,CAAC,CACtC,CACA,GAAIJ,cAAc,CAACI,OAAO,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAAP,cAAc,CAACI,OAAO,EAACI,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAG,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/BC,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBpB,aAAa,CAAC,QAAQ,CAAC,CACvB,KAAM,CAAAS,QAAQ,CAAC,CAAC,CAChBT,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,CAED,KAAM,CAAAS,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3Bf,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACAyB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB,KAAM,CAAAC,SAAS,CAAG,KAAM,CAAAtD,YAAY,CAACuD,SAAS,CAAC,CAAC,CAChDH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEC,SAAS,CAAC,CAEhC,GAAIA,SAAS,CAAE,KAAAE,kBAAA,CACblC,OAAO,CAACgC,SAAS,CAAC,CAClBvB,YAAY,CAAC/B,YAAY,CAACyD,YAAY,CAAC,CAAC,CAAC,CACzCL,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAE,EAAAG,kBAAA,CAAAF,SAAS,CAAC7B,OAAO,UAAA+B,kBAAA,iBAAjBA,kBAAA,CAAmBE,MAAM,GAAI,CAAC,CAAE,GAAG,CAAC,CACvE,CAAC,IAAM,CACLN,OAAO,CAACR,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAE,MAAOA,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACAjB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED,KAAM,CAAAgC,QAAQ,CAAG,CACf,CACEC,GAAG,CAAE,eAAe,CACpBC,KAAK,CAAE,WAAW,CAClBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACD,CACEH,GAAG,CAAE,mBAAmB,CACxBC,KAAK,CAAE,WAAW,CAClBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,GACR,CAAC,CACD,CACEH,GAAG,CAAE,SAAS,CACdC,KAAK,CAAE,WAAW,CAClBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IACR,CAAC,CACF,CAED,KAAM,CAAAC,kBAAkB,CAAIC,UAAU,EAAK,CACzC7C,gBAAgB,CAACD,aAAa,GAAK8C,UAAU,CAAG,IAAI,CAAGA,UAAU,CAAC,CACpE,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGnE,WAAW,CAAC,MAAOoE,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CAC3E,GAAI,CACFrC,aAAa,CAAC,QAAQ,CAAC,CAEvB;AACA,GAAIQ,cAAc,CAACI,OAAO,CAAE,KAAA0B,sBAAA,CAAAC,sBAAA,CAC1B,CAAAD,sBAAA,EAAAC,sBAAA,CAAA/B,cAAc,CAACI,OAAO,EAACI,KAAK,UAAAsB,sBAAA,iBAA5BA,sBAAA,CAAArB,IAAA,CAAAsB,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxCjC,cAAc,CAACI,OAAO,CAAG4B,UAAU,CAEnC;AACA,KAAM,CAAAzE,YAAY,CAAC2E,UAAU,CAACR,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE9D;AACA,GAAI,CAACG,UAAU,CAACG,MAAM,CAACC,OAAO,CAAE,CAC9B5C,aAAa,CAAC,MAAM,CAAC,CACrB6C,UAAU,CAAC,IAAM7C,aAAa,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC5CQ,cAAc,CAACI,OAAO,CAAG,IAAI,CAC/B,CACF,CAAE,MAAOD,KAAK,CAAE,CACd,GAAI,CAACA,KAAK,CAACmC,IAAI,GAAK,YAAY,CAAE,CAChC3B,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BX,aAAa,CAAC,MAAM,CAAC,CACvB,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAA+C,iBAAiB,CAAGA,CAACb,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CAC7D;AACA,KAAM,CAAAW,OAAO,CAAAC,aAAA,IAAQ7D,IAAI,CAAE,CAC3B4D,OAAO,CAACd,OAAO,CAAC,CAACC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CACzChD,OAAO,CAAC2D,OAAO,CAAC,CAEhB;AACA,KAAM,CAAArB,GAAG,IAAAuB,MAAA,CAAMhB,OAAO,MAAAgB,MAAA,CAAIf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAC7C9B,aAAa,CAAC6C,IAAI,EAAAF,aAAA,CAAAA,aAAA,IAAUE,IAAI,MAAE,CAACxB,GAAG,EAAGU,KAAK,EAAG,CAAC,CAElD;AACA,GAAI9B,cAAc,CAACK,OAAO,CAAE,CAC1BC,YAAY,CAACN,cAAc,CAACK,OAAO,CAAC,CACtC,CAEA;AACAL,cAAc,CAACK,OAAO,CAAGiC,UAAU,CAAC,IAAM,CACxCZ,aAAa,CAACC,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAChD,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,CAED;AACA,KAAM,CAAAe,cAAc,CAAG,KAAAA,CAAOlB,OAAO,CAAEC,QAAQ,CAAEC,KAAK,GAAK,CACzD,KAAM,CAAAT,GAAG,IAAAuB,MAAA,CAAMhB,OAAO,MAAAgB,MAAA,CAAIf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAC7C,KAAM,CAAAC,KAAK,CAAGhC,UAAU,CAACsB,GAAG,CAAC,CAE7B,GAAIU,KAAK,GAAKgB,SAAS,CAAE,CACvB;AACA,GAAI9C,cAAc,CAACK,OAAO,CAAE,CAC1BC,YAAY,CAACN,cAAc,CAACK,OAAO,CAAC,CACpCL,cAAc,CAACK,OAAO,CAAG,IAAI,CAC/B,CAEA;AACA,KAAM,CAAAqB,aAAa,CAACC,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAEpD;AACA/B,aAAa,CAAC6C,IAAI,EAAI,CACpB,KAAM,CAAAG,OAAO,CAAAL,aAAA,IAAQE,IAAI,CAAE,CAC3B,MAAO,CAAAG,OAAO,CAAC3B,GAAG,CAAC,CACnB,MAAO,CAAA2B,OAAO,CAChB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,KAAAA,CAAOrB,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CAChEU,iBAAiB,CAACb,OAAO,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CACpD,CAAC,CAED,KAAM,CAAAmB,SAAS,CAAGA,CAACtB,OAAO,CAAEC,QAAQ,CAAEC,KAAK,GAAK,CAC9CxC,cAAc,IAAAsD,MAAA,CAAIhB,OAAO,MAAAgB,MAAA,CAAIf,QAAQ,MAAAe,MAAA,CAAId,KAAK,CAAE,CAAC,CACnD,CAAC,CAED,KAAM,CAAAqB,UAAU,CAAGA,CAAA,GAAM,CACvB7D,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAA8D,cAAc,CAAG,KAAO,CAAAC,aAAa,EAAK,CAC9CvD,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAAwD,MAAM,CAAG,KAAM,CAAA3F,eAAe,CAAC4F,uBAAuB,CAACF,aAAa,CAAC,CAC3E,GAAIC,MAAM,CAACE,OAAO,CAAE,CAClB3C,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEwC,MAAM,CAACG,OAAO,CAAC,CACpC;AACAC,KAAK,CAACJ,MAAM,CAACG,OAAO,CAAC,CACvB,CACF,CAAE,MAAOpD,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BqD,KAAK,CAAC,QAAQ,CAAGrD,KAAK,CAACoD,OAAO,CAAC,CACjC,CAAC,OAAS,CACR3D,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CACF,CAAC,CAED;AACA,KAAM,CAAA+D,iBAAiB,CAAGA,CAAA,GAAM,CAC9B/D,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAgE,kBAAkB,CAAGA,CAAA,GAAM,CAC/BhE,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAiE,WAAW,CAAInC,UAAU,EAAK,CAClC,KAAM,CAAAoC,WAAW,CAAGhF,IAAI,CAAC4C,UAAU,CAAC,EAAI,EAAE,CAC1Cb,OAAO,CAACC,GAAG,gBAAA8B,MAAA,CAAMlB,UAAU,kBAAOoC,WAAW,CAAC,CAE9C,GAAIA,WAAW,CAAC3C,MAAM,GAAK,CAAC,CAAE,CAC5B,mBACElD,KAAA,QAAK8F,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtBjG,IAAA,MAAAiG,QAAA,CAAG,4CAAO,CAAG,CAAC,cACd/F,KAAA,MAAGgG,KAAK,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAE3C,KAAK,CAAE,MAAM,CAAE4C,SAAS,CAAE,MAAO,CAAE,CAAAH,QAAA,EAAC,4BAC5D,CAACtC,UAAU,CAAC,kCAAO,CAACoC,WAAW,CAAC3C,MAAM,EAC3C,CAAC,EACD,CAAC,CAEV,CAEA,KAAM,CAAAiD,WAAW,CAAGN,WAAW,CAACO,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CACpD,KAAM,CAAAC,MAAM,CAAGD,IAAI,CAACE,EAAE,CACtB;AACA,GAAID,MAAM,GAAK,EAAE,EAAIA,MAAM,GAAK,IAAI,EAAIA,MAAM,GAAKzB,SAAS,CAAE,MAAO,CAAAuB,GAAG,CACxE,MAAO,CAAAA,GAAG,EAAI,MAAO,CAAAE,MAAM,GAAK,QAAQ,CAAGA,MAAM,CAAIA,MAAM,CAAGE,MAAM,CAACF,MAAM,CAAC,EAAI,CAAC,CAAG,CAAE,CAAC,CACzF,CAAC,CAAE,CAAC,CAAC,CAEL,mBACEvG,KAAA,QAAK8F,SAAS,CAAC,sBAAsB,CAAAC,QAAA,eACnC/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SAAAiG,QAAA,CACGtC,UAAU,GAAK,SAAS,8BAAAkB,MAAA,CACdkB,WAAW,CAAC3C,MAAM,yCAAAyB,MAAA,CAClBkB,WAAW,CAACa,MAAM,CAACC,GAAG,EAAI,CAACA,GAAG,CAACC,YAAY,EAAKD,GAAG,CAACC,YAAY,EAAID,GAAG,CAACE,GAAI,CAAC,CAAC3D,MAAM,WAAI,CAE/F,CAAC,CAGNO,UAAU,GAAK,SAAS,eACvB3D,IAAA,SACEgG,SAAS,CAAC,wBAAwB,CAClCE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,gFAED,CAAM,CACP,CAEAI,WAAW,CAAG,CAAC,eAAInG,KAAA,SAAMgG,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,EAAC,gBAAI,CAACI,WAAW,CAAC,SAAE,EAAM,CAAC,EAC9E,CAAC,cAENnG,KAAA,UAAO8F,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC3BjG,IAAA,UAAAiG,QAAA,cACE/F,KAAA,OAAA+F,QAAA,eACEjG,IAAA,OAAIgG,SAAS,CAAC,YAAY,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,cAAE,CAAI,CAAC,cAC/DjG,IAAA,OAAIgG,SAAS,CAAC,eAAe,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,cAAE,CAAI,CAAC,cAClEjG,IAAA,OAAIgG,SAAS,CAAC,YAAY,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,oBAAG,CAAI,CAAC,cAChEjG,IAAA,OAAIgG,SAAS,CAAC,YAAY,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,gCAAK,CAAI,CAAC,cAClEjG,IAAA,OAAIgG,SAAS,CAAC,cAAc,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,0BAAI,CAAI,CAAC,cACnEjG,IAAA,OAAIgG,SAAS,CAAC,cAAc,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,0BAAI,CAAI,CAAC,cACnEjG,IAAA,OAAIgG,SAAS,CAAC,iBAAiB,CAACE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAAC,oBAAG,CAAI,CAAC,EACnE,CAAC,CACA,CAAC,cACRjG,IAAA,UAAAiG,QAAA,CACGF,WAAW,CAACiB,GAAG,CAAC,CAACH,GAAG,CAAEI,KAAK,GAAK,CAC/B,KAAM,CAAAC,aAAa,CAAGL,GAAG,CAACK,aAAa,CACvC,KAAM,CAAAJ,YAAY,CAAGD,GAAG,CAACC,YAAY,CACrC,KAAM,CAAAK,OAAO,CAAGN,GAAG,CAACM,OAAO,EAAI,CAAC,CAEhC;AACA,KAAM,CAAAC,eAAe,CAAGP,GAAG,CAACQ,aAAa,EAAIR,GAAG,CAACS,aAAa,GAAK,SAAS,CAE5E,mBACEtH,IAAA,OAAgBgG,SAAS,aAAAnB,MAAA,CAAciC,YAAY,CAAG,YAAY,CAAG,EAAE,MAAAjC,MAAA,CAAIqC,aAAa,CAAG,kBAAkB,CAAG,EAAE,MAAArC,MAAA,CAAIuC,eAAe,CAAG,gBAAgB,CAAG,EAAE,CAAG,CAAAnB,QAAA,CAC7JsB,MAAM,CAACC,OAAO,CAACX,GAAG,CAAC,CAACD,MAAM,CAACa,KAAA,MAAC,CAAC1D,KAAK,CAAC,CAAA0D,KAAA,OAAK,CAAC,CAAC,SAAS,CAAE,eAAe,CAAE,cAAc,CAAE,eAAe,CAAE,eAAe,CAAC,CAACC,QAAQ,CAAC3D,KAAK,CAAC,GAAC,CAACiD,GAAG,CAACW,KAAA,EAAoB,IAAnB,CAAC5D,KAAK,CAAEC,KAAK,CAAC,CAAA2D,KAAA,CAC3J,KAAM,CAAAC,OAAO,IAAA/C,MAAA,CAAMlB,UAAU,MAAAkB,MAAA,CAAIoC,KAAK,MAAApC,MAAA,CAAId,KAAK,CAAE,CACjD,KAAM,CAAA8D,SAAS,CAAGvG,WAAW,GAAKsG,OAAO,CACzC,KAAM,CAAAE,UAAU,CAAG/D,KAAK,GAAK,IAAI,CAEjC;AACA,GAAI+C,YAAY,GAAK/C,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAI,CAAC,CAAE,CACtD,MAAO,KAAI,CACb,CAEA,GAAI,CAAAgE,SAAS,CAAG,CACdzE,GAAG,CAAES,KAAK,CACViC,SAAS,kBAAAnB,MAAA,CAAmBd,KAAK,CAACiE,WAAW,CAAC,CAAC,MAAAnD,MAAA,CAAIiD,UAAU,CAAG,UAAU,CAAG,EAAE,MAAAjD,MAAA,CAAIuC,eAAe,CAAG,gBAAgB,CAAG,EAAE,CAC5H,CAAC,CAED,GAAIF,aAAa,GAAKnD,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAK,IAAI,CAAC,CAAE,CACvDgE,SAAS,CAACZ,OAAO,CAAGA,OAAO,CAC3BY,SAAS,CAAC/B,SAAS,EAAI,sBAAsB,CAC7C,GAAIoB,eAAe,CAAE,CACnBW,SAAS,CAAC/B,SAAS,EAAI,wBAAwB,CACjD,CACF,CAEA;AACA,GAAI,CAAAiC,eAAe,CAAGjE,KAAK,CAE3B;AACA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,CAAE,CAC/C,GAAIA,KAAK,CAACkE,IAAI,GAAKlD,SAAS,CAAE,CAC5BiD,eAAe,CAAGjE,KAAK,CAACkE,IAAI,CAC9B,CAAC,IAAM,IAAIlE,KAAK,CAACmE,QAAQ,GAAKnD,SAAS,CAAE,CACvCiD,eAAe,CAAGjE,KAAK,CAACmE,QAAQ,CAClC,CAAC,IAAM,IAAInE,KAAK,CAACA,KAAK,GAAKgB,SAAS,CAAE,CACpCiD,eAAe,CAAGjE,KAAK,CAACA,KAAK,CAC/B,CAAC,IAAM,CACLiE,eAAe,CAAG,EAAE,CACtB,CACF,CAEA;AACAA,eAAe,CAAGG,MAAM,CAACH,eAAe,EAAI,EAAE,CAAC,CAE/C;AACA,GAAIA,eAAe,CAACP,QAAQ,CAAC,IAAI,CAAC,CAAE,CAChCO,eAAe,CAAGA,eAAe,CAACI,KAAK,CAAC,IAAI,CAAC,CAACrB,GAAG,CAAC,CAACsB,IAAI,CAAEC,CAAC,CAAEC,GAAG,gBAC3DtI,KAAA,CAACb,KAAK,CAACoJ,QAAQ,EAAAxC,QAAA,EACVqC,IAAI,CACJC,CAAC,CAAGC,GAAG,CAACpF,MAAM,CAAG,CAAC,eAAIpD,IAAA,QAAK,CAAC,GAFZuI,CAGL,CACnB,CAAC,CACN,CAAC,IAAM,CACH;AACA,GAAIxE,KAAK,GAAK,IAAI,EAAIkE,eAAe,EAAIA,eAAe,CAACS,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpET,eAAe,IAAApD,MAAA,CAAMoD,eAAe,WAAI,CAC5C,CAEA;AACA,GAAIlE,KAAK,GAAK,KAAK,EAAIqD,eAAe,EAAIa,eAAe,EAAIA,eAAe,CAACP,QAAQ,CAAC,SAAS,CAAC,CAAE,CAC9FO,eAAe,CAAGA,eAAe,CAACU,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CAACD,IAAI,CAAC,CAAC,CACpE,CACJ,CAEA,mBACE1I,IAAA,MAAA4E,aAAA,CAAAA,aAAA,IAAQmD,SAAS,MAAA9B,QAAA,CACd4B,SAAS,cACR7H,IAAA,aACEgE,KAAK,CAAE,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,CAC/CA,KAAK,CAACkE,IAAI,EAAIlE,KAAK,CAACmE,QAAQ,EAAInE,KAAK,CAACA,KAAK,EAAI,EAAE,CACjDA,KAAK,EAAI,EAAI,CAChB4E,QAAQ,CAAGC,CAAC,EAAKnE,iBAAiB,CAACf,UAAU,CAAEsD,KAAK,CAAElD,KAAK,CAAE8E,CAAC,CAACC,MAAM,CAAC9E,KAAK,CAAE,CAC7E+E,MAAM,CAAEA,CAAA,GAAM,CACZhE,cAAc,CAACpB,UAAU,CAAEsD,KAAK,CAAElD,KAAK,CAAC,CACxCqB,UAAU,CAAC,CAAC,CACd,CAAE,CACF4D,SAAS,CAAGH,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACvF,GAAG,GAAK,OAAO,EAAIuF,CAAC,CAACI,OAAO,CAAE,CAClClE,cAAc,CAACpB,UAAU,CAAEsD,KAAK,CAAElD,KAAK,CAAC,CACxCqB,UAAU,CAAC,CAAC,CACd,CACA,GAAIyD,CAAC,CAACvF,GAAG,GAAK,QAAQ,CAAE,CACtB8B,UAAU,CAAC,CAAC,CACd,CACF,CAAE,CACFY,SAAS,CAAC,YAAY,CACtBkD,IAAI,CAAE,MAAO,CAAAlF,KAAK,GAAK,QAAQ,EAAIA,KAAK,CAAC0D,QAAQ,CAAC,IAAI,CAAC,CAAG1D,KAAK,CAACqE,KAAK,CAAC,IAAI,CAAC,CAACjF,MAAM,CAAG,CAAC,CAAG,CAAE,CAC5F,CAAC,cAEFpD,IAAA,SACEmJ,OAAO,CAAEA,CAAA,GAAMrB,UAAU,EAAI3C,SAAS,CAACxB,UAAU,CAAEsD,KAAK,CAAElD,KAAK,CAAE,CACjEiC,SAAS,iBAAAnB,MAAA,CAAkBiD,UAAU,CAAG,eAAe,CAAG,EAAE,CAAG,CAC/DvE,KAAK,CAAEuE,UAAU,CAAG,MAAM,CAAG,EAAG,CAAA7B,QAAA,CAE/BgC,eAAe,CACZ,CACP,EACC,CAAC,CAET,CAAC,CAAC,EAlGKhB,KAsGL,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,EACL,CAAC,CAEV,CAAC,CAED,GAAI7F,OAAO,CAAE,CACX,mBACElB,KAAA,QAAK8F,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClCjG,IAAA,QAAKgG,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvChG,IAAA,MAAAiG,QAAA,CAAG,yCAAS,CAAG,CAAC,EACb,CAAC,CAEV,CAEA,mBACE/F,KAAA,QAAK8F,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1B/F,KAAA,QAAK8F,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjG,IAAA,WACEgG,SAAS,CAAC,cAAc,CACxBmD,OAAO,CAAEA,CAAA,GAAM1I,UAAU,CAAC,MAAM,CAAE,CAClCyF,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,0BAED,CAAQ,CAAC,cAET/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,OAAIgG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0FAAkB,CAAI,CAAC,cAClDjG,IAAA,MAAGgG,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,8FAAgB,CAAG,CAAC,EAC9C,CAAC,cAENjG,IAAA,QAAKgG,SAAS,CAAC,aAAa,CAACE,KAAK,CAAE,CAClCkD,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,kBAAkB,CACvBC,KAAK,CAAE,MAAM,CACbC,SAAS,CAAE,kBAAkB,CAC7BC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVvD,QAAQ,CAAE,MAAM,CAChBwD,MAAM,CAAE,EAAE,CACVC,MAAM,CAAE,MACV,CAAE,CAAA3D,QAAA,cACAjG,IAAA,SACEgG,SAAS,CAAC,kBAAkB,CAC5BE,KAAK,CAAE,CACL2D,OAAO,CAAE,UAAU,CACnBC,YAAY,CAAE,KAAK,CACnB3D,QAAQ,CAAE,MAAM,CAChB4D,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,eAAe,CAC3BC,UAAU,CAAE,QAAQ,CACpBL,MAAM,CAAE,MAAM,CACdJ,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBS,UAAU,CAAE,0CAA0C,CACtD1G,KAAK,CAAE,MAAM,CACb2G,MAAM,CAAE,kCAAkC,CAC1CC,SAAS,CAAE,gCACb,CAAE,CAAAnE,QAAA,CAEDvE,UAAU,CACP,CAAC,CACJ,CAAC,EACH,CAAC,cAGNxB,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAC,iBAAiB,CAC3BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3BlF,IAAI,CAACE,aAAa,CACjBoJ,IAAI,CAACC,GAAG,CAAC,GAAGvJ,IAAI,CAACE,aAAa,CAAC2F,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAAC+D,EAAE,EAAI,CAACC,KAAK,CAAChE,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACR,IAAI,EAAIG,MAAM,CAACH,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAAC,EAAI,CAAC,CAC1G,CAAC,CAED,CAAC,cACPvK,IAAA,SACEgG,SAAS,CAAC,gBAAgB,CAC1BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,0BAED,CAAM,CAAC,EACJ,CAAC,cACN/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAC,iBAAiB,CAC3BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3BlF,IAAI,CAACG,iBAAiB,CACrBmJ,IAAI,CAACC,GAAG,CAAC,GAAGvJ,IAAI,CAACG,iBAAiB,CAAC0F,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAAC+D,EAAE,EAAI,CAACC,KAAK,CAAChE,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACR,IAAI,EAAIG,MAAM,CAACH,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAAC,EAAI,CAAC,CAC9G,CAAC,CAED,CAAC,cACPvK,IAAA,SACEgG,SAAS,CAAC,gBAAgB,CAC1BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,0BAED,CAAM,CAAC,EACJ,CAAC,cACN/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAC,iBAAiB,CAC3BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3BlF,IAAI,CAACI,OAAO,CACXkJ,IAAI,CAACC,GAAG,CAAC,GAAGvJ,IAAI,CAACI,OAAO,CAACyF,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAAC+D,EAAE,EAAI,CAACC,KAAK,CAAChE,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACR,IAAI,EAAIG,MAAM,CAACH,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAAC,EAAI,CAAC,CACpG,CAAC,CAED,CAAC,cACPvK,IAAA,SACEgG,SAAS,CAAC,gBAAgB,CAC1BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,0BAED,CAAM,CAAC,EACJ,CAAC,cACN/F,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAC,iBAAiB,CAC3BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3B,CAAC,EAAA5F,qBAAA,CAAAmB,SAAS,CAAC6E,WAAW,UAAAhG,qBAAA,iBAArBA,qBAAA,CAAuBY,aAAa,GAAI,CAAC,GACzC,EAAAX,sBAAA,CAAAkB,SAAS,CAAC6E,WAAW,UAAA/F,sBAAA,iBAArBA,sBAAA,CAAuBY,iBAAiB,GAAI,CAAC,CAAC,EAC9C,EAAAX,sBAAA,CAAAiB,SAAS,CAAC6E,WAAW,UAAA9F,sBAAA,iBAArBA,sBAAA,CAAuBY,OAAO,GAAI,CAAC,CAAC,CAClC,CAAC,cACPnB,IAAA,SACEgG,SAAS,CAAC,gBAAgB,CAC1BE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,oBAED,CAAM,CAAC,EACJ,CAAC,EACH,CAAC,cAGN/F,KAAA,QAAK8F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE3BjG,IAAA,QAAKgG,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1B5C,QAAQ,CAAC2D,GAAG,CAAEnD,OAAO,eACpB3D,KAAA,QAEE8F,SAAS,gBAAAnB,MAAA,CAAiBhE,aAAa,GAAKgD,OAAO,CAACP,GAAG,CAAG,QAAQ,CAAG,EAAE,CAAG,CAC1E4C,KAAK,CAAE,CAAE,iBAAiB,CAAErC,OAAO,CAACL,KAAM,CAAE,CAC5C2F,OAAO,CAAEA,CAAA,GAAMzF,kBAAkB,CAACG,OAAO,CAACP,GAAG,CAAE,CAAA2C,QAAA,eAE/CjG,IAAA,QACEgG,SAAS,CAAC,UAAU,CACpBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3BpC,OAAO,CAACJ,IAAI,CACV,CAAC,cACNvD,KAAA,QAAK8F,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BjG,IAAA,OACEgG,SAAS,CAAC,WAAW,CACrBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAE3BpC,OAAO,CAACN,KAAK,CACZ,CAAC,cACLrD,KAAA,QACE8F,SAAS,CAAC,WAAW,CACrBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,EAE3BlF,IAAI,CAAC8C,OAAO,CAACP,GAAG,CAAC,CAChB+G,IAAI,CAACC,GAAG,CAAC,GAAGvJ,IAAI,CAAC8C,OAAO,CAACP,GAAG,CAAC,CAACsD,MAAM,CAACJ,IAAI,EAAIA,IAAI,CAAC+D,EAAE,EAAI,CAACC,KAAK,CAAChE,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAACvD,GAAG,CAACR,IAAI,EAAIG,MAAM,CAACH,IAAI,CAAC+D,EAAE,CAAC,CAAC,CAAC,EAAI,CAAC,CACzG,CAAC,CACJ,qBACH,EAAK,CAAC,EACH,CAAC,cACNvK,IAAA,QAAKgG,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBpF,aAAa,GAAKgD,OAAO,CAACP,GAAG,CAAG,GAAG,CAAG,GAAG,CACvC,CAAC,GA9BDO,OAAO,CAACP,GA+BV,CACN,CAAC,CACC,CAAC,CAGLzC,aAAa,eACZX,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B/F,KAAA,QAAK8F,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B/F,KAAA,OAAA+F,QAAA,GAAAzF,cAAA,CACG6C,QAAQ,CAACoH,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACpH,GAAG,GAAKzC,aAAa,CAAC,UAAAL,cAAA,iBAA3CA,cAAA,CAA6C+C,KAAK,CAAC,6BACtD,EAAI,CAAC,cACLrD,KAAA,QAAK8F,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BjG,IAAA,SACEgG,SAAS,CAAC,WAAW,CACrBE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAO,CAAE,CAAAF,QAAA,CAC7B,wGAED,CAAM,CAAC,cACPjG,IAAA,WAAQgG,SAAS,CAAC,aAAa,CAACmD,OAAO,CAAEtG,YAAa,CAAAoD,QAAA,CAAC,uCAEvD,CAAQ,CAAC,cACTjG,IAAA,WACEgG,SAAS,CAAC,cAAc,CACxBmD,OAAO,CAAEvD,iBAAkB,CAC3BM,KAAK,CAAE,CAAEyE,UAAU,CAAE,MAAO,CAAE,CAAA1E,QAAA,CAC/B,6CAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACLH,WAAW,CAACjF,aAAa,CAAC,EACxB,CACN,CAEA,CAACA,aAAa,eACbX,KAAA,QAAK8F,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BjG,IAAA,OAAAiG,QAAA,CAAI,oEAAW,CAAI,CAAC,cACpBjG,IAAA,MAAAiG,QAAA,CAAG,wGAAiB,CAAG,CAAC,EACrB,CACN,EACE,CAAC,cAGNjG,IAAA,CAACL,sBAAsB,EACrBiL,MAAM,CAAEhJ,iBAAkB,CAC1BiJ,OAAO,CAAEhF,kBAAmB,CAC5B9E,IAAI,CAAEA,IAAK,CACX+J,UAAU,CAAEzF,cAAe,CAC5B,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAlF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}