/**
 * 重点工作跟踪数据服务
 * 负责处理"开发中心2025年重点工作跟踪-填写表"的数据解析和同步
 */

import * as XLSX from 'xlsx';

class WorkTrackingService {
  constructor() {
    this.data = [];
    this.isLoaded = false;
    this.syncSuccessCallback = null;
    this.syncErrorCallback = null;
    this.excelFile = null;
  }

  /**
   * 设置同步回调函数
   */
  setSyncCallbacks(onSuccess, onError) {
    this.syncSuccessCallback = onSuccess;
    this.syncErrorCallback = onError;
  }

  /**
   * 加载重点工作跟踪数据
   */
  async loadTrackingData() {
    try {
      console.log('开始加载重点工作跟踪数据...');
      
      // 从服务器获取Excel数据
      const response = await fetch('/api/excel-data?sheet=tracking');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('服务器返回的跟踪数据:', result);
      
      if (result.success && result.data) {
        this.data = this.processTrackingData(result.data);
        this.isLoaded = true;
        console.log('处理后的跟踪数据:', this.data);
        return this.data;
      } else {
        throw new Error(result.message || '获取数据失败');
      }
    } catch (error) {
      console.error('加载跟踪数据失败:', error);
      
      // 如果API失败，尝试加载本地模拟数据
      return this.loadMockData();
    }
  }

  /**
   * 处理从Excel解析的原始数据
   */
  processTrackingData(rawData) {
    if (!rawData || !Array.isArray(rawData) || rawData.length === 0) {
      console.warn('原始数据为空或格式不正确');
      return [];
    }

    console.log('原始数据行数:', rawData.length);
    console.log('原始数据示例:', rawData.slice(0, 3));

    // 跳过标题行，从第3行开始处理数据
    const dataRows = rawData.slice(2);
    
    const processedData = dataRows.map((row, index) => {
      // 安全提取值的函数
      const getValue = (value) => {
        if (value === null || value === undefined) return '';
        if (typeof value === 'object') {
          if (value.text !== undefined) return value.text;
          if (value.richText !== undefined) return value.richText;
          if (value.value !== undefined) return value.value;
          return '';
        }
        return String(value);
      };

      // 根据Excel列顺序映射数据
      const workItem = {
        序号: getValue(row[0]),
        重点工作类型: getValue(row[1]),
        相关指标或方向: getValue(row[2]),
        总体目标值: getValue(row[3]),
        '2025年目标': getValue(row[4]),
        '计算方法&2025年举措': getValue(row[5]),
        负责人: getValue(row[6]),
        跟踪频次: getValue(row[7])
      };

      // 动态添加月份字段（从第8列开始）
      const monthColumns = [
        '2月工作计划', '2月完成情况',
        '3月工作计划', '3月完成情况',
        '4月工作计划', '4月完成情况',
        '5月工作计划', '5月完成情况',
        '6月工作计划', '6月完成情况',
        '7月工作计划', '7月完成情况',
        '8月工作计划', '8月完成情况',
        '9月工作计划', '9月完成情况',
        '10月工作计划', '10月完成情况',
        '11月工作计划', '11月完成情况',
        '12月工作计划', '12月完成情况'
      ];

      monthColumns.forEach((column, colIndex) => {
        const dataIndex = 8 + colIndex; // 从第8列开始
        workItem[column] = getValue(row[dataIndex]);
      });

      return workItem;
    }).filter(item => {
      // 过滤空行（序号为空的行）
      return item.序号 && item.序号.toString().trim() !== '';
    });

    console.log('处理后数据行数:', processedData.length);
    console.log('处理后数据示例:', processedData.slice(0, 2));
    
    return processedData;
  }

  /**
   * 加载模拟数据（用于开发和测试）
   */
  loadMockData() {
    console.log('加载模拟重点工作跟踪数据...');
    
    const mockData = [
      {
        序号: 1,
        重点工作类型: '责任状',
        相关指标或方向: '可控费用管控',
        总体目标值: '',
        '2025年目标': '1万元',
        '计算方法&2025年举措': '财务核算）',
        负责人: '产管中心',
        跟踪频次: '月度',
        '2月工作计划': '831.83',
        '2月完成情况': '562',
        '3月工作计划': '预计完成900万',
        '3月完成情况': '',
        '4月工作计划': '',
        '4月完成情况': '',
        '5月工作计划': '',
        '5月完成情况': ''
      },
      {
        序号: 2,
        重点工作类型: '对标2.0',
        相关指标或方向: '数字研发平台',
        总体目标值: '',
        '2025年目标': '应用MBD，实现三维设计',
        '计算方法&2025年举措': '建立研发过程产品数据库，提升研发数字化水平',
        负责人: '赵斌',
        跟踪频次: '季度',
        '2月工作计划': '完成MBD二期调研',
        '2月完成情况': '已完成调研报告',
        '3月工作计划': '完成锥形簧参数化模板',
        '3月完成情况': '',
        '4月工作计划': '',
        '4月完成情况': '',
        '5月工作计划': '',
        '5月完成情况': ''
      },
      {
        序号: 3,
        重点工作类型: '三年规划',
        相关指标或方向: '技术创新能力提升',
        总体目标值: '',
        '2025年目标': '专利申请20项',
        '计算方法&2025年举措': '加强技术研发，提升创新能力',
        负责人: '技术研究部',
        跟踪频次: '月度',
        '2月工作计划': '申请专利3项',
        '2月完成情况': '已申请2项',
        '3月工作计划': '申请专利4项',
        '3月完成情况': '',
        '4月工作计划': '',
        '4月完成情况': '',
        '5月工作计划': '',
        '5月完成情况': ''
      }
    ];

    this.data = mockData;
    this.isLoaded = true;
    return mockData;
  }

  /**
   * 更新数据并同步到Excel
   */
  async updateData(rowIndex, field, value) {
    try {
      console.log(`更新数据: 行${rowIndex}, 字段${field}, 值${value}`);
      
      // 更新本地数据
      if (rowIndex < this.data.length) {
        this.data[rowIndex][field] = value;
      }

      // 同步到服务器
      const response = await fetch('/api/update-tracking-excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          rowIndex,
          field,
          value,
          sheet: 'tracking'
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        console.log('数据同步成功');
        if (this.syncSuccessCallback) {
          this.syncSuccessCallback();
        }
      } else {
        throw new Error(result.message || '同步失败');
      }
    } catch (error) {
      console.error('数据更新失败:', error);
      if (this.syncErrorCallback) {
        this.syncErrorCallback(error);
      }
    }
  }

  /**
   * 获取工作类型列表
   */
  getWorkTypes() {
    if (!this.isLoaded || !this.data) return [];
    
    const types = new Set();
    this.data.forEach(item => {
      if (item.重点工作类型) {
        types.add(item.重点工作类型);
      }
    });
    
    return Array.from(types).sort();
  }

  /**
   * 根据工作类型筛选数据
   */
  getDataByType(workType) {
    if (!this.isLoaded || !this.data) return [];
    
    if (!workType || workType === '全部') {
      return this.data;
    }
    
    return this.data.filter(item => item.重点工作类型 === workType);
  }

  /**
   * 获取数据统计信息
   */
  getDataStats() {
    if (!this.isLoaded || !this.data) {
      return {
        totalItems: 0,
        workTypes: 0,
        completedItems: 0
      };
    }

    const workTypes = this.getWorkTypes();
    const completedItems = this.data.filter(item => {
      // 检查是否有任何月份的完成情况
      for (let month = 2; month <= 12; month++) {
        const completeField = `${month}月完成情况`;
        if (item[completeField] && item[completeField].toString().trim() !== '') {
          return true;
        }
      }
      return false;
    }).length;

    return {
      totalItems: this.data.length,
      workTypes: workTypes.length,
      completedItems
    };
  }

  /**
   * 导出Excel文件
   */
  exportToExcel() {
    if (!this.isLoaded || !this.data) {
      console.error('没有数据可导出');
      return;
    }

    try {
      // 创建工作簿
      const wb = XLSX.utils.book_new();
      
      // 转换数据格式
      const exportData = this.data.map(item => {
        const row = {};
        Object.keys(item).forEach(key => {
          row[key] = item[key] || '';
        });
        return row;
      });

      // 创建工作表
      const ws = XLSX.utils.json_to_sheet(exportData);
      
      // 添加到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '重点工作跟踪');
      
      // 下载文件
      XLSX.writeFile(wb, `重点工作跟踪-${new Date().toISOString().split('T')[0]}.xlsx`);
      
      console.log('Excel文件导出成功');
    } catch (error) {
      console.error('Excel导出失败:', error);
    }
  }

  /**
   * 获取当前数据
   */
  getData() {
    return this.data;
  }

  /**
   * 检查是否已加载数据
   */
  isDataLoaded() {
    return this.isLoaded;
  }
}

// 创建单例实例
const workTrackingService = new WorkTrackingService();

export default workTrackingService; 