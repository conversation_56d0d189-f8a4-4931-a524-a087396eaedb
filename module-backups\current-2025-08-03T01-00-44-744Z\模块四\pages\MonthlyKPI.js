import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../styles/MonthlyKPI.css';
import kpiService from '../services/kpiService';
import KPISelectiveDownloadModal from '../components/KPISelectiveDownloadModal';
import kpiDownloadService from '../services/kpiDownloadService';

const MonthlyKPI = ({ onNavigate }) => {
  // 状态管理
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingCell, setEditingCell] = useState(null);
  const [syncStatus, setSyncStatus] = useState('已同步');
  const [currentMonthPair, setCurrentMonthPair] = useState([]);
  const [lastFailedOperation, setLastFailedOperation] = useState(null);
  const [showSelectiveDownloadModal, setShowSelectiveDownloadModal] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  // 指标筛选相关状态
  const [selectedIndicators, setSelectedIndicators] = useState([]);

  // 数据输入优化相关状态
  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值
  const saveTimeoutRef = useRef(null);
  const pendingSaveRef = useRef(null);

  // 获取当前月份对（上月和当月）
  const getCurrentMonthPair = () => {
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // 1-12
    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;
    return [previousMonth, currentMonth];
  };

  useEffect(() => {
    // 初始化当前月份对
    setCurrentMonthPair(getCurrentMonthPair());

    // 加载数据
    loadData();

    // 设置同步回调
    kpiService.setSyncCallbacks(
      () => setSyncStatus('同步成功'),
      (error) => setSyncStatus('同步失败')
    );

    // 清理函数
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }
    };
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('开始加载KPI数据...');
      const kpiData = await kpiService.loadKPIData();
      console.log('加载的KPI数据:', kpiData);

      if (kpiData && Array.isArray(kpiData)) {
        // 处理数据，添加合并单元格逻辑
        const processedData = processMergedCells(kpiData);
        setData(processedData);
      } else {
        console.error('未获取到有效的KPI数据');
        setData([]);
      }
    } catch (error) {
      console.error('KPI数据加载失败:', error);
      setData([]);
    }
    setLoading(false);
  };

  // 处理合并单元格逻辑
  const processMergedCells = (rawData) => {
    if (!Array.isArray(rawData) || rawData.length === 0) return [];

    const processedData = [...rawData];

    // 按序号和指标进行合并处理
    for (let i = 0; i < processedData.length; i++) {
      const currentRow = processedData[i];
      let mergeCount = 1;

      // 向下查找相同的序号和指标
      for (let j = i + 1; j < processedData.length; j++) {
        const nextRow = processedData[j];
        if (currentRow.序号 === nextRow.序号 && currentRow.指标 === nextRow.指标) {
          mergeCount++;
        } else {
          break;
        }
      }

      // 如果需要合并
      if (mergeCount > 1) {
        // 标记第一行为合并开始
        processedData[i].isMergedStart = true;
        processedData[i].rowSpan = mergeCount;

        // 标记后续行为合并单元格
        for (let k = i + 1; k < i + mergeCount; k++) {
          processedData[k].isMergedCell = true;
        }

        // 跳过已处理的行
        i += mergeCount - 1;
      }
    }

    return processedData;
  };

  // 强制刷新数据
  const forceRefresh = async () => {
    console.log('强制刷新KPI数据...');
    setSyncStatus('刷新中...');
    await loadData();
    setSyncStatus('刷新完成');
  };

  // 应用筛选逻辑
  useEffect(() => {
    applyFilters();
  }, [data, selectedIndicators]);

  const applyFilters = () => {
    let filtered = [...data];

    // 按指标筛选
    if (selectedIndicators.length > 0) {
      filtered = filtered.filter(item =>
        selectedIndicators.includes(item.指标)
      );
    }

    setFilteredData(filtered);
  };

  // 获取所有唯一指标
  const getUniqueIndicators = () => {
    const indicators = new Set();
    data.forEach(item => {
      if (item.指标 && item.指标.trim()) {
        indicators.add(item.指标.trim());
      }
    });
    return Array.from(indicators).sort();
  };



  // 处理指标选择变化
  const handleIndicatorSelectChange = (event) => {
    const value = event.target.value;
    if (value === 'all') {
      setSelectedIndicators([]);
    } else {
      setSelectedIndicators([value]);
    }
  };

  // 月份导航 - 前后切换
  const navigateMonths = (direction) => {
    const [first, second] = currentMonthPair;
    
    if (direction === 'prev' && first > 2) {
      setCurrentMonthPair([first - 1, second - 1]);
    } else if (direction === 'next' && second < 12) {
      setCurrentMonthPair([first + 1, second + 1]);
    }
  };

  // 防抖保存函数
  const debouncedSave = useCallback(async (rowIndex, field, value) => {
    try {
      setSyncStatus('同步中');

      // 取消之前的保存操作
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }

      // 创建新的保存操作
      const controller = new AbortController();
      pendingSaveRef.current = controller;

      // 保存失败操作信息，用于重试
      setLastFailedOperation({ rowIndex, field, value });

      // 调用双向同步
      await kpiService.updateData(rowIndex, field, value);

      // 如果没有被取消，更新状态
      if (!controller.signal.aborted) {
        setSyncStatus('同步成功');
        setLastFailedOperation(null);
        setTimeout(() => setSyncStatus('已同步'), 1000);
        pendingSaveRef.current = null;
      }
    } catch (error) {
      if (!error.name === 'AbortError') {
        console.error('保存失败:', error);
        setSyncStatus('同步失败');
      }
    }
  }, []);

  // 处理输入变化（实时更新UI，延迟保存）
  const handleInputChange = (rowIndex, field, value) => {
    // 立即更新UI显示
    const newData = [...data];
    newData[rowIndex][field] = value;
    setData(newData);

    // 存储临时值
    const key = `${rowIndex}-${field}`;
    setTempValues(prev => ({ ...prev, [key]: value }));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的防抖定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(rowIndex, field, value);
    }, 800); // 800ms防抖延迟
  };

  // 处理失焦保存（立即保存）
  const handleBlurSave = async (rowIndex, field) => {
    const key = `${rowIndex}-${field}`;
    const value = tempValues[key];

    if (value !== undefined) {
      // 清除防抖定时器
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      // 立即保存
      await debouncedSave(rowIndex, field, value);

      // 清除临时值
      setTempValues(prev => {
        const newTemp = { ...prev };
        delete newTemp[key];
        return newTemp;
      });
    }
  };

  // 兼容原有的handleCellEdit接口
  const handleCellEdit = async (rowIndex, field, value) => {
    handleInputChange(rowIndex, field, value);
  };

  // 重试上次失败的操作
  const retryLastOperation = async () => {
    if (!lastFailedOperation) return;

    const { rowIndex, field, value } = lastFailedOperation;
    setSyncStatus('同步中');

    try {
      await kpiService.updateData(rowIndex, field, value);
    } catch (error) {
      console.error('重试失败:', error);
    }
  };

  const startEdit = (rowIndex, field) => {
    setEditingCell(`${rowIndex}-${field}`);
  };

  const finishEdit = () => {
    setEditingCell(null);
  };

  // 处理选择性下载
  const handleSelectiveDownload = async (selectionData) => {
    setDownloadLoading(true);
    try {
      console.log('开始选择性下载:', selectionData);

      const result = await kpiDownloadService.handleSelectiveDownload(selectionData);

      if (result.success) {
        console.log('下载成功:', result.message);
        alert(`下载成功: ${result.message}`);
      } else {
        throw new Error(result.message || '下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败: ' + error.message);
    } finally {
      setDownloadLoading(false);
      setShowSelectiveDownloadModal(false);
    }
  };

  // 打开选择性下载模态框
  const openSelectiveDownloadModal = () => {
    setShowSelectiveDownloadModal(true);
  };

  // 关闭选择性下载模态框
  const closeSelectiveDownloadModal = () => {
    setShowSelectiveDownloadModal(false);
  };



  // 获取月份名称
  const getMonthName = (month) => {
    const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', 
                       '7月', '8月', '9月', '10月', '11月', '12月'];
    return monthNames[month];
  };

  // 渲染表格
  const renderTable = () => {
    if (filteredData.length === 0) {
      return (
        <div className="no-data">
          <p>{data.length === 0 ? '此部分暂无数据' : '当前筛选条件下没有数据'}</p>
          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '10px' }}>
            调试信息: 总数据 {data.length} 条，筛选后 {filteredData.length} 条
          </p>
        </div>
      );
    }

    return (
      <div className="kpi-table-container">
        <table className="kpi-data-table">
          <thead>
            <tr>
              <th className="col-number">序号</th>
              <th className="col-indicator">指标</th>
              <th className="col-target">目标值</th>
              <th className="col-score">分值(30)</th>
              <th className="col-method">统计方式&口径</th>
              <th className="col-standard">考核标准</th>
              {/* 动态月份列 - 根据提示词要求显示连续两个月 */}
              {currentMonthPair.map(month => [
                <th key={`month-${month}`} className="col-month">{getMonthName(month)}份</th>,
                <th key={`completion-${month}`} className="col-completion">{getMonthName(month)}份完成情况</th>,
                <th key={`score-${month}`} className="col-score-month">{getMonthName(month)}得分</th>
              ])}
            </tr>
          </thead>
          <tbody>
            {filteredData.map((row, index) => {
              const isMergedStart = row.isMergedStart;
              const isMergedCell = row.isMergedCell;
              const rowSpan = row.rowSpan || 1;

              return (
                <tr key={index} className={`kpi-data-row ${isMergedCell ? 'merged-row' : ''} ${isMergedStart ? 'merged-start-row' : ''}`}>
                  {/* 序号列 - 支持合并 */}
                  {!isMergedCell && (
                    <td
                      className="kpi-data-cell col-number"
                      rowSpan={isMergedStart ? rowSpan : 1}
                    >
                      <div className="cell-content">
                        {row.序号 || ''}
                      </div>
                    </td>
                  )}

                  {/* 指标列 - 支持合并 */}
                  {!isMergedCell && (
                    <td
                      className="kpi-data-cell col-indicator editable"
                      rowSpan={isMergedStart ? rowSpan : 1}
                    >
                      {renderEditableCell(index, '指标', row.指标 || '')}
                    </td>
                  )}

                  {/* 目标值列 */}
                  <td className="kpi-data-cell col-target editable">
                    {renderEditableCell(index, '目标值', row.目标值 || '')}
                  </td>

                  {/* 分值列 */}
                  <td className="kpi-data-cell col-score editable">
                    {renderEditableCell(index, '分值', row.分值 || '')}
                  </td>

                  {/* 统计方式&口径列 */}
                  <td className="kpi-data-cell col-method editable">
                    {renderEditableCell(index, '统计方式&口径', row['统计方式&口径'] || '')}
                  </td>

                  {/* 考核标准列 */}
                  <td className="kpi-data-cell col-standard editable">
                    {renderEditableCell(index, '考核标准', row.考核标准 || '')}
                  </td>

                  {/* 动态月份列 */}
                  {currentMonthPair.map(month => [
                    // X月份列
                    <td key={`month-${month}-${index}`} className="kpi-data-cell col-month editable">
                      {renderEditableCell(index, `${month}月份`, row[`${month}月份`] || '')}
                    </td>,
                    // X月份完成情况列
                    <td key={`completion-${month}-${index}`} className="kpi-data-cell col-completion editable">
                      {renderEditableCell(index, `${month}月份完成情况`, row[`${month}月份完成情况`] || '')}
                    </td>,
                    // X月得分列
                    <td key={`score-${month}-${index}`} className="kpi-data-cell col-score-month editable">
                      {renderEditableCell(index, `${month}月得分`, row[`${month}月得分`] || '')}
                    </td>
                  ])}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  // 渲染可编辑单元格
  const renderEditableCell = (rowIndex, field, value) => {
    const cellKey = `${rowIndex}-${field}`;
    const isEditing = editingCell === cellKey;

    // 检查是否为只读列（完成情况列）
    const isReadOnlyField = field.includes('完成情况');
    const isEditable = !isReadOnlyField;

    // 处理换行显示
    let contentToRender = String(value || '');
    if (contentToRender.includes('\n')) {
      contentToRender = contentToRender.split('\n').map((line, i, arr) => (
        <React.Fragment key={i}>{line}{i < arr.length - 1 && <br />}</React.Fragment>
      ));
    }

    if (isEditing && isEditable) {
      return (
        <textarea
          value={value}
          onChange={e => handleInputChange(rowIndex, field, e.target.value)}
          onBlur={() => {
            handleBlurSave(rowIndex, field);
            finishEdit();
          }}
          onKeyDown={e => {
            if (e.key === 'Enter' && e.ctrlKey) {
              handleBlurSave(rowIndex, field);
              finishEdit();
            }
            if (e.key === 'Escape') {
              finishEdit();
            }
          }}
          autoFocus
          className="cell-editor"
        />
      );
    }

    // 确定单元格的CSS类名
    let cellClasses = 'cell-content';
    if (isReadOnlyField) {
      cellClasses += ' readonly-cell';
    } else if (isEditable) {
      cellClasses += ' clickable';
    }

    // 确定提示文本
    let titleText = '';
    if (isReadOnlyField) {
      titleText = '该列不可编辑';
    } else if (isEditable) {
      titleText = '点击编辑';
    }

    return (
      <div
        onClick={() => isEditable && startEdit(rowIndex, field)}
        className={cellClasses}
        title={titleText}
        style={isReadOnlyField ? { cursor: 'not-allowed' } : {}}
      >
        {contentToRender || ''}
      </div>
    );
  };



  if (loading) {
    return (
      <div className="kpi-loading">
        <div className="loading-spinner"></div>
        <p>正在加载KPI数据...</p>
      </div>
    );
  }

  return (
    <div className="monthly-kpi">
      {/* 头部区域 */}
      <div className="kpi-header">
        <div className="header-left">
          <button
            className="back-button"
            onClick={() => onNavigate('home')}
            title="返回首页"
          >
            返回首页
          </button>
          <h1 className="page-title">月度重点KPI跟踪仪表板</h1>
        </div>

        <div className="header-right">
          <div className="sync-status">
            <span className={`status-indicator ${syncStatus === '已同步' ? 'success' : 'pending'}`}>
              {syncStatus}
            </span>
          </div>
        </div>
      </div>

      {/* 控制面板区域 */}
      <div className="control-panel">
        <div className="control-left">
          {/* 指标选择 */}
          <div className="indicator-type-section">
            <span className="filter-label">指标类型:</span>
            <div className="indicator-selector-wrapper">
              <select
                className="indicator-type-selector"
                value={selectedIndicators.length === 1 ? selectedIndicators[0] : 'all'}
                onChange={handleIndicatorSelectChange}
              >
                <option value="all">全部指标</option>
                {getUniqueIndicators().map((indicator, index) => (
                  <option key={index} value={indicator}>
                    {indicator}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 当前显示统计 */}
          <div className="current-display-stats">
            <span className="stats-label">当前显示:</span>
            <span className="stats-value">{filteredData.length} 项</span>
          </div>
        </div>

        <div className="control-right">
          {/* 月份导航 */}
          <div className="month-navigation">
            <button
              className="nav-button"
              onClick={() => navigateMonths('prev')}
              disabled={currentMonthPair[0] <= 2}
            >
              ← 上一组月份
            </button>

            <div className="current-months">
              显示月份: {getMonthName(currentMonthPair[0])} + {getMonthName(currentMonthPair[1])}
            </div>

            <button
              className="nav-button"
              onClick={() => navigateMonths('next')}
              disabled={currentMonthPair[1] >= 12}
            >
              下一组月份 →
            </button>
          </div>

          {/* 操作按钮 */}
          <div className="action-buttons">
            <button
              className="refresh-button"
              onClick={forceRefresh}
              title="刷新数据"
            >
              🔄 刷新数据
            </button>
            <button
              className="download-button"
              onClick={openSelectiveDownloadModal}
              title="选择性下载"
            >
              📥 下载
            </button>
          </div>
        </div>
      </div>



      {/* 数据表格 */}
      <div className="kpi-content">
        {renderTable()}
      </div>

      {/* 底部统计 */}
      <div className="kpi-footer">
        <div className="data-stats">
          {selectedIndicators.length > 0
            ? `显示 ${filteredData.length} / ${data.length} 条KPI记录 (已筛选)`
            : `共 ${data.length} 条KPI记录`
          }
        </div>
      </div>

      {/* 选择性下载模态框 */}
      {showSelectiveDownloadModal && (
        <KPISelectiveDownloadModal
          data={data}
          onDownload={handleSelectiveDownload}
          onClose={closeSelectiveDownloadModal}
          loading={downloadLoading}
        />
      )}
    </div>
  );
};

export default MonthlyKPI; 