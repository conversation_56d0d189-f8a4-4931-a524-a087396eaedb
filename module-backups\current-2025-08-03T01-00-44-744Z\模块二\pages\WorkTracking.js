import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../../styles/WorkTracking.css';
import trackingService from '../services/trackingService';
import WorkTrackingDownloadModal from '../components/WorkTrackingDownloadModal';
import workTrackingDownloadService from '../services/workTrackingDownloadService';

const WorkTracking = ({ onNavigate }) => {
  // 状态管理
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingCell, setEditingCell] = useState(null);
  const [syncStatus, setSyncStatus] = useState('已同步');

  // 数据输入优化相关状态
  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值
  const saveTimeoutRef = useRef(null);
  const pendingSaveRef = useRef(null);
  
  // 负责人筛选相关状态
  const [selectedResponsiblePersons, setSelectedResponsiblePersons] = useState([]);
  const [showResponsibleFilter, setShowResponsibleFilter] = useState(false);
  const [filteredData, setFilteredData] = useState([]);
  
  // 展示状态
  const [currentMonthPair, setCurrentMonthPair] = useState(() => {
    // 根据当前月份设置初始显示的月份对
    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12
    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    
    // 找到当前月份在数组中的位置
    let currentIndex = -1;
    months.forEach((month, index) => {
      const monthNum = parseInt(month.replace('月', ''));
      if (monthNum === currentMonth) {
        currentIndex = index;
      }
    });
    
    // 如果找到当前月份，返回包含当前月份的月份对索引
    if (currentIndex >= 0) {
      // 如果当前月份是第一个月（如2月），则显示第一对
      if (currentIndex === 0) return 0;
      // 否则，尽量让当前月份显示在第一个位置
      return Math.max(0, currentIndex - 1);
    }
    
    // 如果当前月份不在范围内，默认显示第一对
    return 0;
  }); // 显示第几对月份
  const [collapsedTypes, setCollapsedTypes] = useState(new Set());
  
  // 选择性下载相关状态
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  
  // 月份配置
  const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  const monthPairs = [];
  for (let i = 0; i < months.length - 1; i++) {
    monthPairs.push([months[i], months[i + 1]]);
  }

  useEffect(() => {
    loadData();

    // 设置同步回调
    trackingService.setSyncCallbacks(
      () => setSyncStatus('同步成功'),
      (error) => setSyncStatus('同步失败')
    );

    // 清理函数
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }
    };
  }, []);

  // 当数据或筛选条件变化时，更新筛选后的数据
  useEffect(() => {
    applyResponsibleFilter();
  }, [data, selectedResponsiblePersons]);
  
  // 确保filteredData有初始值
  useEffect(() => {
    if (data && data.length > 0 && filteredData.length === 0 && selectedResponsiblePersons.length === 0) {
      setFilteredData(data);
    }
  }, [data]);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('开始加载重点工作跟踪数据...');
      const trackingData = await trackingService.loadTrackingData();
      console.log('加载的跟踪数据:', trackingData);
      
      if (trackingData) {
        setData(trackingData);
        console.log('数据设置完成，共', trackingData.length, '项重点工作');
        
        // 获取所有工作类型并设置为默认折叠状态
        const allWorkTypes = new Set(trackingData.map(item => item.重点工作类型).filter(Boolean));
        setCollapsedTypes(allWorkTypes);
      } else {
        console.error('未获取到跟踪数据');
      }
    } catch (error) {
      console.error('数据加载失败:', error);
    }
    setLoading(false);
  };

  // 获取所有工作类型
  const getWorkTypes = () => {
    const types = ['全部', ...new Set(data.map(item => item.重点工作类型).filter(Boolean))];
    return types;
  };

  // 按工作类型分组数据
  const getGroupedData = () => {
    // 使用筛选后的数据而不是原始数据
    const dataToUse = filteredData.length > 0 || selectedResponsiblePersons.length === 0 ? filteredData : data;
    
    if (!dataToUse || dataToUse.length === 0) return {};
    
    const grouped = {};
    dataToUse.forEach(item => {
      const type = item.重点工作类型 || '其他';
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(item);
    });
    
    return grouped;
  };

  // 防抖保存函数
  const debouncedSave = useCallback(async (rowIndex, field, value) => {
    try {
      setSyncStatus('同步中...');

      // 取消之前的保存操作
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }

      // 创建新的保存操作
      const controller = new AbortController();
      pendingSaveRef.current = controller;

      // 调用双向同步
      await trackingService.updateTrackingData(rowIndex, field, value);

      // 如果没有被取消，更新状态
      if (!controller.signal.aborted) {
        setSyncStatus('同步成功');
        setTimeout(() => setSyncStatus('已同步'), 1000);
        pendingSaveRef.current = null;
      }
    } catch (error) {
      if (!error.name === 'AbortError') {
        console.error('保存失败:', error);
        setSyncStatus('同步失败');
      }
    }
  }, []);

  // 处理输入变化（实时更新UI，延迟保存）
  const handleInputChange = (rowIndex, field, value) => {
    // 立即更新UI显示
    const newData = [...data];
    newData[rowIndex][field] = value;
    setData(newData);

    // 存储临时值
    const key = `${rowIndex}-${field}`;
    setTempValues(prev => ({ ...prev, [key]: value }));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的防抖定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(rowIndex, field, value);
    }, 800); // 800ms防抖延迟
  };

  // 处理失焦保存（立即保存）
  const handleBlurSave = async (rowIndex, field) => {
    const key = `${rowIndex}-${field}`;
    const value = tempValues[key];

    if (value !== undefined) {
      // 清除防抖定时器
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      // 立即保存
      await debouncedSave(rowIndex, field, value);

      // 清除临时值
      setTempValues(prev => {
        const newTemp = { ...prev };
        delete newTemp[key];
        return newTemp;
      });
    }
  };

  // 兼容原有的handleCellEdit接口
  const handleCellEdit = async (rowIndex, field, value) => {
    handleInputChange(rowIndex, field, value);
  };

  const startEdit = (rowIndex, field) => {
    setEditingCell(`${rowIndex}-${field}`);
  };

  const finishEdit = () => {
    setEditingCell(null);
  };

  // 切换工作类型展开/折叠
  const toggleTypeCollapse = (type) => {
    const newCollapsed = new Set(collapsedTypes);
    if (newCollapsed.has(type)) {
      newCollapsed.delete(type);
    } else {
      newCollapsed.add(type);
    }
    setCollapsedTypes(newCollapsed);
  };

  // 全部展开/折叠控制
  const toggleAllTypes = () => {
    const allWorkTypes = new Set(data.map(item => item.重点工作类型).filter(Boolean));
    
    // 如果当前全部折叠或部分折叠，则全部展开
    // 如果当前全部展开，则全部折叠
    if (collapsedTypes.size === 0) {
      // 当前全部展开，执行全部折叠
      setCollapsedTypes(allWorkTypes);
    } else {
      // 当前全部折叠或部分折叠，执行全部展开
      setCollapsedTypes(new Set());
    }
  };

  // 获取全部展开/折叠按钮的显示状态
  const getToggleAllStatus = () => {
    const allWorkTypes = new Set(data.map(item => item.重点工作类型).filter(Boolean));
    
    if (collapsedTypes.size === 0) {
      return { text: '全部折叠', icon: '▼' };
    } else if (collapsedTypes.size === allWorkTypes.size) {
      return { text: '全部展开', icon: '▶' };
    } else {
      return { text: '全部展开', icon: '▶' };
    }
  };

  // 计算总工作数（使用筛选后的数据）
  const getTotalWorkCount = () => {
    const dataToUse = filteredData.length > 0 || selectedResponsiblePersons.length === 0 ? filteredData : data;
    return dataToUse ? dataToUse.length : 0;
  };

  // 检测是否为纯数字
  const isPureNumber = (value) => {
    if (value === null || value === undefined || value === '') return false;
    const strValue = String(value).trim();
    return !isNaN(strValue) && !isNaN(parseFloat(strValue)) && isFinite(strValue);
  };

  // 处理Excel公式对象和格式化数据显示
  const formatDisplayValue = (value) => {
    // 处理null, undefined, 空字符串
    if (value === null || value === undefined || value === '') {
      return '';
    }

    // 处理Excel公式对象（[object Object]乱码问题）
    if (typeof value === 'object') {
      // 尝试提取对象中的值
      if (value.hasOwnProperty('v')) {
        value = value.v; // Excel.js格式
      } else if (value.hasOwnProperty('w')) {
        value = value.w; // 另一种Excel格式
      } else if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) {
        value = value.v; // 包含类型信息的格式
      } else if (value.hasOwnProperty('result')) {
        value = value.result; // 公式结果
      } else {
        // 如果无法提取有效值，返回空字符串而不是[object Object]
        return '';
      }
    }

    // 只对纯数字进行格式化
    if (isPureNumber(value)) {
      const numValue = parseFloat(value);
      
      // 检查是否为整数
      if (Number.isInteger(numValue)) {
        return String(numValue); // 整数按整数显示
      } else {
        return numValue.toFixed(2); // 小数保留两位小数
      }
    }

    // 非纯数字内容保持原样
    return String(value);
  };

  // 渲染可编辑单元格
  const renderEditableCell = (value, rowIndex, field, className = '') => {
    const cellKey = `${rowIndex}-${field}`;
    const isEditing = editingCell === cellKey;
    const isEditable = !['序号', '重点工作类型'].includes(field);
    
    // 格式化显示值
    const displayValue = formatDisplayValue(value);

    if (!isEditable) {
      return <td className={`data-cell ${className}`}>{displayValue}</td>;
    }

    return (
      <td 
        className={`data-cell editable ${className}`}
        onClick={() => startEdit(rowIndex, field)}
      >
        {isEditing ? (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => handleInputChange(rowIndex, field, e.target.value)}
            onBlur={() => {
              handleBlurSave(rowIndex, field);
              finishEdit();
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleBlurSave(rowIndex, field);
                finishEdit();
              }
              if (e.key === 'Escape') {
                finishEdit();
              }
            }}
            autoFocus
            className="cell-input"
          />
        ) : (
          <span className="cell-content">{displayValue}</span>
        )}
      </td>
    );
  };

  // 渲染月份列
  const renderMonthColumns = (row, rowIndex) => {
    if (currentMonthPair >= monthPairs.length) return null;
    
    const [month1, month2] = monthPairs[currentMonthPair];
    const month1Plan = `${month1}工作计划`;
    const month1Complete = `${month1}完成情况`;
    const month2Plan = `${month2}工作计划`;
    const month2Complete = `${month2}完成情况`;

    return (
      <>
        {renderEditableCell(row[month1Plan], rowIndex, month1Plan, 'month-plan')}
        {renderEditableCell(row[month1Complete], rowIndex, month1Complete, 'month-complete')}
        {renderEditableCell(row[month2Plan], rowIndex, month2Plan, 'month-plan')}
        {renderEditableCell(row[month2Complete], rowIndex, month2Complete, 'month-complete')}
      </>
    );
  };

  // 渲染数据表格
  const renderDataTable = () => {
    const groupedData = getGroupedData();
    
    if (Object.keys(groupedData).length === 0) {
      return (
        <div className="no-data">
          <p>暂无数据</p>
        </div>
      );
    }

    const currentPair = monthPairs[currentMonthPair] || ['', ''];
    
    return (
      <div className="tracking-table-container">
        {Object.entries(groupedData).map(([type, items]) => {
          const isCollapsed = collapsedTypes.has(type);
          
          return (
            <div key={type} className="work-type-group">
              <div 
                className="group-header"
                onClick={() => toggleTypeCollapse(type)}
              >
                <span className="group-title">
                  <span className="collapse-icon">{isCollapsed ? '▶' : '▼'}</span>
                  {type} (<span className="item-count-highlight">{items.length}项</span>)
                </span>
              </div>
              
              {!isCollapsed && (
                <div className="table-container">
                  <table className="tracking-table">
                    <thead>
                      <tr>
                        <th className="col-number">序号</th>
                        <th className="col-type">重点工作类型</th>
                        <th className="col-indicator">相关指标或方向</th>
                        <th className="col-total-target">总体目标值</th>
                        <th className="col-target">2025年目标</th>
                        <th className="col-method">计算方法&举措</th>
                        <th className="col-responsible">负责人</th>
                        <th className="col-frequency">跟踪频次</th>
                        <th className="col-month-plan">{currentPair[0]}工作计划</th>
                        <th className="col-month-complete">{currentPair[0]}完成情况</th>
                        <th className="col-month-plan">{currentPair[1]}工作计划</th>
                        <th className="col-month-complete">{currentPair[1]}完成情况</th>
                      </tr>
                    </thead>
                    <tbody>
                      {items.map((row, index) => {
                        const globalIndex = data.findIndex(item => item === row);
                        // 每个类型内部序号从1开始重新计算
                        const typeSequenceNumber = index + 1;
                        return (
                          <tr key={globalIndex} className="data-row">
                            {renderEditableCell(typeSequenceNumber, globalIndex, '序号')}
                            {renderEditableCell(row.重点工作类型, globalIndex, '重点工作类型')}
                            {renderEditableCell(row.相关指标或方向, globalIndex, '相关指标或方向')}
                            {renderEditableCell(row.总体目标值, globalIndex, '总体目标值')}
                            {renderEditableCell(row['2025年目标'], globalIndex, '2025年目标')}
                            {renderEditableCell(row['计算方法&2025年举措'], globalIndex, '计算方法&2025年举措')}
                            {renderEditableCell(row.负责人, globalIndex, '负责人')}
                            {renderEditableCell(row.跟踪频次, globalIndex, '跟踪频次')}
                            {renderMonthColumns(row, globalIndex)}
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // 获取所有负责人列表
  const getAllResponsiblePersons = () => {
    if (!data || data.length === 0) return [];
    
    const responsiblePersons = data
      .map(item => item.负责人)
      .filter(person => person && String(person).trim() !== '')
      .reduce((acc, person) => {
        // 处理多个负责人用逗号、括号等分隔的情况
        const persons = String(person).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);
        persons.forEach(p => {
          if (!acc.includes(p)) {
            acc.push(p);
          }
        });
        return acc;
      }, []);
    
    return responsiblePersons.sort();
  };

  // 应用负责人筛选
  const applyResponsibleFilter = () => {
    if (!data || data.length === 0) {
      setFilteredData([]);
      return;
    }

    if (selectedResponsiblePersons.length === 0) {
      // 如果没有选择任何负责人，显示所有数据
      setFilteredData(data);
    } else {
      // 筛选包含选中负责人的数据行
      const filtered = data.filter(item => {
        if (!item.负责人) return false;
        
        // 检查责任人单元格是否包含任何选中的负责人（包含匹配）
        return selectedResponsiblePersons.some(selectedPerson => 
          String(item.负责人).includes(selectedPerson)
        );
      });
      
      setFilteredData(filtered);
    }
  };

  // 切换负责人筛选器显示状态
  const toggleResponsibleFilter = () => {
    setShowResponsibleFilter(!showResponsibleFilter);
  };

  // 处理负责人选择
  const handleResponsiblePersonSelect = (person) => {
    setSelectedResponsiblePersons(prev => {
      if (prev.includes(person)) {
        // 如果已选中，则取消选择
        return prev.filter(p => p !== person);
      } else {
        // 如果未选中，则添加到选择列表
        return [...prev, person];
      }
    });
  };

  // 清除所有筛选条件
  const clearResponsibleFilter = () => {
    setSelectedResponsiblePersons([]);
    setShowResponsibleFilter(false);
  };

  // 处理选择性下载
  const handleDownload = async (selectionData) => {
    setDownloadLoading(true);
    try {
      const result = await workTrackingDownloadService.handleSelectiveDownload(selectionData);
      if (result.success) {
        console.log('下载成功:', result.message);
        alert(result.message);
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败: ' + error.message);
    } finally {
      setDownloadLoading(false);
      setShowDownloadModal(false);
    }
  };

  // 打开下载模态框
  const openDownloadModal = () => {
    setShowDownloadModal(true);
  };

  // 关闭下载模态框
  const closeDownloadModal = () => {
    setShowDownloadModal(false);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>正在加载数据...</p>
      </div>
    );
  }

  return (
    <div className="work-tracking-container">
      {/* 负责人筛选面板 */}
      {showResponsibleFilter && (
        <>
          <div 
            className="modal-backdrop" 
            onClick={() => setShowResponsibleFilter(false)}
            style={{
              position: 'fixed',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: 'rgba(0, 0, 0, 0.7)',
              zIndex: 1999,
              backdropFilter: 'blur(5px)'
            }}
          />
          <div className="responsible-filter-panel">
            <div className="filter-panel-header">
              <h3>选择负责人</h3>
              <button className="close-panel-btn" onClick={() => setShowResponsibleFilter(false)}>×</button>
            </div>
          <div className="filter-panel-content">
            <div className="filter-options">
              <button 
                className={`filter-option ${selectedResponsiblePersons.length === 0 ? 'selected' : ''}`}
                onClick={clearResponsibleFilter}
              >
                全部负责人
              </button>
              {getAllResponsiblePersons().map(person => (
                <button
                  key={person}
                  className={`filter-option ${selectedResponsiblePersons.includes(person) ? 'selected' : ''}`}
                  onClick={() => handleResponsiblePersonSelect(person)}
                >
                  {person.length > 4 ? person.substring(0, 4) : person}
                </button>
              ))}
            </div>
            <div className="filter-actions">
              <button className="clear-filter-btn" onClick={clearResponsibleFilter}>
                清除筛选
              </button>
              <button className="apply-filter-btn" onClick={() => setShowResponsibleFilter(false)}>
                应用筛选
              </button>
            </div>
          </div>
          </div>
        </>
      )}

      {/* 页面头部 - 优化布局 */}
      <div className="page-header">
      <button 
          className="back-btn-top"
        onClick={() => onNavigate('home')}
        style={{ fontSize: '18px' }}
      >
        返回首页
      </button>
        
        <div className="header-center">
          <h1 className="page-title">开发中心重点工作跟踪-填写表</h1>
          <p className="page-subtitle">月度工作计划与完成情况跟踪</p>
        </div>
        
        <div 
          className="sync-status"
          style={{ fontSize: '16px', marginRight: '10px' }}
        >
          <span 
            className={`status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}
            style={{ fontSize: '18px' }}
          >
            {syncStatus}
          </span>
        </div>
      </div>

      {/* 筛选状态提示 - 重新设计 */}
      {selectedResponsiblePersons.length > 0 && (
        <div 
          style={{
            background: 'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',
            border: '1px solid rgba(0, 212, 170, 0.3)',
            borderRadius: '12px',
            margin: '20px auto',
            width: '100%',
            maxWidth: 'none',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 20px rgba(0, 212, 170, 0.2)'
          }}
        >
          <div 
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '16px 24px',
              gap: '16px'
            }}
          >
            <div 
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',
                borderRadius: '50%',
                fontSize: '18px'
              }}
            >
              🔍
            </div>
            <div style={{ flex: 1 }}>
              <div 
                style={{
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.7)',
                  marginBottom: '4px',
                  fontWeight: '500'
                }}
              >
                当前筛选条件
              </div>
              <div 
                style={{
                  fontSize: '16px',
                  color: '#fff',
                  fontWeight: '600'
                }}
              >
                {(() => {
                  const workCount = getTotalWorkCount();
                  if (selectedResponsiblePersons.length === 1) {
                    return (
                      <>
                        负责人：
                        <span style={{ 
                          color: '#20ff4d', 
                          fontWeight: 'bold',
                          textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',
                          margin: '0 8px'
                        }}>
                          {selectedResponsiblePersons[0]}
                        </span>
                        <span style={{ 
                          color: '#00d4aa',
                          fontSize: '14px'
                        }}>
                          （{workCount}项工作）
                        </span>
                      </>
                    );
                  } else {
                    return (
                      <>
                        负责人：
                        <span style={{ 
                          color: '#20ff4d', 
                          fontWeight: 'bold',
                          textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',
                          margin: '0 8px'
                        }}>
                          {selectedResponsiblePersons.join('、')}
                        </span>
                        <span style={{ 
                          color: '#00d4aa',
                          fontSize: '14px'
                        }}>
                          （{workCount}项工作）
                        </span>
                      </>
                    );
                  }
                })()}
              </div>
            </div>
            <button 
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                background: 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',
                border: '1px solid rgba(255, 75, 75, 0.4)',
                borderRadius: '8px',
                color: '#ff6b6b',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onClick={clearResponsibleFilter}
              onMouseEnter={(e) => {
                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';
                e.target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';
                e.target.style.transform = 'translateY(0)';
              }}
              title="清除筛选条件"
            >
              <span>✕</span>
              <span>清除筛选</span>
            </button>
          </div>
        </div>
      )}

      {/* 统一控制面板 - 重新排版布局 */}
      <div className="unified-control-panel-v2">
        {/* 左侧：数据统计 */}
        <div 
          className="stats-section-left"
          style={{ display: 'flex', flexDirection: 'row', gap: '15px', alignItems: 'center' }}
        >
          <div className="stat-card">
            <div className="stat-number">{getTotalWorkCount()}</div>
            <div className="stat-text">总工作项</div>
          </div>
          <div className="stat-card">
            <div className="stat-number">{getWorkTypes().length - 1}</div>
            <div className="stat-text">工作类型</div>
          </div>
          <div className="stat-card toggle-all-card" onClick={toggleAllTypes}>
            <div className="stat-number">
              <span className="toggle-icon">{getToggleAllStatus().icon}</span>
            </div>
            <div className="stat-text">{getToggleAllStatus().text}</div>
          </div>
        </div>

        {/* 中间：负责人筛选按钮 */}
        <div className="filter-section-center">
          <div className="tech-filter-module" onClick={toggleResponsibleFilter}>
            <div className="tech-module-background">
              <div className="circuit-pattern"></div>
              <div className="energy-flow"></div>
              <div className="hologram-border"></div>
            </div>
            <div className="tech-module-content">
              <div 
                className="icon-container"
                style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
              >
                <div 
                  className="tech-icon" 
                  style={{ 
                    fontSize: '0px',
                    width: '68px',
                    height: '68px',
                    position: 'relative',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {/* 科技感多人员筛选图标 */}
                  <svg 
                    width="60" 
                    height="60" 
                    viewBox="0 0 32 32" 
                    fill="none"
                    style={{
                      filter: 'drop-shadow(0 0 15px rgba(0, 212, 170, 1)) drop-shadow(0 0 25px rgba(32, 255, 77, 0.5))'
                    }}
                  >
                    {/* 背景数据网格 */}
                    <g opacity="0.3">
                      <path d="M4 8L28 8M4 16L28 16M4 24L28 24" stroke="url(#gridGradient)" strokeWidth="0.5"/>
                      <path d="M8 4L8 28M16 4L16 28M24 4L24 28" stroke="url(#gridGradient)" strokeWidth="0.5"/>
                    </g>
                    
                    {/* 主要人物 - 中央 */}
                    <g>
                      {/* 头部 */}
                      <circle cx="16" cy="10" r="3.5" stroke="url(#mainGradient)" strokeWidth="2" fill="url(#headFill1)"/>
                      {/* 身体 */}
                      <path d="M9 26V22C9 19.7909 10.7909 18 13 18H19C21.2091 18 23 19.7909 23 22V26" 
                            stroke="url(#mainGradient)" strokeWidth="2" fill="url(#bodyFill1)" strokeLinecap="round"/>
                      {/* 科技光环 */}
                      <circle cx="16" cy="10" r="5" stroke="rgba(0, 212, 170, 0.6)" strokeWidth="1" fill="none">
                        <animate attributeName="r" values="5;6;5" dur="3s" repeatCount="indefinite"/>
                        <animate attributeName="opacity" values="0.6;0.3;0.6" dur="3s" repeatCount="indefinite"/>
                      </circle>
                    </g>
                    
                    {/* 左侧人物 - 较小 */}
                    <g opacity="0.7">
                      <circle cx="7" cy="12" r="2.5" stroke="url(#secondaryGradient)" strokeWidth="1.5" fill="url(#headFill2)"/>
                      <path d="M2 24V21C2 19.3431 3.34315 18 5 18H9C10.6569 18 12 19.3431 12 21V24" 
                            stroke="url(#secondaryGradient)" strokeWidth="1.5" fill="url(#bodyFill2)" strokeLinecap="round"/>
                      {/* 连接线 */}
                      <path d="M9.5 12L13 10" stroke="url(#connectGradient)" strokeWidth="1" strokeDasharray="2,1">
                        <animate attributeName="stroke-dashoffset" values="0;3;0" dur="2s" repeatCount="indefinite"/>
                      </path>
                    </g>
                    
                    {/* 右侧人物 - 较小 */}
                    <g opacity="0.7">
                      <circle cx="25" cy="12" r="2.5" stroke="url(#secondaryGradient)" strokeWidth="1.5" fill="url(#headFill2)"/>
                      <path d="M20 24V21C20 19.3431 21.3431 18 23 18H27C28.6569 18 30 19.3431 30 21V24" 
                            stroke="url(#secondaryGradient)" strokeWidth="1.5" fill="url(#bodyFill2)" strokeLinecap="round"/>
                      {/* 连接线 */}
                      <path d="M22.5 12L19 10" stroke="url(#connectGradient)" strokeWidth="1" strokeDasharray="2,1">
                        <animate attributeName="stroke-dashoffset" values="0;3;0" dur="2s" repeatCount="indefinite"/>
                      </path>
                    </g>
                    
                    {/* 数据节点 */}
                    <g>
                      <circle cx="16" cy="6" r="1" fill="url(#nodeGradient)">
                        <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
                      </circle>
                      <circle cx="7" cy="8" r="0.8" fill="url(#nodeGradient)">
                        <animate attributeName="opacity" values="0.3;1;0.3" dur="1.5s" repeatCount="indefinite"/>
                      </circle>
                      <circle cx="25" cy="8" r="0.8" fill="url(#nodeGradient)">
                        <animate attributeName="opacity" values="1;0.3;1" dur="1.5s" repeatCount="indefinite"/>
                      </circle>
                    </g>
                    
                    {/* 筛选象征 - 扫描线 */}
                    <g opacity="0.8">
                      <line x1="2" y1="15" x2="30" y2="15" stroke="url(#scanGradient)" strokeWidth="1.5">
                        <animate attributeName="opacity" values="0;1;0" dur="2.5s" repeatCount="indefinite"/>
                        <animate attributeName="y1" values="15;17;15" dur="2.5s" repeatCount="indefinite"/>
                        <animate attributeName="y2" values="15;17;15" dur="2.5s" repeatCount="indefinite"/>
                      </line>
                    </g>
                    
                    <defs>
                      <linearGradient id="mainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#00d4aa"/>
                        <stop offset="50%" stopColor="#20ff4d"/>
                        <stop offset="100%" stopColor="#4dd0ff"/>
                      </linearGradient>
                      
                      <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#4dd0ff"/>
                        <stop offset="100%" stopColor="#00d4aa"/>
                      </linearGradient>
                      
                      <linearGradient id="connectGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="rgba(32, 255, 77, 0.8)"/>
                        <stop offset="50%" stopColor="rgba(0, 212, 170, 1)"/>
                        <stop offset="100%" stopColor="rgba(77, 208, 255, 0.8)"/>
                      </linearGradient>
                      
                      <linearGradient id="scanGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" stopColor="transparent"/>
                        <stop offset="20%" stopColor="#20ff4d"/>
                        <stop offset="50%" stopColor="#00d4aa"/>
                        <stop offset="80%" stopColor="#4dd0ff"/>
                        <stop offset="100%" stopColor="transparent"/>
                      </linearGradient>
                      
                      <linearGradient id="gridGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="rgba(0, 212, 170, 0.3)"/>
                        <stop offset="100%" stopColor="rgba(77, 208, 255, 0.3)"/>
                      </linearGradient>
                      
                      <radialGradient id="headFill1" cx="50%" cy="30%" r="80%">
                        <stop offset="0%" stopColor="rgba(32, 255, 77, 0.4)"/>
                        <stop offset="100%" stopColor="rgba(0, 212, 170, 0.1)"/>
                      </radialGradient>
                      
                      <radialGradient id="headFill2" cx="50%" cy="30%" r="80%">
                        <stop offset="0%" stopColor="rgba(77, 208, 255, 0.3)"/>
                        <stop offset="100%" stopColor="rgba(0, 212, 170, 0.1)"/>
                      </radialGradient>
                      
                      <linearGradient id="bodyFill1" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="rgba(0, 212, 170, 0.25)"/>
                        <stop offset="100%" stopColor="rgba(32, 255, 77, 0.1)"/>
                      </linearGradient>
                      
                      <linearGradient id="bodyFill2" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" stopColor="rgba(77, 208, 255, 0.2)"/>
                        <stop offset="100%" stopColor="rgba(0, 212, 170, 0.1)"/>
                      </linearGradient>
                      
                      <radialGradient id="nodeGradient" cx="50%" cy="50%" r="50%">
                        <stop offset="0%" stopColor="#20ff4d"/>
                        <stop offset="100%" stopColor="#00d4aa"/>
                      </radialGradient>
                    </defs>
                  </svg>
                </div>
                <div className="scan-line"></div>
              </div>
              <div 
                className="module-info"
                style={{ textAlign: 'center' }}
              >
                <div className="module-title">责任人筛选</div>
                {selectedResponsiblePersons.length > 0 && (
                  <div className="active-indicator">
                    <span className="pulse-dot"></span>
                  </div>
                )}
              </div>
            </div>
            <div className="tech-accent-lines">
              <div className="accent-line accent-line-1"></div>
              <div className="accent-line accent-line-2"></div>
              <div className="accent-line accent-line-3"></div>
            </div>
          </div>
        </div>

        {/* 右侧：月份导航和操作按钮 */}
        <div className="controls-section">
          <div className="month-navigation-new">
            <button 
              className="nav-btn-new"
              onClick={() => setCurrentMonthPair(Math.max(0, currentMonthPair - 1))}
              disabled={currentMonthPair === 0}
            >
              ◀ 上个月份对
            </button>
            <span className="current-months-new">
              {monthPairs[currentMonthPair] ? 
                `${monthPairs[currentMonthPair][0]} / ${monthPairs[currentMonthPair][1]}` : 
                '月份加载中'
              }
            </span>
            <button 
              className="nav-btn-new"
              onClick={() => setCurrentMonthPair(Math.min(monthPairs.length - 1, currentMonthPair + 1))}
              disabled={currentMonthPair >= monthPairs.length - 1}
            >
              下个月份对 ▶
            </button>
          </div>

          <div className="action-buttons-group">
            <button 
              className="refresh-btn-new"
              onClick={loadData}
            >
              🔄 刷新数据
            </button>
            
            <button 
              className="download-btn-tracking"
              onClick={openDownloadModal}
              disabled={downloadLoading}
            >
              {downloadLoading ? (
                <>
                  <span className="loading-spinner-small"></span>
                  处理中...
                </>
              ) : (
                <>
                  📈 选择性下载
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* 数据表格 */}
      {renderDataTable()}

      {/* 选择性下载模态框 */}
      <WorkTrackingDownloadModal
        isOpen={showDownloadModal}
        onClose={closeDownloadModal}
        data={data}
        filteredData={filteredData}
        selectedResponsiblePersons={selectedResponsiblePersons}
        currentMonthPair={currentMonthPair}
        monthPairs={monthPairs}
        onDownload={handleDownload}
      />
    </div>
  );
};

export default WorkTracking; 