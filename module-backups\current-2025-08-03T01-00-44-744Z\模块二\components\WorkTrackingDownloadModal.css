/* 模块二选择性下载模态框样式 */
.work-tracking-download-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.work-tracking-download-modal {
  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));
  border: 2px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  width: 90%;
  max-width: 900px;
  max-height: 85vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 模态框头部 */
.modal-header {
  background: linear-gradient(90deg, rgba(0, 212, 170, 0.1), rgba(77, 208, 255, 0.1));
  padding: 20px;
  border-bottom: 1px solid rgba(0, 212, 170, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  color: #00d4aa;
  font-size: 1.5rem;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.6);
}

.close-btn {
  background: none;
  border: none;
  color: #ff4757;
  font-size: 1.8rem;
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 71, 87, 0.2);
  transform: scale(1.1);
}

/* 模态框内容 */
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 当前筛选条件显示 */
.current-filters {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.filter-item:last-child {
  margin-bottom: 0;
}

.filter-label {
  color: #00d4aa;
  font-weight: 600;
  min-width: 80px;
}

.filter-value {
  color: #4dd0ff;
  background: rgba(77, 208, 255, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
  border: 1px solid rgba(77, 208, 255, 0.3);
}

.filter-item select {
  background: rgba(15, 15, 35, 0.8);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 6px 10px;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
}

.filter-item select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.include-filter-checkbox {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #e8e8e8;
  cursor: pointer;
  margin-left: 15px;
}

.include-filter-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #00d4aa;
  cursor: pointer;
}

/* 工作类型部分 */
.work-types-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.work-type-section {
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 10px;
  overflow: hidden;
  background: rgba(15, 15, 35, 0.5);
}

.work-type-header {
  background: linear-gradient(90deg, rgba(0, 212, 170, 0.15), rgba(77, 208, 255, 0.15));
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 212, 170, 0.2);
}

.work-type-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 1.1rem;
  font-weight: 600;
}

.work-type-checkbox input[type="checkbox"] {
  margin-right: 12px;
  width: 18px;
  height: 18px;
  accent-color: #00d4aa;
  cursor: pointer;
}

.work-type-title {
  color: #00d4aa;
  text-shadow: 0 0 5px rgba(0, 212, 170, 0.6);
}

.work-type-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(77, 208, 255, 0.2));
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 6px 12px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: linear-gradient(135deg, rgba(0, 212, 170, 0.3), rgba(77, 208, 255, 0.3));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.3);
}

/* 工作项目列表 */
.work-items-list {
  max-height: 250px;
  overflow-y: auto;
}

.work-item-row {
  padding: 12px 20px;
  border-bottom: 1px solid rgba(0, 212, 170, 0.1);
  transition: all 0.3s ease;
}

.work-item-row:hover {
  background: rgba(0, 212, 170, 0.05);
}

.work-item-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
}

.work-item-checkbox input[type="checkbox"] {
  margin-right: 12px;
  width: 16px;
  height: 16px;
  accent-color: #00d4aa;
  cursor: pointer;
}

.work-item-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
}

.work-item-name {
  color: #e8e8e8;
  font-size: 0.95rem;
  font-weight: 500;
}

.work-item-responsible {
  color: #4dd0ff;
  font-size: 0.85rem;
  background: rgba(77, 208, 255, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(77, 208, 255, 0.3);
  align-self: flex-start;
}

/* 模态框底部 */
.modal-footer {
  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));
  padding: 20px;
  border-top: 1px solid rgba(0, 212, 170, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.statistics {
  display: flex;
  gap: 15px;
  color: #4dd0ff;
  font-weight: 600;
  flex-wrap: wrap;
}

.statistics span {
  background: rgba(77, 208, 255, 0.1);
  padding: 8px 15px;
  border-radius: 20px;
  border: 1px solid rgba(77, 208, 255, 0.3);
  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);
  font-size: 0.9rem;
}

.format-selection {
  color: #e8e8e8;
}

.format-selection label {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.format-selection select {
  background: rgba(15, 15, 35, 0.8);
  border: 1px solid rgba(0, 212, 170, 0.3);
  color: #00d4aa;
  padding: 8px 12px;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
}

.format-selection select:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

.action-buttons {
  display: flex;
  gap: 15px;
}

.cancel-btn, .download-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cancel-btn {
  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.5);
}

.cancel-btn:hover {
  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);
}

.download-btn {
  background: linear-gradient(135deg, #00d4aa, #4dd0ff);
  color: #000;
  border: 1px solid transparent;
}

.download-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);
}

.download-btn:disabled {
  background: rgba(128, 128, 128, 0.3);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 滚动条样式 */
.modal-content::-webkit-scrollbar,
.work-items-list::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track,
.work-items-list::-webkit-scrollbar-track {
  background: rgba(15, 15, 35, 0.5);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb,
.work-items-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #00d4aa, #4dd0ff);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.work-items-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #4dd0ff, #00d4aa);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .work-tracking-download-modal {
    width: 95%;
    max-height: 90vh;
  }
  
  .modal-header {
    padding: 15px;
  }
  
  .modal-header h2 {
    font-size: 1.3rem;
  }
  
  .modal-content {
    padding: 15px;
  }
  
  .work-type-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: stretch;
  }
  
  .statistics {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .format-selection {
    text-align: center;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
} 