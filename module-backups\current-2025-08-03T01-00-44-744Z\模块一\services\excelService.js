import * as XLSX from 'xlsx';

class ExcelService {
  constructor() {
    this.workbook = null;
    this.data = {};
  }

  // 加载Excel文件
  async loadExcel() {
    try {
      // 首先尝试从后端API加载真实数据
      console.log('正在从API加载Excel数据...');
      const response = await fetch('http://localhost:3001/api/excel-data');
      
      if (response.ok) {
        const result = await response.json();
        if (result.data && result.data.keyWork && result.data.keyWork.length > 0) {
          // 只有当API返回的数据包含完整的keyWork数据时才使用
          this.data = result.data;
          this.lastModified = new Date(result.lastModified);
          console.log('从Excel文件成功加载数据:', this.data);
          return this.data;
        } else {
          console.warn('API返回的数据不完整，缺少重点工作数据');
        }
      } else {
        console.warn('API响应失败，状态码:', response.status);
      }
    } catch (error) {
      console.error('从API加载Excel数据失败:', error);
    }

    // 如果API失败或数据不完整，使用本地静态数据
    console.log('使用本地静态数据');
    this.data = this.getExactExcelData();
    return this.data;
  }

  // 解析工作目标管理责任书数据
  parseWorkTargetData() {
    const sheetName = '3.开发中心';
    const sheet = this.workbook.Sheets[sheetName];
    
    if (!sheet) {
      console.error('找不到工作表:', sheetName);
      this.data = this.getExactExcelData();
      return;
    }

    // 如果解析失败，直接使用准确的Excel数据
    this.data = this.getExactExcelData();
  }

  // 获取完全按照Excel内容的准确数据（包含正确的合并单元格结构）
  getExactExcelData() {
    return {
      keyIndicators: [
        {
          序号: 1,
          指标: '产品线毛利率',
          目标值: '橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',
          权重: 6,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '归口指标',
          责任人: '归口指标'
        },
        {
          序号: 2,
          指标: '开发降本（万元）',
          目标值: '≥5000',
          权重: 8,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '责任状指标',
          责任人: ''
        },
        {
          序号: 3,
          指标: '工艺模块及时率（橡胶金属、空气弹簧、系统件等）',
          目标值: '满足模块条件产品转批及时率≥95%',
          权重: 3,
          考核标准: '完成目标得满分，未完成按比例得分。制造中心计划供应部提供数据作为考核',
          指标分类: '世界一流指标',
          责任人: '丁间浩'
        },
        {
          序号: 4,
          指标: '研发可控费用管控',
          目标值: '≤5501万元',
          权重: 3,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '归口指标',
          责任人: '归口指标'
        },
        {
          序号: 5,
          指标: '新产品开发一次命中率',
          目标值: '≥90%',
          权重: 2,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '世界一流指标',
          责任人: '产管中心数据'
        },
        {
          序号: 6,
          指标: '推荐选型数量（个）',
          目标值: '≥90',
          权重: 2,
          考核标准: '以项目激励为准，完成目标得满分，未完成按比例得分。',
          指标分类: '世界一流指标',
          责任人: '产管中心数据'
        },
        {
          序号: 7,
          指标: '新产品开发周期缩短',
          目标值: '≥15%',
          权重: 2,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '世界一流指标',
          责任人: '产管中心'
        },
        // 归口管理指标 - 完全按照Excel结构
        { // 第1行 "归口管理指标"
          序号: 8,
          指标: '归口管理指标',
          目标值: '专利指标，专项下达', // Excel中这一行的目标值
          权重: 2,
          考核标准: '完成目标得满分，未完成按比例得分。',
          指标分类: '归口指标',
          责任人: '归口指标',
          rowSpan: 6, // "序号"和"指标"列向下合并6行
          isMergedStart: true
        },
        { // 第2行 "归口管理指标"
          序号: 8, // 逻辑上合并，渲染时跳过
          指标: '归口管理指标', // 逻辑上合并，渲染时跳过
          目标值: '部门编制（专项下达）', // Excel中这一行的目标值
          权重: 3,
          考核标准: '完成目标得满分，未完成按比例得分。', // 注意：Excel中此处为"満"
          指标分类: '归口指标',
          责任人: '归口指标',
          isMergedCell: true
        },
        { // 第3行 "归口管理指标"
          序号: 8,指标: '归口管理指标',目标值: '精益善人均≥2条',权重: 1,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true
        },
        { // 第4行 "归口管理指标"
          序号: 8,指标: '归口管理指标',目标值: '落实"多位一体"大监管机制要求，开展管理监管≥1项',权重: 2,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true
        },
        { // 第5行 "归口管理指标"
          序号: 8,指标: '归口管理指标',目标值: '巡视整改完成率按要求进度完成率 100%',权重: 1,考核标准: '完成目标得满分，未完成目标按整改情况扣分。',指标分类: '归口指标',责任人: '',isMergedCell: true
        },
        { // 第6行 "归口管理指标"
          序号: 8,指标: '归口管理指标',目标值: '全面预算管控：\n1.可控费用控制在5501万内；（3分）\n2.全面预算工作推进及部门预算管理优化；（2分）',权重: 5,考核标准: '完成目标得满分，未完成按比例得分。',指标分类: '归口指标',责任人: '归口指标',isMergedCell: true
        }
      ],
      qualityIndicators: [
        // 根据Excel，质量指标部分暂无数据
      ],
      keyWork: [
        // 第1行：合并单元格的起始行（序号1）
        {
          序号: 1,
          指标: '新产品、新平台、新技术开发',
          目标值: '车轮降噪头：实现批量交付。\n贝通道：具备贝通道产品批量生产和质量管控能力，获得一家客户贝通道供货资质。\n低感回收再制造技术的研制：①完成一种复合体件回收技术及其工艺体系的搭建（回收成本在金属采购价格25%以下）；②通过至少2种产品的批量化应用建立回收类型数据库。',
          权重: '', // 这一行权重为空
          考核标准: '', // 这一行考核标准为空  
          指标分类: '', // 这一行指标分类为空
          责任人: '开发中心-技术与发展研究室',
          rowSpan: 2, // "序号"和"指标"列向下合并2行
          isMergedStart: true
        },
        // 第2行：合并单元格的第二行
        {
          序号: 1, // 逻辑上合并，渲染时跳过
          指标: '新产品、新平台、新技术开发', // 逻辑上合并，渲染时跳过
          目标值: '新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。\n橡胶制料：',
          权重: '', // 这一行权重为空
          考核标准: '', // 这一行考核标准为空
          指标分类: '', // 这一行指标分类为空
          责任人: '开发中心-车体新材料',
          isMergedCell: true
        },
        {
          序号: 2,
          指标: '新产品、新平台、新技术开发',
          目标值: '新技术研发：具备高压电驱动连接产品批量生产和质量管控能力。',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-技术与发展研究室'
        },
        {
          序号: 3,
          指标: '新产品、新平台、新技术开发',
          目标值: '车体新材料：\n1.碳纤维复合材料产品完成自主能力建设及产业初步建立\n2.完成复合材料货车材料验证及方案设计，产品研制及装车。',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-车体新材料'
        },
        {
          序号: 4,
          指标: '新产品、新平台、新技术开发',
          目标值: 'TPE车轮缓冲器具备批量自主能力，获得三家以上客户批量订单。',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-系统部'
        },
        {
          序号: 5,
          指标: '新产品、新平台、新技术开发',
          目标值: '橡胶件产品有效满足12年或者15%不到年限：\n完成满法规的稳定性验证（完成次级验证试验，目试验结果明当）、固化满法规的新高工艺参数。',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-橡胶件总属部'
        },
        {
          序号: 6,
          指标: '新产品、新平台、新技术开发',
          目标值: 'EN45545 H3阻燃橡胶件研究：\n1.完成沙箱高HL3阻燃胶料产品的型式试验验证\n2.完成H3双层级雅形消管的工艺研究及型式试验验证',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-橡胶件总属部'
        },
        {
          序号: 7,
          指标: '新产品、新平台、新技术开发',
          目标值: '仿真技术：①采用公司高端管的超弹性平台，形成自主、快速、标准的仿真数据试与性能验证技术体系；②基于uniqu软件进行二次开发，完成一类典型橡胶减振产品自动网格划分和处理功能且具有可操作性的软件界面。',
          权重: '', // 权重为空
          考核标准: '', // 考核标准为空
          指标分类: '', // 指标分类为空
          责任人: '开发中心-仿真研究部'
        }
      ]
    };
  }

  // 获取指定部分的数据
  getSectionData(section) {
    return this.data[section] || [];
  }

  // 更新数据 - 实现双向同步
  async updateData(section, index, field, value) {
    if (this.data[section] && this.data[section][index]) {
      // 更新内存中的数据
      this.data[section][index][field] = value;
      console.log('数据已更新:', { section, index, field, value });
      
      // 同步到Excel文件 - 使用新的单字段更新API
      await this.syncToExcel(section, index, field, value);
    }
  }

  // 同步数据到Excel文件 - 双向同步机制（参考模块二的逻辑）
  async syncToExcel(section, index, field, value) {
    try {
      console.log('正在同步工作目标数据到Excel:', { section, index, field, value });
      
      // 调用新的单字段更新API
      const response = await fetch('http://localhost:3001/api/update-worktarget-excel', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          section,
          rowIndex: index,
          field,
          value
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('工作目标数据同步成功');
          this.onSyncSuccess && this.onSyncSuccess();
          return true;
        } else {
          console.error('工作目标数据同步失败:', result.message);
          this.onSyncError && this.onSyncError(result.message);
          return false;
        }
      } else {
        console.error('工作目标数据同步请求失败，状态码:', response.status);
        this.onSyncError && this.onSyncError(`HTTP ${response.status}`);
        return false;
      }
    } catch (error) {
      console.error('工作目标数据同步错误:', error);
      this.onSyncError && this.onSyncError(error.message);
      return false;
    }
  }

  // 将内存数据转换为Excel格式
  convertDataForExcel(section) {
    const sectionData = this.data[section] || [];
    return sectionData.map((row, index) => ({
      ...row,
      // 添加详情字段用于处理合并单元格
      序号_详情: row.isMergedStart ? row.序号 : (row.isMergedCell ? '' : row.序号),
      指标_详情: row.isMergedStart ? row.指标 : (row.isMergedCell ? '' : row.指标),
      目标值_详情: row.目标值,
      权重_详情: row.权重,
      考核标准_详情: row.考核标准,
      指标分类_详情: row.指标分类,
      责任人_详情: row.责任人,
      // 保留合并信息
      isMergedStart_序号: row.isMergedStart && (row.rowSpan > 1),
      rowSpan_序号: row.rowSpan || 1,
      isMergedStart_指标: row.isMergedStart && (row.rowSpan > 1),
      rowSpan_指标: row.rowSpan || 1
    }));
  }

  // 设置同步回调
  setSyncCallbacks(onSuccess, onError) {
    this.onSyncSuccess = onSuccess;
    this.onSyncError = onError;
  }

  // 获取数据统计信息
  getDataStats() {
    const getWeight = (item) => {
      const weight = item.权重;
      return typeof weight === 'number' ? weight : (weight ? Number(weight) || 0 : 0);
    };

    return {
      keyIndicators: this.data.keyIndicators?.length || 0,
      qualityIndicators: this.data.qualityIndicators?.length || 0,
      keyWork: this.data.keyWork?.length || 0,
      totalWeight: {
        keyIndicators: this.data.keyIndicators?.reduce((sum, item) => sum + getWeight(item), 0) || 0,
        qualityIndicators: this.data.qualityIndicators?.reduce((sum, item) => sum + getWeight(item), 0) || 0,
        keyWork: this.data.keyWork?.reduce((sum, item) => sum + getWeight(item), 0) || 0
      }
    };
  }
}

export default new ExcelService(); 