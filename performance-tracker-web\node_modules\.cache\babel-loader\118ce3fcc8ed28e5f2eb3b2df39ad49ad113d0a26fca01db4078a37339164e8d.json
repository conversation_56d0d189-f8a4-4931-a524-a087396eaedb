{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'../styles/WorldClass.css';import worldClassService from'../services/worldClassService';import worldClassDownloadService from'../services/worldClassDownloadService';import WorldClassDownloadModal from'../components/WorldClassDownloadModal';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const WorldClass=_ref=>{let{onNavigate}=_ref;const[data,setData]=useState([]);const[filteredData,setFilteredData]=useState([]);const[loading,setLoading]=useState(true);const[editingCell,setEditingCell]=useState(null);const[syncStatus,setSyncStatus]=useState('已同步');// 数据输入优化相关状态\nconst[tempValues,setTempValues]=useState({});// 临时存储编辑中的值\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);// 层级筛选状态\nconst[levelFilter,setLevelFilter]=useState({level:'一级',value:'全部'});// 负责人筛选相关状态（复用模块二功能）\nconst[selectedResponsiblePersons,setSelectedResponsiblePersons]=useState([]);const[tempSelectedPersons,setTempSelectedPersons]=useState([]);// 临时存储选择的负责人\nconst[showResponsibleFilter,setShowResponsibleFilter]=useState(false);// 月份切换状态 - 从2月开始按月递增，参考模块二的逻辑设置初始显示月份\nconst[currentMonthPair,setCurrentMonthPair]=useState(()=>{// 根据当前月份设置初始显示的月份对\nconst currentMonth=new Date().getMonth()+1;// getMonth()返回0-11，加1得到1-12\nconst months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];// 找到当前月份在数组中的位置\nlet currentIndex=-1;months.forEach((month,index)=>{const monthNum=parseInt(month.replace('月',''));if(monthNum===currentMonth){currentIndex=index;}});// 如果找到当前月份，返回包含当前月份的月份对索引\nif(currentIndex>=0){// 如果当前月份是第一个月（如2月），则显示第一对\nif(currentIndex===0)return 0;// 否则，尽量让当前月份显示在第一个位置\nreturn Math.max(0,currentIndex-1);}// 如果当前月份不在范围内，默认显示第一对\nreturn 0;});const monthPairs=[['2月','3月'],['3月','4月'],['4月','5月'],['5月','6月'],['6月','7月'],['7月','8月'],['8月','9月'],['9月','10月'],['10月','11月'],['11月','12月']];// 下载相关状态\nconst[showDownloadModal,setShowDownloadModal]=useState(false);const[downloadLoading,setDownloadLoading]=useState(false);useEffect(()=>{loadData();// 设置同步回调\nworldClassService.setSyncCallbacks(()=>setSyncStatus('同步成功'),error=>setSyncStatus('同步失败'));// 清理函数\nreturn()=>{if(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}if(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}};},[]);useEffect(()=>{// 应用层级筛选和负责人筛选\napplyFilters();},[data,levelFilter,selectedResponsiblePersons]);const loadData=async()=>{setLoading(true);try{console.log('开始加载对标世界一流数据...');const worldClassData=await worldClassService.loadWorldClassData();console.log('加载的数据:',worldClassData);if(worldClassData){setData(worldClassData);console.log('数据设置完成，总共:',worldClassData.length,'项');console.log('前3条数据预览:',worldClassData.slice(0,3).map(item=>{var _item$2025年目标;return{工作准则:item.工作准则,负责人:item.负责人,'2025年目标':((_item$2025年目标=item['2025年目标'])===null||_item$2025年目标===void 0?void 0:_item$2025年目标.substring(0,50))+'...'};}));}else{console.error('未获取到数据');}}catch(error){console.error('数据加载失败:',error);}setLoading(false);};// 强制刷新数据的方法（复用模块一的设计）\nconst forceRefresh=async()=>{console.log('强制刷新数据...');setSyncStatus('刷新中...');await loadData();setSyncStatus('刷新完成');};// 获取层级选项\nconst getLevelOptions=()=>{if(!data||data.length===0)return[];const field=levelFilter.level==='一级'?'相关指标或方向（一级）':levelFilter.level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';const options=[...new Set(data.map(item=>item[field]).filter(val=>val&&String(val).trim()!=='').map(val=>String(val)))];return['全部',...options];};// 获取所有负责人列表（基于当前显示的数据而不是全部数据）\nconst getAllResponsiblePersons=()=>{// 使用当前筛选后的数据作为数据源\nconst sourceData=filteredData.length>0?filteredData:data;if(!sourceData||sourceData.length===0)return[];const responsiblePersons=sourceData.map(item=>item.负责人).filter(person=>person&&String(person).trim()!=='').reduce((acc,person)=>{// 处理多个负责人用逗号、括号等分隔的情况\nconst persons=String(person).split(/[,，;；、（）()]/).map(p=>p.trim()).filter(p=>p);persons.forEach(p=>{if(!acc.includes(p)){acc.push(p);}});return acc;},[]);return responsiblePersons.sort();};// 应用综合筛选（层级 + 负责人）\nconst applyFilters=()=>{if(!data||data.length===0){setFilteredData([]);return;}let filtered=[...data];// 先应用层级筛选\nif(levelFilter.value!=='全部'){const field=levelFilter.level==='一级'?'相关指标或方向（一级）':levelFilter.level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';filtered=filtered.filter(item=>String(item[field]||'').trim()===levelFilter.value);}// 再应用负责人筛选 - 基于已经层级筛选后的数据\nif(selectedResponsiblePersons.length>0){filtered=filtered.filter(item=>{if(!item.负责人)return false;// 将单元格中的负责人字符串拆分为数组，并进行清理\nconst itemPersons=String(item.负责人).split(/[,，;；、（）()]/).map(p=>p.trim()).filter(p=>p);// 检查是否有任何一个选中的负责人存在于单元格的负责人数组中（精确匹配）\nreturn selectedResponsiblePersons.some(selectedPerson=>itemPersons.includes(selectedPerson));});}setFilteredData(filtered);};// 负责人筛选相关函数\nconst toggleResponsibleFilter=()=>{if(!showResponsibleFilter){// 打开面板时，用当前已应用的筛选初始化临时选择\nsetTempSelectedPersons([...selectedResponsiblePersons]);}setShowResponsibleFilter(!showResponsibleFilter);};const handleResponsiblePersonSelect=person=>{// 只更新临时选择的负责人状态\nsetTempSelectedPersons(prev=>{if(prev.includes(person)){return prev.filter(p=>p!==person);}else{return[...prev,person];}});};const handleClearPanelSelection=()=>{// 仅清除面板内的临时选择，不关闭面板，以便用户重新选择\nsetTempSelectedPersons([]);};const applyResponsibleFilter=()=>{// 应用筛选，将临时选择更新到正式的筛选状态\nsetSelectedResponsiblePersons([...tempSelectedPersons]);setShowResponsibleFilter(false);};const clearResponsibleFilter=()=>{// 清除临时选择和已应用的筛选\nsetTempSelectedPersons([]);setSelectedResponsiblePersons([]);setShowResponsibleFilter(false);};// 月份切换功能（参考模块二设计）\nconst navigateMonth=direction=>{if(direction==='prev'&&currentMonthPair>0){setCurrentMonthPair(currentMonthPair-1);}else if(direction==='next'&&currentMonthPair<monthPairs.length-1){setCurrentMonthPair(currentMonthPair+1);}};// 防抖保存函数\nconst debouncedSave=useCallback(async(rowIndex,field,value)=>{try{setSyncStatus('同步中...');// 取消之前的保存操作\nif(pendingSaveRef.current){var _pendingSaveRef$curre3,_pendingSaveRef$curre4;(_pendingSaveRef$curre3=(_pendingSaveRef$curre4=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre3===void 0?void 0:_pendingSaveRef$curre3.call(_pendingSaveRef$curre4);}// 创建新的保存操作\nconst controller=new AbortController();pendingSaveRef.current=controller;// 调用双向同步\nawait worldClassService.updateData(rowIndex,field,value);// 如果没有被取消，更新状态\nif(!controller.signal.aborted){setSyncStatus('同步成功');setTimeout(()=>setSyncStatus('已同步'),1000);pendingSaveRef.current=null;}}catch(error){if(!error.name==='AbortError'){console.error('保存失败:',error);setSyncStatus('同步失败');}}},[]);// 处理输入变化（实时更新UI，延迟保存）\nconst handleInputChange=(rowIndex,field,value)=>{// 立即更新UI显示\nconst newData=[...data];newData[rowIndex][field]=value;setData(newData);// 存储临时值\nconst key=\"\".concat(rowIndex,\"-\").concat(field);setTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));// 清除之前的定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的防抖定时器\nsaveTimeoutRef.current=setTimeout(()=>{debouncedSave(rowIndex,field,value);},800);// 800ms防抖延迟\n};// 处理失焦保存（立即保存）\nconst handleBlurSave=async(rowIndex,field)=>{const key=\"\".concat(rowIndex,\"-\").concat(field);const value=tempValues[key];if(value!==undefined){// 清除防抖定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);saveTimeoutRef.current=null;}// 立即保存\nawait debouncedSave(rowIndex,field,value);// 清除临时值\nsetTempValues(prev=>{const newTemp=_objectSpread({},prev);delete newTemp[key];return newTemp;});}};// 兼容原有的handleCellEdit接口\nconst handleCellEdit=async(rowIndex,field,value)=>{handleInputChange(rowIndex,field,value);};const startEdit=(rowIndex,field)=>{setEditingCell(\"\".concat(rowIndex,\"-\").concat(field));};const finishEdit=()=>{setEditingCell(null);};// 格式化显示值\nconst formatDisplayValue=value=>{if(value===null||value===undefined||value==='')return'';if(typeof value==='object'){if(value.hasOwnProperty('v'))return value.v;if(value.hasOwnProperty('w'))return value.w;if(value.hasOwnProperty('t')&&value.hasOwnProperty('v'))return value.v;if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);};// 渲染可编辑单元格\nconst renderEditableCell=function(value,rowIndex,field){let className=arguments.length>3&&arguments[3]!==undefined?arguments[3]:'';const cellKey=\"\".concat(rowIndex,\"-\").concat(field);const isEditing=editingCell===cellKey;const displayValue=formatDisplayValue(value);// 检查是否为只读列（完成情况列）\nconst isReadOnlyField=field.includes('完成情况');const isEditable=!['序号'].includes(field)&&!isReadOnlyField;if(isEditing&&isEditable){return/*#__PURE__*/_jsx(\"textarea\",{value:displayValue,onChange:e=>handleInputChange(rowIndex,field,e.target.value),onBlur:()=>{handleBlurSave(rowIndex,field);finishEdit();},onKeyDown:e=>{if(e.key==='Enter'&&e.ctrlKey){handleBlurSave(rowIndex,field);finishEdit();}if(e.key==='Escape'){finishEdit();}},className:\"cell-input\",rows:displayValue.includes('\\n')?displayValue.split('\\n').length+1:2,autoFocus:true});}// 确定单元格的CSS类名\nlet cellClasses=\"cell-content \".concat(className);if(isReadOnlyField){cellClasses+=' readonly-cell';}else if(isEditable){cellClasses+=' editable-cell';}// 确定提示文本\nlet titleText='';if(isReadOnlyField){titleText='该列不可编辑';}else if(isEditable){titleText='点击编辑';}return/*#__PURE__*/_jsx(\"span\",{className:cellClasses,onClick:()=>isEditable&&startEdit(rowIndex,field),title:titleText,style:isReadOnlyField?{cursor:'not-allowed'}:{},children:displayValue.includes('\\n')?displayValue.split('\\n').map((line,i,arr)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[line,i<arr.length-1&&/*#__PURE__*/_jsx(\"br\",{})]},i)):displayValue});};// 渲染月份列\nconst renderMonthColumns=(row,rowIndex)=>{const[month1,month2]=monthPairs[currentMonthPair];return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-plan\",children:renderEditableCell(row[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")],rowIndex,\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),'month-plan')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-complete\",children:renderEditableCell(row[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")],rowIndex,\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"),'month-complete')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-plan\",children:renderEditableCell(row[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")],rowIndex,\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),'month-plan')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-complete\",children:renderEditableCell(row[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")],rowIndex,\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"),'month-complete')})]});};// 下载功能（复用模块一的设计）\nconst handleDownload=async selectionData=>{setDownloadLoading(true);try{console.log('开始下载对标世界一流数据:',selectionData);// 调用下载服务处理实际下载\nconst result=await worldClassDownloadService.handleSelectiveDownload(selectionData);if(result.success){console.log('下载成功:',result.message);alert(\"\\u4E0B\\u8F7D\\u6210\\u529F: \".concat(result.message));}else{throw new Error(result.message||'下载失败');}}catch(error){console.error('下载失败:',error);alert('下载失败: '+error.message);}finally{setDownloadLoading(false);setShowDownloadModal(false);}};const openDownloadModal=()=>{setShowDownloadModal(true);};const closeDownloadModal=()=>{setShowDownloadModal(false);};// 渲染负责人筛选面板\nconst renderResponsibleFilterPanel=()=>{if(!showResponsibleFilter)return null;const allPersons=getAllResponsiblePersons();return/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-responsible-filter-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-filter-panel-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u9009\\u62E9\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsx(\"button\",{onClick:toggleResponsibleFilter,className:\"world-class-close-panel-btn\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-filter-panel-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"world-class-filter-options\",children:allPersons.map(person=>/*#__PURE__*/_jsx(\"div\",{className:\"world-class-filter-option \".concat(tempSelectedPersons.includes(person)?'selected':''),onClick:()=>handleResponsiblePersonSelect(person),children:person},person))}),/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-filter-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:handleClearPanelSelection,className:\"world-class-clear-filter-btn\",children:\"\\u6E05\\u9664\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(\"button\",{onClick:applyResponsibleFilter,className:\"world-class-apply-filter-btn\",children:\"\\u5E94\\u7528\\u7B5B\\u9009\"})]})]})]});};// 加载状态\nif(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"world-class-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u52A0\\u8F7D\\u5BF9\\u6807\\u4E16\\u754C\\u4E00\\u6D41\\u6570\\u636E...\"})]})});}const currentData=filteredData.length>0?filteredData:data;const[month1,month2]=monthPairs[currentMonthPair];// 调试信息\nconsole.log('渲染数据状态:',{原始数据条数:data.length,过滤数据条数:filteredData.length,当前显示条数:currentData.length,当前月份对:\"\".concat(month1,\"-\").concat(month2)});return/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-container\",children:[renderResponsibleFilterPanel(),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{background:'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',border:'1px solid rgba(0, 212, 170, 0.3)',borderRadius:'12px',margin:'20px auto',width:'100%',maxWidth:'none',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 212, 170, 0.2)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',padding:'16px 24px',gap:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',background:'linear-gradient(45deg, #00d4aa, #20ff4d)',borderRadius:'50%',fontSize:'18px'},children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'rgba(255, 255, 255, 0.7)',marginBottom:'4px',fontWeight:'500'},children:\"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'16px',color:'#fff',fontWeight:'600'},children:[\"\\u8D1F\\u8D23\\u4EBA\\uFF1A\",/*#__PURE__*/_jsx(\"span\",{style:{color:'#20ff4d',fontWeight:'bold',textShadow:'0 0 10px rgba(32, 255, 77, 0.6)',margin:'0 8px'},children:selectedResponsiblePersons.join('、')}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#00d4aa',fontSize:'14px'},children:[\"\\uFF08\",filteredData.length,\"\\u9879\\u5DE5\\u4F5C\\uFF09\"]})]})]}),/*#__PURE__*/_jsxs(\"button\",{style:{display:'flex',alignItems:'center',gap:'8px',padding:'8px 16px',background:'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',border:'1px solid rgba(255, 75, 75, 0.4)',borderRadius:'8px',color:'#ff6b6b',fontSize:'14px',fontWeight:'500',cursor:'pointer',transition:'all 0.3s ease'},onClick:clearResponsibleFilter,onMouseEnter:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';e.target.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';e.target.style.transform='translateY(0)';},title:\"\\u6E05\\u9664\\u7B5B\\u9009\\u6761\\u4EF6\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2715\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6E05\\u9664\\u7B5B\\u9009\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"back-btn-top\",onClick:()=>onNavigate('home'),children:\"\\u8FD4\\u56DE\\u9996\\u9875\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"page-title\",children:\"\\u5BF9\\u6807\\u4E16\\u754C\\u4E00\\u6D41\\u4E3E\\u63AA-\\u63D0\\u62A5\\u7248\"}),/*#__PURE__*/_jsx(\"p\",{className:\"page-subtitle\",children:\"\\u4E09\\u7EA7\\u5C42\\u7EA7\\u7BA1\\u7406\\xB7\\u6708\\u5EA6\\u8DDF\\u8E2A\\xB7\\u6570\\u636E\\u540C\\u6B65\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"refresh-btn-new\",onClick:forceRefresh,children:\"\\uD83D\\uDD04 \\u5237\\u65B0\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"status-indicator \".concat(syncStatus.includes('成功')?'success':syncStatus.includes('失败')?'error':'pending'),children:syncStatus})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-panel\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"controls-left\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"filter-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u5C42\\u7EA7\\u7C7B\\u578B:\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"level-selector\",value:levelFilter.level,onChange:e=>{setLevelFilter({level:e.target.value,value:'全部'});},children:[/*#__PURE__*/_jsx(\"option\",{value:\"\\u4E00\\u7EA7\",children:\"\\u4E00\\u7EA7\\u7C7B\\u578B\"}),/*#__PURE__*/_jsx(\"option\",{value:\"\\u4E8C\\u7EA7\",children:\"\\u4E8C\\u7EA7\\u7C7B\\u578B\"}),/*#__PURE__*/_jsx(\"option\",{value:\"\\u4E09\\u7EA7\",children:\"\\u4E09\\u7EA7\\u7C7B\\u578B\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u7B5B\\u9009\\u5185\\u5BB9:\"}),/*#__PURE__*/_jsx(\"select\",{className:\"value-selector\",value:levelFilter.value,onChange:e=>{setLevelFilter(_objectSpread(_objectSpread({},levelFilter),{},{value:e.target.value}));},children:getLevelOptions().map(option=>/*#__PURE__*/_jsx(\"option\",{value:option,children:option},option))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item-inline\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u5F53\\u524D\\u663E\\u793A:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-value\",children:[currentData.length,\" \\u9879\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"responsible-filter-btn-new\",onClick:toggleResponsibleFilter,children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-icon\",children:\"\\uD83D\\uDC65\"}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"filter-badge\",children:selectedResponsiblePersons.length})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"month-navigation-new\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>navigateMonth('prev'),disabled:currentMonthPair===0,children:\"\\u25C0\\u4E0A\\u4E2A\\u6708\\u4EFD\\u5BF9\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"current-months-new\",children:[month1,\" / \",month2]}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>navigateMonth('next'),disabled:currentMonthPair===monthPairs.length-1,children:\"\\u4E0B\\u4E2A\\u6708\\u4EFD\\u5BF9\\u25B6\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"download-btn\",onClick:openDownloadModal,children:\"\\uD83D\\uDCCA \\u9009\\u62E9\\u6027\\u4E0B\\u8F7D\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"world-class-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"col-number\",children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-criteria\",children:\"\\u5DE5\\u4F5C\\u51C6\\u5219\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-target\",children:\"2025\\u5E74\\u76EE\\u6807\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-measure\",children:\"2025\\u5E74\\u4E3E\\u63AA\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-responsible\",children:\"\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-weight\",children:\"\\u6743\\u91CD\"}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"]}),/*#__PURE__*/_jsx(\"th\",{className:\"col-remark\",children:\"\\u5907\\u6CE8\"})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:currentData.map((row,index)=>/*#__PURE__*/_jsxs(\"tr\",{className:\"data-row\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-number\",children:index+1}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-criteria\",children:renderEditableCell(row.工作准则,index,'工作准则')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-target\",children:renderEditableCell(row['2025年目标'],index,'2025年目标')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-measure\",children:renderEditableCell(row['2025年举措'],index,'2025年举措')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-responsible\",children:renderEditableCell(row.负责人,index,'负责人')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-weight\",children:renderEditableCell(row.权重,index,'权重')}),renderMonthColumns(row,index),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-remark\",children:renderEditableCell(row.备注,index,'备注')})]},index))})]})}),currentData.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"no-data\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\\u4E0B\\u6CA1\\u6709\\u6570\\u636E\"})}),/*#__PURE__*/_jsx(WorldClassDownloadModal,{isOpen:showDownloadModal,onClose:closeDownloadModal,data:currentData,filteredData:filteredData,levelFilter:levelFilter,currentMonthPair:currentMonthPair,monthPairs:monthPairs,onDownload:handleDownload})]});};export default WorldClass;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "worldClassService", "worldClassDownloadService", "WorldClassDownloadModal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "WorldClass", "_ref", "onNavigate", "data", "setData", "filteredData", "setFilteredData", "loading", "setLoading", "editingCell", "setEditingCell", "syncStatus", "setSyncStatus", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "saveTimeoutRef", "pendingSaveRef", "levelFilter", "setLevelFilter", "level", "value", "selected<PERSON>espons<PERSON><PERSON><PERSON><PERSON>", "setSelectedResponsiblePersons", "tempSelected<PERSON>ersons", "setTempSelected<PERSON><PERSON>s", "showResponsibleFilter", "setShowResponsibleFilter", "currentMonthPair", "setCurrentMonthPair", "currentMonth", "Date", "getMonth", "months", "currentIndex", "for<PERSON>ach", "month", "index", "monthNum", "parseInt", "replace", "Math", "max", "monthPairs", "showDownloadModal", "setShowDownloadModal", "downloadLoading", "setDownloadLoading", "loadData", "setSyncCallbacks", "error", "current", "clearTimeout", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "applyFilters", "console", "log", "worldClassData", "loadWorldClassData", "length", "slice", "map", "item", "_item$2025年目标", "工作准则", "负责人", "substring", "forceRefresh", "getLevelOptions", "field", "options", "Set", "filter", "val", "String", "trim", "getAllResponsiblePersons", "sourceData", "<PERSON><PERSON><PERSON><PERSON>", "person", "reduce", "acc", "persons", "split", "p", "includes", "push", "sort", "filtered", "itemPersons", "some", "<PERSON><PERSON><PERSON>", "toggleResponsibleFilter", "handleResponsiblePersonSelect", "prev", "handleClearPanelSelection", "applyResponsibleFilter", "clearResponsibleFilter", "navigateMonth", "direction", "debouncedSave", "rowIndex", "_pendingSaveRef$curre3", "_pendingSaveRef$curre4", "controller", "AbortController", "updateData", "signal", "aborted", "setTimeout", "name", "handleInputChange", "newData", "key", "concat", "_objectSpread", "handleBlurSave", "undefined", "newTemp", "handleCellEdit", "startEdit", "finishEdit", "formatDisplayValue", "hasOwnProperty", "v", "w", "text", "richText", "renderEditableCell", "className", "arguments", "cellKey", "isEditing", "displayValue", "isReadOnlyField", "isEditable", "onChange", "e", "target", "onBlur", "onKeyDown", "ctrl<PERSON>ey", "rows", "autoFocus", "cellClasses", "titleText", "onClick", "title", "style", "cursor", "children", "line", "i", "arr", "renderMonthColumns", "row", "month1", "month2", "handleDownload", "selectionData", "result", "handleSelectiveDownload", "success", "message", "alert", "Error", "openDownloadModal", "closeDownloadModal", "renderResponsibleFilterPanel", "<PERSON><PERSON><PERSON><PERSON>", "currentData", "原始数据条数", "过滤数据条数", "当前显示条数", "当前月份对", "background", "border", "borderRadius", "margin", "width", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ilter", "boxShadow", "display", "alignItems", "padding", "gap", "justifyContent", "height", "fontSize", "flex", "color", "marginBottom", "fontWeight", "textShadow", "join", "transition", "onMouseEnter", "transform", "onMouseLeave", "option", "disabled", "权重", "备注", "isOpen", "onClose", "onDownload"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块三/pages/WorldClass.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/WorldClass.css';\nimport worldClassService from '../services/worldClassService';\nimport worldClassDownloadService from '../services/worldClassDownloadService';\nimport WorldClassDownloadModal from '../components/WorldClassDownloadModal';\n\nconst WorldClass = ({ onNavigate }) => {\n  const [data, setData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingCell, setEditingCell] = useState(null);\n  const [syncStatus, setSyncStatus] = useState('已同步');\n\n  // 数据输入优化相关状态\n  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  \n  // 层级筛选状态\n  const [levelFilter, setLevelFilter] = useState({\n    level: '一级',\n    value: '全部'\n  });\n  \n  // 负责人筛选相关状态（复用模块二功能）\n  const [selectedResponsiblePersons, setSelectedResponsiblePersons] = useState([]);\n  const [tempSelectedPersons, setTempSelectedPersons] = useState([]); // 临时存储选择的负责人\n  const [showResponsibleFilter, setShowResponsibleFilter] = useState(false);\n  \n  // 月份切换状态 - 从2月开始按月递增，参考模块二的逻辑设置初始显示月份\n  const [currentMonthPair, setCurrentMonthPair] = useState(() => {\n    // 根据当前月份设置初始显示的月份对\n    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12\n    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\n\n    // 找到当前月份在数组中的位置\n    let currentIndex = -1;\n    months.forEach((month, index) => {\n      const monthNum = parseInt(month.replace('月', ''));\n      if (monthNum === currentMonth) {\n        currentIndex = index;\n      }\n    });\n\n    // 如果找到当前月份，返回包含当前月份的月份对索引\n    if (currentIndex >= 0) {\n      // 如果当前月份是第一个月（如2月），则显示第一对\n      if (currentIndex === 0) return 0;\n      // 否则，尽量让当前月份显示在第一个位置\n      return Math.max(0, currentIndex - 1);\n    }\n\n    // 如果当前月份不在范围内，默认显示第一对\n    return 0;\n  });\n  const monthPairs = [\n    ['2月', '3月'], ['3月', '4月'], ['4月', '5月'],\n    ['5月', '6月'], ['6月', '7月'], ['7月', '8月'],\n    ['8月', '9月'], ['9月', '10月'], ['10月', '11月'],\n    ['11月', '12月']\n  ];\n  \n  // 下载相关状态\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n\n  useEffect(() => {\n    loadData();\n\n    // 设置同步回调\n    worldClassService.setSyncCallbacks(\n      () => setSyncStatus('同步成功'),\n      (error) => setSyncStatus('同步失败')\n    );\n\n    // 清理函数\n    return () => {\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n      }\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    // 应用层级筛选和负责人筛选\n    applyFilters();\n  }, [data, levelFilter, selectedResponsiblePersons]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      console.log('开始加载对标世界一流数据...');\n      const worldClassData = await worldClassService.loadWorldClassData();\n      console.log('加载的数据:', worldClassData);\n      \n      if (worldClassData) {\n        setData(worldClassData);\n        console.log('数据设置完成，总共:', worldClassData.length, '项');\n        console.log('前3条数据预览:', worldClassData.slice(0, 3).map(item => ({\n          工作准则: item.工作准则,\n          负责人: item.负责人,\n          '2025年目标': item['2025年目标']?.substring(0, 50) + '...'\n        })));\n      } else {\n        console.error('未获取到数据');\n      }\n    } catch (error) {\n      console.error('数据加载失败:', error);\n    }\n    setLoading(false);\n  };\n\n  // 强制刷新数据的方法（复用模块一的设计）\n  const forceRefresh = async () => {\n    console.log('强制刷新数据...');\n    setSyncStatus('刷新中...');\n    await loadData();\n    setSyncStatus('刷新完成');\n  };\n\n  // 获取层级选项\n  const getLevelOptions = () => {\n    if (!data || data.length === 0) return [];\n    \n    const field = levelFilter.level === '一级' ? '相关指标或方向（一级）' : \n                  levelFilter.level === '二级' ? '相关指标或方向（二级）' : \n                  '相关指标或方向（三级）';\n    \n    const options = [...new Set(data\n      .map(item => item[field])\n      .filter(val => val && String(val).trim() !== '')\n      .map(val => String(val))\n    )];\n    \n    return ['全部', ...options];\n  };\n\n  // 获取所有负责人列表（基于当前显示的数据而不是全部数据）\n  const getAllResponsiblePersons = () => {\n    // 使用当前筛选后的数据作为数据源\n    const sourceData = filteredData.length > 0 ? filteredData : data;\n    if (!sourceData || sourceData.length === 0) return [];\n    \n    const responsiblePersons = sourceData\n      .map(item => item.负责人)\n      .filter(person => person && String(person).trim() !== '')\n      .reduce((acc, person) => {\n        // 处理多个负责人用逗号、括号等分隔的情况\n        const persons = String(person).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);\n        persons.forEach(p => {\n          if (!acc.includes(p)) {\n            acc.push(p);\n          }\n        });\n        return acc;\n      }, []);\n    \n    return responsiblePersons.sort();\n  };\n\n  // 应用综合筛选（层级 + 负责人）\n  const applyFilters = () => {\n    if (!data || data.length === 0) {\n      setFilteredData([]);\n      return;\n    }\n\n    let filtered = [...data];\n\n    // 先应用层级筛选\n    if (levelFilter.value !== '全部') {\n      const field = levelFilter.level === '一级' ? '相关指标或方向（一级）' : \n                    levelFilter.level === '二级' ? '相关指标或方向（二级）' : \n                    '相关指标或方向（三级）';\n\n      filtered = filtered.filter(item => \n        String(item[field] || '').trim() === levelFilter.value\n      );\n    }\n\n    // 再应用负责人筛选 - 基于已经层级筛选后的数据\n    if (selectedResponsiblePersons.length > 0) {\n      filtered = filtered.filter(item => {\n        if (!item.负责人) return false;\n        \n        // 将单元格中的负责人字符串拆分为数组，并进行清理\n        const itemPersons = String(item.负责人).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);\n        \n        // 检查是否有任何一个选中的负责人存在于单元格的负责人数组中（精确匹配）\n        return selectedResponsiblePersons.some(selectedPerson => \n          itemPersons.includes(selectedPerson)\n        );\n      });\n    }\n    \n    setFilteredData(filtered);\n  };\n\n  // 负责人筛选相关函数\n  const toggleResponsibleFilter = () => {\n    if (!showResponsibleFilter) {\n      // 打开面板时，用当前已应用的筛选初始化临时选择\n      setTempSelectedPersons([...selectedResponsiblePersons]);\n    }\n    setShowResponsibleFilter(!showResponsibleFilter);\n  };\n\n  const handleResponsiblePersonSelect = (person) => {\n    // 只更新临时选择的负责人状态\n    setTempSelectedPersons(prev => {\n      if (prev.includes(person)) {\n        return prev.filter(p => p !== person);\n      } else {\n        return [...prev, person];\n      }\n    });\n  };\n\n  const handleClearPanelSelection = () => {\n    // 仅清除面板内的临时选择，不关闭面板，以便用户重新选择\n    setTempSelectedPersons([]);\n  };\n\n  const applyResponsibleFilter = () => {\n    // 应用筛选，将临时选择更新到正式的筛选状态\n    setSelectedResponsiblePersons([...tempSelectedPersons]);\n    setShowResponsibleFilter(false);\n  };\n\n  const clearResponsibleFilter = () => {\n    // 清除临时选择和已应用的筛选\n    setTempSelectedPersons([]);\n    setSelectedResponsiblePersons([]);\n    setShowResponsibleFilter(false);\n  };\n\n  // 月份切换功能（参考模块二设计）\n  const navigateMonth = (direction) => {\n    if (direction === 'prev' && currentMonthPair > 0) {\n      setCurrentMonthPair(currentMonthPair - 1);\n    } else if (direction === 'next' && currentMonthPair < monthPairs.length - 1) {\n      setCurrentMonthPair(currentMonthPair + 1);\n    }\n  };\n\n  // 防抖保存函数\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      setSyncStatus('同步中...');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 调用双向同步\n      await worldClassService.updateData(rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        setSyncStatus('同步成功');\n        setTimeout(() => setSyncStatus('已同步'), 1000);\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (!error.name === 'AbortError') {\n        console.error('保存失败:', error);\n        setSyncStatus('同步失败');\n      }\n    }\n  }, []);\n\n  // 处理输入变化（实时更新UI，延迟保存）\n  const handleInputChange = (rowIndex, field, value) => {\n    // 立即更新UI显示\n    const newData = [...data];\n    newData[rowIndex][field] = value;\n    setData(newData);\n\n    // 存储临时值\n    const key = `${rowIndex}-${field}`;\n    setTempValues(prev => ({ ...prev, [key]: value }));\n\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 800); // 800ms防抖延迟\n  };\n\n  // 处理失焦保存（立即保存）\n  const handleBlurSave = async (rowIndex, field) => {\n    const key = `${rowIndex}-${field}`;\n    const value = tempValues[key];\n\n    if (value !== undefined) {\n      // 清除防抖定时器\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n        saveTimeoutRef.current = null;\n      }\n\n      // 立即保存\n      await debouncedSave(rowIndex, field, value);\n\n      // 清除临时值\n      setTempValues(prev => {\n        const newTemp = { ...prev };\n        delete newTemp[key];\n        return newTemp;\n      });\n    }\n  };\n\n  // 兼容原有的handleCellEdit接口\n  const handleCellEdit = async (rowIndex, field, value) => {\n    handleInputChange(rowIndex, field, value);\n  };\n\n  const startEdit = (rowIndex, field) => {\n    setEditingCell(`${rowIndex}-${field}`);\n  };\n\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 格式化显示值\n  const formatDisplayValue = (value) => {\n    if (value === null || value === undefined || value === '') return '';\n    if (typeof value === 'object') {\n      if (value.hasOwnProperty('v')) return value.v;\n      if (value.hasOwnProperty('w')) return value.w;\n      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;\n      if (value.text !== undefined) return value.text;\n      if (value.richText !== undefined) return value.richText;\n      if (value.value !== undefined) return value.value;\n      return String(value);\n    }\n    return String(value);\n  };\n\n  // 渲染可编辑单元格\n  const renderEditableCell = (value, rowIndex, field, className = '') => {\n    const cellKey = `${rowIndex}-${field}`;\n    const isEditing = editingCell === cellKey;\n    const displayValue = formatDisplayValue(value);\n\n    // 检查是否为只读列（完成情况列）\n    const isReadOnlyField = field.includes('完成情况');\n    const isEditable = !['序号'].includes(field) && !isReadOnlyField;\n\n    if (isEditing && isEditable) {\n      return (\n        <textarea\n          value={displayValue}\n          onChange={(e) => handleInputChange(rowIndex, field, e.target.value)}\n          onBlur={() => {\n            handleBlurSave(rowIndex, field);\n            finishEdit();\n          }}\n          onKeyDown={(e) => {\n            if (e.key === 'Enter' && e.ctrlKey) {\n              handleBlurSave(rowIndex, field);\n              finishEdit();\n            }\n            if (e.key === 'Escape') {\n              finishEdit();\n            }\n          }}\n          className=\"cell-input\"\n          rows={displayValue.includes('\\n') ? displayValue.split('\\n').length + 1 : 2}\n          autoFocus\n        />\n      );\n    }\n\n    // 确定单元格的CSS类名\n    let cellClasses = `cell-content ${className}`;\n    if (isReadOnlyField) {\n      cellClasses += ' readonly-cell';\n    } else if (isEditable) {\n      cellClasses += ' editable-cell';\n    }\n\n    // 确定提示文本\n    let titleText = '';\n    if (isReadOnlyField) {\n      titleText = '该列不可编辑';\n    } else if (isEditable) {\n      titleText = '点击编辑';\n    }\n\n    return (\n      <span\n        className={cellClasses}\n        onClick={() => isEditable && startEdit(rowIndex, field)}\n        title={titleText}\n        style={isReadOnlyField ? { cursor: 'not-allowed' } : {}}\n      >\n        {displayValue.includes('\\n') ?\n          displayValue.split('\\n').map((line, i, arr) => (\n            <React.Fragment key={i}>\n              {line}\n              {i < arr.length - 1 && <br />}\n            </React.Fragment>\n          )) :\n          displayValue\n        }\n      </span>\n    );\n  };\n\n  // 渲染月份列\n  const renderMonthColumns = (row, rowIndex) => {\n    const [month1, month2] = monthPairs[currentMonthPair];\n    \n    return (\n      <>\n        <td className=\"data-cell col-month-plan\">\n          {renderEditableCell(row[`${month1}工作计划`], rowIndex, `${month1}工作计划`, 'month-plan')}\n        </td>\n        <td className=\"data-cell col-month-complete\">\n          {renderEditableCell(row[`${month1}完成情况`], rowIndex, `${month1}完成情况`, 'month-complete')}\n        </td>\n        <td className=\"data-cell col-month-plan\">\n          {renderEditableCell(row[`${month2}工作计划`], rowIndex, `${month2}工作计划`, 'month-plan')}\n        </td>\n        <td className=\"data-cell col-month-complete\">\n          {renderEditableCell(row[`${month2}完成情况`], rowIndex, `${month2}完成情况`, 'month-complete')}\n        </td>\n      </>\n    );\n  };\n\n  // 下载功能（复用模块一的设计）\n  const handleDownload = async (selectionData) => {\n    setDownloadLoading(true);\n    try {\n      console.log('开始下载对标世界一流数据:', selectionData);\n      \n      // 调用下载服务处理实际下载\n      const result = await worldClassDownloadService.handleSelectiveDownload(selectionData);\n      \n      if (result.success) {\n        console.log('下载成功:', result.message);\n        alert(`下载成功: ${result.message}`);\n      } else {\n        throw new Error(result.message || '下载失败');\n      }\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败: ' + error.message);\n    } finally {\n      setDownloadLoading(false);\n      setShowDownloadModal(false);\n    }\n  };\n\n  const openDownloadModal = () => {\n    setShowDownloadModal(true);\n  };\n\n  const closeDownloadModal = () => {\n    setShowDownloadModal(false);\n  };\n\n  // 渲染负责人筛选面板\n  const renderResponsibleFilterPanel = () => {\n    if (!showResponsibleFilter) return null;\n    const allPersons = getAllResponsiblePersons();\n\n    return (\n      <div className=\"world-class-responsible-filter-panel\">\n        <div className=\"world-class-filter-panel-header\">\n          <h3>选择负责人</h3>\n          <button onClick={toggleResponsibleFilter} className=\"world-class-close-panel-btn\">×</button>\n        </div>\n        <div className=\"world-class-filter-panel-content\">\n          <div className=\"world-class-filter-options\">\n            {allPersons.map(person => (\n              <div \n                key={person} \n                className={`world-class-filter-option ${tempSelectedPersons.includes(person) ? 'selected' : ''}`}\n                onClick={() => handleResponsiblePersonSelect(person)}\n              >\n                {person}\n              </div>\n            ))}\n          </div>\n          <div className=\"world-class-filter-actions\">\n            <button onClick={handleClearPanelSelection} className=\"world-class-clear-filter-btn\">\n              清除选择\n            </button>\n            <button onClick={applyResponsibleFilter} className=\"world-class-apply-filter-btn\">\n              应用筛选\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // 加载状态\n  if (loading) {\n    return (\n      <div className=\"world-class-container\">\n        <div className=\"world-class-loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>正在加载对标世界一流数据...</p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentData = filteredData.length > 0 ? filteredData : data;\n  const [month1, month2] = monthPairs[currentMonthPair];\n\n  // 调试信息\n  console.log('渲染数据状态:', {\n    原始数据条数: data.length,\n    过滤数据条数: filteredData.length,\n    当前显示条数: currentData.length,\n    当前月份对: `${month1}-${month2}`\n  });\n\n  return (\n    <div className=\"world-class-container\">\n      {/* 负责人筛选面板（复用模块二设计） */}\n      {renderResponsibleFilterPanel()}\n\n      {/* 负责人筛选状态提示 */}\n      {selectedResponsiblePersons.length > 0 && (\n        <div \n          style={{\n            background: 'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',\n            border: '1px solid rgba(0, 212, 170, 0.3)',\n            borderRadius: '12px',\n            margin: '20px auto',\n            width: '100%',\n            maxWidth: 'none',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(0, 212, 170, 0.2)'\n          }}\n        >\n          <div \n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              padding: '16px 24px',\n              gap: '16px'\n            }}\n          >\n            <div \n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',\n                borderRadius: '50%',\n                fontSize: '18px'\n              }}\n            >\n              🔍\n            </div>\n            <div style={{ flex: 1 }}>\n              <div \n                style={{\n                  fontSize: '14px',\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  marginBottom: '4px',\n                  fontWeight: '500'\n                }}\n              >\n                当前筛选条件\n              </div>\n              <div \n                style={{\n                  fontSize: '16px',\n                  color: '#fff',\n                  fontWeight: '600'\n                }}\n              >\n                负责人：\n                <span style={{ \n                  color: '#20ff4d', \n                  fontWeight: 'bold',\n                  textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',\n                  margin: '0 8px'\n                }}>\n                  {selectedResponsiblePersons.join('、')}\n                </span>\n                <span style={{ \n                  color: '#00d4aa',\n                  fontSize: '14px'\n                }}>\n                  （{filteredData.length}项工作）\n                </span>\n              </div>\n            </div>\n            <button \n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                padding: '8px 16px',\n                background: 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',\n                border: '1px solid rgba(255, 75, 75, 0.4)',\n                borderRadius: '8px',\n                color: '#ff6b6b',\n                fontSize: '14px',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n              onClick={clearResponsibleFilter}\n              onMouseEnter={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';\n                e.target.style.transform = 'translateY(-1px)';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';\n                e.target.style.transform = 'translateY(0)';\n              }}\n              title=\"清除筛选条件\"\n            >\n              <span>✕</span>\n              <span>清除筛选</span>\n            </button>\n          </div>\n        </div>\n      )}\n      {/* 页面头部 */}\n      <div className=\"page-header\">\n        <button \n          className=\"back-btn-top\"\n          onClick={() => onNavigate('home')}\n        >\n          返回首页\n        </button>\n        \n        <div className=\"header-center\">\n          <h1 className=\"page-title\">对标世界一流举措-提报版</h1>\n          <p className=\"page-subtitle\">三级层级管理·月度跟踪·数据同步</p>\n        </div>\n        \n        <div className=\"header-actions\">\n          <button className=\"refresh-btn-new\" onClick={forceRefresh}>\n            🔄 刷新数据\n          </button>\n          <div className=\"sync-status\">\n            <span \n              className={`status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}\n            >\n              {syncStatus}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* 控制面板 */}\n      <div className=\"control-panel\">\n        <div className=\"controls-left\">\n          <div className=\"filter-controls\">\n            <div className=\"filter-group\">\n              <label>层级类型:</label>\n              <select\n                className=\"level-selector\"\n                value={levelFilter.level}\n                onChange={(e) => {\n                  setLevelFilter({\n                    level: e.target.value,\n                    value: '全部'\n                  });\n                }}\n              >\n                <option value=\"一级\">一级类型</option>\n                <option value=\"二级\">二级类型</option>\n                <option value=\"三级\">三级类型</option>\n              </select>\n            </div>\n            \n            <div className=\"filter-group\">\n              <label>筛选内容:</label>\n              <select\n                className=\"value-selector\"\n                value={levelFilter.value}\n                onChange={(e) => {\n                  setLevelFilter({\n                    ...levelFilter,\n                    value: e.target.value\n                  });\n                }}\n              >\n                {getLevelOptions().map(option => (\n                  <option key={option} value={option}>{option}</option>\n                ))}\n              </select>\n            </div>\n            \n            <div className=\"stat-item-inline\">\n              <span className=\"stat-label\">当前显示:</span>\n              <span className=\"stat-value\">{currentData.length} 项</span>\n            </div>\n            \n            <div \n              className=\"responsible-filter-btn-new\"\n              onClick={toggleResponsibleFilter}\n            >\n              <span className=\"filter-icon\">👥</span>\n              {selectedResponsiblePersons.length > 0 && (\n                <div className=\"filter-badge\">\n                  {selectedResponsiblePersons.length}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"action-buttons\">\n          <div className=\"month-navigation-new\">\n            <button \n              className=\"nav-btn-new\"\n              onClick={() => navigateMonth('prev')}\n              disabled={currentMonthPair === 0}\n            >\n              ◀上个月份对\n            </button>\n            <span className=\"current-months-new\">\n              {month1} / {month2}\n            </span>\n            <button \n              className=\"nav-btn-new\"\n              onClick={() => navigateMonth('next')}\n              disabled={currentMonthPair === monthPairs.length - 1}\n            >\n              下个月份对▶\n            </button>\n          </div>\n          <button \n            className=\"download-btn\" \n            onClick={openDownloadModal}\n          >\n            📊 选择性下载\n          </button>\n        </div>\n      </div>\n\n      {/* 数据表格 */}\n      <div className=\"table-container\">\n        <table className=\"world-class-table\">\n          <thead>\n            <tr>\n              <th className=\"col-number\">序号</th>\n              <th className=\"col-criteria\">工作准则</th>\n              <th className=\"col-target\">2025年目标</th>\n              <th className=\"col-measure\">2025年举措</th>\n              <th className=\"col-responsible\">负责人</th>\n              <th className=\"col-weight\">权重</th>\n              <th className=\"col-month-plan\">{month1}工作计划</th>\n              <th className=\"col-month-complete\">{month1}完成情况</th>\n              <th className=\"col-month-plan\">{month2}工作计划</th>\n              <th className=\"col-month-complete\">{month2}完成情况</th>\n              <th className=\"col-remark\">备注</th>\n            </tr>\n          </thead>\n          <tbody>\n            {currentData.map((row, index) => (\n              <tr key={index} className=\"data-row\">\n                <td className=\"data-cell col-number\">\n                  {index + 1}\n                </td>\n                <td className=\"data-cell col-criteria\">\n                  {renderEditableCell(row.工作准则, index, '工作准则')}\n                </td>\n                <td className=\"data-cell col-target\">\n                  {renderEditableCell(row['2025年目标'], index, '2025年目标')}\n                </td>\n                <td className=\"data-cell col-measure\">\n                  {renderEditableCell(row['2025年举措'], index, '2025年举措')}\n                </td>\n                <td className=\"data-cell col-responsible\">\n                  {renderEditableCell(row.负责人, index, '负责人')}\n                </td>\n                <td className=\"data-cell col-weight\">\n                  {renderEditableCell(row.权重, index, '权重')}\n                </td>\n                {renderMonthColumns(row, index)}\n                <td className=\"data-cell col-remark\">\n                  {renderEditableCell(row.备注, index, '备注')}\n                </td>\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n\n      {currentData.length === 0 && (\n        <div className=\"no-data\">\n          <p>当前筛选条件下没有数据</p>\n        </div>\n      )}\n\n      {/* 选择性下载模态框 */}\n      <WorldClassDownloadModal\n        isOpen={showDownloadModal}\n        onClose={closeDownloadModal}\n        data={currentData}\n        filteredData={filteredData}\n        levelFilter={levelFilter}\n        currentMonthPair={currentMonthPair}\n        monthPairs={monthPairs}\n        onDownload={handleDownload}\n      />\n    </div>\n  );\n};\n\nexport default WorldClass; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,iBAAiB,KAAM,+BAA+B,CAC7D,MAAO,CAAAC,yBAAyB,KAAM,uCAAuC,CAC7E,MAAO,CAAAC,uBAAuB,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5E,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAChC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACkB,YAAY,CAAEC,eAAe,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAEnD;AACA,KAAM,CAAC0B,UAAU,CAAEC,aAAa,CAAC,CAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE;AAClD,KAAM,CAAA4B,cAAc,CAAG1B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA2B,cAAc,CAAG3B,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAC4B,WAAW,CAAEC,cAAc,CAAC,CAAG/B,QAAQ,CAAC,CAC7CgC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IACT,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAChF,KAAM,CAACoC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrC,QAAQ,CAAC,EAAE,CAAC,CAAE;AACpE,KAAM,CAACsC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGvC,QAAQ,CAAC,KAAK,CAAC,CAEzE;AACA,KAAM,CAACwC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzC,QAAQ,CAAC,IAAM,CAC7D;AACA,KAAM,CAAA0C,YAAY,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAE;AAChD,KAAM,CAAAC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAEpF;AACA,GAAI,CAAAC,YAAY,CAAG,CAAC,CAAC,CACrBD,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC/B,KAAM,CAAAC,QAAQ,CAAGC,QAAQ,CAACH,KAAK,CAACI,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAAC,CACjD,GAAIF,QAAQ,GAAKR,YAAY,CAAE,CAC7BI,YAAY,CAAGG,KAAK,CACtB,CACF,CAAC,CAAC,CAEF;AACA,GAAIH,YAAY,EAAI,CAAC,CAAE,CACrB;AACA,GAAIA,YAAY,GAAK,CAAC,CAAE,MAAO,EAAC,CAChC;AACA,MAAO,CAAAO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAER,YAAY,CAAG,CAAC,CAAC,CACtC,CAEA;AACA,MAAO,EAAC,CACV,CAAC,CAAC,CACF,KAAM,CAAAS,UAAU,CAAG,CACjB,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CACxC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CACxC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,KAAK,CAAC,CAAE,CAAC,KAAK,CAAE,KAAK,CAAC,CAC3C,CAAC,KAAK,CAAE,KAAK,CAAC,CACf,CAED;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzD,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC0D,eAAe,CAAEC,kBAAkB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CAE7DC,SAAS,CAAC,IAAM,CACd2D,QAAQ,CAAC,CAAC,CAEV;AACAxD,iBAAiB,CAACyD,gBAAgB,CAChC,IAAMpC,aAAa,CAAC,MAAM,CAAC,CAC1BqC,KAAK,EAAKrC,aAAa,CAAC,MAAM,CACjC,CAAC,CAED;AACA,MAAO,IAAM,CACX,GAAIG,cAAc,CAACmC,OAAO,CAAE,CAC1BC,YAAY,CAACpC,cAAc,CAACmC,OAAO,CAAC,CACtC,CACA,GAAIlC,cAAc,CAACkC,OAAO,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAArC,cAAc,CAACkC,OAAO,EAACI,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAENjE,SAAS,CAAC,IAAM,CACd;AACAoE,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACrD,IAAI,CAAEc,WAAW,CAAEI,0BAA0B,CAAC,CAAC,CAEnD,KAAM,CAAA0B,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BvC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACFiD,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC,CAC9B,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAApE,iBAAiB,CAACqE,kBAAkB,CAAC,CAAC,CACnEH,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEC,cAAc,CAAC,CAErC,GAAIA,cAAc,CAAE,CAClBvD,OAAO,CAACuD,cAAc,CAAC,CACvBF,OAAO,CAACC,GAAG,CAAC,YAAY,CAAEC,cAAc,CAACE,MAAM,CAAE,GAAG,CAAC,CACrDJ,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEC,cAAc,CAACG,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAI,OAAAC,aAAA,OAAK,CAC9DC,IAAI,CAAEF,IAAI,CAACE,IAAI,CACfC,GAAG,CAAEH,IAAI,CAACG,GAAG,CACb,SAAS,CAAE,EAAAF,aAAA,CAAAD,IAAI,CAAC,SAAS,CAAC,UAAAC,aAAA,iBAAfA,aAAA,CAAiBG,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,EAAG,KACjD,CAAC,EAAC,CAAC,CAAC,CACN,CAAC,IAAM,CACLX,OAAO,CAACR,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAE,MAAOA,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CACjC,CACAzC,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAA6D,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/BZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxB9C,aAAa,CAAC,QAAQ,CAAC,CACvB,KAAM,CAAAmC,QAAQ,CAAC,CAAC,CAChBnC,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA0D,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACnE,IAAI,EAAIA,IAAI,CAAC0D,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAEzC,KAAM,CAAAU,KAAK,CAAGtD,WAAW,CAACE,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1CF,WAAW,CAACE,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1C,aAAa,CAE3B,KAAM,CAAAqD,OAAO,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAACtE,IAAI,CAC7B4D,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACO,KAAK,CAAC,CAAC,CACxBG,MAAM,CAACC,GAAG,EAAIA,GAAG,EAAIC,MAAM,CAACD,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC/Cd,GAAG,CAACY,GAAG,EAAIC,MAAM,CAACD,GAAG,CAAC,CACzB,CAAC,CAAC,CAEF,MAAO,CAAC,IAAI,CAAE,GAAGH,OAAO,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAM,wBAAwB,CAAGA,CAAA,GAAM,CACrC;AACA,KAAM,CAAAC,UAAU,CAAG1E,YAAY,CAACwD,MAAM,CAAG,CAAC,CAAGxD,YAAY,CAAGF,IAAI,CAChE,GAAI,CAAC4E,UAAU,EAAIA,UAAU,CAAClB,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAErD,KAAM,CAAAmB,kBAAkB,CAAGD,UAAU,CAClChB,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACG,GAAG,CAAC,CACrBO,MAAM,CAACO,MAAM,EAAIA,MAAM,EAAIL,MAAM,CAACK,MAAM,CAAC,CAACJ,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACxDK,MAAM,CAAC,CAACC,GAAG,CAAEF,MAAM,GAAK,CACvB;AACA,KAAM,CAAAG,OAAO,CAAGR,MAAM,CAACK,MAAM,CAAC,CAACI,KAAK,CAAC,aAAa,CAAC,CAACtB,GAAG,CAACuB,CAAC,EAAIA,CAAC,CAACT,IAAI,CAAC,CAAC,CAAC,CAACH,MAAM,CAACY,CAAC,EAAIA,CAAC,CAAC,CACrFF,OAAO,CAAClD,OAAO,CAACoD,CAAC,EAAI,CACnB,GAAI,CAACH,GAAG,CAACI,QAAQ,CAACD,CAAC,CAAC,CAAE,CACpBH,GAAG,CAACK,IAAI,CAACF,CAAC,CAAC,CACb,CACF,CAAC,CAAC,CACF,MAAO,CAAAH,GAAG,CACZ,CAAC,CAAE,EAAE,CAAC,CAER,MAAO,CAAAH,kBAAkB,CAACS,IAAI,CAAC,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAjC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAACrD,IAAI,EAAIA,IAAI,CAAC0D,MAAM,GAAK,CAAC,CAAE,CAC9BvD,eAAe,CAAC,EAAE,CAAC,CACnB,OACF,CAEA,GAAI,CAAAoF,QAAQ,CAAG,CAAC,GAAGvF,IAAI,CAAC,CAExB;AACA,GAAIc,WAAW,CAACG,KAAK,GAAK,IAAI,CAAE,CAC9B,KAAM,CAAAmD,KAAK,CAAGtD,WAAW,CAACE,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1CF,WAAW,CAACE,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1C,aAAa,CAE3BuE,QAAQ,CAAGA,QAAQ,CAAChB,MAAM,CAACV,IAAI,EAC7BY,MAAM,CAACZ,IAAI,CAACO,KAAK,CAAC,EAAI,EAAE,CAAC,CAACM,IAAI,CAAC,CAAC,GAAK5D,WAAW,CAACG,KACnD,CAAC,CACH,CAEA;AACA,GAAIC,0BAA0B,CAACwC,MAAM,CAAG,CAAC,CAAE,CACzC6B,QAAQ,CAAGA,QAAQ,CAAChB,MAAM,CAACV,IAAI,EAAI,CACjC,GAAI,CAACA,IAAI,CAACG,GAAG,CAAE,MAAO,MAAK,CAE3B;AACA,KAAM,CAAAwB,WAAW,CAAGf,MAAM,CAACZ,IAAI,CAACG,GAAG,CAAC,CAACkB,KAAK,CAAC,aAAa,CAAC,CAACtB,GAAG,CAACuB,CAAC,EAAIA,CAAC,CAACT,IAAI,CAAC,CAAC,CAAC,CAACH,MAAM,CAACY,CAAC,EAAIA,CAAC,CAAC,CAE3F;AACA,MAAO,CAAAjE,0BAA0B,CAACuE,IAAI,CAACC,cAAc,EACnDF,WAAW,CAACJ,QAAQ,CAACM,cAAc,CACrC,CAAC,CACH,CAAC,CAAC,CACJ,CAEAvF,eAAe,CAACoF,QAAQ,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAI,uBAAuB,CAAGA,CAAA,GAAM,CACpC,GAAI,CAACrE,qBAAqB,CAAE,CAC1B;AACAD,sBAAsB,CAAC,CAAC,GAAGH,0BAA0B,CAAC,CAAC,CACzD,CACAK,wBAAwB,CAAC,CAACD,qBAAqB,CAAC,CAClD,CAAC,CAED,KAAM,CAAAsE,6BAA6B,CAAId,MAAM,EAAK,CAChD;AACAzD,sBAAsB,CAACwE,IAAI,EAAI,CAC7B,GAAIA,IAAI,CAACT,QAAQ,CAACN,MAAM,CAAC,CAAE,CACzB,MAAO,CAAAe,IAAI,CAACtB,MAAM,CAACY,CAAC,EAAIA,CAAC,GAAKL,MAAM,CAAC,CACvC,CAAC,IAAM,CACL,MAAO,CAAC,GAAGe,IAAI,CAAEf,MAAM,CAAC,CAC1B,CACF,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAgB,yBAAyB,CAAGA,CAAA,GAAM,CACtC;AACAzE,sBAAsB,CAAC,EAAE,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA0E,sBAAsB,CAAGA,CAAA,GAAM,CACnC;AACA5E,6BAA6B,CAAC,CAAC,GAAGC,mBAAmB,CAAC,CAAC,CACvDG,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAC,CAED,KAAM,CAAAyE,sBAAsB,CAAGA,CAAA,GAAM,CACnC;AACA3E,sBAAsB,CAAC,EAAE,CAAC,CAC1BF,6BAA6B,CAAC,EAAE,CAAC,CACjCI,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAA0E,aAAa,CAAIC,SAAS,EAAK,CACnC,GAAIA,SAAS,GAAK,MAAM,EAAI1E,gBAAgB,CAAG,CAAC,CAAE,CAChDC,mBAAmB,CAACD,gBAAgB,CAAG,CAAC,CAAC,CAC3C,CAAC,IAAM,IAAI0E,SAAS,GAAK,MAAM,EAAI1E,gBAAgB,CAAGe,UAAU,CAACmB,MAAM,CAAG,CAAC,CAAE,CAC3EjC,mBAAmB,CAACD,gBAAgB,CAAG,CAAC,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAA2E,aAAa,CAAGhH,WAAW,CAAC,MAAOiH,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,GAAK,CAClE,GAAI,CACFR,aAAa,CAAC,QAAQ,CAAC,CAEvB;AACA,GAAII,cAAc,CAACkC,OAAO,CAAE,KAAAsD,sBAAA,CAAAC,sBAAA,CAC1B,CAAAD,sBAAA,EAAAC,sBAAA,CAAAzF,cAAc,CAACkC,OAAO,EAACI,KAAK,UAAAkD,sBAAA,iBAA5BA,sBAAA,CAAAjD,IAAA,CAAAkD,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC3F,cAAc,CAACkC,OAAO,CAAGwD,UAAU,CAEnC;AACA,KAAM,CAAAnH,iBAAiB,CAACqH,UAAU,CAACL,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,CAAC,CAE1D;AACA,GAAI,CAACsF,UAAU,CAACG,MAAM,CAACC,OAAO,CAAE,CAC9BlG,aAAa,CAAC,MAAM,CAAC,CACrBmG,UAAU,CAAC,IAAMnG,aAAa,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC5CI,cAAc,CAACkC,OAAO,CAAG,IAAI,CAC/B,CACF,CAAE,MAAOD,KAAK,CAAE,CACd,GAAI,CAACA,KAAK,CAAC+D,IAAI,GAAK,YAAY,CAAE,CAChCvD,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BrC,aAAa,CAAC,MAAM,CAAC,CACvB,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqG,iBAAiB,CAAGA,CAACV,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,GAAK,CACpD;AACA,KAAM,CAAA8F,OAAO,CAAG,CAAC,GAAG/G,IAAI,CAAC,CACzB+G,OAAO,CAACX,QAAQ,CAAC,CAAChC,KAAK,CAAC,CAAGnD,KAAK,CAChChB,OAAO,CAAC8G,OAAO,CAAC,CAEhB;AACA,KAAM,CAAAC,GAAG,IAAAC,MAAA,CAAMb,QAAQ,MAAAa,MAAA,CAAI7C,KAAK,CAAE,CAClCzD,aAAa,CAACkF,IAAI,EAAAqB,aAAA,CAAAA,aAAA,IAAUrB,IAAI,MAAE,CAACmB,GAAG,EAAG/F,KAAK,EAAG,CAAC,CAElD;AACA,GAAIL,cAAc,CAACmC,OAAO,CAAE,CAC1BC,YAAY,CAACpC,cAAc,CAACmC,OAAO,CAAC,CACtC,CAEA;AACAnC,cAAc,CAACmC,OAAO,CAAG6D,UAAU,CAAC,IAAM,CACxCT,aAAa,CAACC,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,CAAC,CACvC,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,CAED;AACA,KAAM,CAAAkG,cAAc,CAAG,KAAAA,CAAOf,QAAQ,CAAEhC,KAAK,GAAK,CAChD,KAAM,CAAA4C,GAAG,IAAAC,MAAA,CAAMb,QAAQ,MAAAa,MAAA,CAAI7C,KAAK,CAAE,CAClC,KAAM,CAAAnD,KAAK,CAAGP,UAAU,CAACsG,GAAG,CAAC,CAE7B,GAAI/F,KAAK,GAAKmG,SAAS,CAAE,CACvB;AACA,GAAIxG,cAAc,CAACmC,OAAO,CAAE,CAC1BC,YAAY,CAACpC,cAAc,CAACmC,OAAO,CAAC,CACpCnC,cAAc,CAACmC,OAAO,CAAG,IAAI,CAC/B,CAEA;AACA,KAAM,CAAAoD,aAAa,CAACC,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,CAAC,CAE3C;AACAN,aAAa,CAACkF,IAAI,EAAI,CACpB,KAAM,CAAAwB,OAAO,CAAAH,aAAA,IAAQrB,IAAI,CAAE,CAC3B,MAAO,CAAAwB,OAAO,CAACL,GAAG,CAAC,CACnB,MAAO,CAAAK,OAAO,CAChB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,KAAAA,CAAOlB,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,GAAK,CACvD6F,iBAAiB,CAACV,QAAQ,CAAEhC,KAAK,CAAEnD,KAAK,CAAC,CAC3C,CAAC,CAED,KAAM,CAAAsG,SAAS,CAAGA,CAACnB,QAAQ,CAAEhC,KAAK,GAAK,CACrC7D,cAAc,IAAA0G,MAAA,CAAIb,QAAQ,MAAAa,MAAA,CAAI7C,KAAK,CAAE,CAAC,CACxC,CAAC,CAED,KAAM,CAAAoD,UAAU,CAAGA,CAAA,GAAM,CACvBjH,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAkH,kBAAkB,CAAIxG,KAAK,EAAK,CACpC,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKmG,SAAS,EAAInG,KAAK,GAAK,EAAE,CAAE,MAAO,EAAE,CACpE,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACyG,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAzG,KAAK,CAAC0G,CAAC,CAC7C,GAAI1G,KAAK,CAACyG,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAzG,KAAK,CAAC2G,CAAC,CAC7C,GAAI3G,KAAK,CAACyG,cAAc,CAAC,GAAG,CAAC,EAAIzG,KAAK,CAACyG,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAzG,KAAK,CAAC0G,CAAC,CAC1E,GAAI1G,KAAK,CAAC4G,IAAI,GAAKT,SAAS,CAAE,MAAO,CAAAnG,KAAK,CAAC4G,IAAI,CAC/C,GAAI5G,KAAK,CAAC6G,QAAQ,GAAKV,SAAS,CAAE,MAAO,CAAAnG,KAAK,CAAC6G,QAAQ,CACvD,GAAI7G,KAAK,CAACA,KAAK,GAAKmG,SAAS,CAAE,MAAO,CAAAnG,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAwD,MAAM,CAACxD,KAAK,CAAC,CACtB,CACA,MAAO,CAAAwD,MAAM,CAACxD,KAAK,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAA8G,kBAAkB,CAAG,QAAAA,CAAC9G,KAAK,CAAEmF,QAAQ,CAAEhC,KAAK,CAAqB,IAAnB,CAAA4D,SAAS,CAAAC,SAAA,CAAAvE,MAAA,IAAAuE,SAAA,MAAAb,SAAA,CAAAa,SAAA,IAAG,EAAE,CAChE,KAAM,CAAAC,OAAO,IAAAjB,MAAA,CAAMb,QAAQ,MAAAa,MAAA,CAAI7C,KAAK,CAAE,CACtC,KAAM,CAAA+D,SAAS,CAAG7H,WAAW,GAAK4H,OAAO,CACzC,KAAM,CAAAE,YAAY,CAAGX,kBAAkB,CAACxG,KAAK,CAAC,CAE9C;AACA,KAAM,CAAAoH,eAAe,CAAGjE,KAAK,CAACgB,QAAQ,CAAC,MAAM,CAAC,CAC9C,KAAM,CAAAkD,UAAU,CAAG,CAAC,CAAC,IAAI,CAAC,CAAClD,QAAQ,CAAChB,KAAK,CAAC,EAAI,CAACiE,eAAe,CAE9D,GAAIF,SAAS,EAAIG,UAAU,CAAE,CAC3B,mBACE9I,IAAA,aACEyB,KAAK,CAAEmH,YAAa,CACpBG,QAAQ,CAAGC,CAAC,EAAK1B,iBAAiB,CAACV,QAAQ,CAAEhC,KAAK,CAAEoE,CAAC,CAACC,MAAM,CAACxH,KAAK,CAAE,CACpEyH,MAAM,CAAEA,CAAA,GAAM,CACZvB,cAAc,CAACf,QAAQ,CAAEhC,KAAK,CAAC,CAC/BoD,UAAU,CAAC,CAAC,CACd,CAAE,CACFmB,SAAS,CAAGH,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACxB,GAAG,GAAK,OAAO,EAAIwB,CAAC,CAACI,OAAO,CAAE,CAClCzB,cAAc,CAACf,QAAQ,CAAEhC,KAAK,CAAC,CAC/BoD,UAAU,CAAC,CAAC,CACd,CACA,GAAIgB,CAAC,CAACxB,GAAG,GAAK,QAAQ,CAAE,CACtBQ,UAAU,CAAC,CAAC,CACd,CACF,CAAE,CACFQ,SAAS,CAAC,YAAY,CACtBa,IAAI,CAAET,YAAY,CAAChD,QAAQ,CAAC,IAAI,CAAC,CAAGgD,YAAY,CAAClD,KAAK,CAAC,IAAI,CAAC,CAACxB,MAAM,CAAG,CAAC,CAAG,CAAE,CAC5EoF,SAAS,MACV,CAAC,CAEN,CAEA;AACA,GAAI,CAAAC,WAAW,iBAAA9B,MAAA,CAAmBe,SAAS,CAAE,CAC7C,GAAIK,eAAe,CAAE,CACnBU,WAAW,EAAI,gBAAgB,CACjC,CAAC,IAAM,IAAIT,UAAU,CAAE,CACrBS,WAAW,EAAI,gBAAgB,CACjC,CAEA;AACA,GAAI,CAAAC,SAAS,CAAG,EAAE,CAClB,GAAIX,eAAe,CAAE,CACnBW,SAAS,CAAG,QAAQ,CACtB,CAAC,IAAM,IAAIV,UAAU,CAAE,CACrBU,SAAS,CAAG,MAAM,CACpB,CAEA,mBACExJ,IAAA,SACEwI,SAAS,CAAEe,WAAY,CACvBE,OAAO,CAAEA,CAAA,GAAMX,UAAU,EAAIf,SAAS,CAACnB,QAAQ,CAAEhC,KAAK,CAAE,CACxD8E,KAAK,CAAEF,SAAU,CACjBG,KAAK,CAAEd,eAAe,CAAG,CAAEe,MAAM,CAAE,aAAc,CAAC,CAAG,CAAC,CAAE,CAAAC,QAAA,CAEvDjB,YAAY,CAAChD,QAAQ,CAAC,IAAI,CAAC,CAC1BgD,YAAY,CAAClD,KAAK,CAAC,IAAI,CAAC,CAACtB,GAAG,CAAC,CAAC0F,IAAI,CAAEC,CAAC,CAAEC,GAAG,gBACxC9J,KAAA,CAACX,KAAK,CAACY,QAAQ,EAAA0J,QAAA,EACZC,IAAI,CACJC,CAAC,CAAGC,GAAG,CAAC9F,MAAM,CAAG,CAAC,eAAIlE,IAAA,QAAK,CAAC,GAFV+J,CAGL,CACjB,CAAC,CACFnB,YAAY,CAEV,CAAC,CAEX,CAAC,CAED;AACA,KAAM,CAAAqB,kBAAkB,CAAGA,CAACC,GAAG,CAAEtD,QAAQ,GAAK,CAC5C,KAAM,CAACuD,MAAM,CAAEC,MAAM,CAAC,CAAGrH,UAAU,CAACf,gBAAgB,CAAC,CAErD,mBACE9B,KAAA,CAAAE,SAAA,EAAAyJ,QAAA,eACE7J,IAAA,OAAIwI,SAAS,CAAC,0BAA0B,CAAAqB,QAAA,CACrCtB,kBAAkB,CAAC2B,GAAG,IAAAzC,MAAA,CAAI0C,MAAM,6BAAO,CAAEvD,QAAQ,IAAAa,MAAA,CAAK0C,MAAM,6BAAQ,YAAY,CAAC,CAChF,CAAC,cACLnK,IAAA,OAAIwI,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CACzCtB,kBAAkB,CAAC2B,GAAG,IAAAzC,MAAA,CAAI0C,MAAM,6BAAO,CAAEvD,QAAQ,IAAAa,MAAA,CAAK0C,MAAM,6BAAQ,gBAAgB,CAAC,CACpF,CAAC,cACLnK,IAAA,OAAIwI,SAAS,CAAC,0BAA0B,CAAAqB,QAAA,CACrCtB,kBAAkB,CAAC2B,GAAG,IAAAzC,MAAA,CAAI2C,MAAM,6BAAO,CAAExD,QAAQ,IAAAa,MAAA,CAAK2C,MAAM,6BAAQ,YAAY,CAAC,CAChF,CAAC,cACLpK,IAAA,OAAIwI,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CACzCtB,kBAAkB,CAAC2B,GAAG,IAAAzC,MAAA,CAAI2C,MAAM,6BAAO,CAAExD,QAAQ,IAAAa,MAAA,CAAK2C,MAAM,6BAAQ,gBAAgB,CAAC,CACpF,CAAC,EACL,CAAC,CAEP,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,KAAO,CAAAC,aAAa,EAAK,CAC9CnH,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACFW,OAAO,CAACC,GAAG,CAAC,eAAe,CAAEuG,aAAa,CAAC,CAE3C;AACA,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA1K,yBAAyB,CAAC2K,uBAAuB,CAACF,aAAa,CAAC,CAErF,GAAIC,MAAM,CAACE,OAAO,CAAE,CAClB3G,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEwG,MAAM,CAACG,OAAO,CAAC,CACpCC,KAAK,8BAAAlD,MAAA,CAAU8C,MAAM,CAACG,OAAO,CAAE,CAAC,CAClC,CAAC,IAAM,CACL,KAAM,IAAI,CAAAE,KAAK,CAACL,MAAM,CAACG,OAAO,EAAI,MAAM,CAAC,CAC3C,CACF,CAAE,MAAOpH,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BqH,KAAK,CAAC,QAAQ,CAAGrH,KAAK,CAACoH,OAAO,CAAC,CACjC,CAAC,OAAS,CACRvH,kBAAkB,CAAC,KAAK,CAAC,CACzBF,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CACF,CAAC,CAED,KAAM,CAAA4H,iBAAiB,CAAGA,CAAA,GAAM,CAC9B5H,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED,KAAM,CAAA6H,kBAAkB,CAAGA,CAAA,GAAM,CAC/B7H,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAED;AACA,KAAM,CAAA8H,4BAA4B,CAAGA,CAAA,GAAM,CACzC,GAAI,CAACjJ,qBAAqB,CAAE,MAAO,KAAI,CACvC,KAAM,CAAAkJ,UAAU,CAAG7F,wBAAwB,CAAC,CAAC,CAE7C,mBACEjF,KAAA,QAAKsI,SAAS,CAAC,sCAAsC,CAAAqB,QAAA,eACnD3J,KAAA,QAAKsI,SAAS,CAAC,iCAAiC,CAAAqB,QAAA,eAC9C7J,IAAA,OAAA6J,QAAA,CAAI,gCAAK,CAAI,CAAC,cACd7J,IAAA,WAAQyJ,OAAO,CAAEtD,uBAAwB,CAACqC,SAAS,CAAC,6BAA6B,CAAAqB,QAAA,CAAC,MAAC,CAAQ,CAAC,EACzF,CAAC,cACN3J,KAAA,QAAKsI,SAAS,CAAC,kCAAkC,CAAAqB,QAAA,eAC/C7J,IAAA,QAAKwI,SAAS,CAAC,4BAA4B,CAAAqB,QAAA,CACxCmB,UAAU,CAAC5G,GAAG,CAACkB,MAAM,eACpBtF,IAAA,QAEEwI,SAAS,8BAAAf,MAAA,CAA+B7F,mBAAmB,CAACgE,QAAQ,CAACN,MAAM,CAAC,CAAG,UAAU,CAAG,EAAE,CAAG,CACjGmE,OAAO,CAAEA,CAAA,GAAMrD,6BAA6B,CAACd,MAAM,CAAE,CAAAuE,QAAA,CAEpDvE,MAAM,EAJFA,MAKF,CACN,CAAC,CACC,CAAC,cACNpF,KAAA,QAAKsI,SAAS,CAAC,4BAA4B,CAAAqB,QAAA,eACzC7J,IAAA,WAAQyJ,OAAO,CAAEnD,yBAA0B,CAACkC,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CAAC,0BAErF,CAAQ,CAAC,cACT7J,IAAA,WAAQyJ,OAAO,CAAElD,sBAAuB,CAACiC,SAAS,CAAC,8BAA8B,CAAAqB,QAAA,CAAC,0BAElF,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,GAAIjJ,OAAO,CAAE,CACX,mBACEZ,IAAA,QAAKwI,SAAS,CAAC,uBAAuB,CAAAqB,QAAA,cACpC3J,KAAA,QAAKsI,SAAS,CAAC,qBAAqB,CAAAqB,QAAA,eAClC7J,IAAA,QAAKwI,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCxI,IAAA,MAAA6J,QAAA,CAAG,6EAAe,CAAG,CAAC,EACnB,CAAC,CACH,CAAC,CAEV,CAEA,KAAM,CAAAoB,WAAW,CAAGvK,YAAY,CAACwD,MAAM,CAAG,CAAC,CAAGxD,YAAY,CAAGF,IAAI,CACjE,KAAM,CAAC2J,MAAM,CAAEC,MAAM,CAAC,CAAGrH,UAAU,CAACf,gBAAgB,CAAC,CAErD;AACA8B,OAAO,CAACC,GAAG,CAAC,SAAS,CAAE,CACrBmH,MAAM,CAAE1K,IAAI,CAAC0D,MAAM,CACnBiH,MAAM,CAAEzK,YAAY,CAACwD,MAAM,CAC3BkH,MAAM,CAAEH,WAAW,CAAC/G,MAAM,CAC1BmH,KAAK,IAAA5D,MAAA,CAAK0C,MAAM,MAAA1C,MAAA,CAAI2C,MAAM,CAC5B,CAAC,CAAC,CAEF,mBACElK,KAAA,QAAKsI,SAAS,CAAC,uBAAuB,CAAAqB,QAAA,EAEnCkB,4BAA4B,CAAC,CAAC,CAG9BrJ,0BAA0B,CAACwC,MAAM,CAAG,CAAC,eACpClE,IAAA,QACE2J,KAAK,CAAE,CACL2B,UAAU,CAAE,yEAAyE,CACrFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,WAAW,CACnBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,cAAc,CAAE,YAAY,CAC5BC,SAAS,CAAE,mCACb,CAAE,CAAAhC,QAAA,cAEF3J,KAAA,QACEyJ,KAAK,CAAE,CACLmC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,WAAW,CACpBC,GAAG,CAAE,MACP,CAAE,CAAApC,QAAA,eAEF7J,IAAA,QACE2J,KAAK,CAAE,CACLmC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,cAAc,CAAE,QAAQ,CACxBR,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,MAAM,CACdb,UAAU,CAAE,0CAA0C,CACtDE,YAAY,CAAE,KAAK,CACnBY,QAAQ,CAAE,MACZ,CAAE,CAAAvC,QAAA,CACH,cAED,CAAK,CAAC,cACN3J,KAAA,QAAKyJ,KAAK,CAAE,CAAE0C,IAAI,CAAE,CAAE,CAAE,CAAAxC,QAAA,eACtB7J,IAAA,QACE2J,KAAK,CAAE,CACLyC,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,0BAA0B,CACjCC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KACd,CAAE,CAAA3C,QAAA,CACH,sCAED,CAAK,CAAC,cACN3J,KAAA,QACEyJ,KAAK,CAAE,CACLyC,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,KACd,CAAE,CAAA3C,QAAA,EACH,0BAEC,cAAA7J,IAAA,SAAM2J,KAAK,CAAE,CACX2C,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,MAAM,CAClBC,UAAU,CAAE,iCAAiC,CAC7ChB,MAAM,CAAE,OACV,CAAE,CAAA5B,QAAA,CACCnI,0BAA0B,CAACgL,IAAI,CAAC,GAAG,CAAC,CACjC,CAAC,cACPxM,KAAA,SAAMyJ,KAAK,CAAE,CACX2C,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,MACZ,CAAE,CAAAvC,QAAA,EAAC,QACA,CAACnJ,YAAY,CAACwD,MAAM,CAAC,0BACxB,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,cACNhE,KAAA,WACEyJ,KAAK,CAAE,CACLmC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,KAAK,CACVD,OAAO,CAAE,UAAU,CACnBV,UAAU,CAAE,0EAA0E,CACtFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,KAAK,CACnBc,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,KAAK,CACjB5C,MAAM,CAAE,SAAS,CACjB+C,UAAU,CAAE,eACd,CAAE,CACFlD,OAAO,CAAEjD,sBAAuB,CAChCoG,YAAY,CAAG5D,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACU,KAAK,CAAC2B,UAAU,CAAG,0EAA0E,CACtGtC,CAAC,CAACC,MAAM,CAACU,KAAK,CAACkD,SAAS,CAAG,kBAAkB,CAC/C,CAAE,CACFC,YAAY,CAAG9D,CAAC,EAAK,CACnBA,CAAC,CAACC,MAAM,CAACU,KAAK,CAAC2B,UAAU,CAAG,0EAA0E,CACtGtC,CAAC,CAACC,MAAM,CAACU,KAAK,CAACkD,SAAS,CAAG,eAAe,CAC5C,CAAE,CACFnD,KAAK,CAAC,sCAAQ,CAAAG,QAAA,eAEd7J,IAAA,SAAA6J,QAAA,CAAM,QAAC,CAAM,CAAC,cACd7J,IAAA,SAAA6J,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,EACN,CAAC,CACH,CACN,cAED3J,KAAA,QAAKsI,SAAS,CAAC,aAAa,CAAAqB,QAAA,eAC1B7J,IAAA,WACEwI,SAAS,CAAC,cAAc,CACxBiB,OAAO,CAAEA,CAAA,GAAMlJ,UAAU,CAAC,MAAM,CAAE,CAAAsJ,QAAA,CACnC,0BAED,CAAQ,CAAC,cAET3J,KAAA,QAAKsI,SAAS,CAAC,eAAe,CAAAqB,QAAA,eAC5B7J,IAAA,OAAIwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,qEAAY,CAAI,CAAC,cAC5C7J,IAAA,MAAGwI,SAAS,CAAC,eAAe,CAAAqB,QAAA,CAAC,8FAAgB,CAAG,CAAC,EAC9C,CAAC,cAEN3J,KAAA,QAAKsI,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,eAC7B7J,IAAA,WAAQwI,SAAS,CAAC,iBAAiB,CAACiB,OAAO,CAAE/E,YAAa,CAAAmF,QAAA,CAAC,uCAE3D,CAAQ,CAAC,cACT7J,IAAA,QAAKwI,SAAS,CAAC,aAAa,CAAAqB,QAAA,cAC1B7J,IAAA,SACEwI,SAAS,qBAAAf,MAAA,CAAsBzG,UAAU,CAAC4E,QAAQ,CAAC,IAAI,CAAC,CAAG,SAAS,CAAG5E,UAAU,CAAC4E,QAAQ,CAAC,IAAI,CAAC,CAAG,OAAO,CAAG,SAAS,CAAG,CAAAiE,QAAA,CAExH7I,UAAU,CACP,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNd,KAAA,QAAKsI,SAAS,CAAC,eAAe,CAAAqB,QAAA,eAC5B7J,IAAA,QAAKwI,SAAS,CAAC,eAAe,CAAAqB,QAAA,cAC5B3J,KAAA,QAAKsI,SAAS,CAAC,iBAAiB,CAAAqB,QAAA,eAC9B3J,KAAA,QAAKsI,SAAS,CAAC,cAAc,CAAAqB,QAAA,eAC3B7J,IAAA,UAAA6J,QAAA,CAAO,2BAAK,CAAO,CAAC,cACpB3J,KAAA,WACEsI,SAAS,CAAC,gBAAgB,CAC1B/G,KAAK,CAAEH,WAAW,CAACE,KAAM,CACzBuH,QAAQ,CAAGC,CAAC,EAAK,CACfzH,cAAc,CAAC,CACbC,KAAK,CAAEwH,CAAC,CAACC,MAAM,CAACxH,KAAK,CACrBA,KAAK,CAAE,IACT,CAAC,CAAC,CACJ,CAAE,CAAAoI,QAAA,eAEF7J,IAAA,WAAQyB,KAAK,CAAC,cAAI,CAAAoI,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAChC7J,IAAA,WAAQyB,KAAK,CAAC,cAAI,CAAAoI,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAChC7J,IAAA,WAAQyB,KAAK,CAAC,cAAI,CAAAoI,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC1B,CAAC,EACN,CAAC,cAEN3J,KAAA,QAAKsI,SAAS,CAAC,cAAc,CAAAqB,QAAA,eAC3B7J,IAAA,UAAA6J,QAAA,CAAO,2BAAK,CAAO,CAAC,cACpB7J,IAAA,WACEwI,SAAS,CAAC,gBAAgB,CAC1B/G,KAAK,CAAEH,WAAW,CAACG,KAAM,CACzBsH,QAAQ,CAAGC,CAAC,EAAK,CACfzH,cAAc,CAAAmG,aAAA,CAAAA,aAAA,IACTpG,WAAW,MACdG,KAAK,CAAEuH,CAAC,CAACC,MAAM,CAACxH,KAAK,EACtB,CAAC,CACJ,CAAE,CAAAoI,QAAA,CAEDlF,eAAe,CAAC,CAAC,CAACP,GAAG,CAAC2I,MAAM,eAC3B/M,IAAA,WAAqByB,KAAK,CAAEsL,MAAO,CAAAlD,QAAA,CAAEkD,MAAM,EAA9BA,MAAuC,CACrD,CAAC,CACI,CAAC,EACN,CAAC,cAEN7M,KAAA,QAAKsI,SAAS,CAAC,kBAAkB,CAAAqB,QAAA,eAC/B7J,IAAA,SAAMwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzC3J,KAAA,SAAMsI,SAAS,CAAC,YAAY,CAAAqB,QAAA,EAAEoB,WAAW,CAAC/G,MAAM,CAAC,SAAE,EAAM,CAAC,EACvD,CAAC,cAENhE,KAAA,QACEsI,SAAS,CAAC,4BAA4B,CACtCiB,OAAO,CAAEtD,uBAAwB,CAAA0D,QAAA,eAEjC7J,IAAA,SAAMwI,SAAS,CAAC,aAAa,CAAAqB,QAAA,CAAC,cAAE,CAAM,CAAC,CACtCnI,0BAA0B,CAACwC,MAAM,CAAG,CAAC,eACpClE,IAAA,QAAKwI,SAAS,CAAC,cAAc,CAAAqB,QAAA,CAC1BnI,0BAA0B,CAACwC,MAAM,CAC/B,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAENhE,KAAA,QAAKsI,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,eAC7B3J,KAAA,QAAKsI,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,eACnC7J,IAAA,WACEwI,SAAS,CAAC,aAAa,CACvBiB,OAAO,CAAEA,CAAA,GAAMhD,aAAa,CAAC,MAAM,CAAE,CACrCuG,QAAQ,CAAEhL,gBAAgB,GAAK,CAAE,CAAA6H,QAAA,CAClC,sCAED,CAAQ,CAAC,cACT3J,KAAA,SAAMsI,SAAS,CAAC,oBAAoB,CAAAqB,QAAA,EACjCM,MAAM,CAAC,KAAG,CAACC,MAAM,EACd,CAAC,cACPpK,IAAA,WACEwI,SAAS,CAAC,aAAa,CACvBiB,OAAO,CAAEA,CAAA,GAAMhD,aAAa,CAAC,MAAM,CAAE,CACrCuG,QAAQ,CAAEhL,gBAAgB,GAAKe,UAAU,CAACmB,MAAM,CAAG,CAAE,CAAA2F,QAAA,CACtD,sCAED,CAAQ,CAAC,EACN,CAAC,cACN7J,IAAA,WACEwI,SAAS,CAAC,cAAc,CACxBiB,OAAO,CAAEoB,iBAAkB,CAAAhB,QAAA,CAC5B,6CAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGN7J,IAAA,QAAKwI,SAAS,CAAC,iBAAiB,CAAAqB,QAAA,cAC9B3J,KAAA,UAAOsI,SAAS,CAAC,mBAAmB,CAAAqB,QAAA,eAClC7J,IAAA,UAAA6J,QAAA,cACE3J,KAAA,OAAA2J,QAAA,eACE7J,IAAA,OAAIwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,cAAE,CAAI,CAAC,cAClC7J,IAAA,OAAIwI,SAAS,CAAC,cAAc,CAAAqB,QAAA,CAAC,0BAAI,CAAI,CAAC,cACtC7J,IAAA,OAAIwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,wBAAO,CAAI,CAAC,cACvC7J,IAAA,OAAIwI,SAAS,CAAC,aAAa,CAAAqB,QAAA,CAAC,wBAAO,CAAI,CAAC,cACxC7J,IAAA,OAAIwI,SAAS,CAAC,iBAAiB,CAAAqB,QAAA,CAAC,oBAAG,CAAI,CAAC,cACxC7J,IAAA,OAAIwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,cAAE,CAAI,CAAC,cAClC3J,KAAA,OAAIsI,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,EAAEM,MAAM,CAAC,0BAAI,EAAI,CAAC,cAChDjK,KAAA,OAAIsI,SAAS,CAAC,oBAAoB,CAAAqB,QAAA,EAAEM,MAAM,CAAC,0BAAI,EAAI,CAAC,cACpDjK,KAAA,OAAIsI,SAAS,CAAC,gBAAgB,CAAAqB,QAAA,EAAEO,MAAM,CAAC,0BAAI,EAAI,CAAC,cAChDlK,KAAA,OAAIsI,SAAS,CAAC,oBAAoB,CAAAqB,QAAA,EAAEO,MAAM,CAAC,0BAAI,EAAI,CAAC,cACpDpK,IAAA,OAAIwI,SAAS,CAAC,YAAY,CAAAqB,QAAA,CAAC,cAAE,CAAI,CAAC,EAChC,CAAC,CACA,CAAC,cACR7J,IAAA,UAAA6J,QAAA,CACGoB,WAAW,CAAC7G,GAAG,CAAC,CAAC8F,GAAG,CAAEzH,KAAK,gBAC1BvC,KAAA,OAAgBsI,SAAS,CAAC,UAAU,CAAAqB,QAAA,eAClC7J,IAAA,OAAIwI,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,CACjCpH,KAAK,CAAG,CAAC,CACR,CAAC,cACLzC,IAAA,OAAIwI,SAAS,CAAC,wBAAwB,CAAAqB,QAAA,CACnCtB,kBAAkB,CAAC2B,GAAG,CAAC3F,IAAI,CAAE9B,KAAK,CAAE,MAAM,CAAC,CAC1C,CAAC,cACLzC,IAAA,OAAIwI,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,CACjCtB,kBAAkB,CAAC2B,GAAG,CAAC,SAAS,CAAC,CAAEzH,KAAK,CAAE,SAAS,CAAC,CACnD,CAAC,cACLzC,IAAA,OAAIwI,SAAS,CAAC,uBAAuB,CAAAqB,QAAA,CAClCtB,kBAAkB,CAAC2B,GAAG,CAAC,SAAS,CAAC,CAAEzH,KAAK,CAAE,SAAS,CAAC,CACnD,CAAC,cACLzC,IAAA,OAAIwI,SAAS,CAAC,2BAA2B,CAAAqB,QAAA,CACtCtB,kBAAkB,CAAC2B,GAAG,CAAC1F,GAAG,CAAE/B,KAAK,CAAE,KAAK,CAAC,CACxC,CAAC,cACLzC,IAAA,OAAIwI,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,CACjCtB,kBAAkB,CAAC2B,GAAG,CAAC+C,EAAE,CAAExK,KAAK,CAAE,IAAI,CAAC,CACtC,CAAC,CACJwH,kBAAkB,CAACC,GAAG,CAAEzH,KAAK,CAAC,cAC/BzC,IAAA,OAAIwI,SAAS,CAAC,sBAAsB,CAAAqB,QAAA,CACjCtB,kBAAkB,CAAC2B,GAAG,CAACgD,EAAE,CAAEzK,KAAK,CAAE,IAAI,CAAC,CACtC,CAAC,GAtBEA,KAuBL,CACL,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAELwI,WAAW,CAAC/G,MAAM,GAAK,CAAC,eACvBlE,IAAA,QAAKwI,SAAS,CAAC,SAAS,CAAAqB,QAAA,cACtB7J,IAAA,MAAA6J,QAAA,CAAG,oEAAW,CAAG,CAAC,CACf,CACN,cAGD7J,IAAA,CAACF,uBAAuB,EACtBqN,MAAM,CAAEnK,iBAAkB,CAC1BoK,OAAO,CAAEtC,kBAAmB,CAC5BtK,IAAI,CAAEyK,WAAY,CAClBvK,YAAY,CAAEA,YAAa,CAC3BY,WAAW,CAAEA,WAAY,CACzBU,gBAAgB,CAAEA,gBAAiB,CACnCe,UAAU,CAAEA,UAAW,CACvBsK,UAAU,CAAEhD,cAAe,CAC5B,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}