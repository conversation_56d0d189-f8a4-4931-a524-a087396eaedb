{"ast": null, "code": "// 路由守卫 - 控制页面访问权限\nimport authService from'../services/authService';import{canAccessPage,ROLES}from'./rolePermissions';class RouteGuard{constructor(){this.redirectCallbacks=[];}// 注册重定向回调\nonRedirect(callback){this.redirectCallbacks.push(callback);}// 触发重定向\ntriggerRedirect(targetPage){let reason=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';this.redirectCallbacks.forEach(callback=>{callback(targetPage,reason);});}// 检查页面访问权限\ncanAccess(pageName){// 检查用户是否已登录\nif(!authService.isLoggedIn()){return{allowed:false,reason:'not_authenticated',message:'请先登录系统'};}const userRole=authService.getUserRole();// 检查角色权限\nif(!canAccessPage(userRole,pageName)){return{allowed:false,reason:'insufficient_permissions',message:'您没有访问此页面的权限'};}return{allowed:true,reason:'authorized',message:'访问已授权'};}// 页面访问前的守卫检查\nbeforePageAccess(pageName,onSuccess,onFailure){const accessResult=this.canAccess(pageName);if(accessResult.allowed){// 刷新会话\nauthService.refreshSession();if(onSuccess){onSuccess(accessResult);}return true;}else{if(onFailure){onFailure(accessResult);}// 根据失败原因进行不同处理\nif(accessResult.reason==='not_authenticated'){this.triggerRedirect('login',accessResult.message);}else if(accessResult.reason==='insufficient_permissions'){this.triggerRedirect('unauthorized',accessResult.message);}return false;}}// 获取用户可访问的导航菜单\ngetAccessibleNavigation(){if(!authService.isLoggedIn()){return[];}const userRole=authService.getUserRole();const allNavItems=[{id:'home',title:'首页',icon:'🏠',page:'home'},{id:'work-target',title:'工作目标管理责任书',icon:'🎯',page:'work-target'},{id:'work-tracking',title:'重点工作跟踪-填写表',icon:'📊',page:'work-tracking'},{id:'world-class',title:'对标世界一流举措-提报版',icon:'🌍',page:'world-class'},{id:'monthly-kpi',title:'月度重点KPI',icon:'📈',page:'monthly-kpi'},{id:'project-one',title:'项目管理',icon:'📋',page:'project-one'},{id:'module-six',title:'数据分析',icon:'📊',page:'module-six'},{id:'department',title:'部门管理',icon:'👥',page:'department'},{id:'user-management',title:'用户管理',icon:'👤',page:'user-management'},{id:'personal-settings',title:'个人设置',icon:'⚙️',page:'personal-settings'}];// 过滤用户可访问的菜单项\nreturn allNavItems.filter(item=>{const accessResult=this.canAccess(item.page);return accessResult.allowed;});}// 检查按钮权限\ncheckButtonPermission(buttonType){let userRole=arguments.length>1&&arguments[1]!==undefined?arguments[1]:null;const role=userRole||authService.getUserRole();if(!role)return false;switch(buttonType){case'change_file':return role===ROLES.SUPER_ADMIN||role===ROLES.MANAGER;case'import_data':return role===ROLES.SUPER_ADMIN||role===ROLES.MANAGER;case'export_data':return role===ROLES.SUPER_ADMIN||role===ROLES.MANAGER||role===ROLES.DESIGNER;case'delete_project':return role===ROLES.SUPER_ADMIN;case'create_user':return role===ROLES.SUPER_ADMIN;case'modify_user':return role===ROLES.SUPER_ADMIN;case'delete_user':return role===ROLES.SUPER_ADMIN;case'system_settings':return role===ROLES.SUPER_ADMIN;default:return true;}}// 获取权限受限的提示信息\ngetPermissionDeniedMessage(buttonType){const messages={'change_file':'只有超级管理员和部长可以更换文件','import_data':'只有超级管理员和部长可以导入数据','export_data':'您没有导出数据的权限','delete_project':'只有超级管理员可以删除项目','create_user':'只有超级管理员可以创建用户','modify_user':'只有超级管理员可以修改用户','delete_user':'只有超级管理员可以删除用户','system_settings':'只有超级管理员可以访问系统设置'};return messages[buttonType]||'您没有执行此操作的权限';}// 页面级权限检查中间件\ncreatePageGuard(pageName){return next=>{return this.beforePageAccess(pageName,()=>next(),// 成功回调\nresult=>{console.warn(\"\\u9875\\u9762\\u8BBF\\u95EE\\u88AB\\u62D2\\u7EDD: \".concat(pageName),result);// 可以在这里显示错误提示\nif(window.showNotification){window.showNotification(result.message,'error');}});};}// 重置守卫状态\nreset(){this.redirectCallbacks=[];}}// 创建单例实例\nconst routeGuard=new RouteGuard();export default routeGuard;", "map": {"version": 3, "names": ["authService", "canAccessPage", "ROLES", "RouteGuard", "constructor", "redirectCallbacks", "onRedirect", "callback", "push", "triggerRedirect", "targetPage", "reason", "arguments", "length", "undefined", "for<PERSON>ach", "canAccess", "pageName", "isLoggedIn", "allowed", "message", "userRole", "getUserRole", "beforePageAccess", "onSuccess", "onFailure", "accessResult", "refreshSession", "getAccessibleNavigation", "allNavItems", "id", "title", "icon", "page", "filter", "item", "checkButtonPermission", "buttonType", "role", "SUPER_ADMIN", "MANAGER", "DESIGNER", "getPermissionDeniedMessage", "messages", "createPageGuard", "next", "result", "console", "warn", "concat", "window", "showNotification", "reset", "routeGuard"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/utils/routeGuards.js"], "sourcesContent": ["// 路由守卫 - 控制页面访问权限\r\n\r\nimport authService from '../services/authService';\r\nimport { canAccessPage, ROLES } from './rolePermissions';\r\n\r\nclass RouteGuard {\r\n  constructor() {\r\n    this.redirectCallbacks = [];\r\n  }\r\n\r\n  // 注册重定向回调\r\n  onRedirect(callback) {\r\n    this.redirectCallbacks.push(callback);\r\n  }\r\n\r\n  // 触发重定向\r\n  triggerRedirect(targetPage, reason = '') {\r\n    this.redirectCallbacks.forEach(callback => {\r\n      callback(targetPage, reason);\r\n    });\r\n  }\r\n\r\n  // 检查页面访问权限\r\n  canAccess(pageName) {\r\n    // 检查用户是否已登录\r\n    if (!authService.isLoggedIn()) {\r\n      return {\r\n        allowed: false,\r\n        reason: 'not_authenticated',\r\n        message: '请先登录系统'\r\n      };\r\n    }\r\n\r\n    const userRole = authService.getUserRole();\r\n    \r\n    // 检查角色权限\r\n    if (!canAccessPage(userRole, pageName)) {\r\n      return {\r\n        allowed: false,\r\n        reason: 'insufficient_permissions',\r\n        message: '您没有访问此页面的权限'\r\n      };\r\n    }\r\n\r\n    return {\r\n      allowed: true,\r\n      reason: 'authorized',\r\n      message: '访问已授权'\r\n    };\r\n  }\r\n\r\n  // 页面访问前的守卫检查\r\n  beforePageAccess(pageName, onSuccess, onFailure) {\r\n    const accessResult = this.canAccess(pageName);\r\n    \r\n    if (accessResult.allowed) {\r\n      // 刷新会话\r\n      authService.refreshSession();\r\n      \r\n      if (onSuccess) {\r\n        onSuccess(accessResult);\r\n      }\r\n      return true;\r\n    } else {\r\n      if (onFailure) {\r\n        onFailure(accessResult);\r\n      }\r\n      \r\n      // 根据失败原因进行不同处理\r\n      if (accessResult.reason === 'not_authenticated') {\r\n        this.triggerRedirect('login', accessResult.message);\r\n      } else if (accessResult.reason === 'insufficient_permissions') {\r\n        this.triggerRedirect('unauthorized', accessResult.message);\r\n      }\r\n      \r\n      return false;\r\n    }\r\n  }\r\n\r\n  // 获取用户可访问的导航菜单\r\n  getAccessibleNavigation() {\r\n    if (!authService.isLoggedIn()) {\r\n      return [];\r\n    }\r\n\r\n    const userRole = authService.getUserRole();\r\n    const allNavItems = [\r\n      {\r\n        id: 'home',\r\n        title: '首页',\r\n        icon: '🏠',\r\n        page: 'home'\r\n      },\r\n      {\r\n        id: 'work-target',\r\n        title: '工作目标管理责任书',\r\n        icon: '🎯',\r\n        page: 'work-target'\r\n      },\r\n      {\r\n        id: 'work-tracking',\r\n        title: '重点工作跟踪-填写表',\r\n        icon: '📊',\r\n        page: 'work-tracking'\r\n      },\r\n      {\r\n        id: 'world-class',\r\n        title: '对标世界一流举措-提报版',\r\n        icon: '🌍',\r\n        page: 'world-class'\r\n      },\r\n      {\r\n        id: 'monthly-kpi',\r\n        title: '月度重点KPI',\r\n        icon: '📈',\r\n        page: 'monthly-kpi'\r\n      },\r\n      {\r\n        id: 'project-one',\r\n        title: '项目管理',\r\n        icon: '📋',\r\n        page: 'project-one'\r\n      },\r\n      {\r\n        id: 'module-six',\r\n        title: '数据分析',\r\n        icon: '📊',\r\n        page: 'module-six'\r\n      },\r\n      {\r\n        id: 'department',\r\n        title: '部门管理',\r\n        icon: '👥',\r\n        page: 'department'\r\n      },\r\n      {\r\n        id: 'user-management',\r\n        title: '用户管理',\r\n        icon: '👤',\r\n        page: 'user-management'\r\n      },\r\n      {\r\n        id: 'personal-settings',\r\n        title: '个人设置',\r\n        icon: '⚙️',\r\n        page: 'personal-settings'\r\n      }\r\n    ];\r\n\r\n    // 过滤用户可访问的菜单项\r\n    return allNavItems.filter(item => {\r\n      const accessResult = this.canAccess(item.page);\r\n      return accessResult.allowed;\r\n    });\r\n  }\r\n\r\n  // 检查按钮权限\r\n  checkButtonPermission(buttonType, userRole = null) {\r\n    const role = userRole || authService.getUserRole();\r\n    if (!role) return false;\r\n\r\n    switch (buttonType) {\r\n      case 'change_file':\r\n        return role === ROLES.SUPER_ADMIN || role === ROLES.MANAGER;\r\n      \r\n      case 'import_data':\r\n        return role === ROLES.SUPER_ADMIN || role === ROLES.MANAGER;\r\n      \r\n      case 'export_data':\r\n        return role === ROLES.SUPER_ADMIN || role === ROLES.MANAGER || role === ROLES.DESIGNER;\r\n      \r\n      case 'delete_project':\r\n        return role === ROLES.SUPER_ADMIN;\r\n      \r\n      case 'create_user':\r\n        return role === ROLES.SUPER_ADMIN;\r\n      \r\n      case 'modify_user':\r\n        return role === ROLES.SUPER_ADMIN;\r\n      \r\n      case 'delete_user':\r\n        return role === ROLES.SUPER_ADMIN;\r\n      \r\n      case 'system_settings':\r\n        return role === ROLES.SUPER_ADMIN;\r\n      \r\n      default:\r\n        return true;\r\n    }\r\n  }\r\n\r\n  // 获取权限受限的提示信息\r\n  getPermissionDeniedMessage(buttonType) {\r\n    const messages = {\r\n      'change_file': '只有超级管理员和部长可以更换文件',\r\n      'import_data': '只有超级管理员和部长可以导入数据',\r\n      'export_data': '您没有导出数据的权限',\r\n      'delete_project': '只有超级管理员可以删除项目',\r\n      'create_user': '只有超级管理员可以创建用户',\r\n      'modify_user': '只有超级管理员可以修改用户',\r\n      'delete_user': '只有超级管理员可以删除用户',\r\n      'system_settings': '只有超级管理员可以访问系统设置'\r\n    };\r\n\r\n    return messages[buttonType] || '您没有执行此操作的权限';\r\n  }\r\n\r\n  // 页面级权限检查中间件\r\n  createPageGuard(pageName) {\r\n    return (next) => {\r\n      return this.beforePageAccess(\r\n        pageName,\r\n        () => next(), // 成功回调\r\n        (result) => {\r\n          console.warn(`页面访问被拒绝: ${pageName}`, result);\r\n          // 可以在这里显示错误提示\r\n          if (window.showNotification) {\r\n            window.showNotification(result.message, 'error');\r\n          }\r\n        }\r\n      );\r\n    };\r\n  }\r\n\r\n  // 重置守卫状态\r\n  reset() {\r\n    this.redirectCallbacks = [];\r\n  }\r\n}\r\n\r\n// 创建单例实例\r\nconst routeGuard = new RouteGuard();\r\n\r\nexport default routeGuard;\r\n"], "mappings": "AAAA;AAEA,MAAO,CAAAA,WAAW,KAAM,yBAAyB,CACjD,OAASC,aAAa,CAAEC,KAAK,KAAQ,mBAAmB,CAExD,KAAM,CAAAC,UAAW,CACfC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,iBAAiB,CAAG,EAAE,CAC7B,CAEA;AACAC,UAAUA,CAACC,QAAQ,CAAE,CACnB,IAAI,CAACF,iBAAiB,CAACG,IAAI,CAACD,QAAQ,CAAC,CACvC,CAEA;AACAE,eAAeA,CAACC,UAAU,CAAe,IAAb,CAAAC,MAAM,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrC,IAAI,CAACP,iBAAiB,CAACU,OAAO,CAACR,QAAQ,EAAI,CACzCA,QAAQ,CAACG,UAAU,CAAEC,MAAM,CAAC,CAC9B,CAAC,CAAC,CACJ,CAEA;AACAK,SAASA,CAACC,QAAQ,CAAE,CAClB;AACA,GAAI,CAACjB,WAAW,CAACkB,UAAU,CAAC,CAAC,CAAE,CAC7B,MAAO,CACLC,OAAO,CAAE,KAAK,CACdR,MAAM,CAAE,mBAAmB,CAC3BS,OAAO,CAAE,QACX,CAAC,CACH,CAEA,KAAM,CAAAC,QAAQ,CAAGrB,WAAW,CAACsB,WAAW,CAAC,CAAC,CAE1C;AACA,GAAI,CAACrB,aAAa,CAACoB,QAAQ,CAAEJ,QAAQ,CAAC,CAAE,CACtC,MAAO,CACLE,OAAO,CAAE,KAAK,CACdR,MAAM,CAAE,0BAA0B,CAClCS,OAAO,CAAE,aACX,CAAC,CACH,CAEA,MAAO,CACLD,OAAO,CAAE,IAAI,CACbR,MAAM,CAAE,YAAY,CACpBS,OAAO,CAAE,OACX,CAAC,CACH,CAEA;AACAG,gBAAgBA,CAACN,QAAQ,CAAEO,SAAS,CAAEC,SAAS,CAAE,CAC/C,KAAM,CAAAC,YAAY,CAAG,IAAI,CAACV,SAAS,CAACC,QAAQ,CAAC,CAE7C,GAAIS,YAAY,CAACP,OAAO,CAAE,CACxB;AACAnB,WAAW,CAAC2B,cAAc,CAAC,CAAC,CAE5B,GAAIH,SAAS,CAAE,CACbA,SAAS,CAACE,YAAY,CAAC,CACzB,CACA,MAAO,KAAI,CACb,CAAC,IAAM,CACL,GAAID,SAAS,CAAE,CACbA,SAAS,CAACC,YAAY,CAAC,CACzB,CAEA;AACA,GAAIA,YAAY,CAACf,MAAM,GAAK,mBAAmB,CAAE,CAC/C,IAAI,CAACF,eAAe,CAAC,OAAO,CAAEiB,YAAY,CAACN,OAAO,CAAC,CACrD,CAAC,IAAM,IAAIM,YAAY,CAACf,MAAM,GAAK,0BAA0B,CAAE,CAC7D,IAAI,CAACF,eAAe,CAAC,cAAc,CAAEiB,YAAY,CAACN,OAAO,CAAC,CAC5D,CAEA,MAAO,MAAK,CACd,CACF,CAEA;AACAQ,uBAAuBA,CAAA,CAAG,CACxB,GAAI,CAAC5B,WAAW,CAACkB,UAAU,CAAC,CAAC,CAAE,CAC7B,MAAO,EAAE,CACX,CAEA,KAAM,CAAAG,QAAQ,CAAGrB,WAAW,CAACsB,WAAW,CAAC,CAAC,CAC1C,KAAM,CAAAO,WAAW,CAAG,CAClB,CACEC,EAAE,CAAE,MAAM,CACVC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,MACR,CAAC,CACD,CACEH,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,WAAW,CAClBC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,aACR,CAAC,CACD,CACEH,EAAE,CAAE,eAAe,CACnBC,KAAK,CAAE,YAAY,CACnBC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,eACR,CAAC,CACD,CACEH,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,cAAc,CACrBC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,aACR,CAAC,CACD,CACEH,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,aACR,CAAC,CACD,CACEH,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,aACR,CAAC,CACD,CACEH,EAAE,CAAE,YAAY,CAChBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,YACR,CAAC,CACD,CACEH,EAAE,CAAE,YAAY,CAChBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,YACR,CAAC,CACD,CACEH,EAAE,CAAE,iBAAiB,CACrBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,iBACR,CAAC,CACD,CACEH,EAAE,CAAE,mBAAmB,CACvBC,KAAK,CAAE,MAAM,CACbC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,mBACR,CAAC,CACF,CAED;AACA,MAAO,CAAAJ,WAAW,CAACK,MAAM,CAACC,IAAI,EAAI,CAChC,KAAM,CAAAT,YAAY,CAAG,IAAI,CAACV,SAAS,CAACmB,IAAI,CAACF,IAAI,CAAC,CAC9C,MAAO,CAAAP,YAAY,CAACP,OAAO,CAC7B,CAAC,CAAC,CACJ,CAEA;AACAiB,qBAAqBA,CAACC,UAAU,CAAmB,IAAjB,CAAAhB,QAAQ,CAAAT,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,IAAI,CAC/C,KAAM,CAAA0B,IAAI,CAAGjB,QAAQ,EAAIrB,WAAW,CAACsB,WAAW,CAAC,CAAC,CAClD,GAAI,CAACgB,IAAI,CAAE,MAAO,MAAK,CAEvB,OAAQD,UAAU,EAChB,IAAK,aAAa,CAChB,MAAO,CAAAC,IAAI,GAAKpC,KAAK,CAACqC,WAAW,EAAID,IAAI,GAAKpC,KAAK,CAACsC,OAAO,CAE7D,IAAK,aAAa,CAChB,MAAO,CAAAF,IAAI,GAAKpC,KAAK,CAACqC,WAAW,EAAID,IAAI,GAAKpC,KAAK,CAACsC,OAAO,CAE7D,IAAK,aAAa,CAChB,MAAO,CAAAF,IAAI,GAAKpC,KAAK,CAACqC,WAAW,EAAID,IAAI,GAAKpC,KAAK,CAACsC,OAAO,EAAIF,IAAI,GAAKpC,KAAK,CAACuC,QAAQ,CAExF,IAAK,gBAAgB,CACnB,MAAO,CAAAH,IAAI,GAAKpC,KAAK,CAACqC,WAAW,CAEnC,IAAK,aAAa,CAChB,MAAO,CAAAD,IAAI,GAAKpC,KAAK,CAACqC,WAAW,CAEnC,IAAK,aAAa,CAChB,MAAO,CAAAD,IAAI,GAAKpC,KAAK,CAACqC,WAAW,CAEnC,IAAK,aAAa,CAChB,MAAO,CAAAD,IAAI,GAAKpC,KAAK,CAACqC,WAAW,CAEnC,IAAK,iBAAiB,CACpB,MAAO,CAAAD,IAAI,GAAKpC,KAAK,CAACqC,WAAW,CAEnC,QACE,MAAO,KAAI,CACf,CACF,CAEA;AACAG,0BAA0BA,CAACL,UAAU,CAAE,CACrC,KAAM,CAAAM,QAAQ,CAAG,CACf,aAAa,CAAE,kBAAkB,CACjC,aAAa,CAAE,kBAAkB,CACjC,aAAa,CAAE,YAAY,CAC3B,gBAAgB,CAAE,eAAe,CACjC,aAAa,CAAE,eAAe,CAC9B,aAAa,CAAE,eAAe,CAC9B,aAAa,CAAE,eAAe,CAC9B,iBAAiB,CAAE,iBACrB,CAAC,CAED,MAAO,CAAAA,QAAQ,CAACN,UAAU,CAAC,EAAI,aAAa,CAC9C,CAEA;AACAO,eAAeA,CAAC3B,QAAQ,CAAE,CACxB,MAAQ,CAAA4B,IAAI,EAAK,CACf,MAAO,KAAI,CAACtB,gBAAgB,CAC1BN,QAAQ,CACR,IAAM4B,IAAI,CAAC,CAAC,CAAE;AACbC,MAAM,EAAK,CACVC,OAAO,CAACC,IAAI,gDAAAC,MAAA,CAAahC,QAAQ,EAAI6B,MAAM,CAAC,CAC5C;AACA,GAAII,MAAM,CAACC,gBAAgB,CAAE,CAC3BD,MAAM,CAACC,gBAAgB,CAACL,MAAM,CAAC1B,OAAO,CAAE,OAAO,CAAC,CAClD,CACF,CACF,CAAC,CACH,CAAC,CACH,CAEA;AACAgC,KAAKA,CAAA,CAAG,CACN,IAAI,CAAC/C,iBAAiB,CAAG,EAAE,CAC7B,CACF,CAEA;AACA,KAAM,CAAAgD,UAAU,CAAG,GAAI,CAAAlD,UAAU,CAAC,CAAC,CAEnC,cAAe,CAAAkD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}