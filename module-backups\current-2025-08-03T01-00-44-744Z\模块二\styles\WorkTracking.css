/* WorkTracking.css - 重点工作跟踪页面样式 */

.work-tracking-container {
  min-height: 100vh;
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
  padding: 15px;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* 页面头部 - 优化布局 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
  padding: 10px 0;
}

.header-center {
  text-align: center;
  flex: 1;
}

.back-btn-top {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-family: 'Raj<PERSON>ni', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  order: -1;
}

.back-btn-top:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

.work-tracking-container .page-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.2rem;
  color: #00d4aa;
  margin-bottom: 8px;
  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
}

.page-subtitle {
  font-size: 1rem;
  color: #20ff4d;
  margin-bottom: 0;
  opacity: 0.8;
}

.sync-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
}

.status-indicator.success {
  background: rgba(32, 255, 77, 0.2);
  color: #20ff4d;
  border: 1px solid #20ff4d;
}

.status-indicator.error {
  background: rgba(255, 87, 87, 0.2);
  color: #ff5757;
  border: 1px solid #ff5757;
}

.status-indicator.pending {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid #ffc107;
}

/* 控制面板 */
.control-panel {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 25px;
  backdrop-filter: blur(10px);
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 30px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  color: #00d4aa;
  font-weight: 600;
  font-size: 1rem;
}

.type-selector {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.5);
  border-radius: 8px;
  color: #ffffff;
  padding: 8px 15px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  min-width: 150px;
}

.type-selector:focus {
  outline: none;
  border-color: #00d4aa;
  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

/* 月份导航 */
.month-navigation {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(0, 212, 170, 0.1);
  padding: 10px 20px;
  border-radius: 25px;
  border: 1px solid rgba(0, 212, 170, 0.3);
}

/* 新月份导航样式 */
.month-navigation-new {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(0, 212, 170, 0.08);
  padding: 12px 20px;
  border-radius: 25px;
  border: 1px solid rgba(0, 212, 170, 0.4);
  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);
}

.nav-btn-new {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
  border: none;
  border-radius: 18px;
  padding: 8px 16px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-shadow: none;
  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);
}

.nav-btn-new:hover:not(:disabled) {
  transform: scale(1.05) translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
  background: linear-gradient(45deg, #20ff4d, #00d4aa);
}

.nav-btn-new:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.current-months-new {
  color: #00d4aa;
  font-weight: 700;
  font-size: 1.1rem;
  min-width: 100px;
  text-align: center;
  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);
  font-family: 'Orbitron', monospace;
}

.refresh-btn-new {
  background: linear-gradient(45deg, #4dd0ff, #00d4aa);
  color: #000;
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(77, 208, 255, 0.2);
  white-space: nowrap;
}

.refresh-btn-new:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(77, 208, 255, 0.4);
  background: linear-gradient(45deg, #00d4aa, #4dd0ff);
}

.nav-btn {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
  border: none;
  border-radius: 20px;
  padding: 8px 15px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

.nav-btn:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
}

.current-months {
  color: #00d4aa;
  font-weight: 700;
  font-size: 1.1rem;
  min-width: 120px;
  text-align: center;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.refresh-btn, .back-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn {
  background: linear-gradient(45deg, #4dd0ff, #00d4aa);
  color: #000;
}

.back-btn {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  color: #000;
}

.refresh-btn:hover, .back-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);
}

/* 统一控制面板 V2 - 优化美观布局 */
.unified-control-panel-v2 {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);
  position: relative;
  overflow: hidden;
}

/* 统一控制面板 - 美观布局 (保留兼容) */
.unified-control-panel {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 212, 170, 0.3);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 30px;
  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);
}

/* 高科技装饰元素 */
.tech-decoration {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 100px;
  position: relative;
}

.hologram-container {
  position: relative;
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.hologram-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
  background: linear-gradient(45deg, #00d4aa, #20ff4d, #4dd0ff);
  background-clip: padding-box;
  animation: rotate-ring 4s linear infinite;
}

.ring-segment {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid transparent;
}

.ring-1 {
  border-top: 2px solid #00d4aa;
  animation: rotate-segment-1 2s linear infinite;
}

.ring-2 {
  border-right: 2px solid #20ff4d;
  animation: rotate-segment-2 3s linear infinite reverse;
}

.ring-3 {
  border-bottom: 2px solid #4dd0ff;
  animation: rotate-segment-3 2.5s linear infinite;
}

.central-core {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: radial-gradient(circle, rgba(0, 212, 170, 0.8), rgba(32, 255, 77, 0.4));
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(0, 212, 170, 0.6);
}

.core-pulse {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #00d4aa;
  border-radius: 50%;
  animation: pulse-core 2s ease-in-out infinite;
}

.core-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
}

.data-stream {
  position: absolute;
  width: 2px;
  height: 8px;
  background: linear-gradient(to bottom, #20ff4d, transparent);
  animation: data-flow 1.5s linear infinite;
}

.data-stream.delay-1 {
  animation-delay: 0.5s;
  transform: rotate(120deg);
}

.data-stream.delay-2 {
  animation-delay: 1s;
  transform: rotate(240deg);
}

.tech-grid {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  opacity: 0.3;
}

.grid-line {
  position: absolute;
  background: linear-gradient(90deg, transparent, #00d4aa, transparent);
  height: 1px;
  width: 100%;
  animation: grid-scan 3s linear infinite;
}

.grid-line:nth-child(1) { top: 0; animation-delay: 0s; }
.grid-line:nth-child(2) { top: 5px; animation-delay: 0.5s; }
.grid-line:nth-child(3) { top: 10px; animation-delay: 1s; }
.grid-line:nth-child(4) { top: 15px; animation-delay: 1.5s; }

/* 中间统计区域 */
.stats-section-center {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  flex: 1;
}

/* 左侧统计区域 (保留兼容) */
.stats-section {
  display: flex;
  gap: 20px;
  align-items: center;
}

.stat-card {
  background: linear-gradient(145deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));
  border: 1px solid rgba(0, 212, 170, 0.4);
  border-radius: 12px;
  padding: 15px 20px;
  text-align: center;
  min-width: 80px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.stat-card:hover::before {
  left: 100%;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);
  border-color: rgba(0, 212, 170, 0.6);
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: #00d4aa;
  margin-bottom: 5px;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);
}

.stat-text {
  font-size: 0.9rem;
  color: #20ff4d;
  font-weight: 500;
  opacity: 0.9;
}

/* 全部展开/折叠卡片特殊样式 */
.toggle-all-card {
  cursor: pointer;
  background: linear-gradient(145deg, rgba(255, 165, 0, 0.1), rgba(255, 215, 0, 0.1));
  border: 1px solid rgba(255, 165, 0, 0.4);
  transition: all 0.3s ease;
}

.toggle-all-card:hover {
  background: linear-gradient(145deg, rgba(255, 165, 0, 0.2), rgba(255, 215, 0, 0.2));
  border-color: rgba(255, 165, 0, 0.6);
  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);
  transform: translateY(-3px) scale(1.05);
}

.toggle-all-card .stat-number {
  color: #ffa500;
  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

.toggle-all-card .stat-text {
  color: #ffd700;
}

.toggle-icon {
  font-size: 1.5rem;
  display: inline-block;
  transition: transform 0.3s ease;
}

.toggle-all-card:hover .toggle-icon {
  transform: scale(1.2);
}

/* 右侧控制区域 */
.controls-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 数据统计 */
.data-stats {
  display: flex;
  gap: 30px;
  margin-bottom: 25px;
  justify-content: center;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(32, 255, 77, 0.3);
  border-radius: 12px;
  padding: 15px 25px;
  text-align: center;
}

.data-stats-compact .stat-item {
  padding: 10px 15px;
  border-radius: 8px;
}

.stat-label {
  color: #20ff4d;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 5px;
}

.stat-value {
  color: #ffffff;
  font-size: 1.4rem;
  font-weight: 700;
}

/* 工作类型分组 */
.tracking-table-container {
  space-y: 20px;
}

.work-type-group {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(0, 212, 170, 0.2);
  border-radius: 15px;
  margin-bottom: 20px;
  overflow: hidden;
}

.group-header {
  background: linear-gradient(45deg, rgba(0, 212, 170, 0.2), rgba(32, 255, 77, 0.1));
  padding: 15px 25px;
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 212, 170, 0.3);
  transition: all 0.3s ease;
}

.group-header:hover {
  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));
}

.group-title {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #00d4aa;
  font-size: 1.2rem;
  font-weight: 700;
}

.collapse-icon {
  color: #20ff4d;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

/* 项目数量高亮显示 */
.item-count-highlight {
  color: #ffd700;
  font-weight: 800;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid rgba(255, 215, 0, 0.4);
  text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  display: inline-block;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.item-count-highlight:hover {
  color: #ffff00;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.2));
  border-color: rgba(255, 215, 0, 0.6);
  text-shadow: 0 0 12px rgba(255, 215, 0, 0.8);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);
  transform: scale(1.05);
}

/* 表格容器 */
.table-container {
  overflow-x: auto;
  padding: 20px;
}

.tracking-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 10px;
  overflow: hidden;
}

.tracking-table th {
  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));
  color: #ffffff;
  padding: 15px 10px;
  text-align: center;
  font-weight: 700;
  font-size: 0.9rem;
  border-bottom: 2px solid rgba(0, 212, 170, 0.5);
  position: sticky;
  top: 0;
  z-index: 10;
}

.tracking-table td {
  padding: 12px 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
  vertical-align: middle;
  transition: all 0.3s ease;
}

.data-row:hover td {
  background: rgba(0, 212, 170, 0.05);
}

/* 列宽控制 */
.col-number { width: 60px; }
.col-type { width: 120px; }
.col-indicator { width: 150px; }
.col-total-target { width: 120px; }
.col-target { width: 150px; }
.col-method { width: 200px; }
.col-responsible { width: 100px; }
.col-frequency { width: 80px; }
.col-month-plan, .col-month-complete { width: 150px; }

/* 可编辑单元格 */
.data-cell.editable {
  cursor: pointer;
  position: relative;
}

.data-cell.editable:hover {
  background: rgba(0, 212, 170, 0.1);
  border: 1px solid rgba(0, 212, 170, 0.3);
}

.cell-input {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid #00d4aa;
  border-radius: 4px;
  color: #ffffff;
  padding: 5px 8px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  width: 100%;
  box-sizing: border-box;
}

.cell-input:focus {
  outline: none;
  border-color: #20ff4d;
  box-shadow: 0 0 8px rgba(32, 255, 77, 0.3);
}

.cell-content {
  display: block;
  word-break: break-word;
  line-height: 1.4;
}

/* 月份列特殊样式 */
.month-plan {
  background: rgba(77, 208, 255, 0.05);
  border-left: 3px solid rgba(77, 208, 255, 0.3);
}

.month-complete {
  background: rgba(32, 255, 77, 0.05);
  border-left: 3px solid rgba(32, 255, 77, 0.3);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 212, 170, 0.3);
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 高科技元素动画 */
@keyframes rotate-ring {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate-segment-1 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate-segment-2 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes rotate-segment-3 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse-core {
  0%, 100% { 
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% { 
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
}

@keyframes data-flow {
  0% { 
    transform: translateY(-10px) scale(1);
    opacity: 0;
  }
  50% { 
    transform: translateY(0) scale(1.2);
    opacity: 1;
  }
  100% { 
    transform: translateY(10px) scale(1);
    opacity: 0;
  }
}

@keyframes grid-scan {
  0% { 
    transform: translateX(-100%);
    opacity: 0;
  }
  50% { 
    opacity: 1;
  }
  100% { 
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 60px 20px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .unified-control-panel-v2 {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .tech-decoration {
    width: 100%;
    height: 60px;
    flex-direction: row;
    justify-content: center;
  }

  .hologram-container {
    width: 60px;
    height: 60px;
    margin-bottom: 0;
    margin-right: 20px;
  }

  .stats-section-center {
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .controls-section {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .month-navigation-new {
    width: 100%;
    justify-content: center;
  }

  .refresh-btn-new {
    width: 100%;
    max-width: 300px;
  }

  .unified-control-panel {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .stats-section {
    justify-content: center;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .month-navigation {
    flex-direction: column;
    gap: 10px;
  }
  
  .data-stats {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .work-tracking-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 1.8rem;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
  }

  .header-center {
    order: 1;
  }

  .back-btn-top {
    order: 0;
    align-self: flex-start;
  }

  .sync-status {
    order: 2;
    align-self: center;
  }

  .stat-card {
    min-width: 60px;
    padding: 10px 15px;
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .nav-btn-new {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .current-months-new {
    font-size: 1rem;
    min-width: 80px;
  }

  .tech-decoration {
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
  }

  .hologram-container {
    width: 40px;
    height: 40px;
    margin-right: 10px;
  }

  .stats-section-center {
    gap: 10px;
  }
  
  .control-panel {
    padding: 15px;
  }
  
  .action-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .tracking-table {
    font-size: 0.8rem;
  }
  
  .tracking-table th,
  .tracking-table td {
    padding: 8px 5px;
  }
}

/* 自定义滚动条 */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #00d4aa, #20ff4d);
}

/* 高科技感负责人筛选器 */
.responsible-filter-orb-simplified {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #00d4aa, #00b894, #0984e3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 1000;
  box-shadow: 
    0 0 20px rgba(0, 212, 170, 0.5),
    0 0 40px rgba(0, 212, 170, 0.3),
    inset 0 0 20px rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  border: 2px solid rgba(0, 255, 204, 0.3);
  backdrop-filter: blur(10px);
}

.responsible-filter-orb-simplified:hover {
  transform: scale(1.1);
  box-shadow: 
    0 0 30px rgba(0, 212, 170, 0.8),
    0 0 60px rgba(0, 212, 170, 0.4),
    inset 0 0 30px rgba(255, 255, 255, 0.2);
}

.orb-icon-simplified {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.filter-count {
  position: absolute;
  top: -12px;
  right: -12px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 11px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.orb-tooltip {
  position: absolute;
  top: 70px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 20, 40, 0.95);
  color: #00ffcc;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 255, 204, 0.3);
  backdrop-filter: blur(10px);
}

.responsible-filter-orb-simplified:hover .orb-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 高科技感筛选面板 */
.responsible-filter-panel {
  position: fixed;
  top: 100px;
  left: 20px;
  width: 320px;
  max-height: 500px;
  background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 60, 0.95));
  border: 2px solid rgba(0, 255, 204, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  box-shadow: 
    0 10px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(0, 255, 204, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  z-index: 1001;
  overflow: hidden;
}

.filter-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(90deg, rgba(0, 255, 204, 0.1), rgba(0, 184, 148, 0.1));
  border-bottom: 1px solid rgba(0, 255, 204, 0.2);
}

.filter-panel-header h3 {
  margin: 0;
  color: #00ffcc;
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0, 255, 204, 0.5);
}

.close-panel-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  font-size: 24px;
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-panel-btn:hover {
  background: rgba(255, 107, 107, 0.2);
  transform: rotate(90deg);
}

.filter-panel-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.filter-options {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 15px;
}

.filter-option {
  background: rgba(0, 255, 204, 0.1);
  border: 1px solid rgba(0, 255, 204, 0.3);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  text-align: center;
  flex: 0 0 calc(33.33% - 4px);
  box-sizing: border-box;
}

.filter-option:hover {
  background: rgba(0, 255, 204, 0.2);
  border-color: rgba(0, 255, 204, 0.5);
  box-shadow: 0 0 15px rgba(0, 255, 204, 0.3);
}

.filter-option.selected {
  background: linear-gradient(135deg, rgba(0, 255, 204, 0.3), rgba(0, 184, 148, 0.3));
  border-color: #00ffcc;
  color: #00ffcc;
  box-shadow: 0 0 20px rgba(0, 255, 204, 0.4);
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.clear-filter-btn, .apply-filter-btn {
  flex: 1;
  padding: 10px 16px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.clear-filter-btn {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.clear-filter-btn:hover {
  background: rgba(255, 107, 107, 0.3);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);
}

.apply-filter-btn {
  background: linear-gradient(135deg, #00d4aa, #00b894);
  color: white;
  border: 1px solid rgba(0, 255, 204, 0.3);
}

.apply-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);
}

/* 自定义滚动条 */
.filter-panel-content::-webkit-scrollbar {
  width: 6px;
}

.filter-panel-content::-webkit-scrollbar-track {
  background: rgba(0, 255, 204, 0.1);
  border-radius: 3px;
}

.filter-panel-content::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 204, 0.5);
  border-radius: 3px;
}

.filter-panel-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 204, 0.7);
}

/* 整合的负责人筛选器 */
.responsible-filter-integrated {
  cursor: pointer;
  transition: all 0.3s ease;
}

.responsible-filter-integrated:hover {
  transform: scale(1.05);
}

.filter-icon-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 18px;
  color: #ffffff;
  z-index: 2;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.filter-count-integrated {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  z-index: 3;
} 