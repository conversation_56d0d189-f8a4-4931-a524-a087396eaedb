/* 工作目标管理责任书页面样式 - 优化版 */
.work-target {
  min-height: 100vh;
  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  color: #ffffff;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;
  padding: 10px 15px;
  background: rgba(0, 0, 0, 0.4);
  border-bottom: 2px solid rgba(32, 255, 77, 0.3);
  backdrop-filter: blur(10px);
}

.back-button {
  padding: 8px 16px;
  background: linear-gradient(45deg, #20ff4d, #00d4aa);
  border: none;
  border-radius: 6px;
  color: #000;
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(32, 255, 77, 0.3);
}

.work-target .page-title {
  font-family: 'Orbitron', monospace;
  font-size: 2.2rem;
  font-weight: 700;
  color: #00d4aa;
  text-align: center;
  flex: 1;
  margin: 0 15px;
  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sync-status {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #00d4aa;
  font-size: 0.8rem;
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #20ff4d;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

/* 数据概览 */
.data-overview {
  display: flex;
  justify-content: center;
  gap: 30px;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.2);
}

.overview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.overview-number {
  font-family: 'Orbitron', monospace;
  font-size: 1.8rem;
  font-weight: 700;
  color: #20ff4d;
  text-shadow: 0 0 10px rgba(32, 255, 77, 0.5);
}

.overview-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

/* 主要内容区域 */
.content-area {
  padding: 20px 30px 30px;
  max-width: 1600px;
  margin: 0 auto;
}

/* 三个大标题区域 */
.section-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 15px;
  margin-bottom: 25px;
}

.section-tab {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-tab:hover,
.section-tab.active {
  transform: translateY(-3px);
  border-color: var(--section-color);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 8px 25px rgba(0, 0, 0, 0.3),
    0 0 20px var(--section-color);
}

.tab-icon {
  font-size: 2rem;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.section-tab:hover .tab-icon,
.section-tab.active .tab-icon {
  filter: grayscale(0%);
}

.tab-content {
  flex: 1;
  text-align: left;
}

.tab-title {
  font-family: 'Orbitron', monospace;
  font-size: 1.1rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-count {
  font-size: 0.8rem;
  color: var(--section-color);
  opacity: 0.8;
}

.tab-arrow {
  font-size: 1rem;
  color: var(--section-color);
  transition: transform 0.3s ease;
}

/* 表格区域 */
.table-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 20px;
  border: 1px solid rgba(32, 255, 77, 0.2);
  animation: fadeIn 0.5s ease-out;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.table-header h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  color: #20ff4d;
  margin: 0;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.edit-hint {
  font-size: 0.8rem;
  color: #00d4aa;
  opacity: 0.8;
}

.refresh-btn {
  padding: 6px 12px;
  background: rgba(32, 255, 77, 0.2);
  border: 1px solid #20ff4d;
  border-radius: 4px;
  color: #20ff4d;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(32, 255, 77, 0.3);
}

/* 表格概要信息 */
.table-summary {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  padding: 10px 15px;
  background: rgba(32, 255, 77, 0.1);
  border-radius: 6px;
  border: 1px solid rgba(32, 255, 77, 0.3);
  font-size: 0.9rem;
  align-items: center;
  justify-content: space-between;
}

.table-summary span {
  color: #20ff4d;
  font-weight: 500;
}

/* 重点工作说明内联样式 */
.key-work-notice-inline {
  color: #ffa500 !important;
  font-weight: 600 !important;
  font-family: 'Orbitron', monospace !important;
  background: rgba(255, 165, 0, 0.15);
  padding: 4px 12px;
  border-radius: 4px;
  border: 1px solid rgba(255, 165, 0, 0.4);
  font-size: 0.85rem !important;
}

/* 数据表格 - 优化列宽 */
.data-table-container {
  overflow-x: auto;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: rgba(0, 0, 0, 0.2);
  font-size: 0.9rem; /* 增大字体提升可读性 */
  table-layout: fixed;
  line-height: 1.5; /* 增加行高 */
}

/* 优化的列宽设置 */
.data-table .col-number { width: 5%; }
.data-table .col-indicator { width: 15%; }
.data-table .col-target { width: 30%; }
.data-table .col-weight { width: 8%; }
.data-table .col-standard { width: 22%; }
.data-table .col-category { width: 10%; }
.data-table .col-responsible { width: 10%; }

.data-table th {
  background: linear-gradient(135deg, rgba(32, 255, 77, 0.4), rgba(32, 255, 77, 0.3)); /* 增强背景对比度 */
  color: #ffffff;
  padding: 12px 10px; /* 增加内边距 */
  text-align: center;
  font-family: 'Orbitron', monospace;
  font-size: 0.85rem; /* 稍微增大表头字体 */
  font-weight: 700; /* 增强字体粗细 */
  border-bottom: 2px solid #20ff4d;
  position: sticky;
  top: 0;
  z-index: 10;
  vertical-align: middle;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */
}

.data-row {
  transition: all 0.3s ease;
}

.data-row:hover {
  background: rgba(32, 255, 77, 0.12); /* 增强悬停效果 */
  transform: translateY(-1px); /* 轻微上移效果 */
  box-shadow: 0 2px 8px rgba(32, 255, 77, 0.2); /* 添加阴影 */
}

.data-row:nth-child(even) {
  background: rgba(255, 255, 255, 0.04); /* 增强斑马纹对比度 */
}

/* 正确的合并单元格样式及内容居中 */
.merged-start-row {
  /* border-left: 3px solid rgba(32, 255, 77, 0.6); // 可以移除或调整 */
}

.merged-row {
  background: rgba(32, 255, 77, 0.03) !important;
  /* border-left: 3px solid rgba(32, 255, 77, 0.3); // 可以移除或调整 */
}

.merged-row:hover {
  background: rgba(32, 255, 77, 0.1) !important;
}

.data-cell.merged-cell-content { /* 应用于具有rowSpan的<td> */
  background: linear-gradient(135deg, rgba(32, 255, 77, 0.15), rgba(32, 255, 77, 0.1));
  text-align: center;     /* 水平居中 */
  vertical-align: middle; /* 垂直居中 */
  font-weight: 600;
  border-right: 2px solid rgba(32, 255, 77, 0.4);
  position: relative;
}

.data-cell.merged-cell-content::after { /* 强调合并单元格的左边框 */
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #20ff4d, #00d4aa);
}

.data-cell {
  padding: 10px 8px; /* 增加内边距提升可读性 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* 增强边框对比度 */
  border-right: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.2s ease;
  text-align: center;        /* 添加：水平居中 */
  vertical-align: middle;    /* 修改：从top改为middle，垂直居中 */
  word-wrap: break-word;
  color: #ffffff; /* 确保文字颜色 */
  line-height: 1.6; /* 增加行高 */
  /* white-space: pre-wrap; // 如果使用<br/>则不再严格需要，但保留无害 */
}

.data-cell.editable {
  cursor: pointer;
}

.data-cell.editable:hover {
  background: rgba(32, 255, 77, 0.18); /* 增强悬停背景 */
  border-color: rgba(32, 255, 77, 0.3); /* 添加边框高亮 */
  box-shadow: inset 0 0 8px rgba(32, 255, 77, 0.2); /* 内阴影效果 */
}

.cell-content {
  display: block; /* 使span占据整个单元格，方便点击和对齐 */
  width: 100%;
  color: #ffffff;
  line-height: 1.6; /* 增加行高使多行文本更舒适 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal; /* 允许自动换行，并通过<br/>强制换行 */
  text-align: center;        /* 添加：确保内容居中显示 */
  font-weight: 500; /* 稍微增加字体粗细 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 添加文字阴影增强可读性 */
}

.editable-cell {
  border-radius: 3px;
  padding: 4px 6px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.editable-cell:hover {
  background: rgba(32, 255, 77, 0.1);
  border-color: rgba(32, 255, 77, 0.3);
}

.cell-input { /* 修改为textarea */
  width: 100%;
  box-sizing: border-box; /* 确保padding和border不增加总宽度 */
  background: rgba(0, 0, 0, 0.8);
  border: 2px solid #20ff4d;
  border-radius: 3px;
  padding: 6px 8px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.85rem;
  outline: none;
  resize: vertical; /* 允许用户调整高度 */
  min-height: 30px; /* 最小高度 */
  line-height: 1.4;
  text-align: center;        /* 添加：编辑时也居中显示 */
}

.cell-input:focus {
  box-shadow: 0 0 10px rgba(32, 255, 77, 0.5);
  background: rgba(10, 10, 20, 0.9); /* 深一点的背景色 */
}

/* 欢迎消息 */
.welcome-message {
  text-align: center;
  padding: 40px 20px;
  color: rgba(255, 255, 255, 0.8);
}

.welcome-message h3 {
  font-family: 'Orbitron', monospace;
  font-size: 1.3rem;
  color: #20ff4d;
  margin-bottom: 10px;
}

.welcome-message p {
  font-size: 1rem;
  margin-bottom: 20px;
  opacity: 0.8;
}

/* 无数据状态 */
.no-data {
  text-align: center;
  padding: 30px;
  color: rgba(255, 255, 255, 0.6);
}

/* 加载状态 */
.work-target-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  color: #20ff4d;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(32, 255, 77, 0.3);
  border-top: 2px solid #20ff4d;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .section-tabs {
    grid-template-columns: 1fr;
  }
  
  .content-area {
    padding: 15px 20px 25px;
  }
  
  /* 调整表格列宽 */
  .data-table .col-target { width: 200px; }
  .data-table .col-standard { width: 180px; }
}

@media (max-width: 768px) {
  .data-overview {
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
    padding: 10px 15px;
  }
  
  .page-title {
    font-size: 1.2rem;
    margin: 0;
  }
  
  .table-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .data-table {
    font-size: 0.8rem; /* 稍微增大移动端字体 */
  }

  .data-table th,
  .data-table td {
    padding: 8px 6px; /* 增加移动端内边距 */
  }
  
  /* 移动端列宽调整 */
  .data-table .col-target { width: 150px; }
  .data-table .col-standard { width: 120px; }
}

/* 动画效果 */
@keyframes fadeIn {
  from { 
    opacity: 0; 
    transform: translateY(15px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 新材级重点工作突出显示样式 */
.highlight-work {
  background: rgba(255, 165, 0, 0.08) !important;
  border-left: 4px solid #ffa500 !important;
}

.highlight-work:hover {
  background: rgba(255, 165, 0, 0.15) !important;
}

.highlight-cell {
  background: rgba(255, 165, 0, 0.1);
  font-weight: 600;
  color: #ffffff;
  box-shadow: inset 0 0 0 1px rgba(255, 165, 0, 0.3);
}

.highlight-merged-cell {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.25), rgba(255, 165, 0, 0.15)) !important;
  border-right: 2px solid rgba(255, 165, 0, 0.6) !important;
}

.highlight-merged-cell::after {
  background: linear-gradient(to bottom, #ffa500, #ff8c00) !important;
}

/* 下载按钮样式 */
.download-btn {
  background: linear-gradient(135deg, #4dd0ff, #20ff4d);
  border: none;
  color: #000;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(77, 208, 255, 0.4);
  background: linear-gradient(135deg, #20ff4d, #4dd0ff);
}

.download-btn:active {
  transform: translateY(0);
} 