{"ast": null, "code": "import React,{useState,useEffect}from'react';import'../styles/ExportModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ExportModal=_ref=>{let{onClose,onExport,loading,data}=_ref;const[selectedIndicators,setSelectedIndicators]=useState([]);const[exportFormat,setExportFormat]=useState('excel');const[selectAll,setSelectAll]=useState(true);useEffect(()=>{// 默认全选所有指标\nif(data&&data.length>0){setSelectedIndicators(data.map((_,index)=>index));}},[data]);// 处理全选/反选\nconst handleSelectAll=()=>{if(selectAll){setSelectedIndicators([]);setSelectAll(false);}else{setSelectedIndicators(data.map((_,index)=>index));setSelectAll(true);}};// 处理单个指标选择\nconst handleIndicatorSelect=index=>{setSelectedIndicators(prev=>{const newSelection=prev.includes(index)?prev.filter(i=>i!==index):[...prev,index];// 更新全选状态\nsetSelectAll(newSelection.length===data.length);return newSelection;});};// 处理导出\nconst handleExport=()=>{if(selectedIndicators.length===0){alert('请至少选择一个指标进行导出');return;}const exportConfig={format:exportFormat,selectedIndices:selectedIndicators,selectedData:selectedIndicators.map(index=>data[index])};onExport(exportConfig);};return/*#__PURE__*/_jsx(\"div\",{className:\"module6_export-modal-overlay\",onClick:onClose,children:/*#__PURE__*/_jsxs(\"div\",{className:\"module6_export-modal\",onClick:e=>e.stopPropagation(),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_modal-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u5BFC\\u51FA\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_close-btn\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_selection-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"module6_section-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u9009\\u62E9\\u8981\\u5BFC\\u51FA\\u7684\\u6307\\u6807\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_selection-controls\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"module6_select-all-label\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectAll,onChange:handleSelectAll}),\"\\u5168\\u9009/\\u53CD\\u9009\"]}),/*#__PURE__*/_jsxs(\"span\",{className:\"module6_selection-count\",children:[\"\\u5DF2\\u9009\\u62E9 \",selectedIndicators.length,\" \\u9879\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"module6_indicators-list\",children:data.map((item,index)=>/*#__PURE__*/_jsxs(\"label\",{className:\"module6_indicator-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedIndicators.includes(index),onChange:()=>handleIndicatorSelect(index)}),/*#__PURE__*/_jsxs(\"span\",{className:\"module6_indicator-text\",children:[item.序号,\". \",item.指标]})]},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_format-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u9009\\u62E9\\u5BFC\\u51FA\\u683C\\u5F0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_format-options\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"module6_format-option\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",name:\"exportFormat\",value:\"excel\",checked:exportFormat==='excel',onChange:e=>setExportFormat(e.target.value)}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_format-icon\",children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_format-text\",children:\"Excel (.xlsx)\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"module6_format-option\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",name:\"exportFormat\",value:\"csv\",checked:exportFormat==='csv',onChange:e=>setExportFormat(e.target.value)}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_format-icon\",children:\"\\uD83D\\uDCC4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"module6_format-text\",children:\"CSV (.csv)\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"module6_modal-footer\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"module6_cancel-btn\",onClick:onClose,disabled:loading,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(\"button\",{className:\"module6_export-btn\",onClick:handleExport,disabled:loading||selectedIndicators.length===0,children:loading?'导出中...':'确认导出'})]})]})});};export default ExportModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "ExportModal", "_ref", "onClose", "onExport", "loading", "data", "selectedIndicators", "setSelectedIndicators", "exportFormat", "setExportFormat", "selectAll", "setSelectAll", "length", "map", "_", "index", "handleSelectAll", "handleIndicatorSelect", "prev", "newSelection", "includes", "filter", "i", "handleExport", "alert", "exportConfig", "format", "selectedIndices", "selectedData", "className", "onClick", "children", "e", "stopPropagation", "type", "checked", "onChange", "item", "序号", "指标", "name", "value", "target", "disabled"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块六/components/ExportModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/ExportModal.css';\r\n\r\nconst ExportModal = ({ onClose, onExport, loading, data }) => {\r\n  const [selectedIndicators, setSelectedIndicators] = useState([]);\r\n  const [exportFormat, setExportFormat] = useState('excel');\r\n  const [selectAll, setSelectAll] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // 默认全选所有指标\r\n    if (data && data.length > 0) {\r\n      setSelectedIndicators(data.map((_, index) => index));\r\n    }\r\n  }, [data]);\r\n\r\n  // 处理全选/反选\r\n  const handleSelectAll = () => {\r\n    if (selectAll) {\r\n      setSelectedIndicators([]);\r\n      setSelectAll(false);\r\n    } else {\r\n      setSelectedIndicators(data.map((_, index) => index));\r\n      setSelectAll(true);\r\n    }\r\n  };\r\n\r\n  // 处理单个指标选择\r\n  const handleIndicatorSelect = (index) => {\r\n    setSelectedIndicators(prev => {\r\n      const newSelection = prev.includes(index)\r\n        ? prev.filter(i => i !== index)\r\n        : [...prev, index];\r\n      \r\n      // 更新全选状态\r\n      setSelectAll(newSelection.length === data.length);\r\n      \r\n      return newSelection;\r\n    });\r\n  };\r\n\r\n  // 处理导出\r\n  const handleExport = () => {\r\n    if (selectedIndicators.length === 0) {\r\n      alert('请至少选择一个指标进行导出');\r\n      return;\r\n    }\r\n\r\n    const exportConfig = {\r\n      format: exportFormat,\r\n      selectedIndices: selectedIndicators,\r\n      selectedData: selectedIndicators.map(index => data[index])\r\n    };\r\n\r\n    onExport(exportConfig);\r\n  };\r\n\r\n  return (\r\n    <div className=\"module6_export-modal-overlay\" onClick={onClose}>\r\n      <div className=\"module6_export-modal\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"module6_modal-header\">\r\n          <h3>导出数据</h3>\r\n          <button className=\"module6_close-btn\" onClick={onClose}>×</button>\r\n        </div>\r\n\r\n        <div className=\"module6_modal-content\">\r\n          {/* 选择指标 */}\r\n          <div className=\"module6_selection-section\">\r\n            <div className=\"module6_section-header\">\r\n              <h4>选择要导出的指标</h4>\r\n              <div className=\"module6_selection-controls\">\r\n                <label className=\"module6_select-all-label\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectAll}\r\n                    onChange={handleSelectAll}\r\n                  />\r\n                  全选/反选\r\n                </label>\r\n                <span className=\"module6_selection-count\">\r\n                  已选择 {selectedIndicators.length} 项\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"module6_indicators-list\">\r\n              {data.map((item, index) => (\r\n                <label key={index} className=\"module6_indicator-item\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectedIndicators.includes(index)}\r\n                    onChange={() => handleIndicatorSelect(index)}\r\n                  />\r\n                  <span className=\"module6_indicator-text\">\r\n                    {item.序号}. {item.指标}\r\n                  </span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* 选择导出格式 */}\r\n          <div className=\"module6_format-section\">\r\n            <h4>选择导出格式</h4>\r\n            <div className=\"module6_format-options\">\r\n              <label className=\"module6_format-option\">\r\n                <input\r\n                  type=\"radio\"\r\n                  name=\"exportFormat\"\r\n                  value=\"excel\"\r\n                  checked={exportFormat === 'excel'}\r\n                  onChange={(e) => setExportFormat(e.target.value)}\r\n                />\r\n                <span className=\"module6_format-icon\">📊</span>\r\n                <span className=\"module6_format-text\">Excel (.xlsx)</span>\r\n              </label>\r\n              \r\n              <label className=\"module6_format-option\">\r\n                <input\r\n                  type=\"radio\"\r\n                  name=\"exportFormat\"\r\n                  value=\"csv\"\r\n                  checked={exportFormat === 'csv'}\r\n                  onChange={(e) => setExportFormat(e.target.value)}\r\n                />\r\n                <span className=\"module6_format-icon\">📄</span>\r\n                <span className=\"module6_format-text\">CSV (.csv)</span>\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"module6_modal-footer\">\r\n          <button \r\n            className=\"module6_cancel-btn\" \r\n            onClick={onClose}\r\n            disabled={loading}\r\n          >\r\n            取消\r\n          </button>\r\n          <button \r\n            className=\"module6_export-btn\"\r\n            onClick={handleExport}\r\n            disabled={loading || selectedIndicators.length === 0}\r\n          >\r\n            {loading ? '导出中...' : '确认导出'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExportModal; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnC,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAA0C,IAAzC,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,IAAK,CAAC,CAAAJ,IAAA,CACvD,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGb,QAAQ,CAAC,EAAE,CAAC,CAChE,KAAM,CAACc,YAAY,CAAEC,eAAe,CAAC,CAAGf,QAAQ,CAAC,OAAO,CAAC,CACzD,KAAM,CAACgB,SAAS,CAAEC,YAAY,CAAC,CAAGjB,QAAQ,CAAC,IAAI,CAAC,CAEhDC,SAAS,CAAC,IAAM,CACd;AACA,GAAIU,IAAI,EAAIA,IAAI,CAACO,MAAM,CAAG,CAAC,CAAE,CAC3BL,qBAAqB,CAACF,IAAI,CAACQ,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,GAAKA,KAAK,CAAC,CAAC,CACtD,CACF,CAAC,CAAE,CAACV,IAAI,CAAC,CAAC,CAEV;AACA,KAAM,CAAAW,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIN,SAAS,CAAE,CACbH,qBAAqB,CAAC,EAAE,CAAC,CACzBI,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,IAAM,CACLJ,qBAAqB,CAACF,IAAI,CAACQ,GAAG,CAAC,CAACC,CAAC,CAAEC,KAAK,GAAKA,KAAK,CAAC,CAAC,CACpDJ,YAAY,CAAC,IAAI,CAAC,CACpB,CACF,CAAC,CAED;AACA,KAAM,CAAAM,qBAAqB,CAAIF,KAAK,EAAK,CACvCR,qBAAqB,CAACW,IAAI,EAAI,CAC5B,KAAM,CAAAC,YAAY,CAAGD,IAAI,CAACE,QAAQ,CAACL,KAAK,CAAC,CACrCG,IAAI,CAACG,MAAM,CAACC,CAAC,EAAIA,CAAC,GAAKP,KAAK,CAAC,CAC7B,CAAC,GAAGG,IAAI,CAAEH,KAAK,CAAC,CAEpB;AACAJ,YAAY,CAACQ,YAAY,CAACP,MAAM,GAAKP,IAAI,CAACO,MAAM,CAAC,CAEjD,MAAO,CAAAO,YAAY,CACrB,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAI,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAIjB,kBAAkB,CAACM,MAAM,GAAK,CAAC,CAAE,CACnCY,KAAK,CAAC,eAAe,CAAC,CACtB,OACF,CAEA,KAAM,CAAAC,YAAY,CAAG,CACnBC,MAAM,CAAElB,YAAY,CACpBmB,eAAe,CAAErB,kBAAkB,CACnCsB,YAAY,CAAEtB,kBAAkB,CAACO,GAAG,CAACE,KAAK,EAAIV,IAAI,CAACU,KAAK,CAAC,CAC3D,CAAC,CAEDZ,QAAQ,CAACsB,YAAY,CAAC,CACxB,CAAC,CAED,mBACE5B,IAAA,QAAKgC,SAAS,CAAC,8BAA8B,CAACC,OAAO,CAAE5B,OAAQ,CAAA6B,QAAA,cAC7DhC,KAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAACC,OAAO,CAAGE,CAAC,EAAKA,CAAC,CAACC,eAAe,CAAC,CAAE,CAAAF,QAAA,eACxEhC,KAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAAAE,QAAA,eACnClC,IAAA,OAAAkC,QAAA,CAAI,0BAAI,CAAI,CAAC,cACblC,IAAA,WAAQgC,SAAS,CAAC,mBAAmB,CAACC,OAAO,CAAE5B,OAAQ,CAAA6B,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC/D,CAAC,cAENhC,KAAA,QAAK8B,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eAEpChC,KAAA,QAAK8B,SAAS,CAAC,2BAA2B,CAAAE,QAAA,eACxChC,KAAA,QAAK8B,SAAS,CAAC,wBAAwB,CAAAE,QAAA,eACrClC,IAAA,OAAAkC,QAAA,CAAI,kDAAQ,CAAI,CAAC,cACjBhC,KAAA,QAAK8B,SAAS,CAAC,4BAA4B,CAAAE,QAAA,eACzChC,KAAA,UAAO8B,SAAS,CAAC,0BAA0B,CAAAE,QAAA,eACzClC,IAAA,UACEqC,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEzB,SAAU,CACnB0B,QAAQ,CAAEpB,eAAgB,CAC3B,CAAC,4BAEJ,EAAO,CAAC,cACRjB,KAAA,SAAM8B,SAAS,CAAC,yBAAyB,CAAAE,QAAA,EAAC,qBACpC,CAACzB,kBAAkB,CAACM,MAAM,CAAC,SACjC,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,cAENf,IAAA,QAAKgC,SAAS,CAAC,yBAAyB,CAAAE,QAAA,CACrC1B,IAAI,CAACQ,GAAG,CAAC,CAACwB,IAAI,CAAEtB,KAAK,gBACpBhB,KAAA,UAAmB8B,SAAS,CAAC,wBAAwB,CAAAE,QAAA,eACnDlC,IAAA,UACEqC,IAAI,CAAC,UAAU,CACfC,OAAO,CAAE7B,kBAAkB,CAACc,QAAQ,CAACL,KAAK,CAAE,CAC5CqB,QAAQ,CAAEA,CAAA,GAAMnB,qBAAqB,CAACF,KAAK,CAAE,CAC9C,CAAC,cACFhB,KAAA,SAAM8B,SAAS,CAAC,wBAAwB,CAAAE,QAAA,EACrCM,IAAI,CAACC,EAAE,CAAC,IAAE,CAACD,IAAI,CAACE,EAAE,EACf,CAAC,GARGxB,KASL,CACR,CAAC,CACC,CAAC,EACH,CAAC,cAGNhB,KAAA,QAAK8B,SAAS,CAAC,wBAAwB,CAAAE,QAAA,eACrClC,IAAA,OAAAkC,QAAA,CAAI,sCAAM,CAAI,CAAC,cACfhC,KAAA,QAAK8B,SAAS,CAAC,wBAAwB,CAAAE,QAAA,eACrChC,KAAA,UAAO8B,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACtClC,IAAA,UACEqC,IAAI,CAAC,OAAO,CACZM,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAC,OAAO,CACbN,OAAO,CAAE3B,YAAY,GAAK,OAAQ,CAClC4B,QAAQ,CAAGJ,CAAC,EAAKvB,eAAe,CAACuB,CAAC,CAACU,MAAM,CAACD,KAAK,CAAE,CAClD,CAAC,cACF5C,IAAA,SAAMgC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,CAAC,cAAE,CAAM,CAAC,cAC/ClC,IAAA,SAAMgC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,CAAC,eAAa,CAAM,CAAC,EACrD,CAAC,cAERhC,KAAA,UAAO8B,SAAS,CAAC,uBAAuB,CAAAE,QAAA,eACtClC,IAAA,UACEqC,IAAI,CAAC,OAAO,CACZM,IAAI,CAAC,cAAc,CACnBC,KAAK,CAAC,KAAK,CACXN,OAAO,CAAE3B,YAAY,GAAK,KAAM,CAChC4B,QAAQ,CAAGJ,CAAC,EAAKvB,eAAe,CAACuB,CAAC,CAACU,MAAM,CAACD,KAAK,CAAE,CAClD,CAAC,cACF5C,IAAA,SAAMgC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,CAAC,cAAE,CAAM,CAAC,cAC/ClC,IAAA,SAAMgC,SAAS,CAAC,qBAAqB,CAAAE,QAAA,CAAC,YAAU,CAAM,CAAC,EAClD,CAAC,EACL,CAAC,EACH,CAAC,EACH,CAAC,cAENhC,KAAA,QAAK8B,SAAS,CAAC,sBAAsB,CAAAE,QAAA,eACnClC,IAAA,WACEgC,SAAS,CAAC,oBAAoB,CAC9BC,OAAO,CAAE5B,OAAQ,CACjByC,QAAQ,CAAEvC,OAAQ,CAAA2B,QAAA,CACnB,cAED,CAAQ,CAAC,cACTlC,IAAA,WACEgC,SAAS,CAAC,oBAAoB,CAC9BC,OAAO,CAAEP,YAAa,CACtBoB,QAAQ,CAAEvC,OAAO,EAAIE,kBAAkB,CAACM,MAAM,GAAK,CAAE,CAAAmB,QAAA,CAEpD3B,OAAO,CAAG,QAAQ,CAAG,MAAM,CACtB,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}