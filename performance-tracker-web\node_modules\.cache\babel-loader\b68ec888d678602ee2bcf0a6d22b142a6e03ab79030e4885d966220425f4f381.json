{"ast": null, "code": "import React,{useState,useEffect}from'react';import LoginForm from'./LoginForm';import authService from'../services/authService';import routeGuard from'../utils/routeGuards';import{ROLE_DISPLAY_NAMES}from'../utils/rolePermissions';import'../styles/LoginManager.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const LoginManager=_ref=>{var _currentUser$username,_currentUser$username2,_currentUser$username3,_currentUser$username4;let{onAuthStateChange}=_ref;const[isLoginVisible,setIsLoginVisible]=useState(false);const[isAuthenticated,setIsAuthenticated]=useState(false);const[currentUser,setCurrentUser]=useState(null);const[showUserMenu,setShowUserMenu]=useState(false);useEffect(()=>{// 检查初始登录状态\ncheckAuthStatus();// 设置路由守卫重定向回调\nrouteGuard.onRedirect((targetPage,reason)=>{if(targetPage==='login'){setIsLoginVisible(true);}});},[]);// 添加点击空白区域隐藏用户菜单的功能\nuseEffect(()=>{const handleClickOutside=event=>{if(showUserMenu){// 检查点击是否在用户菜单外部\nconst userMenu=document.querySelector('.auth-user-dropdown');const userAvatar=document.querySelector('.auth-user-avatar-mini');if(userMenu&&userAvatar&&!userMenu.contains(event.target)&&!userAvatar.contains(event.target)){setShowUserMenu(false);}}};// 添加事件监听器\ndocument.addEventListener('mousedown',handleClickOutside);// 清理函数\nreturn()=>{document.removeEventListener('mousedown',handleClickOutside);};},[showUserMenu]);const checkAuthStatus=()=>{const isLoggedIn=authService.checkAuthStatus();setIsAuthenticated(isLoggedIn);if(isLoggedIn){const user=authService.getCurrentUser();setCurrentUser(user);// 通知父组件认证状态变化\nif(onAuthStateChange){onAuthStateChange(true,user);}}else{setCurrentUser(null);if(onAuthStateChange){onAuthStateChange(false,null);}}};const handleLoginSuccess=user=>{setIsAuthenticated(true);setCurrentUser(user);setIsLoginVisible(false);// 通知父组件认证状态变化\nif(onAuthStateChange){onAuthStateChange(true,user);}// 显示成功提示\nshowNotification('登录成功！欢迎回来','success');};const handleLogout=async()=>{const result=await authService.logout();if(result.success){setIsAuthenticated(false);setCurrentUser(null);setShowUserMenu(false);// 通知父组件认证状态变化\nif(onAuthStateChange){onAuthStateChange(false,null);}showNotification('已安全登出','success');}else{showNotification(result.message,'error');}};const showNotification=(message,type)=>{// 创建通知元素\nconst notification=document.createElement('div');notification.className=\"auth-notification \".concat(type);notification.innerHTML=\"\\n      <span class=\\\"auth-notification-icon\\\">\".concat(type==='success'?'✅':'❌',\"</span>\\n      <span class=\\\"auth-notification-text\\\">\").concat(message,\"</span>\\n    \");// 添加到页面\ndocument.body.appendChild(notification);// 3秒后自动移除\nsetTimeout(()=>{if(notification.parentNode){notification.parentNode.removeChild(notification);}},3000);};const toggleUserMenu=()=>{setShowUserMenu(!showUserMenu);};const openLogin=()=>{setIsLoginVisible(true);};const closeLogin=()=>{setIsLoginVisible(false);};const handleUserManagement=()=>{setShowUserMenu(false);// 通知父组件导航到用户管理页面\nif(onAuthStateChange){onAuthStateChange(isAuthenticated,currentUser,'user-management');}};return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-mini-icon\",children:[isAuthenticated?/*#__PURE__*/// 已登录状态 - 显示用户头像\n_jsx(\"div\",{className:\"auth-user-avatar-mini\",onClick:toggleUserMenu,children:(currentUser===null||currentUser===void 0?void 0:(_currentUser$username=currentUser.username)===null||_currentUser$username===void 0?void 0:(_currentUser$username2=_currentUser$username.charAt(0))===null||_currentUser$username2===void 0?void 0:_currentUser$username2.toUpperCase())||'U'}):/*#__PURE__*/// 未登录状态 - 显示登录图标\n_jsx(\"div\",{className:\"auth-login-icon-mini\",onClick:openLogin,children:\"\\uD83D\\uDC64\"}),isAuthenticated&&showUserMenu&&/*#__PURE__*/_jsxs(\"div\",{className:\"auth-user-dropdown\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"auth-dropdown-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"auth-dropdown-avatar\",children:(currentUser===null||currentUser===void 0?void 0:(_currentUser$username3=currentUser.username)===null||_currentUser$username3===void 0?void 0:(_currentUser$username4=_currentUser$username3.charAt(0))===null||_currentUser$username4===void 0?void 0:_currentUser$username4.toUpperCase())||'U'}),/*#__PURE__*/_jsxs(\"div\",{className:\"auth-dropdown-info\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"auth-dropdown-name\",children:currentUser===null||currentUser===void 0?void 0:currentUser.username}),/*#__PURE__*/_jsx(\"div\",{className:\"auth-dropdown-role\",children:ROLE_DISPLAY_NAMES[currentUser===null||currentUser===void 0?void 0:currentUser.role]||'未知角色'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"auth-dropdown-menu\",children:[((currentUser===null||currentUser===void 0?void 0:currentUser.role)==='super_admin'||(currentUser===null||currentUser===void 0?void 0:currentUser.role)==='manager')&&/*#__PURE__*/_jsxs(\"button\",{className:\"auth-dropdown-item\",onClick:handleUserManagement,children:[/*#__PURE__*/_jsx(\"span\",{className:\"auth-dropdown-icon\",children:\"\\uD83D\\uDC65\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u7528\\u6237\\u7BA1\\u7406\"})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"auth-dropdown-item\",onClick:()=>onAuthStateChange&&onAuthStateChange(true,currentUser,'personal-settings'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"auth-dropdown-icon\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u4E2A\\u4EBA\\u8BBE\\u7F6E\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"auth-dropdown-divider\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"auth-dropdown-item logout\",onClick:handleLogout,children:[/*#__PURE__*/_jsx(\"span\",{className:\"auth-dropdown-icon\",children:\"\\uD83D\\uDEAA\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u9000\\u51FA\\u767B\\u5F55\"})]})]})]})]}),isLoginVisible&&/*#__PURE__*/_jsx(LoginForm,{onLoginSuccess:handleLoginSuccess,onClose:closeLogin})]});};export default LoginManager;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "LoginForm", "authService", "routeGuard", "ROLE_DISPLAY_NAMES", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "Login<PERSON><PERSON>ger", "_ref", "_currentUser$username", "_currentUser$username2", "_currentUser$username3", "_currentUser$username4", "onAuthStateChange", "isLoginVisible", "setIsLoginVisible", "isAuthenticated", "setIsAuthenticated", "currentUser", "setCurrentUser", "showUserMenu", "setShowUserMenu", "checkAuthStatus", "onRedirect", "targetPage", "reason", "handleClickOutside", "event", "userMenu", "document", "querySelector", "userAvatar", "contains", "target", "addEventListener", "removeEventListener", "isLoggedIn", "user", "getCurrentUser", "handleLoginSuccess", "showNotification", "handleLogout", "result", "logout", "success", "message", "type", "notification", "createElement", "className", "concat", "innerHTML", "body", "append<PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "toggleUserMenu", "openLogin", "closeLogin", "handleUserManagement", "children", "onClick", "username", "char<PERSON>t", "toUpperCase", "role", "onLoginSuccess", "onClose"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/components/LoginManager.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport LoginForm from './LoginForm';\nimport authService from '../services/authService';\nimport routeGuard from '../utils/routeGuards';\nimport { ROLE_DISPLAY_NAMES } from '../utils/rolePermissions';\nimport '../styles/LoginManager.css';\n\nconst LoginManager = ({ onAuthStateChange }) => {\n  const [isLoginVisible, setIsLoginVisible] = useState(false);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [showUserMenu, setShowUserMenu] = useState(false);\n\n  useEffect(() => {\n    // 检查初始登录状态\n    checkAuthStatus();\n    \n    // 设置路由守卫重定向回调\n    routeGuard.onRedirect((targetPage, reason) => {\n      if (targetPage === 'login') {\n        setIsLoginVisible(true);\n      }\n    });\n  }, []);\n\n  // 添加点击空白区域隐藏用户菜单的功能\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (showUserMenu) {\n        // 检查点击是否在用户菜单外部\n        const userMenu = document.querySelector('.auth-user-dropdown');\n        const userAvatar = document.querySelector('.auth-user-avatar-mini');\n        \n        if (userMenu && userAvatar && \n            !userMenu.contains(event.target) && \n            !userAvatar.contains(event.target)) {\n          setShowUserMenu(false);\n        }\n      }\n    };\n\n    // 添加事件监听器\n    document.addEventListener('mousedown', handleClickOutside);\n    \n    // 清理函数\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [showUserMenu]);\n\n  const checkAuthStatus = () => {\n    const isLoggedIn = authService.checkAuthStatus();\n    setIsAuthenticated(isLoggedIn);\n    \n    if (isLoggedIn) {\n      const user = authService.getCurrentUser();\n      setCurrentUser(user);\n      \n      // 通知父组件认证状态变化\n      if (onAuthStateChange) {\n        onAuthStateChange(true, user);\n      }\n    } else {\n      setCurrentUser(null);\n      if (onAuthStateChange) {\n        onAuthStateChange(false, null);\n      }\n    }\n  };\n\n  const handleLoginSuccess = (user) => {\n    setIsAuthenticated(true);\n    setCurrentUser(user);\n    setIsLoginVisible(false);\n    \n    // 通知父组件认证状态变化\n    if (onAuthStateChange) {\n      onAuthStateChange(true, user);\n    }\n    \n    // 显示成功提示\n    showNotification('登录成功！欢迎回来', 'success');\n  };\n\n  const handleLogout = async () => {\n    const result = await authService.logout();\n\n    if (result.success) {\n      setIsAuthenticated(false);\n      setCurrentUser(null);\n      setShowUserMenu(false);\n\n      // 通知父组件认证状态变化\n      if (onAuthStateChange) {\n        onAuthStateChange(false, null);\n      }\n\n      showNotification('已安全登出', 'success');\n    } else {\n      showNotification(result.message, 'error');\n    }\n  };\n\n  const showNotification = (message, type) => {\n    // 创建通知元素\n    const notification = document.createElement('div');\n    notification.className = `auth-notification ${type}`;\n    notification.innerHTML = `\n      <span class=\"auth-notification-icon\">${type === 'success' ? '✅' : '❌'}</span>\n      <span class=\"auth-notification-text\">${message}</span>\n    `;\n    \n    // 添加到页面\n    document.body.appendChild(notification);\n    \n    // 3秒后自动移除\n    setTimeout(() => {\n      if (notification.parentNode) {\n        notification.parentNode.removeChild(notification);\n      }\n    }, 3000);\n  };\n\n  const toggleUserMenu = () => {\n    setShowUserMenu(!showUserMenu);\n  };\n\n  const openLogin = () => {\n    setIsLoginVisible(true);\n  };\n\n  const closeLogin = () => {\n    setIsLoginVisible(false);\n  };\n\n  const handleUserManagement = () => {\n    setShowUserMenu(false);\n    // 通知父组件导航到用户管理页面\n    if (onAuthStateChange) {\n      onAuthStateChange(isAuthenticated, currentUser, 'user-management');\n    }\n  };\n\n  return (\n    <>\n      {/* 小型登录图标 */}\n      <div className=\"auth-mini-icon\">\n        {isAuthenticated ? (\n          // 已登录状态 - 显示用户头像\n          <div className=\"auth-user-avatar-mini\" onClick={toggleUserMenu}>\n            {currentUser?.username?.charAt(0)?.toUpperCase() || 'U'}\n          </div>\n        ) : (\n          // 未登录状态 - 显示登录图标\n          <div className=\"auth-login-icon-mini\" onClick={openLogin}>\n            👤\n          </div>\n        )}\n\n        {/* 用户菜单下拉框 */}\n        {isAuthenticated && showUserMenu && (\n          <div className=\"auth-user-dropdown\">\n            <div className=\"auth-dropdown-header\">\n              <div className=\"auth-dropdown-avatar\">\n                {currentUser?.username?.charAt(0)?.toUpperCase() || 'U'}\n              </div>\n              <div className=\"auth-dropdown-info\">\n                <div className=\"auth-dropdown-name\">{currentUser?.username}</div>\n                <div className=\"auth-dropdown-role\">\n                  {ROLE_DISPLAY_NAMES[currentUser?.role] || '未知角色'}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"auth-dropdown-menu\">\n              {/* 用户管理选项 - 仅超级管理员和管理员可见 */}\n              {(currentUser?.role === 'super_admin' || currentUser?.role === 'manager') && (\n                <button\n                  className=\"auth-dropdown-item\"\n                  onClick={handleUserManagement}\n                >\n                  <span className=\"auth-dropdown-icon\">👥</span>\n                  <span>用户管理</span>\n                </button>\n              )}\n\n              <button \n                className=\"auth-dropdown-item\"\n                onClick={() => onAuthStateChange && onAuthStateChange(true, currentUser, 'personal-settings')}\n              >\n                <span className=\"auth-dropdown-icon\">⚙️</span>\n                <span>个人设置</span>\n              </button>\n\n              <div className=\"auth-dropdown-divider\"></div>\n\n              <button\n                className=\"auth-dropdown-item logout\"\n                onClick={handleLogout}\n              >\n                <span className=\"auth-dropdown-icon\">🚪</span>\n                <span>退出登录</span>\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 登录表单模态框 */}\n      {isLoginVisible && (\n        <LoginForm\n          onLoginSuccess={handleLoginSuccess}\n          onClose={closeLogin}\n        />\n      )}\n    </>\n  );\n};\n\nexport default LoginManager;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,MAAO,CAAAC,UAAU,KAAM,sBAAsB,CAC7C,OAASC,kBAAkB,KAAQ,0BAA0B,CAC7D,MAAO,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEpC,KAAM,CAAAC,YAAY,CAAGC,IAAA,EAA2B,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,IAA1B,CAAEC,iBAAkB,CAAC,CAAAL,IAAA,CACzC,KAAM,CAACM,cAAc,CAAEC,iBAAiB,CAAC,CAAGpB,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACqB,eAAe,CAAEC,kBAAkB,CAAC,CAAGtB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAACuB,WAAW,CAAEC,cAAc,CAAC,CAAGxB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACyB,YAAY,CAAEC,eAAe,CAAC,CAAG1B,QAAQ,CAAC,KAAK,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd;AACA0B,eAAe,CAAC,CAAC,CAEjB;AACAvB,UAAU,CAACwB,UAAU,CAAC,CAACC,UAAU,CAAEC,MAAM,GAAK,CAC5C,GAAID,UAAU,GAAK,OAAO,CAAE,CAC1BT,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,EAAE,CAAC,CAEN;AACAnB,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8B,kBAAkB,CAAIC,KAAK,EAAK,CACpC,GAAIP,YAAY,CAAE,CAChB;AACA,KAAM,CAAAQ,QAAQ,CAAGC,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC,CAC9D,KAAM,CAAAC,UAAU,CAAGF,QAAQ,CAACC,aAAa,CAAC,wBAAwB,CAAC,CAEnE,GAAIF,QAAQ,EAAIG,UAAU,EACtB,CAACH,QAAQ,CAACI,QAAQ,CAACL,KAAK,CAACM,MAAM,CAAC,EAChC,CAACF,UAAU,CAACC,QAAQ,CAACL,KAAK,CAACM,MAAM,CAAC,CAAE,CACtCZ,eAAe,CAAC,KAAK,CAAC,CACxB,CACF,CACF,CAAC,CAED;AACAQ,QAAQ,CAACK,gBAAgB,CAAC,WAAW,CAAER,kBAAkB,CAAC,CAE1D;AACA,MAAO,IAAM,CACXG,QAAQ,CAACM,mBAAmB,CAAC,WAAW,CAAET,kBAAkB,CAAC,CAC/D,CAAC,CACH,CAAC,CAAE,CAACN,YAAY,CAAC,CAAC,CAElB,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAc,UAAU,CAAGtC,WAAW,CAACwB,eAAe,CAAC,CAAC,CAChDL,kBAAkB,CAACmB,UAAU,CAAC,CAE9B,GAAIA,UAAU,CAAE,CACd,KAAM,CAAAC,IAAI,CAAGvC,WAAW,CAACwC,cAAc,CAAC,CAAC,CACzCnB,cAAc,CAACkB,IAAI,CAAC,CAEpB;AACA,GAAIxB,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,IAAI,CAAEwB,IAAI,CAAC,CAC/B,CACF,CAAC,IAAM,CACLlB,cAAc,CAAC,IAAI,CAAC,CACpB,GAAIN,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,KAAK,CAAE,IAAI,CAAC,CAChC,CACF,CACF,CAAC,CAED,KAAM,CAAA0B,kBAAkB,CAAIF,IAAI,EAAK,CACnCpB,kBAAkB,CAAC,IAAI,CAAC,CACxBE,cAAc,CAACkB,IAAI,CAAC,CACpBtB,iBAAiB,CAAC,KAAK,CAAC,CAExB;AACA,GAAIF,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,IAAI,CAAEwB,IAAI,CAAC,CAC/B,CAEA;AACAG,gBAAgB,CAAC,WAAW,CAAE,SAAS,CAAC,CAC1C,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAA5C,WAAW,CAAC6C,MAAM,CAAC,CAAC,CAEzC,GAAID,MAAM,CAACE,OAAO,CAAE,CAClB3B,kBAAkB,CAAC,KAAK,CAAC,CACzBE,cAAc,CAAC,IAAI,CAAC,CACpBE,eAAe,CAAC,KAAK,CAAC,CAEtB;AACA,GAAIR,iBAAiB,CAAE,CACrBA,iBAAiB,CAAC,KAAK,CAAE,IAAI,CAAC,CAChC,CAEA2B,gBAAgB,CAAC,OAAO,CAAE,SAAS,CAAC,CACtC,CAAC,IAAM,CACLA,gBAAgB,CAACE,MAAM,CAACG,OAAO,CAAE,OAAO,CAAC,CAC3C,CACF,CAAC,CAED,KAAM,CAAAL,gBAAgB,CAAGA,CAACK,OAAO,CAAEC,IAAI,GAAK,CAC1C;AACA,KAAM,CAAAC,YAAY,CAAGlB,QAAQ,CAACmB,aAAa,CAAC,KAAK,CAAC,CAClDD,YAAY,CAACE,SAAS,sBAAAC,MAAA,CAAwBJ,IAAI,CAAE,CACpDC,YAAY,CAACI,SAAS,mDAAAD,MAAA,CACmBJ,IAAI,GAAK,SAAS,CAAG,GAAG,CAAG,GAAG,2DAAAI,MAAA,CAC9BL,OAAO,iBAC/C,CAED;AACAhB,QAAQ,CAACuB,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC,CAEvC;AACAO,UAAU,CAAC,IAAM,CACf,GAAIP,YAAY,CAACQ,UAAU,CAAE,CAC3BR,YAAY,CAACQ,UAAU,CAACC,WAAW,CAACT,YAAY,CAAC,CACnD,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED,KAAM,CAAAU,cAAc,CAAGA,CAAA,GAAM,CAC3BpC,eAAe,CAAC,CAACD,YAAY,CAAC,CAChC,CAAC,CAED,KAAM,CAAAsC,SAAS,CAAGA,CAAA,GAAM,CACtB3C,iBAAiB,CAAC,IAAI,CAAC,CACzB,CAAC,CAED,KAAM,CAAA4C,UAAU,CAAGA,CAAA,GAAM,CACvB5C,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAED,KAAM,CAAA6C,oBAAoB,CAAGA,CAAA,GAAM,CACjCvC,eAAe,CAAC,KAAK,CAAC,CACtB;AACA,GAAIR,iBAAiB,CAAE,CACrBA,iBAAiB,CAACG,eAAe,CAAEE,WAAW,CAAE,iBAAiB,CAAC,CACpE,CACF,CAAC,CAED,mBACEd,KAAA,CAAAE,SAAA,EAAAuD,QAAA,eAEEzD,KAAA,QAAK6C,SAAS,CAAC,gBAAgB,CAAAY,QAAA,EAC5B7C,eAAe,cACd;AACAd,IAAA,QAAK+C,SAAS,CAAC,uBAAuB,CAACa,OAAO,CAAEL,cAAe,CAAAI,QAAA,CAC5D,CAAA3C,WAAW,SAAXA,WAAW,kBAAAT,qBAAA,CAAXS,WAAW,CAAE6C,QAAQ,UAAAtD,qBAAA,kBAAAC,sBAAA,CAArBD,qBAAA,CAAuBuD,MAAM,CAAC,CAAC,CAAC,UAAAtD,sBAAA,iBAAhCA,sBAAA,CAAkCuD,WAAW,CAAC,CAAC,GAAI,GAAG,CACpD,CAAC,cAEN;AACA/D,IAAA,QAAK+C,SAAS,CAAC,sBAAsB,CAACa,OAAO,CAAEJ,SAAU,CAAAG,QAAA,CAAC,cAE1D,CAAK,CACN,CAGA7C,eAAe,EAAII,YAAY,eAC9BhB,KAAA,QAAK6C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,eACjCzD,KAAA,QAAK6C,SAAS,CAAC,sBAAsB,CAAAY,QAAA,eACnC3D,IAAA,QAAK+C,SAAS,CAAC,sBAAsB,CAAAY,QAAA,CAClC,CAAA3C,WAAW,SAAXA,WAAW,kBAAAP,sBAAA,CAAXO,WAAW,CAAE6C,QAAQ,UAAApD,sBAAA,kBAAAC,sBAAA,CAArBD,sBAAA,CAAuBqD,MAAM,CAAC,CAAC,CAAC,UAAApD,sBAAA,iBAAhCA,sBAAA,CAAkCqD,WAAW,CAAC,CAAC,GAAI,GAAG,CACpD,CAAC,cACN7D,KAAA,QAAK6C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,eACjC3D,IAAA,QAAK+C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,CAAE3C,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE6C,QAAQ,CAAM,CAAC,cACjE7D,IAAA,QAAK+C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,CAChC7D,kBAAkB,CAACkB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEgD,IAAI,CAAC,EAAI,MAAM,CAC7C,CAAC,EACH,CAAC,EACH,CAAC,cAEN9D,KAAA,QAAK6C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,EAEhC,CAAC,CAAA3C,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEgD,IAAI,IAAK,aAAa,EAAI,CAAAhD,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEgD,IAAI,IAAK,SAAS,gBACtE9D,KAAA,WACE6C,SAAS,CAAC,oBAAoB,CAC9Ba,OAAO,CAAEF,oBAAqB,CAAAC,QAAA,eAE9B3D,IAAA,SAAM+C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,CAAC,cAAE,CAAM,CAAC,cAC9C3D,IAAA,SAAA2D,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CACT,cAEDzD,KAAA,WACE6C,SAAS,CAAC,oBAAoB,CAC9Ba,OAAO,CAAEA,CAAA,GAAMjD,iBAAiB,EAAIA,iBAAiB,CAAC,IAAI,CAAEK,WAAW,CAAE,mBAAmB,CAAE,CAAA2C,QAAA,eAE9F3D,IAAA,SAAM+C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,CAAC,cAAE,CAAM,CAAC,cAC9C3D,IAAA,SAAA2D,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,cAET3D,IAAA,QAAK+C,SAAS,CAAC,uBAAuB,CAAM,CAAC,cAE7C7C,KAAA,WACE6C,SAAS,CAAC,2BAA2B,CACrCa,OAAO,CAAErB,YAAa,CAAAoB,QAAA,eAEtB3D,IAAA,SAAM+C,SAAS,CAAC,oBAAoB,CAAAY,QAAA,CAAC,cAAE,CAAM,CAAC,cAC9C3D,IAAA,SAAA2D,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,EACN,CAAC,EACH,CACN,EACE,CAAC,CAGL/C,cAAc,eACbZ,IAAA,CAACL,SAAS,EACRsE,cAAc,CAAE5B,kBAAmB,CACnC6B,OAAO,CAAET,UAAW,CACrB,CACF,EACD,CAAC,CAEP,CAAC,CAED,cAAe,CAAApD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}