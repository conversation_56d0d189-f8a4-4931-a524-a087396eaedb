// 国际化语言资源文件

// 语言资源定义
const translations = {
  'zh-CN': {
    // 个人设置页面
    personalSettings: '个人设置',
    personalSettingsDesc: '自定义您的使用体验',
    
    // 导航菜单
    profile: '个人信息',
    security: '安全设置',
    preferences: '偏好设置',
    
    // 个人信息
    profileInfo: '个人信息',
    profileInfoDesc: '管理您的基本信息',
    username: '用户名',
    email: '邮箱',
    role: '角色',
    status: '状态',
    lastLogin: '最后登录',
    createdAt: '创建时间',
    active: '活跃',
    inactive: '非活跃',
    
    // 角色名称
    super_admin: '超级管理员',
    manager: '管理员',
    director: '部长',
    user: '普通用户',
    
    // 安全设置
    securitySettings: '安全设置',
    securitySettingsDesc: '保护您的账户安全',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    changePassword: '修改密码',
    
    // 偏好设置
    preferencesSettings: '偏好设置',
    preferencesSettingsDesc: '自定义您的使用体验',
    interfaceSettings: '界面设置',
    themeMode: '主题模式',
    themeDesc: '选择您喜欢的界面主题',
    languageSettings: '语言设置',
    languageDesc: '选择界面显示语言',
    notificationSettings: '通知设置',
    emailNotifications: '邮件通知',
    emailNotificationsDesc: '接收重要更新和系统通知',
    desktopNotifications: '桌面通知',
    desktopNotificationsDesc: '在桌面显示系统通知',
    
    // 主题选项
    darkMode: '深色模式',
    lightMode: '浅色模式',
    autoMode: '跟随系统',
    
    // 语言选项
    simplifiedChinese: '简体中文',
    english: 'English',
    
    // 按钮
    save: '保存',
    cancel: '取消',
    update: '更新',
    back: '返回',
    
    // 消息提示
    saveSuccess: '设置已保存',
    updateSuccess: '信息更新成功',
    passwordChangeSuccess: '密码修改成功',
    saveError: '保存失败',
    updateError: '更新失败',
    passwordChangeError: '密码修改失败',
    
    // 验证消息
    usernameRequired: '用户名不能为空',
    emailRequired: '邮箱不能为空',
    emailInvalid: '邮箱格式不正确',
    passwordRequired: '密码不能为空',
    passwordTooShort: '密码长度至少6位',
    passwordMismatch: '两次输入的密码不一致',
    currentPasswordRequired: '请输入当前密码'
  },
  
  'en-US': {
    // Personal Settings Page
    personalSettings: 'Personal Settings',
    personalSettingsDesc: 'Customize your experience',
    
    // Navigation Menu
    profile: 'Profile',
    security: 'Security',
    preferences: 'Preferences',
    
    // Profile Information
    profileInfo: 'Profile Information',
    profileInfoDesc: 'Manage your basic information',
    username: 'Username',
    email: 'Email',
    role: 'Role',
    status: 'Status',
    lastLogin: 'Last Login',
    createdAt: 'Created At',
    active: 'Active',
    inactive: 'Inactive',
    
    // Role Names
    super_admin: 'Super Admin',
    manager: 'Manager',
    director: 'Director',
    user: 'User',
    
    // Security Settings
    securitySettings: 'Security Settings',
    securitySettingsDesc: 'Protect your account security',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    changePassword: 'Change Password',
    
    // Preferences Settings
    preferencesSettings: 'Preferences',
    preferencesSettingsDesc: 'Customize your experience',
    interfaceSettings: 'Interface Settings',
    themeMode: 'Theme Mode',
    themeDesc: 'Choose your preferred interface theme',
    languageSettings: 'Language Settings',
    languageDesc: 'Select interface display language',
    notificationSettings: 'Notification Settings',
    emailNotifications: 'Email Notifications',
    emailNotificationsDesc: 'Receive important updates and system notifications',
    desktopNotifications: 'Desktop Notifications',
    desktopNotificationsDesc: 'Show system notifications on desktop',
    
    // Theme Options
    darkMode: 'Dark Mode',
    lightMode: 'Light Mode',
    autoMode: 'Follow System',
    
    // Language Options
    simplifiedChinese: '简体中文',
    english: 'English',
    
    // Buttons
    save: 'Save',
    cancel: 'Cancel',
    update: 'Update',
    back: 'Back',
    
    // Messages
    saveSuccess: 'Settings saved',
    updateSuccess: 'Information updated successfully',
    passwordChangeSuccess: 'Password changed successfully',
    saveError: 'Save failed',
    updateError: 'Update failed',
    passwordChangeError: 'Password change failed',
    
    // Validation Messages
    usernameRequired: 'Username is required',
    emailRequired: 'Email is required',
    emailInvalid: 'Invalid email format',
    passwordRequired: 'Password is required',
    passwordTooShort: 'Password must be at least 6 characters',
    passwordMismatch: 'Passwords do not match',
    currentPasswordRequired: 'Please enter current password'
  }
};

// 当前语言状态
let currentLanguage = 'zh-CN';

// 语言切换函数
export const setLanguage = (language) => {
  if (translations[language]) {
    currentLanguage = language;
    // 保存到本地存储
    localStorage.setItem('userLanguage', language);
    // 设置HTML lang属性
    document.documentElement.lang = language;
    return true;
  }
  return false;
};

// 获取当前语言
export const getCurrentLanguage = () => {
  return currentLanguage;
};

// 翻译函数
export const t = (key) => {
  const keys = key.split('.');
  let value = translations[currentLanguage];
  
  for (const k of keys) {
    if (value && typeof value === 'object' && k in value) {
      value = value[k];
    } else {
      // 如果找不到翻译，返回key本身
      return key;
    }
  }
  
  return value || key;
};

// 初始化语言设置
export const initLanguage = () => {
  // 从本地存储获取保存的语言设置
  const savedLanguage = localStorage.getItem('userLanguage');
  if (savedLanguage && translations[savedLanguage]) {
    setLanguage(savedLanguage);
  } else {
    // 默认使用中文
    setLanguage('zh-CN');
  }
};

// 获取所有可用语言
export const getAvailableLanguages = () => {
  return Object.keys(translations);
};

// 导出默认对象
export default {
  setLanguage,
  getCurrentLanguage,
  t,
  initLanguage,
  getAvailableLanguages
};
