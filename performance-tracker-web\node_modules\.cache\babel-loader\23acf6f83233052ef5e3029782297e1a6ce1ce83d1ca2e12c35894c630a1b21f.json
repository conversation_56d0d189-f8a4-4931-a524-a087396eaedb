{"ast": null, "code": "import*as XLSX from'xlsx';class WorldClassDownloadService{constructor(){this.baseURL='/api';}// 处理选择性下载\nasync handleSelectiveDownload(selectionData){const{selectedData,format,monthRange,monthPairs,statistics}=selectionData;try{switch(format){case'excel':return await this.generateExcel(selectedData,monthRange,monthPairs,statistics);case'pdf':return await this.generatePDF(selectedData,monthRange,monthPairs,statistics);case'csv':return await this.generateCSV(selectedData,monthRange,monthPairs,statistics);default:throw new Error(\"\\u4E0D\\u652F\\u6301\\u7684\\u683C\\u5F0F: \".concat(format));}}catch(error){console.error('下载失败:',error);throw error;}}// 生成Excel文件\nasync generateExcel(selectedData,monthRange,monthPairs,statistics){try{// 创建新的工作簿\nconst workbook=XLSX.utils.book_new();// 创建主数据工作表\nthis.createMainWorksheet(workbook,selectedData,monthRange,monthPairs);// 创建汇总页\nthis.createSummarySheet(workbook,selectedData,monthRange,monthPairs,statistics);// 生成并下载文件\nconst fileName=\"\\u5BF9\\u6807\\u4E16\\u754C\\u4E00\\u6D41\\u4E3E\\u63AA\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".xlsx\");XLSX.writeFile(workbook,fileName);return{success:true,fileName,message:\"Excel\\u6587\\u4EF6\\u5DF2\\u751F\\u6210: \".concat(fileName)};}catch(error){console.error('Excel生成失败:',error);throw error;}}// 创建主数据工作表\ncreateMainWorksheet(workbook,selectedData,monthRange,monthPairs){// 准备表头\nconst headers=['序号','工作准则','2025年目标','2025年举措','负责人','权重'];// 添加月份列 - 根据选择的月份范围\nfor(let i=monthRange.start;i<=monthRange.end;i++){const[month1,month2]=monthPairs[i];headers.push(\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));headers.push(\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));}headers.push('备注');// 准备数据行\nconst rows=[headers];selectedData.forEach((item,index)=>{const data=item.data;const row=[index+1,// 重新编号\nthis.formatValue(data.工作准则),this.formatValue(data['2025年目标']),this.formatValue(data['2025年举措']),this.formatValue(data.负责人),this.formatValue(data.权重)];// 添加月份数据\nfor(let i=monthRange.start;i<=monthRange.end;i++){const[month1,month2]=monthPairs[i];row.push(this.formatValue(data[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.formatValue(data[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]));}row.push(this.formatValue(data.备注));rows.push(row);});// 创建工作表\nconst worksheet=XLSX.utils.aoa_to_sheet(rows);// 设置列宽\nconst colWidths=[{wch:8},// 序号\n{wch:18},// 工作准则（缩小一点）\n{wch:20},// 2025年目标\n{wch:20},// 2025年举措\n{wch:12},// 负责人（扩大一点点）\n{wch:8}// 权重\n];// 为每个月份对添加列宽（工作计划和完成情况）\nfor(let i=monthRange.start;i<=monthRange.end;i++){colWidths.push({wch:20});// 工作计划\ncolWidths.push({wch:20});// 完成情况\ncolWidths.push({wch:20});// 工作计划\ncolWidths.push({wch:20});// 完成情况\n}colWidths.push({wch:15});// 备注\nworksheet['!cols']=colWidths;// 设置表头样式\nthis.setHeaderStyle(worksheet,headers.length);// 添加到工作簿\nXLSX.utils.book_append_sheet(workbook,worksheet,'对标世界一流举措数据');}// 创建汇总页\ncreateSummarySheet(workbook,selectedData,monthRange,monthPairs,statistics){var _monthPairs$monthRang,_monthPairs$monthRang2;const startMonth=((_monthPairs$monthRang=monthPairs[monthRange.start])===null||_monthPairs$monthRang===void 0?void 0:_monthPairs$monthRang[0])||'';const endMonth=((_monthPairs$monthRang2=monthPairs[monthRange.end])===null||_monthPairs$monthRang2===void 0?void 0:_monthPairs$monthRang2[1])||'';const summaryData=[['对标世界一流举措导出汇总','','',''],['导出时间',this.formatDateTime(new Date()),'',''],['月份范围',\"\".concat(startMonth,\" \\u81F3 \").concat(endMonth),'',''],['','','',''],['统计项目','数值','',''],['选择项目数',statistics.totalItems,'',''],['涵盖层级数',statistics.levelCount,'',''],['','','','']];const summaryWorksheet=XLSX.utils.aoa_to_sheet(summaryData);// 设置列宽\nsummaryWorksheet['!cols']=[{wch:20},{wch:15},{wch:15},{wch:10}];XLSX.utils.book_append_sheet(workbook,summaryWorksheet,'导出汇总');}// 生成CSV文件\nasync generateCSV(selectedData,monthRange,monthPairs,statistics){try{let csvContent='\\uFEFF';// UTF-8 BOM for Excel compatibility\n// 准备表头\nconst headers=['序号','工作准则','2025年目标','2025年举措','负责人','权重'];// 添加月份列\nfor(let i=monthRange.start;i<=monthRange.end;i++){const[month1,month2]=monthPairs[i];headers.push(\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));headers.push(\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));}headers.push('备注');// 添加表头\ncsvContent+=headers.map(h=>this.escapeCsvValue(h)).join(',')+'\\n';// 添加数据行\nselectedData.forEach((item,index)=>{const data=item.data;const row=[index+1,this.formatValue(data.工作准则),this.formatValue(data['2025年目标']),this.formatValue(data['2025年举措']),this.formatValue(data.负责人),this.formatValue(data.权重)];// 添加月份数据\nfor(let i=monthRange.start;i<=monthRange.end;i++){const[month1,month2]=monthPairs[i];row.push(this.formatValue(data[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.formatValue(data[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]));}row.push(this.formatValue(data.备注));csvContent+=row.map(cell=>this.escapeCsvValue(cell)).join(',')+'\\n';});// 创建并下载文件\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u5BF9\\u6807\\u4E16\\u754C\\u4E00\\u6D41\\u4E3E\\u63AA\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".csv\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'CSV文件已生成并下载'};}catch(error){console.error('CSV生成失败:',error);throw error;}}// 生成PDF文件（临时实现）\nasync generatePDF(selectedData,monthRange,monthPairs,statistics){try{console.log('PDF生成功能待实现');const jsonData=this.prepareDataForExport(selectedData,monthRange,monthPairs,statistics);const dataStr=JSON.stringify(jsonData,null,2);const blob=new Blob([dataStr],{type:'application/json'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u5BF9\\u6807\\u4E16\\u754C\\u4E00\\u6D41\\u4E3E\\u63AA\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".json\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'PDF功能开发中，已生成JSON格式文件'};}catch(error){console.error('PDF生成失败:',error);throw error;}}// 工具方法\nformatValue(value){if(value===null||value===undefined||value===''){return'';}if(typeof value==='object'){if(value.hasOwnProperty('v'))return String(value.v);if(value.hasOwnProperty('w'))return String(value.w);if(value.hasOwnProperty('t')&&value.hasOwnProperty('v'))return String(value.v);if(value.text!==undefined)return String(value.text);if(value.richText!==undefined)return String(value.richText);if(value.value!==undefined)return String(value.value);return String(value);}return String(value);}escapeCsvValue(value){const stringValue=this.formatValue(value);if(stringValue.includes(',')||stringValue.includes('\"')||stringValue.includes('\\n')){return\"\\\"\".concat(stringValue.replace(/\"/g,'\"\"'),\"\\\"\");}return stringValue;}setHeaderStyle(worksheet,colCount){// 设置表头样式（加粗）\nfor(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(worksheet[cellRef]){worksheet[cellRef].s={font:{bold:true},fill:{fgColor:{rgb:\"E8F5E8\"}}};}}}formatDate(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year).concat(month).concat(day);}formatDateTime(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day,\" \").concat(hours,\":\").concat(minutes);}prepareDataForExport(selectedData,monthRange,monthPairs,statistics){var _monthPairs$monthRang3,_monthPairs$monthRang4;const startMonth=((_monthPairs$monthRang3=monthPairs[monthRange.start])===null||_monthPairs$monthRang3===void 0?void 0:_monthPairs$monthRang3[0])||'';const endMonth=((_monthPairs$monthRang4=monthPairs[monthRange.end])===null||_monthPairs$monthRang4===void 0?void 0:_monthPairs$monthRang4[1])||'';return{exportInfo:{exportTime:this.formatDateTime(new Date()),monthRange:\"\".concat(startMonth,\" \\u81F3 \").concat(endMonth),totalItems:statistics.totalItems,levelCount:statistics.levelCount},data:selectedData.map(item=>item.data)};}}export default new WorldClassDownloadService();", "map": {"version": 3, "names": ["XLSX", "WorldClassDownloadService", "constructor", "baseURL", "handleSelectiveDownload", "selectionData", "selectedData", "format", "<PERSON><PERSON><PERSON><PERSON>", "monthPairs", "statistics", "generateExcel", "generatePDF", "generateCSV", "Error", "concat", "error", "console", "workbook", "utils", "book_new", "createMainWorksheet", "createSummarySheet", "fileName", "formatDate", "Date", "writeFile", "success", "message", "headers", "i", "start", "end", "month1", "month2", "push", "rows", "for<PERSON>ach", "item", "index", "data", "row", "formatValue", "工作准则", "负责人", "权重", "备注", "worksheet", "aoa_to_sheet", "col<PERSON><PERSON><PERSON>", "wch", "setHeaderStyle", "length", "book_append_sheet", "_monthPairs$monthRang", "_monthPairs$monthRang2", "startMonth", "endMonth", "summaryData", "formatDateTime", "totalItems", "levelCount", "summaryWorksheet", "csv<PERSON><PERSON>nt", "map", "h", "escapeCsvValue", "join", "cell", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "log", "jsonData", "prepareDataForExport", "dataStr", "JSON", "stringify", "value", "undefined", "hasOwnProperty", "String", "v", "w", "text", "richText", "stringValue", "includes", "replace", "col<PERSON>ount", "cellRef", "encode_cell", "r", "c", "s", "font", "bold", "fill", "fgColor", "rgb", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "_monthPairs$monthRang3", "_monthPairs$monthRang4", "exportInfo", "exportTime"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块三/services/worldClassDownloadService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\n\r\nclass WorldClassDownloadService {\r\n  constructor() {\r\n    this.baseURL = '/api';\r\n  }\r\n\r\n  // 处理选择性下载\r\n  async handleSelectiveDownload(selectionData) {\r\n    const { selectedData, format, monthRange, monthPairs, statistics } = selectionData;\r\n    \r\n    try {\r\n      switch (format) {\r\n        case 'excel':\r\n          return await this.generateExcel(selectedData, monthRange, monthPairs, statistics);\r\n        case 'pdf':\r\n          return await this.generatePDF(selectedData, monthRange, monthPairs, statistics);\r\n        case 'csv':\r\n          return await this.generateCSV(selectedData, monthRange, monthPairs, statistics);\r\n        default:\r\n          throw new Error(`不支持的格式: ${format}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('下载失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成Excel文件\r\n  async generateExcel(selectedData, monthRange, monthPairs, statistics) {\r\n    try {\r\n      // 创建新的工作簿\r\n      const workbook = XLSX.utils.book_new();\r\n      \r\n      // 创建主数据工作表\r\n      this.createMainWorksheet(workbook, selectedData, monthRange, monthPairs);\r\n      \r\n      // 创建汇总页\r\n      this.createSummarySheet(workbook, selectedData, monthRange, monthPairs, statistics);\r\n\r\n      // 生成并下载文件\r\n      const fileName = `对标世界一流举措导出_${this.formatDate(new Date())}.xlsx`;\r\n      XLSX.writeFile(workbook, fileName);\r\n      \r\n      return {\r\n        success: true,\r\n        fileName,\r\n        message: `Excel文件已生成: ${fileName}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Excel生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 创建主数据工作表\r\n  createMainWorksheet(workbook, selectedData, monthRange, monthPairs) {\r\n    // 准备表头\r\n    const headers = [\r\n      '序号', '工作准则', '2025年目标', '2025年举措', '负责人', '权重'\r\n    ];\r\n    \r\n    // 添加月份列 - 根据选择的月份范围\r\n    for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n      const [month1, month2] = monthPairs[i];\r\n      headers.push(`${month1}工作计划`, `${month1}完成情况`);\r\n      headers.push(`${month2}工作计划`, `${month2}完成情况`);\r\n    }\r\n    \r\n    headers.push('备注');\r\n    \r\n    // 准备数据行\r\n    const rows = [headers];\r\n    \r\n    selectedData.forEach((item, index) => {\r\n      const data = item.data;\r\n      const row = [\r\n        index + 1, // 重新编号\r\n        this.formatValue(data.工作准则),\r\n        this.formatValue(data['2025年目标']),\r\n        this.formatValue(data['2025年举措']),\r\n        this.formatValue(data.负责人),\r\n        this.formatValue(data.权重)\r\n      ];\r\n      \r\n      // 添加月份数据\r\n      for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n        const [month1, month2] = monthPairs[i];\r\n        row.push(\r\n          this.formatValue(data[`${month1}工作计划`]),\r\n          this.formatValue(data[`${month1}完成情况`]),\r\n          this.formatValue(data[`${month2}工作计划`]),\r\n          this.formatValue(data[`${month2}完成情况`])\r\n        );\r\n      }\r\n      \r\n      row.push(this.formatValue(data.备注));\r\n      rows.push(row);\r\n    });\r\n\r\n    // 创建工作表\r\n    const worksheet = XLSX.utils.aoa_to_sheet(rows);\r\n    \r\n    // 设置列宽\r\n    const colWidths = [\r\n      { wch: 8 },   // 序号\r\n      { wch: 18 },  // 工作准则（缩小一点）\r\n      { wch: 20 },  // 2025年目标\r\n      { wch: 20 },  // 2025年举措\r\n      { wch: 12 },  // 负责人（扩大一点点）\r\n      { wch: 8 },   // 权重\r\n    ];\r\n    \r\n    // 为每个月份对添加列宽（工作计划和完成情况）\r\n    for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n      colWidths.push({ wch: 20 }); // 工作计划\r\n      colWidths.push({ wch: 20 }); // 完成情况\r\n      colWidths.push({ wch: 20 }); // 工作计划\r\n      colWidths.push({ wch: 20 }); // 完成情况\r\n    }\r\n    \r\n    colWidths.push({ wch: 15 }); // 备注\r\n    \r\n    worksheet['!cols'] = colWidths;\r\n\r\n    // 设置表头样式\r\n    this.setHeaderStyle(worksheet, headers.length);\r\n    \r\n    // 添加到工作簿\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, '对标世界一流举措数据');\r\n  }\r\n\r\n  // 创建汇总页\r\n  createSummarySheet(workbook, selectedData, monthRange, monthPairs, statistics) {\r\n    const startMonth = monthPairs[monthRange.start]?.[0] || '';\r\n    const endMonth = monthPairs[monthRange.end]?.[1] || '';\r\n    \r\n    const summaryData = [\r\n      ['对标世界一流举措导出汇总', '', '', ''],\r\n      ['导出时间', this.formatDateTime(new Date()), '', ''],\r\n      ['月份范围', `${startMonth} 至 ${endMonth}`, '', ''],\r\n      ['', '', '', ''],\r\n      ['统计项目', '数值', '', ''],\r\n      ['选择项目数', statistics.totalItems, '', ''],\r\n      ['涵盖层级数', statistics.levelCount, '', ''],\r\n      ['', '', '', '']\r\n    ];\r\n\r\n    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);\r\n    \r\n    // 设置列宽\r\n    summaryWorksheet['!cols'] = [\r\n      { wch: 20 },\r\n      { wch: 15 },\r\n      { wch: 15 },\r\n      { wch: 10 }\r\n    ];\r\n\r\n    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '导出汇总');\r\n  }\r\n\r\n  // 生成CSV文件\r\n  async generateCSV(selectedData, monthRange, monthPairs, statistics) {\r\n    try {\r\n      let csvContent = '\\uFEFF'; // UTF-8 BOM for Excel compatibility\r\n      \r\n      // 准备表头\r\n      const headers = [\r\n        '序号', '工作准则', '2025年目标', '2025年举措', '负责人', '权重'\r\n      ];\r\n      \r\n      // 添加月份列\r\n      for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n        const [month1, month2] = monthPairs[i];\r\n        headers.push(`${month1}工作计划`, `${month1}完成情况`);\r\n        headers.push(`${month2}工作计划`, `${month2}完成情况`);\r\n      }\r\n      \r\n      headers.push('备注');\r\n      \r\n      // 添加表头\r\n      csvContent += headers.map(h => this.escapeCsvValue(h)).join(',') + '\\n';\r\n      \r\n      // 添加数据行\r\n      selectedData.forEach((item, index) => {\r\n        const data = item.data;\r\n        const row = [\r\n          index + 1,\r\n          this.formatValue(data.工作准则),\r\n          this.formatValue(data['2025年目标']),\r\n          this.formatValue(data['2025年举措']),\r\n          this.formatValue(data.负责人),\r\n          this.formatValue(data.权重)\r\n        ];\r\n        \r\n        // 添加月份数据\r\n        for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n          const [month1, month2] = monthPairs[i];\r\n          row.push(\r\n            this.formatValue(data[`${month1}工作计划`]),\r\n            this.formatValue(data[`${month1}完成情况`]),\r\n            this.formatValue(data[`${month2}工作计划`]),\r\n            this.formatValue(data[`${month2}完成情况`])\r\n          );\r\n        }\r\n        \r\n        row.push(this.formatValue(data.备注));\r\n        \r\n        csvContent += row.map(cell => this.escapeCsvValue(cell)).join(',') + '\\n';\r\n      });\r\n\r\n      // 创建并下载文件\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `对标世界一流举措导出_${this.formatDate(new Date())}.csv`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'CSV文件已生成并下载'\r\n      };\r\n    } catch (error) {\r\n      console.error('CSV生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成PDF文件（临时实现）\r\n  async generatePDF(selectedData, monthRange, monthPairs, statistics) {\r\n    try {\r\n      console.log('PDF生成功能待实现');\r\n      \r\n      const jsonData = this.prepareDataForExport(selectedData, monthRange, monthPairs, statistics);\r\n      const dataStr = JSON.stringify(jsonData, null, 2);\r\n      const blob = new Blob([dataStr], { type: 'application/json' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `对标世界一流举措导出_${this.formatDate(new Date())}.json`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'PDF功能开发中，已生成JSON格式文件'\r\n      };\r\n    } catch (error) {\r\n      console.error('PDF生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 工具方法\r\n  formatValue(value) {\r\n    if (value === null || value === undefined || value === '') {\r\n      return '';\r\n    }\r\n    \r\n    if (typeof value === 'object') {\r\n      if (value.hasOwnProperty('v')) return String(value.v);\r\n      if (value.hasOwnProperty('w')) return String(value.w);\r\n      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return String(value.v);\r\n      if (value.text !== undefined) return String(value.text);\r\n      if (value.richText !== undefined) return String(value.richText);\r\n      if (value.value !== undefined) return String(value.value);\r\n      return String(value);\r\n    }\r\n    \r\n    return String(value);\r\n  }\r\n\r\n  escapeCsvValue(value) {\r\n    const stringValue = this.formatValue(value);\r\n    \r\n    if (stringValue.includes(',') || stringValue.includes('\"') || stringValue.includes('\\n')) {\r\n      return `\"${stringValue.replace(/\"/g, '\"\"')}\"`;\r\n    }\r\n    \r\n    return stringValue;\r\n  }\r\n\r\n  setHeaderStyle(worksheet, colCount) {\r\n    // 设置表头样式（加粗）\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (worksheet[cellRef]) {\r\n        worksheet[cellRef].s = {\r\n          font: { bold: true },\r\n          fill: { fgColor: { rgb: \"E8F5E8\" } }\r\n        };\r\n      }\r\n    }\r\n  }\r\n\r\n  formatDate(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}${month}${day}`;\r\n  }\r\n\r\n  formatDateTime(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n  }\r\n\r\n  prepareDataForExport(selectedData, monthRange, monthPairs, statistics) {\r\n    const startMonth = monthPairs[monthRange.start]?.[0] || '';\r\n    const endMonth = monthPairs[monthRange.end]?.[1] || '';\r\n    \r\n    return {\r\n      exportInfo: {\r\n        exportTime: this.formatDateTime(new Date()),\r\n        monthRange: `${startMonth} 至 ${endMonth}`,\r\n        totalItems: statistics.totalItems,\r\n        levelCount: statistics.levelCount\r\n      },\r\n      data: selectedData.map(item => item.data)\r\n    };\r\n  }\r\n}\r\n\r\nexport default new WorldClassDownloadService();\r\n"], "mappings": "AAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,yBAA0B,CAC9BC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,MAAM,CACvB,CAEA;AACA,KAAM,CAAAC,uBAAuBA,CAACC,aAAa,CAAE,CAC3C,KAAM,CAAEC,YAAY,CAAEC,MAAM,CAAEC,UAAU,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAGL,aAAa,CAElF,GAAI,CACF,OAAQE,MAAM,EACZ,IAAK,OAAO,CACV,MAAO,MAAM,KAAI,CAACI,aAAa,CAACL,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACnF,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACE,WAAW,CAACN,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACjF,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACG,WAAW,CAACP,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACjF,QACE,KAAM,IAAI,CAAAI,KAAK,0CAAAC,MAAA,CAAYR,MAAM,CAAE,CAAC,CACxC,CACF,CAAE,MAAOS,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAL,aAAaA,CAACL,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAE,CACpE,GAAI,CACF;AACA,KAAM,CAAAQ,QAAQ,CAAGlB,IAAI,CAACmB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,IAAI,CAACC,mBAAmB,CAACH,QAAQ,CAAEZ,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAC,CAExE;AACA,IAAI,CAACa,kBAAkB,CAACJ,QAAQ,CAAEZ,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAC,CAEnF;AACA,KAAM,CAAAa,QAAQ,iEAAAR,MAAA,CAAiB,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CACjEzB,IAAI,CAAC0B,SAAS,CAACR,QAAQ,CAAEK,QAAQ,CAAC,CAElC,MAAO,CACLI,OAAO,CAAE,IAAI,CACbJ,QAAQ,CACRK,OAAO,yCAAAb,MAAA,CAAiBQ,QAAQ,CAClC,CAAC,CACH,CAAE,MAAOP,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAK,mBAAmBA,CAACH,QAAQ,CAAEZ,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAE,CAClE;AACA,KAAM,CAAAoB,OAAO,CAAG,CACd,IAAI,CAAE,MAAM,CAAE,SAAS,CAAE,SAAS,CAAE,KAAK,CAAE,IAAI,CAChD,CAED;AACA,IAAK,GAAI,CAAAC,CAAC,CAAGtB,UAAU,CAACuB,KAAK,CAAED,CAAC,EAAItB,UAAU,CAACwB,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,KAAM,CAACG,MAAM,CAAEC,MAAM,CAAC,CAAGzB,UAAU,CAACqB,CAAC,CAAC,CACtCD,OAAO,CAACM,IAAI,IAAApB,MAAA,CAAIkB,MAAM,gCAAAlB,MAAA,CAAWkB,MAAM,4BAAM,CAAC,CAC9CJ,OAAO,CAACM,IAAI,IAAApB,MAAA,CAAImB,MAAM,gCAAAnB,MAAA,CAAWmB,MAAM,4BAAM,CAAC,CAChD,CAEAL,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC,CAElB;AACA,KAAM,CAAAC,IAAI,CAAG,CAACP,OAAO,CAAC,CAEtBvB,YAAY,CAAC+B,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACE,IAAI,CACtB,KAAM,CAAAC,GAAG,CAAG,CACVF,KAAK,CAAG,CAAC,CAAE;AACX,IAAI,CAACG,WAAW,CAACF,IAAI,CAACG,IAAI,CAAC,CAC3B,IAAI,CAACD,WAAW,CAACF,IAAI,CAAC,SAAS,CAAC,CAAC,CACjC,IAAI,CAACE,WAAW,CAACF,IAAI,CAAC,SAAS,CAAC,CAAC,CACjC,IAAI,CAACE,WAAW,CAACF,IAAI,CAACI,GAAG,CAAC,CAC1B,IAAI,CAACF,WAAW,CAACF,IAAI,CAACK,EAAE,CAAC,CAC1B,CAED;AACA,IAAK,GAAI,CAAAf,CAAC,CAAGtB,UAAU,CAACuB,KAAK,CAAED,CAAC,EAAItB,UAAU,CAACwB,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,KAAM,CAACG,MAAM,CAAEC,MAAM,CAAC,CAAGzB,UAAU,CAACqB,CAAC,CAAC,CACtCW,GAAG,CAACN,IAAI,CACN,IAAI,CAACO,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAIkB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACS,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAIkB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACS,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAImB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACQ,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAImB,MAAM,6BAAO,CACxC,CAAC,CACH,CAEAO,GAAG,CAACN,IAAI,CAAC,IAAI,CAACO,WAAW,CAACF,IAAI,CAACM,EAAE,CAAC,CAAC,CACnCV,IAAI,CAACD,IAAI,CAACM,GAAG,CAAC,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAM,SAAS,CAAG/C,IAAI,CAACmB,KAAK,CAAC6B,YAAY,CAACZ,IAAI,CAAC,CAE/C;AACA,KAAM,CAAAa,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,CAAE,CAAC,CAAI;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,CAAE,CAAK;AAAA,CACf,CAED;AACA,IAAK,GAAI,CAAApB,CAAC,CAAGtB,UAAU,CAACuB,KAAK,CAAED,CAAC,EAAItB,UAAU,CAACwB,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvDmB,SAAS,CAACd,IAAI,CAAC,CAAEe,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAC7BD,SAAS,CAACd,IAAI,CAAC,CAAEe,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAC7BD,SAAS,CAACd,IAAI,CAAC,CAAEe,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAC7BD,SAAS,CAACd,IAAI,CAAC,CAAEe,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAC/B,CAEAD,SAAS,CAACd,IAAI,CAAC,CAAEe,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAE7BH,SAAS,CAAC,OAAO,CAAC,CAAGE,SAAS,CAE9B;AACA,IAAI,CAACE,cAAc,CAACJ,SAAS,CAAElB,OAAO,CAACuB,MAAM,CAAC,CAE9C;AACApD,IAAI,CAACmB,KAAK,CAACkC,iBAAiB,CAACnC,QAAQ,CAAE6B,SAAS,CAAE,YAAY,CAAC,CACjE,CAEA;AACAzB,kBAAkBA,CAACJ,QAAQ,CAAEZ,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAE,KAAA4C,qBAAA,CAAAC,sBAAA,CAC7E,KAAM,CAAAC,UAAU,CAAG,EAAAF,qBAAA,CAAA7C,UAAU,CAACD,UAAU,CAACuB,KAAK,CAAC,UAAAuB,qBAAA,iBAA5BA,qBAAA,CAA+B,CAAC,CAAC,GAAI,EAAE,CAC1D,KAAM,CAAAG,QAAQ,CAAG,EAAAF,sBAAA,CAAA9C,UAAU,CAACD,UAAU,CAACwB,GAAG,CAAC,UAAAuB,sBAAA,iBAA1BA,sBAAA,CAA6B,CAAC,CAAC,GAAI,EAAE,CAEtD,KAAM,CAAAG,WAAW,CAAG,CAClB,CAAC,cAAc,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC5B,CAAC,MAAM,CAAE,IAAI,CAACC,cAAc,CAAC,GAAI,CAAAlC,IAAI,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACjD,CAAC,MAAM,IAAAV,MAAA,CAAKyC,UAAU,aAAAzC,MAAA,CAAM0C,QAAQ,EAAI,EAAE,CAAE,EAAE,CAAC,CAC/C,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAChB,CAAC,MAAM,CAAE,IAAI,CAAE,EAAE,CAAE,EAAE,CAAC,CACtB,CAAC,OAAO,CAAE/C,UAAU,CAACkD,UAAU,CAAE,EAAE,CAAE,EAAE,CAAC,CACxC,CAAC,OAAO,CAAElD,UAAU,CAACmD,UAAU,CAAE,EAAE,CAAE,EAAE,CAAC,CACxC,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACjB,CAED,KAAM,CAAAC,gBAAgB,CAAG9D,IAAI,CAACmB,KAAK,CAAC6B,YAAY,CAACU,WAAW,CAAC,CAE7D;AACAI,gBAAgB,CAAC,OAAO,CAAC,CAAG,CAC1B,CAAEZ,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACZ,CAEDlD,IAAI,CAACmB,KAAK,CAACkC,iBAAiB,CAACnC,QAAQ,CAAE4C,gBAAgB,CAAE,MAAM,CAAC,CAClE,CAEA;AACA,KAAM,CAAAjD,WAAWA,CAACP,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAE,CAClE,GAAI,CACF,GAAI,CAAAqD,UAAU,CAAG,QAAQ,CAAE;AAE3B;AACA,KAAM,CAAAlC,OAAO,CAAG,CACd,IAAI,CAAE,MAAM,CAAE,SAAS,CAAE,SAAS,CAAE,KAAK,CAAE,IAAI,CAChD,CAED;AACA,IAAK,GAAI,CAAAC,CAAC,CAAGtB,UAAU,CAACuB,KAAK,CAAED,CAAC,EAAItB,UAAU,CAACwB,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,KAAM,CAACG,MAAM,CAAEC,MAAM,CAAC,CAAGzB,UAAU,CAACqB,CAAC,CAAC,CACtCD,OAAO,CAACM,IAAI,IAAApB,MAAA,CAAIkB,MAAM,gCAAAlB,MAAA,CAAWkB,MAAM,4BAAM,CAAC,CAC9CJ,OAAO,CAACM,IAAI,IAAApB,MAAA,CAAImB,MAAM,gCAAAnB,MAAA,CAAWmB,MAAM,4BAAM,CAAC,CAChD,CAEAL,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC,CAElB;AACA4B,UAAU,EAAIlC,OAAO,CAACmC,GAAG,CAACC,CAAC,EAAI,IAAI,CAACC,cAAc,CAACD,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CAEvE;AACA7D,YAAY,CAAC+B,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACE,IAAI,CACtB,KAAM,CAAAC,GAAG,CAAG,CACVF,KAAK,CAAG,CAAC,CACT,IAAI,CAACG,WAAW,CAACF,IAAI,CAACG,IAAI,CAAC,CAC3B,IAAI,CAACD,WAAW,CAACF,IAAI,CAAC,SAAS,CAAC,CAAC,CACjC,IAAI,CAACE,WAAW,CAACF,IAAI,CAAC,SAAS,CAAC,CAAC,CACjC,IAAI,CAACE,WAAW,CAACF,IAAI,CAACI,GAAG,CAAC,CAC1B,IAAI,CAACF,WAAW,CAACF,IAAI,CAACK,EAAE,CAAC,CAC1B,CAED;AACA,IAAK,GAAI,CAAAf,CAAC,CAAGtB,UAAU,CAACuB,KAAK,CAAED,CAAC,EAAItB,UAAU,CAACwB,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,KAAM,CAACG,MAAM,CAAEC,MAAM,CAAC,CAAGzB,UAAU,CAACqB,CAAC,CAAC,CACtCW,GAAG,CAACN,IAAI,CACN,IAAI,CAACO,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAIkB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACS,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAIkB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACS,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAImB,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACQ,WAAW,CAACF,IAAI,IAAAzB,MAAA,CAAImB,MAAM,6BAAO,CACxC,CAAC,CACH,CAEAO,GAAG,CAACN,IAAI,CAAC,IAAI,CAACO,WAAW,CAACF,IAAI,CAACM,EAAE,CAAC,CAAC,CAEnCiB,UAAU,EAAItB,GAAG,CAACuB,GAAG,CAACI,IAAI,EAAI,IAAI,CAACF,cAAc,CAACE,IAAI,CAAC,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CAC3E,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACP,UAAU,CAAC,CAAE,CAAEQ,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,iEAAAhE,MAAA,CAAiB,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,QAAM,CAC5DmD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACL7C,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,aACX,CAAC,CACH,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAJ,WAAWA,CAACN,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAE,CAClE,GAAI,CACFO,OAAO,CAACoE,GAAG,CAAC,YAAY,CAAC,CAEzB,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,oBAAoB,CAACjF,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAC,CAC5F,KAAM,CAAA8E,OAAO,CAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CACjD,KAAM,CAAAjB,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACkB,OAAO,CAAC,CAAE,CAAEjB,IAAI,CAAE,kBAAmB,CAAC,CAAC,CAC9D,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,iEAAAhE,MAAA,CAAiB,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CAC7DmD,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACL7C,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,sBACX,CAAC,CACH,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA0B,WAAWA,CAACiD,KAAK,CAAE,CACjB,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,CACzD,MAAO,EAAE,CACX,CAEA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAC,MAAM,CAACH,KAAK,CAACI,CAAC,CAAC,CACrD,GAAIJ,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAC,MAAM,CAACH,KAAK,CAACK,CAAC,CAAC,CACrD,GAAIL,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,EAAIF,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAC,MAAM,CAACH,KAAK,CAACI,CAAC,CAAC,CAClF,GAAIJ,KAAK,CAACM,IAAI,GAAKL,SAAS,CAAE,MAAO,CAAAE,MAAM,CAACH,KAAK,CAACM,IAAI,CAAC,CACvD,GAAIN,KAAK,CAACO,QAAQ,GAAKN,SAAS,CAAE,MAAO,CAAAE,MAAM,CAACH,KAAK,CAACO,QAAQ,CAAC,CAC/D,GAAIP,KAAK,CAACA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAE,MAAM,CAACH,KAAK,CAACA,KAAK,CAAC,CACzD,MAAO,CAAAG,MAAM,CAACH,KAAK,CAAC,CACtB,CAEA,MAAO,CAAAG,MAAM,CAACH,KAAK,CAAC,CACtB,CAEAzB,cAAcA,CAACyB,KAAK,CAAE,CACpB,KAAM,CAAAQ,WAAW,CAAG,IAAI,CAACzD,WAAW,CAACiD,KAAK,CAAC,CAE3C,GAAIQ,WAAW,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAID,WAAW,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAID,WAAW,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAE,CACxF,WAAArF,MAAA,CAAWoF,WAAW,CAACE,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,OAC5C,CAEA,MAAO,CAAAF,WAAW,CACpB,CAEAhD,cAAcA,CAACJ,SAAS,CAAEuD,QAAQ,CAAE,CAClC;AACA,IAAK,GAAI,CAAAxE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGwE,QAAQ,CAAExE,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAyE,OAAO,CAAGvG,IAAI,CAACmB,KAAK,CAACqF,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE5E,CAAE,CAAC,CAAC,CACtD,GAAIiB,SAAS,CAACwD,OAAO,CAAC,CAAE,CACtBxD,SAAS,CAACwD,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAK,CAAC,CACpBC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CACrC,CAAC,CACH,CACF,CACF,CAEAxF,UAAUA,CAACyF,IAAI,CAAE,CACf,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAGtB,MAAM,CAACmB,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGzB,MAAM,CAACmB,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,SAAAvG,MAAA,CAAUmG,IAAI,EAAAnG,MAAA,CAAGqG,KAAK,EAAArG,MAAA,CAAGwG,GAAG,EAC9B,CAEA5D,cAAcA,CAACsD,IAAI,CAAE,CACnB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAGtB,MAAM,CAACmB,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGzB,MAAM,CAACmB,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAG,KAAK,CAAG3B,MAAM,CAACmB,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAK,OAAO,CAAG7B,MAAM,CAACmB,IAAI,CAACW,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,SAAAvG,MAAA,CAAUmG,IAAI,MAAAnG,MAAA,CAAIqG,KAAK,MAAArG,MAAA,CAAIwG,GAAG,MAAAxG,MAAA,CAAI0G,KAAK,MAAA1G,MAAA,CAAI4G,OAAO,EACpD,CAEApC,oBAAoBA,CAACjF,YAAY,CAAEE,UAAU,CAAEC,UAAU,CAAEC,UAAU,CAAE,KAAAmH,sBAAA,CAAAC,sBAAA,CACrE,KAAM,CAAAtE,UAAU,CAAG,EAAAqE,sBAAA,CAAApH,UAAU,CAACD,UAAU,CAACuB,KAAK,CAAC,UAAA8F,sBAAA,iBAA5BA,sBAAA,CAA+B,CAAC,CAAC,GAAI,EAAE,CAC1D,KAAM,CAAApE,QAAQ,CAAG,EAAAqE,sBAAA,CAAArH,UAAU,CAACD,UAAU,CAACwB,GAAG,CAAC,UAAA8F,sBAAA,iBAA1BA,sBAAA,CAA6B,CAAC,CAAC,GAAI,EAAE,CAEtD,MAAO,CACLC,UAAU,CAAE,CACVC,UAAU,CAAE,IAAI,CAACrE,cAAc,CAAC,GAAI,CAAAlC,IAAI,CAAC,CAAC,CAAC,CAC3CjB,UAAU,IAAAO,MAAA,CAAKyC,UAAU,aAAAzC,MAAA,CAAM0C,QAAQ,CAAE,CACzCG,UAAU,CAAElD,UAAU,CAACkD,UAAU,CACjCC,UAAU,CAAEnD,UAAU,CAACmD,UACzB,CAAC,CACDrB,IAAI,CAAElC,YAAY,CAAC0D,GAAG,CAAC1B,IAAI,EAAIA,IAAI,CAACE,IAAI,CAC1C,CAAC,CACH,CACF,CAEA,cAAe,IAAI,CAAAvC,yBAAyB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}