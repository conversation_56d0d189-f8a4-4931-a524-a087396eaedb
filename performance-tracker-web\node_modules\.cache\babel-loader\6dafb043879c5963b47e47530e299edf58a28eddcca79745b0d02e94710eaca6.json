{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'../styles/ProjectOne.css';import projectOneService from'../services/projectOneService';import projectOneDownloadService from'../services/projectOneDownloadService';import ProjectOneDownloadModal from'../components/ProjectOneDownloadModal';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const ProjectOne=_ref=>{let{onNavigate}=_ref;const[data,setData]=useState([]);const[filteredData,setFilteredData]=useState([]);const[loading,setLoading]=useState(true);const[editingCell,setEditingCell]=useState(null);const[syncStatus,setSyncStatus]=useState('已同步');const[dataWarning,setDataWarning]=useState(null);// 数据输入优化相关状态\nconst[tempValues,setTempValues]=useState({});// 临时存储编辑中的值\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);// 负责人筛选相关状态\nconst[selectedResponsiblePersons,setSelectedResponsiblePersons]=useState([]);const[tempSelectedPersons,setTempSelectedPersons]=useState([]);// 临时存储选择的负责人\nconst[showResponsibleFilter,setShowResponsibleFilter]=useState(false);// 类型筛选状态\nconst[typeFilter,setTypeFilter]=useState('全部');const[availableTypes,setAvailableTypes]=useState([]);// 动态获取的类型列表\n// 月份切换状态 - 从2月开始按月递增\nconst[currentMonthPair,setCurrentMonthPair]=useState(()=>{// 根据当前月份设置初始显示的月份对\nconst currentMonth=new Date().getMonth()+1;// getMonth()返回0-11，加1得到1-12\nconst months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];// 找到当前月份在数组中的位置\nlet currentIndex=-1;months.forEach((month,index)=>{const monthNum=parseInt(month.replace('月',''));if(monthNum===currentMonth){currentIndex=index;}});// 如果找到当前月份，返回包含当前月份的月份对索引\nif(currentIndex>=0){// 如果当前月份是第一个月（如2月），则显示第一对\nif(currentIndex===0)return 0;// 否则，尽量让当前月份显示在第一个位置\nreturn Math.max(0,currentIndex-1);}// 如果当前月份不在范围内，默认显示第一对\nreturn 0;});const monthPairs=[['2月','3月'],['3月','4月'],['4月','5月'],['5月','6月'],['6月','7月'],['7月','8月'],['8月','9月'],['9月','10月'],['10月','11月'],['11月','12月']];// 下载相关状态\nconst[showDownloadModal,setShowDownloadModal]=useState(false);const[downloadLoading,setDownloadLoading]=useState(false);useEffect(()=>{loadData();// 设置同步回调\nprojectOneService.setSyncCallbacks(()=>setSyncStatus('同步成功'),error=>setSyncStatus('同步失败'));// 清理函数\nreturn()=>{if(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}if(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}};},[]);useEffect(()=>{// 应用筛选\napplyFilters();},[data,selectedResponsiblePersons,typeFilter]);const loadData=async()=>{setLoading(true);try{console.log('开始加载1号项目责任状数据...');const projectOneData=await projectOneService.loadProjectOneData();console.log('原始加载的数据:',projectOneData);console.log('原始数据条数:',projectOneData?projectOneData.length:0);if(projectOneData&&Array.isArray(projectOneData)){// 过滤掉可能的模拟数据，只保留真实数据\nconst filteredData=projectOneData.filter(item=>{if(!item||typeof item!=='object')return false;const problem=item['需解决的问题/提升的需求']||'';const person=item.负责人||'';// 排除明显的模拟数据特征\nconst isMockData=problem.includes('测试')||problem.includes('模拟')||problem.includes('示例')||problem.includes('Test')||problem.includes('Mock')||person==='张三'||person==='李四'||person==='王五'||problem==='测试项目2'||problem.startsWith('测试');return!isMockData;});console.log('过滤后的数据条数:',filteredData.length);console.log('过滤后的数据:',filteredData);// 如果过滤后的数据为空，使用原始数据（可能全部都是真实数据）\nlet finalData=filteredData.length>0?filteredData:projectOneData;// 添加数据去重保护机制\nconst uniqueData=[];const seenProblems=new Set();finalData.forEach((item,index)=>{const problem=item['需解决的问题/提升的需求']||'';const problemKey=problem.trim();// 如果问题描述为空或已存在，跳过（但记录日志）\nif(!problemKey){console.warn(\"\\u7B2C\".concat(index+1,\"\\u884C\\u6570\\u636E\\u95EE\\u9898\\u63CF\\u8FF0\\u4E3A\\u7A7A\\uFF0C\\u5DF2\\u8DF3\\u8FC7\"));return;}if(seenProblems.has(problemKey)){console.warn(\"\\u7B2C\".concat(index+1,\"\\u884C\\u6570\\u636E\\u91CD\\u590D\\uFF0C\\u95EE\\u9898\\u63CF\\u8FF0: \\\"\").concat(problemKey.substring(0,30),\"...\\\"\\uFF0C\\u5DF2\\u8DF3\\u8FC7\"));return;}seenProblems.add(problemKey);uniqueData.push(item);});setData(uniqueData);console.log('原始数据条数:',finalData.length);console.log('去重后数据条数:',uniqueData.length);if(finalData.length!==uniqueData.length){const removedCount=finalData.length-uniqueData.length;console.warn(\"\\u68C0\\u6D4B\\u5230\\u5E76\\u79FB\\u9664\\u4E86 \".concat(removedCount,\" \\u6761\\u91CD\\u590D\\u6570\\u636E\"));setDataWarning(\"\\u68C0\\u6D4B\\u5230\\u5E76\\u81EA\\u52A8\\u79FB\\u9664\\u4E86 \".concat(removedCount,\" \\u6761\\u91CD\\u590D\\u6570\\u636E\"));}else{setDataWarning(null);}// 更新类型列表\nconst types=[...new Set(uniqueData.map(item=>item.类型).filter(type=>type&&type.trim()!==''))].sort();setAvailableTypes(types);console.log('可用类型:',types);// 执行数据完整性检查\nvalidateDataIntegrity(uniqueData);}else{console.error('未获取到有效数据');setData([]);}}catch(error){console.error('数据加载失败:',error);setData([]);}setLoading(false);};// 数据完整性检查\nconst validateDataIntegrity=dataToCheck=>{if(!dataToCheck||!Array.isArray(dataToCheck)){console.error('数据格式错误：不是有效数组');return false;}const issues=[];const seenProblems=new Set();const seenNumbers=new Set();dataToCheck.forEach((item,index)=>{const rowNum=index+1;// 检查必要字段\nif(!item['需解决的问题/提升的需求']||!item['需解决的问题/提升的需求'].trim()){issues.push(\"\\u7B2C\".concat(rowNum,\"\\u884C\\uFF1A\\u7F3A\\u5C11\\u95EE\\u9898\\u63CF\\u8FF0\"));}// 检查重复问题\nconst problem=(item['需解决的问题/提升的需求']||'').trim();if(problem&&seenProblems.has(problem)){issues.push(\"\\u7B2C\".concat(rowNum,\"\\u884C\\uFF1A\\u95EE\\u9898\\u63CF\\u8FF0\\u91CD\\u590D \\\"\").concat(problem.substring(0,30),\"...\\\"\"));}else if(problem){seenProblems.add(problem);}// 检查序号重复\nconst number=item.序号;if(number!==null&&number!==undefined&&number!==''){if(seenNumbers.has(number)){issues.push(\"\\u7B2C\".concat(rowNum,\"\\u884C\\uFF1A\\u5E8F\\u53F7\\u91CD\\u590D \\\"\").concat(number,\"\\\"\"));}else{seenNumbers.add(number);}}});if(issues.length>0){console.warn('🔍 数据完整性检查发现问题:');issues.forEach(issue=>console.warn(\"   - \".concat(issue)));return false;}else{console.log('✅ 数据完整性检查通过');return true;}};// 强制刷新数据的方法\nconst forceRefresh=async()=>{console.log('强制刷新数据...');setSyncStatus('刷新中...');await loadData();setSyncStatus('刷新完成');};// 获取所有负责人列表\nconst getAllResponsiblePersons=()=>{// 使用当前筛选后的数据作为数据源\nconst sourceData=filteredData.length>0?filteredData:data;if(!sourceData||sourceData.length===0)return[];const responsiblePersons=sourceData.map(item=>item.负责人).filter(person=>person&&String(person).trim()!=='').reduce((acc,person)=>{// 处理多个负责人用逗号、括号等分隔的情况\nconst persons=String(person).split(/[,，;；、（）()]/).map(p=>p.trim()).filter(p=>p);persons.forEach(p=>{if(!acc.includes(p)){acc.push(p);}});return acc;},[]);return responsiblePersons.sort();};// 应用筛选\nconst applyFilters=()=>{let filtered=[...data];// 应用类型筛选\nif(typeFilter!=='全部'){filtered=filtered.filter(item=>item.类型===typeFilter);}// 应用负责人筛选\nif(selectedResponsiblePersons.length>0){filtered=filtered.filter(item=>{if(!item.负责人)return false;// 处理多个负责人的情况\nconst itemPersons=String(item.负责人).split(/[,，;；、（）()]/).map(p=>p.trim()).filter(p=>p);return itemPersons.some(person=>selectedResponsiblePersons.includes(person));});}setFilteredData(filtered);};// 处理负责人筛选\nconst handleResponsiblePersonChange=person=>{const newTempSelected=tempSelectedPersons.includes(person)?tempSelectedPersons.filter(p=>p!==person):[...tempSelectedPersons,person];setTempSelectedPersons(newTempSelected);};const applyResponsiblePersonFilter=()=>{setSelectedResponsiblePersons([...tempSelectedPersons]);setShowResponsibleFilter(false);};const resetResponsiblePersonFilter=()=>{setTempSelectedPersons([]);setSelectedResponsiblePersons([]);setShowResponsibleFilter(false);};// 处理单元格编辑\nconst handleCellEdit=(rowIndex,field,value)=>{const cellKey=\"\".concat(rowIndex,\"-\").concat(field);// 更新临时值\nsetTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[cellKey]:value}));// 清除之前的保存定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的保存定时器\nsaveTimeoutRef.current=setTimeout(()=>{saveCell(rowIndex,field,value);},1000);// 1秒后自动保存\n};const saveCell=async(rowIndex,field,value)=>{try{setSyncStatus('同步中...');// 取消之前的保存请求\nif(pendingSaveRef.current){var _pendingSaveRef$curre3,_pendingSaveRef$curre4;(_pendingSaveRef$curre3=(_pendingSaveRef$curre4=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre3===void 0?void 0:_pendingSaveRef$curre3.call(_pendingSaveRef$curre4);}// 创建新的保存请求\nconst abortController=new AbortController();pendingSaveRef.current=abortController;const success=await projectOneService.updateData(rowIndex,field,value);if(success&&!abortController.signal.aborted){// 更新本地数据\nconst newData=[...data];if(newData[rowIndex]){newData[rowIndex][field]=value;setData(newData);}// 清除临时值\nconst cellKey=\"\".concat(rowIndex,\"-\").concat(field);setTempValues(prev=>{const newTempValues=_objectSpread({},prev);delete newTempValues[cellKey];return newTempValues;});setSyncStatus('已同步');}}catch(error){console.error('保存失败:',error);setSyncStatus('同步失败');}};// 处理月份切换\nconst handleMonthChange=direction=>{if(direction==='prev'&&currentMonthPair>0){setCurrentMonthPair(currentMonthPair-1);}else if(direction==='next'&&currentMonthPair<monthPairs.length-1){setCurrentMonthPair(currentMonthPair+1);}};// 处理下载\nconst handleDownload=async selectionData=>{setDownloadLoading(true);try{const result=await projectOneDownloadService.handleSelectiveDownload(selectionData);console.log('下载完成:',result);setShowDownloadModal(false);}catch(error){console.error('下载失败:',error);alert('下载失败: '+error.message);}finally{setDownloadLoading(false);}};// 渲染可编辑单元格\nconst renderEditableCell=function(value,rowIndex,field){var _ref2,_tempValues$cellKey;let cellType=arguments.length>3&&arguments[3]!==undefined?arguments[3]:'default';const cellKey=\"\".concat(rowIndex,\"-\").concat(field);const isEditing=editingCell===cellKey;// 确保displayValue始终是字符串类型，过滤掉对象和无效值\nconst rawValue=(_ref2=(_tempValues$cellKey=tempValues[cellKey])!==null&&_tempValues$cellKey!==void 0?_tempValues$cellKey:value)!==null&&_ref2!==void 0?_ref2:'';let displayValue='';// 只处理有效的字符串和数字值\nif(rawValue!==null&&rawValue!==undefined){if(typeof rawValue==='string'){displayValue=rawValue;}else if(typeof rawValue==='number'){displayValue=String(rawValue);}else if(typeof rawValue==='object'){// 如果是对象，不显示任何内容\ndisplayValue='';}else{displayValue=String(rawValue);}}// 检查是否为只读字段：\n// 1. 序号字段始终只读\n// 2. 工作计划、完成情况字段只读（从源表同步，禁止编辑）\nconst isReadOnlyField=field==='序号'||field.includes('工作计划')||field.includes('完成情况');const isEditable=!isReadOnlyField;const cellClasses=['editable-cell',cellType,isEditing?'editing':'',isReadOnlyField?'readonly':'',displayValue?'has-content':'empty'].filter(Boolean).join(' ');// 根据字段类型设置提示文本\nlet titleText='点击编辑';if(field==='序号'){titleText='此字段不可编辑';}else if(field.includes('工作计划')||field.includes('完成情况')){titleText='此字段从源表同步，不可编辑';}if(isEditing&&isEditable){return/*#__PURE__*/_jsx(\"textarea\",{className:\"cell-editor\",value:displayValue,onChange:e=>{const newValue=e.target.value;setTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[cellKey]:newValue}));},onBlur:()=>{setEditingCell(null);handleCellEdit(rowIndex,field,displayValue);},onKeyDown:e=>{if(e.key==='Enter'&&!e.shiftKey){e.preventDefault();e.target.blur();}if(e.key==='Escape'){setTempValues(prev=>{const newTempValues=_objectSpread({},prev);delete newTempValues[cellKey];return newTempValues;});setEditingCell(null);}},autoFocus:true,rows:3});}return/*#__PURE__*/_jsx(\"div\",{className:cellClasses,onClick:()=>isEditable&&setEditingCell(cellKey),title:titleText,style:isReadOnlyField?{cursor:'not-allowed'}:{},children:displayValue.includes('\\n')?displayValue.split('\\n').map((line,i,arr)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[line,i<arr.length-1&&/*#__PURE__*/_jsx(\"br\",{})]},i)):displayValue});};// 渲染月份列\nconst renderMonthColumns=(row,rowIndex)=>{const[month1,month2]=monthPairs[currentMonthPair];return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-plan\",children:renderEditableCell(row[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")],rowIndex,\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),'month-plan')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-complete\",children:renderEditableCell(row[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")],rowIndex,\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"),'month-complete')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-plan\",children:renderEditableCell(row[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")],rowIndex,\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),'month-plan')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-month-complete\",children:renderEditableCell(row[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")],rowIndex,\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"),'month-complete')})]});};// 获取当前显示的数据\nconst displayData=filteredData.length>0?filteredData:data;const[month1,month2]=monthPairs[currentMonthPair];if(loading){return/*#__PURE__*/_jsx(\"div\",{className:\"project-one-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u52A0\\u8F7D1\\u53F7\\u9879\\u76EE\\u8D23\\u4EFB\\u72B6\\u6570\\u636E...\"})]})});}// 渲染负责人筛选面板\nconst renderResponsibleFilterPanel=()=>{if(!showResponsibleFilter)return null;const allPersons=getAllResponsiblePersons();return/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-responsible-filter-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-filter-panel-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u9009\\u62E9\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>setShowResponsibleFilter(false),className:\"project-one-close-panel-btn\",children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-filter-panel-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"project-one-filter-options\",children:allPersons.map(person=>/*#__PURE__*/_jsx(\"div\",{className:\"project-one-filter-option \".concat(tempSelectedPersons.includes(person)?'selected':''),onClick:()=>handleResponsiblePersonChange(person),children:person},person))}),/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-filter-actions\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:resetResponsiblePersonFilter,className:\"project-one-clear-filter-btn\",children:\"\\u6E05\\u9664\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(\"button\",{onClick:applyResponsiblePersonFilter,className:\"project-one-apply-filter-btn\",children:\"\\u5E94\\u7528\\u7B5B\\u9009\"})]})]})]});};return/*#__PURE__*/_jsxs(\"div\",{className:\"project-one-container\",children:[renderResponsibleFilterPanel(),dataWarning&&/*#__PURE__*/_jsx(\"div\",{style:{background:'linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1))',border:'1px solid rgba(255, 193, 7, 0.3)',borderRadius:'12px',margin:'20px auto',width:'100%',maxWidth:'none',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(255, 193, 7, 0.2)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',padding:'16px 24px',gap:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',background:'linear-gradient(45deg, #ffc107, #ff9800)',borderRadius:'50%',fontSize:'18px'},children:\"\\u26A0\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'rgba(255, 255, 255, 0.7)',marginBottom:'4px',fontWeight:'500'},children:\"\\u6570\\u636E\\u8D28\\u91CF\\u63D0\\u9192\"}),/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'16px',color:'#fff',fontWeight:'600'},children:dataWarning})]}),/*#__PURE__*/_jsx(\"button\",{style:{display:'flex',alignItems:'center',gap:'8px',padding:'8px 16px',background:'linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',border:'1px solid rgba(255, 255, 255, 0.3)',borderRadius:'8px',color:'#fff',fontSize:'14px',fontWeight:'500',cursor:'pointer',transition:'all 0.3s ease'},onClick:()=>setDataWarning(null),title:\"\\u5173\\u95ED\\u63D0\\u9192\",children:/*#__PURE__*/_jsx(\"span\",{children:\"\\u2715\"})})]})}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{style:{background:'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',border:'1px solid rgba(0, 212, 170, 0.3)',borderRadius:'12px',margin:'20px auto',width:'100%',maxWidth:'none',backdropFilter:'blur(10px)',boxShadow:'0 4px 20px rgba(0, 212, 170, 0.2)'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',padding:'16px 24px',gap:'16px'},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:'flex',alignItems:'center',justifyContent:'center',width:'40px',height:'40px',background:'linear-gradient(45deg, #00d4aa, #20ff4d)',borderRadius:'50%',fontSize:'18px'},children:\"\\uD83D\\uDD0D\"}),/*#__PURE__*/_jsxs(\"div\",{style:{flex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'14px',color:'rgba(255, 255, 255, 0.7)',marginBottom:'4px',fontWeight:'500'},children:\"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\"}),/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'16px',color:'#fff',fontWeight:'600'},children:[\"\\u8D1F\\u8D23\\u4EBA\\uFF1A\",/*#__PURE__*/_jsx(\"span\",{style:{color:'#20ff4d',fontWeight:'bold',textShadow:'0 0 10px rgba(32, 255, 77, 0.6)',margin:'0 8px'},children:selectedResponsiblePersons.join('、')}),/*#__PURE__*/_jsxs(\"span\",{style:{color:'#00d4aa',fontSize:'14px'},children:[\"\\uFF08\",displayData.length,\"\\u4E2A\\u9879\\u76EE\\uFF09\"]})]})]}),/*#__PURE__*/_jsxs(\"button\",{style:{display:'flex',alignItems:'center',gap:'8px',padding:'8px 16px',background:'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',border:'1px solid rgba(255, 75, 75, 0.4)',borderRadius:'8px',color:'#ff6b6b',fontSize:'14px',fontWeight:'500',cursor:'pointer',transition:'all 0.3s ease'},onClick:()=>{setSelectedResponsiblePersons([]);setTempSelectedPersons([]);},onMouseEnter:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';e.target.style.transform='translateY(-1px)';},onMouseLeave:e=>{e.target.style.background='linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';e.target.style.transform='translateY(0)';},title:\"\\u6E05\\u9664\\u7B5B\\u9009\\u6761\\u4EF6\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2715\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u6E05\\u9664\\u7B5B\\u9009\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"back-btn-top\",onClick:()=>onNavigate('home'),children:\"\\u8FD4\\u56DE\\u9996\\u9875\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"page-title\",children:\"1\\u53F7\\u9879\\u76EE\\u8D23\\u4EFB\\u72B6\"}),/*#__PURE__*/_jsx(\"p\",{className:\"page-subtitle\",children:\"\\u91CD\\u70B9\\u9879\\u76EE\\u63A8\\u8FDB\\xB7\\u8D23\\u4EFB\\u843D\\u5B9E\\xB7\\u6708\\u5EA6\\u8DDF\\u8E2A\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"header-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"refresh-btn-new\",onClick:forceRefresh,children:\"\\uD83D\\uDD04 \\u5237\\u65B0\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"div\",{className:\"sync-status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"status-indicator \".concat(syncStatus.includes('成功')||syncStatus==='已同步'?'success':syncStatus.includes('失败')?'error':'pending'),children:syncStatus})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-panel\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"controls-left\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"filter-controls\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-group\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u9879\\u76EE\\u7C7B\\u578B:\"}),/*#__PURE__*/_jsxs(\"select\",{className:\"type-selector\",value:typeFilter,onChange:e=>setTypeFilter(e.target.value),children:[/*#__PURE__*/_jsx(\"option\",{value:\"\\u5168\\u90E8\",children:\"\\u5168\\u90E8\\u7C7B\\u578B\"}),availableTypes.map(type=>/*#__PURE__*/_jsx(\"option\",{value:type,children:type},type))]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item-inline\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u5F53\\u524D\\u663E\\u793A:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"stat-value\",children:[displayData.length,\" \\u9879\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"responsible-filter-btn-new\",onClick:()=>{setTempSelectedPersons([...selectedResponsiblePersons]);setShowResponsibleFilter(!showResponsibleFilter);},children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-icon\",children:\"\\uD83D\\uDC65\"}),selectedResponsiblePersons.length>0&&/*#__PURE__*/_jsx(\"div\",{className:\"filter-badge\",children:selectedResponsiblePersons.length})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"month-navigation-new\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>handleMonthChange('prev'),disabled:currentMonthPair===0,children:\"\\u25C0\\u4E0A\\u4E2A\\u6708\\u4EFD\\u5BF9\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"current-months-new\",children:[month1,\" / \",month2]}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-btn-new\",onClick:()=>handleMonthChange('next'),disabled:currentMonthPair===monthPairs.length-1,children:\"\\u4E0B\\u4E2A\\u6708\\u4EFD\\u5BF9\\u25B6\"})]}),/*#__PURE__*/_jsx(\"button\",{className:\"download-btn\",onClick:()=>setShowDownloadModal(true),children:\"\\uD83D\\uDCCA \\u9009\\u62E9\\u6027\\u4E0B\\u8F7D\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"project-one-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"col-number\",children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-problem\",children:\"\\u9700\\u89E3\\u51B3\\u7684\\u95EE\\u9898/\\u63D0\\u5347\\u7684\\u9700\\u6C42\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-type\",children:\"\\u7C7B\\u578B\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-target\",children:\"2025\\u5E74\\u76EE\\u6807\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-deadline\",children:\"\\u5B8C\\u6210\\u65F6\\u95F4\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-form\",children:\"\\u5F00\\u5C55\\u5F62\\u5F0F\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-responsible\",children:\"\\u8D1F\\u8D23\\u4EBA\"}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-plan\",children:[month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"]}),/*#__PURE__*/_jsxs(\"th\",{className:\"col-month-complete\",children:[month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\"]})]})}),/*#__PURE__*/_jsx(\"tbody\",{children:displayData.map((item,index)=>{const actualIndex=data.findIndex(d=>d===item);// 确保key的唯一性，避免-1导致的渲染问题\nconst uniqueKey=actualIndex>=0?actualIndex:\"filtered-\".concat(index);return/*#__PURE__*/_jsxs(\"tr\",{className:\"data-row\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-number\",children:index+1}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-problem\",children:renderEditableCell(item['需解决的问题/提升的需求'],actualIndex,'需解决的问题/提升的需求')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-type\",children:renderEditableCell(item.类型,actualIndex,'类型')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-target\",children:renderEditableCell(item['2025年目标'],actualIndex,'2025年目标')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-deadline\",children:renderEditableCell(item.完成时间,actualIndex,'完成时间')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-form\",children:renderEditableCell(item.开展形式,actualIndex,'开展形式')}),/*#__PURE__*/_jsx(\"td\",{className:\"data-cell col-responsible\",children:renderEditableCell(item.负责人,actualIndex,'负责人')}),renderMonthColumns(item,actualIndex)]},uniqueKey);})})]})}),displayData.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"no-data\",children:/*#__PURE__*/_jsx(\"p\",{children:\"\\u5F53\\u524D\\u7B5B\\u9009\\u6761\\u4EF6\\u4E0B\\u6CA1\\u6709\\u6570\\u636E\"})}),showDownloadModal&&/*#__PURE__*/_jsx(ProjectOneDownloadModal,{data:displayData,monthPairs:monthPairs,onDownload:handleDownload,onClose:()=>setShowDownloadModal(false),loading:downloadLoading})]});};export default ProjectOne;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "projectOneService", "projectOneDownloadService", "ProjectOneDownloadModal", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "ProjectOne", "_ref", "onNavigate", "data", "setData", "filteredData", "setFilteredData", "loading", "setLoading", "editingCell", "setEditingCell", "syncStatus", "setSyncStatus", "dataWarning", "setDataWarning", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "saveTimeoutRef", "pendingSaveRef", "selected<PERSON>espons<PERSON><PERSON><PERSON><PERSON>", "setSelectedResponsiblePersons", "tempSelected<PERSON>ersons", "setTempSelected<PERSON><PERSON>s", "showResponsibleFilter", "setShowResponsibleFilter", "typeFilter", "setTypeFilter", "availableTypes", "setAvailableTypes", "currentMonthPair", "setCurrentMonthPair", "currentMonth", "Date", "getMonth", "months", "currentIndex", "for<PERSON>ach", "month", "index", "monthNum", "parseInt", "replace", "Math", "max", "monthPairs", "showDownloadModal", "setShowDownloadModal", "downloadLoading", "setDownloadLoading", "loadData", "setSyncCallbacks", "error", "current", "clearTimeout", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "applyFilters", "console", "log", "projectOneData", "loadProjectOneData", "length", "Array", "isArray", "filter", "item", "problem", "person", "负责人", "isMockData", "includes", "startsWith", "finalData", "uniqueData", "seenProblems", "Set", "<PERSON><PERSON><PERSON>", "trim", "warn", "concat", "has", "substring", "add", "push", "removedCount", "types", "map", "类型", "type", "sort", "validateDataIntegrity", "dataToCheck", "issues", "seenN<PERSON>bers", "row<PERSON>um", "number", "序号", "undefined", "issue", "forceRefresh", "getAllResponsiblePersons", "sourceData", "<PERSON><PERSON><PERSON><PERSON>", "String", "reduce", "acc", "persons", "split", "p", "filtered", "itemPersons", "some", "handleResponsiblePersonChange", "newTempSelected", "applyResponsiblePersonFilter", "resetResponsiblePersonFilter", "handleCellEdit", "rowIndex", "field", "value", "cellKey", "prev", "_objectSpread", "setTimeout", "saveCell", "_pendingSaveRef$curre3", "_pendingSaveRef$curre4", "abortController", "AbortController", "success", "updateData", "signal", "aborted", "newData", "newTempValues", "handleMonthChange", "direction", "handleDownload", "selectionData", "result", "handleSelectiveDownload", "alert", "message", "renderEditableCell", "_ref2", "_tempValues$cellKey", "cellType", "arguments", "isEditing", "rawValue", "displayValue", "isReadOnlyField", "isEditable", "cellClasses", "Boolean", "join", "titleText", "className", "onChange", "e", "newValue", "target", "onBlur", "onKeyDown", "key", "shift<PERSON>ey", "preventDefault", "blur", "autoFocus", "rows", "onClick", "title", "style", "cursor", "children", "line", "i", "arr", "renderMonthColumns", "row", "month1", "month2", "displayData", "renderResponsibleFilterPanel", "<PERSON><PERSON><PERSON><PERSON>", "background", "border", "borderRadius", "margin", "width", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ilter", "boxShadow", "display", "alignItems", "padding", "gap", "justifyContent", "height", "fontSize", "flex", "color", "marginBottom", "fontWeight", "transition", "textShadow", "onMouseEnter", "transform", "onMouseLeave", "disabled", "actualIndex", "findIndex", "d", "<PERSON><PERSON><PERSON>", "完成时间", "开展形式", "onDownload", "onClose"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块五/pages/ProjectOne.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/ProjectOne.css';\nimport projectOneService from '../services/projectOneService';\nimport projectOneDownloadService from '../services/projectOneDownloadService';\nimport ProjectOneDownloadModal from '../components/ProjectOneDownloadModal';\n\nconst ProjectOne = ({ onNavigate }) => {\n  const [data, setData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingCell, setEditingCell] = useState(null);\n  const [syncStatus, setSyncStatus] = useState('已同步');\n  const [dataWarning, setDataWarning] = useState(null);\n\n  // 数据输入优化相关状态\n  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n  \n  // 负责人筛选相关状态\n  const [selectedResponsiblePersons, setSelectedResponsiblePersons] = useState([]);\n  const [tempSelectedPersons, setTempSelectedPersons] = useState([]); // 临时存储选择的负责人\n  const [showResponsibleFilter, setShowResponsibleFilter] = useState(false);\n\n  // 类型筛选状态\n  const [typeFilter, setTypeFilter] = useState('全部');\n  const [availableTypes, setAvailableTypes] = useState([]); // 动态获取的类型列表\n  \n  // 月份切换状态 - 从2月开始按月递增\n  const [currentMonthPair, setCurrentMonthPair] = useState(() => {\n    // 根据当前月份设置初始显示的月份对\n    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12\n    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\n\n    // 找到当前月份在数组中的位置\n    let currentIndex = -1;\n    months.forEach((month, index) => {\n      const monthNum = parseInt(month.replace('月', ''));\n      if (monthNum === currentMonth) {\n        currentIndex = index;\n      }\n    });\n\n    // 如果找到当前月份，返回包含当前月份的月份对索引\n    if (currentIndex >= 0) {\n      // 如果当前月份是第一个月（如2月），则显示第一对\n      if (currentIndex === 0) return 0;\n      // 否则，尽量让当前月份显示在第一个位置\n      return Math.max(0, currentIndex - 1);\n    }\n\n    // 如果当前月份不在范围内，默认显示第一对\n    return 0;\n  });\n  const monthPairs = [\n    ['2月', '3月'], ['3月', '4月'], ['4月', '5月'],\n    ['5月', '6月'], ['6月', '7月'], ['7月', '8月'],\n    ['8月', '9月'], ['9月', '10月'], ['10月', '11月'],\n    ['11月', '12月']\n  ];\n  \n  // 下载相关状态\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n\n  useEffect(() => {\n    loadData();\n\n    // 设置同步回调\n    projectOneService.setSyncCallbacks(\n      () => setSyncStatus('同步成功'),\n      (error) => setSyncStatus('同步失败')\n    );\n\n    // 清理函数\n    return () => {\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n      }\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    // 应用筛选\n    applyFilters();\n  }, [data, selectedResponsiblePersons, typeFilter]);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      console.log('开始加载1号项目责任状数据...');\n      const projectOneData = await projectOneService.loadProjectOneData();\n      console.log('原始加载的数据:', projectOneData);\n      console.log('原始数据条数:', projectOneData ? projectOneData.length : 0);\n\n      if (projectOneData && Array.isArray(projectOneData)) {\n        // 过滤掉可能的模拟数据，只保留真实数据\n        const filteredData = projectOneData.filter(item => {\n          if (!item || typeof item !== 'object') return false;\n\n          const problem = item['需解决的问题/提升的需求'] || '';\n          const person = item.负责人 || '';\n\n          // 排除明显的模拟数据特征\n          const isMockData =\n            problem.includes('测试') ||\n            problem.includes('模拟') ||\n            problem.includes('示例') ||\n            problem.includes('Test') ||\n            problem.includes('Mock') ||\n            person === '张三' ||\n            person === '李四' ||\n            person === '王五' ||\n            problem === '测试项目2' ||\n            problem.startsWith('测试');\n\n          return !isMockData;\n        });\n\n        console.log('过滤后的数据条数:', filteredData.length);\n        console.log('过滤后的数据:', filteredData);\n\n        // 如果过滤后的数据为空，使用原始数据（可能全部都是真实数据）\n        let finalData = filteredData.length > 0 ? filteredData : projectOneData;\n\n        // 添加数据去重保护机制\n        const uniqueData = [];\n        const seenProblems = new Set();\n\n        finalData.forEach((item, index) => {\n          const problem = item['需解决的问题/提升的需求'] || '';\n          const problemKey = problem.trim();\n\n          // 如果问题描述为空或已存在，跳过（但记录日志）\n          if (!problemKey) {\n            console.warn(`第${index + 1}行数据问题描述为空，已跳过`);\n            return;\n          }\n\n          if (seenProblems.has(problemKey)) {\n            console.warn(`第${index + 1}行数据重复，问题描述: \"${problemKey.substring(0, 30)}...\"，已跳过`);\n            return;\n          }\n\n          seenProblems.add(problemKey);\n          uniqueData.push(item);\n        });\n\n        setData(uniqueData);\n        console.log('原始数据条数:', finalData.length);\n        console.log('去重后数据条数:', uniqueData.length);\n        if (finalData.length !== uniqueData.length) {\n          const removedCount = finalData.length - uniqueData.length;\n          console.warn(`检测到并移除了 ${removedCount} 条重复数据`);\n          setDataWarning(`检测到并自动移除了 ${removedCount} 条重复数据`);\n        } else {\n          setDataWarning(null);\n        }\n\n        // 更新类型列表\n        const types = [...new Set(uniqueData.map(item => item.类型).filter(type => type && type.trim() !== ''))].sort();\n        setAvailableTypes(types);\n        console.log('可用类型:', types);\n\n        // 执行数据完整性检查\n        validateDataIntegrity(uniqueData);\n      } else {\n        console.error('未获取到有效数据');\n        setData([]);\n      }\n    } catch (error) {\n      console.error('数据加载失败:', error);\n      setData([]);\n    }\n    setLoading(false);\n  };\n\n  // 数据完整性检查\n  const validateDataIntegrity = (dataToCheck) => {\n    if (!dataToCheck || !Array.isArray(dataToCheck)) {\n      console.error('数据格式错误：不是有效数组');\n      return false;\n    }\n\n    const issues = [];\n    const seenProblems = new Set();\n    const seenNumbers = new Set();\n\n    dataToCheck.forEach((item, index) => {\n      const rowNum = index + 1;\n\n      // 检查必要字段\n      if (!item['需解决的问题/提升的需求'] || !item['需解决的问题/提升的需求'].trim()) {\n        issues.push(`第${rowNum}行：缺少问题描述`);\n      }\n\n      // 检查重复问题\n      const problem = (item['需解决的问题/提升的需求'] || '').trim();\n      if (problem && seenProblems.has(problem)) {\n        issues.push(`第${rowNum}行：问题描述重复 \"${problem.substring(0, 30)}...\"`);\n      } else if (problem) {\n        seenProblems.add(problem);\n      }\n\n      // 检查序号重复\n      const number = item.序号;\n      if (number !== null && number !== undefined && number !== '') {\n        if (seenNumbers.has(number)) {\n          issues.push(`第${rowNum}行：序号重复 \"${number}\"`);\n        } else {\n          seenNumbers.add(number);\n        }\n      }\n    });\n\n    if (issues.length > 0) {\n      console.warn('🔍 数据完整性检查发现问题:');\n      issues.forEach(issue => console.warn(`   - ${issue}`));\n      return false;\n    } else {\n      console.log('✅ 数据完整性检查通过');\n      return true;\n    }\n  };\n\n  // 强制刷新数据的方法\n  const forceRefresh = async () => {\n    console.log('强制刷新数据...');\n    setSyncStatus('刷新中...');\n    await loadData();\n    setSyncStatus('刷新完成');\n  };\n\n  // 获取所有负责人列表\n  const getAllResponsiblePersons = () => {\n    // 使用当前筛选后的数据作为数据源\n    const sourceData = filteredData.length > 0 ? filteredData : data;\n    if (!sourceData || sourceData.length === 0) return [];\n    \n    const responsiblePersons = sourceData\n      .map(item => item.负责人)\n      .filter(person => person && String(person).trim() !== '')\n      .reduce((acc, person) => {\n        // 处理多个负责人用逗号、括号等分隔的情况\n        const persons = String(person).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);\n        persons.forEach(p => {\n          if (!acc.includes(p)) {\n            acc.push(p);\n          }\n        });\n        return acc;\n      }, []);\n    \n    return responsiblePersons.sort();\n  };\n\n  // 应用筛选\n  const applyFilters = () => {\n    let filtered = [...data];\n\n    // 应用类型筛选\n    if (typeFilter !== '全部') {\n      filtered = filtered.filter(item => item.类型 === typeFilter);\n    }\n\n    // 应用负责人筛选\n    if (selectedResponsiblePersons.length > 0) {\n      filtered = filtered.filter(item => {\n        if (!item.负责人) return false;\n\n        // 处理多个负责人的情况\n        const itemPersons = String(item.负责人).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);\n        return itemPersons.some(person => selectedResponsiblePersons.includes(person));\n      });\n    }\n\n    setFilteredData(filtered);\n  };\n\n  // 处理负责人筛选\n  const handleResponsiblePersonChange = (person) => {\n    const newTempSelected = tempSelectedPersons.includes(person)\n      ? tempSelectedPersons.filter(p => p !== person)\n      : [...tempSelectedPersons, person];\n    setTempSelectedPersons(newTempSelected);\n  };\n\n  const applyResponsiblePersonFilter = () => {\n    setSelectedResponsiblePersons([...tempSelectedPersons]);\n    setShowResponsibleFilter(false);\n  };\n\n  const resetResponsiblePersonFilter = () => {\n    setTempSelectedPersons([]);\n    setSelectedResponsiblePersons([]);\n    setShowResponsibleFilter(false);\n  };\n\n  // 处理单元格编辑\n  const handleCellEdit = (rowIndex, field, value) => {\n    const cellKey = `${rowIndex}-${field}`;\n    \n    // 更新临时值\n    setTempValues(prev => ({\n      ...prev,\n      [cellKey]: value\n    }));\n\n    // 清除之前的保存定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的保存定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      saveCell(rowIndex, field, value);\n    }, 1000); // 1秒后自动保存\n  };\n\n  const saveCell = async (rowIndex, field, value) => {\n    try {\n      setSyncStatus('同步中...');\n      \n      // 取消之前的保存请求\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存请求\n      const abortController = new AbortController();\n      pendingSaveRef.current = abortController;\n\n      const success = await projectOneService.updateData(rowIndex, field, value);\n      \n      if (success && !abortController.signal.aborted) {\n        // 更新本地数据\n        const newData = [...data];\n        if (newData[rowIndex]) {\n          newData[rowIndex][field] = value;\n          setData(newData);\n        }\n        \n        // 清除临时值\n        const cellKey = `${rowIndex}-${field}`;\n        setTempValues(prev => {\n          const newTempValues = { ...prev };\n          delete newTempValues[cellKey];\n          return newTempValues;\n        });\n        \n        setSyncStatus('已同步');\n      }\n    } catch (error) {\n      console.error('保存失败:', error);\n      setSyncStatus('同步失败');\n    }\n  };\n\n  // 处理月份切换\n  const handleMonthChange = (direction) => {\n    if (direction === 'prev' && currentMonthPair > 0) {\n      setCurrentMonthPair(currentMonthPair - 1);\n    } else if (direction === 'next' && currentMonthPair < monthPairs.length - 1) {\n      setCurrentMonthPair(currentMonthPair + 1);\n    }\n  };\n\n  // 处理下载\n  const handleDownload = async (selectionData) => {\n    setDownloadLoading(true);\n    try {\n      const result = await projectOneDownloadService.handleSelectiveDownload(selectionData);\n      console.log('下载完成:', result);\n      setShowDownloadModal(false);\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败: ' + error.message);\n    } finally {\n      setDownloadLoading(false);\n    }\n  };\n\n  // 渲染可编辑单元格\n  const renderEditableCell = (value, rowIndex, field, cellType = 'default') => {\n    const cellKey = `${rowIndex}-${field}`;\n    const isEditing = editingCell === cellKey;\n    // 确保displayValue始终是字符串类型，过滤掉对象和无效值\n    const rawValue = tempValues[cellKey] ?? value ?? '';\n    let displayValue = '';\n\n    // 只处理有效的字符串和数字值\n    if (rawValue !== null && rawValue !== undefined) {\n      if (typeof rawValue === 'string') {\n        displayValue = rawValue;\n      } else if (typeof rawValue === 'number') {\n        displayValue = String(rawValue);\n      } else if (typeof rawValue === 'object') {\n        // 如果是对象，不显示任何内容\n        displayValue = '';\n      } else {\n        displayValue = String(rawValue);\n      }\n    }\n    // 检查是否为只读字段：\n    // 1. 序号字段始终只读\n    // 2. 工作计划、完成情况字段只读（从源表同步，禁止编辑）\n    const isReadOnlyField = field === '序号' ||\n                           field.includes('工作计划') ||\n                           field.includes('完成情况');\n    const isEditable = !isReadOnlyField;\n\n    const cellClasses = [\n      'editable-cell',\n      cellType,\n      isEditing ? 'editing' : '',\n      isReadOnlyField ? 'readonly' : '',\n      displayValue ? 'has-content' : 'empty'\n    ].filter(Boolean).join(' ');\n\n    // 根据字段类型设置提示文本\n    let titleText = '点击编辑';\n    if (field === '序号') {\n      titleText = '此字段不可编辑';\n    } else if (field.includes('工作计划') || field.includes('完成情况')) {\n      titleText = '此字段从源表同步，不可编辑';\n    }\n\n    if (isEditing && isEditable) {\n      return (\n        <textarea\n          className=\"cell-editor\"\n          value={displayValue}\n          onChange={(e) => {\n            const newValue = e.target.value;\n            setTempValues(prev => ({\n              ...prev,\n              [cellKey]: newValue\n            }));\n          }}\n          onBlur={() => {\n            setEditingCell(null);\n            handleCellEdit(rowIndex, field, displayValue);\n          }}\n          onKeyDown={(e) => {\n            if (e.key === 'Enter' && !e.shiftKey) {\n              e.preventDefault();\n              e.target.blur();\n            }\n            if (e.key === 'Escape') {\n              setTempValues(prev => {\n                const newTempValues = { ...prev };\n                delete newTempValues[cellKey];\n                return newTempValues;\n              });\n              setEditingCell(null);\n            }\n          }}\n          autoFocus\n          rows={3}\n        />\n      );\n    }\n\n    return (\n      <div\n        className={cellClasses}\n        onClick={() => isEditable && setEditingCell(cellKey)}\n        title={titleText}\n        style={isReadOnlyField ? { cursor: 'not-allowed' } : {}}\n      >\n        {displayValue.includes('\\n') ?\n          displayValue.split('\\n').map((line, i, arr) => (\n            <React.Fragment key={i}>\n              {line}\n              {i < arr.length - 1 && <br />}\n            </React.Fragment>\n          )) :\n          displayValue\n        }\n      </div>\n    );\n  };\n\n  // 渲染月份列\n  const renderMonthColumns = (row, rowIndex) => {\n    const [month1, month2] = monthPairs[currentMonthPair];\n\n    return (\n      <>\n        <td className=\"data-cell col-month-plan\">\n          {renderEditableCell(row[`${month1}工作计划`], rowIndex, `${month1}工作计划`, 'month-plan')}\n        </td>\n        <td className=\"data-cell col-month-complete\">\n          {renderEditableCell(row[`${month1}完成情况`], rowIndex, `${month1}完成情况`, 'month-complete')}\n        </td>\n        <td className=\"data-cell col-month-plan\">\n          {renderEditableCell(row[`${month2}工作计划`], rowIndex, `${month2}工作计划`, 'month-plan')}\n        </td>\n        <td className=\"data-cell col-month-complete\">\n          {renderEditableCell(row[`${month2}完成情况`], rowIndex, `${month2}完成情况`, 'month-complete')}\n        </td>\n      </>\n    );\n  };\n\n  // 获取当前显示的数据\n  const displayData = filteredData.length > 0 ? filteredData : data;\n  const [month1, month2] = monthPairs[currentMonthPair];\n\n  if (loading) {\n    return (\n      <div className=\"project-one-container\">\n        <div className=\"project-one-loading\">\n          <div className=\"loading-spinner\"></div>\n          <p>正在加载1号项目责任状数据...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // 渲染负责人筛选面板\n  const renderResponsibleFilterPanel = () => {\n    if (!showResponsibleFilter) return null;\n    const allPersons = getAllResponsiblePersons();\n\n    return (\n      <div className=\"project-one-responsible-filter-panel\">\n        <div className=\"project-one-filter-panel-header\">\n          <h3>选择负责人</h3>\n          <button onClick={() => setShowResponsibleFilter(false)} className=\"project-one-close-panel-btn\">×</button>\n        </div>\n        <div className=\"project-one-filter-panel-content\">\n          <div className=\"project-one-filter-options\">\n            {allPersons.map(person => (\n              <div\n                key={person}\n                className={`project-one-filter-option ${tempSelectedPersons.includes(person) ? 'selected' : ''}`}\n                onClick={() => handleResponsiblePersonChange(person)}\n              >\n                {person}\n              </div>\n            ))}\n          </div>\n          <div className=\"project-one-filter-actions\">\n            <button onClick={resetResponsiblePersonFilter} className=\"project-one-clear-filter-btn\">\n              清除选择\n            </button>\n            <button onClick={applyResponsiblePersonFilter} className=\"project-one-apply-filter-btn\">\n              应用筛选\n            </button>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"project-one-container\">\n      {/* 负责人筛选面板 */}\n      {renderResponsibleFilterPanel()}\n\n      {/* 数据警告提示 */}\n      {dataWarning && (\n        <div\n          style={{\n            background: 'linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1))',\n            border: '1px solid rgba(255, 193, 7, 0.3)',\n            borderRadius: '12px',\n            margin: '20px auto',\n            width: '100%',\n            maxWidth: 'none',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(255, 193, 7, 0.2)'\n          }}\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              padding: '16px 24px',\n              gap: '16px'\n            }}\n          >\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                background: 'linear-gradient(45deg, #ffc107, #ff9800)',\n                borderRadius: '50%',\n                fontSize: '18px'\n              }}\n            >\n              ⚠️\n            </div>\n            <div style={{ flex: 1 }}>\n              <div\n                style={{\n                  fontSize: '14px',\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  marginBottom: '4px',\n                  fontWeight: '500'\n                }}\n              >\n                数据质量提醒\n              </div>\n              <div\n                style={{\n                  fontSize: '16px',\n                  color: '#fff',\n                  fontWeight: '600'\n                }}\n              >\n                {dataWarning}\n              </div>\n            </div>\n            <button\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                padding: '8px 16px',\n                background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))',\n                border: '1px solid rgba(255, 255, 255, 0.3)',\n                borderRadius: '8px',\n                color: '#fff',\n                fontSize: '14px',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n              onClick={() => setDataWarning(null)}\n              title=\"关闭提醒\"\n            >\n              <span>✕</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 负责人筛选状态提示 */}\n      {selectedResponsiblePersons.length > 0 && (\n        <div\n          style={{\n            background: 'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',\n            border: '1px solid rgba(0, 212, 170, 0.3)',\n            borderRadius: '12px',\n            margin: '20px auto',\n            width: '100%',\n            maxWidth: 'none',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 20px rgba(0, 212, 170, 0.2)'\n          }}\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              padding: '16px 24px',\n              gap: '16px'\n            }}\n          >\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                width: '40px',\n                height: '40px',\n                background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',\n                borderRadius: '50%',\n                fontSize: '18px'\n              }}\n            >\n              🔍\n            </div>\n            <div style={{ flex: 1 }}>\n              <div\n                style={{\n                  fontSize: '14px',\n                  color: 'rgba(255, 255, 255, 0.7)',\n                  marginBottom: '4px',\n                  fontWeight: '500'\n                }}\n              >\n                当前筛选条件\n              </div>\n              <div\n                style={{\n                  fontSize: '16px',\n                  color: '#fff',\n                  fontWeight: '600'\n                }}\n              >\n                负责人：\n                <span style={{\n                  color: '#20ff4d',\n                  fontWeight: 'bold',\n                  textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',\n                  margin: '0 8px'\n                }}>\n                  {selectedResponsiblePersons.join('、')}\n                </span>\n                <span style={{\n                  color: '#00d4aa',\n                  fontSize: '14px'\n                }}>\n                  （{displayData.length}个项目）\n                </span>\n              </div>\n            </div>\n            <button\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                padding: '8px 16px',\n                background: 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',\n                border: '1px solid rgba(255, 75, 75, 0.4)',\n                borderRadius: '8px',\n                color: '#ff6b6b',\n                fontSize: '14px',\n                fontWeight: '500',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              }}\n              onClick={() => {\n                setSelectedResponsiblePersons([]);\n                setTempSelectedPersons([]);\n              }}\n              onMouseEnter={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';\n                e.target.style.transform = 'translateY(-1px)';\n              }}\n              onMouseLeave={(e) => {\n                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';\n                e.target.style.transform = 'translateY(0)';\n              }}\n              title=\"清除筛选条件\"\n            >\n              <span>✕</span>\n              <span>清除筛选</span>\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* 页面头部 */}\n      <div className=\"page-header\">\n        <button\n          className=\"back-btn-top\"\n          onClick={() => onNavigate('home')}\n        >\n          返回首页\n        </button>\n\n        <div className=\"header-center\">\n          <h1 className=\"page-title\">1号项目责任状</h1>\n          <p className=\"page-subtitle\">重点项目推进·责任落实·月度跟踪</p>\n        </div>\n\n        <div className=\"header-actions\">\n          <button className=\"refresh-btn-new\" onClick={forceRefresh}>\n            🔄 刷新数据\n          </button>\n          <div className=\"sync-status\">\n            <span\n              className={`status-indicator ${syncStatus.includes('成功') || syncStatus === '已同步' ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}\n            >\n              {syncStatus}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* 控制面板 */}\n      <div className=\"control-panel\">\n        <div className=\"controls-left\">\n          <div className=\"filter-controls\">\n            <div className=\"filter-group\">\n              <label>项目类型:</label>\n              <select\n                className=\"type-selector\"\n                value={typeFilter}\n                onChange={(e) => setTypeFilter(e.target.value)}\n              >\n                <option value=\"全部\">全部类型</option>\n                {availableTypes.map(type => (\n                  <option key={type} value={type}>{type}</option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"stat-item-inline\">\n              <span className=\"stat-label\">当前显示:</span>\n              <span className=\"stat-value\">{displayData.length} 项</span>\n            </div>\n\n            <div\n              className=\"responsible-filter-btn-new\"\n              onClick={() => {\n                setTempSelectedPersons([...selectedResponsiblePersons]);\n                setShowResponsibleFilter(!showResponsibleFilter);\n              }}\n            >\n              <span className=\"filter-icon\">👥</span>\n              {selectedResponsiblePersons.length > 0 && (\n                <div className=\"filter-badge\">\n                  {selectedResponsiblePersons.length}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"action-buttons\">\n          <div className=\"month-navigation-new\">\n            <button\n              className=\"nav-btn-new\"\n              onClick={() => handleMonthChange('prev')}\n              disabled={currentMonthPair === 0}\n            >\n              ◀上个月份对\n            </button>\n            <span className=\"current-months-new\">\n              {month1} / {month2}\n            </span>\n            <button\n              className=\"nav-btn-new\"\n              onClick={() => handleMonthChange('next')}\n              disabled={currentMonthPair === monthPairs.length - 1}\n            >\n              下个月份对▶\n            </button>\n          </div>\n          <button\n            className=\"download-btn\"\n            onClick={() => setShowDownloadModal(true)}\n          >\n            📊 选择性下载\n          </button>\n        </div>\n      </div>\n\n      {/* 数据表格 */}\n      <div className=\"table-container\">\n        <table className=\"project-one-table\">\n          <thead>\n            <tr>\n              <th className=\"col-number\">序号</th>\n              <th className=\"col-problem\">需解决的问题/提升的需求</th>\n              <th className=\"col-type\">类型</th>\n              <th className=\"col-target\">2025年目标</th>\n              <th className=\"col-deadline\">完成时间</th>\n              <th className=\"col-form\">开展形式</th>\n              <th className=\"col-responsible\">负责人</th>\n              <th className=\"col-month-plan\">{month1}工作计划</th>\n              <th className=\"col-month-complete\">{month1}完成情况</th>\n              <th className=\"col-month-plan\">{month2}工作计划</th>\n              <th className=\"col-month-complete\">{month2}完成情况</th>\n            </tr>\n          </thead>\n          <tbody>\n            {displayData.map((item, index) => {\n              const actualIndex = data.findIndex(d => d === item);\n              // 确保key的唯一性，避免-1导致的渲染问题\n              const uniqueKey = actualIndex >= 0 ? actualIndex : `filtered-${index}`;\n              return (\n                <tr key={uniqueKey} className=\"data-row\">\n                  <td className=\"data-cell col-number\">\n                    {index + 1}\n                  </td>\n                  <td className=\"data-cell col-problem\">\n                    {renderEditableCell(item['需解决的问题/提升的需求'], actualIndex, '需解决的问题/提升的需求')}\n                  </td>\n                  <td className=\"data-cell col-type\">\n                    {renderEditableCell(item.类型, actualIndex, '类型')}\n                  </td>\n                  <td className=\"data-cell col-target\">\n                    {renderEditableCell(item['2025年目标'], actualIndex, '2025年目标')}\n                  </td>\n                  <td className=\"data-cell col-deadline\">\n                    {renderEditableCell(item.完成时间, actualIndex, '完成时间')}\n                  </td>\n                  <td className=\"data-cell col-form\">\n                    {renderEditableCell(item.开展形式, actualIndex, '开展形式')}\n                  </td>\n                  <td className=\"data-cell col-responsible\">\n                    {renderEditableCell(item.负责人, actualIndex, '负责人')}\n                  </td>\n                  {renderMonthColumns(item, actualIndex)}\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n\n      {displayData.length === 0 && (\n        <div className=\"no-data\">\n          <p>当前筛选条件下没有数据</p>\n        </div>\n      )}\n\n      {/* 下载模态框 */}\n      {showDownloadModal && (\n        <ProjectOneDownloadModal\n          data={displayData}\n          monthPairs={monthPairs}\n          onDownload={handleDownload}\n          onClose={() => setShowDownloadModal(false)}\n          loading={downloadLoading}\n        />\n      )}\n    </div>\n  );\n};\n\n\n\nexport default ProjectOne;\n"], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,iBAAiB,KAAM,+BAA+B,CAC7D,MAAO,CAAAC,yBAAyB,KAAM,uCAAuC,CAC7E,MAAO,CAAAC,uBAAuB,KAAM,uCAAuC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAE5E,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAChC,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACkB,YAAY,CAAEC,eAAe,CAAC,CAAGnB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACoB,OAAO,CAAEC,UAAU,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACsB,WAAW,CAAEC,cAAc,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACwB,UAAU,CAAEC,aAAa,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAEpD;AACA,KAAM,CAAC4B,UAAU,CAAEC,aAAa,CAAC,CAAG7B,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE;AAClD,KAAM,CAAA8B,cAAc,CAAG5B,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAA6B,cAAc,CAAG7B,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAC8B,0BAA0B,CAAEC,6BAA6B,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAChF,KAAM,CAACkC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGnC,QAAQ,CAAC,EAAE,CAAC,CAAE;AACpE,KAAM,CAACoC,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CAEzE;AACA,KAAM,CAACsC,UAAU,CAAEC,aAAa,CAAC,CAAGvC,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACwC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzC,QAAQ,CAAC,EAAE,CAAC,CAAE;AAE1D;AACA,KAAM,CAAC0C,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3C,QAAQ,CAAC,IAAM,CAC7D;AACA,KAAM,CAAA4C,YAAY,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAE;AAChD,KAAM,CAAAC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAEpF;AACA,GAAI,CAAAC,YAAY,CAAG,CAAC,CAAC,CACrBD,MAAM,CAACE,OAAO,CAAC,CAACC,KAAK,CAAEC,KAAK,GAAK,CAC/B,KAAM,CAAAC,QAAQ,CAAGC,QAAQ,CAACH,KAAK,CAACI,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAAC,CACjD,GAAIF,QAAQ,GAAKR,YAAY,CAAE,CAC7BI,YAAY,CAAGG,KAAK,CACtB,CACF,CAAC,CAAC,CAEF;AACA,GAAIH,YAAY,EAAI,CAAC,CAAE,CACrB;AACA,GAAIA,YAAY,GAAK,CAAC,CAAE,MAAO,EAAC,CAChC;AACA,MAAO,CAAAO,IAAI,CAACC,GAAG,CAAC,CAAC,CAAER,YAAY,CAAG,CAAC,CAAC,CACtC,CAEA;AACA,MAAO,EAAC,CACV,CAAC,CAAC,CACF,KAAM,CAAAS,UAAU,CAAG,CACjB,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CACxC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,IAAI,CAAC,CACxC,CAAC,IAAI,CAAE,IAAI,CAAC,CAAE,CAAC,IAAI,CAAE,KAAK,CAAC,CAAE,CAAC,KAAK,CAAE,KAAK,CAAC,CAC3C,CAAC,KAAK,CAAE,KAAK,CAAC,CACf,CAED;AACA,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC4D,eAAe,CAAEC,kBAAkB,CAAC,CAAG7D,QAAQ,CAAC,KAAK,CAAC,CAE7DC,SAAS,CAAC,IAAM,CACd6D,QAAQ,CAAC,CAAC,CAEV;AACA1D,iBAAiB,CAAC2D,gBAAgB,CAChC,IAAMtC,aAAa,CAAC,MAAM,CAAC,CAC1BuC,KAAK,EAAKvC,aAAa,CAAC,MAAM,CACjC,CAAC,CAED;AACA,MAAO,IAAM,CACX,GAAIK,cAAc,CAACmC,OAAO,CAAE,CAC1BC,YAAY,CAACpC,cAAc,CAACmC,OAAO,CAAC,CACtC,CACA,GAAIlC,cAAc,CAACkC,OAAO,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAArC,cAAc,CAACkC,OAAO,EAACI,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAENnE,SAAS,CAAC,IAAM,CACd;AACAsE,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACvD,IAAI,CAAEgB,0BAA0B,CAAEM,UAAU,CAAC,CAAC,CAElD,KAAM,CAAAwB,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BzC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACFmD,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC,CAC/B,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAtE,iBAAiB,CAACuE,kBAAkB,CAAC,CAAC,CACnEH,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEC,cAAc,CAAC,CACvCF,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEC,cAAc,CAAGA,cAAc,CAACE,MAAM,CAAG,CAAC,CAAC,CAElE,GAAIF,cAAc,EAAIG,KAAK,CAACC,OAAO,CAACJ,cAAc,CAAC,CAAE,CACnD;AACA,KAAM,CAAAxD,YAAY,CAAGwD,cAAc,CAACK,MAAM,CAACC,IAAI,EAAI,CACjD,GAAI,CAACA,IAAI,EAAI,MAAO,CAAAA,IAAI,GAAK,QAAQ,CAAE,MAAO,MAAK,CAEnD,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAAC,cAAc,CAAC,EAAI,EAAE,CAC1C,KAAM,CAAAE,MAAM,CAAGF,IAAI,CAACG,GAAG,EAAI,EAAE,CAE7B;AACA,KAAM,CAAAC,UAAU,CACdH,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBJ,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBJ,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBJ,OAAO,CAACI,QAAQ,CAAC,MAAM,CAAC,EACxBJ,OAAO,CAACI,QAAQ,CAAC,MAAM,CAAC,EACxBH,MAAM,GAAK,IAAI,EACfA,MAAM,GAAK,IAAI,EACfA,MAAM,GAAK,IAAI,EACfD,OAAO,GAAK,OAAO,EACnBA,OAAO,CAACK,UAAU,CAAC,IAAI,CAAC,CAE1B,MAAO,CAACF,UAAU,CACpB,CAAC,CAAC,CAEFZ,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEvD,YAAY,CAAC0D,MAAM,CAAC,CAC7CJ,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEvD,YAAY,CAAC,CAEpC;AACA,GAAI,CAAAqE,SAAS,CAAGrE,YAAY,CAAC0D,MAAM,CAAG,CAAC,CAAG1D,YAAY,CAAGwD,cAAc,CAEvE;AACA,KAAM,CAAAc,UAAU,CAAG,EAAE,CACrB,KAAM,CAAAC,YAAY,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAE9BH,SAAS,CAACtC,OAAO,CAAC,CAAC+B,IAAI,CAAE7B,KAAK,GAAK,CACjC,KAAM,CAAA8B,OAAO,CAAGD,IAAI,CAAC,cAAc,CAAC,EAAI,EAAE,CAC1C,KAAM,CAAAW,UAAU,CAAGV,OAAO,CAACW,IAAI,CAAC,CAAC,CAEjC;AACA,GAAI,CAACD,UAAU,CAAE,CACfnB,OAAO,CAACqB,IAAI,UAAAC,MAAA,CAAK3C,KAAK,CAAG,CAAC,kFAAe,CAAC,CAC1C,OACF,CAEA,GAAIsC,YAAY,CAACM,GAAG,CAACJ,UAAU,CAAC,CAAE,CAChCnB,OAAO,CAACqB,IAAI,UAAAC,MAAA,CAAK3C,KAAK,CAAG,CAAC,qEAAA2C,MAAA,CAAgBH,UAAU,CAACK,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,iCAAU,CAAC,CAChF,OACF,CAEAP,YAAY,CAACQ,GAAG,CAACN,UAAU,CAAC,CAC5BH,UAAU,CAACU,IAAI,CAAClB,IAAI,CAAC,CACvB,CAAC,CAAC,CAEF/D,OAAO,CAACuE,UAAU,CAAC,CACnBhB,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEc,SAAS,CAACX,MAAM,CAAC,CACxCJ,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEe,UAAU,CAACZ,MAAM,CAAC,CAC1C,GAAIW,SAAS,CAACX,MAAM,GAAKY,UAAU,CAACZ,MAAM,CAAE,CAC1C,KAAM,CAAAuB,YAAY,CAAGZ,SAAS,CAACX,MAAM,CAAGY,UAAU,CAACZ,MAAM,CACzDJ,OAAO,CAACqB,IAAI,+CAAAC,MAAA,CAAYK,YAAY,mCAAQ,CAAC,CAC7CxE,cAAc,2DAAAmE,MAAA,CAAcK,YAAY,mCAAQ,CAAC,CACnD,CAAC,IAAM,CACLxE,cAAc,CAAC,IAAI,CAAC,CACtB,CAEA;AACA,KAAM,CAAAyE,KAAK,CAAG,CAAC,GAAG,GAAI,CAAAV,GAAG,CAACF,UAAU,CAACa,GAAG,CAACrB,IAAI,EAAIA,IAAI,CAACsB,EAAE,CAAC,CAACvB,MAAM,CAACwB,IAAI,EAAIA,IAAI,EAAIA,IAAI,CAACX,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAAC,CAAC,CAACY,IAAI,CAAC,CAAC,CAC7G/D,iBAAiB,CAAC2D,KAAK,CAAC,CACxB5B,OAAO,CAACC,GAAG,CAAC,OAAO,CAAE2B,KAAK,CAAC,CAE3B;AACAK,qBAAqB,CAACjB,UAAU,CAAC,CACnC,CAAC,IAAM,CACLhB,OAAO,CAACR,KAAK,CAAC,UAAU,CAAC,CACzB/C,OAAO,CAAC,EAAE,CAAC,CACb,CACF,CAAE,MAAO+C,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B/C,OAAO,CAAC,EAAE,CAAC,CACb,CACAI,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAoF,qBAAqB,CAAIC,WAAW,EAAK,CAC7C,GAAI,CAACA,WAAW,EAAI,CAAC7B,KAAK,CAACC,OAAO,CAAC4B,WAAW,CAAC,CAAE,CAC/ClC,OAAO,CAACR,KAAK,CAAC,eAAe,CAAC,CAC9B,MAAO,MAAK,CACd,CAEA,KAAM,CAAA2C,MAAM,CAAG,EAAE,CACjB,KAAM,CAAAlB,YAAY,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC9B,KAAM,CAAAkB,WAAW,CAAG,GAAI,CAAAlB,GAAG,CAAC,CAAC,CAE7BgB,WAAW,CAACzD,OAAO,CAAC,CAAC+B,IAAI,CAAE7B,KAAK,GAAK,CACnC,KAAM,CAAA0D,MAAM,CAAG1D,KAAK,CAAG,CAAC,CAExB;AACA,GAAI,CAAC6B,IAAI,CAAC,cAAc,CAAC,EAAI,CAACA,IAAI,CAAC,cAAc,CAAC,CAACY,IAAI,CAAC,CAAC,CAAE,CACzDe,MAAM,CAACT,IAAI,UAAAJ,MAAA,CAAKe,MAAM,oDAAU,CAAC,CACnC,CAEA;AACA,KAAM,CAAA5B,OAAO,CAAG,CAACD,IAAI,CAAC,cAAc,CAAC,EAAI,EAAE,EAAEY,IAAI,CAAC,CAAC,CACnD,GAAIX,OAAO,EAAIQ,YAAY,CAACM,GAAG,CAACd,OAAO,CAAC,CAAE,CACxC0B,MAAM,CAACT,IAAI,UAAAJ,MAAA,CAAKe,MAAM,wDAAAf,MAAA,CAAab,OAAO,CAACe,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,SAAM,CAAC,CACpE,CAAC,IAAM,IAAIf,OAAO,CAAE,CAClBQ,YAAY,CAACQ,GAAG,CAAChB,OAAO,CAAC,CAC3B,CAEA;AACA,KAAM,CAAA6B,MAAM,CAAG9B,IAAI,CAAC+B,EAAE,CACtB,GAAID,MAAM,GAAK,IAAI,EAAIA,MAAM,GAAKE,SAAS,EAAIF,MAAM,GAAK,EAAE,CAAE,CAC5D,GAAIF,WAAW,CAACb,GAAG,CAACe,MAAM,CAAC,CAAE,CAC3BH,MAAM,CAACT,IAAI,UAAAJ,MAAA,CAAKe,MAAM,4CAAAf,MAAA,CAAWgB,MAAM,MAAG,CAAC,CAC7C,CAAC,IAAM,CACLF,WAAW,CAACX,GAAG,CAACa,MAAM,CAAC,CACzB,CACF,CACF,CAAC,CAAC,CAEF,GAAIH,MAAM,CAAC/B,MAAM,CAAG,CAAC,CAAE,CACrBJ,OAAO,CAACqB,IAAI,CAAC,iBAAiB,CAAC,CAC/Bc,MAAM,CAAC1D,OAAO,CAACgE,KAAK,EAAIzC,OAAO,CAACqB,IAAI,SAAAC,MAAA,CAASmB,KAAK,CAAE,CAAC,CAAC,CACtD,MAAO,MAAK,CACd,CAAC,IAAM,CACLzC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B,MAAO,KAAI,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAyC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B1C,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CACxBhD,aAAa,CAAC,QAAQ,CAAC,CACvB,KAAM,CAAAqC,QAAQ,CAAC,CAAC,CAChBrC,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAA0F,wBAAwB,CAAGA,CAAA,GAAM,CACrC;AACA,KAAM,CAAAC,UAAU,CAAGlG,YAAY,CAAC0D,MAAM,CAAG,CAAC,CAAG1D,YAAY,CAAGF,IAAI,CAChE,GAAI,CAACoG,UAAU,EAAIA,UAAU,CAACxC,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAErD,KAAM,CAAAyC,kBAAkB,CAAGD,UAAU,CAClCf,GAAG,CAACrB,IAAI,EAAIA,IAAI,CAACG,GAAG,CAAC,CACrBJ,MAAM,CAACG,MAAM,EAAIA,MAAM,EAAIoC,MAAM,CAACpC,MAAM,CAAC,CAACU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CACxD2B,MAAM,CAAC,CAACC,GAAG,CAAEtC,MAAM,GAAK,CACvB;AACA,KAAM,CAAAuC,OAAO,CAAGH,MAAM,CAACpC,MAAM,CAAC,CAACwC,KAAK,CAAC,aAAa,CAAC,CAACrB,GAAG,CAACsB,CAAC,EAAIA,CAAC,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAAC4C,CAAC,EAAIA,CAAC,CAAC,CACrFF,OAAO,CAACxE,OAAO,CAAC0E,CAAC,EAAI,CACnB,GAAI,CAACH,GAAG,CAACnC,QAAQ,CAACsC,CAAC,CAAC,CAAE,CACpBH,GAAG,CAACtB,IAAI,CAACyB,CAAC,CAAC,CACb,CACF,CAAC,CAAC,CACF,MAAO,CAAAH,GAAG,CACZ,CAAC,CAAE,EAAE,CAAC,CAER,MAAO,CAAAH,kBAAkB,CAACb,IAAI,CAAC,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAjC,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAAqD,QAAQ,CAAG,CAAC,GAAG5G,IAAI,CAAC,CAExB;AACA,GAAIsB,UAAU,GAAK,IAAI,CAAE,CACvBsF,QAAQ,CAAGA,QAAQ,CAAC7C,MAAM,CAACC,IAAI,EAAIA,IAAI,CAACsB,EAAE,GAAKhE,UAAU,CAAC,CAC5D,CAEA;AACA,GAAIN,0BAA0B,CAAC4C,MAAM,CAAG,CAAC,CAAE,CACzCgD,QAAQ,CAAGA,QAAQ,CAAC7C,MAAM,CAACC,IAAI,EAAI,CACjC,GAAI,CAACA,IAAI,CAACG,GAAG,CAAE,MAAO,MAAK,CAE3B;AACA,KAAM,CAAA0C,WAAW,CAAGP,MAAM,CAACtC,IAAI,CAACG,GAAG,CAAC,CAACuC,KAAK,CAAC,aAAa,CAAC,CAACrB,GAAG,CAACsB,CAAC,EAAIA,CAAC,CAAC/B,IAAI,CAAC,CAAC,CAAC,CAACb,MAAM,CAAC4C,CAAC,EAAIA,CAAC,CAAC,CAC3F,MAAO,CAAAE,WAAW,CAACC,IAAI,CAAC5C,MAAM,EAAIlD,0BAA0B,CAACqD,QAAQ,CAACH,MAAM,CAAC,CAAC,CAChF,CAAC,CAAC,CACJ,CAEA/D,eAAe,CAACyG,QAAQ,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAG,6BAA6B,CAAI7C,MAAM,EAAK,CAChD,KAAM,CAAA8C,eAAe,CAAG9F,mBAAmB,CAACmD,QAAQ,CAACH,MAAM,CAAC,CACxDhD,mBAAmB,CAAC6C,MAAM,CAAC4C,CAAC,EAAIA,CAAC,GAAKzC,MAAM,CAAC,CAC7C,CAAC,GAAGhD,mBAAmB,CAAEgD,MAAM,CAAC,CACpC/C,sBAAsB,CAAC6F,eAAe,CAAC,CACzC,CAAC,CAED,KAAM,CAAAC,4BAA4B,CAAGA,CAAA,GAAM,CACzChG,6BAA6B,CAAC,CAAC,GAAGC,mBAAmB,CAAC,CAAC,CACvDG,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAC,CAED,KAAM,CAAA6F,4BAA4B,CAAGA,CAAA,GAAM,CACzC/F,sBAAsB,CAAC,EAAE,CAAC,CAC1BF,6BAA6B,CAAC,EAAE,CAAC,CACjCI,wBAAwB,CAAC,KAAK,CAAC,CACjC,CAAC,CAED;AACA,KAAM,CAAA8F,cAAc,CAAGA,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACjD,KAAM,CAAAC,OAAO,IAAAzC,MAAA,CAAMsC,QAAQ,MAAAtC,MAAA,CAAIuC,KAAK,CAAE,CAEtC;AACAxG,aAAa,CAAC2G,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACbD,IAAI,MACP,CAACD,OAAO,EAAGD,KAAK,EAChB,CAAC,CAEH;AACA,GAAIxG,cAAc,CAACmC,OAAO,CAAE,CAC1BC,YAAY,CAACpC,cAAc,CAACmC,OAAO,CAAC,CACtC,CAEA;AACAnC,cAAc,CAACmC,OAAO,CAAGyE,UAAU,CAAC,IAAM,CACxCC,QAAQ,CAACP,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAClC,CAAC,CAAE,IAAI,CAAC,CAAE;AACZ,CAAC,CAED,KAAM,CAAAK,QAAQ,CAAG,KAAAA,CAAOP,QAAQ,CAAEC,KAAK,CAAEC,KAAK,GAAK,CACjD,GAAI,CACF7G,aAAa,CAAC,QAAQ,CAAC,CAEvB;AACA,GAAIM,cAAc,CAACkC,OAAO,CAAE,KAAA2E,sBAAA,CAAAC,sBAAA,CAC1B,CAAAD,sBAAA,EAAAC,sBAAA,CAAA9G,cAAc,CAACkC,OAAO,EAACI,KAAK,UAAAuE,sBAAA,iBAA5BA,sBAAA,CAAAtE,IAAA,CAAAuE,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CAC7ChH,cAAc,CAACkC,OAAO,CAAG6E,eAAe,CAExC,KAAM,CAAAE,OAAO,CAAG,KAAM,CAAA5I,iBAAiB,CAAC6I,UAAU,CAACb,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE1E,GAAIU,OAAO,EAAI,CAACF,eAAe,CAACI,MAAM,CAACC,OAAO,CAAE,CAC9C;AACA,KAAM,CAAAC,OAAO,CAAG,CAAC,GAAGpI,IAAI,CAAC,CACzB,GAAIoI,OAAO,CAAChB,QAAQ,CAAC,CAAE,CACrBgB,OAAO,CAAChB,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CAChCrH,OAAO,CAACmI,OAAO,CAAC,CAClB,CAEA;AACA,KAAM,CAAAb,OAAO,IAAAzC,MAAA,CAAMsC,QAAQ,MAAAtC,MAAA,CAAIuC,KAAK,CAAE,CACtCxG,aAAa,CAAC2G,IAAI,EAAI,CACpB,KAAM,CAAAa,aAAa,CAAAZ,aAAA,IAAQD,IAAI,CAAE,CACjC,MAAO,CAAAa,aAAa,CAACd,OAAO,CAAC,CAC7B,MAAO,CAAAc,aAAa,CACtB,CAAC,CAAC,CAEF5H,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAE,MAAOuC,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BvC,aAAa,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAA6H,iBAAiB,CAAIC,SAAS,EAAK,CACvC,GAAIA,SAAS,GAAK,MAAM,EAAI7G,gBAAgB,CAAG,CAAC,CAAE,CAChDC,mBAAmB,CAACD,gBAAgB,CAAG,CAAC,CAAC,CAC3C,CAAC,IAAM,IAAI6G,SAAS,GAAK,MAAM,EAAI7G,gBAAgB,CAAGe,UAAU,CAACmB,MAAM,CAAG,CAAC,CAAE,CAC3EjC,mBAAmB,CAACD,gBAAgB,CAAG,CAAC,CAAC,CAC3C,CACF,CAAC,CAED;AACA,KAAM,CAAA8G,cAAc,CAAG,KAAO,CAAAC,aAAa,EAAK,CAC9C5F,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACF,KAAM,CAAA6F,MAAM,CAAG,KAAM,CAAArJ,yBAAyB,CAACsJ,uBAAuB,CAACF,aAAa,CAAC,CACrFjF,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEiF,MAAM,CAAC,CAC5B/F,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAE,MAAOK,KAAK,CAAE,CACdQ,OAAO,CAACR,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B4F,KAAK,CAAC,QAAQ,CAAG5F,KAAK,CAAC6F,OAAO,CAAC,CACjC,CAAC,OAAS,CACRhG,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CACF,CAAC,CAED;AACA,KAAM,CAAAiG,kBAAkB,CAAG,QAAAA,CAACxB,KAAK,CAAEF,QAAQ,CAAEC,KAAK,CAA2B,KAAA0B,KAAA,CAAAC,mBAAA,IAAzB,CAAAC,QAAQ,CAAAC,SAAA,CAAAtF,MAAA,IAAAsF,SAAA,MAAAlD,SAAA,CAAAkD,SAAA,IAAG,SAAS,CACtE,KAAM,CAAA3B,OAAO,IAAAzC,MAAA,CAAMsC,QAAQ,MAAAtC,MAAA,CAAIuC,KAAK,CAAE,CACtC,KAAM,CAAA8B,SAAS,CAAG7I,WAAW,GAAKiH,OAAO,CACzC;AACA,KAAM,CAAA6B,QAAQ,EAAAL,KAAA,EAAAC,mBAAA,CAAGpI,UAAU,CAAC2G,OAAO,CAAC,UAAAyB,mBAAA,UAAAA,mBAAA,CAAI1B,KAAK,UAAAyB,KAAA,UAAAA,KAAA,CAAI,EAAE,CACnD,GAAI,CAAAM,YAAY,CAAG,EAAE,CAErB;AACA,GAAID,QAAQ,GAAK,IAAI,EAAIA,QAAQ,GAAKpD,SAAS,CAAE,CAC/C,GAAI,MAAO,CAAAoD,QAAQ,GAAK,QAAQ,CAAE,CAChCC,YAAY,CAAGD,QAAQ,CACzB,CAAC,IAAM,IAAI,MAAO,CAAAA,QAAQ,GAAK,QAAQ,CAAE,CACvCC,YAAY,CAAG/C,MAAM,CAAC8C,QAAQ,CAAC,CACjC,CAAC,IAAM,IAAI,MAAO,CAAAA,QAAQ,GAAK,QAAQ,CAAE,CACvC;AACAC,YAAY,CAAG,EAAE,CACnB,CAAC,IAAM,CACLA,YAAY,CAAG/C,MAAM,CAAC8C,QAAQ,CAAC,CACjC,CACF,CACA;AACA;AACA;AACA,KAAM,CAAAE,eAAe,CAAGjC,KAAK,GAAK,IAAI,EACfA,KAAK,CAAChD,QAAQ,CAAC,MAAM,CAAC,EACtBgD,KAAK,CAAChD,QAAQ,CAAC,MAAM,CAAC,CAC7C,KAAM,CAAAkF,UAAU,CAAG,CAACD,eAAe,CAEnC,KAAM,CAAAE,WAAW,CAAG,CAClB,eAAe,CACfP,QAAQ,CACRE,SAAS,CAAG,SAAS,CAAG,EAAE,CAC1BG,eAAe,CAAG,UAAU,CAAG,EAAE,CACjCD,YAAY,CAAG,aAAa,CAAG,OAAO,CACvC,CAACtF,MAAM,CAAC0F,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAE3B;AACA,GAAI,CAAAC,SAAS,CAAG,MAAM,CACtB,GAAItC,KAAK,GAAK,IAAI,CAAE,CAClBsC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAItC,KAAK,CAAChD,QAAQ,CAAC,MAAM,CAAC,EAAIgD,KAAK,CAAChD,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC3DsF,SAAS,CAAG,eAAe,CAC7B,CAEA,GAAIR,SAAS,EAAII,UAAU,CAAE,CAC3B,mBACE/J,IAAA,aACEoK,SAAS,CAAC,aAAa,CACvBtC,KAAK,CAAE+B,YAAa,CACpBQ,QAAQ,CAAGC,CAAC,EAAK,CACf,KAAM,CAAAC,QAAQ,CAAGD,CAAC,CAACE,MAAM,CAAC1C,KAAK,CAC/BzG,aAAa,CAAC2G,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACbD,IAAI,MACP,CAACD,OAAO,EAAGwC,QAAQ,EACnB,CAAC,CACL,CAAE,CACFE,MAAM,CAAEA,CAAA,GAAM,CACZ1J,cAAc,CAAC,IAAI,CAAC,CACpB4G,cAAc,CAACC,QAAQ,CAAEC,KAAK,CAAEgC,YAAY,CAAC,CAC/C,CAAE,CACFa,SAAS,CAAGJ,CAAC,EAAK,CAChB,GAAIA,CAAC,CAACK,GAAG,GAAK,OAAO,EAAI,CAACL,CAAC,CAACM,QAAQ,CAAE,CACpCN,CAAC,CAACO,cAAc,CAAC,CAAC,CAClBP,CAAC,CAACE,MAAM,CAACM,IAAI,CAAC,CAAC,CACjB,CACA,GAAIR,CAAC,CAACK,GAAG,GAAK,QAAQ,CAAE,CACtBtJ,aAAa,CAAC2G,IAAI,EAAI,CACpB,KAAM,CAAAa,aAAa,CAAAZ,aAAA,IAAQD,IAAI,CAAE,CACjC,MAAO,CAAAa,aAAa,CAACd,OAAO,CAAC,CAC7B,MAAO,CAAAc,aAAa,CACtB,CAAC,CAAC,CACF9H,cAAc,CAAC,IAAI,CAAC,CACtB,CACF,CAAE,CACFgK,SAAS,MACTC,IAAI,CAAE,CAAE,CACT,CAAC,CAEN,CAEA,mBACEhL,IAAA,QACEoK,SAAS,CAAEJ,WAAY,CACvBiB,OAAO,CAAEA,CAAA,GAAMlB,UAAU,EAAIhJ,cAAc,CAACgH,OAAO,CAAE,CACrDmD,KAAK,CAAEf,SAAU,CACjBgB,KAAK,CAAErB,eAAe,CAAG,CAAEsB,MAAM,CAAE,aAAc,CAAC,CAAG,CAAC,CAAE,CAAAC,QAAA,CAEvDxB,YAAY,CAAChF,QAAQ,CAAC,IAAI,CAAC,CAC1BgF,YAAY,CAAC3C,KAAK,CAAC,IAAI,CAAC,CAACrB,GAAG,CAAC,CAACyF,IAAI,CAAEC,CAAC,CAAEC,GAAG,gBACxCtL,KAAA,CAACX,KAAK,CAACY,QAAQ,EAAAkL,QAAA,EACZC,IAAI,CACJC,CAAC,CAAGC,GAAG,CAACpH,MAAM,CAAG,CAAC,eAAIpE,IAAA,QAAK,CAAC,GAFVuL,CAGL,CACjB,CAAC,CACF1B,YAAY,CAEX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA4B,kBAAkB,CAAGA,CAACC,GAAG,CAAE9D,QAAQ,GAAK,CAC5C,KAAM,CAAC+D,MAAM,CAAEC,MAAM,CAAC,CAAG3I,UAAU,CAACf,gBAAgB,CAAC,CAErD,mBACEhC,KAAA,CAAAE,SAAA,EAAAiL,QAAA,eACErL,IAAA,OAAIoK,SAAS,CAAC,0BAA0B,CAAAiB,QAAA,CACrC/B,kBAAkB,CAACoC,GAAG,IAAApG,MAAA,CAAIqG,MAAM,6BAAO,CAAE/D,QAAQ,IAAAtC,MAAA,CAAKqG,MAAM,6BAAQ,YAAY,CAAC,CAChF,CAAC,cACL3L,IAAA,OAAIoK,SAAS,CAAC,8BAA8B,CAAAiB,QAAA,CACzC/B,kBAAkB,CAACoC,GAAG,IAAApG,MAAA,CAAIqG,MAAM,6BAAO,CAAE/D,QAAQ,IAAAtC,MAAA,CAAKqG,MAAM,6BAAQ,gBAAgB,CAAC,CACpF,CAAC,cACL3L,IAAA,OAAIoK,SAAS,CAAC,0BAA0B,CAAAiB,QAAA,CACrC/B,kBAAkB,CAACoC,GAAG,IAAApG,MAAA,CAAIsG,MAAM,6BAAO,CAAEhE,QAAQ,IAAAtC,MAAA,CAAKsG,MAAM,6BAAQ,YAAY,CAAC,CAChF,CAAC,cACL5L,IAAA,OAAIoK,SAAS,CAAC,8BAA8B,CAAAiB,QAAA,CACzC/B,kBAAkB,CAACoC,GAAG,IAAApG,MAAA,CAAIsG,MAAM,6BAAO,CAAEhE,QAAQ,IAAAtC,MAAA,CAAKsG,MAAM,6BAAQ,gBAAgB,CAAC,CACpF,CAAC,EACL,CAAC,CAEP,CAAC,CAED;AACA,KAAM,CAAAC,WAAW,CAAGnL,YAAY,CAAC0D,MAAM,CAAG,CAAC,CAAG1D,YAAY,CAAGF,IAAI,CACjE,KAAM,CAACmL,MAAM,CAAEC,MAAM,CAAC,CAAG3I,UAAU,CAACf,gBAAgB,CAAC,CAErD,GAAItB,OAAO,CAAE,CACX,mBACEZ,IAAA,QAAKoK,SAAS,CAAC,uBAAuB,CAAAiB,QAAA,cACpCnL,KAAA,QAAKkK,SAAS,CAAC,qBAAqB,CAAAiB,QAAA,eAClCrL,IAAA,QAAKoK,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvCpK,IAAA,MAAAqL,QAAA,CAAG,8EAAgB,CAAG,CAAC,EACpB,CAAC,CACH,CAAC,CAEV,CAEA;AACA,KAAM,CAAAS,4BAA4B,CAAGA,CAAA,GAAM,CACzC,GAAI,CAAClK,qBAAqB,CAAE,MAAO,KAAI,CACvC,KAAM,CAAAmK,UAAU,CAAGpF,wBAAwB,CAAC,CAAC,CAE7C,mBACEzG,KAAA,QAAKkK,SAAS,CAAC,sCAAsC,CAAAiB,QAAA,eACnDnL,KAAA,QAAKkK,SAAS,CAAC,iCAAiC,CAAAiB,QAAA,eAC9CrL,IAAA,OAAAqL,QAAA,CAAI,gCAAK,CAAI,CAAC,cACdrL,IAAA,WAAQiL,OAAO,CAAEA,CAAA,GAAMpJ,wBAAwB,CAAC,KAAK,CAAE,CAACuI,SAAS,CAAC,6BAA6B,CAAAiB,QAAA,CAAC,MAAC,CAAQ,CAAC,EACvG,CAAC,cACNnL,KAAA,QAAKkK,SAAS,CAAC,kCAAkC,CAAAiB,QAAA,eAC/CrL,IAAA,QAAKoK,SAAS,CAAC,4BAA4B,CAAAiB,QAAA,CACxCU,UAAU,CAAClG,GAAG,CAACnB,MAAM,eACpB1E,IAAA,QAEEoK,SAAS,8BAAA9E,MAAA,CAA+B5D,mBAAmB,CAACmD,QAAQ,CAACH,MAAM,CAAC,CAAG,UAAU,CAAG,EAAE,CAAG,CACjGuG,OAAO,CAAEA,CAAA,GAAM1D,6BAA6B,CAAC7C,MAAM,CAAE,CAAA2G,QAAA,CAEpD3G,MAAM,EAJFA,MAKF,CACN,CAAC,CACC,CAAC,cACNxE,KAAA,QAAKkK,SAAS,CAAC,4BAA4B,CAAAiB,QAAA,eACzCrL,IAAA,WAAQiL,OAAO,CAAEvD,4BAA6B,CAAC0C,SAAS,CAAC,8BAA8B,CAAAiB,QAAA,CAAC,0BAExF,CAAQ,CAAC,cACTrL,IAAA,WAAQiL,OAAO,CAAExD,4BAA6B,CAAC2C,SAAS,CAAC,8BAA8B,CAAAiB,QAAA,CAAC,0BAExF,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,mBACEnL,KAAA,QAAKkK,SAAS,CAAC,uBAAuB,CAAAiB,QAAA,EAEnCS,4BAA4B,CAAC,CAAC,CAG9B5K,WAAW,eACVlB,IAAA,QACEmL,KAAK,CAAE,CACLa,UAAU,CAAE,yEAAyE,CACrFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,WAAW,CACnBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,cAAc,CAAE,YAAY,CAC5BC,SAAS,CAAE,mCACb,CAAE,CAAAlB,QAAA,cAEFnL,KAAA,QACEiL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,WAAW,CACpBC,GAAG,CAAE,MACP,CAAE,CAAAtB,QAAA,eAEFrL,IAAA,QACEmL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,cAAc,CAAE,QAAQ,CACxBR,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,MAAM,CACdb,UAAU,CAAE,0CAA0C,CACtDE,YAAY,CAAE,KAAK,CACnBY,QAAQ,CAAE,MACZ,CAAE,CAAAzB,QAAA,CACH,cAED,CAAK,CAAC,cACNnL,KAAA,QAAKiL,KAAK,CAAE,CAAE4B,IAAI,CAAE,CAAE,CAAE,CAAA1B,QAAA,eACtBrL,IAAA,QACEmL,KAAK,CAAE,CACL2B,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,0BAA0B,CACjCC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KACd,CAAE,CAAA7B,QAAA,CACH,sCAED,CAAK,CAAC,cACNrL,IAAA,QACEmL,KAAK,CAAE,CACL2B,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,KACd,CAAE,CAAA7B,QAAA,CAEDnK,WAAW,CACT,CAAC,EACH,CAAC,cACNlB,IAAA,WACEmL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,KAAK,CACVD,OAAO,CAAE,UAAU,CACnBV,UAAU,CAAE,4EAA4E,CACxFC,MAAM,CAAE,oCAAoC,CAC5CC,YAAY,CAAE,KAAK,CACnBc,KAAK,CAAE,MAAM,CACbF,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,KAAK,CACjB9B,MAAM,CAAE,SAAS,CACjB+B,UAAU,CAAE,eACd,CAAE,CACFlC,OAAO,CAAEA,CAAA,GAAM9J,cAAc,CAAC,IAAI,CAAE,CACpC+J,KAAK,CAAC,0BAAM,CAAAG,QAAA,cAEZrL,IAAA,SAAAqL,QAAA,CAAM,QAAC,CAAM,CAAC,CACR,CAAC,EACN,CAAC,CACH,CACN,CAGA7J,0BAA0B,CAAC4C,MAAM,CAAG,CAAC,eACpCpE,IAAA,QACEmL,KAAK,CAAE,CACLa,UAAU,CAAE,yEAAyE,CACrFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,WAAW,CACnBC,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,MAAM,CAChBC,cAAc,CAAE,YAAY,CAC5BC,SAAS,CAAE,mCACb,CAAE,CAAAlB,QAAA,cAEFnL,KAAA,QACEiL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,OAAO,CAAE,WAAW,CACpBC,GAAG,CAAE,MACP,CAAE,CAAAtB,QAAA,eAEFrL,IAAA,QACEmL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBG,cAAc,CAAE,QAAQ,CACxBR,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,MAAM,CACdb,UAAU,CAAE,0CAA0C,CACtDE,YAAY,CAAE,KAAK,CACnBY,QAAQ,CAAE,MACZ,CAAE,CAAAzB,QAAA,CACH,cAED,CAAK,CAAC,cACNnL,KAAA,QAAKiL,KAAK,CAAE,CAAE4B,IAAI,CAAE,CAAE,CAAE,CAAA1B,QAAA,eACtBrL,IAAA,QACEmL,KAAK,CAAE,CACL2B,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,0BAA0B,CACjCC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,KACd,CAAE,CAAA7B,QAAA,CACH,sCAED,CAAK,CAAC,cACNnL,KAAA,QACEiL,KAAK,CAAE,CACL2B,QAAQ,CAAE,MAAM,CAChBE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,KACd,CAAE,CAAA7B,QAAA,EACH,0BAEC,cAAArL,IAAA,SAAMmL,KAAK,CAAE,CACX6B,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,MAAM,CAClBE,UAAU,CAAE,iCAAiC,CAC7CjB,MAAM,CAAE,OACV,CAAE,CAAAd,QAAA,CACC7J,0BAA0B,CAAC0I,IAAI,CAAC,GAAG,CAAC,CACjC,CAAC,cACPhK,KAAA,SAAMiL,KAAK,CAAE,CACX6B,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,MACZ,CAAE,CAAAzB,QAAA,EAAC,QACA,CAACQ,WAAW,CAACzH,MAAM,CAAC,0BACvB,EAAM,CAAC,EACJ,CAAC,EACH,CAAC,cACNlE,KAAA,WACEiL,KAAK,CAAE,CACLqB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBE,GAAG,CAAE,KAAK,CACVD,OAAO,CAAE,UAAU,CACnBV,UAAU,CAAE,0EAA0E,CACtFC,MAAM,CAAE,kCAAkC,CAC1CC,YAAY,CAAE,KAAK,CACnBc,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,MAAM,CAChBI,UAAU,CAAE,KAAK,CACjB9B,MAAM,CAAE,SAAS,CACjB+B,UAAU,CAAE,eACd,CAAE,CACFlC,OAAO,CAAEA,CAAA,GAAM,CACbxJ,6BAA6B,CAAC,EAAE,CAAC,CACjCE,sBAAsB,CAAC,EAAE,CAAC,CAC5B,CAAE,CACF0L,YAAY,CAAG/C,CAAC,EAAK,CACnBA,CAAC,CAACE,MAAM,CAACW,KAAK,CAACa,UAAU,CAAG,0EAA0E,CACtG1B,CAAC,CAACE,MAAM,CAACW,KAAK,CAACmC,SAAS,CAAG,kBAAkB,CAC/C,CAAE,CACFC,YAAY,CAAGjD,CAAC,EAAK,CACnBA,CAAC,CAACE,MAAM,CAACW,KAAK,CAACa,UAAU,CAAG,0EAA0E,CACtG1B,CAAC,CAACE,MAAM,CAACW,KAAK,CAACmC,SAAS,CAAG,eAAe,CAC5C,CAAE,CACFpC,KAAK,CAAC,sCAAQ,CAAAG,QAAA,eAEdrL,IAAA,SAAAqL,QAAA,CAAM,QAAC,CAAM,CAAC,cACdrL,IAAA,SAAAqL,QAAA,CAAM,0BAAI,CAAM,CAAC,EACX,CAAC,EACN,CAAC,CACH,CACN,cAGDnL,KAAA,QAAKkK,SAAS,CAAC,aAAa,CAAAiB,QAAA,eAC1BrL,IAAA,WACEoK,SAAS,CAAC,cAAc,CACxBa,OAAO,CAAEA,CAAA,GAAM1K,UAAU,CAAC,MAAM,CAAE,CAAA8K,QAAA,CACnC,0BAED,CAAQ,CAAC,cAETnL,KAAA,QAAKkK,SAAS,CAAC,eAAe,CAAAiB,QAAA,eAC5BrL,IAAA,OAAIoK,SAAS,CAAC,YAAY,CAAAiB,QAAA,CAAC,uCAAO,CAAI,CAAC,cACvCrL,IAAA,MAAGoK,SAAS,CAAC,eAAe,CAAAiB,QAAA,CAAC,8FAAgB,CAAG,CAAC,EAC9C,CAAC,cAENnL,KAAA,QAAKkK,SAAS,CAAC,gBAAgB,CAAAiB,QAAA,eAC7BrL,IAAA,WAAQoK,SAAS,CAAC,iBAAiB,CAACa,OAAO,CAAEvE,YAAa,CAAA2E,QAAA,CAAC,uCAE3D,CAAQ,CAAC,cACTrL,IAAA,QAAKoK,SAAS,CAAC,aAAa,CAAAiB,QAAA,cAC1BrL,IAAA,SACEoK,SAAS,qBAAA9E,MAAA,CAAsBtE,UAAU,CAAC6D,QAAQ,CAAC,IAAI,CAAC,EAAI7D,UAAU,GAAK,KAAK,CAAG,SAAS,CAAGA,UAAU,CAAC6D,QAAQ,CAAC,IAAI,CAAC,CAAG,OAAO,CAAG,SAAS,CAAG,CAAAwG,QAAA,CAEhJrK,UAAU,CACP,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,cAGNd,KAAA,QAAKkK,SAAS,CAAC,eAAe,CAAAiB,QAAA,eAC5BrL,IAAA,QAAKoK,SAAS,CAAC,eAAe,CAAAiB,QAAA,cAC5BnL,KAAA,QAAKkK,SAAS,CAAC,iBAAiB,CAAAiB,QAAA,eAC9BnL,KAAA,QAAKkK,SAAS,CAAC,cAAc,CAAAiB,QAAA,eAC3BrL,IAAA,UAAAqL,QAAA,CAAO,2BAAK,CAAO,CAAC,cACpBnL,KAAA,WACEkK,SAAS,CAAC,eAAe,CACzBtC,KAAK,CAAEhG,UAAW,CAClBuI,QAAQ,CAAGC,CAAC,EAAKvI,aAAa,CAACuI,CAAC,CAACE,MAAM,CAAC1C,KAAK,CAAE,CAAAuD,QAAA,eAE/CrL,IAAA,WAAQ8H,KAAK,CAAC,cAAI,CAAAuD,QAAA,CAAC,0BAAI,CAAQ,CAAC,CAC/BrJ,cAAc,CAAC6D,GAAG,CAACE,IAAI,eACtB/F,IAAA,WAAmB8H,KAAK,CAAE/B,IAAK,CAAAsF,QAAA,CAAEtF,IAAI,EAAxBA,IAAiC,CAC/C,CAAC,EACI,CAAC,EACN,CAAC,cAEN7F,KAAA,QAAKkK,SAAS,CAAC,kBAAkB,CAAAiB,QAAA,eAC/BrL,IAAA,SAAMoK,SAAS,CAAC,YAAY,CAAAiB,QAAA,CAAC,2BAAK,CAAM,CAAC,cACzCnL,KAAA,SAAMkK,SAAS,CAAC,YAAY,CAAAiB,QAAA,EAAEQ,WAAW,CAACzH,MAAM,CAAC,SAAE,EAAM,CAAC,EACvD,CAAC,cAENlE,KAAA,QACEkK,SAAS,CAAC,4BAA4B,CACtCa,OAAO,CAAEA,CAAA,GAAM,CACbtJ,sBAAsB,CAAC,CAAC,GAAGH,0BAA0B,CAAC,CAAC,CACvDK,wBAAwB,CAAC,CAACD,qBAAqB,CAAC,CAClD,CAAE,CAAAyJ,QAAA,eAEFrL,IAAA,SAAMoK,SAAS,CAAC,aAAa,CAAAiB,QAAA,CAAC,cAAE,CAAM,CAAC,CACtC7J,0BAA0B,CAAC4C,MAAM,CAAG,CAAC,eACpCpE,IAAA,QAAKoK,SAAS,CAAC,cAAc,CAAAiB,QAAA,CAC1B7J,0BAA0B,CAAC4C,MAAM,CAC/B,CACN,EACE,CAAC,EACH,CAAC,CACH,CAAC,cAENlE,KAAA,QAAKkK,SAAS,CAAC,gBAAgB,CAAAiB,QAAA,eAC7BnL,KAAA,QAAKkK,SAAS,CAAC,sBAAsB,CAAAiB,QAAA,eACnCrL,IAAA,WACEoK,SAAS,CAAC,aAAa,CACvBa,OAAO,CAAEA,CAAA,GAAMnC,iBAAiB,CAAC,MAAM,CAAE,CACzC0E,QAAQ,CAAEtL,gBAAgB,GAAK,CAAE,CAAAmJ,QAAA,CAClC,sCAED,CAAQ,CAAC,cACTnL,KAAA,SAAMkK,SAAS,CAAC,oBAAoB,CAAAiB,QAAA,EACjCM,MAAM,CAAC,KAAG,CAACC,MAAM,EACd,CAAC,cACP5L,IAAA,WACEoK,SAAS,CAAC,aAAa,CACvBa,OAAO,CAAEA,CAAA,GAAMnC,iBAAiB,CAAC,MAAM,CAAE,CACzC0E,QAAQ,CAAEtL,gBAAgB,GAAKe,UAAU,CAACmB,MAAM,CAAG,CAAE,CAAAiH,QAAA,CACtD,sCAED,CAAQ,CAAC,EACN,CAAC,cACNrL,IAAA,WACEoK,SAAS,CAAC,cAAc,CACxBa,OAAO,CAAEA,CAAA,GAAM9H,oBAAoB,CAAC,IAAI,CAAE,CAAAkI,QAAA,CAC3C,6CAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAGNrL,IAAA,QAAKoK,SAAS,CAAC,iBAAiB,CAAAiB,QAAA,cAC9BnL,KAAA,UAAOkK,SAAS,CAAC,mBAAmB,CAAAiB,QAAA,eAClCrL,IAAA,UAAAqL,QAAA,cACEnL,KAAA,OAAAmL,QAAA,eACErL,IAAA,OAAIoK,SAAS,CAAC,YAAY,CAAAiB,QAAA,CAAC,cAAE,CAAI,CAAC,cAClCrL,IAAA,OAAIoK,SAAS,CAAC,aAAa,CAAAiB,QAAA,CAAC,qEAAY,CAAI,CAAC,cAC7CrL,IAAA,OAAIoK,SAAS,CAAC,UAAU,CAAAiB,QAAA,CAAC,cAAE,CAAI,CAAC,cAChCrL,IAAA,OAAIoK,SAAS,CAAC,YAAY,CAAAiB,QAAA,CAAC,wBAAO,CAAI,CAAC,cACvCrL,IAAA,OAAIoK,SAAS,CAAC,cAAc,CAAAiB,QAAA,CAAC,0BAAI,CAAI,CAAC,cACtCrL,IAAA,OAAIoK,SAAS,CAAC,UAAU,CAAAiB,QAAA,CAAC,0BAAI,CAAI,CAAC,cAClCrL,IAAA,OAAIoK,SAAS,CAAC,iBAAiB,CAAAiB,QAAA,CAAC,oBAAG,CAAI,CAAC,cACxCnL,KAAA,OAAIkK,SAAS,CAAC,gBAAgB,CAAAiB,QAAA,EAAEM,MAAM,CAAC,0BAAI,EAAI,CAAC,cAChDzL,KAAA,OAAIkK,SAAS,CAAC,oBAAoB,CAAAiB,QAAA,EAAEM,MAAM,CAAC,0BAAI,EAAI,CAAC,cACpDzL,KAAA,OAAIkK,SAAS,CAAC,gBAAgB,CAAAiB,QAAA,EAAEO,MAAM,CAAC,0BAAI,EAAI,CAAC,cAChD1L,KAAA,OAAIkK,SAAS,CAAC,oBAAoB,CAAAiB,QAAA,EAAEO,MAAM,CAAC,0BAAI,EAAI,CAAC,EAClD,CAAC,CACA,CAAC,cACR5L,IAAA,UAAAqL,QAAA,CACGQ,WAAW,CAAChG,GAAG,CAAC,CAACrB,IAAI,CAAE7B,KAAK,GAAK,CAChC,KAAM,CAAA8K,WAAW,CAAGjN,IAAI,CAACkN,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKnJ,IAAI,CAAC,CACnD;AACA,KAAM,CAAAoJ,SAAS,CAAGH,WAAW,EAAI,CAAC,CAAGA,WAAW,aAAAnI,MAAA,CAAe3C,KAAK,CAAE,CACtE,mBACEzC,KAAA,OAAoBkK,SAAS,CAAC,UAAU,CAAAiB,QAAA,eACtCrL,IAAA,OAAIoK,SAAS,CAAC,sBAAsB,CAAAiB,QAAA,CACjC1I,KAAK,CAAG,CAAC,CACR,CAAC,cACL3C,IAAA,OAAIoK,SAAS,CAAC,uBAAuB,CAAAiB,QAAA,CAClC/B,kBAAkB,CAAC9E,IAAI,CAAC,cAAc,CAAC,CAAEiJ,WAAW,CAAE,cAAc,CAAC,CACpE,CAAC,cACLzN,IAAA,OAAIoK,SAAS,CAAC,oBAAoB,CAAAiB,QAAA,CAC/B/B,kBAAkB,CAAC9E,IAAI,CAACsB,EAAE,CAAE2H,WAAW,CAAE,IAAI,CAAC,CAC7C,CAAC,cACLzN,IAAA,OAAIoK,SAAS,CAAC,sBAAsB,CAAAiB,QAAA,CACjC/B,kBAAkB,CAAC9E,IAAI,CAAC,SAAS,CAAC,CAAEiJ,WAAW,CAAE,SAAS,CAAC,CAC1D,CAAC,cACLzN,IAAA,OAAIoK,SAAS,CAAC,wBAAwB,CAAAiB,QAAA,CACnC/B,kBAAkB,CAAC9E,IAAI,CAACqJ,IAAI,CAAEJ,WAAW,CAAE,MAAM,CAAC,CACjD,CAAC,cACLzN,IAAA,OAAIoK,SAAS,CAAC,oBAAoB,CAAAiB,QAAA,CAC/B/B,kBAAkB,CAAC9E,IAAI,CAACsJ,IAAI,CAAEL,WAAW,CAAE,MAAM,CAAC,CACjD,CAAC,cACLzN,IAAA,OAAIoK,SAAS,CAAC,2BAA2B,CAAAiB,QAAA,CACtC/B,kBAAkB,CAAC9E,IAAI,CAACG,GAAG,CAAE8I,WAAW,CAAE,KAAK,CAAC,CAC/C,CAAC,CACJhC,kBAAkB,CAACjH,IAAI,CAAEiJ,WAAW,CAAC,GAtB/BG,SAuBL,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAEL/B,WAAW,CAACzH,MAAM,GAAK,CAAC,eACvBpE,IAAA,QAAKoK,SAAS,CAAC,SAAS,CAAAiB,QAAA,cACtBrL,IAAA,MAAAqL,QAAA,CAAG,oEAAW,CAAG,CAAC,CACf,CACN,CAGAnI,iBAAiB,eAChBlD,IAAA,CAACF,uBAAuB,EACtBU,IAAI,CAAEqL,WAAY,CAClB5I,UAAU,CAAEA,UAAW,CACvB8K,UAAU,CAAE/E,cAAe,CAC3BgF,OAAO,CAAEA,CAAA,GAAM7K,oBAAoB,CAAC,KAAK,CAAE,CAC3CvC,OAAO,CAAEwC,eAAgB,CAC1B,CACF,EACE,CAAC,CAEV,CAAC,CAID,cAAe,CAAA/C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}