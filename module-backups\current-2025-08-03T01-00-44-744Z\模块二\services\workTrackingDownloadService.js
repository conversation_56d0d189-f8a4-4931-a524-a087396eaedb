import * as XLSX from 'xlsx';

class WorkTrackingDownloadService {
  constructor() {
    this.baseURL = '/api';
  }

  // 处理选择性下载
  async handleSelectiveDownload(selectionData) {
    const { selectedData, format, monthRange, responsibleFilter, statistics, monthPairs } = selectionData;
    
    try {
      switch (format) {
        case 'excel':
          return await this.generateExcel(selectedData, monthRange, responsibleFilter, statistics, monthPairs);
        case 'pdf':
          return await this.generatePDF(selectedData, monthRange, responsibleFilter, statistics, monthPairs);
        case 'csv':
          return await this.generateCSV(selectedData, monthRange, responsibleFilter, statistics, monthPairs);
        default:
          throw new Error(`不支持的格式: ${format}`);
      }
    } catch (error) {
      console.error('下载失败:', error);
      throw error;
    }
  }

  // 生成Excel文件
  async generateExcel(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {
    try {
      // 创建新的工作簿
      const workbook = XLSX.utils.book_new();
      
      // 创建统一的工作表，包含所有工作类型数据
      this.createUnifiedTrackingWorksheet(workbook, selectedData, monthRange, responsibleFilter, statistics, monthPairs);

      // 生成并下载文件
      const fileName = `重点工作跟踪选择导出_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      return {
        success: true,
        fileName,
        message: `Excel文件已生成: ${fileName}`
      };
    } catch (error) {
      console.error('Excel生成失败:', error);
      throw error;
    }
  }

  // 按工作类型分组数据
  groupDataByType(selectedData) {
    const grouped = {};
    selectedData.forEach(item => {
      const workType = item.data.重点工作类型 || '其他';
      if (!grouped[workType]) {
        grouped[workType] = [];
      }
      grouped[workType].push(item);
    });
    return grouped;
  }

  // 创建统一跟踪工作表（新方法）
  createUnifiedTrackingWorksheet(workbook, selectedData, monthRange, responsibleFilter, statistics, monthPairs) {
    // 准备基础表头
    const baseHeaders = [
      '序号', 
      '重点工作类型',
      '相关指标或方向',
      '总体目标值',
      '2025年目标',
      '计算方法&2025年举措',
      '负责人',
      '跟踪频次'
    ];
    
    // 根据月份范围生成月份列
    const monthHeaders = [];
    for (let i = monthRange.start; i <= monthRange.end; i++) {
      if (monthPairs[i]) {
        monthHeaders.push(`${monthPairs[i][0]}工作计划`, `${monthPairs[i][0]}完成情况`);
        monthHeaders.push(`${monthPairs[i][1]}工作计划`, `${monthPairs[i][1]}完成情况`);
      }
    }
    
    const headers = [...baseHeaders, ...monthHeaders];
    
    // 准备所有数据行
    const allRows = [];
    
    // 添加标题行
    allRows.push(['开发中心重点工作跟踪表', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
    
    // 添加筛选信息
    const startMonth = monthPairs[monthRange.start]?.[0] || '';
    const endMonth = monthPairs[monthRange.end]?.[1] || '';
    allRows.push([`数据范围: ${startMonth} 至 ${endMonth}`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
    
    if (responsibleFilter.length > 0) {
      allRows.push([`负责人筛选: ${responsibleFilter.join('、')}`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
    }
    
    // 按工作类型分组数据
    const groupedData = this.groupDataByType(selectedData);
    let hasData = false;
    
    Object.entries(groupedData).forEach(([workType, items]) => {
      if (items && items.length > 0) {
        hasData = true;
        
        // 添加工作类型标题行
        allRows.push([`📋 ${workType} (${items.length}项)`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
        
        // 添加表头
        allRows.push([...headers]);
        
        // 添加该工作类型的数据
        items.forEach((item, index) => {
          const data = item.data;
          
          const row = [
            this.formatValue(data.序号),
            this.formatValue(data.重点工作类型),
            this.formatValue(data.相关指标或方向),
            this.formatValue(data.总体目标值),
            this.formatValue(data['2025年目标']),
            this.formatValue(data['计算方法&2025年举措']),
            this.formatValue(data.负责人),
            this.formatValue(data.跟踪频次)
          ];

          // 添加月份数据
          for (let i = monthRange.start; i <= monthRange.end; i++) {
            if (monthPairs[i]) {
              const month1 = monthPairs[i][0];
              const month2 = monthPairs[i][1];
              
              row.push(
                this.formatValue(data[`${month1}工作计划`]),
                this.formatValue(data[`${month1}完成情况`]),
                this.formatValue(data[`${month2}工作计划`]),
                this.formatValue(data[`${month2}完成情况`])
              );
            }
          }
          
          allRows.push(row);
        });
      }
    });

    // 如果没有数据，添加提示
    if (!hasData) {
      allRows.push(['暂无选择的数据', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
    } else {
      // 添加总计
      allRows.push(['=== 总计 ===', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);
      allRows.push([
        '总工作项数',
        `${statistics.totalItems}项`,
        '',
        `工作类型: ${statistics.workTypeCount}种`,
        '',
        '',
        '',
        '',
        ...monthHeaders.map(() => '')
      ]);
    }
    
    // 添加导出时间到最后一行
    allRows.push(['导出时间: ' + this.formatDateTime(new Date()), '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(allRows);
    
    // 设置列宽 - 调整为包含所有列
    const colWidths = [
      { wch: 8 },   // 序号
      { wch: 20 },  // 重点工作类型
      { wch: 35 },  // 相关指标或方向
      { wch: 20 },  // 总体目标值
      { wch: 20 },  // 2025年目标
      { wch: 40 },  // 计算方法&2025年举措
      { wch: 12 },  // 负责人
      { wch: 12 },  // 跟踪频次
      ...monthHeaders.map(() => ({ wch: 15 })) // 月份列
    ];
    worksheet['!cols'] = colWidths;

    // 设置样式
    this.setUnifiedTrackingWorksheetStyle(worksheet, allRows.length, headers.length);
    
    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '重点工作跟踪导出');
  }

  // 设置统一跟踪工作表样式（新方法）
  setUnifiedTrackingWorksheetStyle(worksheet, totalRows, colCount) {
    // 设置主标题样式 (第1行)
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (worksheet[cellRef]) {
        worksheet[cellRef].s = {
          font: { bold: true, size: 16, color: { rgb: "FFFFFF" } },
          fill: { fgColor: { rgb: "00D4AA" } },
          alignment: { horizontal: "center", vertical: "center" }
        };
      }
    }

    // 合并主标题单元格
    if (!worksheet['!merges']) worksheet['!merges'] = [];
    worksheet['!merges'].push({
      s: { r: 0, c: 0 },
      e: { r: 0, c: colCount - 1 }
    });

    // 查找并设置各种样式
    for (let r = 0; r < totalRows; r++) {
      const cellRef = XLSX.utils.encode_cell({ r: r, c: 0 });
      if (worksheet[cellRef] && worksheet[cellRef].v) {
        const cellValue = worksheet[cellRef].v.toString();
        
        // 工作类型标题样式
        if (cellValue.includes('📋') && cellValue.includes('项)')) {
          for (let i = 0; i < colCount; i++) {
            const titleCellRef = XLSX.utils.encode_cell({ r: r, c: i });
            if (worksheet[titleCellRef]) {
              worksheet[titleCellRef].s = {
                font: { bold: true, size: 14, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "4DD0FF" } },
                alignment: { horizontal: "left", vertical: "center" }
              };
            }
          }
          // 合并工作类型标题
          worksheet['!merges'].push({
            s: { r: r, c: 0 },
            e: { r: r, c: colCount - 1 }
          });
        }
        
        // 表头样式
        else if (cellValue === '序号') {
          for (let i = 0; i < colCount; i++) {
            const headerCellRef = XLSX.utils.encode_cell({ r: r, c: i });
            if (worksheet[headerCellRef]) {
              worksheet[headerCellRef].s = {
                font: { bold: true, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "20FF4D" } },
                alignment: { horizontal: "center", vertical: "center" }
              };
            }
          }
        }
        
        // 总计样式
        else if (cellValue.includes('总计') || cellValue.includes('=== 总计 ===')) {
          for (let i = 0; i < colCount; i++) {
            const totalCellRef = XLSX.utils.encode_cell({ r: r, c: i });
            if (worksheet[totalCellRef]) {
              worksheet[totalCellRef].s = {
                font: { bold: true, color: { rgb: "000000" } },
                fill: { fgColor: { rgb: "FFFF4D" } },
                alignment: { horizontal: "center", vertical: "center" }
              };
            }
          }
        }
        
        // 信息行样式（导出时间、数据范围等）
        else if (cellValue.includes('导出时间:') || cellValue.includes('数据范围:') || cellValue.includes('负责人筛选:')) {
          for (let i = 0; i < colCount; i++) {
            const infoCellRef = XLSX.utils.encode_cell({ r: r, c: i });
            if (worksheet[infoCellRef]) {
              worksheet[infoCellRef].s = {
                font: { bold: true, color: { rgb: "333333" } },
                fill: { fgColor: { rgb: "E0E0E0" } },
                alignment: { horizontal: "left", vertical: "center" }
              };
            }
          }
          // 合并信息行
          worksheet['!merges'].push({
            s: { r: r, c: 0 },
            e: { r: r, c: colCount - 1 }
          });
        }
      }
    }
  }





  // 生成PDF文件（临时实现）
  async generatePDF(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {
    try {
      console.log('PDF生成功能待实现');
      
      // 临时实现：转为JSON后提示
      const jsonData = this.prepareDataForExport(selectedData, monthRange, responsibleFilter, statistics, monthPairs);
      const dataStr = JSON.stringify(jsonData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `重点工作跟踪选择导出_${this.formatDate(new Date())}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'PDF功能开发中，已生成JSON格式文件'
      };
    } catch (error) {
      console.error('PDF生成失败:', error);
      throw error;
    }
  }

  // 生成CSV文件
  async generateCSV(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {
    try {
      let csvContent = '\uFEFF'; // UTF-8 BOM for Excel compatibility
      
      // 添加汇总信息
      csvContent += '重点工作跟踪选择导出\n';
      csvContent += `导出时间,${this.formatDateTime(new Date())}\n`;
      csvContent += `月份范围,${monthPairs[monthRange.start]?.[0] || ''} 至 ${monthPairs[monthRange.end]?.[1] || ''}\n`;
      if (responsibleFilter.length > 0) {
        csvContent += `负责人筛选,${responsibleFilter.join('、')}\n`;
      }
      csvContent += `总工作项数,${statistics.totalItems}\n`;
      csvContent += `工作类型数,${statistics.workTypeCount}\n\n`;

      // 按工作类型分组导出
      const groupedData = this.groupDataByType(selectedData);
      
      Object.entries(groupedData).forEach(([workType, items]) => {
        csvContent += `\n=== ${workType} ===\n`;
        
        // 完整表头 - 包含所有必要字段
        const baseHeaders = [
          '序号', 
          '重点工作类型',
          '相关指标或方向',
          '总体目标值',
          '2025年目标',
          '计算方法&2025年举措',
          '负责人',
          '跟踪频次'
        ];
        
        const monthHeaders = [];
        
        for (let i = monthRange.start; i <= monthRange.end; i++) {
          if (monthPairs[i]) {
            monthHeaders.push(`${monthPairs[i][0]}工作计划`, `${monthPairs[i][0]}完成情况`);
            monthHeaders.push(`${monthPairs[i][1]}工作计划`, `${monthPairs[i][1]}完成情况`);
          }
        }
        
        const headers = [...baseHeaders, ...monthHeaders];
        csvContent += headers.join(',') + '\n';
        
        // 数据行
        items.forEach(item => {
          const data = item.data;
          
          const row = [
            this.escapeCsvValue(data.序号),
            this.escapeCsvValue(data.重点工作类型),
            this.escapeCsvValue(data.相关指标或方向),
            this.escapeCsvValue(data.总体目标值),
            this.escapeCsvValue(data['2025年目标']),
            this.escapeCsvValue(data['计算方法&2025年举措']),
            this.escapeCsvValue(data.负责人),
            this.escapeCsvValue(data.跟踪频次)
          ];

          // 添加月份数据
          for (let i = monthRange.start; i <= monthRange.end; i++) {
            if (monthPairs[i]) {
              const month1 = monthPairs[i][0];
              const month2 = monthPairs[i][1];
              
              row.push(
                this.escapeCsvValue(data[`${month1}工作计划`]),
                this.escapeCsvValue(data[`${month1}完成情况`]),
                this.escapeCsvValue(data[`${month2}工作计划`]),
                this.escapeCsvValue(data[`${month2}完成情况`])
              );
            }
          }
          
          csvContent += row.join(',') + '\n';
        });
        csvContent += '\n';
      });

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `重点工作跟踪选择导出_${this.formatDate(new Date())}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'CSV文件已生成并下载'
      };
    } catch (error) {
      console.error('CSV生成失败:', error);
      throw error;
    }
  }

  // 工具方法：格式化值
  formatValue(value) {
    if (value === null || value === undefined || value === '') {
      return '';
    }
    
    // 处理Excel公式对象
    if (typeof value === 'object') {
      if (value.hasOwnProperty('v')) return value.v;
      if (value.hasOwnProperty('w')) return value.w;
      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    
    return String(value);
  }

  // 工具方法：CSV值转义
  escapeCsvValue(value) {
    const formattedValue = this.formatValue(value);
    if (formattedValue.includes(',') || formattedValue.includes('"') || formattedValue.includes('\n')) {
      return `"${formattedValue.replace(/"/g, '""')}"`;
    }
    return formattedValue;
  }

  // 工具方法：设置表头样式
  setHeaderStyle(worksheet, colCount) {
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (!worksheet[cellRef]) continue;
      
      worksheet[cellRef].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "00D4AA" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }
  }

  // 工具方法：格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  // 工具方法：格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // 准备导出数据
  prepareDataForExport(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {
    return {
      exportInfo: {
        timestamp: new Date().toISOString(),
        totalItems: statistics.totalItems,
        workTypeCount: statistics.workTypeCount,
        monthRange: {
          start: monthPairs[monthRange.start]?.[0] || '',
          end: monthPairs[monthRange.end]?.[1] || ''
        },
        responsibleFilter
      },
      data: selectedData
    };
  }
}

export default new WorkTrackingDownloadService(); 