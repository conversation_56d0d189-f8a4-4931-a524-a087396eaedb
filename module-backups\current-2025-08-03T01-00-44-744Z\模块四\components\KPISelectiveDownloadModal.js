import React, { useState, useEffect } from 'react';
import './KPISelectiveDownloadModal.css';

const KPISelectiveDownloadModal = ({ data, onDownload, onClose, loading }) => {
  const [selectedIndicators, setSelectedIndicators] = useState(new Set());
  const [downloadFormat, setDownloadFormat] = useState('excel');
  const [selectAll, setSelectAll] = useState(false);
  const [statistics, setStatistics] = useState({
    totalItems: 0,
    selectedItems: 0,
    totalScore: 0,
    selectedScore: 0
  });

  // 获取所有指标列表（去重）
  const getIndicators = () => {
    const indicators = [];
    const seen = new Set();
    
    data.forEach((item, index) => {
      const indicator = item.指标 || '';
      if (indicator && !seen.has(indicator)) {
        seen.add(indicator);
        indicators.push({
          name: indicator,
          index: index,
          score: parseFloat(item.分值) || 0,
          target: item.目标值 || '',
          method: item['统计方式&口径'] || item.统计方式 || ''
        });
      }
    });
    
    return indicators;
  };

  // 更新统计信息
  useEffect(() => {
    const indicators = getIndicators();
    const selectedItems = indicators.filter(indicator => 
      selectedIndicators.has(indicator.name)
    );
    
    setStatistics({
      totalItems: indicators.length,
      selectedItems: selectedItems.length,
      totalScore: indicators.reduce((sum, item) => sum + item.score, 0),
      selectedScore: selectedItems.reduce((sum, item) => sum + item.score, 0)
    });
  }, [selectedIndicators, data]);

  // 处理指标选择
  const handleIndicatorChange = (indicatorName) => {
    setSelectedIndicators(prev => {
      const newSet = new Set(prev);
      if (newSet.has(indicatorName)) {
        newSet.delete(indicatorName);
      } else {
        newSet.add(indicatorName);
      }
      return newSet;
    });
  };

  // 处理全选/反选
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedIndicators(new Set());
    } else {
      const allIndicators = getIndicators().map(item => item.name);
      setSelectedIndicators(new Set(allIndicators));
    }
    setSelectAll(!selectAll);
  };

  // 更新全选状态
  useEffect(() => {
    const indicators = getIndicators();
    const allSelected = indicators.length > 0 && 
                       indicators.every(item => selectedIndicators.has(item.name));
    setSelectAll(allSelected);
  }, [selectedIndicators, data]);

  // 处理下载
  const handleDownloadClick = () => {
    if (selectedIndicators.size === 0) {
      alert('请至少选择一个指标');
      return;
    }

    // 获取选中指标对应的数据行
    const selectedData = data.filter(item => 
      selectedIndicators.has(item.指标)
    );

    const selectionData = {
      selectedData: selectedData,
      selectedIndicators: Array.from(selectedIndicators),
      format: downloadFormat,
      includeAllMonths: true, // 包含全年数据
      statistics: statistics
    };
    
    onDownload(selectionData);
  };

  const indicators = getIndicators();

  return (
    <div className="kpi-selective-modal-overlay">
      <div className="kpi-selective-modal">
        <div className="modal-header">
          <h3>选择性下载 - 基于指标选择</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          {/* 统计信息 */}
          <div className="statistics-section">
            <div className="stats-grid">
              <div className="stat-item">
                <span className="stat-label">总指标数</span>
                <span className="stat-value">{statistics.totalItems}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">已选择</span>
                <span className="stat-value selected">{statistics.selectedItems}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">总分值</span>
                <span className="stat-value">{statistics.totalScore}</span>
              </div>
              <div className="stat-item">
                <span className="stat-label">选中分值</span>
                <span className="stat-value selected">{statistics.selectedScore}</span>
              </div>
            </div>
          </div>

          {/* 快速操作 */}
          <div className="selection-section">
            <div className="section-header">
              <h4>快速操作</h4>
              <button 
                className={`select-all-button ${selectAll ? 'selected' : ''}`}
                onClick={handleSelectAll}
              >
                {selectAll ? '取消全选' : '全选指标'}
              </button>
            </div>
          </div>

          {/* 指标选择列表 */}
          <div className="selection-section">
            <h4>选择指标 ({indicators.length} 项)</h4>
            <div className="indicators-list">
              {indicators.map((indicator, index) => (
                <label key={index} className="checkbox-item indicator-item">
                  <input
                    type="checkbox"
                    checked={selectedIndicators.has(indicator.name)}
                    onChange={() => handleIndicatorChange(indicator.name)}
                  />
                  <div className="indicator-info">
                    <div className="indicator-main">
                      <span className="indicator-name">{indicator.name}</span>
                      <span className="indicator-score">({indicator.score}分)</span>
                    </div>
                    <div className="indicator-details">
                      <span className="indicator-target">目标: {indicator.target}</span>
                      {indicator.method && (
                        <span className="indicator-method">方式: {indicator.method}</span>
                      )}
                    </div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* 下载格式选择 */}
          <div className="selection-section">
            <h4>下载格式</h4>
            <div className="format-options">
              <label className="radio-item">
                <input
                  type="radio"
                  name="format"
                  value="excel"
                  checked={downloadFormat === 'excel'}
                  onChange={(e) => setDownloadFormat(e.target.value)}
                />
                <span>Excel格式 (.xlsx)</span>
              </label>
              <label className="radio-item">
                <input
                  type="radio"
                  name="format"
                  value="csv"
                  checked={downloadFormat === 'csv'}
                  onChange={(e) => setDownloadFormat(e.target.value)}
                />
                <span>CSV格式 (.csv)</span>
              </label>
            </div>
          </div>

          {/* 数据范围说明 */}
          <div className="info-section">
            <div className="info-box">
              <h5>📊 数据范围说明</h5>
              <ul>
                <li>✅ 包含完整的2月-12月全年数据</li>
                <li>✅ 包含所有固定列：序号、指标、目标值、分值、统计方式&口径、考核标准</li>
                <li>✅ 包含所有月份的计划、完成情况、得分数据</li>
                <li>⚠️ 只导出选中的指标行数据</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="modal-footer">
          <button 
            className="cancel-button"
            onClick={onClose}
            disabled={loading}
          >
            取消
          </button>
          <button 
            className="download-button"
            onClick={handleDownloadClick}
            disabled={loading || selectedIndicators.size === 0}
          >
            {loading ? '下载中...' : `下载选中项 (${statistics.selectedItems})`}
          </button>
        </div>
      </div>
    </div>
  );
};

export default KPISelectiveDownloadModal;
