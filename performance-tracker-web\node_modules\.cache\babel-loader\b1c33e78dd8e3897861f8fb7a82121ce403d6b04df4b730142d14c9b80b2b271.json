{"ast": null, "code": "import React,{useState,useEffect}from'react';import'./KPISelectiveDownloadModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const KPISelectiveDownloadModal=_ref=>{let{data,onDownload,onClose,loading}=_ref;const[selectedIndicators,setSelectedIndicators]=useState(new Set());const[downloadFormat,setDownloadFormat]=useState('excel');const[selectAll,setSelectAll]=useState(false);const[statistics,setStatistics]=useState({totalItems:0,selectedItems:0,totalScore:0,selectedScore:0});// 获取所有指标列表（去重）\nconst getIndicators=()=>{const indicators=[];const seen=new Set();data.forEach((item,index)=>{const indicator=item.指标||'';if(indicator&&!seen.has(indicator)){seen.add(indicator);indicators.push({name:indicator,index:index,score:parseFloat(item.分值)||0,target:item.目标值||'',method:item['统计方式&口径']||item.统计方式||''});}});return indicators;};// 更新统计信息\nuseEffect(()=>{const indicators=getIndicators();const selectedItems=indicators.filter(indicator=>selectedIndicators.has(indicator.name));setStatistics({totalItems:indicators.length,selectedItems:selectedItems.length,totalScore:indicators.reduce((sum,item)=>sum+item.score,0),selectedScore:selectedItems.reduce((sum,item)=>sum+item.score,0)});},[selectedIndicators,data]);// 处理指标选择\nconst handleIndicatorChange=indicatorName=>{setSelectedIndicators(prev=>{const newSet=new Set(prev);if(newSet.has(indicatorName)){newSet.delete(indicatorName);}else{newSet.add(indicatorName);}return newSet;});};// 处理全选/反选\nconst handleSelectAll=()=>{if(selectAll){setSelectedIndicators(new Set());}else{const allIndicators=getIndicators().map(item=>item.name);setSelectedIndicators(new Set(allIndicators));}setSelectAll(!selectAll);};// 更新全选状态\nuseEffect(()=>{const indicators=getIndicators();const allSelected=indicators.length>0&&indicators.every(item=>selectedIndicators.has(item.name));setSelectAll(allSelected);},[selectedIndicators,data]);// 处理下载\nconst handleDownloadClick=()=>{if(selectedIndicators.size===0){alert('请至少选择一个指标');return;}// 获取选中指标对应的数据行\nconst selectedData=data.filter(item=>selectedIndicators.has(item.指标));const selectionData={selectedData:selectedData,selectedIndicators:Array.from(selectedIndicators),format:downloadFormat,includeAllMonths:true,// 包含全年数据\nstatistics:statistics};onDownload(selectionData);};const indicators=getIndicators();return/*#__PURE__*/_jsx(\"div\",{className:\"kpi-selective-modal-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kpi-selective-modal\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u9009\\u62E9\\u6027\\u4E0B\\u8F7D - \\u57FA\\u4E8E\\u6307\\u6807\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-button\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"statistics-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"stats-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u603B\\u6307\\u6807\\u6570\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:statistics.totalItems})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u5DF2\\u9009\\u62E9\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value selected\",children:statistics.selectedItems})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u603B\\u5206\\u503C\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value\",children:statistics.totalScore})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"stat-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stat-label\",children:\"\\u9009\\u4E2D\\u5206\\u503C\"}),/*#__PURE__*/_jsx(\"span\",{className:\"stat-value selected\",children:statistics.selectedScore})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"selection-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"section-header\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u5FEB\\u901F\\u64CD\\u4F5C\"}),/*#__PURE__*/_jsx(\"button\",{className:\"select-all-button \".concat(selectAll?'selected':''),onClick:handleSelectAll,children:selectAll?'取消全选':'全选指标'})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-section\",children:[/*#__PURE__*/_jsxs(\"h4\",{children:[\"\\u9009\\u62E9\\u6307\\u6807 (\",indicators.length,\" \\u9879)\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"indicators-list\",children:indicators.map((indicator,index)=>/*#__PURE__*/_jsxs(\"label\",{className:\"checkbox-item indicator-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectedIndicators.has(indicator.name),onChange:()=>handleIndicatorChange(indicator.name)}),/*#__PURE__*/_jsxs(\"div\",{className:\"indicator-info\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"indicator-main\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"indicator-name\",children:indicator.name}),/*#__PURE__*/_jsxs(\"span\",{className:\"indicator-score\",children:[\"(\",indicator.score,\"\\u5206)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"indicator-details\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"indicator-target\",children:[\"\\u76EE\\u6807: \",indicator.target]}),indicator.method&&/*#__PURE__*/_jsxs(\"span\",{className:\"indicator-method\",children:[\"\\u65B9\\u5F0F: \",indicator.method]})]})]})]},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"selection-section\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u4E0B\\u8F7D\\u683C\\u5F0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"format-options\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"radio-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",name:\"format\",value:\"excel\",checked:downloadFormat==='excel',onChange:e=>setDownloadFormat(e.target.value)}),/*#__PURE__*/_jsx(\"span\",{children:\"Excel\\u683C\\u5F0F (.xlsx)\"})]}),/*#__PURE__*/_jsxs(\"label\",{className:\"radio-item\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"radio\",name:\"format\",value:\"csv\",checked:downloadFormat==='csv',onChange:e=>setDownloadFormat(e.target.value)}),/*#__PURE__*/_jsx(\"span\",{children:\"CSV\\u683C\\u5F0F (.csv)\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"info-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"info-box\",children:[/*#__PURE__*/_jsx(\"h5\",{children:\"\\uD83D\\uDCCA \\u6570\\u636E\\u8303\\u56F4\\u8BF4\\u660E\"}),/*#__PURE__*/_jsxs(\"ul\",{children:[/*#__PURE__*/_jsx(\"li\",{children:\"\\u2705 \\u5305\\u542B\\u5B8C\\u6574\\u76842\\u6708-12\\u6708\\u5168\\u5E74\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2705 \\u5305\\u542B\\u6240\\u6709\\u56FA\\u5B9A\\u5217\\uFF1A\\u5E8F\\u53F7\\u3001\\u6307\\u6807\\u3001\\u76EE\\u6807\\u503C\\u3001\\u5206\\u503C\\u3001\\u7EDF\\u8BA1\\u65B9\\u5F0F&\\u53E3\\u5F84\\u3001\\u8003\\u6838\\u6807\\u51C6\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u2705 \\u5305\\u542B\\u6240\\u6709\\u6708\\u4EFD\\u7684\\u8BA1\\u5212\\u3001\\u5B8C\\u6210\\u60C5\\u51B5\\u3001\\u5F97\\u5206\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"li\",{children:\"\\u26A0\\uFE0F \\u53EA\\u5BFC\\u51FA\\u9009\\u4E2D\\u7684\\u6307\\u6807\\u884C\\u6570\\u636E\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-button\",onClick:onClose,disabled:loading,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsx(\"button\",{className:\"download-button\",onClick:handleDownloadClick,disabled:loading||selectedIndicators.size===0,children:loading?'下载中...':\"\\u4E0B\\u8F7D\\u9009\\u4E2D\\u9879 (\".concat(statistics.selectedItems,\")\")})]})]})});};export default KPISelectiveDownloadModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "KPISelectiveDownloadModal", "_ref", "data", "onDownload", "onClose", "loading", "selectedIndicators", "setSelectedIndicators", "Set", "downloadFormat", "setDownloadFormat", "selectAll", "setSelectAll", "statistics", "setStatistics", "totalItems", "selectedItems", "totalScore", "selectedScore", "getIndicators", "indicators", "seen", "for<PERSON>ach", "item", "index", "indicator", "指标", "has", "add", "push", "name", "score", "parseFloat", "分值", "target", "目标值", "method", "统计方式", "filter", "length", "reduce", "sum", "handleIndicatorChange", "indicatorName", "prev", "newSet", "delete", "handleSelectAll", "allIndicators", "map", "allSelected", "every", "handleDownloadClick", "size", "alert", "selectedData", "selectionData", "Array", "from", "format", "includeAllMonths", "className", "children", "onClick", "concat", "type", "checked", "onChange", "value", "e", "disabled"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块四/components/KPISelectiveDownloadModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './KPISelectiveDownloadModal.css';\n\nconst KPISelectiveDownloadModal = ({ data, onDownload, onClose, loading }) => {\n  const [selectedIndicators, setSelectedIndicators] = useState(new Set());\n  const [downloadFormat, setDownloadFormat] = useState('excel');\n  const [selectAll, setSelectAll] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    selectedItems: 0,\n    totalScore: 0,\n    selectedScore: 0\n  });\n\n  // 获取所有指标列表（去重）\n  const getIndicators = () => {\n    const indicators = [];\n    const seen = new Set();\n    \n    data.forEach((item, index) => {\n      const indicator = item.指标 || '';\n      if (indicator && !seen.has(indicator)) {\n        seen.add(indicator);\n        indicators.push({\n          name: indicator,\n          index: index,\n          score: parseFloat(item.分值) || 0,\n          target: item.目标值 || '',\n          method: item['统计方式&口径'] || item.统计方式 || ''\n        });\n      }\n    });\n    \n    return indicators;\n  };\n\n  // 更新统计信息\n  useEffect(() => {\n    const indicators = getIndicators();\n    const selectedItems = indicators.filter(indicator => \n      selectedIndicators.has(indicator.name)\n    );\n    \n    setStatistics({\n      totalItems: indicators.length,\n      selectedItems: selectedItems.length,\n      totalScore: indicators.reduce((sum, item) => sum + item.score, 0),\n      selectedScore: selectedItems.reduce((sum, item) => sum + item.score, 0)\n    });\n  }, [selectedIndicators, data]);\n\n  // 处理指标选择\n  const handleIndicatorChange = (indicatorName) => {\n    setSelectedIndicators(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(indicatorName)) {\n        newSet.delete(indicatorName);\n      } else {\n        newSet.add(indicatorName);\n      }\n      return newSet;\n    });\n  };\n\n  // 处理全选/反选\n  const handleSelectAll = () => {\n    if (selectAll) {\n      setSelectedIndicators(new Set());\n    } else {\n      const allIndicators = getIndicators().map(item => item.name);\n      setSelectedIndicators(new Set(allIndicators));\n    }\n    setSelectAll(!selectAll);\n  };\n\n  // 更新全选状态\n  useEffect(() => {\n    const indicators = getIndicators();\n    const allSelected = indicators.length > 0 && \n                       indicators.every(item => selectedIndicators.has(item.name));\n    setSelectAll(allSelected);\n  }, [selectedIndicators, data]);\n\n  // 处理下载\n  const handleDownloadClick = () => {\n    if (selectedIndicators.size === 0) {\n      alert('请至少选择一个指标');\n      return;\n    }\n\n    // 获取选中指标对应的数据行\n    const selectedData = data.filter(item => \n      selectedIndicators.has(item.指标)\n    );\n\n    const selectionData = {\n      selectedData: selectedData,\n      selectedIndicators: Array.from(selectedIndicators),\n      format: downloadFormat,\n      includeAllMonths: true, // 包含全年数据\n      statistics: statistics\n    };\n    \n    onDownload(selectionData);\n  };\n\n  const indicators = getIndicators();\n\n  return (\n    <div className=\"kpi-selective-modal-overlay\">\n      <div className=\"kpi-selective-modal\">\n        <div className=\"modal-header\">\n          <h3>选择性下载 - 基于指标选择</h3>\n          <button className=\"close-button\" onClick={onClose}>×</button>\n        </div>\n\n        <div className=\"modal-content\">\n          {/* 统计信息 */}\n          <div className=\"statistics-section\">\n            <div className=\"stats-grid\">\n              <div className=\"stat-item\">\n                <span className=\"stat-label\">总指标数</span>\n                <span className=\"stat-value\">{statistics.totalItems}</span>\n              </div>\n              <div className=\"stat-item\">\n                <span className=\"stat-label\">已选择</span>\n                <span className=\"stat-value selected\">{statistics.selectedItems}</span>\n              </div>\n              <div className=\"stat-item\">\n                <span className=\"stat-label\">总分值</span>\n                <span className=\"stat-value\">{statistics.totalScore}</span>\n              </div>\n              <div className=\"stat-item\">\n                <span className=\"stat-label\">选中分值</span>\n                <span className=\"stat-value selected\">{statistics.selectedScore}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* 快速操作 */}\n          <div className=\"selection-section\">\n            <div className=\"section-header\">\n              <h4>快速操作</h4>\n              <button \n                className={`select-all-button ${selectAll ? 'selected' : ''}`}\n                onClick={handleSelectAll}\n              >\n                {selectAll ? '取消全选' : '全选指标'}\n              </button>\n            </div>\n          </div>\n\n          {/* 指标选择列表 */}\n          <div className=\"selection-section\">\n            <h4>选择指标 ({indicators.length} 项)</h4>\n            <div className=\"indicators-list\">\n              {indicators.map((indicator, index) => (\n                <label key={index} className=\"checkbox-item indicator-item\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedIndicators.has(indicator.name)}\n                    onChange={() => handleIndicatorChange(indicator.name)}\n                  />\n                  <div className=\"indicator-info\">\n                    <div className=\"indicator-main\">\n                      <span className=\"indicator-name\">{indicator.name}</span>\n                      <span className=\"indicator-score\">({indicator.score}分)</span>\n                    </div>\n                    <div className=\"indicator-details\">\n                      <span className=\"indicator-target\">目标: {indicator.target}</span>\n                      {indicator.method && (\n                        <span className=\"indicator-method\">方式: {indicator.method}</span>\n                      )}\n                    </div>\n                  </div>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 下载格式选择 */}\n          <div className=\"selection-section\">\n            <h4>下载格式</h4>\n            <div className=\"format-options\">\n              <label className=\"radio-item\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"excel\"\n                  checked={downloadFormat === 'excel'}\n                  onChange={(e) => setDownloadFormat(e.target.value)}\n                />\n                <span>Excel格式 (.xlsx)</span>\n              </label>\n              <label className=\"radio-item\">\n                <input\n                  type=\"radio\"\n                  name=\"format\"\n                  value=\"csv\"\n                  checked={downloadFormat === 'csv'}\n                  onChange={(e) => setDownloadFormat(e.target.value)}\n                />\n                <span>CSV格式 (.csv)</span>\n              </label>\n            </div>\n          </div>\n\n          {/* 数据范围说明 */}\n          <div className=\"info-section\">\n            <div className=\"info-box\">\n              <h5>📊 数据范围说明</h5>\n              <ul>\n                <li>✅ 包含完整的2月-12月全年数据</li>\n                <li>✅ 包含所有固定列：序号、指标、目标值、分值、统计方式&口径、考核标准</li>\n                <li>✅ 包含所有月份的计划、完成情况、得分数据</li>\n                <li>⚠️ 只导出选中的指标行数据</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"modal-footer\">\n          <button \n            className=\"cancel-button\"\n            onClick={onClose}\n            disabled={loading}\n          >\n            取消\n          </button>\n          <button \n            className=\"download-button\"\n            onClick={handleDownloadClick}\n            disabled={loading || selectedIndicators.size === 0}\n          >\n            {loading ? '下载中...' : `下载选中项 (${statistics.selectedItems})`}\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default KPISelectiveDownloadModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,iCAAiC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzC,KAAM,CAAAC,yBAAyB,CAAGC,IAAA,EAA4C,IAA3C,CAAEC,IAAI,CAAEC,UAAU,CAAEC,OAAO,CAAEC,OAAQ,CAAC,CAAAJ,IAAA,CACvE,KAAM,CAACK,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGb,QAAQ,CAAC,GAAI,CAAAc,GAAG,CAAC,CAAC,CAAC,CACvE,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAGhB,QAAQ,CAAC,OAAO,CAAC,CAC7D,KAAM,CAACiB,SAAS,CAAEC,YAAY,CAAC,CAAGlB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAACmB,UAAU,CAAEC,aAAa,CAAC,CAAGpB,QAAQ,CAAC,CAC3CqB,UAAU,CAAE,CAAC,CACbC,aAAa,CAAE,CAAC,CAChBC,UAAU,CAAE,CAAC,CACbC,aAAa,CAAE,CACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B,KAAM,CAAAC,UAAU,CAAG,EAAE,CACrB,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAb,GAAG,CAAC,CAAC,CAEtBN,IAAI,CAACoB,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CAC5B,KAAM,CAAAC,SAAS,CAAGF,IAAI,CAACG,EAAE,EAAI,EAAE,CAC/B,GAAID,SAAS,EAAI,CAACJ,IAAI,CAACM,GAAG,CAACF,SAAS,CAAC,CAAE,CACrCJ,IAAI,CAACO,GAAG,CAACH,SAAS,CAAC,CACnBL,UAAU,CAACS,IAAI,CAAC,CACdC,IAAI,CAAEL,SAAS,CACfD,KAAK,CAAEA,KAAK,CACZO,KAAK,CAAEC,UAAU,CAACT,IAAI,CAACU,EAAE,CAAC,EAAI,CAAC,CAC/BC,MAAM,CAAEX,IAAI,CAACY,GAAG,EAAI,EAAE,CACtBC,MAAM,CAAEb,IAAI,CAAC,SAAS,CAAC,EAAIA,IAAI,CAACc,IAAI,EAAI,EAC1C,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,CAAAjB,UAAU,CACnB,CAAC,CAED;AACAzB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyB,UAAU,CAAGD,aAAa,CAAC,CAAC,CAClC,KAAM,CAAAH,aAAa,CAAGI,UAAU,CAACkB,MAAM,CAACb,SAAS,EAC/CnB,kBAAkB,CAACqB,GAAG,CAACF,SAAS,CAACK,IAAI,CACvC,CAAC,CAEDhB,aAAa,CAAC,CACZC,UAAU,CAAEK,UAAU,CAACmB,MAAM,CAC7BvB,aAAa,CAAEA,aAAa,CAACuB,MAAM,CACnCtB,UAAU,CAAEG,UAAU,CAACoB,MAAM,CAAC,CAACC,GAAG,CAAElB,IAAI,GAAKkB,GAAG,CAAGlB,IAAI,CAACQ,KAAK,CAAE,CAAC,CAAC,CACjEb,aAAa,CAAEF,aAAa,CAACwB,MAAM,CAAC,CAACC,GAAG,CAAElB,IAAI,GAAKkB,GAAG,CAAGlB,IAAI,CAACQ,KAAK,CAAE,CAAC,CACxE,CAAC,CAAC,CACJ,CAAC,CAAE,CAACzB,kBAAkB,CAAEJ,IAAI,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAwC,qBAAqB,CAAIC,aAAa,EAAK,CAC/CpC,qBAAqB,CAACqC,IAAI,EAAI,CAC5B,KAAM,CAAAC,MAAM,CAAG,GAAI,CAAArC,GAAG,CAACoC,IAAI,CAAC,CAC5B,GAAIC,MAAM,CAAClB,GAAG,CAACgB,aAAa,CAAC,CAAE,CAC7BE,MAAM,CAACC,MAAM,CAACH,aAAa,CAAC,CAC9B,CAAC,IAAM,CACLE,MAAM,CAACjB,GAAG,CAACe,aAAa,CAAC,CAC3B,CACA,MAAO,CAAAE,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAIpC,SAAS,CAAE,CACbJ,qBAAqB,CAAC,GAAI,CAAAC,GAAG,CAAC,CAAC,CAAC,CAClC,CAAC,IAAM,CACL,KAAM,CAAAwC,aAAa,CAAG7B,aAAa,CAAC,CAAC,CAAC8B,GAAG,CAAC1B,IAAI,EAAIA,IAAI,CAACO,IAAI,CAAC,CAC5DvB,qBAAqB,CAAC,GAAI,CAAAC,GAAG,CAACwC,aAAa,CAAC,CAAC,CAC/C,CACApC,YAAY,CAAC,CAACD,SAAS,CAAC,CAC1B,CAAC,CAED;AACAhB,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyB,UAAU,CAAGD,aAAa,CAAC,CAAC,CAClC,KAAM,CAAA+B,WAAW,CAAG9B,UAAU,CAACmB,MAAM,CAAG,CAAC,EACtBnB,UAAU,CAAC+B,KAAK,CAAC5B,IAAI,EAAIjB,kBAAkB,CAACqB,GAAG,CAACJ,IAAI,CAACO,IAAI,CAAC,CAAC,CAC9ElB,YAAY,CAACsC,WAAW,CAAC,CAC3B,CAAC,CAAE,CAAC5C,kBAAkB,CAAEJ,IAAI,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAkD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI9C,kBAAkB,CAAC+C,IAAI,GAAK,CAAC,CAAE,CACjCC,KAAK,CAAC,WAAW,CAAC,CAClB,OACF,CAEA;AACA,KAAM,CAAAC,YAAY,CAAGrD,IAAI,CAACoC,MAAM,CAACf,IAAI,EACnCjB,kBAAkB,CAACqB,GAAG,CAACJ,IAAI,CAACG,EAAE,CAChC,CAAC,CAED,KAAM,CAAA8B,aAAa,CAAG,CACpBD,YAAY,CAAEA,YAAY,CAC1BjD,kBAAkB,CAAEmD,KAAK,CAACC,IAAI,CAACpD,kBAAkB,CAAC,CAClDqD,MAAM,CAAElD,cAAc,CACtBmD,gBAAgB,CAAE,IAAI,CAAE;AACxB/C,UAAU,CAAEA,UACd,CAAC,CAEDV,UAAU,CAACqD,aAAa,CAAC,CAC3B,CAAC,CAED,KAAM,CAAApC,UAAU,CAAGD,aAAa,CAAC,CAAC,CAElC,mBACEtB,IAAA,QAAKgE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C/D,KAAA,QAAK8D,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC/D,KAAA,QAAK8D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjE,IAAA,OAAAiE,QAAA,CAAI,uEAAc,CAAI,CAAC,cACvBjE,IAAA,WAAQgE,SAAS,CAAC,cAAc,CAACE,OAAO,CAAE3D,OAAQ,CAAA0D,QAAA,CAAC,MAAC,CAAQ,CAAC,EAC1D,CAAC,cAEN/D,KAAA,QAAK8D,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BjE,IAAA,QAAKgE,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjC/D,KAAA,QAAK8D,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB/D,KAAA,QAAK8D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxCjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEjD,UAAU,CAACE,UAAU,CAAO,CAAC,EACxD,CAAC,cACNhB,KAAA,QAAK8D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvCjE,IAAA,SAAMgE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEjD,UAAU,CAACG,aAAa,CAAO,CAAC,EACpE,CAAC,cACNjB,KAAA,QAAK8D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAG,CAAM,CAAC,cACvCjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAEjD,UAAU,CAACI,UAAU,CAAO,CAAC,EACxD,CAAC,cACNlB,KAAA,QAAK8D,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjE,IAAA,SAAMgE,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxCjE,IAAA,SAAMgE,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAEjD,UAAU,CAACK,aAAa,CAAO,CAAC,EACpE,CAAC,EACH,CAAC,CACH,CAAC,cAGNrB,IAAA,QAAKgE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,cAChC/D,KAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjE,IAAA,OAAAiE,QAAA,CAAI,0BAAI,CAAI,CAAC,cACbjE,IAAA,WACEgE,SAAS,sBAAAG,MAAA,CAAuBrD,SAAS,CAAG,UAAU,CAAG,EAAE,CAAG,CAC9DoD,OAAO,CAAEhB,eAAgB,CAAAe,QAAA,CAExBnD,SAAS,CAAG,MAAM,CAAG,MAAM,CACtB,CAAC,EACN,CAAC,CACH,CAAC,cAGNZ,KAAA,QAAK8D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/D,KAAA,OAAA+D,QAAA,EAAI,4BAAM,CAAC1C,UAAU,CAACmB,MAAM,CAAC,UAAG,EAAI,CAAC,cACrC1C,IAAA,QAAKgE,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAC7B1C,UAAU,CAAC6B,GAAG,CAAC,CAACxB,SAAS,CAAED,KAAK,gBAC/BzB,KAAA,UAAmB8D,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eACzDjE,IAAA,UACEoE,IAAI,CAAC,UAAU,CACfC,OAAO,CAAE5D,kBAAkB,CAACqB,GAAG,CAACF,SAAS,CAACK,IAAI,CAAE,CAChDqC,QAAQ,CAAEA,CAAA,GAAMzB,qBAAqB,CAACjB,SAAS,CAACK,IAAI,CAAE,CACvD,CAAC,cACF/B,KAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/D,KAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BjE,IAAA,SAAMgE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAErC,SAAS,CAACK,IAAI,CAAO,CAAC,cACxD/B,KAAA,SAAM8D,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,GAAC,CAACrC,SAAS,CAACM,KAAK,CAAC,SAAE,EAAM,CAAC,EAC1D,CAAC,cACNhC,KAAA,QAAK8D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC/D,KAAA,SAAM8D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,gBAAI,CAACrC,SAAS,CAACS,MAAM,EAAO,CAAC,CAC/DT,SAAS,CAACW,MAAM,eACfrC,KAAA,SAAM8D,SAAS,CAAC,kBAAkB,CAAAC,QAAA,EAAC,gBAAI,CAACrC,SAAS,CAACW,MAAM,EAAO,CAChE,EACE,CAAC,EACH,CAAC,GAjBIZ,KAkBL,CACR,CAAC,CACC,CAAC,EACH,CAAC,cAGNzB,KAAA,QAAK8D,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCjE,IAAA,OAAAiE,QAAA,CAAI,0BAAI,CAAI,CAAC,cACb/D,KAAA,QAAK8D,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/D,KAAA,UAAO8D,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC3BjE,IAAA,UACEoE,IAAI,CAAC,OAAO,CACZnC,IAAI,CAAC,QAAQ,CACbsC,KAAK,CAAC,OAAO,CACbF,OAAO,CAAEzD,cAAc,GAAK,OAAQ,CACpC0D,QAAQ,CAAGE,CAAC,EAAK3D,iBAAiB,CAAC2D,CAAC,CAACnC,MAAM,CAACkC,KAAK,CAAE,CACpD,CAAC,cACFvE,IAAA,SAAAiE,QAAA,CAAM,2BAAe,CAAM,CAAC,EACvB,CAAC,cACR/D,KAAA,UAAO8D,SAAS,CAAC,YAAY,CAAAC,QAAA,eAC3BjE,IAAA,UACEoE,IAAI,CAAC,OAAO,CACZnC,IAAI,CAAC,QAAQ,CACbsC,KAAK,CAAC,KAAK,CACXF,OAAO,CAAEzD,cAAc,GAAK,KAAM,CAClC0D,QAAQ,CAAGE,CAAC,EAAK3D,iBAAiB,CAAC2D,CAAC,CAACnC,MAAM,CAACkC,KAAK,CAAE,CACpD,CAAC,cACFvE,IAAA,SAAAiE,QAAA,CAAM,wBAAY,CAAM,CAAC,EACpB,CAAC,EACL,CAAC,EACH,CAAC,cAGNjE,IAAA,QAAKgE,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B/D,KAAA,QAAK8D,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBjE,IAAA,OAAAiE,QAAA,CAAI,mDAAS,CAAI,CAAC,cAClB/D,KAAA,OAAA+D,QAAA,eACEjE,IAAA,OAAAiE,QAAA,CAAI,+EAAiB,CAAI,CAAC,cAC1BjE,IAAA,OAAAiE,QAAA,CAAI,0MAAmC,CAAI,CAAC,cAC5CjE,IAAA,OAAAiE,QAAA,CAAI,2HAAqB,CAAI,CAAC,cAC9BjE,IAAA,OAAAiE,QAAA,CAAI,iFAAc,CAAI,CAAC,EACrB,CAAC,EACF,CAAC,CACH,CAAC,EACH,CAAC,cAEN/D,KAAA,QAAK8D,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BjE,IAAA,WACEgE,SAAS,CAAC,eAAe,CACzBE,OAAO,CAAE3D,OAAQ,CACjBkE,QAAQ,CAAEjE,OAAQ,CAAAyD,QAAA,CACnB,cAED,CAAQ,CAAC,cACTjE,IAAA,WACEgE,SAAS,CAAC,iBAAiB,CAC3BE,OAAO,CAAEX,mBAAoB,CAC7BkB,QAAQ,CAAEjE,OAAO,EAAIC,kBAAkB,CAAC+C,IAAI,GAAK,CAAE,CAAAS,QAAA,CAElDzD,OAAO,CAAG,QAAQ,oCAAA2D,MAAA,CAAanD,UAAU,CAACG,aAAa,KAAG,CACrD,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}