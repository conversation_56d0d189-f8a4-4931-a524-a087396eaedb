import * as XLSX from 'xlsx';

class DownloadService {
  constructor() {
    this.baseURL = '/api';
  }

  // 处理选择性下载
  async handleSelectiveDownload(selectionData) {
    const { selectedData, format, statistics } = selectionData;
    
    try {
      switch (format) {
        case 'excel':
          return await this.generateExcel(selectedData, statistics);
        case 'pdf':
          return await this.generatePDF(selectedData, statistics);
        case 'csv':
          return await this.generateCSV(selectedData, statistics);
        default:
          throw new Error(`不支持的格式: ${format}`);
      }
    } catch (error) {
      console.error('下载失败:', error);
      throw error;
    }
  }

  // 生成Excel文件
  async generateExcel(selectedData, statistics) {
    try {
      // 创建新的工作簿
      const workbook = XLSX.utils.book_new();

      // 创建统一的工作表，包含所有分类数据
      this.createUnifiedWorksheet(workbook, selectedData, statistics);

      // 创建汇总页
      this.createSummarySheet(workbook, selectedData, statistics);

      // 生成并下载文件
      const fileName = `工作目标选择导出_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      return {
        success: true,
        fileName,
        message: `Excel文件已生成: ${fileName}`
      };
    } catch (error) {
      console.error('Excel生成失败:', error);
      throw error;
    }
  }

  // 创建工作表
  createWorksheet(workbook, categoryData, category) {
    // 准备表头
    const headers = ['序号', '指标', '目标值', '权重（分）', '考核标准', '指标分类', '责任人'];
    
    // 准备数据行
    const rows = [headers];
    
    categoryData.forEach((item, index) => {
      const data = item.data;
      const row = [
        this.formatValue(data.序号),
        this.formatValue(data.指标),
        this.formatValue(data.目标值),
        this.formatValue(data.权重),
        this.formatValue(data.考核标准),
        this.formatValue(data.指标分类),
        this.formatValue(data.责任人)
      ];
      rows.push(row);
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(rows);
    
    // 设置列宽
    const colWidths = [
      { wch: 8 },   // 序号
      { wch: 30 },  // 指标
      { wch: 20 },  // 目标值
      { wch: 12 },  // 权重
      { wch: 40 },  // 考核标准
      { wch: 15 },  // 指标分类
      { wch: 15 }   // 责任人
    ];
    worksheet['!cols'] = colWidths;

    // 设置表头样式
    this.setHeaderStyle(worksheet, headers.length);
    
    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, category.sheetName);
  }

  // 创建统一工作表（包含所有分类数据）
  createUnifiedWorksheet(workbook, selectedData, statistics) {
    // 准备表头
    const headers = ['分类', '序号', '指标', '目标值', '权重（分）', '考核标准', '指标分类', '责任人'];

    // 准备数据行
    const rows = [headers];

    // 定义分类信息
    const categories = [
      { key: 'keyIndicators', title: '关键指标' },
      { key: 'qualityIndicators', title: '质量指标' },
      { key: 'keyWork', title: '重点工作' }
    ];

    // 按分类组织数据
    categories.forEach(category => {
      const categoryData = selectedData[category.key];
      if (categoryData && categoryData.length > 0) {
        // 添加分类标题行
        rows.push([
          `=== ${category.title} ===`,
          '', '', '', '', '', '', ''
        ]);

        // 添加该分类的数据行
        categoryData.forEach((item, index) => {
          const data = item.data;
          const row = [
            category.title,
            this.formatValue(data.序号),
            this.formatValue(data.指标),
            this.formatValue(data.目标值),
            this.formatValue(data.权重),
            this.formatValue(data.考核标准),
            this.formatValue(data.指标分类),
            this.formatValue(data.责任人)
          ];
          rows.push(row);
        });

        // 添加空行分隔
        rows.push(['', '', '', '', '', '', '', '']);
      }
    });

    // 添加导出信息
    rows.push(['导出时间: ' + this.formatDateTime(new Date()), '', '', '', '', '', '', '']);
    rows.push([`总选择项目: ${statistics.totalItems}`, '', '', '', '', '', '', '']);
    rows.push([`总权重: ${statistics.totalWeight}`, '', '', '', '', '', '', '']);

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(rows);

    // 设置列宽
    const colWidths = [
      { wch: 15 },  // 分类
      { wch: 8 },   // 序号
      { wch: 30 },  // 指标
      { wch: 20 },  // 目标值
      { wch: 12 },  // 权重
      { wch: 40 },  // 考核标准
      { wch: 15 },  // 指标分类
      { wch: 15 }   // 责任人
    ];
    worksheet['!cols'] = colWidths;

    // 设置样式
    this.setUnifiedWorksheetStyle(worksheet, rows.length, headers.length);

    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '工作目标数据');
  }

  // 创建汇总页
  createSummarySheet(workbook, selectedData, statistics) {
    const summaryData = [
      ['工作目标导出汇总', '', '', ''],
      ['导出时间', this.formatDateTime(new Date()), '', ''],
      ['', '', '', ''],
      ['分类', '选择项目数', '权重合计', ''],
      ['', '', '', '']
    ];

    // 统计每个分类的数据
    const categories = [
      { key: 'keyIndicators', title: '关键指标' },
      { key: 'qualityIndicators', title: '质量指标' },
      { key: 'keyWork', title: '重点工作' }
    ];

    categories.forEach(category => {
      const categoryData = selectedData[category.key] || [];
      const count = categoryData.length;
      const weight = categoryData.reduce((sum, item) => {
        const w = item.data.权重;
        return sum + (this.parseNumber(w) || 0);
      }, 0);
      
      summaryData.push([category.title, count, weight, '']);
    });

    summaryData.push(['', '', '', '']);
    summaryData.push(['总计', statistics.totalItems, statistics.totalWeight, '']);

    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
    
    // 设置列宽
    summaryWorksheet['!cols'] = [
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 10 }
    ];

    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '导出汇总');
  }

  // 生成PDF文件
  async generatePDF(selectedData, statistics) {
    try {
      // 这里实现PDF生成逻辑
      // 可以使用jsPDF或者调用后端API
      console.log('PDF生成功能待实现');
      
      // 临时实现：转为JSON后提示
      const jsonData = this.prepareDataForExport(selectedData, statistics);
      const dataStr = JSON.stringify(jsonData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `工作目标选择导出_${this.formatDate(new Date())}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'PDF功能开发中，已生成JSON格式文件'
      };
    } catch (error) {
      console.error('PDF生成失败:', error);
      throw error;
    }
  }

  // 生成CSV文件
  async generateCSV(selectedData, statistics) {
    try {
      let csvContent = '\uFEFF'; // UTF-8 BOM for Excel compatibility
      
      // 为每个分类生成CSV内容
      const categories = [
        { key: 'keyIndicators', title: '关键指标' },
        { key: 'qualityIndicators', title: '质量指标' },
        { key: 'keyWork', title: '重点工作' }
      ];

      categories.forEach(category => {
        const categoryData = selectedData[category.key];
        if (categoryData && categoryData.length > 0) {
          csvContent += `\n=== ${category.title} ===\n`;
          csvContent += '序号,指标,目标值,权重（分）,考核标准,指标分类,责任人\n';
          
          categoryData.forEach(item => {
            const data = item.data;
            const row = [
              this.escapeCsvValue(data.序号),
              this.escapeCsvValue(data.指标),
              this.escapeCsvValue(data.目标值),
              this.escapeCsvValue(data.权重),
              this.escapeCsvValue(data.考核标准),
              this.escapeCsvValue(data.指标分类),
              this.escapeCsvValue(data.责任人)
            ].join(',');
            csvContent += row + '\n';
          });
          csvContent += '\n';
        }
      });

      // 添加汇总信息
      csvContent += '\n=== 导出汇总 ===\n';
      csvContent += `导出时间,${this.formatDateTime(new Date())}\n`;
      csvContent += `总选择项目,${statistics.totalItems}\n`;
      csvContent += `总权重,${statistics.totalWeight}\n`;

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `工作目标选择导出_${this.formatDate(new Date())}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'CSV文件已生成并下载'
      };
    } catch (error) {
      console.error('CSV生成失败:', error);
      throw error;
    }
  }

  // 工具方法：格式化值
  formatValue(value) {
    if (value === null || value === undefined || value === '') {
      return '';
    }
    
    if (typeof value === 'object') {
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    
    return String(value);
  }

  // 工具方法：解析数字
  parseNumber(value) {
    if (value === null || value === undefined || value === '') return 0;
    const num = typeof value === 'number' ? value : Number(value);
    return isNaN(num) ? 0 : num;
  }

  // 工具方法：CSV值转义
  escapeCsvValue(value) {
    const formattedValue = this.formatValue(value);
    if (formattedValue.includes(',') || formattedValue.includes('"') || formattedValue.includes('\n')) {
      return `"${formattedValue.replace(/"/g, '""')}"`;
    }
    return formattedValue;
  }

  // 工具方法：设置表头样式
  setHeaderStyle(worksheet, colCount) {
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (!worksheet[cellRef]) continue;

      worksheet[cellRef].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "20FF4D" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }
  }

  // 工具方法：设置统一工作表样式
  setUnifiedWorksheetStyle(worksheet, rowCount, colCount) {
    // 设置表头样式
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (!worksheet[cellRef]) continue;

      worksheet[cellRef].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "20FF4D" } },
        alignment: { horizontal: "center", vertical: "center" }
      };
    }

    // 设置分类标题行样式
    for (let r = 1; r < rowCount; r++) {
      const firstCellRef = XLSX.utils.encode_cell({ r: r, c: 0 });
      if (!worksheet[firstCellRef]) continue;

      const cellValue = worksheet[firstCellRef].v;
      if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('===')) {
        // 分类标题行样式
        for (let c = 0; c < colCount; c++) {
          const cellRef = XLSX.utils.encode_cell({ r: r, c: c });
          if (!worksheet[cellRef]) continue;

          worksheet[cellRef].s = {
            font: { bold: true, color: { rgb: "000000" } },
            fill: { fgColor: { rgb: "FFE066" } },
            alignment: { horizontal: "center", vertical: "center" }
          };
        }
      }
    }
  }

  // 工具方法：格式化日期
  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  // 工具方法：格式化日期时间
  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // 准备导出数据
  prepareDataForExport(selectedData, statistics) {
    return {
      exportInfo: {
        timestamp: new Date().toISOString(),
        totalItems: statistics.totalItems,
        totalWeight: statistics.totalWeight
      },
      data: selectedData
    };
  }
}

export default new DownloadService(); 