{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'../styles/PersonalSettings.css';import'../../styles/themes.css';import authService from'../services/authService';import{t,setLanguage,getCurrentLanguage,initLanguage}from'../../utils/i18n';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PersonalSettings=_ref=>{let{onNavigate,currentUser,onUserUpdate}=_ref;const[formData,setFormData]=useState({username:'',email:'',currentPassword:'',newPassword:'',confirmPassword:'',role:'',isActive:true,lastLogin:'',createdAt:''});// 偏好设置状态\nconst[preferences,setPreferences]=useState({theme:'dark',language:'zh-CN',emailNotifications:true,desktopNotifications:false});const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[activeTab,setActiveTab]=useState('profile');// profile, security, preferences\nconst[currentLang,setCurrentLang]=useState('zh-CN');// 当前语言状态\nconst[forceUpdate,setForceUpdate]=useState(0);// 强制重新渲染\nuseEffect(()=>{if(currentUser){setFormData({username:currentUser.username||'',email:currentUser.email||'',currentPassword:'',newPassword:'',confirmPassword:'',role:currentUser.role||'',isActive:currentUser.isActive!==false,lastLogin:currentUser.lastLogin||'',createdAt:currentUser.createdAt||''});}// 初始化语言设置\ninitLanguage();setCurrentLang(getCurrentLanguage());// 加载偏好设置\nloadPreferences();},[currentUser]);const loadPreferences=()=>{try{const savedPreferences=localStorage.getItem('userPreferences');if(savedPreferences){const parsed=JSON.parse(savedPreferences);const newPreferences=_objectSpread(_objectSpread({},preferences),parsed);setPreferences(newPreferences);// 应用已保存的设置\napplyTheme(newPreferences.theme);applyLanguage(newPreferences.language);}}catch(error){console.error('加载偏好设置失败:',error);}};const savePreferences=newPreferences=>{try{localStorage.setItem('userPreferences',JSON.stringify(newPreferences));setPreferences(newPreferences);setSuccess('偏好设置已保存');// 应用主题设置\napplyTheme(newPreferences.theme);// 应用语言设置\napplyLanguage(newPreferences.language);}catch(error){console.error('保存偏好设置失败:',error);setError('保存偏好设置失败');}};const applyTheme=theme=>{const body=document.body;body.classList.remove('theme-dark','theme-light','theme-auto');if(theme==='auto'){// 跟随系统主题\nconst prefersDark=window.matchMedia('(prefers-color-scheme: dark)').matches;body.classList.add(prefersDark?'theme-dark':'theme-light');}else{body.classList.add(\"theme-\".concat(theme));}};const applyLanguage=language=>{// 实现完整的语言切换逻辑\nconst success=setLanguage(language);if(success){setCurrentLang(language);console.log('语言设置已切换到:',language);// 强制重新渲染组件以应用新语言\nsetForceUpdate(prev=>prev+1);// 延迟显示成功消息，确保使用新语言\nsetTimeout(()=>{setSuccess(t('saveSuccess'));setTimeout(()=>setSuccess(''),2000);},100);}else{console.error('语言切换失败:',language);setError('语言切换失败');}};const handleInputChange=e=>{const{name,value,type,checked}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:type==='checkbox'?checked:value}));// 清除错误和成功消息\nif(error)setError('');if(success)setSuccess('');};const validateForm=()=>{if(!formData.username.trim()){setError('用户名不能为空');return false;}if(!formData.email.trim()){setError('邮箱不能为空');return false;}// 邮箱格式验证\nconst emailRegex=/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;if(!emailRegex.test(formData.email)){setError('请输入有效的邮箱地址');return false;}return true;};const validatePasswordForm=()=>{if(!formData.currentPassword){setError('请输入当前密码');return false;}if(!formData.newPassword){setError('请输入新密码');return false;}if(formData.newPassword.length<6){setError('新密码长度至少6位');return false;}if(formData.newPassword!==formData.confirmPassword){setError('新密码和确认密码不匹配');return false;}return true;};const handleUpdateProfile=async e=>{e.preventDefault();if(!validateForm())return;setLoading(true);setError('');setSuccess('');try{const response=await authService.authenticatedFetch(\"http://localhost:3001/api/auth/users/\".concat(currentUser.id),{method:'PUT',headers:{'Content-Type':'application/json'},body:JSON.stringify({username:formData.username,email:formData.email})});const data=await response.json();if(data.success){setSuccess('个人信息更新成功');// 更新当前用户信息\nconst updatedUser=_objectSpread(_objectSpread({},currentUser),data.user);authService.updateUserInfo(updatedUser);if(onUserUpdate){onUserUpdate(updatedUser);}}else{console.error('更新个人信息失败:',data);setError(data.message||'更新个人信息失败，请稍后重试');}}catch(error){console.error('更新个人信息失败:',error);// 根据错误类型提供更具体的错误信息\nif(error.name==='TypeError'&&error.message.includes('fetch')){setError('网络连接失败，请检查网络连接后重试');}else if(error.message.includes('401')){setError('登录已过期，请重新登录');}else if(error.message.includes('403')){setError('权限不足，无法修改个人信息');}else if(error.message.includes('404')){setError('用户不存在，请重新登录');}else{setError('更新个人信息失败，请稍后重试');}}finally{setLoading(false);}};const handleChangePassword=async e=>{e.preventDefault();if(!validatePasswordForm())return;setLoading(true);setError('');setSuccess('');try{const response=await authService.authenticatedFetch(\"http://localhost:3001/api/auth/change-password\",{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({currentPassword:formData.currentPassword,newPassword:formData.newPassword})});const data=await response.json();if(data.success){setSuccess('密码修改成功');setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{currentPassword:'',newPassword:'',confirmPassword:''}));}else{console.error('修改密码失败:',data);setError(data.message||'修改密码失败，请稍后重试');}}catch(error){console.error('修改密码失败:',error);// 根据错误类型提供更具体的错误信息\nif(error.name==='TypeError'&&error.message.includes('fetch')){setError('网络连接失败，请检查网络连接后重试');}else if(error.message.includes('401')){setError('当前密码错误，请重新输入');}else if(error.message.includes('403')){setError('权限不足，无法修改密码');}else if(error.message.includes('404')){setError('用户不存在，请重新登录');}else{setError('修改密码失败，请稍后重试');}}finally{setLoading(false);}};const formatDate=dateString=>{if(!dateString)return'未知';try{return new Date(dateString).toLocaleString('zh-CN');}catch(_unused){return'未知';}};const getRoleDisplayName=role=>{const roleNames={'super_admin':'超级管理员','manager':'管理员','director':'部长','user':'普通用户'};return roleNames[role]||role;};// 偏好设置处理函数\nconst handlePreferenceChange=(key,value)=>{const newPreferences=_objectSpread(_objectSpread({},preferences),{},{[key]:value});savePreferences(newPreferences);};const handleThemeChange=e=>{handlePreferenceChange('theme',e.target.value);};const handleLanguageChange=e=>{handlePreferenceChange('language',e.target.value);};const handleNotificationChange=key=>e=>{handlePreferenceChange(key,e.target.checked);};return/*#__PURE__*/_jsxs(\"div\",{className:\"personal-settings\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"page-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"header-content\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"header-title\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"title-icon\",children:\"\\u2699\\uFE0F\"}),/*#__PURE__*/_jsx(\"h1\",{children:t('personalSettings')})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"header-actions\",children:/*#__PURE__*/_jsxs(\"button\",{className:\"action-button back\",onClick:()=>onNavigate('home'),children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFE0\"}),t('back')]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"settings-sidebar\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"sidebar-nav\",children:[/*#__PURE__*/_jsxs(\"button\",{className:\"nav-item \".concat(activeTab==='profile'?'active':''),onClick:()=>setActiveTab('profile'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-icon\",children:\"\\uD83D\\uDC64\"}),/*#__PURE__*/_jsx(\"span\",{children:t('profile')})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"nav-item \".concat(activeTab==='security'?'active':''),onClick:()=>setActiveTab('security'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-icon\",children:\"\\uD83D\\uDD12\"}),/*#__PURE__*/_jsx(\"span\",{children:t('security')})]}),/*#__PURE__*/_jsxs(\"button\",{className:\"nav-item \".concat(activeTab==='preferences'?'active':''),onClick:()=>setActiveTab('preferences'),children:[/*#__PURE__*/_jsx(\"span\",{className:\"nav-icon\",children:\"\\uD83C\\uDFA8\"}),/*#__PURE__*/_jsx(\"span\",{children:t('preferences')})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-main\",children:[activeTab==='profile'&&/*#__PURE__*/_jsxs(\"div\",{className:\"settings-tab\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"tab-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u4E2A\\u4EBA\\u4FE1\\u606F\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u7BA1\\u7406\\u60A8\\u7684\\u57FA\\u672C\\u4FE1\\u606F\\u548C\\u8D26\\u6237\\u8BE6\\u60C5\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-error\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"alert-icon\",children:\"\\u26A0\\uFE0F\"}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-success\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"alert-icon\",children:\"\\u2705\"}),success]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleUpdateProfile,className:\"settings-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u57FA\\u672C\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",children:\"\\u7528\\u6237\\u540D\"}),/*#__PURE__*/_jsx(\"input\",{type:\"text\",id:\"username\",name:\"username\",value:formData.username,onChange:handleInputChange,placeholder:\"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"email\",children:\"\\u90AE\\u7BB1\\u5730\\u5740\"}),/*#__PURE__*/_jsx(\"input\",{type:\"email\",id:\"email\",name:\"email\",value:formData.email,onChange:handleInputChange,placeholder:\"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",required:true})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u8D26\\u6237\\u4FE1\\u606F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-grid\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u7528\\u6237\\u89D2\\u8272\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value role-badge\",children:getRoleDisplayName(formData.role)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u8D26\\u6237\\u72B6\\u6001\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value status-badge \".concat(formData.isActive?'active':'inactive'),children:formData.isActive?'正常':'已禁用'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u6700\\u540E\\u767B\\u5F55\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value\",children:formatDate(formData.lastLogin)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"info-item\",children:[/*#__PURE__*/_jsx(\"label\",{children:\"\\u6CE8\\u518C\\u65F6\\u95F4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"info-value\",children:formatDate(formData.createdAt)})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-actions\",children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading,children:loading?'保存中...':'保存更改'})})]})]}),activeTab==='security'&&/*#__PURE__*/_jsxs(\"div\",{className:\"settings-tab\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"tab-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\u5B89\\u5168\\u8BBE\\u7F6E\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u7BA1\\u7406\\u60A8\\u7684\\u5BC6\\u7801\\u548C\\u5B89\\u5168\\u9009\\u9879\"})]}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-error\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"alert-icon\",children:\"\\u26A0\\uFE0F\"}),error]}),success&&/*#__PURE__*/_jsxs(\"div\",{className:\"alert alert-success\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"alert-icon\",children:\"\\u2705\"}),success]}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleChangePassword,className:\"settings-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:\"\\u4FEE\\u6539\\u5BC6\\u7801\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"currentPassword\",children:\"\\u5F53\\u524D\\u5BC6\\u7801\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"currentPassword\",name:\"currentPassword\",value:formData.currentPassword,onChange:handleInputChange,placeholder:\"\\u8BF7\\u8F93\\u5165\\u5F53\\u524D\\u5BC6\\u7801\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"newPassword\",children:\"\\u65B0\\u5BC6\\u7801\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"newPassword\",name:\"newPassword\",value:formData.newPassword,onChange:handleInputChange,placeholder:\"\\u8BF7\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\\uFF08\\u81F3\\u5C116\\u4F4D\\uFF09\",required:true,minLength:\"6\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-group\",children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"confirmPassword\",children:\"\\u786E\\u8BA4\\u65B0\\u5BC6\\u7801\"}),/*#__PURE__*/_jsx(\"input\",{type:\"password\",id:\"confirmPassword\",name:\"confirmPassword\",value:formData.confirmPassword,onChange:handleInputChange,placeholder:\"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u65B0\\u5BC6\\u7801\",required:true})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"form-actions\",children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",className:\"btn btn-primary\",disabled:loading,children:loading?'修改中...':'修改密码'})})]})]}),activeTab==='preferences'&&/*#__PURE__*/_jsxs(\"div\",{className:\"settings-tab\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"tab-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:t('preferencesSettings')}),/*#__PURE__*/_jsx(\"p\",{children:t('preferencesSettingsDesc')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"settings-form\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:t('interfaceSettings')}),/*#__PURE__*/_jsxs(\"div\",{className:\"preference-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preference-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:t('themeMode')}),/*#__PURE__*/_jsx(\"p\",{children:t('themeDesc')})]}),/*#__PURE__*/_jsx(\"div\",{className:\"preference-control\",children:/*#__PURE__*/_jsxs(\"select\",{className:\"form-select\",value:preferences.theme,onChange:handleThemeChange,children:[/*#__PURE__*/_jsx(\"option\",{value:\"dark\",children:t('darkMode')}),/*#__PURE__*/_jsx(\"option\",{value:\"light\",children:t('lightMode')}),/*#__PURE__*/_jsx(\"option\",{value:\"auto\",children:t('autoMode')})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"preference-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preference-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:t('languageSettings')}),/*#__PURE__*/_jsx(\"p\",{children:t('languageDesc')})]}),/*#__PURE__*/_jsx(\"div\",{className:\"preference-control\",children:/*#__PURE__*/_jsxs(\"select\",{className:\"form-select\",value:preferences.language,onChange:handleLanguageChange,children:[/*#__PURE__*/_jsx(\"option\",{value:\"zh-CN\",children:t('simplifiedChinese')}),/*#__PURE__*/_jsx(\"option\",{value:\"en-US\",children:t('english')})]})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"form-section\",children:[/*#__PURE__*/_jsx(\"h3\",{children:t('notificationSettings')}),/*#__PURE__*/_jsxs(\"div\",{className:\"preference-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preference-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:t('emailNotifications')}),/*#__PURE__*/_jsx(\"p\",{children:t('emailNotificationsDesc')})]}),/*#__PURE__*/_jsx(\"div\",{className:\"preference-control\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:preferences.emailNotifications,onChange:handleNotificationChange('emailNotifications')}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"preference-item\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"preference-info\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u684C\\u9762\\u901A\\u77E5\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u5728\\u684C\\u9762\\u663E\\u793A\\u7CFB\\u7EDF\\u901A\\u77E5\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"preference-control\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"switch\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:preferences.desktopNotifications,onChange:handleNotificationChange('desktopNotifications')}),/*#__PURE__*/_jsx(\"span\",{className:\"slider\"})]})})]})]})]})]})]})]})]});};export default PersonalSettings;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "authService", "t", "setLanguage", "getCurrentLanguage", "initLanguage", "jsx", "_jsx", "jsxs", "_jsxs", "PersonalSettings", "_ref", "onNavigate", "currentUser", "onUserUpdate", "formData", "setFormData", "username", "email", "currentPassword", "newPassword", "confirmPassword", "role", "isActive", "lastLogin", "createdAt", "preferences", "setPreferences", "theme", "language", "emailNotifications", "desktopNotifications", "loading", "setLoading", "error", "setError", "success", "setSuccess", "activeTab", "setActiveTab", "currentLang", "setCurrentLang", "forceUpdate", "setForceUpdate", "loadPreferences", "savedPreferences", "localStorage", "getItem", "parsed", "JSON", "parse", "newPreferences", "_objectSpread", "applyTheme", "applyLanguage", "console", "savePreferences", "setItem", "stringify", "body", "document", "classList", "remove", "prefersDark", "window", "matchMedia", "matches", "add", "concat", "log", "prev", "setTimeout", "handleInputChange", "e", "name", "value", "type", "checked", "target", "validateForm", "trim", "emailRegex", "test", "validatePasswordForm", "length", "handleUpdateProfile", "preventDefault", "response", "authenticatedFetch", "id", "method", "headers", "data", "json", "updatedUser", "user", "updateUserInfo", "message", "includes", "handleChangePassword", "formatDate", "dateString", "Date", "toLocaleString", "_unused", "getRoleDisplayName", "roleNames", "handlePreferenceChange", "key", "handleThemeChange", "handleLanguageChange", "handleNotificationChange", "className", "children", "onClick", "onSubmit", "htmlFor", "onChange", "placeholder", "required", "disabled", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/pages/PersonalSettings.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/PersonalSettings.css';\r\nimport '../../styles/themes.css';\r\nimport authService from '../services/authService';\r\nimport { t, setLanguage, getCurrentLanguage, initLanguage } from '../../utils/i18n';\r\n\r\nconst PersonalSettings = ({ onNavigate, currentUser, onUserUpdate }) => {\r\n  const [formData, setFormData] = useState({\r\n    username: '',\r\n    email: '',\r\n    currentPassword: '',\r\n    newPassword: '',\r\n    confirmPassword: '',\r\n    role: '',\r\n    isActive: true,\r\n    lastLogin: '',\r\n    createdAt: ''\r\n  });\r\n\r\n  // 偏好设置状态\r\n  const [preferences, setPreferences] = useState({\r\n    theme: 'dark',\r\n    language: 'zh-CN',\r\n    emailNotifications: true,\r\n    desktopNotifications: false\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState('');\r\n  const [success, setSuccess] = useState('');\r\n  const [activeTab, setActiveTab] = useState('profile'); // profile, security, preferences\r\n  const [currentLang, setCurrentLang] = useState('zh-CN'); // 当前语言状态\r\n  const [forceUpdate, setForceUpdate] = useState(0); // 强制重新渲染\r\n\r\n  useEffect(() => {\r\n    if (currentUser) {\r\n      setFormData({\r\n        username: currentUser.username || '',\r\n        email: currentUser.email || '',\r\n        currentPassword: '',\r\n        newPassword: '',\r\n        confirmPassword: '',\r\n        role: currentUser.role || '',\r\n        isActive: currentUser.isActive !== false,\r\n        lastLogin: currentUser.lastLogin || '',\r\n        createdAt: currentUser.createdAt || ''\r\n      });\r\n    }\r\n\r\n    // 初始化语言设置\r\n    initLanguage();\r\n    setCurrentLang(getCurrentLanguage());\r\n\r\n    // 加载偏好设置\r\n    loadPreferences();\r\n  }, [currentUser]);\r\n\r\n  const loadPreferences = () => {\r\n    try {\r\n      const savedPreferences = localStorage.getItem('userPreferences');\r\n      if (savedPreferences) {\r\n        const parsed = JSON.parse(savedPreferences);\r\n        const newPreferences = {\r\n          ...preferences,\r\n          ...parsed\r\n        };\r\n        setPreferences(newPreferences);\r\n\r\n        // 应用已保存的设置\r\n        applyTheme(newPreferences.theme);\r\n        applyLanguage(newPreferences.language);\r\n      }\r\n    } catch (error) {\r\n      console.error('加载偏好设置失败:', error);\r\n    }\r\n  };\r\n\r\n  const savePreferences = (newPreferences) => {\r\n    try {\r\n      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));\r\n      setPreferences(newPreferences);\r\n      setSuccess('偏好设置已保存');\r\n\r\n      // 应用主题设置\r\n      applyTheme(newPreferences.theme);\r\n\r\n      // 应用语言设置\r\n      applyLanguage(newPreferences.language);\r\n\r\n    } catch (error) {\r\n      console.error('保存偏好设置失败:', error);\r\n      setError('保存偏好设置失败');\r\n    }\r\n  };\r\n\r\n  const applyTheme = (theme) => {\r\n    const body = document.body;\r\n    body.classList.remove('theme-dark', 'theme-light', 'theme-auto');\r\n\r\n    if (theme === 'auto') {\r\n      // 跟随系统主题\r\n      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\r\n      body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');\r\n    } else {\r\n      body.classList.add(`theme-${theme}`);\r\n    }\r\n  };\r\n\r\n  const applyLanguage = (language) => {\r\n    // 实现完整的语言切换逻辑\r\n    const success = setLanguage(language);\r\n    if (success) {\r\n      setCurrentLang(language);\r\n      console.log('语言设置已切换到:', language);\r\n      // 强制重新渲染组件以应用新语言\r\n      setForceUpdate(prev => prev + 1);\r\n      // 延迟显示成功消息，确保使用新语言\r\n      setTimeout(() => {\r\n        setSuccess(t('saveSuccess'));\r\n        setTimeout(() => setSuccess(''), 2000);\r\n      }, 100);\r\n    } else {\r\n      console.error('语言切换失败:', language);\r\n      setError('语言切换失败');\r\n    }\r\n  };\r\n\r\n  const handleInputChange = (e) => {\r\n    const { name, value, type, checked } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: type === 'checkbox' ? checked : value\r\n    }));\r\n\r\n    // 清除错误和成功消息\r\n    if (error) setError('');\r\n    if (success) setSuccess('');\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!formData.username.trim()) {\r\n      setError('用户名不能为空');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.email.trim()) {\r\n      setError('邮箱不能为空');\r\n      return false;\r\n    }\r\n\r\n    // 邮箱格式验证\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    if (!emailRegex.test(formData.email)) {\r\n      setError('请输入有效的邮箱地址');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const validatePasswordForm = () => {\r\n    if (!formData.currentPassword) {\r\n      setError('请输入当前密码');\r\n      return false;\r\n    }\r\n\r\n    if (!formData.newPassword) {\r\n      setError('请输入新密码');\r\n      return false;\r\n    }\r\n\r\n    if (formData.newPassword.length < 6) {\r\n      setError('新密码长度至少6位');\r\n      return false;\r\n    }\r\n\r\n    if (formData.newPassword !== formData.confirmPassword) {\r\n      setError('新密码和确认密码不匹配');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleUpdateProfile = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) return;\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${currentUser.id}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          username: formData.username,\r\n          email: formData.email\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setSuccess('个人信息更新成功');\r\n        \r\n        // 更新当前用户信息\r\n        const updatedUser = { ...currentUser, ...data.user };\r\n        authService.updateUserInfo(updatedUser);\r\n        \r\n        if (onUserUpdate) {\r\n          onUserUpdate(updatedUser);\r\n        }\r\n      } else {\r\n        console.error('更新个人信息失败:', data);\r\n        setError(data.message || '更新个人信息失败，请稍后重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('更新个人信息失败:', error);\r\n      \r\n      // 根据错误类型提供更具体的错误信息\r\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n        setError('网络连接失败，请检查网络连接后重试');\r\n      } else if (error.message.includes('401')) {\r\n        setError('登录已过期，请重新登录');\r\n      } else if (error.message.includes('403')) {\r\n        setError('权限不足，无法修改个人信息');\r\n      } else if (error.message.includes('404')) {\r\n        setError('用户不存在，请重新登录');\r\n      } else {\r\n        setError('更新个人信息失败，请稍后重试');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChangePassword = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validatePasswordForm()) return;\r\n\r\n    setLoading(true);\r\n    setError('');\r\n    setSuccess('');\r\n\r\n    try {\r\n      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/change-password`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          currentPassword: formData.currentPassword,\r\n          newPassword: formData.newPassword\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (data.success) {\r\n        setSuccess('密码修改成功');\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          currentPassword: '',\r\n          newPassword: '',\r\n          confirmPassword: ''\r\n        }));\r\n      } else {\r\n        console.error('修改密码失败:', data);\r\n        setError(data.message || '修改密码失败，请稍后重试');\r\n      }\r\n    } catch (error) {\r\n      console.error('修改密码失败:', error);\r\n      \r\n      // 根据错误类型提供更具体的错误信息\r\n      if (error.name === 'TypeError' && error.message.includes('fetch')) {\r\n        setError('网络连接失败，请检查网络连接后重试');\r\n      } else if (error.message.includes('401')) {\r\n        setError('当前密码错误，请重新输入');\r\n      } else if (error.message.includes('403')) {\r\n        setError('权限不足，无法修改密码');\r\n      } else if (error.message.includes('404')) {\r\n        setError('用户不存在，请重新登录');\r\n      } else {\r\n        setError('修改密码失败，请稍后重试');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return '未知';\r\n    try {\r\n      return new Date(dateString).toLocaleString('zh-CN');\r\n    } catch {\r\n      return '未知';\r\n    }\r\n  };\r\n\r\n  const getRoleDisplayName = (role) => {\r\n    const roleNames = {\r\n      'super_admin': '超级管理员',\r\n      'manager': '管理员',\r\n      'director': '部长',\r\n      'user': '普通用户'\r\n    };\r\n    return roleNames[role] || role;\r\n  };\r\n\r\n  // 偏好设置处理函数\r\n  const handlePreferenceChange = (key, value) => {\r\n    const newPreferences = {\r\n      ...preferences,\r\n      [key]: value\r\n    };\r\n    savePreferences(newPreferences);\r\n  };\r\n\r\n  const handleThemeChange = (e) => {\r\n    handlePreferenceChange('theme', e.target.value);\r\n  };\r\n\r\n  const handleLanguageChange = (e) => {\r\n    handlePreferenceChange('language', e.target.value);\r\n  };\r\n\r\n  const handleNotificationChange = (key) => (e) => {\r\n    handlePreferenceChange(key, e.target.checked);\r\n  };\r\n\r\n  return (\r\n    <div className=\"personal-settings\">\r\n      {/* 页面头部 */}\r\n      <div className=\"page-header\">\r\n        <div className=\"header-content\">\r\n          <div className=\"header-title\">\r\n            <span className=\"title-icon\">⚙️</span>\r\n            <h1>{t('personalSettings')}</h1>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"header-actions\">\r\n          <button \r\n            className=\"action-button back\"\r\n            onClick={() => onNavigate('home')}\r\n          >\r\n            <span>🏠</span>\r\n            {t('back')}\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 主要内容区域 */}\r\n      <div className=\"settings-content\">\r\n        {/* 侧边栏导航 */}\r\n        <div className=\"settings-sidebar\">\r\n          <div className=\"sidebar-nav\">\r\n            <button\r\n              className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('profile')}\r\n            >\r\n              <span className=\"nav-icon\">👤</span>\r\n              <span>{t('profile')}</span>\r\n            </button>\r\n            <button\r\n              className={`nav-item ${activeTab === 'security' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('security')}\r\n            >\r\n              <span className=\"nav-icon\">🔒</span>\r\n              <span>{t('security')}</span>\r\n            </button>\r\n            <button\r\n              className={`nav-item ${activeTab === 'preferences' ? 'active' : ''}`}\r\n              onClick={() => setActiveTab('preferences')}\r\n            >\r\n              <span className=\"nav-icon\">🎨</span>\r\n              <span>{t('preferences')}</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* 设置内容区域 */}\r\n        <div className=\"settings-main\">\r\n          {/* 个人信息标签页 */}\r\n          {activeTab === 'profile' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>个人信息</h2>\r\n                <p>管理您的基本信息和账户详情</p>\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"alert alert-error\">\r\n                  <span className=\"alert-icon\">⚠️</span>\r\n                  {error}\r\n                </div>\r\n              )}\r\n\r\n              {success && (\r\n                <div className=\"alert alert-success\">\r\n                  <span className=\"alert-icon\">✅</span>\r\n                  {success}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleUpdateProfile} className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>基本信息</h3>\r\n                  \r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"username\">用户名</label>\r\n                    <input\r\n                      type=\"text\"\r\n                      id=\"username\"\r\n                      name=\"username\"\r\n                      value={formData.username}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入用户名\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"email\">邮箱地址</label>\r\n                    <input\r\n                      type=\"email\"\r\n                      id=\"email\"\r\n                      name=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入邮箱地址\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h3>账户信息</h3>\r\n                  \r\n                  <div className=\"info-grid\">\r\n                    <div className=\"info-item\">\r\n                      <label>用户角色</label>\r\n                      <div className=\"info-value role-badge\">\r\n                        {getRoleDisplayName(formData.role)}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>账户状态</label>\r\n                      <div className={`info-value status-badge ${formData.isActive ? 'active' : 'inactive'}`}>\r\n                        {formData.isActive ? '正常' : '已禁用'}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>最后登录</label>\r\n                      <div className=\"info-value\">\r\n                        {formatDate(formData.lastLogin)}\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"info-item\">\r\n                      <label>注册时间</label>\r\n                      <div className=\"info-value\">\r\n                        {formatDate(formData.createdAt)}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-actions\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"btn btn-primary\"\r\n                    disabled={loading}\r\n                  >\r\n                    {loading ? '保存中...' : '保存更改'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          )}\r\n\r\n          {/* 安全设置标签页 */}\r\n          {activeTab === 'security' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>安全设置</h2>\r\n                <p>管理您的密码和安全选项</p>\r\n              </div>\r\n\r\n              {error && (\r\n                <div className=\"alert alert-error\">\r\n                  <span className=\"alert-icon\">⚠️</span>\r\n                  {error}\r\n                </div>\r\n              )}\r\n\r\n              {success && (\r\n                <div className=\"alert alert-success\">\r\n                  <span className=\"alert-icon\">✅</span>\r\n                  {success}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleChangePassword} className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>修改密码</h3>\r\n                  \r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"currentPassword\">当前密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"currentPassword\"\r\n                      name=\"currentPassword\"\r\n                      value={formData.currentPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入当前密码\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"newPassword\">新密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"newPassword\"\r\n                      name=\"newPassword\"\r\n                      value={formData.newPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请输入新密码（至少6位）\"\r\n                      required\r\n                      minLength=\"6\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"form-group\">\r\n                    <label htmlFor=\"confirmPassword\">确认新密码</label>\r\n                    <input\r\n                      type=\"password\"\r\n                      id=\"confirmPassword\"\r\n                      name=\"confirmPassword\"\r\n                      value={formData.confirmPassword}\r\n                      onChange={handleInputChange}\r\n                      placeholder=\"请再次输入新密码\"\r\n                      required\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-actions\">\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"btn btn-primary\"\r\n                    disabled={loading}\r\n                  >\r\n                    {loading ? '修改中...' : '修改密码'}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          )}\r\n\r\n          {/* 偏好设置标签页 */}\r\n          {activeTab === 'preferences' && (\r\n            <div className=\"settings-tab\">\r\n              <div className=\"tab-header\">\r\n                <h2>{t('preferencesSettings')}</h2>\r\n                <p>{t('preferencesSettingsDesc')}</p>\r\n              </div>\r\n\r\n              <div className=\"settings-form\">\r\n                <div className=\"form-section\">\r\n                  <h3>{t('interfaceSettings')}</h3>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('themeMode')}</h4>\r\n                      <p>{t('themeDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <select\r\n                        className=\"form-select\"\r\n                        value={preferences.theme}\r\n                        onChange={handleThemeChange}\r\n                      >\r\n                        <option value=\"dark\">{t('darkMode')}</option>\r\n                        <option value=\"light\">{t('lightMode')}</option>\r\n                        <option value=\"auto\">{t('autoMode')}</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('languageSettings')}</h4>\r\n                      <p>{t('languageDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <select\r\n                        className=\"form-select\"\r\n                        value={preferences.language}\r\n                        onChange={handleLanguageChange}\r\n                      >\r\n                        <option value=\"zh-CN\">{t('simplifiedChinese')}</option>\r\n                        <option value=\"en-US\">{t('english')}</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"form-section\">\r\n                  <h3>{t('notificationSettings')}</h3>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>{t('emailNotifications')}</h4>\r\n                      <p>{t('emailNotificationsDesc')}</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <label className=\"switch\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={preferences.emailNotifications}\r\n                          onChange={handleNotificationChange('emailNotifications')}\r\n                        />\r\n                        <span className=\"slider\"></span>\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"preference-item\">\r\n                    <div className=\"preference-info\">\r\n                      <h4>桌面通知</h4>\r\n                      <p>在桌面显示系统通知</p>\r\n                    </div>\r\n                    <div className=\"preference-control\">\r\n                      <label className=\"switch\">\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={preferences.desktopNotifications}\r\n                          onChange={handleNotificationChange('desktopNotifications')}\r\n                        />\r\n                        <span className=\"slider\"></span>\r\n                      </label>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonalSettings;\r\n"], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,gCAAgC,CACvC,MAAO,yBAAyB,CAChC,MAAO,CAAAC,WAAW,KAAM,yBAAyB,CACjD,OAASC,CAAC,CAAEC,WAAW,CAAEC,kBAAkB,CAAEC,YAAY,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpF,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAA+C,IAA9C,CAAEC,UAAU,CAAEC,WAAW,CAAEC,YAAa,CAAC,CAAAH,IAAA,CACjE,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGjB,QAAQ,CAAC,CACvCkB,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,EAAE,CACTC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EAAE,CACnBC,IAAI,CAAE,EAAE,CACRC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,EAAE,CACbC,SAAS,CAAE,EACb,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAC,CAC7C6B,KAAK,CAAE,MAAM,CACbC,QAAQ,CAAE,OAAO,CACjBC,kBAAkB,CAAE,IAAI,CACxBC,oBAAoB,CAAE,KACxB,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmC,KAAK,CAAEC,QAAQ,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACuC,SAAS,CAAEC,YAAY,CAAC,CAAGxC,QAAQ,CAAC,SAAS,CAAC,CAAE;AACvD,KAAM,CAACyC,WAAW,CAAEC,cAAc,CAAC,CAAG1C,QAAQ,CAAC,OAAO,CAAC,CAAE;AACzD,KAAM,CAAC2C,WAAW,CAAEC,cAAc,CAAC,CAAG5C,QAAQ,CAAC,CAAC,CAAC,CAAE;AAEnDC,SAAS,CAAC,IAAM,CACd,GAAIa,WAAW,CAAE,CACfG,WAAW,CAAC,CACVC,QAAQ,CAAEJ,WAAW,CAACI,QAAQ,EAAI,EAAE,CACpCC,KAAK,CAAEL,WAAW,CAACK,KAAK,EAAI,EAAE,CAC9BC,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EAAE,CACnBC,IAAI,CAAET,WAAW,CAACS,IAAI,EAAI,EAAE,CAC5BC,QAAQ,CAAEV,WAAW,CAACU,QAAQ,GAAK,KAAK,CACxCC,SAAS,CAAEX,WAAW,CAACW,SAAS,EAAI,EAAE,CACtCC,SAAS,CAAEZ,WAAW,CAACY,SAAS,EAAI,EACtC,CAAC,CAAC,CACJ,CAEA;AACApB,YAAY,CAAC,CAAC,CACdoC,cAAc,CAACrC,kBAAkB,CAAC,CAAC,CAAC,CAEpC;AACAwC,eAAe,CAAC,CAAC,CACnB,CAAC,CAAE,CAAC/B,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAA+B,eAAe,CAAGA,CAAA,GAAM,CAC5B,GAAI,CACF,KAAM,CAAAC,gBAAgB,CAAGC,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAChE,GAAIF,gBAAgB,CAAE,CACpB,KAAM,CAAAG,MAAM,CAAGC,IAAI,CAACC,KAAK,CAACL,gBAAgB,CAAC,CAC3C,KAAM,CAAAM,cAAc,CAAAC,aAAA,CAAAA,aAAA,IACf1B,WAAW,EACXsB,MAAM,CACV,CACDrB,cAAc,CAACwB,cAAc,CAAC,CAE9B;AACAE,UAAU,CAACF,cAAc,CAACvB,KAAK,CAAC,CAChC0B,aAAa,CAACH,cAAc,CAACtB,QAAQ,CAAC,CACxC,CACF,CAAE,MAAOK,KAAK,CAAE,CACdqB,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACnC,CACF,CAAC,CAED,KAAM,CAAAsB,eAAe,CAAIL,cAAc,EAAK,CAC1C,GAAI,CACFL,YAAY,CAACW,OAAO,CAAC,iBAAiB,CAAER,IAAI,CAACS,SAAS,CAACP,cAAc,CAAC,CAAC,CACvExB,cAAc,CAACwB,cAAc,CAAC,CAC9Bd,UAAU,CAAC,SAAS,CAAC,CAErB;AACAgB,UAAU,CAACF,cAAc,CAACvB,KAAK,CAAC,CAEhC;AACA0B,aAAa,CAACH,cAAc,CAACtB,QAAQ,CAAC,CAExC,CAAE,MAAOK,KAAK,CAAE,CACdqB,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjCC,QAAQ,CAAC,UAAU,CAAC,CACtB,CACF,CAAC,CAED,KAAM,CAAAkB,UAAU,CAAIzB,KAAK,EAAK,CAC5B,KAAM,CAAA+B,IAAI,CAAGC,QAAQ,CAACD,IAAI,CAC1BA,IAAI,CAACE,SAAS,CAACC,MAAM,CAAC,YAAY,CAAE,aAAa,CAAE,YAAY,CAAC,CAEhE,GAAIlC,KAAK,GAAK,MAAM,CAAE,CACpB;AACA,KAAM,CAAAmC,WAAW,CAAGC,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO,CAC7EP,IAAI,CAACE,SAAS,CAACM,GAAG,CAACJ,WAAW,CAAG,YAAY,CAAG,aAAa,CAAC,CAChE,CAAC,IAAM,CACLJ,IAAI,CAACE,SAAS,CAACM,GAAG,UAAAC,MAAA,CAAUxC,KAAK,CAAE,CAAC,CACtC,CACF,CAAC,CAED,KAAM,CAAA0B,aAAa,CAAIzB,QAAQ,EAAK,CAClC;AACA,KAAM,CAAAO,OAAO,CAAGjC,WAAW,CAAC0B,QAAQ,CAAC,CACrC,GAAIO,OAAO,CAAE,CACXK,cAAc,CAACZ,QAAQ,CAAC,CACxB0B,OAAO,CAACc,GAAG,CAAC,WAAW,CAAExC,QAAQ,CAAC,CAClC;AACAc,cAAc,CAAC2B,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CAChC;AACAC,UAAU,CAAC,IAAM,CACflC,UAAU,CAACnC,CAAC,CAAC,aAAa,CAAC,CAAC,CAC5BqE,UAAU,CAAC,IAAMlC,UAAU,CAAC,EAAE,CAAC,CAAE,IAAI,CAAC,CACxC,CAAC,CAAE,GAAG,CAAC,CACT,CAAC,IAAM,CACLkB,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAEL,QAAQ,CAAC,CAClCM,QAAQ,CAAC,QAAQ,CAAC,CACpB,CACF,CAAC,CAED,KAAM,CAAAqC,iBAAiB,CAAIC,CAAC,EAAK,CAC/B,KAAM,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAGJ,CAAC,CAACK,MAAM,CAC/C9D,WAAW,CAACsD,IAAI,EAAAlB,aAAA,CAAAA,aAAA,IACXkB,IAAI,MACP,CAACI,IAAI,EAAGE,IAAI,GAAK,UAAU,CAAGC,OAAO,CAAGF,KAAK,EAC7C,CAAC,CAEH;AACA,GAAIzC,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,CACvB,GAAIC,OAAO,CAAEC,UAAU,CAAC,EAAE,CAAC,CAC7B,CAAC,CAED,KAAM,CAAA0C,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAChE,QAAQ,CAACE,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAAE,CAC7B7C,QAAQ,CAAC,SAAS,CAAC,CACnB,MAAO,MAAK,CACd,CAEA,GAAI,CAACpB,QAAQ,CAACG,KAAK,CAAC8D,IAAI,CAAC,CAAC,CAAE,CAC1B7C,QAAQ,CAAC,QAAQ,CAAC,CAClB,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAA8C,UAAU,CAAG,4BAA4B,CAC/C,GAAI,CAACA,UAAU,CAACC,IAAI,CAACnE,QAAQ,CAACG,KAAK,CAAC,CAAE,CACpCiB,QAAQ,CAAC,YAAY,CAAC,CACtB,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAgD,oBAAoB,CAAGA,CAAA,GAAM,CACjC,GAAI,CAACpE,QAAQ,CAACI,eAAe,CAAE,CAC7BgB,QAAQ,CAAC,SAAS,CAAC,CACnB,MAAO,MAAK,CACd,CAEA,GAAI,CAACpB,QAAQ,CAACK,WAAW,CAAE,CACzBe,QAAQ,CAAC,QAAQ,CAAC,CAClB,MAAO,MAAK,CACd,CAEA,GAAIpB,QAAQ,CAACK,WAAW,CAACgE,MAAM,CAAG,CAAC,CAAE,CACnCjD,QAAQ,CAAC,WAAW,CAAC,CACrB,MAAO,MAAK,CACd,CAEA,GAAIpB,QAAQ,CAACK,WAAW,GAAKL,QAAQ,CAACM,eAAe,CAAE,CACrDc,QAAQ,CAAC,aAAa,CAAC,CACvB,MAAO,MAAK,CACd,CAEA,MAAO,KAAI,CACb,CAAC,CAED,KAAM,CAAAkD,mBAAmB,CAAG,KAAO,CAAAZ,CAAC,EAAK,CACvCA,CAAC,CAACa,cAAc,CAAC,CAAC,CAElB,GAAI,CAACP,YAAY,CAAC,CAAC,CAAE,OAErB9C,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAkD,QAAQ,CAAG,KAAM,CAAAtF,WAAW,CAACuF,kBAAkB,yCAAApB,MAAA,CAAyCvD,WAAW,CAAC4E,EAAE,EAAI,CAC9GC,MAAM,CAAE,KAAK,CACbC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDhC,IAAI,CAAEV,IAAI,CAACS,SAAS,CAAC,CACnBzC,QAAQ,CAAEF,QAAQ,CAACE,QAAQ,CAC3BC,KAAK,CAAEH,QAAQ,CAACG,KAClB,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAA0E,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACxD,OAAO,CAAE,CAChBC,UAAU,CAAC,UAAU,CAAC,CAEtB;AACA,KAAM,CAAAyD,WAAW,CAAA1C,aAAA,CAAAA,aAAA,IAAQvC,WAAW,EAAK+E,IAAI,CAACG,IAAI,CAAE,CACpD9F,WAAW,CAAC+F,cAAc,CAACF,WAAW,CAAC,CAEvC,GAAIhF,YAAY,CAAE,CAChBA,YAAY,CAACgF,WAAW,CAAC,CAC3B,CACF,CAAC,IAAM,CACLvC,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAE0D,IAAI,CAAC,CAChCzD,QAAQ,CAACyD,IAAI,CAACK,OAAO,EAAI,gBAAgB,CAAC,CAC5C,CACF,CAAE,MAAO/D,KAAK,CAAE,CACdqB,OAAO,CAACrB,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CAEjC;AACA,GAAIA,KAAK,CAACwC,IAAI,GAAK,WAAW,EAAIxC,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CACjE/D,QAAQ,CAAC,mBAAmB,CAAC,CAC/B,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,eAAe,CAAC,CAC3B,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,IAAM,CACLA,QAAQ,CAAC,gBAAgB,CAAC,CAC5B,CACF,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAkE,oBAAoB,CAAG,KAAO,CAAA1B,CAAC,EAAK,CACxCA,CAAC,CAACa,cAAc,CAAC,CAAC,CAElB,GAAI,CAACH,oBAAoB,CAAC,CAAC,CAAE,OAE7BlD,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAkD,QAAQ,CAAG,KAAM,CAAAtF,WAAW,CAACuF,kBAAkB,kDAAmD,CACtGE,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDhC,IAAI,CAAEV,IAAI,CAACS,SAAS,CAAC,CACnBvC,eAAe,CAAEJ,QAAQ,CAACI,eAAe,CACzCC,WAAW,CAAEL,QAAQ,CAACK,WACxB,CAAC,CACH,CAAC,CAAC,CAEF,KAAM,CAAAwE,IAAI,CAAG,KAAM,CAAAL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACxD,OAAO,CAAE,CAChBC,UAAU,CAAC,QAAQ,CAAC,CACpBrB,WAAW,CAACsD,IAAI,EAAAlB,aAAA,CAAAA,aAAA,IACXkB,IAAI,MACPnD,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EAAE,EACnB,CAAC,CACL,CAAC,IAAM,CACLkC,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAE0D,IAAI,CAAC,CAC9BzD,QAAQ,CAACyD,IAAI,CAACK,OAAO,EAAI,cAAc,CAAC,CAC1C,CACF,CAAE,MAAO/D,KAAK,CAAE,CACdqB,OAAO,CAACrB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAE/B;AACA,GAAIA,KAAK,CAACwC,IAAI,GAAK,WAAW,EAAIxC,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,OAAO,CAAC,CAAE,CACjE/D,QAAQ,CAAC,mBAAmB,CAAC,CAC/B,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,cAAc,CAAC,CAC1B,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,IAAM,IAAID,KAAK,CAAC+D,OAAO,CAACC,QAAQ,CAAC,KAAK,CAAC,CAAE,CACxC/D,QAAQ,CAAC,aAAa,CAAC,CACzB,CAAC,IAAM,CACLA,QAAQ,CAAC,cAAc,CAAC,CAC1B,CACF,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAmE,UAAU,CAAIC,UAAU,EAAK,CACjC,GAAI,CAACA,UAAU,CAAE,MAAO,IAAI,CAC5B,GAAI,CACF,MAAO,IAAI,CAAAC,IAAI,CAACD,UAAU,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC,CACrD,CAAE,MAAAC,OAAA,CAAM,CACN,MAAO,IAAI,CACb,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAInF,IAAI,EAAK,CACnC,KAAM,CAAAoF,SAAS,CAAG,CAChB,aAAa,CAAE,OAAO,CACtB,SAAS,CAAE,KAAK,CAChB,UAAU,CAAE,IAAI,CAChB,MAAM,CAAE,MACV,CAAC,CACD,MAAO,CAAAA,SAAS,CAACpF,IAAI,CAAC,EAAIA,IAAI,CAChC,CAAC,CAED;AACA,KAAM,CAAAqF,sBAAsB,CAAGA,CAACC,GAAG,CAAEjC,KAAK,GAAK,CAC7C,KAAM,CAAAxB,cAAc,CAAAC,aAAA,CAAAA,aAAA,IACf1B,WAAW,MACd,CAACkF,GAAG,EAAGjC,KAAK,EACb,CACDnB,eAAe,CAACL,cAAc,CAAC,CACjC,CAAC,CAED,KAAM,CAAA0D,iBAAiB,CAAIpC,CAAC,EAAK,CAC/BkC,sBAAsB,CAAC,OAAO,CAAElC,CAAC,CAACK,MAAM,CAACH,KAAK,CAAC,CACjD,CAAC,CAED,KAAM,CAAAmC,oBAAoB,CAAIrC,CAAC,EAAK,CAClCkC,sBAAsB,CAAC,UAAU,CAAElC,CAAC,CAACK,MAAM,CAACH,KAAK,CAAC,CACpD,CAAC,CAED,KAAM,CAAAoC,wBAAwB,CAAIH,GAAG,EAAMnC,CAAC,EAAK,CAC/CkC,sBAAsB,CAACC,GAAG,CAAEnC,CAAC,CAACK,MAAM,CAACD,OAAO,CAAC,CAC/C,CAAC,CAED,mBACEpE,KAAA,QAAKuG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAEhCxG,KAAA,QAAKuG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B1G,IAAA,QAAKyG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BxG,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,SAAMyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACtC1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,EAC7B,CAAC,CACH,CAAC,cAENK,IAAA,QAAKyG,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BxG,KAAA,WACEuG,SAAS,CAAC,oBAAoB,CAC9BE,OAAO,CAAEA,CAAA,GAAMtG,UAAU,CAAC,MAAM,CAAE,CAAAqG,QAAA,eAElC1G,IAAA,SAAA0G,QAAA,CAAM,cAAE,CAAM,CAAC,CACd/G,CAAC,CAAC,MAAM,CAAC,EACJ,CAAC,CACN,CAAC,EACH,CAAC,cAGNO,KAAA,QAAKuG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAE/B1G,IAAA,QAAKyG,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BxG,KAAA,QAAKuG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BxG,KAAA,WACEuG,SAAS,aAAA5C,MAAA,CAAc9B,SAAS,GAAK,SAAS,CAAG,QAAQ,CAAG,EAAE,CAAG,CACjE4E,OAAO,CAAEA,CAAA,GAAM3E,YAAY,CAAC,SAAS,CAAE,CAAA0E,QAAA,eAEvC1G,IAAA,SAAMyG,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACpC1G,IAAA,SAAA0G,QAAA,CAAO/G,CAAC,CAAC,SAAS,CAAC,CAAO,CAAC,EACrB,CAAC,cACTO,KAAA,WACEuG,SAAS,aAAA5C,MAAA,CAAc9B,SAAS,GAAK,UAAU,CAAG,QAAQ,CAAG,EAAE,CAAG,CAClE4E,OAAO,CAAEA,CAAA,GAAM3E,YAAY,CAAC,UAAU,CAAE,CAAA0E,QAAA,eAExC1G,IAAA,SAAMyG,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACpC1G,IAAA,SAAA0G,QAAA,CAAO/G,CAAC,CAAC,UAAU,CAAC,CAAO,CAAC,EACtB,CAAC,cACTO,KAAA,WACEuG,SAAS,aAAA5C,MAAA,CAAc9B,SAAS,GAAK,aAAa,CAAG,QAAQ,CAAG,EAAE,CAAG,CACrE4E,OAAO,CAAEA,CAAA,GAAM3E,YAAY,CAAC,aAAa,CAAE,CAAA0E,QAAA,eAE3C1G,IAAA,SAAMyG,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,cACpC1G,IAAA,SAAA0G,QAAA,CAAO/G,CAAC,CAAC,aAAa,CAAC,CAAO,CAAC,EACzB,CAAC,EACN,CAAC,CACH,CAAC,cAGNO,KAAA,QAAKuG,SAAS,CAAC,eAAe,CAAAC,QAAA,EAE3B3E,SAAS,GAAK,SAAS,eACtB7B,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxG,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cACb1G,IAAA,MAAA0G,QAAA,CAAG,gFAAa,CAAG,CAAC,EACjB,CAAC,CAEL/E,KAAK,eACJzB,KAAA,QAAKuG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1G,IAAA,SAAMyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrC/E,KAAK,EACH,CACN,CAEAE,OAAO,eACN3B,KAAA,QAAKuG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1G,IAAA,SAAMyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CACpC7E,OAAO,EACL,CACN,cAED3B,KAAA,SAAM0G,QAAQ,CAAE9B,mBAAoB,CAAC2B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5DxG,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cAEbxG,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,UAAO6G,OAAO,CAAC,UAAU,CAAAH,QAAA,CAAC,oBAAG,CAAO,CAAC,cACrC1G,IAAA,UACEqE,IAAI,CAAC,MAAM,CACXa,EAAE,CAAC,UAAU,CACbf,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE5D,QAAQ,CAACE,QAAS,CACzBoG,QAAQ,CAAE7C,iBAAkB,CAC5B8C,WAAW,CAAC,sCAAQ,CACpBC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN9G,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,UAAO6G,OAAO,CAAC,OAAO,CAAAH,QAAA,CAAC,0BAAI,CAAO,CAAC,cACnC1G,IAAA,UACEqE,IAAI,CAAC,OAAO,CACZa,EAAE,CAAC,OAAO,CACVf,IAAI,CAAC,OAAO,CACZC,KAAK,CAAE5D,QAAQ,CAACG,KAAM,CACtBmG,QAAQ,CAAE7C,iBAAkB,CAC5B8C,WAAW,CAAC,4CAAS,CACrBC,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,cAEN9G,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cAEbxG,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBxG,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1G,IAAA,UAAA0G,QAAA,CAAO,0BAAI,CAAO,CAAC,cACnB1G,IAAA,QAAKyG,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CACnCR,kBAAkB,CAAC1F,QAAQ,CAACO,IAAI,CAAC,CAC/B,CAAC,EACH,CAAC,cAENb,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1G,IAAA,UAAA0G,QAAA,CAAO,0BAAI,CAAO,CAAC,cACnB1G,IAAA,QAAKyG,SAAS,4BAAA5C,MAAA,CAA6BrD,QAAQ,CAACQ,QAAQ,CAAG,QAAQ,CAAG,UAAU,CAAG,CAAA0F,QAAA,CACpFlG,QAAQ,CAACQ,QAAQ,CAAG,IAAI,CAAG,KAAK,CAC9B,CAAC,EACH,CAAC,cAENd,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1G,IAAA,UAAA0G,QAAA,CAAO,0BAAI,CAAO,CAAC,cACnB1G,IAAA,QAAKyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBb,UAAU,CAACrF,QAAQ,CAACS,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,cAENf,KAAA,QAAKuG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB1G,IAAA,UAAA0G,QAAA,CAAO,0BAAI,CAAO,CAAC,cACnB1G,IAAA,QAAKyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBb,UAAU,CAACrF,QAAQ,CAACU,SAAS,CAAC,CAC5B,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAENlB,IAAA,QAAKyG,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B1G,IAAA,WACEqE,IAAI,CAAC,QAAQ,CACboC,SAAS,CAAC,iBAAiB,CAC3BQ,QAAQ,CAAExF,OAAQ,CAAAiF,QAAA,CAEjBjF,OAAO,CAAG,QAAQ,CAAG,MAAM,CACtB,CAAC,CACN,CAAC,EACF,CAAC,EACJ,CACN,CAGAM,SAAS,GAAK,UAAU,eACvB7B,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxG,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cACb1G,IAAA,MAAA0G,QAAA,CAAG,oEAAW,CAAG,CAAC,EACf,CAAC,CAEL/E,KAAK,eACJzB,KAAA,QAAKuG,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChC1G,IAAA,SAAMyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAM,CAAC,CACrC/E,KAAK,EACH,CACN,CAEAE,OAAO,eACN3B,KAAA,QAAKuG,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eAClC1G,IAAA,SAAMyG,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,QAAC,CAAM,CAAC,CACpC7E,OAAO,EACL,CACN,cAED3B,KAAA,SAAM0G,QAAQ,CAAEhB,oBAAqB,CAACa,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC7DxG,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cAEbxG,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,UAAO6G,OAAO,CAAC,iBAAiB,CAAAH,QAAA,CAAC,0BAAI,CAAO,CAAC,cAC7C1G,IAAA,UACEqE,IAAI,CAAC,UAAU,CACfa,EAAE,CAAC,iBAAiB,CACpBf,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAE5D,QAAQ,CAACI,eAAgB,CAChCkG,QAAQ,CAAE7C,iBAAkB,CAC5B8C,WAAW,CAAC,4CAAS,CACrBC,QAAQ,MACT,CAAC,EACC,CAAC,cAEN9G,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,UAAO6G,OAAO,CAAC,aAAa,CAAAH,QAAA,CAAC,oBAAG,CAAO,CAAC,cACxC1G,IAAA,UACEqE,IAAI,CAAC,UAAU,CACfa,EAAE,CAAC,aAAa,CAChBf,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAE5D,QAAQ,CAACK,WAAY,CAC5BiG,QAAQ,CAAE7C,iBAAkB,CAC5B8C,WAAW,CAAC,qEAAc,CAC1BC,QAAQ,MACRE,SAAS,CAAC,GAAG,CACd,CAAC,EACC,CAAC,cAENhH,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,UAAO6G,OAAO,CAAC,iBAAiB,CAAAH,QAAA,CAAC,gCAAK,CAAO,CAAC,cAC9C1G,IAAA,UACEqE,IAAI,CAAC,UAAU,CACfa,EAAE,CAAC,iBAAiB,CACpBf,IAAI,CAAC,iBAAiB,CACtBC,KAAK,CAAE5D,QAAQ,CAACM,eAAgB,CAChCgG,QAAQ,CAAE7C,iBAAkB,CAC5B8C,WAAW,CAAC,kDAAU,CACtBC,QAAQ,MACT,CAAC,EACC,CAAC,EACH,CAAC,cAENhH,IAAA,QAAKyG,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B1G,IAAA,WACEqE,IAAI,CAAC,QAAQ,CACboC,SAAS,CAAC,iBAAiB,CAC3BQ,QAAQ,CAAExF,OAAQ,CAAAiF,QAAA,CAEjBjF,OAAO,CAAG,QAAQ,CAAG,MAAM,CACtB,CAAC,CACN,CAAC,EACF,CAAC,EACJ,CACN,CAGAM,SAAS,GAAK,aAAa,eAC1B7B,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BxG,KAAA,QAAKuG,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,qBAAqB,CAAC,CAAK,CAAC,cACnCK,IAAA,MAAA0G,QAAA,CAAI/G,CAAC,CAAC,yBAAyB,CAAC,CAAI,CAAC,EAClC,CAAC,cAENO,KAAA,QAAKuG,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BxG,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,mBAAmB,CAAC,CAAK,CAAC,cAEjCO,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxG,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,WAAW,CAAC,CAAK,CAAC,cACzBK,IAAA,MAAA0G,QAAA,CAAI/G,CAAC,CAAC,WAAW,CAAC,CAAI,CAAC,EACpB,CAAC,cACNK,IAAA,QAAKyG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCxG,KAAA,WACEuG,SAAS,CAAC,aAAa,CACvBrC,KAAK,CAAEjD,WAAW,CAACE,KAAM,CACzByF,QAAQ,CAAER,iBAAkB,CAAAI,QAAA,eAE5B1G,IAAA,WAAQoE,KAAK,CAAC,MAAM,CAAAsC,QAAA,CAAE/G,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,cAC7CK,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAsC,QAAA,CAAE/G,CAAC,CAAC,WAAW,CAAC,CAAS,CAAC,cAC/CK,IAAA,WAAQoE,KAAK,CAAC,MAAM,CAAAsC,QAAA,CAAE/G,CAAC,CAAC,UAAU,CAAC,CAAS,CAAC,EACvC,CAAC,CACN,CAAC,EACH,CAAC,cAENO,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxG,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,kBAAkB,CAAC,CAAK,CAAC,cAChCK,IAAA,MAAA0G,QAAA,CAAI/G,CAAC,CAAC,cAAc,CAAC,CAAI,CAAC,EACvB,CAAC,cACNK,IAAA,QAAKyG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCxG,KAAA,WACEuG,SAAS,CAAC,aAAa,CACvBrC,KAAK,CAAEjD,WAAW,CAACG,QAAS,CAC5BwF,QAAQ,CAAEP,oBAAqB,CAAAG,QAAA,eAE/B1G,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAsC,QAAA,CAAE/G,CAAC,CAAC,mBAAmB,CAAC,CAAS,CAAC,cACvDK,IAAA,WAAQoE,KAAK,CAAC,OAAO,CAAAsC,QAAA,CAAE/G,CAAC,CAAC,SAAS,CAAC,CAAS,CAAC,EACvC,CAAC,CACN,CAAC,EACH,CAAC,EACH,CAAC,cAENO,KAAA,QAAKuG,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3B1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,sBAAsB,CAAC,CAAK,CAAC,cAEpCO,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxG,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1G,IAAA,OAAA0G,QAAA,CAAK/G,CAAC,CAAC,oBAAoB,CAAC,CAAK,CAAC,cAClCK,IAAA,MAAA0G,QAAA,CAAI/G,CAAC,CAAC,wBAAwB,CAAC,CAAI,CAAC,EACjC,CAAC,cACNK,IAAA,QAAKyG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCxG,KAAA,UAAOuG,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACvB1G,IAAA,UACEqE,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEnD,WAAW,CAACI,kBAAmB,CACxCuF,QAAQ,CAAEN,wBAAwB,CAAC,oBAAoB,CAAE,CAC1D,CAAC,cACFxG,IAAA,SAAMyG,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC3B,CAAC,CACL,CAAC,EACH,CAAC,cAENvG,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BxG,KAAA,QAAKuG,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9B1G,IAAA,OAAA0G,QAAA,CAAI,0BAAI,CAAI,CAAC,cACb1G,IAAA,MAAA0G,QAAA,CAAG,wDAAS,CAAG,CAAC,EACb,CAAC,cACN1G,IAAA,QAAKyG,SAAS,CAAC,oBAAoB,CAAAC,QAAA,cACjCxG,KAAA,UAAOuG,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACvB1G,IAAA,UACEqE,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEnD,WAAW,CAACK,oBAAqB,CAC1CsF,QAAQ,CAAEN,wBAAwB,CAAC,sBAAsB,CAAE,CAC5D,CAAC,cACFxG,IAAA,SAAMyG,SAAS,CAAC,QAAQ,CAAO,CAAC,EAC3B,CAAC,CACL,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}