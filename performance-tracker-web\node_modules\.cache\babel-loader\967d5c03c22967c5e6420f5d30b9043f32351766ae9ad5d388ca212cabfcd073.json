{"ast": null, "code": "class ProjectOneService{constructor(){this.baseURL='http://localhost:3001';this.data=[];this.lastModified=null;}// 设置同步回调（复用模块一和模块二的设计）\nsetSyncCallbacks(onSuccess,onError){this.onSyncSuccess=onSuccess;this.onSyncError=onError;}// 加载1号项目责任状数据\nasync loadProjectOneData(){try{console.log('正在从API加载1号项目责任状数据...');console.log('API地址:',\"\".concat(this.baseURL,\"/api/project-one-data\"));const response=await fetch(\"\".concat(this.baseURL,\"/api/project-one-data\"));if(response.ok){const result=await response.json();console.log('API响应详情:',{success:result.success,dataLength:result.data?result.data.length:0,message:result.message});if(result.success&&result.data&&Array.isArray(result.data)){// 详细分析返回的数据\nconsole.log('📊 详细数据分析:');console.log('总数据条数:',result.data.length);// 检查是否有模拟数据特征\nlet mockCount=0;let realCount=0;result.data.forEach((item,index)=>{const problem=item['需解决的问题/提升的需求']||'';const person=item.负责人||'';const isMockData=problem.includes('测试')||problem.includes('模拟')||problem.includes('示例')||person==='张三'||person==='李四'||person==='王五';if(isMockData){mockCount++;console.log(\"\\uD83D\\uDD34 \\u7B2C\".concat(index+1,\"\\u884C [\\u6A21\\u62DF]: \\\"\").concat(problem,\"\\\" (\").concat(person,\")\"));}else{realCount++;if(realCount<=5){// 只显示前5条真实数据\nconsole.log(\"\\uD83D\\uDFE2 \\u7B2C\".concat(index+1,\"\\u884C [\\u771F\\u5B9E]: \\\"\").concat(problem,\"\\\" (\").concat(person,\")\"));}}});console.log(\"\\uD83D\\uDCC8 \\u6570\\u636E\\u7EDF\\u8BA1: \\u6A21\\u62DF\\u6570\\u636E \".concat(mockCount,\" \\u6761, \\u771F\\u5B9E\\u6570\\u636E \").concat(realCount,\" \\u6761\"));this.data=result.data;this.lastModified=new Date(result.lastModified);console.log('✅ 从Excel文件成功加载数据，条数:',this.data.length);return this.data;}else{console.error('❌ API返回的数据格式不正确:',result);throw new Error('API数据格式错误');}}else{console.error('❌ API响应失败，状态码:',response.status);throw new Error(\"API\\u54CD\\u5E94\\u5931\\u8D25: \".concat(response.status));}}catch(error){console.error('❌ 从API加载1号项目责任状数据失败:',error);// 不再使用模拟数据，直接抛出错误\nthrow error;}}// 更新数据到Excel\nasync updateData(rowIndex,field,value){try{console.log(\"\\u66F4\\u65B01\\u53F7\\u9879\\u76EE\\u8D23\\u4EFB\\u72B6\\u6570\\u636E: \\u884C\".concat(rowIndex,\", \\u5B57\\u6BB5\").concat(field,\", \\u503C\").concat(value));const response=await fetch(\"\".concat(this.baseURL,\"/api/update-project-one-excel\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({rowIndex,field,value})});if(response.ok){const result=await response.json();if(result.success){// 更新本地数据\nif(this.data[rowIndex]){this.data[rowIndex][field]=value;}// 调用成功回调\nif(this.onSyncSuccess){this.onSyncSuccess();}console.log('数据更新成功');return true;}}throw new Error('更新失败');}catch(error){console.error('更新数据失败:',error);// 调用错误回调\nif(this.onSyncError){this.onSyncError(error);}return false;}}// 获取负责人列表（用于筛选）\ngetResponsiblePersons(){const persons=new Set();this.data.forEach(item=>{if(item.负责人&&item.负责人.trim()!==''){persons.add(item.负责人.trim());}});return Array.from(persons).sort();}// 获取项目类型列表（用于筛选）\ngetProjectTypes(){const types=new Set();this.data.forEach(item=>{if(item.类型&&item.类型.trim()!==''){types.add(item.类型.trim());}});return Array.from(types).sort();}// 根据负责人筛选数据\nfilterByResponsiblePersons(selectedPersons){if(!selectedPersons||selectedPersons.length===0){return this.data;}return this.data.filter(item=>selectedPersons.includes(item.负责人));}// 获取月份列表（用于月份切换）\ngetAvailableMonths(){return['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];}// 获取当前数据\ngetData(){return this.data;}// 获取数据统计信息\ngetDataStats(){return{totalItems:this.data.length,responsiblePersons:this.getResponsiblePersons().length,lastModified:this.lastModified};}}// 创建单例实例\nconst projectOneService=new ProjectOneService();export default projectOneService;", "map": {"version": 3, "names": ["ProjectOneService", "constructor", "baseURL", "data", "lastModified", "setSyncCallbacks", "onSuccess", "onError", "onSyncSuccess", "onSyncError", "loadProjectOneData", "console", "log", "concat", "response", "fetch", "ok", "result", "json", "success", "dataLength", "length", "message", "Array", "isArray", "mockCount", "realCount", "for<PERSON>ach", "item", "index", "problem", "person", "负责人", "isMockData", "includes", "Date", "error", "Error", "status", "updateData", "rowIndex", "field", "value", "method", "headers", "body", "JSON", "stringify", "getResponsiblePersons", "persons", "Set", "trim", "add", "from", "sort", "getProjectTypes", "types", "类型", "filterByResponsiblePersons", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getAvailableMonths", "getData", "getDataStats", "totalItems", "<PERSON><PERSON><PERSON><PERSON>", "projectOneService"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块五/services/projectOneService.js"], "sourcesContent": ["class ProjectOneService {\n  constructor() {\n    this.baseURL = 'http://localhost:3001';\n    this.data = [];\n    this.lastModified = null;\n  }\n\n  // 设置同步回调（复用模块一和模块二的设计）\n  setSyncCallbacks(onSuccess, onError) {\n    this.onSyncSuccess = onSuccess;\n    this.onSyncError = onError;\n  }\n\n  // 加载1号项目责任状数据\n  async loadProjectOneData() {\n    try {\n      console.log('正在从API加载1号项目责任状数据...');\n      console.log('API地址:', `${this.baseURL}/api/project-one-data`);\n\n      const response = await fetch(`${this.baseURL}/api/project-one-data`);\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('API响应详情:', {\n          success: result.success,\n          dataLength: result.data ? result.data.length : 0,\n          message: result.message\n        });\n\n        if (result.success && result.data && Array.isArray(result.data)) {\n          // 详细分析返回的数据\n          console.log('📊 详细数据分析:');\n          console.log('总数据条数:', result.data.length);\n\n          // 检查是否有模拟数据特征\n          let mockCount = 0;\n          let realCount = 0;\n\n          result.data.forEach((item, index) => {\n            const problem = item['需解决的问题/提升的需求'] || '';\n            const person = item.负责人 || '';\n\n            const isMockData =\n              problem.includes('测试') ||\n              problem.includes('模拟') ||\n              problem.includes('示例') ||\n              person === '张三' ||\n              person === '李四' ||\n              person === '王五';\n\n            if (isMockData) {\n              mockCount++;\n              console.log(`🔴 第${index + 1}行 [模拟]: \"${problem}\" (${person})`);\n            } else {\n              realCount++;\n              if (realCount <= 5) { // 只显示前5条真实数据\n                console.log(`🟢 第${index + 1}行 [真实]: \"${problem}\" (${person})`);\n              }\n            }\n          });\n\n          console.log(`📈 数据统计: 模拟数据 ${mockCount} 条, 真实数据 ${realCount} 条`);\n\n          this.data = result.data;\n          this.lastModified = new Date(result.lastModified);\n          console.log('✅ 从Excel文件成功加载数据，条数:', this.data.length);\n          return this.data;\n        } else {\n          console.error('❌ API返回的数据格式不正确:', result);\n          throw new Error('API数据格式错误');\n        }\n      } else {\n        console.error('❌ API响应失败，状态码:', response.status);\n        throw new Error(`API响应失败: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('❌ 从API加载1号项目责任状数据失败:', error);\n      // 不再使用模拟数据，直接抛出错误\n      throw error;\n    }\n  }\n\n\n  // 更新数据到Excel\n  async updateData(rowIndex, field, value) {\n    try {\n      console.log(`更新1号项目责任状数据: 行${rowIndex}, 字段${field}, 值${value}`);\n\n      const response = await fetch(`${this.baseURL}/api/update-project-one-excel`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          rowIndex,\n          field,\n          value\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success) {\n          // 更新本地数据\n          if (this.data[rowIndex]) {\n            this.data[rowIndex][field] = value;\n          }\n\n          // 调用成功回调\n          if (this.onSyncSuccess) {\n            this.onSyncSuccess();\n          }\n\n          console.log('数据更新成功');\n          return true;\n        }\n      }\n\n      throw new Error('更新失败');\n    } catch (error) {\n      console.error('更新数据失败:', error);\n\n      // 调用错误回调\n      if (this.onSyncError) {\n        this.onSyncError(error);\n      }\n\n      return false;\n    }\n  }\n\n\n\n  // 获取负责人列表（用于筛选）\n  getResponsiblePersons() {\n    const persons = new Set();\n    this.data.forEach(item => {\n      if (item.负责人 && item.负责人.trim() !== '') {\n        persons.add(item.负责人.trim());\n      }\n    });\n    return Array.from(persons).sort();\n  }\n\n  // 获取项目类型列表（用于筛选）\n  getProjectTypes() {\n    const types = new Set();\n    this.data.forEach(item => {\n      if (item.类型 && item.类型.trim() !== '') {\n        types.add(item.类型.trim());\n      }\n    });\n    return Array.from(types).sort();\n  }\n\n  // 根据负责人筛选数据\n  filterByResponsiblePersons(selectedPersons) {\n    if (!selectedPersons || selectedPersons.length === 0) {\n      return this.data;\n    }\n    \n    return this.data.filter(item => \n      selectedPersons.includes(item.负责人)\n    );\n  }\n\n  // 获取月份列表（用于月份切换）\n  getAvailableMonths() {\n    return ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];\n  }\n\n  // 获取当前数据\n  getData() {\n    return this.data;\n  }\n\n  // 获取数据统计信息\n  getDataStats() {\n    return {\n      totalItems: this.data.length,\n      responsiblePersons: this.getResponsiblePersons().length,\n      lastModified: this.lastModified\n    };\n  }\n}\n\n// 创建单例实例\nconst projectOneService = new ProjectOneService();\nexport default projectOneService;\n"], "mappings": "AAAA,KAAM,CAAAA,iBAAkB,CACtBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,uBAAuB,CACtC,IAAI,CAACC,IAAI,CAAG,EAAE,CACd,IAAI,CAACC,YAAY,CAAG,IAAI,CAC1B,CAEA;AACAC,gBAAgBA,CAACC,SAAS,CAAEC,OAAO,CAAE,CACnC,IAAI,CAACC,aAAa,CAAGF,SAAS,CAC9B,IAAI,CAACG,WAAW,CAAGF,OAAO,CAC5B,CAEA;AACA,KAAM,CAAAG,kBAAkBA,CAAA,CAAG,CACzB,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC,CACnCD,OAAO,CAACC,GAAG,CAAC,QAAQ,IAAAC,MAAA,CAAK,IAAI,CAACX,OAAO,yBAAuB,CAAC,CAE7D,KAAM,CAAAY,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAF,MAAA,CAAI,IAAI,CAACX,OAAO,yBAAuB,CAAC,CAEpE,GAAIY,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpCP,OAAO,CAACC,GAAG,CAAC,UAAU,CAAE,CACtBO,OAAO,CAAEF,MAAM,CAACE,OAAO,CACvBC,UAAU,CAAEH,MAAM,CAACd,IAAI,CAAGc,MAAM,CAACd,IAAI,CAACkB,MAAM,CAAG,CAAC,CAChDC,OAAO,CAAEL,MAAM,CAACK,OAClB,CAAC,CAAC,CAEF,GAAIL,MAAM,CAACE,OAAO,EAAIF,MAAM,CAACd,IAAI,EAAIoB,KAAK,CAACC,OAAO,CAACP,MAAM,CAACd,IAAI,CAAC,CAAE,CAC/D;AACAQ,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CACzBD,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAEK,MAAM,CAACd,IAAI,CAACkB,MAAM,CAAC,CAEzC;AACA,GAAI,CAAAI,SAAS,CAAG,CAAC,CACjB,GAAI,CAAAC,SAAS,CAAG,CAAC,CAEjBT,MAAM,CAACd,IAAI,CAACwB,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACnC,KAAM,CAAAC,OAAO,CAAGF,IAAI,CAAC,cAAc,CAAC,EAAI,EAAE,CAC1C,KAAM,CAAAG,MAAM,CAAGH,IAAI,CAACI,GAAG,EAAI,EAAE,CAE7B,KAAM,CAAAC,UAAU,CACdH,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBJ,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBJ,OAAO,CAACI,QAAQ,CAAC,IAAI,CAAC,EACtBH,MAAM,GAAK,IAAI,EACfA,MAAM,GAAK,IAAI,EACfA,MAAM,GAAK,IAAI,CAEjB,GAAIE,UAAU,CAAE,CACdR,SAAS,EAAE,CACXd,OAAO,CAACC,GAAG,uBAAAC,MAAA,CAAQgB,KAAK,CAAG,CAAC,8BAAAhB,MAAA,CAAYiB,OAAO,SAAAjB,MAAA,CAAMkB,MAAM,KAAG,CAAC,CACjE,CAAC,IAAM,CACLL,SAAS,EAAE,CACX,GAAIA,SAAS,EAAI,CAAC,CAAE,CAAE;AACpBf,OAAO,CAACC,GAAG,uBAAAC,MAAA,CAAQgB,KAAK,CAAG,CAAC,8BAAAhB,MAAA,CAAYiB,OAAO,SAAAjB,MAAA,CAAMkB,MAAM,KAAG,CAAC,CACjE,CACF,CACF,CAAC,CAAC,CAEFpB,OAAO,CAACC,GAAG,oEAAAC,MAAA,CAAkBY,SAAS,uCAAAZ,MAAA,CAAYa,SAAS,WAAI,CAAC,CAEhE,IAAI,CAACvB,IAAI,CAAGc,MAAM,CAACd,IAAI,CACvB,IAAI,CAACC,YAAY,CAAG,GAAI,CAAA+B,IAAI,CAAClB,MAAM,CAACb,YAAY,CAAC,CACjDO,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE,IAAI,CAACT,IAAI,CAACkB,MAAM,CAAC,CACrD,MAAO,KAAI,CAAClB,IAAI,CAClB,CAAC,IAAM,CACLQ,OAAO,CAACyB,KAAK,CAAC,kBAAkB,CAAEnB,MAAM,CAAC,CACzC,KAAM,IAAI,CAAAoB,KAAK,CAAC,WAAW,CAAC,CAC9B,CACF,CAAC,IAAM,CACL1B,OAAO,CAACyB,KAAK,CAAC,gBAAgB,CAAEtB,QAAQ,CAACwB,MAAM,CAAC,CAChD,KAAM,IAAI,CAAAD,KAAK,iCAAAxB,MAAA,CAAaC,QAAQ,CAACwB,MAAM,CAAE,CAAC,CAChD,CACF,CAAE,MAAOF,KAAK,CAAE,CACdzB,OAAO,CAACyB,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5C;AACA,KAAM,CAAAA,KAAK,CACb,CACF,CAGA;AACA,KAAM,CAAAG,UAAUA,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACvC,GAAI,CACF/B,OAAO,CAACC,GAAG,yEAAAC,MAAA,CAAkB2B,QAAQ,mBAAA3B,MAAA,CAAO4B,KAAK,aAAA5B,MAAA,CAAM6B,KAAK,CAAE,CAAC,CAE/D,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAF,MAAA,CAAI,IAAI,CAACX,OAAO,kCAAiC,CAC3EyC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBP,QAAQ,CACRC,KAAK,CACLC,KACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAI5B,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACE,OAAO,CAAE,CAClB;AACA,GAAI,IAAI,CAAChB,IAAI,CAACqC,QAAQ,CAAC,CAAE,CACvB,IAAI,CAACrC,IAAI,CAACqC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CACpC,CAEA;AACA,GAAI,IAAI,CAAClC,aAAa,CAAE,CACtB,IAAI,CAACA,aAAa,CAAC,CAAC,CACtB,CAEAG,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrB,MAAO,KAAI,CACb,CACF,CAEA,KAAM,IAAI,CAAAyB,KAAK,CAAC,MAAM,CAAC,CACzB,CAAE,MAAOD,KAAK,CAAE,CACdzB,OAAO,CAACyB,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAE/B;AACA,GAAI,IAAI,CAAC3B,WAAW,CAAE,CACpB,IAAI,CAACA,WAAW,CAAC2B,KAAK,CAAC,CACzB,CAEA,MAAO,MAAK,CACd,CACF,CAIA;AACAY,qBAAqBA,CAAA,CAAG,CACtB,KAAM,CAAAC,OAAO,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzB,IAAI,CAAC/C,IAAI,CAACwB,OAAO,CAACC,IAAI,EAAI,CACxB,GAAIA,IAAI,CAACI,GAAG,EAAIJ,IAAI,CAACI,GAAG,CAACmB,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACtCF,OAAO,CAACG,GAAG,CAACxB,IAAI,CAACI,GAAG,CAACmB,IAAI,CAAC,CAAC,CAAC,CAC9B,CACF,CAAC,CAAC,CACF,MAAO,CAAA5B,KAAK,CAAC8B,IAAI,CAACJ,OAAO,CAAC,CAACK,IAAI,CAAC,CAAC,CACnC,CAEA;AACAC,eAAeA,CAAA,CAAG,CAChB,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAN,GAAG,CAAC,CAAC,CACvB,IAAI,CAAC/C,IAAI,CAACwB,OAAO,CAACC,IAAI,EAAI,CACxB,GAAIA,IAAI,CAAC6B,EAAE,EAAI7B,IAAI,CAAC6B,EAAE,CAACN,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CACpCK,KAAK,CAACJ,GAAG,CAACxB,IAAI,CAAC6B,EAAE,CAACN,IAAI,CAAC,CAAC,CAAC,CAC3B,CACF,CAAC,CAAC,CACF,MAAO,CAAA5B,KAAK,CAAC8B,IAAI,CAACG,KAAK,CAAC,CAACF,IAAI,CAAC,CAAC,CACjC,CAEA;AACAI,0BAA0BA,CAACC,eAAe,CAAE,CAC1C,GAAI,CAACA,eAAe,EAAIA,eAAe,CAACtC,MAAM,GAAK,CAAC,CAAE,CACpD,MAAO,KAAI,CAAClB,IAAI,CAClB,CAEA,MAAO,KAAI,CAACA,IAAI,CAACyD,MAAM,CAAChC,IAAI,EAC1B+B,eAAe,CAACzB,QAAQ,CAACN,IAAI,CAACI,GAAG,CACnC,CAAC,CACH,CAEA;AACA6B,kBAAkBA,CAAA,CAAG,CACnB,MAAO,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CAC9E,CAEA;AACAC,OAAOA,CAAA,CAAG,CACR,MAAO,KAAI,CAAC3D,IAAI,CAClB,CAEA;AACA4D,YAAYA,CAAA,CAAG,CACb,MAAO,CACLC,UAAU,CAAE,IAAI,CAAC7D,IAAI,CAACkB,MAAM,CAC5B4C,kBAAkB,CAAE,IAAI,CAACjB,qBAAqB,CAAC,CAAC,CAAC3B,MAAM,CACvDjB,YAAY,CAAE,IAAI,CAACA,YACrB,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAA8D,iBAAiB,CAAG,GAAI,CAAAlE,iBAAiB,CAAC,CAAC,CACjD,cAAe,CAAAkE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}