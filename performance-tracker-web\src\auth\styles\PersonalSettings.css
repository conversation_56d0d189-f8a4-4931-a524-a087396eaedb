/* 个人设置页面样式 */
.personal-settings {
  min-height: 100vh;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #e0e6ed;
  padding: 20px;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 28px;
  color: #4ecdc4;
}

.header-title h1 {
  color: #4ecdc4;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(78, 205, 196, 0.3);
}

.header-actions {
  display: flex;
  gap: 15px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.action-button.back {
  background: rgba(255, 255, 255, 0.1);
  color: #e0e6ed;
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-right: 20px; /* 向左移动20px，避免贴边显示 */
}

.action-button.back:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 主要内容区域 */
.settings-content {
  display: flex;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* 侧边栏导航 */
.settings-sidebar {
  width: 250px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  height: fit-content;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border: none;
  background: transparent;
  color: #b8c5d1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  text-align: left;
  width: 100%;
}

.nav-item:hover {
  background: rgba(78, 205, 196, 0.1);
  color: #4ecdc4;
  transform: translateX(5px);
}

.nav-item.active {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* 设置主内容区域 */
.settings-main {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-tab {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center; /* 居中显示 */
}

.tab-header h2 {
  color: #4ecdc4;
  font-size: 28px; /* 增大4号字体 */
  font-weight: 600;
  margin: 0 0 8px 0;
}

.tab-header p {
  color: #b8c5d1;
  font-size: 18px; /* 增大4号字体 */
  margin: 0;
}

/* 表单样式 */
.settings-form {
  max-width: 600px;
}

.form-section {
  margin-bottom: 40px;
  padding: 25px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section h3 {
  color: #4ecdc4;
  font-size: 22px; /* 增大4号字体 */
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(78, 205, 196, 0.2);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #e0e6ed;
  font-weight: 500;
  font-size: 18px; /* 增大4号字体 */
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #e0e6ed;
  font-size: 18px; /* 增大4号字体 */
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
  background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder {
  color: #8a9ba8;
}

/* 信息网格 */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  color: #b8c5d1;
  font-size: 16px; /* 增大4号字体 */
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-value {
  color: #e0e6ed;
  font-size: 18px; /* 增大4号字体 */
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.role-badge {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  font-weight: 600;
  text-align: center;
}

.status-badge {
  text-align: center;
  font-weight: 600;
}

.status-badge.active {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border-color: rgba(76, 175, 80, 0.3);
}

.status-badge.inactive {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border-color: rgba(244, 67, 54, 0.3);
}

/* 偏好设置项 */
.preference-item {
  display: flex;
  justify-content: center; /* 居中显示 */
  align-items: center;
  padding: 20px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
  flex-direction: column; /* 垂直排列 */
  text-align: center; /* 文本居中 */
  gap: 15px; /* 增加间距 */
}

.preference-info {
  flex: 1;
  text-align: center; /* 信息部分居中 */
}

.preference-info h4 {
  color: #e0e6ed;
  font-size: 20px; /* 增大4号字体 */
  font-weight: 600;
  margin: 0 0 5px 0;
}

.preference-info p {
  color: #b8c5d1;
  font-size: 17px; /* 增大4号字体 */
  margin: 0;
}

.preference-control {
  margin-left: 0; /* 移除左边距，因为现在是垂直布局 */
  text-align: center; /* 控件居中 */
}

.form-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #e0e6ed;
  font-size: 18px; /* 增大4号字体 */
  min-width: 150px;
}

/* 修复下拉菜单选项样式 - 确保文字清晰可见 */
.form-select option {
  background: #4ecdc4 !important; /* 蓝色背景 */
  color: #ffffff !important; /* 白色文字 */
  padding: 8px 12px !important;
  font-size: 16px !important;
}

/* 针对不同浏览器的兼容性处理 */
.form-select:focus {
  outline: none;
  border-color: #4ecdc4;
  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);
  background: rgba(78, 205, 196, 0.1);
}

/* 确保下拉菜单在所有浏览器中正常显示 */
.form-select {
  -webkit-appearance: menulist;
  -moz-appearance: menulist;
  appearance: menulist;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4ecdc4;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 按钮样式 */
.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 18px; /* 增大4号字体 */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(78, 205, 196, 0.4);
}

/* 警告和成功消息 */
.alert {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 18px; /* 增大4号字体 */
  font-weight: 500;
}

.alert-error {
  background: rgba(244, 67, 54, 0.1);
  border: 1px solid rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.alert-success {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.alert-icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-content {
    flex-direction: column;
    gap: 20px;
  }
  
  .settings-sidebar {
    width: 100%;
  }
  
  .sidebar-nav {
    flex-direction: row;
    overflow-x: auto;
    gap: 10px;
  }
  
  .nav-item {
    white-space: nowrap;
    min-width: 120px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .preference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .preference-control {
    margin-left: 0;
    width: 100%;
  }
  
  .form-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .personal-settings {
    padding: 10px;
  }
  
  .settings-main {
    padding: 20px;
  }
  
  .form-section {
    padding: 15px;
  }
}
