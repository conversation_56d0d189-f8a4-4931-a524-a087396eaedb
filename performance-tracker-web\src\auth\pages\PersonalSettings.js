import React, { useState, useEffect } from 'react';
import '../styles/PersonalSettings.css';
import '../../styles/themes.css';
import authService from '../services/authService';
import { t, setLanguage, getCurrentLanguage, initLanguage } from '../../utils/i18n';

const PersonalSettings = ({ onNavigate, currentUser, onUserUpdate }) => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    role: '',
    isActive: true,
    lastLogin: '',
    createdAt: ''
  });

  // 偏好设置状态
  const [preferences, setPreferences] = useState({
    theme: 'dark',
    language: 'zh-CN',
    emailNotifications: true,
    desktopNotifications: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('profile'); // profile, security, preferences
  const [currentLang, setCurrentLang] = useState('zh-CN'); // 当前语言状态
  const [forceUpdate, setForceUpdate] = useState(0); // 强制重新渲染

  useEffect(() => {
    if (currentUser) {
      setFormData({
        username: currentUser.username || '',
        email: currentUser.email || '',
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
        role: currentUser.role || '',
        isActive: currentUser.isActive !== false,
        lastLogin: currentUser.lastLogin || '',
        createdAt: currentUser.createdAt || ''
      });
    }

    // 初始化语言设置
    initLanguage();
    setCurrentLang(getCurrentLanguage());

    // 加载偏好设置
    loadPreferences();
  }, [currentUser]);

  const loadPreferences = () => {
    try {
      const savedPreferences = localStorage.getItem('userPreferences');
      if (savedPreferences) {
        const parsed = JSON.parse(savedPreferences);
        const newPreferences = {
          ...preferences,
          ...parsed
        };
        setPreferences(newPreferences);

        // 应用已保存的设置
        applyTheme(newPreferences.theme);
        applyLanguage(newPreferences.language);
      }
    } catch (error) {
      console.error('加载偏好设置失败:', error);
    }
  };

  const savePreferences = (newPreferences) => {
    try {
      localStorage.setItem('userPreferences', JSON.stringify(newPreferences));
      setPreferences(newPreferences);
      setSuccess('偏好设置已保存');

      // 应用主题设置
      applyTheme(newPreferences.theme);

      // 应用语言设置
      applyLanguage(newPreferences.language);

    } catch (error) {
      console.error('保存偏好设置失败:', error);
      setError('保存偏好设置失败');
    }
  };

  const applyTheme = (theme) => {
    const body = document.body;
    body.classList.remove('theme-dark', 'theme-light', 'theme-auto');

    if (theme === 'auto') {
      // 跟随系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      body.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
    } else {
      body.classList.add(`theme-${theme}`);
    }
  };

  const applyLanguage = (language) => {
    // 实现完整的语言切换逻辑
    const success = setLanguage(language);
    if (success) {
      setCurrentLang(language);
      console.log('语言设置已切换到:', language);
      // 强制重新渲染组件以应用新语言
      setForceUpdate(prev => prev + 1);
      // 延迟显示成功消息，确保使用新语言
      setTimeout(() => {
        setSuccess(t('saveSuccess'));
        setTimeout(() => setSuccess(''), 2000);
      }, 100);
    } else {
      console.error('语言切换失败:', language);
      setError('语言切换失败');
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // 清除错误和成功消息
    if (error) setError('');
    if (success) setSuccess('');
  };

  const validateForm = () => {
    if (!formData.username.trim()) {
      setError('用户名不能为空');
      return false;
    }

    if (!formData.email.trim()) {
      setError('邮箱不能为空');
      return false;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('请输入有效的邮箱地址');
      return false;
    }

    return true;
  };

  const validatePasswordForm = () => {
    if (!formData.currentPassword) {
      setError('请输入当前密码');
      return false;
    }

    if (!formData.newPassword) {
      setError('请输入新密码');
      return false;
    }

    if (formData.newPassword.length < 6) {
      setError('新密码长度至少6位');
      return false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setError('新密码和确认密码不匹配');
      return false;
    }

    return true;
  };

  const handleUpdateProfile = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/users/${currentUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username,
          email: formData.email
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('个人信息更新成功');
        
        // 更新当前用户信息
        const updatedUser = { ...currentUser, ...data.user };
        authService.updateUserInfo(updatedUser);
        
        if (onUserUpdate) {
          onUserUpdate(updatedUser);
        }
      } else {
        console.error('更新个人信息失败:', data);
        setError(data.message || '更新个人信息失败，请稍后重试');
      }
    } catch (error) {
      console.error('更新个人信息失败:', error);
      
      // 根据错误类型提供更具体的错误信息
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setError('网络连接失败，请检查网络连接后重试');
      } else if (error.message.includes('401')) {
        setError('登录已过期，请重新登录');
      } else if (error.message.includes('403')) {
        setError('权限不足，无法修改个人信息');
      } else if (error.message.includes('404')) {
        setError('用户不存在，请重新登录');
      } else {
        setError('更新个人信息失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleChangePassword = async (e) => {
    e.preventDefault();

    if (!validatePasswordForm()) return;

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await authService.authenticatedFetch(`http://localhost:3001/api/auth/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: formData.currentPassword,
          newPassword: formData.newPassword
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('密码修改成功');
        setFormData(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }));
      } else {
        console.error('修改密码失败:', data);
        setError(data.message || '修改密码失败，请稍后重试');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      
      // 根据错误类型提供更具体的错误信息
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        setError('网络连接失败，请检查网络连接后重试');
      } else if (error.message.includes('401')) {
        setError('当前密码错误，请重新输入');
      } else if (error.message.includes('403')) {
        setError('权限不足，无法修改密码');
      } else if (error.message.includes('404')) {
        setError('用户不存在，请重新登录');
      } else {
        setError('修改密码失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return '未知';
    try {
      return new Date(dateString).toLocaleString('zh-CN');
    } catch {
      return '未知';
    }
  };

  const getRoleDisplayName = (role) => {
    const roleNames = {
      'super_admin': '超级管理员',
      'manager': '管理员',
      'director': '部长',
      'user': '普通用户'
    };
    return roleNames[role] || role;
  };

  // 偏好设置处理函数
  const handlePreferenceChange = (key, value) => {
    const newPreferences = {
      ...preferences,
      [key]: value
    };
    savePreferences(newPreferences);
  };

  const handleThemeChange = (e) => {
    handlePreferenceChange('theme', e.target.value);
  };

  const handleLanguageChange = (e) => {
    handlePreferenceChange('language', e.target.value);
  };

  const handleNotificationChange = (key) => (e) => {
    handlePreferenceChange(key, e.target.checked);
  };

  return (
    <div className="personal-settings">
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-title">
            <span className="title-icon">⚙️</span>
            <h1>{t('personalSettings')}</h1>
          </div>
        </div>

        <div className="header-actions">
          <button 
            className="action-button back"
            onClick={() => onNavigate('home')}
          >
            <span>🏠</span>
            {t('back')}
          </button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="settings-content">
        {/* 侧边栏导航 */}
        <div className="settings-sidebar">
          <div className="sidebar-nav">
            <button
              className={`nav-item ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              <span className="nav-icon">👤</span>
              <span>{t('profile')}</span>
            </button>
            <button
              className={`nav-item ${activeTab === 'security' ? 'active' : ''}`}
              onClick={() => setActiveTab('security')}
            >
              <span className="nav-icon">🔒</span>
              <span>{t('security')}</span>
            </button>
            <button
              className={`nav-item ${activeTab === 'preferences' ? 'active' : ''}`}
              onClick={() => setActiveTab('preferences')}
            >
              <span className="nav-icon">🎨</span>
              <span>{t('preferences')}</span>
            </button>
          </div>
        </div>

        {/* 设置内容区域 */}
        <div className="settings-main">
          {/* 个人信息标签页 */}
          {activeTab === 'profile' && (
            <div className="settings-tab">
              <div className="tab-header">
                <h2>个人信息</h2>
                <p>管理您的基本信息和账户详情</p>
              </div>

              {error && (
                <div className="alert alert-error">
                  <span className="alert-icon">⚠️</span>
                  {error}
                </div>
              )}

              {success && (
                <div className="alert alert-success">
                  <span className="alert-icon">✅</span>
                  {success}
                </div>
              )}

              <form onSubmit={handleUpdateProfile} className="settings-form">
                <div className="form-section">
                  <h3>基本信息</h3>
                  
                  <div className="form-group">
                    <label htmlFor="username">用户名</label>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      placeholder="请输入用户名"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="email">邮箱地址</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="请输入邮箱地址"
                      required
                    />
                  </div>
                </div>

                <div className="form-section">
                  <h3>账户信息</h3>
                  
                  <div className="info-grid">
                    <div className="info-item">
                      <label>用户角色</label>
                      <div className="info-value role-badge">
                        {getRoleDisplayName(formData.role)}
                      </div>
                    </div>

                    <div className="info-item">
                      <label>账户状态</label>
                      <div className={`info-value status-badge ${formData.isActive ? 'active' : 'inactive'}`}>
                        {formData.isActive ? '正常' : '已禁用'}
                      </div>
                    </div>

                    <div className="info-item">
                      <label>最后登录</label>
                      <div className="info-value">
                        {formatDate(formData.lastLogin)}
                      </div>
                    </div>

                    <div className="info-item">
                      <label>注册时间</label>
                      <div className="info-value">
                        {formatDate(formData.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? '保存中...' : '保存更改'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* 安全设置标签页 */}
          {activeTab === 'security' && (
            <div className="settings-tab">
              <div className="tab-header">
                <h2>安全设置</h2>
                <p>管理您的密码和安全选项</p>
              </div>

              {error && (
                <div className="alert alert-error">
                  <span className="alert-icon">⚠️</span>
                  {error}
                </div>
              )}

              {success && (
                <div className="alert alert-success">
                  <span className="alert-icon">✅</span>
                  {success}
                </div>
              )}

              <form onSubmit={handleChangePassword} className="settings-form">
                <div className="form-section">
                  <h3>修改密码</h3>
                  
                  <div className="form-group">
                    <label htmlFor="currentPassword">当前密码</label>
                    <input
                      type="password"
                      id="currentPassword"
                      name="currentPassword"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      placeholder="请输入当前密码"
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="newPassword">新密码</label>
                    <input
                      type="password"
                      id="newPassword"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      placeholder="请输入新密码（至少6位）"
                      required
                      minLength="6"
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="confirmPassword">确认新密码</label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      placeholder="请再次输入新密码"
                      required
                    />
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={loading}
                  >
                    {loading ? '修改中...' : '修改密码'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* 偏好设置标签页 */}
          {activeTab === 'preferences' && (
            <div className="settings-tab">
              <div className="tab-header">
                <h2>{t('preferencesSettings')}</h2>
                <p>{t('preferencesSettingsDesc')}</p>
              </div>

              <div className="settings-form">
                <div className="form-section">
                  <h3>{t('interfaceSettings')}</h3>

                  <div className="preference-item">
                    <div className="preference-info">
                      <h4>{t('themeMode')}</h4>
                      <p>{t('themeDesc')}</p>
                    </div>
                    <div className="preference-control">
                      <select
                        className="form-select"
                        value={preferences.theme}
                        onChange={handleThemeChange}
                      >
                        <option value="dark">{t('darkMode')}</option>
                        <option value="light">{t('lightMode')}</option>
                        <option value="auto">{t('autoMode')}</option>
                      </select>
                    </div>
                  </div>

                  <div className="preference-item">
                    <div className="preference-info">
                      <h4>{t('languageSettings')}</h4>
                      <p>{t('languageDesc')}</p>
                    </div>
                    <div className="preference-control">
                      <select
                        className="form-select"
                        value={preferences.language}
                        onChange={handleLanguageChange}
                      >
                        <option value="zh-CN">{t('simplifiedChinese')}</option>
                        <option value="en-US">{t('english')}</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-section">
                  <h3>{t('notificationSettings')}</h3>

                  <div className="preference-item">
                    <div className="preference-info">
                      <h4>{t('emailNotifications')}</h4>
                      <p>{t('emailNotificationsDesc')}</p>
                    </div>
                    <div className="preference-control">
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={preferences.emailNotifications}
                          onChange={handleNotificationChange('emailNotifications')}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>

                  <div className="preference-item">
                    <div className="preference-info">
                      <h4>桌面通知</h4>
                      <p>在桌面显示系统通知</p>
                    </div>
                    <div className="preference-control">
                      <label className="switch">
                        <input
                          type="checkbox"
                          checked={preferences.desktopNotifications}
                          onChange={handleNotificationChange('desktopNotifications')}
                        />
                        <span className="slider"></span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PersonalSettings;
