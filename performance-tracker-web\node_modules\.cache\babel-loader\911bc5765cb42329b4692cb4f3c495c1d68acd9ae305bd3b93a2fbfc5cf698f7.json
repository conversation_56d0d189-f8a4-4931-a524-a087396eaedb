{"ast": null, "code": "// 角色权限配置文件\n// 定义系统中所有角色及其对应的权限\n// 权限常量定义\nexport const PERMISSIONS={// 页面访问权限\nDASHBOARD:'dashboard',PROJECT_MANAGEMENT:'project_management',FILE_LIBRARY:'file_library',USER_MANAGEMENT:'user_management',SYSTEM_SETTINGS:'system_settings',DEPARTMENT_MANAGEMENT:'department_management',PERSONAL_DASHBOARD:'personal_dashboard',PERSONAL_TASKS:'personal_tasks',PROJECT_VIEW:'project_view',TASK_MANAGEMENT:'task_management',FILE_LIBRARY_LIMITED:'file_library_limited',// 操作权限\nCREATE_USER:'create_user',DELETE_USER:'delete_user',MODIFY_USER:'modify_user',ASSIGN_ROLE:'assign_role',DATA_CRUD:'data_crud',DATA_IMPORT_EXPORT:'data_import_export',MODIFY_GLOBAL_CONFIG:'modify_global_config',CHANGE_FILE:'change_file',CREATE_PROJECT:'create_project',DELETE_PROJECT:'delete_project',MODIFY_PROJECT_STATUS:'modify_project_status',VIEW_DEPARTMENT_MEMBERS:'view_department_members',UPLOAD_FILES:'upload_files',DELETE_FILES:'delete_files',DOWNLOAD_FILES:'download_files',UPDATE_TASK_STATUS:'update_task_status',EDIT_OWN_PROJECT:'edit_own_project',CREATE_OWN_PROJECT:'create_own_project',DOWNLOAD_OWN_PROJECT:'download_own_project',VIEW_TASKS_ONLY:'view_tasks_only',MODIFY_PERSONAL_INFO:'modify_personal_info'};// 角色常量定义\nexport const ROLES={SUPER_ADMIN:'super_admin',MANAGER:'manager',DESIGNER:'designer',USER:'user'};// 角色权限映射\nexport const ROLE_PERMISSIONS={[ROLES.SUPER_ADMIN]:[// 页面权限 - 所有页面\nPERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT,PERMISSIONS.FILE_LIBRARY,PERMISSIONS.USER_MANAGEMENT,PERMISSIONS.SYSTEM_SETTINGS,PERMISSIONS.DEPARTMENT_MANAGEMENT,// 操作权限 - 所有操作\nPERMISSIONS.CREATE_USER,PERMISSIONS.DELETE_USER,PERMISSIONS.MODIFY_USER,PERMISSIONS.ASSIGN_ROLE,PERMISSIONS.DATA_CRUD,PERMISSIONS.DATA_IMPORT_EXPORT,PERMISSIONS.MODIFY_GLOBAL_CONFIG,PERMISSIONS.CHANGE_FILE,PERMISSIONS.CREATE_PROJECT,PERMISSIONS.DELETE_PROJECT,PERMISSIONS.MODIFY_PROJECT_STATUS,PERMISSIONS.VIEW_DEPARTMENT_MEMBERS,PERMISSIONS.UPLOAD_FILES,PERMISSIONS.DELETE_FILES,PERMISSIONS.DOWNLOAD_FILES,PERMISSIONS.UPDATE_TASK_STATUS,PERMISSIONS.EDIT_OWN_PROJECT,PERMISSIONS.CREATE_OWN_PROJECT,PERMISSIONS.DOWNLOAD_OWN_PROJECT,PERMISSIONS.MODIFY_PERSONAL_INFO],[ROLES.MANAGER]:[// 页面权限\nPERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT,PERMISSIONS.FILE_LIBRARY,PERMISSIONS.DEPARTMENT_MANAGEMENT,// 操作权限\nPERMISSIONS.CREATE_PROJECT,PERMISSIONS.MODIFY_PROJECT_STATUS,PERMISSIONS.VIEW_DEPARTMENT_MEMBERS,PERMISSIONS.UPLOAD_FILES,PERMISSIONS.DOWNLOAD_FILES,PERMISSIONS.DELETE_FILES,// 仅能删除自己上传的文件\nPERMISSIONS.CHANGE_FILE,PERMISSIONS.MODIFY_PERSONAL_INFO],[ROLES.DESIGNER]:[// 页面权限\nPERMISSIONS.PROJECT_VIEW,PERMISSIONS.TASK_MANAGEMENT,PERMISSIONS.FILE_LIBRARY_LIMITED,// 操作权限\nPERMISSIONS.UPDATE_TASK_STATUS,PERMISSIONS.UPLOAD_FILES,PERMISSIONS.DOWNLOAD_FILES,PERMISSIONS.EDIT_OWN_PROJECT,PERMISSIONS.CREATE_OWN_PROJECT,PERMISSIONS.DOWNLOAD_OWN_PROJECT,PERMISSIONS.MODIFY_PERSONAL_INFO],[ROLES.USER]:[// 页面权限\nPERMISSIONS.PERSONAL_DASHBOARD,PERMISSIONS.PERSONAL_TASKS,// 操作权限\nPERMISSIONS.VIEW_TASKS_ONLY,PERMISSIONS.MODIFY_PERSONAL_INFO]};// 页面路由权限映射\nexport const PAGE_PERMISSIONS={'home':[],// 首页所有人都可以访问\n'work-target':[PERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT],'work-tracking':[PERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT],'world-class':[PERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT],'monthly-kpi':[PERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT],'project-one':[PERMISSIONS.PROJECT_MANAGEMENT,PERMISSIONS.PROJECT_VIEW],'module-six':[PERMISSIONS.DASHBOARD,PERMISSIONS.PROJECT_MANAGEMENT],'department':[PERMISSIONS.DEPARTMENT_MANAGEMENT],'user-management':[PERMISSIONS.USER_MANAGEMENT],'personal-settings':[PERMISSIONS.MODIFY_PERSONAL_INFO],'system-settings':[PERMISSIONS.SYSTEM_SETTINGS]};// 检查用户是否有特定权限\nexport const hasPermission=(userRole,permission)=>{if(!userRole||!permission)return false;const rolePermissions=ROLE_PERMISSIONS[userRole]||[];return rolePermissions.includes(permission);};// 检查用户是否可以访问特定页面\nexport const canAccessPage=(userRole,pageName)=>{if(!userRole||!pageName)return false;const requiredPermissions=PAGE_PERMISSIONS[pageName]||[];// 如果页面不需要特殊权限，则允许访问\nif(requiredPermissions.length===0)return true;const userPermissions=ROLE_PERMISSIONS[userRole]||[];// 检查用户是否拥有页面所需的任一权限\nreturn requiredPermissions.some(permission=>userPermissions.includes(permission));};// 获取用户可访问的页面列表\nexport const getAccessiblePages=userRole=>{if(!userRole)return['home'];return Object.keys(PAGE_PERMISSIONS).filter(pageName=>canAccessPage(userRole,pageName));};// 角色显示名称映射\nexport const ROLE_DISPLAY_NAMES={[ROLES.SUPER_ADMIN]:'超级管理员',[ROLES.MANAGER]:'部长',[ROLES.DESIGNER]:'设计师',[ROLES.USER]:'普通用户'};// 角色描述映射\nexport const ROLE_DESCRIPTIONS={[ROLES.SUPER_ADMIN]:'拥有系统所有权限，可管理用户、配置系统',[ROLES.MANAGER]:'可管理项目、部门成员，访问数据看板',[ROLES.DESIGNER]:'可查看项目、管理任务、上传文件',[ROLES.USER]:'只能查看个人任务和基本信息'};export default{PERMISSIONS,ROLES,ROLE_PERMISSIONS,PAGE_PERMISSIONS,hasPermission,canAccessPage,getAccessiblePages,ROLE_DISPLAY_NAMES,ROLE_DESCRIPTIONS};", "map": {"version": 3, "names": ["PERMISSIONS", "DASHBOARD", "PROJECT_MANAGEMENT", "FILE_LIBRARY", "USER_MANAGEMENT", "SYSTEM_SETTINGS", "DEPARTMENT_MANAGEMENT", "PERSONAL_DASHBOARD", "PERSONAL_TASKS", "PROJECT_VIEW", "TASK_MANAGEMENT", "FILE_LIBRARY_LIMITED", "CREATE_USER", "DELETE_USER", "MODIFY_USER", "ASSIGN_ROLE", "DATA_CRUD", "DATA_IMPORT_EXPORT", "MODIFY_GLOBAL_CONFIG", "CHANGE_FILE", "CREATE_PROJECT", "DELETE_PROJECT", "MODIFY_PROJECT_STATUS", "VIEW_DEPARTMENT_MEMBERS", "UPLOAD_FILES", "DELETE_FILES", "DOWNLOAD_FILES", "UPDATE_TASK_STATUS", "EDIT_OWN_PROJECT", "CREATE_OWN_PROJECT", "DOWNLOAD_OWN_PROJECT", "VIEW_TASKS_ONLY", "MODIFY_PERSONAL_INFO", "ROLES", "SUPER_ADMIN", "MANAGER", "DESIGNER", "USER", "ROLE_PERMISSIONS", "PAGE_PERMISSIONS", "hasPermission", "userRole", "permission", "rolePermissions", "includes", "canAccessPage", "pageName", "requiredPermissions", "length", "userPermissions", "some", "getAccessiblePages", "Object", "keys", "filter", "ROLE_DISPLAY_NAMES", "ROLE_DESCRIPTIONS"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/auth/utils/rolePermissions.js"], "sourcesContent": ["// 角色权限配置文件\r\n// 定义系统中所有角色及其对应的权限\r\n\r\n// 权限常量定义\r\nexport const PERMISSIONS = {\r\n  // 页面访问权限\r\n  DASHBOARD: 'dashboard',\r\n  PROJECT_MANAGEMENT: 'project_management',\r\n  FILE_LIBRARY: 'file_library',\r\n  USER_MANAGEMENT: 'user_management',\r\n  SYSTEM_SETTINGS: 'system_settings',\r\n  DEPARTMENT_MANAGEMENT: 'department_management',\r\n  PERSONAL_DASHBOARD: 'personal_dashboard',\r\n  PERSONAL_TASKS: 'personal_tasks',\r\n  PROJECT_VIEW: 'project_view',\r\n  TASK_MANAGEMENT: 'task_management',\r\n  FILE_LIBRARY_LIMITED: 'file_library_limited',\r\n  \r\n  // 操作权限\r\n  CREATE_USER: 'create_user',\r\n  DELETE_USER: 'delete_user',\r\n  MODIFY_USER: 'modify_user',\r\n  ASSIGN_ROLE: 'assign_role',\r\n  DATA_CRUD: 'data_crud',\r\n  DATA_IMPORT_EXPORT: 'data_import_export',\r\n  MODIFY_GLOBAL_CONFIG: 'modify_global_config',\r\n  CHANGE_FILE: 'change_file',\r\n  CREATE_PROJECT: 'create_project',\r\n  DELETE_PROJECT: 'delete_project',\r\n  MODIFY_PROJECT_STATUS: 'modify_project_status',\r\n  VIEW_DEPARTMENT_MEMBERS: 'view_department_members',\r\n  UPLOAD_FILES: 'upload_files',\r\n  DELETE_FILES: 'delete_files',\r\n  DOWNLOAD_FILES: 'download_files',\r\n  UPDATE_TASK_STATUS: 'update_task_status',\r\n  EDIT_OWN_PROJECT: 'edit_own_project',\r\n  CREATE_OWN_PROJECT: 'create_own_project',\r\n  DOWNLOAD_OWN_PROJECT: 'download_own_project',\r\n  VIEW_TASKS_ONLY: 'view_tasks_only',\r\n  MODIFY_PERSONAL_INFO: 'modify_personal_info'\r\n};\r\n\r\n// 角色常量定义\r\nexport const ROLES = {\r\n  SUPER_ADMIN: 'super_admin',\r\n  MANAGER: 'manager',\r\n  DESIGNER: 'designer',\r\n  USER: 'user'\r\n};\r\n\r\n// 角色权限映射\r\nexport const ROLE_PERMISSIONS = {\r\n  [ROLES.SUPER_ADMIN]: [\r\n    // 页面权限 - 所有页面\r\n    PERMISSIONS.DASHBOARD,\r\n    PERMISSIONS.PROJECT_MANAGEMENT,\r\n    PERMISSIONS.FILE_LIBRARY,\r\n    PERMISSIONS.USER_MANAGEMENT,\r\n    PERMISSIONS.SYSTEM_SETTINGS,\r\n    PERMISSIONS.DEPARTMENT_MANAGEMENT,\r\n    \r\n    // 操作权限 - 所有操作\r\n    PERMISSIONS.CREATE_USER,\r\n    PERMISSIONS.DELETE_USER,\r\n    PERMISSIONS.MODIFY_USER,\r\n    PERMISSIONS.ASSIGN_ROLE,\r\n    PERMISSIONS.DATA_CRUD,\r\n    PERMISSIONS.DATA_IMPORT_EXPORT,\r\n    PERMISSIONS.MODIFY_GLOBAL_CONFIG,\r\n    PERMISSIONS.CHANGE_FILE,\r\n    PERMISSIONS.CREATE_PROJECT,\r\n    PERMISSIONS.DELETE_PROJECT,\r\n    PERMISSIONS.MODIFY_PROJECT_STATUS,\r\n    PERMISSIONS.VIEW_DEPARTMENT_MEMBERS,\r\n    PERMISSIONS.UPLOAD_FILES,\r\n    PERMISSIONS.DELETE_FILES,\r\n    PERMISSIONS.DOWNLOAD_FILES,\r\n    PERMISSIONS.UPDATE_TASK_STATUS,\r\n    PERMISSIONS.EDIT_OWN_PROJECT,\r\n    PERMISSIONS.CREATE_OWN_PROJECT,\r\n    PERMISSIONS.DOWNLOAD_OWN_PROJECT,\r\n    PERMISSIONS.MODIFY_PERSONAL_INFO\r\n  ],\r\n  \r\n  [ROLES.MANAGER]: [\r\n    // 页面权限\r\n    PERMISSIONS.DASHBOARD,\r\n    PERMISSIONS.PROJECT_MANAGEMENT,\r\n    PERMISSIONS.FILE_LIBRARY,\r\n    PERMISSIONS.DEPARTMENT_MANAGEMENT,\r\n    \r\n    // 操作权限\r\n    PERMISSIONS.CREATE_PROJECT,\r\n    PERMISSIONS.MODIFY_PROJECT_STATUS,\r\n    PERMISSIONS.VIEW_DEPARTMENT_MEMBERS,\r\n    PERMISSIONS.UPLOAD_FILES,\r\n    PERMISSIONS.DOWNLOAD_FILES,\r\n    PERMISSIONS.DELETE_FILES, // 仅能删除自己上传的文件\r\n    PERMISSIONS.CHANGE_FILE,\r\n    PERMISSIONS.MODIFY_PERSONAL_INFO\r\n  ],\r\n  \r\n  [ROLES.DESIGNER]: [\r\n    // 页面权限\r\n    PERMISSIONS.PROJECT_VIEW,\r\n    PERMISSIONS.TASK_MANAGEMENT,\r\n    PERMISSIONS.FILE_LIBRARY_LIMITED,\r\n    \r\n    // 操作权限\r\n    PERMISSIONS.UPDATE_TASK_STATUS,\r\n    PERMISSIONS.UPLOAD_FILES,\r\n    PERMISSIONS.DOWNLOAD_FILES,\r\n    PERMISSIONS.EDIT_OWN_PROJECT,\r\n    PERMISSIONS.CREATE_OWN_PROJECT,\r\n    PERMISSIONS.DOWNLOAD_OWN_PROJECT,\r\n    PERMISSIONS.MODIFY_PERSONAL_INFO\r\n  ],\r\n  \r\n  [ROLES.USER]: [\r\n    // 页面权限\r\n    PERMISSIONS.PERSONAL_DASHBOARD,\r\n    PERMISSIONS.PERSONAL_TASKS,\r\n    \r\n    // 操作权限\r\n    PERMISSIONS.VIEW_TASKS_ONLY,\r\n    PERMISSIONS.MODIFY_PERSONAL_INFO\r\n  ]\r\n};\r\n\r\n// 页面路由权限映射\r\nexport const PAGE_PERMISSIONS = {\r\n  'home': [], // 首页所有人都可以访问\r\n  'work-target': [PERMISSIONS.DASHBOARD, PERMISSIONS.PROJECT_MANAGEMENT],\r\n  'work-tracking': [PERMISSIONS.DASHBOARD, PERMISSIONS.PROJECT_MANAGEMENT],\r\n  'world-class': [PERMISSIONS.DASHBOARD, PERMISSIONS.PROJECT_MANAGEMENT],\r\n  'monthly-kpi': [PERMISSIONS.DASHBOARD, PERMISSIONS.PROJECT_MANAGEMENT],\r\n  'project-one': [PERMISSIONS.PROJECT_MANAGEMENT, PERMISSIONS.PROJECT_VIEW],\r\n  'module-six': [PERMISSIONS.DASHBOARD, PERMISSIONS.PROJECT_MANAGEMENT],\r\n  'department': [PERMISSIONS.DEPARTMENT_MANAGEMENT],\r\n  'user-management': [PERMISSIONS.USER_MANAGEMENT],\r\n  'personal-settings': [PERMISSIONS.MODIFY_PERSONAL_INFO],\r\n  'system-settings': [PERMISSIONS.SYSTEM_SETTINGS]\r\n};\r\n\r\n// 检查用户是否有特定权限\r\nexport const hasPermission = (userRole, permission) => {\r\n  if (!userRole || !permission) return false;\r\n  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];\r\n  return rolePermissions.includes(permission);\r\n};\r\n\r\n// 检查用户是否可以访问特定页面\r\nexport const canAccessPage = (userRole, pageName) => {\r\n  if (!userRole || !pageName) return false;\r\n  \r\n  const requiredPermissions = PAGE_PERMISSIONS[pageName] || [];\r\n  \r\n  // 如果页面不需要特殊权限，则允许访问\r\n  if (requiredPermissions.length === 0) return true;\r\n  \r\n  const userPermissions = ROLE_PERMISSIONS[userRole] || [];\r\n  \r\n  // 检查用户是否拥有页面所需的任一权限\r\n  return requiredPermissions.some(permission => userPermissions.includes(permission));\r\n};\r\n\r\n// 获取用户可访问的页面列表\r\nexport const getAccessiblePages = (userRole) => {\r\n  if (!userRole) return ['home'];\r\n  \r\n  return Object.keys(PAGE_PERMISSIONS).filter(pageName => \r\n    canAccessPage(userRole, pageName)\r\n  );\r\n};\r\n\r\n// 角色显示名称映射\r\nexport const ROLE_DISPLAY_NAMES = {\r\n  [ROLES.SUPER_ADMIN]: '超级管理员',\r\n  [ROLES.MANAGER]: '部长',\r\n  [ROLES.DESIGNER]: '设计师',\r\n  [ROLES.USER]: '普通用户'\r\n};\r\n\r\n// 角色描述映射\r\nexport const ROLE_DESCRIPTIONS = {\r\n  [ROLES.SUPER_ADMIN]: '拥有系统所有权限，可管理用户、配置系统',\r\n  [ROLES.MANAGER]: '可管理项目、部门成员，访问数据看板',\r\n  [ROLES.DESIGNER]: '可查看项目、管理任务、上传文件',\r\n  [ROLES.USER]: '只能查看个人任务和基本信息'\r\n};\r\n\r\nexport default {\r\n  PERMISSIONS,\r\n  ROLES,\r\n  ROLE_PERMISSIONS,\r\n  PAGE_PERMISSIONS,\r\n  hasPermission,\r\n  canAccessPage,\r\n  getAccessiblePages,\r\n  ROLE_DISPLAY_NAMES,\r\n  ROLE_DESCRIPTIONS\r\n};\r\n"], "mappings": "AAAA;AACA;AAEA;AACA,MAAO,MAAM,CAAAA,WAAW,CAAG,CACzB;AACAC,SAAS,CAAE,WAAW,CACtBC,kBAAkB,CAAE,oBAAoB,CACxCC,YAAY,CAAE,cAAc,CAC5BC,eAAe,CAAE,iBAAiB,CAClCC,eAAe,CAAE,iBAAiB,CAClCC,qBAAqB,CAAE,uBAAuB,CAC9CC,kBAAkB,CAAE,oBAAoB,CACxCC,cAAc,CAAE,gBAAgB,CAChCC,YAAY,CAAE,cAAc,CAC5BC,eAAe,CAAE,iBAAiB,CAClCC,oBAAoB,CAAE,sBAAsB,CAE5C;AACAC,WAAW,CAAE,aAAa,CAC1BC,WAAW,CAAE,aAAa,CAC1BC,WAAW,CAAE,aAAa,CAC1BC,WAAW,CAAE,aAAa,CAC1BC,SAAS,CAAE,WAAW,CACtBC,kBAAkB,CAAE,oBAAoB,CACxCC,oBAAoB,CAAE,sBAAsB,CAC5CC,WAAW,CAAE,aAAa,CAC1BC,cAAc,CAAE,gBAAgB,CAChCC,cAAc,CAAE,gBAAgB,CAChCC,qBAAqB,CAAE,uBAAuB,CAC9CC,uBAAuB,CAAE,yBAAyB,CAClDC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,cAAc,CAC5BC,cAAc,CAAE,gBAAgB,CAChCC,kBAAkB,CAAE,oBAAoB,CACxCC,gBAAgB,CAAE,kBAAkB,CACpCC,kBAAkB,CAAE,oBAAoB,CACxCC,oBAAoB,CAAE,sBAAsB,CAC5CC,eAAe,CAAE,iBAAiB,CAClCC,oBAAoB,CAAE,sBACxB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,KAAK,CAAG,CACnBC,WAAW,CAAE,aAAa,CAC1BC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,MACR,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,gBAAgB,CAAG,CAC9B,CAACL,KAAK,CAACC,WAAW,EAAG,CACnB;AACAlC,WAAW,CAACC,SAAS,CACrBD,WAAW,CAACE,kBAAkB,CAC9BF,WAAW,CAACG,YAAY,CACxBH,WAAW,CAACI,eAAe,CAC3BJ,WAAW,CAACK,eAAe,CAC3BL,WAAW,CAACM,qBAAqB,CAEjC;AACAN,WAAW,CAACY,WAAW,CACvBZ,WAAW,CAACa,WAAW,CACvBb,WAAW,CAACc,WAAW,CACvBd,WAAW,CAACe,WAAW,CACvBf,WAAW,CAACgB,SAAS,CACrBhB,WAAW,CAACiB,kBAAkB,CAC9BjB,WAAW,CAACkB,oBAAoB,CAChClB,WAAW,CAACmB,WAAW,CACvBnB,WAAW,CAACoB,cAAc,CAC1BpB,WAAW,CAACqB,cAAc,CAC1BrB,WAAW,CAACsB,qBAAqB,CACjCtB,WAAW,CAACuB,uBAAuB,CACnCvB,WAAW,CAACwB,YAAY,CACxBxB,WAAW,CAACyB,YAAY,CACxBzB,WAAW,CAAC0B,cAAc,CAC1B1B,WAAW,CAAC2B,kBAAkB,CAC9B3B,WAAW,CAAC4B,gBAAgB,CAC5B5B,WAAW,CAAC6B,kBAAkB,CAC9B7B,WAAW,CAAC8B,oBAAoB,CAChC9B,WAAW,CAACgC,oBAAoB,CACjC,CAED,CAACC,KAAK,CAACE,OAAO,EAAG,CACf;AACAnC,WAAW,CAACC,SAAS,CACrBD,WAAW,CAACE,kBAAkB,CAC9BF,WAAW,CAACG,YAAY,CACxBH,WAAW,CAACM,qBAAqB,CAEjC;AACAN,WAAW,CAACoB,cAAc,CAC1BpB,WAAW,CAACsB,qBAAqB,CACjCtB,WAAW,CAACuB,uBAAuB,CACnCvB,WAAW,CAACwB,YAAY,CACxBxB,WAAW,CAAC0B,cAAc,CAC1B1B,WAAW,CAACyB,YAAY,CAAE;AAC1BzB,WAAW,CAACmB,WAAW,CACvBnB,WAAW,CAACgC,oBAAoB,CACjC,CAED,CAACC,KAAK,CAACG,QAAQ,EAAG,CAChB;AACApC,WAAW,CAACS,YAAY,CACxBT,WAAW,CAACU,eAAe,CAC3BV,WAAW,CAACW,oBAAoB,CAEhC;AACAX,WAAW,CAAC2B,kBAAkB,CAC9B3B,WAAW,CAACwB,YAAY,CACxBxB,WAAW,CAAC0B,cAAc,CAC1B1B,WAAW,CAAC4B,gBAAgB,CAC5B5B,WAAW,CAAC6B,kBAAkB,CAC9B7B,WAAW,CAAC8B,oBAAoB,CAChC9B,WAAW,CAACgC,oBAAoB,CACjC,CAED,CAACC,KAAK,CAACI,IAAI,EAAG,CACZ;AACArC,WAAW,CAACO,kBAAkB,CAC9BP,WAAW,CAACQ,cAAc,CAE1B;AACAR,WAAW,CAAC+B,eAAe,CAC3B/B,WAAW,CAACgC,oBAAoB,CAEpC,CAAC,CAED;AACA,MAAO,MAAM,CAAAO,gBAAgB,CAAG,CAC9B,MAAM,CAAE,EAAE,CAAE;AACZ,aAAa,CAAE,CAACvC,WAAW,CAACC,SAAS,CAAED,WAAW,CAACE,kBAAkB,CAAC,CACtE,eAAe,CAAE,CAACF,WAAW,CAACC,SAAS,CAAED,WAAW,CAACE,kBAAkB,CAAC,CACxE,aAAa,CAAE,CAACF,WAAW,CAACC,SAAS,CAAED,WAAW,CAACE,kBAAkB,CAAC,CACtE,aAAa,CAAE,CAACF,WAAW,CAACC,SAAS,CAAED,WAAW,CAACE,kBAAkB,CAAC,CACtE,aAAa,CAAE,CAACF,WAAW,CAACE,kBAAkB,CAAEF,WAAW,CAACS,YAAY,CAAC,CACzE,YAAY,CAAE,CAACT,WAAW,CAACC,SAAS,CAAED,WAAW,CAACE,kBAAkB,CAAC,CACrE,YAAY,CAAE,CAACF,WAAW,CAACM,qBAAqB,CAAC,CACjD,iBAAiB,CAAE,CAACN,WAAW,CAACI,eAAe,CAAC,CAChD,mBAAmB,CAAE,CAACJ,WAAW,CAACgC,oBAAoB,CAAC,CACvD,iBAAiB,CAAE,CAAChC,WAAW,CAACK,eAAe,CACjD,CAAC,CAED;AACA,MAAO,MAAM,CAAAmC,aAAa,CAAGA,CAACC,QAAQ,CAAEC,UAAU,GAAK,CACrD,GAAI,CAACD,QAAQ,EAAI,CAACC,UAAU,CAAE,MAAO,MAAK,CAC1C,KAAM,CAAAC,eAAe,CAAGL,gBAAgB,CAACG,QAAQ,CAAC,EAAI,EAAE,CACxD,MAAO,CAAAE,eAAe,CAACC,QAAQ,CAACF,UAAU,CAAC,CAC7C,CAAC,CAED;AACA,MAAO,MAAM,CAAAG,aAAa,CAAGA,CAACJ,QAAQ,CAAEK,QAAQ,GAAK,CACnD,GAAI,CAACL,QAAQ,EAAI,CAACK,QAAQ,CAAE,MAAO,MAAK,CAExC,KAAM,CAAAC,mBAAmB,CAAGR,gBAAgB,CAACO,QAAQ,CAAC,EAAI,EAAE,CAE5D;AACA,GAAIC,mBAAmB,CAACC,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEjD,KAAM,CAAAC,eAAe,CAAGX,gBAAgB,CAACG,QAAQ,CAAC,EAAI,EAAE,CAExD;AACA,MAAO,CAAAM,mBAAmB,CAACG,IAAI,CAACR,UAAU,EAAIO,eAAe,CAACL,QAAQ,CAACF,UAAU,CAAC,CAAC,CACrF,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,kBAAkB,CAAIV,QAAQ,EAAK,CAC9C,GAAI,CAACA,QAAQ,CAAE,MAAO,CAAC,MAAM,CAAC,CAE9B,MAAO,CAAAW,MAAM,CAACC,IAAI,CAACd,gBAAgB,CAAC,CAACe,MAAM,CAACR,QAAQ,EAClDD,aAAa,CAACJ,QAAQ,CAAEK,QAAQ,CAClC,CAAC,CACH,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,kBAAkB,CAAG,CAChC,CAACtB,KAAK,CAACC,WAAW,EAAG,OAAO,CAC5B,CAACD,KAAK,CAACE,OAAO,EAAG,IAAI,CACrB,CAACF,KAAK,CAACG,QAAQ,EAAG,KAAK,CACvB,CAACH,KAAK,CAACI,IAAI,EAAG,MAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAmB,iBAAiB,CAAG,CAC/B,CAACvB,KAAK,CAACC,WAAW,EAAG,qBAAqB,CAC1C,CAACD,KAAK,CAACE,OAAO,EAAG,mBAAmB,CACpC,CAACF,KAAK,CAACG,QAAQ,EAAG,iBAAiB,CACnC,CAACH,KAAK,CAACI,IAAI,EAAG,eAChB,CAAC,CAED,cAAe,CACbrC,WAAW,CACXiC,KAAK,CACLK,gBAAgB,CAChBC,gBAAgB,CAChBC,aAAa,CACbK,aAAa,CACbM,kBAAkB,CAClBI,kBAAkB,CAClBC,iBACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}