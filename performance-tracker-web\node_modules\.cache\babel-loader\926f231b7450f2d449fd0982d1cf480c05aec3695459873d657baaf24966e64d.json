{"ast": null, "code": "import*as XLSX from'xlsx';class DownloadService{constructor(){this.baseURL='/api';}// 处理选择性下载\nasync handleSelectiveDownload(selectionData){const{selectedData,format,statistics}=selectionData;try{switch(format){case'excel':return await this.generateExcel(selectedData,statistics);case'pdf':return await this.generatePDF(selectedData,statistics);case'csv':return await this.generateCSV(selectedData,statistics);default:throw new Error(\"\\u4E0D\\u652F\\u6301\\u7684\\u683C\\u5F0F: \".concat(format));}}catch(error){console.error('下载失败:',error);throw error;}}// 生成Excel文件\nasync generateExcel(selectedData,statistics){try{// 创建新的工作簿\nconst workbook=XLSX.utils.book_new();// 创建统一的工作表，包含所有分类数据\nthis.createUnifiedWorksheet(workbook,selectedData,statistics);// 创建汇总页\nthis.createSummarySheet(workbook,selectedData,statistics);// 生成并下载文件\nconst fileName=\"\\u5DE5\\u4F5C\\u76EE\\u6807\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".xlsx\");XLSX.writeFile(workbook,fileName);return{success:true,fileName,message:\"Excel\\u6587\\u4EF6\\u5DF2\\u751F\\u6210: \".concat(fileName)};}catch(error){console.error('Excel生成失败:',error);throw error;}}// 创建工作表\ncreateWorksheet(workbook,categoryData,category){// 准备表头\nconst headers=['序号','指标','目标值','权重（分）','考核标准','指标分类','责任人'];// 准备数据行\nconst rows=[headers];categoryData.forEach((item,index)=>{const data=item.data;const row=[this.formatValue(data.序号),this.formatValue(data.指标),this.formatValue(data.目标值),this.formatValue(data.权重),this.formatValue(data.考核标准),this.formatValue(data.指标分类),this.formatValue(data.责任人)];rows.push(row);});// 创建工作表\nconst worksheet=XLSX.utils.aoa_to_sheet(rows);// 设置列宽\nconst colWidths=[{wch:8},// 序号\n{wch:30},// 指标\n{wch:20},// 目标值\n{wch:12},// 权重\n{wch:40},// 考核标准\n{wch:15},// 指标分类\n{wch:15}// 责任人\n];worksheet['!cols']=colWidths;// 设置表头样式\nthis.setHeaderStyle(worksheet,headers.length);// 添加到工作簿\nXLSX.utils.book_append_sheet(workbook,worksheet,category.sheetName);}// 创建统一工作表（包含所有分类数据）\ncreateUnifiedWorksheet(workbook,selectedData,statistics){// 准备表头\nconst headers=['分类','序号','指标','目标值','权重（分）','考核标准','指标分类','责任人'];// 准备数据行\nconst rows=[headers];// 定义分类信息\nconst categories=[{key:'keyIndicators',title:'关键指标'},{key:'qualityIndicators',title:'质量指标'},{key:'keyWork',title:'重点工作'}];// 按分类组织数据\ncategories.forEach(category=>{const categoryData=selectedData[category.key];if(categoryData&&categoryData.length>0){// 添加分类标题行\nrows.push([\"=== \".concat(category.title,\" ===\"),'','','','','','','']);// 添加该分类的数据行\ncategoryData.forEach((item,index)=>{const data=item.data;const row=[category.title,this.formatValue(data.序号),this.formatValue(data.指标),this.formatValue(data.目标值),this.formatValue(data.权重),this.formatValue(data.考核标准),this.formatValue(data.指标分类),this.formatValue(data.责任人)];rows.push(row);});// 添加空行分隔\nrows.push(['','','','','','','','']);}});// 添加导出信息\nrows.push(['导出时间: '+this.formatDateTime(new Date()),'','','','','','','']);rows.push([\"\\u603B\\u9009\\u62E9\\u9879\\u76EE: \".concat(statistics.totalItems),'','','','','','','']);rows.push([\"\\u603B\\u6743\\u91CD: \".concat(statistics.totalWeight),'','','','','','','']);// 创建工作表\nconst worksheet=XLSX.utils.aoa_to_sheet(rows);// 设置列宽\nconst colWidths=[{wch:15},// 分类\n{wch:8},// 序号\n{wch:30},// 指标\n{wch:20},// 目标值\n{wch:12},// 权重\n{wch:40},// 考核标准\n{wch:15},// 指标分类\n{wch:15}// 责任人\n];worksheet['!cols']=colWidths;// 设置样式\nthis.setUnifiedWorksheetStyle(worksheet,rows.length,headers.length);// 添加到工作簿\nXLSX.utils.book_append_sheet(workbook,worksheet,'工作目标数据');}// 创建汇总页\ncreateSummarySheet(workbook,selectedData,statistics){const summaryData=[['工作目标导出汇总','','',''],['导出时间',this.formatDateTime(new Date()),'',''],['','','',''],['分类','选择项目数','权重合计',''],['','','','']];// 统计每个分类的数据\nconst categories=[{key:'keyIndicators',title:'关键指标'},{key:'qualityIndicators',title:'质量指标'},{key:'keyWork',title:'重点工作'}];categories.forEach(category=>{const categoryData=selectedData[category.key]||[];const count=categoryData.length;const weight=categoryData.reduce((sum,item)=>{const w=item.data.权重;return sum+(this.parseNumber(w)||0);},0);summaryData.push([category.title,count,weight,'']);});summaryData.push(['','','','']);summaryData.push(['总计',statistics.totalItems,statistics.totalWeight,'']);const summaryWorksheet=XLSX.utils.aoa_to_sheet(summaryData);// 设置列宽\nsummaryWorksheet['!cols']=[{wch:20},{wch:15},{wch:15},{wch:10}];XLSX.utils.book_append_sheet(workbook,summaryWorksheet,'导出汇总');}// 生成PDF文件\nasync generatePDF(selectedData,statistics){try{// 这里实现PDF生成逻辑\n// 可以使用jsPDF或者调用后端API\nconsole.log('PDF生成功能待实现');// 临时实现：转为JSON后提示\nconst jsonData=this.prepareDataForExport(selectedData,statistics);const dataStr=JSON.stringify(jsonData,null,2);const blob=new Blob([dataStr],{type:'application/json'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u5DE5\\u4F5C\\u76EE\\u6807\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".json\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'PDF功能开发中，已生成JSON格式文件'};}catch(error){console.error('PDF生成失败:',error);throw error;}}// 生成CSV文件\nasync generateCSV(selectedData,statistics){try{let csvContent='\\uFEFF';// UTF-8 BOM for Excel compatibility\n// 为每个分类生成CSV内容\nconst categories=[{key:'keyIndicators',title:'关键指标'},{key:'qualityIndicators',title:'质量指标'},{key:'keyWork',title:'重点工作'}];categories.forEach(category=>{const categoryData=selectedData[category.key];if(categoryData&&categoryData.length>0){csvContent+=\"\\n=== \".concat(category.title,\" ===\\n\");csvContent+='序号,指标,目标值,权重（分）,考核标准,指标分类,责任人\\n';categoryData.forEach(item=>{const data=item.data;const row=[this.escapeCsvValue(data.序号),this.escapeCsvValue(data.指标),this.escapeCsvValue(data.目标值),this.escapeCsvValue(data.权重),this.escapeCsvValue(data.考核标准),this.escapeCsvValue(data.指标分类),this.escapeCsvValue(data.责任人)].join(',');csvContent+=row+'\\n';});csvContent+='\\n';}});// 添加汇总信息\ncsvContent+='\\n=== 导出汇总 ===\\n';csvContent+=\"\\u5BFC\\u51FA\\u65F6\\u95F4,\".concat(this.formatDateTime(new Date()),\"\\n\");csvContent+=\"\\u603B\\u9009\\u62E9\\u9879\\u76EE,\".concat(statistics.totalItems,\"\\n\");csvContent+=\"\\u603B\\u6743\\u91CD,\".concat(statistics.totalWeight,\"\\n\");// 下载文件\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u5DE5\\u4F5C\\u76EE\\u6807\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".csv\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'CSV文件已生成并下载'};}catch(error){console.error('CSV生成失败:',error);throw error;}}// 工具方法：格式化值\nformatValue(value){if(value===null||value===undefined||value===''){return'';}if(typeof value==='object'){if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);}// 工具方法：解析数字\nparseNumber(value){if(value===null||value===undefined||value==='')return 0;const num=typeof value==='number'?value:Number(value);return isNaN(num)?0:num;}// 工具方法：CSV值转义\nescapeCsvValue(value){const formattedValue=this.formatValue(value);if(formattedValue.includes(',')||formattedValue.includes('\"')||formattedValue.includes('\\n')){return\"\\\"\".concat(formattedValue.replace(/\"/g,'\"\"'),\"\\\"\");}return formattedValue;}// 工具方法：设置表头样式\nsetHeaderStyle(worksheet,colCount){for(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(!worksheet[cellRef])continue;worksheet[cellRef].s={font:{bold:true,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"20FF4D\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}// 工具方法：设置统一工作表样式\nsetUnifiedWorksheetStyle(worksheet,rowCount,colCount){// 设置表头样式\nfor(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(!worksheet[cellRef])continue;worksheet[cellRef].s={font:{bold:true,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"20FF4D\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}// 设置分类标题行样式\nfor(let r=1;r<rowCount;r++){const firstCellRef=XLSX.utils.encode_cell({r:r,c:0});if(!worksheet[firstCellRef])continue;const cellValue=worksheet[firstCellRef].v;if(cellValue&&typeof cellValue==='string'&&cellValue.startsWith('===')){// 分类标题行样式\nfor(let c=0;c<colCount;c++){const cellRef=XLSX.utils.encode_cell({r:r,c:c});if(!worksheet[cellRef])continue;worksheet[cellRef].s={font:{bold:true,color:{rgb:\"000000\"}},fill:{fgColor:{rgb:\"FFE066\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}}}// 工具方法：格式化日期\nformatDate(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year).concat(month).concat(day);}// 工具方法：格式化日期时间\nformatDateTime(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day,\" \").concat(hours,\":\").concat(minutes);}// 准备导出数据\nprepareDataForExport(selectedData,statistics){return{exportInfo:{timestamp:new Date().toISOString(),totalItems:statistics.totalItems,totalWeight:statistics.totalWeight},data:selectedData};}}export default new DownloadService();", "map": {"version": 3, "names": ["XLSX", "DownloadService", "constructor", "baseURL", "handleSelectiveDownload", "selectionData", "selectedData", "format", "statistics", "generateExcel", "generatePDF", "generateCSV", "Error", "concat", "error", "console", "workbook", "utils", "book_new", "createUnifiedWorksheet", "createSummarySheet", "fileName", "formatDate", "Date", "writeFile", "success", "message", "createWorksheet", "categoryData", "category", "headers", "rows", "for<PERSON>ach", "item", "index", "data", "row", "formatValue", "序号", "指标", "目标值", "权重", "考核标准", "指标分类", "责任人", "push", "worksheet", "aoa_to_sheet", "col<PERSON><PERSON><PERSON>", "wch", "setHeaderStyle", "length", "book_append_sheet", "sheetName", "categories", "key", "title", "formatDateTime", "totalItems", "totalWeight", "setUnifiedWorksheetStyle", "summaryData", "count", "weight", "reduce", "sum", "w", "parseNumber", "summaryWorksheet", "log", "jsonData", "prepareDataForExport", "dataStr", "JSON", "stringify", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "csv<PERSON><PERSON>nt", "escapeCsvValue", "join", "value", "undefined", "text", "richText", "String", "num", "Number", "isNaN", "formattedValue", "includes", "replace", "col<PERSON>ount", "i", "cellRef", "encode_cell", "r", "c", "s", "font", "bold", "color", "rgb", "fill", "fgColor", "alignment", "horizontal", "vertical", "rowCount", "firstCellRef", "cellValue", "v", "startsWith", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "exportInfo", "timestamp", "toISOString"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块一/services/downloadService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\n\r\nclass DownloadService {\r\n  constructor() {\r\n    this.baseURL = '/api';\r\n  }\r\n\r\n  // 处理选择性下载\r\n  async handleSelectiveDownload(selectionData) {\r\n    const { selectedData, format, statistics } = selectionData;\r\n    \r\n    try {\r\n      switch (format) {\r\n        case 'excel':\r\n          return await this.generateExcel(selectedData, statistics);\r\n        case 'pdf':\r\n          return await this.generatePDF(selectedData, statistics);\r\n        case 'csv':\r\n          return await this.generateCSV(selectedData, statistics);\r\n        default:\r\n          throw new Error(`不支持的格式: ${format}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('下载失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成Excel文件\r\n  async generateExcel(selectedData, statistics) {\r\n    try {\r\n      // 创建新的工作簿\r\n      const workbook = XLSX.utils.book_new();\r\n\r\n      // 创建统一的工作表，包含所有分类数据\r\n      this.createUnifiedWorksheet(workbook, selectedData, statistics);\r\n\r\n      // 创建汇总页\r\n      this.createSummarySheet(workbook, selectedData, statistics);\r\n\r\n      // 生成并下载文件\r\n      const fileName = `工作目标选择导出_${this.formatDate(new Date())}.xlsx`;\r\n      XLSX.writeFile(workbook, fileName);\r\n\r\n      return {\r\n        success: true,\r\n        fileName,\r\n        message: `Excel文件已生成: ${fileName}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Excel生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 创建工作表\r\n  createWorksheet(workbook, categoryData, category) {\r\n    // 准备表头\r\n    const headers = ['序号', '指标', '目标值', '权重（分）', '考核标准', '指标分类', '责任人'];\r\n    \r\n    // 准备数据行\r\n    const rows = [headers];\r\n    \r\n    categoryData.forEach((item, index) => {\r\n      const data = item.data;\r\n      const row = [\r\n        this.formatValue(data.序号),\r\n        this.formatValue(data.指标),\r\n        this.formatValue(data.目标值),\r\n        this.formatValue(data.权重),\r\n        this.formatValue(data.考核标准),\r\n        this.formatValue(data.指标分类),\r\n        this.formatValue(data.责任人)\r\n      ];\r\n      rows.push(row);\r\n    });\r\n\r\n    // 创建工作表\r\n    const worksheet = XLSX.utils.aoa_to_sheet(rows);\r\n    \r\n    // 设置列宽\r\n    const colWidths = [\r\n      { wch: 8 },   // 序号\r\n      { wch: 30 },  // 指标\r\n      { wch: 20 },  // 目标值\r\n      { wch: 12 },  // 权重\r\n      { wch: 40 },  // 考核标准\r\n      { wch: 15 },  // 指标分类\r\n      { wch: 15 }   // 责任人\r\n    ];\r\n    worksheet['!cols'] = colWidths;\r\n\r\n    // 设置表头样式\r\n    this.setHeaderStyle(worksheet, headers.length);\r\n    \r\n    // 添加到工作簿\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, category.sheetName);\r\n  }\r\n\r\n  // 创建统一工作表（包含所有分类数据）\r\n  createUnifiedWorksheet(workbook, selectedData, statistics) {\r\n    // 准备表头\r\n    const headers = ['分类', '序号', '指标', '目标值', '权重（分）', '考核标准', '指标分类', '责任人'];\r\n\r\n    // 准备数据行\r\n    const rows = [headers];\r\n\r\n    // 定义分类信息\r\n    const categories = [\r\n      { key: 'keyIndicators', title: '关键指标' },\r\n      { key: 'qualityIndicators', title: '质量指标' },\r\n      { key: 'keyWork', title: '重点工作' }\r\n    ];\r\n\r\n    // 按分类组织数据\r\n    categories.forEach(category => {\r\n      const categoryData = selectedData[category.key];\r\n      if (categoryData && categoryData.length > 0) {\r\n        // 添加分类标题行\r\n        rows.push([\r\n          `=== ${category.title} ===`,\r\n          '', '', '', '', '', '', ''\r\n        ]);\r\n\r\n        // 添加该分类的数据行\r\n        categoryData.forEach((item, index) => {\r\n          const data = item.data;\r\n          const row = [\r\n            category.title,\r\n            this.formatValue(data.序号),\r\n            this.formatValue(data.指标),\r\n            this.formatValue(data.目标值),\r\n            this.formatValue(data.权重),\r\n            this.formatValue(data.考核标准),\r\n            this.formatValue(data.指标分类),\r\n            this.formatValue(data.责任人)\r\n          ];\r\n          rows.push(row);\r\n        });\r\n\r\n        // 添加空行分隔\r\n        rows.push(['', '', '', '', '', '', '', '']);\r\n      }\r\n    });\r\n\r\n    // 添加导出信息\r\n    rows.push(['导出时间: ' + this.formatDateTime(new Date()), '', '', '', '', '', '', '']);\r\n    rows.push([`总选择项目: ${statistics.totalItems}`, '', '', '', '', '', '', '']);\r\n    rows.push([`总权重: ${statistics.totalWeight}`, '', '', '', '', '', '', '']);\r\n\r\n    // 创建工作表\r\n    const worksheet = XLSX.utils.aoa_to_sheet(rows);\r\n\r\n    // 设置列宽\r\n    const colWidths = [\r\n      { wch: 15 },  // 分类\r\n      { wch: 8 },   // 序号\r\n      { wch: 30 },  // 指标\r\n      { wch: 20 },  // 目标值\r\n      { wch: 12 },  // 权重\r\n      { wch: 40 },  // 考核标准\r\n      { wch: 15 },  // 指标分类\r\n      { wch: 15 }   // 责任人\r\n    ];\r\n    worksheet['!cols'] = colWidths;\r\n\r\n    // 设置样式\r\n    this.setUnifiedWorksheetStyle(worksheet, rows.length, headers.length);\r\n\r\n    // 添加到工作簿\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, '工作目标数据');\r\n  }\r\n\r\n  // 创建汇总页\r\n  createSummarySheet(workbook, selectedData, statistics) {\r\n    const summaryData = [\r\n      ['工作目标导出汇总', '', '', ''],\r\n      ['导出时间', this.formatDateTime(new Date()), '', ''],\r\n      ['', '', '', ''],\r\n      ['分类', '选择项目数', '权重合计', ''],\r\n      ['', '', '', '']\r\n    ];\r\n\r\n    // 统计每个分类的数据\r\n    const categories = [\r\n      { key: 'keyIndicators', title: '关键指标' },\r\n      { key: 'qualityIndicators', title: '质量指标' },\r\n      { key: 'keyWork', title: '重点工作' }\r\n    ];\r\n\r\n    categories.forEach(category => {\r\n      const categoryData = selectedData[category.key] || [];\r\n      const count = categoryData.length;\r\n      const weight = categoryData.reduce((sum, item) => {\r\n        const w = item.data.权重;\r\n        return sum + (this.parseNumber(w) || 0);\r\n      }, 0);\r\n      \r\n      summaryData.push([category.title, count, weight, '']);\r\n    });\r\n\r\n    summaryData.push(['', '', '', '']);\r\n    summaryData.push(['总计', statistics.totalItems, statistics.totalWeight, '']);\r\n\r\n    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);\r\n    \r\n    // 设置列宽\r\n    summaryWorksheet['!cols'] = [\r\n      { wch: 20 },\r\n      { wch: 15 },\r\n      { wch: 15 },\r\n      { wch: 10 }\r\n    ];\r\n\r\n    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '导出汇总');\r\n  }\r\n\r\n  // 生成PDF文件\r\n  async generatePDF(selectedData, statistics) {\r\n    try {\r\n      // 这里实现PDF生成逻辑\r\n      // 可以使用jsPDF或者调用后端API\r\n      console.log('PDF生成功能待实现');\r\n      \r\n      // 临时实现：转为JSON后提示\r\n      const jsonData = this.prepareDataForExport(selectedData, statistics);\r\n      const dataStr = JSON.stringify(jsonData, null, 2);\r\n      const blob = new Blob([dataStr], { type: 'application/json' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `工作目标选择导出_${this.formatDate(new Date())}.json`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'PDF功能开发中，已生成JSON格式文件'\r\n      };\r\n    } catch (error) {\r\n      console.error('PDF生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成CSV文件\r\n  async generateCSV(selectedData, statistics) {\r\n    try {\r\n      let csvContent = '\\uFEFF'; // UTF-8 BOM for Excel compatibility\r\n      \r\n      // 为每个分类生成CSV内容\r\n      const categories = [\r\n        { key: 'keyIndicators', title: '关键指标' },\r\n        { key: 'qualityIndicators', title: '质量指标' },\r\n        { key: 'keyWork', title: '重点工作' }\r\n      ];\r\n\r\n      categories.forEach(category => {\r\n        const categoryData = selectedData[category.key];\r\n        if (categoryData && categoryData.length > 0) {\r\n          csvContent += `\\n=== ${category.title} ===\\n`;\r\n          csvContent += '序号,指标,目标值,权重（分）,考核标准,指标分类,责任人\\n';\r\n          \r\n          categoryData.forEach(item => {\r\n            const data = item.data;\r\n            const row = [\r\n              this.escapeCsvValue(data.序号),\r\n              this.escapeCsvValue(data.指标),\r\n              this.escapeCsvValue(data.目标值),\r\n              this.escapeCsvValue(data.权重),\r\n              this.escapeCsvValue(data.考核标准),\r\n              this.escapeCsvValue(data.指标分类),\r\n              this.escapeCsvValue(data.责任人)\r\n            ].join(',');\r\n            csvContent += row + '\\n';\r\n          });\r\n          csvContent += '\\n';\r\n        }\r\n      });\r\n\r\n      // 添加汇总信息\r\n      csvContent += '\\n=== 导出汇总 ===\\n';\r\n      csvContent += `导出时间,${this.formatDateTime(new Date())}\\n`;\r\n      csvContent += `总选择项目,${statistics.totalItems}\\n`;\r\n      csvContent += `总权重,${statistics.totalWeight}\\n`;\r\n\r\n      // 下载文件\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `工作目标选择导出_${this.formatDate(new Date())}.csv`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'CSV文件已生成并下载'\r\n      };\r\n    } catch (error) {\r\n      console.error('CSV生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 工具方法：格式化值\r\n  formatValue(value) {\r\n    if (value === null || value === undefined || value === '') {\r\n      return '';\r\n    }\r\n    \r\n    if (typeof value === 'object') {\r\n      if (value.text !== undefined) return value.text;\r\n      if (value.richText !== undefined) return value.richText;\r\n      if (value.value !== undefined) return value.value;\r\n      return String(value);\r\n    }\r\n    \r\n    return String(value);\r\n  }\r\n\r\n  // 工具方法：解析数字\r\n  parseNumber(value) {\r\n    if (value === null || value === undefined || value === '') return 0;\r\n    const num = typeof value === 'number' ? value : Number(value);\r\n    return isNaN(num) ? 0 : num;\r\n  }\r\n\r\n  // 工具方法：CSV值转义\r\n  escapeCsvValue(value) {\r\n    const formattedValue = this.formatValue(value);\r\n    if (formattedValue.includes(',') || formattedValue.includes('\"') || formattedValue.includes('\\n')) {\r\n      return `\"${formattedValue.replace(/\"/g, '\"\"')}\"`;\r\n    }\r\n    return formattedValue;\r\n  }\r\n\r\n  // 工具方法：设置表头样式\r\n  setHeaderStyle(worksheet, colCount) {\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (!worksheet[cellRef]) continue;\r\n\r\n      worksheet[cellRef].s = {\r\n        font: { bold: true, color: { rgb: \"FFFFFF\" } },\r\n        fill: { fgColor: { rgb: \"20FF4D\" } },\r\n        alignment: { horizontal: \"center\", vertical: \"center\" }\r\n      };\r\n    }\r\n  }\r\n\r\n  // 工具方法：设置统一工作表样式\r\n  setUnifiedWorksheetStyle(worksheet, rowCount, colCount) {\r\n    // 设置表头样式\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (!worksheet[cellRef]) continue;\r\n\r\n      worksheet[cellRef].s = {\r\n        font: { bold: true, color: { rgb: \"FFFFFF\" } },\r\n        fill: { fgColor: { rgb: \"20FF4D\" } },\r\n        alignment: { horizontal: \"center\", vertical: \"center\" }\r\n      };\r\n    }\r\n\r\n    // 设置分类标题行样式\r\n    for (let r = 1; r < rowCount; r++) {\r\n      const firstCellRef = XLSX.utils.encode_cell({ r: r, c: 0 });\r\n      if (!worksheet[firstCellRef]) continue;\r\n\r\n      const cellValue = worksheet[firstCellRef].v;\r\n      if (cellValue && typeof cellValue === 'string' && cellValue.startsWith('===')) {\r\n        // 分类标题行样式\r\n        for (let c = 0; c < colCount; c++) {\r\n          const cellRef = XLSX.utils.encode_cell({ r: r, c: c });\r\n          if (!worksheet[cellRef]) continue;\r\n\r\n          worksheet[cellRef].s = {\r\n            font: { bold: true, color: { rgb: \"000000\" } },\r\n            fill: { fgColor: { rgb: \"FFE066\" } },\r\n            alignment: { horizontal: \"center\", vertical: \"center\" }\r\n          };\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 工具方法：格式化日期\r\n  formatDate(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}${month}${day}`;\r\n  }\r\n\r\n  // 工具方法：格式化日期时间\r\n  formatDateTime(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n  }\r\n\r\n  // 准备导出数据\r\n  prepareDataForExport(selectedData, statistics) {\r\n    return {\r\n      exportInfo: {\r\n        timestamp: new Date().toISOString(),\r\n        totalItems: statistics.totalItems,\r\n        totalWeight: statistics.totalWeight\r\n      },\r\n      data: selectedData\r\n    };\r\n  }\r\n}\r\n\r\nexport default new DownloadService(); "], "mappings": "AAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,eAAgB,CACpBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,MAAM,CACvB,CAEA;AACA,KAAM,CAAAC,uBAAuBA,CAACC,aAAa,CAAE,CAC3C,KAAM,CAAEC,YAAY,CAAEC,MAAM,CAAEC,UAAW,CAAC,CAAGH,aAAa,CAE1D,GAAI,CACF,OAAQE,MAAM,EACZ,IAAK,OAAO,CACV,MAAO,MAAM,KAAI,CAACE,aAAa,CAACH,YAAY,CAAEE,UAAU,CAAC,CAC3D,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACE,WAAW,CAACJ,YAAY,CAAEE,UAAU,CAAC,CACzD,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACG,WAAW,CAACL,YAAY,CAAEE,UAAU,CAAC,CACzD,QACE,KAAM,IAAI,CAAAI,KAAK,0CAAAC,MAAA,CAAYN,MAAM,CAAE,CAAC,CACxC,CACF,CAAE,MAAOO,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAL,aAAaA,CAACH,YAAY,CAAEE,UAAU,CAAE,CAC5C,GAAI,CACF;AACA,KAAM,CAAAQ,QAAQ,CAAGhB,IAAI,CAACiB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,IAAI,CAACC,sBAAsB,CAACH,QAAQ,CAAEV,YAAY,CAAEE,UAAU,CAAC,CAE/D;AACA,IAAI,CAACY,kBAAkB,CAACJ,QAAQ,CAAEV,YAAY,CAAEE,UAAU,CAAC,CAE3D;AACA,KAAM,CAAAa,QAAQ,qDAAAR,MAAA,CAAe,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CAC/DvB,IAAI,CAACwB,SAAS,CAACR,QAAQ,CAAEK,QAAQ,CAAC,CAElC,MAAO,CACLI,OAAO,CAAE,IAAI,CACbJ,QAAQ,CACRK,OAAO,yCAAAb,MAAA,CAAiBQ,QAAQ,CAClC,CAAC,CACH,CAAE,MAAOP,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAa,eAAeA,CAACX,QAAQ,CAAEY,YAAY,CAAEC,QAAQ,CAAE,CAChD;AACA,KAAM,CAAAC,OAAO,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,KAAK,CAAC,CAEnE;AACA,KAAM,CAAAC,IAAI,CAAG,CAACD,OAAO,CAAC,CAEtBF,YAAY,CAACI,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACE,IAAI,CACtB,KAAM,CAAAC,GAAG,CAAG,CACV,IAAI,CAACC,WAAW,CAACF,IAAI,CAACG,EAAE,CAAC,CACzB,IAAI,CAACD,WAAW,CAACF,IAAI,CAACI,EAAE,CAAC,CACzB,IAAI,CAACF,WAAW,CAACF,IAAI,CAACK,GAAG,CAAC,CAC1B,IAAI,CAACH,WAAW,CAACF,IAAI,CAACM,EAAE,CAAC,CACzB,IAAI,CAACJ,WAAW,CAACF,IAAI,CAACO,IAAI,CAAC,CAC3B,IAAI,CAACL,WAAW,CAACF,IAAI,CAACQ,IAAI,CAAC,CAC3B,IAAI,CAACN,WAAW,CAACF,IAAI,CAACS,GAAG,CAAC,CAC3B,CACDb,IAAI,CAACc,IAAI,CAACT,GAAG,CAAC,CAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAU,SAAS,CAAG9C,IAAI,CAACiB,KAAK,CAAC8B,YAAY,CAAChB,IAAI,CAAC,CAE/C;AACA,KAAM,CAAAiB,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,CAAE,CAAC,CAAI;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAI;AAAA,CACf,CACDH,SAAS,CAAC,OAAO,CAAC,CAAGE,SAAS,CAE9B;AACA,IAAI,CAACE,cAAc,CAACJ,SAAS,CAAEhB,OAAO,CAACqB,MAAM,CAAC,CAE9C;AACAnD,IAAI,CAACiB,KAAK,CAACmC,iBAAiB,CAACpC,QAAQ,CAAE8B,SAAS,CAAEjB,QAAQ,CAACwB,SAAS,CAAC,CACvE,CAEA;AACAlC,sBAAsBA,CAACH,QAAQ,CAAEV,YAAY,CAAEE,UAAU,CAAE,CACzD;AACA,KAAM,CAAAsB,OAAO,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,OAAO,CAAE,MAAM,CAAE,MAAM,CAAE,KAAK,CAAC,CAEzE;AACA,KAAM,CAAAC,IAAI,CAAG,CAACD,OAAO,CAAC,CAEtB;AACA,KAAM,CAAAwB,UAAU,CAAG,CACjB,CAAEC,GAAG,CAAE,eAAe,CAAEC,KAAK,CAAE,MAAO,CAAC,CACvC,CAAED,GAAG,CAAE,mBAAmB,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC3C,CAAED,GAAG,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAClC,CAED;AACAF,UAAU,CAACtB,OAAO,CAACH,QAAQ,EAAI,CAC7B,KAAM,CAAAD,YAAY,CAAGtB,YAAY,CAACuB,QAAQ,CAAC0B,GAAG,CAAC,CAC/C,GAAI3B,YAAY,EAAIA,YAAY,CAACuB,MAAM,CAAG,CAAC,CAAE,CAC3C;AACApB,IAAI,CAACc,IAAI,CAAC,QAAAhC,MAAA,CACDgB,QAAQ,CAAC2B,KAAK,SACrB,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAC3B,CAAC,CAEF;AACA5B,YAAY,CAACI,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CACpC,KAAM,CAAAC,IAAI,CAAGF,IAAI,CAACE,IAAI,CACtB,KAAM,CAAAC,GAAG,CAAG,CACVP,QAAQ,CAAC2B,KAAK,CACd,IAAI,CAACnB,WAAW,CAACF,IAAI,CAACG,EAAE,CAAC,CACzB,IAAI,CAACD,WAAW,CAACF,IAAI,CAACI,EAAE,CAAC,CACzB,IAAI,CAACF,WAAW,CAACF,IAAI,CAACK,GAAG,CAAC,CAC1B,IAAI,CAACH,WAAW,CAACF,IAAI,CAACM,EAAE,CAAC,CACzB,IAAI,CAACJ,WAAW,CAACF,IAAI,CAACO,IAAI,CAAC,CAC3B,IAAI,CAACL,WAAW,CAACF,IAAI,CAACQ,IAAI,CAAC,CAC3B,IAAI,CAACN,WAAW,CAACF,IAAI,CAACS,GAAG,CAAC,CAC3B,CACDb,IAAI,CAACc,IAAI,CAACT,GAAG,CAAC,CAChB,CAAC,CAAC,CAEF;AACAL,IAAI,CAACc,IAAI,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CAC7C,CACF,CAAC,CAAC,CAEF;AACAd,IAAI,CAACc,IAAI,CAAC,CAAC,QAAQ,CAAG,IAAI,CAACY,cAAc,CAAC,GAAI,CAAAlC,IAAI,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CACnFQ,IAAI,CAACc,IAAI,CAAC,oCAAAhC,MAAA,CAAWL,UAAU,CAACkD,UAAU,EAAI,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CAC1E3B,IAAI,CAACc,IAAI,CAAC,wBAAAhC,MAAA,CAASL,UAAU,CAACmD,WAAW,EAAI,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CAEzE;AACA,KAAM,CAAAb,SAAS,CAAG9C,IAAI,CAACiB,KAAK,CAAC8B,YAAY,CAAChB,IAAI,CAAC,CAE/C;AACA,KAAM,CAAAiB,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,CAAE,CAAC,CAAI;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAI;AAAA,CACf,CACDH,SAAS,CAAC,OAAO,CAAC,CAAGE,SAAS,CAE9B;AACA,IAAI,CAACY,wBAAwB,CAACd,SAAS,CAAEf,IAAI,CAACoB,MAAM,CAAErB,OAAO,CAACqB,MAAM,CAAC,CAErE;AACAnD,IAAI,CAACiB,KAAK,CAACmC,iBAAiB,CAACpC,QAAQ,CAAE8B,SAAS,CAAE,QAAQ,CAAC,CAC7D,CAEA;AACA1B,kBAAkBA,CAACJ,QAAQ,CAAEV,YAAY,CAAEE,UAAU,CAAE,CACrD,KAAM,CAAAqD,WAAW,CAAG,CAClB,CAAC,UAAU,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACxB,CAAC,MAAM,CAAE,IAAI,CAACJ,cAAc,CAAC,GAAI,CAAAlC,IAAI,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAC,CACjD,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAChB,CAAC,IAAI,CAAE,OAAO,CAAE,MAAM,CAAE,EAAE,CAAC,CAC3B,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CACjB,CAED;AACA,KAAM,CAAA+B,UAAU,CAAG,CACjB,CAAEC,GAAG,CAAE,eAAe,CAAEC,KAAK,CAAE,MAAO,CAAC,CACvC,CAAED,GAAG,CAAE,mBAAmB,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC3C,CAAED,GAAG,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAClC,CAEDF,UAAU,CAACtB,OAAO,CAACH,QAAQ,EAAI,CAC7B,KAAM,CAAAD,YAAY,CAAGtB,YAAY,CAACuB,QAAQ,CAAC0B,GAAG,CAAC,EAAI,EAAE,CACrD,KAAM,CAAAO,KAAK,CAAGlC,YAAY,CAACuB,MAAM,CACjC,KAAM,CAAAY,MAAM,CAAGnC,YAAY,CAACoC,MAAM,CAAC,CAACC,GAAG,CAAEhC,IAAI,GAAK,CAChD,KAAM,CAAAiC,CAAC,CAAGjC,IAAI,CAACE,IAAI,CAACM,EAAE,CACtB,MAAO,CAAAwB,GAAG,EAAI,IAAI,CAACE,WAAW,CAACD,CAAC,CAAC,EAAI,CAAC,CAAC,CACzC,CAAC,CAAE,CAAC,CAAC,CAELL,WAAW,CAAChB,IAAI,CAAC,CAAChB,QAAQ,CAAC2B,KAAK,CAAEM,KAAK,CAAEC,MAAM,CAAE,EAAE,CAAC,CAAC,CACvD,CAAC,CAAC,CAEFF,WAAW,CAAChB,IAAI,CAAC,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAAC,CAClCgB,WAAW,CAAChB,IAAI,CAAC,CAAC,IAAI,CAAErC,UAAU,CAACkD,UAAU,CAAElD,UAAU,CAACmD,WAAW,CAAE,EAAE,CAAC,CAAC,CAE3E,KAAM,CAAAS,gBAAgB,CAAGpE,IAAI,CAACiB,KAAK,CAAC8B,YAAY,CAACc,WAAW,CAAC,CAE7D;AACAO,gBAAgB,CAAC,OAAO,CAAC,CAAG,CAC1B,CAAEnB,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACX,CAAEA,GAAG,CAAE,EAAG,CAAC,CACZ,CAEDjD,IAAI,CAACiB,KAAK,CAACmC,iBAAiB,CAACpC,QAAQ,CAAEoD,gBAAgB,CAAE,MAAM,CAAC,CAClE,CAEA;AACA,KAAM,CAAA1D,WAAWA,CAACJ,YAAY,CAAEE,UAAU,CAAE,CAC1C,GAAI,CACF;AACA;AACAO,OAAO,CAACsD,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,oBAAoB,CAACjE,YAAY,CAAEE,UAAU,CAAC,CACpE,KAAM,CAAAgE,OAAO,CAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CACjD,KAAM,CAAAK,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACJ,OAAO,CAAC,CAAE,CAAEK,IAAI,CAAE,kBAAmB,CAAC,CAAC,CAC9D,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,qDAAAxE,MAAA,CAAe,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CAC3D2D,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACLrD,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,sBACX,CAAC,CACH,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAH,WAAWA,CAACL,YAAY,CAAEE,UAAU,CAAE,CAC1C,GAAI,CACF,GAAI,CAAAmF,UAAU,CAAG,QAAQ,CAAE;AAE3B;AACA,KAAM,CAAArC,UAAU,CAAG,CACjB,CAAEC,GAAG,CAAE,eAAe,CAAEC,KAAK,CAAE,MAAO,CAAC,CACvC,CAAED,GAAG,CAAE,mBAAmB,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC3C,CAAED,GAAG,CAAE,SAAS,CAAEC,KAAK,CAAE,MAAO,CAAC,CAClC,CAEDF,UAAU,CAACtB,OAAO,CAACH,QAAQ,EAAI,CAC7B,KAAM,CAAAD,YAAY,CAAGtB,YAAY,CAACuB,QAAQ,CAAC0B,GAAG,CAAC,CAC/C,GAAI3B,YAAY,EAAIA,YAAY,CAACuB,MAAM,CAAG,CAAC,CAAE,CAC3CwC,UAAU,WAAA9E,MAAA,CAAagB,QAAQ,CAAC2B,KAAK,UAAQ,CAC7CmC,UAAU,EAAI,iCAAiC,CAE/C/D,YAAY,CAACI,OAAO,CAACC,IAAI,EAAI,CAC3B,KAAM,CAAAE,IAAI,CAAGF,IAAI,CAACE,IAAI,CACtB,KAAM,CAAAC,GAAG,CAAG,CACV,IAAI,CAACwD,cAAc,CAACzD,IAAI,CAACG,EAAE,CAAC,CAC5B,IAAI,CAACsD,cAAc,CAACzD,IAAI,CAACI,EAAE,CAAC,CAC5B,IAAI,CAACqD,cAAc,CAACzD,IAAI,CAACK,GAAG,CAAC,CAC7B,IAAI,CAACoD,cAAc,CAACzD,IAAI,CAACM,EAAE,CAAC,CAC5B,IAAI,CAACmD,cAAc,CAACzD,IAAI,CAACO,IAAI,CAAC,CAC9B,IAAI,CAACkD,cAAc,CAACzD,IAAI,CAACQ,IAAI,CAAC,CAC9B,IAAI,CAACiD,cAAc,CAACzD,IAAI,CAACS,GAAG,CAAC,CAC9B,CAACiD,IAAI,CAAC,GAAG,CAAC,CACXF,UAAU,EAAIvD,GAAG,CAAG,IAAI,CAC1B,CAAC,CAAC,CACFuD,UAAU,EAAI,IAAI,CACpB,CACF,CAAC,CAAC,CAEF;AACAA,UAAU,EAAI,kBAAkB,CAChCA,UAAU,8BAAA9E,MAAA,CAAY,IAAI,CAAC4C,cAAc,CAAC,GAAI,CAAAlC,IAAI,CAAC,CAAC,CAAC,MAAI,CACzDoE,UAAU,oCAAA9E,MAAA,CAAaL,UAAU,CAACkD,UAAU,MAAI,CAChDiC,UAAU,wBAAA9E,MAAA,CAAWL,UAAU,CAACmD,WAAW,MAAI,CAE/C;AACA,KAAM,CAAAgB,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACe,UAAU,CAAC,CAAE,CAAEd,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,qDAAAxE,MAAA,CAAe,IAAI,CAACS,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,QAAM,CAC1D2D,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACLrD,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,aACX,CAAC,CACH,CAAE,MAAOZ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAuB,WAAWA,CAACyD,KAAK,CAAE,CACjB,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,CACzD,MAAO,EAAE,CACX,CAEA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACE,IAAI,GAAKD,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACE,IAAI,CAC/C,GAAIF,KAAK,CAACG,QAAQ,GAAKF,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACG,QAAQ,CACvD,GAAIH,KAAK,CAACA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAI,MAAM,CAACJ,KAAK,CAAC,CACtB,CAEA,MAAO,CAAAI,MAAM,CAACJ,KAAK,CAAC,CACtB,CAEA;AACA3B,WAAWA,CAAC2B,KAAK,CAAE,CACjB,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,MAAO,EAAC,CACnE,KAAM,CAAAK,GAAG,CAAG,MAAO,CAAAL,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAAGM,MAAM,CAACN,KAAK,CAAC,CAC7D,MAAO,CAAAO,KAAK,CAACF,GAAG,CAAC,CAAG,CAAC,CAAGA,GAAG,CAC7B,CAEA;AACAP,cAAcA,CAACE,KAAK,CAAE,CACpB,KAAM,CAAAQ,cAAc,CAAG,IAAI,CAACjE,WAAW,CAACyD,KAAK,CAAC,CAC9C,GAAIQ,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAID,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAID,cAAc,CAACC,QAAQ,CAAC,IAAI,CAAC,CAAE,CACjG,WAAA1F,MAAA,CAAWyF,cAAc,CAACE,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,OAC/C,CACA,MAAO,CAAAF,cAAc,CACvB,CAEA;AACApD,cAAcA,CAACJ,SAAS,CAAE2D,QAAQ,CAAE,CAClC,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,QAAQ,CAAEC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAC,OAAO,CAAG3G,IAAI,CAACiB,KAAK,CAAC2F,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAEJ,CAAE,CAAC,CAAC,CACtD,GAAI,CAAC5D,SAAS,CAAC6D,OAAO,CAAC,CAAE,SAEzB7D,SAAS,CAAC6D,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CAEA;AACA5D,wBAAwBA,CAACd,SAAS,CAAE2E,QAAQ,CAAEhB,QAAQ,CAAE,CACtD;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGD,QAAQ,CAAEC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAC,OAAO,CAAG3G,IAAI,CAACiB,KAAK,CAAC2F,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAEJ,CAAE,CAAC,CAAC,CACtD,GAAI,CAAC5D,SAAS,CAAC6D,OAAO,CAAC,CAAE,SAEzB7D,SAAS,CAAC6D,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CAEA;AACA,IAAK,GAAI,CAAAX,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGY,QAAQ,CAAEZ,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAa,YAAY,CAAG1H,IAAI,CAACiB,KAAK,CAAC2F,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CAC3D,GAAI,CAAChE,SAAS,CAAC4E,YAAY,CAAC,CAAE,SAE9B,KAAM,CAAAC,SAAS,CAAG7E,SAAS,CAAC4E,YAAY,CAAC,CAACE,CAAC,CAC3C,GAAID,SAAS,EAAI,MAAO,CAAAA,SAAS,GAAK,QAAQ,EAAIA,SAAS,CAACE,UAAU,CAAC,KAAK,CAAC,CAAE,CAC7E;AACA,IAAK,GAAI,CAAAf,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGL,QAAQ,CAAEK,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAH,OAAO,CAAG3G,IAAI,CAACiB,KAAK,CAAC2F,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAEA,CAAE,CAAC,CAAC,CACtD,GAAI,CAAChE,SAAS,CAAC6D,OAAO,CAAC,CAAE,SAEzB7D,SAAS,CAAC6D,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CACF,CACF,CAEA;AACAlG,UAAUA,CAACwG,IAAI,CAAE,CACf,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAG/B,MAAM,CAAC4B,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGlC,MAAM,CAAC4B,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,SAAAtH,MAAA,CAAUkH,IAAI,EAAAlH,MAAA,CAAGoH,KAAK,EAAApH,MAAA,CAAGuH,GAAG,EAC9B,CAEA;AACA3E,cAAcA,CAACqE,IAAI,CAAE,CACnB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAG/B,MAAM,CAAC4B,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGlC,MAAM,CAAC4B,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAG,KAAK,CAAGpC,MAAM,CAAC4B,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAK,OAAO,CAAGtC,MAAM,CAAC4B,IAAI,CAACW,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,SAAAtH,MAAA,CAAUkH,IAAI,MAAAlH,MAAA,CAAIoH,KAAK,MAAApH,MAAA,CAAIuH,GAAG,MAAAvH,MAAA,CAAIyH,KAAK,MAAAzH,MAAA,CAAI2H,OAAO,EACpD,CAEA;AACAjE,oBAAoBA,CAACjE,YAAY,CAAEE,UAAU,CAAE,CAC7C,MAAO,CACLkI,UAAU,CAAE,CACVC,SAAS,CAAE,GAAI,CAAApH,IAAI,CAAC,CAAC,CAACqH,WAAW,CAAC,CAAC,CACnClF,UAAU,CAAElD,UAAU,CAACkD,UAAU,CACjCC,WAAW,CAAEnD,UAAU,CAACmD,WAC1B,CAAC,CACDxB,IAAI,CAAE7B,YACR,CAAC,CACH,CACF,CAEA,cAAe,IAAI,CAAAL,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}