{"ast": null, "code": "import React,{useState,useEffect}from'react';import'../styles/HomePage.css';import GlobalFileSelector from'../../components/GlobalFileSelector';import routeGuard from'../../auth/utils/routeGuards';import{ROLES}from'../../auth/utils/rolePermissions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const HomePage=_ref=>{let{onNavigate,isAuthenticated,currentUser,userRole}=_ref;const[currentTime,setCurrentTime]=useState(new Date());const[showFileSelector,setShowFileSelector]=useState(false);const[currentFileName,setCurrentFileName]=useState('附件1：2025年开发中心重点工作跟踪表-0414.xlsx');useEffect(()=>{const timer=setInterval(()=>{setCurrentTime(new Date());},1000);return()=>clearInterval(timer);},[]);const navigationItems=[{id:'work-target',title:'工作目标管理责任书',subtitle:'Work Target Management',icon:'🎯',description:'关键指标·质量指标·重点工作管理',color:'#20ff4d'},{id:'work-tracking',title:'重点工作跟踪-填写表',subtitle:'Work Tracking Form',icon:'📊',description:'月度工作计划与完成情况跟踪',color:'#00d4aa'},{id:'world-class',title:'对标世界一流举措-提报版',subtitle:'World-Class Benchmarking',icon:'🌍',description:'世界一流标准对标与举措实施',color:'#4dd0ff'},{id:'monthly-kpi',title:'月度重点KPI',subtitle:'Monthly Key Performance Indicators',icon:'📈',description:'KPI指标监控与分析评估',color:'#ff6b4d'},{id:'project-one',title:'1号项目责任状',subtitle:'Project One Responsibility',icon:'🚀',description:'重点项目推进与责任落实',color:'#ff4dff'},{id:'module-six',title:'开发中心二级部门工作目标管理责任书',subtitle:'Department Target Management',icon:'🏢',description:'各部门目标管理与绩效考核',color:'#ffff4d'}];// 获取导航项（不再包含用户管理）\nconst getNavigationItems=()=>{return navigationItems;};const handleNavigation=itemId=>{// 检查权限\nconst accessResult=routeGuard.canAccess(itemId);if(accessResult.allowed){onNavigate(itemId);}else{// 显示权限不足提示\nshowPermissionDeniedNotification(accessResult.message);}};const showPermissionDeniedNotification=message=>{// 创建权限不足提示\nconst notification=document.createElement('div');notification.className='auth-permission-denied';notification.innerHTML=\"\\n      <span class=\\\"permission-denied-icon\\\">\\uD83D\\uDEAB</span>\\n      <span class=\\\"permission-denied-text\\\">\".concat(message,\"</span>\\n    \");// 添加样式\nnotification.style.cssText=\"\\n      position: fixed;\\n      top: 50%;\\n      left: 50%;\\n      transform: translate(-50%, -50%);\\n      background: rgba(255, 69, 58, 0.95);\\n      color: white;\\n      padding: 20px 30px;\\n      border-radius: 15px;\\n      font-size: 16px;\\n      font-weight: 600;\\n      z-index: 10000;\\n      display: flex;\\n      align-items: center;\\n      gap: 10px;\\n      backdrop-filter: blur(10px);\\n      border: 1px solid rgba(255, 69, 58, 0.3);\\n      box-shadow: 0 10px 30px rgba(255, 69, 58, 0.3);\\n      animation: permissionDeniedShake 0.5s ease-in-out;\\n    \";// 添加动画样式\nconst style=document.createElement('style');style.textContent=\"\\n      @keyframes permissionDeniedShake {\\n        0%, 100% { transform: translate(-50%, -50%) scale(1); }\\n        25% { transform: translate(-50%, -50%) scale(1.05) rotate(-1deg); }\\n        75% { transform: translate(-50%, -50%) scale(1.05) rotate(1deg); }\\n      }\\n    \";document.head.appendChild(style);document.body.appendChild(notification);// 3秒后自动移除\nsetTimeout(()=>{if(notification.parentNode){notification.parentNode.removeChild(notification);}if(style.parentNode){style.parentNode.removeChild(style);}},3000);};// 检查更换文件按钮权限\nconst canChangeFile=()=>{return routeGuard.checkButtonPermission('change_file',userRole);};const handleFileChanged=fileName=>{setCurrentFileName(fileName);// 可以在这里添加其他处理逻辑，比如刷新数据等\n};return/*#__PURE__*/_jsxs(\"div\",{className:\"homepage\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"background-animation\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"particle\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid-lines\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"time-display\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"current-time\",children:currentTime.toLocaleString('zh-CN',{year:'numeric',month:'2-digit',day:'2-digit',hour:'2-digit',minute:'2-digit',second:'2-digit'})}),/*#__PURE__*/_jsx(\"div\",{className:\"system-status\",children:isAuthenticated?'系统运行正常':'未登录状态'}),canChangeFile()?/*#__PURE__*/_jsx(\"button\",{className:\"file-selector-btn\",onClick:()=>setShowFileSelector(true),title:\"\\u66F4\\u6362Excel\\u6587\\u4EF6\",children:\"\\uD83D\\uDCC1 \\u66F4\\u6362\\u6587\\u4EF6\"}):isAuthenticated?/*#__PURE__*/_jsx(\"button\",{className:\"file-selector-btn disabled\",onClick:()=>showPermissionDeniedNotification('您没有更换文件的权限'),title:\"\\u6743\\u9650\\u4E0D\\u8DB3\",children:\"\\uD83D\\uDD12 \\u66F4\\u6362\\u6587\\u4EF6\"}):null]}),/*#__PURE__*/_jsx(\"div\",{className:\"main-header\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"title-container\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"\\u667A\\u80FD\\u9A71\\u52A8\\xB7\\u8D28\\u6548\\u53CC\\u5347\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"sub-title\",children:\"2025\\u5F00\\u53D1\\u4E2D\\u5FC3\\u91CD\\u70B9\\u9879\\u76EE\\u6708\\u5EA6\\u7EE9\\u6548\\u8DDF\\u8E2A\\u62A5\\u544A\"}),/*#__PURE__*/_jsx(\"div\",{className:\"title-divider\"}),/*#__PURE__*/_jsx(\"p\",{className:\"system-description\",children:\"\\u542B\\u4E16\\u754C\\u4E00\\u6D41\\u5BF9\\u6807\\u4F53\\u7CFB \\xB7 \\u667A\\u80FD\\u5316\\u6570\\u636E\\u7BA1\\u7406\\u5E73\\u53F0\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"navigation-grid\",children:getNavigationItems().map((item,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"nav-card\",style:{'--delay':\"\".concat(index*0.1,\"s\"),'--color':item.color},onClick:()=>handleNavigation(item.id),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-icon\",children:item.icon}),/*#__PURE__*/_jsx(\"div\",{className:\"card-number\",children:String(index+1).padStart(2,'0')})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-content\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"card-title\",children:item.title}),/*#__PURE__*/_jsx(\"p\",{className:\"card-subtitle\",children:item.subtitle}),/*#__PURE__*/_jsx(\"p\",{className:\"card-description\",children:item.description})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card-footer\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"enter-text\",children:\"\\u8FDB\\u5165\\u6A21\\u5757\"}),/*#__PURE__*/_jsx(\"span\",{className:\"arrow\",children:\"\\u2192\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"})]},item.id))}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-bar\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"\\u6570\\u636E\\u540C\\u6B65\\u72B6\\u6001:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value online\",children:\"\\u5728\\u7EBF\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"Excel\\u6587\\u4EF6:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value\",children:currentFileName})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"status-info\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"status-label\",children:\"\\u6A21\\u5757\\u6570\\u91CF:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"status-value\",children:getNavigationItems().length})]})]}),/*#__PURE__*/_jsx(GlobalFileSelector,{isVisible:showFileSelector,onClose:()=>setShowFileSelector(false),onFileChanged:handleFileChanged})]});};export default HomePage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "GlobalFileSelector", "routeGuard", "ROLES", "jsx", "_jsx", "jsxs", "_jsxs", "HomePage", "_ref", "onNavigate", "isAuthenticated", "currentUser", "userRole", "currentTime", "setCurrentTime", "Date", "showFileSelector", "setShowFileSelector", "currentFileName", "setCurrentFileName", "timer", "setInterval", "clearInterval", "navigationItems", "id", "title", "subtitle", "icon", "description", "color", "getNavigationItems", "handleNavigation", "itemId", "accessResult", "canAccess", "allowed", "showPermissionDeniedNotification", "message", "notification", "document", "createElement", "className", "innerHTML", "concat", "style", "cssText", "textContent", "head", "append<PERSON><PERSON><PERSON>", "body", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "canChangeFile", "checkButtonPermission", "handleFileChanged", "fileName", "children", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "onClick", "map", "item", "index", "String", "padStart", "length", "isVisible", "onClose", "onFileChanged"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块一/pages/HomePage.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport '../styles/HomePage.css';\r\nimport GlobalFileSelector from '../../components/GlobalFileSelector';\r\nimport routeGuard from '../../auth/utils/routeGuards';\r\nimport { ROLES } from '../../auth/utils/rolePermissions';\r\n\r\nconst HomePage = ({ onNavigate, isAuthenticated, currentUser, userRole }) => {\r\n  const [currentTime, setCurrentTime] = useState(new Date());\r\n  const [showFileSelector, setShowFileSelector] = useState(false);\r\n  const [currentFileName, setCurrentFileName] = useState('附件1：2025年开发中心重点工作跟踪表-0414.xlsx');\r\n\r\n  useEffect(() => {\r\n    const timer = setInterval(() => {\r\n      setCurrentTime(new Date());\r\n    }, 1000);\r\n\r\n    return () => clearInterval(timer);\r\n  }, []);\r\n\r\n  const navigationItems = [\r\n    {\r\n      id: 'work-target',\r\n      title: '工作目标管理责任书',\r\n      subtitle: 'Work Target Management',\r\n      icon: '🎯',\r\n      description: '关键指标·质量指标·重点工作管理',\r\n      color: '#20ff4d'\r\n    },\r\n    {\r\n      id: 'work-tracking',\r\n      title: '重点工作跟踪-填写表',\r\n      subtitle: 'Work Tracking Form',\r\n      icon: '📊',\r\n      description: '月度工作计划与完成情况跟踪',\r\n      color: '#00d4aa'\r\n    },\r\n    {\r\n      id: 'world-class',\r\n      title: '对标世界一流举措-提报版',\r\n      subtitle: 'World-Class Benchmarking',\r\n      icon: '🌍',\r\n      description: '世界一流标准对标与举措实施',\r\n      color: '#4dd0ff'\r\n    },\r\n    {\r\n      id: 'monthly-kpi',\r\n      title: '月度重点KPI',\r\n      subtitle: 'Monthly Key Performance Indicators',\r\n      icon: '📈',\r\n      description: 'KPI指标监控与分析评估',\r\n      color: '#ff6b4d'\r\n    },\r\n    {\r\n      id: 'project-one',\r\n      title: '1号项目责任状',\r\n      subtitle: 'Project One Responsibility',\r\n      icon: '🚀',\r\n      description: '重点项目推进与责任落实',\r\n      color: '#ff4dff'\r\n    },\r\n    {\r\n      id: 'module-six',\r\n      title: '开发中心二级部门工作目标管理责任书',\r\n      subtitle: 'Department Target Management',\r\n      icon: '🏢',\r\n      description: '各部门目标管理与绩效考核',\r\n      color: '#ffff4d'\r\n    }\r\n  ];\r\n\r\n  // 获取导航项（不再包含用户管理）\r\n  const getNavigationItems = () => {\r\n    return navigationItems;\r\n  };\r\n\r\n  const handleNavigation = (itemId) => {\r\n    // 检查权限\r\n    const accessResult = routeGuard.canAccess(itemId);\r\n\r\n    if (accessResult.allowed) {\r\n      onNavigate(itemId);\r\n    } else {\r\n      // 显示权限不足提示\r\n      showPermissionDeniedNotification(accessResult.message);\r\n    }\r\n  };\r\n\r\n  const showPermissionDeniedNotification = (message) => {\r\n    // 创建权限不足提示\r\n    const notification = document.createElement('div');\r\n    notification.className = 'auth-permission-denied';\r\n    notification.innerHTML = `\r\n      <span class=\"permission-denied-icon\">🚫</span>\r\n      <span class=\"permission-denied-text\">${message}</span>\r\n    `;\r\n\r\n    // 添加样式\r\n    notification.style.cssText = `\r\n      position: fixed;\r\n      top: 50%;\r\n      left: 50%;\r\n      transform: translate(-50%, -50%);\r\n      background: rgba(255, 69, 58, 0.95);\r\n      color: white;\r\n      padding: 20px 30px;\r\n      border-radius: 15px;\r\n      font-size: 16px;\r\n      font-weight: 600;\r\n      z-index: 10000;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n      backdrop-filter: blur(10px);\r\n      border: 1px solid rgba(255, 69, 58, 0.3);\r\n      box-shadow: 0 10px 30px rgba(255, 69, 58, 0.3);\r\n      animation: permissionDeniedShake 0.5s ease-in-out;\r\n    `;\r\n\r\n    // 添加动画样式\r\n    const style = document.createElement('style');\r\n    style.textContent = `\r\n      @keyframes permissionDeniedShake {\r\n        0%, 100% { transform: translate(-50%, -50%) scale(1); }\r\n        25% { transform: translate(-50%, -50%) scale(1.05) rotate(-1deg); }\r\n        75% { transform: translate(-50%, -50%) scale(1.05) rotate(1deg); }\r\n      }\r\n    `;\r\n    document.head.appendChild(style);\r\n\r\n    document.body.appendChild(notification);\r\n\r\n    // 3秒后自动移除\r\n    setTimeout(() => {\r\n      if (notification.parentNode) {\r\n        notification.parentNode.removeChild(notification);\r\n      }\r\n      if (style.parentNode) {\r\n        style.parentNode.removeChild(style);\r\n      }\r\n    }, 3000);\r\n  };\r\n\r\n  // 检查更换文件按钮权限\r\n  const canChangeFile = () => {\r\n    return routeGuard.checkButtonPermission('change_file', userRole);\r\n  };\r\n\r\n  const handleFileChanged = (fileName) => {\r\n    setCurrentFileName(fileName);\r\n    // 可以在这里添加其他处理逻辑，比如刷新数据等\r\n  };\r\n\r\n  return (\r\n    <div className=\"homepage\">\r\n      {/* 背景动画 */}\r\n      <div className=\"background-animation\">\r\n        <div className=\"particle\"></div>\r\n        <div className=\"particle\"></div>\r\n        <div className=\"particle\"></div>\r\n        <div className=\"grid-lines\"></div>\r\n      </div>\r\n\r\n      {/* 顶部时间显示 */}\r\n      <div className=\"time-display\">\r\n        <div className=\"current-time\">\r\n          {currentTime.toLocaleString('zh-CN', {\r\n            year: 'numeric',\r\n            month: '2-digit',\r\n            day: '2-digit',\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            second: '2-digit'\r\n          })}\r\n        </div>\r\n        <div className=\"system-status\">\r\n          {isAuthenticated ? '系统运行正常' : '未登录状态'}\r\n        </div>\r\n        {/* 更换文件按钮 - 根据权限显示 */}\r\n        {canChangeFile() ? (\r\n          <button\r\n            className=\"file-selector-btn\"\r\n            onClick={() => setShowFileSelector(true)}\r\n            title=\"更换Excel文件\"\r\n          >\r\n            📁 更换文件\r\n          </button>\r\n        ) : isAuthenticated ? (\r\n          <button\r\n            className=\"file-selector-btn disabled\"\r\n            onClick={() => showPermissionDeniedNotification('您没有更换文件的权限')}\r\n            title=\"权限不足\"\r\n          >\r\n            🔒 更换文件\r\n          </button>\r\n        ) : null}\r\n      </div>\r\n\r\n      {/* 主标题区域 */}\r\n      <div className=\"main-header\">\r\n        <div className=\"title-container\">\r\n          <h1 className=\"main-title\">\r\n            智能驱动·质效双升\r\n          </h1>\r\n          <h2 className=\"sub-title\">\r\n            2025开发中心重点项目月度绩效跟踪报告\r\n          </h2>\r\n          <div className=\"title-divider\"></div>\r\n          <p className=\"system-description\">\r\n            含世界一流对标体系 · 智能化数据管理平台\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 导航网格 */}\r\n      <div className=\"navigation-grid\">\r\n        {getNavigationItems().map((item, index) => (\r\n          <div\r\n            key={item.id}\r\n            className=\"nav-card\"\r\n            style={{ '--delay': `${index * 0.1}s`, '--color': item.color }}\r\n            onClick={() => handleNavigation(item.id)}\r\n          >\r\n            <div className=\"card-header\">\r\n              <div className=\"card-icon\">{item.icon}</div>\r\n              <div className=\"card-number\">{String(index + 1).padStart(2, '0')}</div>\r\n            </div>\r\n            <div className=\"card-content\">\r\n              <h3 className=\"card-title\">{item.title}</h3>\r\n              <p className=\"card-subtitle\">{item.subtitle}</p>\r\n              <p className=\"card-description\">{item.description}</p>\r\n            </div>\r\n            <div className=\"card-footer\">\r\n              <span className=\"enter-text\">进入模块</span>\r\n              <span className=\"arrow\">→</span>\r\n            </div>\r\n            <div className=\"card-glow\"></div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n\r\n      {/* 底部状态栏 */}\r\n      <div className=\"status-bar\">\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">数据同步状态:</span>\r\n          <span className=\"status-value online\">在线</span>\r\n        </div>\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">Excel文件:</span>\r\n          <span className=\"status-value\">{currentFileName}</span>\r\n        </div>\r\n        <div className=\"status-info\">\r\n          <span className=\"status-label\">模块数量:</span>\r\n          <span className=\"status-value\">{getNavigationItems().length}</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* 文件选择器 */}\r\n      <GlobalFileSelector\r\n        isVisible={showFileSelector}\r\n        onClose={() => setShowFileSelector(false)}\r\n        onFileChanged={handleFileChanged}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,wBAAwB,CAC/B,MAAO,CAAAC,kBAAkB,KAAM,qCAAqC,CACpE,MAAO,CAAAC,UAAU,KAAM,8BAA8B,CACrD,OAASC,KAAK,KAAQ,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzD,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAA4D,IAA3D,CAAEC,UAAU,CAAEC,eAAe,CAAEC,WAAW,CAAEC,QAAS,CAAC,CAAAJ,IAAA,CACtE,KAAM,CAACK,WAAW,CAAEC,cAAc,CAAC,CAAGhB,QAAQ,CAAC,GAAI,CAAAiB,IAAI,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnB,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAACoB,eAAe,CAAEC,kBAAkB,CAAC,CAAGrB,QAAQ,CAAC,gCAAgC,CAAC,CAExFC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqB,KAAK,CAAGC,WAAW,CAAC,IAAM,CAC9BP,cAAc,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,CAC5B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMO,aAAa,CAACF,KAAK,CAAC,CACnC,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAG,eAAe,CAAG,CACtB,CACEC,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,WAAW,CAClBC,QAAQ,CAAE,wBAAwB,CAClCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,kBAAkB,CAC/BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,eAAe,CACnBC,KAAK,CAAE,YAAY,CACnBC,QAAQ,CAAE,oBAAoB,CAC9BC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,eAAe,CAC5BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,cAAc,CACrBC,QAAQ,CAAE,0BAA0B,CACpCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,eAAe,CAC5BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,oCAAoC,CAC9CC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,cAAc,CAC3BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,aAAa,CACjBC,KAAK,CAAE,SAAS,CAChBC,QAAQ,CAAE,4BAA4B,CACtCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,aAAa,CAC1BC,KAAK,CAAE,SACT,CAAC,CACD,CACEL,EAAE,CAAE,YAAY,CAChBC,KAAK,CAAE,mBAAmB,CAC1BC,QAAQ,CAAE,8BAA8B,CACxCC,IAAI,CAAE,IAAI,CACVC,WAAW,CAAE,cAAc,CAC3BC,KAAK,CAAE,SACT,CAAC,CACF,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,MAAO,CAAAP,eAAe,CACxB,CAAC,CAED,KAAM,CAAAQ,gBAAgB,CAAIC,MAAM,EAAK,CACnC;AACA,KAAM,CAAAC,YAAY,CAAGhC,UAAU,CAACiC,SAAS,CAACF,MAAM,CAAC,CAEjD,GAAIC,YAAY,CAACE,OAAO,CAAE,CACxB1B,UAAU,CAACuB,MAAM,CAAC,CACpB,CAAC,IAAM,CACL;AACAI,gCAAgC,CAACH,YAAY,CAACI,OAAO,CAAC,CACxD,CACF,CAAC,CAED,KAAM,CAAAD,gCAAgC,CAAIC,OAAO,EAAK,CACpD;AACA,KAAM,CAAAC,YAAY,CAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,CAClDF,YAAY,CAACG,SAAS,CAAG,wBAAwB,CACjDH,YAAY,CAACI,SAAS,qHAAAC,MAAA,CAEmBN,OAAO,iBAC/C,CAED;AACAC,YAAY,CAACM,KAAK,CAACC,OAAO,ujBAmBzB,CAED;AACA,KAAM,CAAAD,KAAK,CAAGL,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC,CAC7CI,KAAK,CAACE,WAAW,sRAMhB,CACDP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACJ,KAAK,CAAC,CAEhCL,QAAQ,CAACU,IAAI,CAACD,WAAW,CAACV,YAAY,CAAC,CAEvC;AACAY,UAAU,CAAC,IAAM,CACf,GAAIZ,YAAY,CAACa,UAAU,CAAE,CAC3Bb,YAAY,CAACa,UAAU,CAACC,WAAW,CAACd,YAAY,CAAC,CACnD,CACA,GAAIM,KAAK,CAACO,UAAU,CAAE,CACpBP,KAAK,CAACO,UAAU,CAACC,WAAW,CAACR,KAAK,CAAC,CACrC,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAC,CAED;AACA,KAAM,CAAAS,aAAa,CAAGA,CAAA,GAAM,CAC1B,MAAO,CAAApD,UAAU,CAACqD,qBAAqB,CAAC,aAAa,CAAE1C,QAAQ,CAAC,CAClE,CAAC,CAED,KAAM,CAAA2C,iBAAiB,CAAIC,QAAQ,EAAK,CACtCrC,kBAAkB,CAACqC,QAAQ,CAAC,CAC5B;AACF,CAAC,CAED,mBACElD,KAAA,QAAKmC,SAAS,CAAC,UAAU,CAAAgB,QAAA,eAEvBnD,KAAA,QAAKmC,SAAS,CAAC,sBAAsB,CAAAgB,QAAA,eACnCrD,IAAA,QAAKqC,SAAS,CAAC,UAAU,CAAM,CAAC,cAChCrC,IAAA,QAAKqC,SAAS,CAAC,UAAU,CAAM,CAAC,cAChCrC,IAAA,QAAKqC,SAAS,CAAC,UAAU,CAAM,CAAC,cAChCrC,IAAA,QAAKqC,SAAS,CAAC,YAAY,CAAM,CAAC,EAC/B,CAAC,cAGNnC,KAAA,QAAKmC,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3BrD,IAAA,QAAKqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAC1B5C,WAAW,CAAC6C,cAAc,CAAC,OAAO,CAAE,CACnCC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SACV,CAAC,CAAC,CACC,CAAC,cACN5D,IAAA,QAAKqC,SAAS,CAAC,eAAe,CAAAgB,QAAA,CAC3B/C,eAAe,CAAG,QAAQ,CAAG,OAAO,CAClC,CAAC,CAEL2C,aAAa,CAAC,CAAC,cACdjD,IAAA,WACEqC,SAAS,CAAC,mBAAmB,CAC7BwB,OAAO,CAAEA,CAAA,GAAMhD,mBAAmB,CAAC,IAAI,CAAE,CACzCQ,KAAK,CAAC,+BAAW,CAAAgC,QAAA,CAClB,uCAED,CAAQ,CAAC,CACP/C,eAAe,cACjBN,IAAA,WACEqC,SAAS,CAAC,4BAA4B,CACtCwB,OAAO,CAAEA,CAAA,GAAM7B,gCAAgC,CAAC,YAAY,CAAE,CAC9DX,KAAK,CAAC,0BAAM,CAAAgC,QAAA,CACb,uCAED,CAAQ,CAAC,CACP,IAAI,EACL,CAAC,cAGNrD,IAAA,QAAKqC,SAAS,CAAC,aAAa,CAAAgB,QAAA,cAC1BnD,KAAA,QAAKmC,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,eAC9BrD,IAAA,OAAIqC,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,sDAE3B,CAAI,CAAC,cACLrD,IAAA,OAAIqC,SAAS,CAAC,WAAW,CAAAgB,QAAA,CAAC,sGAE1B,CAAI,CAAC,cACLrD,IAAA,QAAKqC,SAAS,CAAC,eAAe,CAAM,CAAC,cACrCrC,IAAA,MAAGqC,SAAS,CAAC,oBAAoB,CAAAgB,QAAA,CAAC,oHAElC,CAAG,CAAC,EACD,CAAC,CACH,CAAC,cAGNrD,IAAA,QAAKqC,SAAS,CAAC,iBAAiB,CAAAgB,QAAA,CAC7B3B,kBAAkB,CAAC,CAAC,CAACoC,GAAG,CAAC,CAACC,IAAI,CAAEC,KAAK,gBACpC9D,KAAA,QAEEmC,SAAS,CAAC,UAAU,CACpBG,KAAK,CAAE,CAAE,SAAS,IAAAD,MAAA,CAAKyB,KAAK,CAAG,GAAG,KAAG,CAAE,SAAS,CAAED,IAAI,CAACtC,KAAM,CAAE,CAC/DoC,OAAO,CAAEA,CAAA,GAAMlC,gBAAgB,CAACoC,IAAI,CAAC3C,EAAE,CAAE,CAAAiC,QAAA,eAEzCnD,KAAA,QAAKmC,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BrD,IAAA,QAAKqC,SAAS,CAAC,WAAW,CAAAgB,QAAA,CAAEU,IAAI,CAACxC,IAAI,CAAM,CAAC,cAC5CvB,IAAA,QAAKqC,SAAS,CAAC,aAAa,CAAAgB,QAAA,CAAEY,MAAM,CAACD,KAAK,CAAG,CAAC,CAAC,CAACE,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAM,CAAC,EACpE,CAAC,cACNhE,KAAA,QAAKmC,SAAS,CAAC,cAAc,CAAAgB,QAAA,eAC3BrD,IAAA,OAAIqC,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAEU,IAAI,CAAC1C,KAAK,CAAK,CAAC,cAC5CrB,IAAA,MAAGqC,SAAS,CAAC,eAAe,CAAAgB,QAAA,CAAEU,IAAI,CAACzC,QAAQ,CAAI,CAAC,cAChDtB,IAAA,MAAGqC,SAAS,CAAC,kBAAkB,CAAAgB,QAAA,CAAEU,IAAI,CAACvC,WAAW,CAAI,CAAC,EACnD,CAAC,cACNtB,KAAA,QAAKmC,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BrD,IAAA,SAAMqC,SAAS,CAAC,YAAY,CAAAgB,QAAA,CAAC,0BAAI,CAAM,CAAC,cACxCrD,IAAA,SAAMqC,SAAS,CAAC,OAAO,CAAAgB,QAAA,CAAC,QAAC,CAAM,CAAC,EAC7B,CAAC,cACNrD,IAAA,QAAKqC,SAAS,CAAC,WAAW,CAAM,CAAC,GAlB5B0B,IAAI,CAAC3C,EAmBP,CACN,CAAC,CACC,CAAC,cAGNlB,KAAA,QAAKmC,SAAS,CAAC,YAAY,CAAAgB,QAAA,eACzBnD,KAAA,QAAKmC,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BrD,IAAA,SAAMqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAC,uCAAO,CAAM,CAAC,cAC7CrD,IAAA,SAAMqC,SAAS,CAAC,qBAAqB,CAAAgB,QAAA,CAAC,cAAE,CAAM,CAAC,EAC5C,CAAC,cACNnD,KAAA,QAAKmC,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BrD,IAAA,SAAMqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAC,oBAAQ,CAAM,CAAC,cAC9CrD,IAAA,SAAMqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAEvC,eAAe,CAAO,CAAC,EACpD,CAAC,cACNZ,KAAA,QAAKmC,SAAS,CAAC,aAAa,CAAAgB,QAAA,eAC1BrD,IAAA,SAAMqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC3CrD,IAAA,SAAMqC,SAAS,CAAC,cAAc,CAAAgB,QAAA,CAAE3B,kBAAkB,CAAC,CAAC,CAACyC,MAAM,CAAO,CAAC,EAChE,CAAC,EACH,CAAC,cAGNnE,IAAA,CAACJ,kBAAkB,EACjBwE,SAAS,CAAExD,gBAAiB,CAC5ByD,OAAO,CAAEA,CAAA,GAAMxD,mBAAmB,CAAC,KAAK,CAAE,CAC1CyD,aAAa,CAAEnB,iBAAkB,CAClC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}