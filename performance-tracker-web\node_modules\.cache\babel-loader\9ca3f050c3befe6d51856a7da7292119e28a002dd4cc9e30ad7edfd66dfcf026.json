{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./styles/App.css';import HomePage from'./模块一/pages/HomePage';import WorkTarget from'./模块一/pages/WorkTarget';import WorkTracking from'./模块二/pages/WorkTracking';import WorldClass from'./模块三/pages/WorldClass';import MonthlyKPI from'./模块四/pages/MonthlyKPI';import ProjectOne from'./模块五/pages/ProjectOne';import ModuleSix from'./模块六/pages/ModuleSix';import Department from'./pages/Department';import LoginManager from'./auth/components/LoginManager';import UserManagement from'./auth/pages/UserManagement';import PersonalSettings from'./auth/pages/PersonalSettings';import authService from'./auth/services/authService';import routeGuard from'./auth/utils/routeGuards';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[currentPage,setCurrentPage]=useState('home');const[isLoading,setIsLoading]=useState(true);const[isAuthenticated,setIsAuthenticated]=useState(false);const[currentUser,setCurrentUser]=useState(null);useEffect(()=>{// 系统初始化\nconst timer=setTimeout(()=>{// 检查登录状态\nconst isLoggedIn=authService.checkAuthStatus();setIsAuthenticated(isLoggedIn);if(isLoggedIn){setCurrentUser(authService.getCurrentUser());}setIsLoading(false);},2000);return()=>clearTimeout(timer);},[]);// 处理认证状态变化\nconst handleAuthStateChange=(authenticated,user,navigateTo)=>{setIsAuthenticated(authenticated);setCurrentUser(user);// 如果有导航请求，执行导航\nif(navigateTo){setCurrentPage(navigateTo);}};// 页面导航处理（带权限检查）\nconst handleNavigation=pageName=>{const canAccess=routeGuard.canAccess(pageName);if(canAccess.allowed){setCurrentPage(pageName);}else{// 显示权限不足提示\nconsole.warn('页面访问被拒绝:',canAccess.message);}};const renderPage=()=>{// 传递认证信息和权限检查的导航函数给所有页面\nconst pageProps={onNavigate:handleNavigation,isAuthenticated,currentUser,userRole:currentUser===null||currentUser===void 0?void 0:currentUser.role};switch(currentPage){case'home':return/*#__PURE__*/_jsx(HomePage,_objectSpread({},pageProps));case'work-target':return/*#__PURE__*/_jsx(WorkTarget,_objectSpread({},pageProps));case'work-tracking':return/*#__PURE__*/_jsx(WorkTracking,_objectSpread({},pageProps));case'world-class':return/*#__PURE__*/_jsx(WorldClass,_objectSpread({},pageProps));case'monthly-kpi':return/*#__PURE__*/_jsx(MonthlyKPI,_objectSpread({},pageProps));case'project-one':return/*#__PURE__*/_jsx(ProjectOne,_objectSpread({},pageProps));case'module-six':return/*#__PURE__*/_jsx(ModuleSix,_objectSpread({},pageProps));case'department':return/*#__PURE__*/_jsx(Department,_objectSpread({},pageProps));case'user-management':return/*#__PURE__*/_jsx(UserManagement,_objectSpread({},pageProps));case'personal-settings':return/*#__PURE__*/_jsx(PersonalSettings,_objectSpread(_objectSpread({},pageProps),{},{onUserUpdate:handleAuthStateChange}));default:return/*#__PURE__*/_jsx(HomePage,_objectSpread({},pageProps));}};if(isLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"loading-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"loading-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"loading-logo\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"logo-text\",children:\"\\u667A\\u80FD\\u7EE9\\u6548\\u8DDF\\u8E2A\\u7CFB\\u7EDF\"}),/*#__PURE__*/_jsx(\"div\",{className:\"logo-subtitle\",children:\"INTELLIGENT PERFORMANCE TRACKING SYSTEM\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-progress\",children:/*#__PURE__*/_jsx(\"div\",{className:\"progress-bar\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"loading-status\",children:\"\\u7CFB\\u7EDF\\u521D\\u59CB\\u5316\\u4E2D...\"})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[currentPage==='home'&&/*#__PURE__*/_jsx(LoginManager,{onAuthStateChange:handleAuthStateChange}),renderPage()]});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "HomePage", "WorkTarget", "WorkTracking", "WorldClass", "MonthlyKPI", "ProjectOne", "ModuleSix", "Department", "Login<PERSON><PERSON>ger", "UserManagement", "PersonalSettings", "authService", "routeGuard", "jsx", "_jsx", "jsxs", "_jsxs", "App", "currentPage", "setCurrentPage", "isLoading", "setIsLoading", "isAuthenticated", "setIsAuthenticated", "currentUser", "setCurrentUser", "timer", "setTimeout", "isLoggedIn", "checkAuthStatus", "getCurrentUser", "clearTimeout", "handleAuthStateChange", "authenticated", "user", "navigateTo", "handleNavigation", "pageName", "canAccess", "allowed", "console", "warn", "message", "renderPage", "pageProps", "onNavigate", "userRole", "role", "_objectSpread", "onUserUpdate", "className", "children", "onAuthStateChange"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './styles/App.css';\nimport HomePage from './模块一/pages/HomePage';\nimport WorkTarget from './模块一/pages/WorkTarget';\nimport WorkTracking from './模块二/pages/WorkTracking';\nimport WorldClass from './模块三/pages/WorldClass';\nimport MonthlyKPI from './模块四/pages/MonthlyKPI';\nimport ProjectOne from './模块五/pages/ProjectOne';\nimport ModuleSix from './模块六/pages/ModuleSix';\nimport Department from './pages/Department';\nimport LoginManager from './auth/components/LoginManager';\nimport UserManagement from './auth/pages/UserManagement';\nimport PersonalSettings from './auth/pages/PersonalSettings';\nimport authService from './auth/services/authService';\nimport routeGuard from './auth/utils/routeGuards';\n\n\nfunction App() {\n  const [currentPage, setCurrentPage] = useState('home');\n  const [isLoading, setIsLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n  const [currentUser, setCurrentUser] = useState(null);\n\n  useEffect(() => {\n    // 系统初始化\n    const timer = setTimeout(() => {\n      // 检查登录状态\n      const isLoggedIn = authService.checkAuthStatus();\n      setIsAuthenticated(isLoggedIn);\n\n      if (isLoggedIn) {\n        setCurrentUser(authService.getCurrentUser());\n      }\n\n      setIsLoading(false);\n    }, 2000);\n\n    return () => clearTimeout(timer);\n  }, []);\n\n  // 处理认证状态变化\n  const handleAuthStateChange = (authenticated, user, navigateTo) => {\n    setIsAuthenticated(authenticated);\n    setCurrentUser(user);\n\n    // 如果有导航请求，执行导航\n    if (navigateTo) {\n      setCurrentPage(navigateTo);\n    }\n  };\n\n  // 页面导航处理（带权限检查）\n  const handleNavigation = (pageName) => {\n    const canAccess = routeGuard.canAccess(pageName);\n\n    if (canAccess.allowed) {\n      setCurrentPage(pageName);\n    } else {\n      // 显示权限不足提示\n      console.warn('页面访问被拒绝:', canAccess.message);\n    }\n  };\n\n  const renderPage = () => {\n    // 传递认证信息和权限检查的导航函数给所有页面\n    const pageProps = {\n      onNavigate: handleNavigation,\n      isAuthenticated,\n      currentUser,\n      userRole: currentUser?.role\n    };\n\n    switch (currentPage) {\n      case 'home':\n        return <HomePage {...pageProps} />;\n      case 'work-target':\n        return <WorkTarget {...pageProps} />;\n      case 'work-tracking':\n        return <WorkTracking {...pageProps} />;\n      case 'world-class':\n        return <WorldClass {...pageProps} />;\n      case 'monthly-kpi':\n        return <MonthlyKPI {...pageProps} />;\n      case 'project-one':\n        return <ProjectOne {...pageProps} />;\n      case 'module-six':\n        return <ModuleSix {...pageProps} />;\n      case 'department':\n        return <Department {...pageProps} />;\n      case 'user-management':\n        return <UserManagement {...pageProps} />;\n      case 'personal-settings':\n        return <PersonalSettings {...pageProps} onUserUpdate={handleAuthStateChange} />;\n\n      default:\n        return <HomePage {...pageProps} />;\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"loading-screen\">\n        <div className=\"loading-content\">\n          <div className=\"loading-logo\">\n            <div className=\"logo-text\">智能绩效跟踪系统</div>\n            <div className=\"logo-subtitle\">INTELLIGENT PERFORMANCE TRACKING SYSTEM</div>\n          </div>\n          <div className=\"loading-progress\">\n            <div className=\"progress-bar\"></div>\n          </div>\n          <div className=\"loading-status\">系统初始化中...</div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"App\">\n      {/* 登录管理器 - 只在首页显示 */}\n      {currentPage === 'home' && (\n        <LoginManager onAuthStateChange={handleAuthStateChange} />\n      )}\n\n      {/* 主要内容区域 */}\n      {renderPage()}\n    </div>\n  );\n}\n\nexport default App; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,kBAAkB,CACzB,MAAO,CAAAC,QAAQ,KAAM,sBAAsB,CAC3C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,YAAY,KAAM,0BAA0B,CACnD,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,SAAS,KAAM,uBAAuB,CAC7C,MAAO,CAAAC,UAAU,KAAM,oBAAoB,CAC3C,MAAO,CAAAC,YAAY,KAAM,gCAAgC,CACzD,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGlD,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,MAAM,CAAC,CACtD,KAAM,CAACsB,SAAS,CAAEC,YAAY,CAAC,CAAGvB,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACwB,eAAe,CAAEC,kBAAkB,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAC0B,WAAW,CAAEC,cAAc,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CAEpDC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA2B,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B;AACA,KAAM,CAAAC,UAAU,CAAGjB,WAAW,CAACkB,eAAe,CAAC,CAAC,CAChDN,kBAAkB,CAACK,UAAU,CAAC,CAE9B,GAAIA,UAAU,CAAE,CACdH,cAAc,CAACd,WAAW,CAACmB,cAAc,CAAC,CAAC,CAAC,CAC9C,CAEAT,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMU,YAAY,CAACL,KAAK,CAAC,CAClC,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAM,qBAAqB,CAAGA,CAACC,aAAa,CAAEC,IAAI,CAAEC,UAAU,GAAK,CACjEZ,kBAAkB,CAACU,aAAa,CAAC,CACjCR,cAAc,CAACS,IAAI,CAAC,CAEpB;AACA,GAAIC,UAAU,CAAE,CACdhB,cAAc,CAACgB,UAAU,CAAC,CAC5B,CACF,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAIC,QAAQ,EAAK,CACrC,KAAM,CAAAC,SAAS,CAAG1B,UAAU,CAAC0B,SAAS,CAACD,QAAQ,CAAC,CAEhD,GAAIC,SAAS,CAACC,OAAO,CAAE,CACrBpB,cAAc,CAACkB,QAAQ,CAAC,CAC1B,CAAC,IAAM,CACL;AACAG,OAAO,CAACC,IAAI,CAAC,UAAU,CAAEH,SAAS,CAACI,OAAO,CAAC,CAC7C,CACF,CAAC,CAED,KAAM,CAAAC,UAAU,CAAGA,CAAA,GAAM,CACvB;AACA,KAAM,CAAAC,SAAS,CAAG,CAChBC,UAAU,CAAET,gBAAgB,CAC5Bd,eAAe,CACfE,WAAW,CACXsB,QAAQ,CAAEtB,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuB,IACzB,CAAC,CAED,OAAQ7B,WAAW,EACjB,IAAK,MAAM,CACT,mBAAOJ,IAAA,CAACd,QAAQ,CAAAgD,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACpC,IAAK,aAAa,CAChB,mBAAO9B,IAAA,CAACb,UAAU,CAAA+C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,IAAK,eAAe,CAClB,mBAAO9B,IAAA,CAACZ,YAAY,CAAA8C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACxC,IAAK,aAAa,CAChB,mBAAO9B,IAAA,CAACX,UAAU,CAAA6C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,IAAK,aAAa,CAChB,mBAAO9B,IAAA,CAACV,UAAU,CAAA4C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,IAAK,aAAa,CAChB,mBAAO9B,IAAA,CAACT,UAAU,CAAA2C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,IAAK,YAAY,CACf,mBAAO9B,IAAA,CAACR,SAAS,CAAA0C,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACrC,IAAK,YAAY,CACf,mBAAO9B,IAAA,CAACP,UAAU,CAAAyC,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,IAAK,iBAAiB,CACpB,mBAAO9B,IAAA,CAACL,cAAc,CAAAuC,aAAA,IAAKJ,SAAS,CAAG,CAAC,CAC1C,IAAK,mBAAmB,CACtB,mBAAO9B,IAAA,CAACJ,gBAAgB,CAAAsC,aAAA,CAAAA,aAAA,IAAKJ,SAAS,MAAEK,YAAY,CAAEjB,qBAAsB,EAAE,CAAC,CAEjF,QACE,mBAAOlB,IAAA,CAACd,QAAQ,CAAAgD,aAAA,IAAKJ,SAAS,CAAG,CAAC,CACtC,CACF,CAAC,CAED,GAAIxB,SAAS,CAAE,CACb,mBACEN,IAAA,QAAKoC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BnC,KAAA,QAAKkC,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BnC,KAAA,QAAKkC,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BrC,IAAA,QAAKoC,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,kDAAQ,CAAK,CAAC,cACzCrC,IAAA,QAAKoC,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yCAAuC,CAAK,CAAC,EACzE,CAAC,cACNrC,IAAA,QAAKoC,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BrC,IAAA,QAAKoC,SAAS,CAAC,cAAc,CAAM,CAAC,CACjC,CAAC,cACNpC,IAAA,QAAKoC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,yCAAS,CAAK,CAAC,EAC5C,CAAC,CACH,CAAC,CAEV,CAEA,mBACEnC,KAAA,QAAKkC,SAAS,CAAC,KAAK,CAAAC,QAAA,EAEjBjC,WAAW,GAAK,MAAM,eACrBJ,IAAA,CAACN,YAAY,EAAC4C,iBAAiB,CAAEpB,qBAAsB,CAAE,CAC1D,CAGAW,UAAU,CAAC,CAAC,EACV,CAAC,CAEV,CAEA,cAAe,CAAA1B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}