{"ast": null, "code": "import*as XLSX from'xlsx';class ModuleSixService{constructor(){this.module6_sourceData=null;this.module6_tableData={};this.module6_realData=null;// 存储真实数据\nthis.module6_syncCallbacks={onSuccess:null,onError:null};this.loadRealData();// 初始化时加载真实数据\n}// 加载真实数据\nasync loadRealData(){try{// 从public目录加载真实数据JSON文件\nconst response=await fetch('/data/realData.json');if(response.ok){this.module6_realData=await response.json();console.log('真实数据加载成功，包含',Object.keys(this.module6_realData).length,'个部门的数据');}else{console.warn('无法加载真实数据文件，将使用API数据');}}catch(error){console.warn('加载真实数据失败，将使用API数据:',error);}}// 设置同步回调\nmodule6_setSyncCallbacks(onSuccess,onError){this.module6_syncCallbacks.onSuccess=onSuccess;this.module6_syncCallbacks.onError=onError;}// 加载指定表的数据\nasync module6_loadTableData(tableName){try{// 优先使用真实数据\nif(this.module6_realData&&this.module6_realData[tableName]){console.log(\"\\u4F7F\\u7528\\u771F\\u5B9E\\u6570\\u636E\\u52A0\\u8F7D \".concat(tableName));const realData=this.module6_realData[tableName];this.module6_tableData[tableName]=realData;// 应用数据匹配逻辑\nawait this.module6_applyDataMatching(tableName);return realData;}// 如果真实数据不可用，回退到API加载\nconsole.log(\"\\u771F\\u5B9E\\u6570\\u636E\\u4E0D\\u53EF\\u7528\\uFF0C\\u5C1D\\u8BD5\\u4ECEAPI\\u52A0\\u8F7D \".concat(tableName));const response=await fetch(\"http://localhost:3001/api/module6-excel-data/\".concat(encodeURIComponent(tableName)));if(response.ok){const result=await response.json();if(result.data){// 返回的数据结构应为 { keyIndicators: [...], keyWork: [...] }\nthis.module6_tableData[tableName]=result.data;return result.data;}else{console.warn(\"API for \".concat(tableName,\" returned no data.\"));// 如果API没有返回数据，则返回一个空结构，避免前端崩溃\nreturn{keyIndicators:[],keyWork:[]};}}else{console.error(\"API request failed for \".concat(tableName,\" with status: \").concat(response.status));// API请求失败也返回空结构\nreturn{keyIndicators:[],keyWork:[]};}}catch(error){console.error(\"\\u4ECEAPI\\u52A0\\u8F7D \".concat(tableName,\" \\u6570\\u636E\\u5931\\u8D25:\"),error);// 捕获到异常时同样返回空结构\nreturn{keyIndicators:[],keyWork:[]};}}// 更新单元格数据 - 优化版本\nasync module6_updateCellData(tableName,viewType,rowIndex,field,value){try{var _this$module6_realDat,_this$module6_realDat2,_this$module6_realDat3,_this$module6_realDat4,_this$module6_tableDa,_this$module6_tableDa2,_this$module6_tableDa3,_this$module6_tableDa4;console.log(\"\\u5F00\\u59CB\\u66F4\\u65B0\\u5355\\u5143\\u683C\\u6570\\u636E: \".concat(tableName,\".\").concat(viewType,\"[\").concat(rowIndex,\"].\").concat(field,\" = \\\"\").concat(value,\"\\\"\"));// 验证输入参数\nif(!tableName||!viewType||rowIndex<0||!field){throw new Error('无效的更新参数');}// 检查当前单元格是否只读\nconst currentCellData=((_this$module6_realDat=this.module6_realData)===null||_this$module6_realDat===void 0?void 0:(_this$module6_realDat2=_this$module6_realDat[tableName])===null||_this$module6_realDat2===void 0?void 0:(_this$module6_realDat3=_this$module6_realDat2[viewType])===null||_this$module6_realDat3===void 0?void 0:(_this$module6_realDat4=_this$module6_realDat3[rowIndex])===null||_this$module6_realDat4===void 0?void 0:_this$module6_realDat4[field])||((_this$module6_tableDa=this.module6_tableData)===null||_this$module6_tableDa===void 0?void 0:(_this$module6_tableDa2=_this$module6_tableDa[tableName])===null||_this$module6_tableDa2===void 0?void 0:(_this$module6_tableDa3=_this$module6_tableDa2[viewType])===null||_this$module6_tableDa3===void 0?void 0:(_this$module6_tableDa4=_this$module6_tableDa3[rowIndex])===null||_this$module6_tableDa4===void 0?void 0:_this$module6_tableDa4[field]);if(currentCellData!==null&&currentCellData!==void 0&&currentCellData.isReadOnly){console.warn(\"\\u5C1D\\u8BD5\\u7F16\\u8F91\\u53EA\\u8BFB\\u5355\\u5143\\u683C: \".concat(tableName,\" \").concat(viewType,\" [\").concat(rowIndex,\"] \").concat(field));throw new Error('该单元格为只读，不允许编辑');}// 更新本地缓存数据\nif(this.module6_tableData[tableName]&&this.module6_tableData[tableName][viewType]){this.module6_tableData[tableName][viewType][rowIndex][field]={value:value,isReadOnly:false,_dataSource:'target',_lastModified:new Date().toISOString()};}// 同时更新真实数据\nif(this.module6_realData&&this.module6_realData[tableName]&&this.module6_realData[tableName][viewType]){this.module6_realData[tableName][viewType][rowIndex][field]={value:value,isReadOnly:false,_dataSource:'target',_lastModified:new Date().toISOString()};}// 立即同步到Excel文件\nconst syncResult=await this.module6_syncToExcel(tableName,viewType,rowIndex,field,value);// 触发成功回调\nif(this.module6_syncCallbacks.onSuccess){this.module6_syncCallbacks.onSuccess(\"\\u6570\\u636E\\u5DF2\\u4FDD\\u5B58\".concat(syncResult.backendSync?'并同步到服务器':'到本地'));}console.log(\"\\u5355\\u5143\\u683C\\u6570\\u636E\\u66F4\\u65B0\\u6210\\u529F: \".concat(tableName,\".\").concat(viewType,\"[\").concat(rowIndex,\"].\").concat(field));return syncResult;}catch(error){console.error('更新数据失败:',error);if(this.module6_syncCallbacks.onError){this.module6_syncCallbacks.onError(error);}throw error;}}// 同步数据到Excel文件\nasync module6_syncToExcel(tableName,viewType,rowIndex,field,value){const syncResult={localSync:false,backendSync:false,error:null};try{// 1. 尝试通过后端API同步到Excel文件\nconsole.log('尝试通过后端API同步到Excel文件...');const response=await fetch(\"http://localhost:3001/api/module6-sync-excel\",{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({tableName,viewType,rowIndex,field,value,timestamp:new Date().toISOString()})});if(response.ok){const result=await response.json();console.log('后端同步成功:',result);syncResult.backendSync=true;syncResult.localSync=true;return syncResult;}else{console.warn(\"\\u540E\\u7AEF\\u540C\\u6B65\\u5931\\u8D25\\uFF0C\\u72B6\\u6001\\u7801: \".concat(response.status));syncResult.error=\"\\u540E\\u7AEF\\u540C\\u6B65\\u5931\\u8D25: \".concat(response.status);}}catch(backendError){console.warn('后端API不可用:',backendError.message);syncResult.error=\"\\u540E\\u7AEFAPI\\u4E0D\\u53EF\\u7528: \".concat(backendError.message);}// 2. 如果后端同步失败，尝试本地存储\ntry{console.log('尝试本地存储...');// 将更改记录到本地存储中，等待后续同步\nconst changeRecord={tableName,viewType,rowIndex,field,value,timestamp:new Date().toISOString(),synced:false};// 获取现有的待同步记录\nconst pendingChanges=JSON.parse(localStorage.getItem('module6_pendingChanges')||'[]');pendingChanges.push(changeRecord);// 保存到本地存储\nlocalStorage.setItem('module6_pendingChanges',JSON.stringify(pendingChanges));console.log('数据已保存到本地存储，等待后续同步');syncResult.localSync=true;}catch(localError){console.error('本地存储失败:',localError);syncResult.error=\"\\u672C\\u5730\\u5B58\\u50A8\\u5931\\u8D25: \".concat(localError.message);throw localError;}return syncResult;}// 重试同步待处理的更改\nasync module6_retrySyncPendingChanges(){try{const pendingChanges=JSON.parse(localStorage.getItem('module6_pendingChanges')||'[]');if(pendingChanges.length===0){console.log('没有待同步的更改');return{success:true,syncedCount:0};}console.log(\"\\u5F00\\u59CB\\u91CD\\u8BD5\\u540C\\u6B65 \".concat(pendingChanges.length,\" \\u4E2A\\u5F85\\u5904\\u7406\\u66F4\\u6539...\"));let syncedCount=0;const remainingChanges=[];for(const change of pendingChanges){try{const syncResult=await this.module6_syncToExcel(change.tableName,change.viewType,change.rowIndex,change.field,change.value);if(syncResult.backendSync){syncedCount++;console.log(\"\\u91CD\\u8BD5\\u540C\\u6B65\\u6210\\u529F: \".concat(change.tableName,\".\").concat(change.viewType,\"[\").concat(change.rowIndex,\"].\").concat(change.field));}else{remainingChanges.push(change);}}catch(error){console.error(\"\\u91CD\\u8BD5\\u540C\\u6B65\\u5931\\u8D25: \".concat(change.tableName,\".\").concat(change.viewType,\"[\").concat(change.rowIndex,\"].\").concat(change.field),error);remainingChanges.push(change);}}// 更新本地存储中的待同步记录\nlocalStorage.setItem('module6_pendingChanges',JSON.stringify(remainingChanges));console.log(\"\\u91CD\\u8BD5\\u540C\\u6B65\\u5B8C\\u6210: \\u6210\\u529F \".concat(syncedCount,\" \\u4E2A\\uFF0C\\u5269\\u4F59 \").concat(remainingChanges.length,\" \\u4E2A\"));return{success:true,syncedCount,remainingCount:remainingChanges.length};}catch(error){console.error('重试同步失败:',error);throw error;}}// 获取待同步更改的数量\nmodule6_getPendingChangesCount(){try{const pendingChanges=JSON.parse(localStorage.getItem('module6_pendingChanges')||'[]');return pendingChanges.length;}catch(error){console.error('获取待同步更改数量失败:',error);return 0;}}// 清除所有待同步更改\nmodule6_clearPendingChanges(){try{localStorage.removeItem('module6_pendingChanges');console.log('已清除所有待同步更改');return true;}catch(error){console.error('清除待同步更改失败:',error);return false;}}// 导出数据\nasync module6_exportData(tableName,viewType,exportConfig){try{var _this$module6_tableDa5;// 优先使用真实数据，如果不可用则使用缓存数据\nconst data=this.module6_realData&&this.module6_realData[tableName]&&this.module6_realData[tableName][viewType]||((_this$module6_tableDa5=this.module6_tableData[tableName])===null||_this$module6_tableDa5===void 0?void 0:_this$module6_tableDa5[viewType])||[];const selectedData=exportConfig.selectedData||data;if(exportConfig.format==='excel'){await this.module6_exportToExcel(selectedData,tableName,viewType);}else if(exportConfig.format==='csv'){await this.module6_exportToCSV(selectedData,tableName,viewType);}}catch(error){console.error('导出失败:',error);throw error;}}// 导出到Excel\nasync module6_exportToExcel(data,tableName,viewType){const workbook=XLSX.utils.book_new();// 准备导出数据\nconst exportData=data.map(row=>{const exportRow={'序号':row.序号,'指标':row.指标,'目标值':row.目标值,'权重':row.权重,'指标分类':row.指标分类,'跟踪频次':row.跟踪频次};// 添加月份数据\nfor(let month=1;month<=12;month++){const monthKey=\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\");const completionKey=\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\");const scoreKey=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");if(row[monthKey]){var _row$completionKey,_row$scoreKey;exportRow[monthKey]=row[monthKey].value||'';exportRow[completionKey]=((_row$completionKey=row[completionKey])===null||_row$completionKey===void 0?void 0:_row$completionKey.value)||'';exportRow[scoreKey]=((_row$scoreKey=row[scoreKey])===null||_row$scoreKey===void 0?void 0:_row$scoreKey.value)||'';}}return exportRow;});const worksheet=XLSX.utils.json_to_sheet(exportData);XLSX.utils.book_append_sheet(workbook,worksheet,\"\".concat(tableName,\"-\").concat(viewType));// 生成文件名\nconst fileName=\"\".concat(tableName,\"_\").concat(viewType,\"_\").concat(new Date().toISOString().split('T')[0],\".xlsx\");// 下载文件\nXLSX.writeFile(workbook,fileName);}// 导出到CSV\nasync module6_exportToCSV(data,tableName,viewType){// 准备CSV数据\nconst headers=['序号','指标','目标值','权重','指标分类','跟踪频次'];// 添加月份列头\nfor(let month=1;month<=12;month++){headers.push(\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\"),\"\".concat(month,\"\\u6708\\u8BC4\\u5206\"));}const csvData=[headers.join(',')];data.forEach(row=>{const rowData=[row.序号,\"\\\"\".concat(row.指标,\"\\\"\"),\"\\\"\".concat(row.目标值,\"\\\"\"),row.权重,\"\\\"\".concat(row.指标分类,\"\\\"\"),\"\\\"\".concat(row.跟踪频次,\"\\\"\")];// 添加月份数据\nfor(let month=1;month<=12;month++){var _row$monthKey,_row$completionKey2,_row$scoreKey2;const monthKey=\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\");const completionKey=\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\");const scoreKey=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");rowData.push(\"\\\"\".concat(((_row$monthKey=row[monthKey])===null||_row$monthKey===void 0?void 0:_row$monthKey.value)||'',\"\\\"\"),\"\\\"\".concat(((_row$completionKey2=row[completionKey])===null||_row$completionKey2===void 0?void 0:_row$completionKey2.value)||'',\"\\\"\"),\"\\\"\".concat(((_row$scoreKey2=row[scoreKey])===null||_row$scoreKey2===void 0?void 0:_row$scoreKey2.value)||'',\"\\\"\"));}csvData.push(rowData.join(','));});// 生成文件名\nconst fileName=\"\".concat(tableName,\"_\").concat(viewType,\"_\").concat(new Date().toISOString().split('T')[0],\".csv\");// 创建下载链接\nconst blob=new Blob([csvData.join('\\n')],{type:'text/csv;charset=utf-8;'});const link=document.createElement('a');link.href=URL.createObjectURL(blob);link.download=fileName;link.click();}// 从Excel源文件同步数据\nasync module6_syncFromSourceExcel(){try{// 这里应该实现从源Excel文件读取数据的逻辑\n// 根据提示词，需要从\"开发中心2025年重点工作跟踪-填写表\"读取数据\n// 并匹配到对应的目标表\nconst response=await fetch('http://localhost:3001/api/sync-source-excel');if(response.ok){const result=await response.json();this.module6_sourceData=result.data;return result.data;}}catch(error){console.error('同步源数据失败:',error);throw error;}}// 应用数据匹配逻辑 - 优化版本\nasync module6_applyDataMatching(tableName){try{console.log(\"\\u5F00\\u59CB\\u4E3A\\u8868 \".concat(tableName,\" \\u5E94\\u7528\\u6570\\u636E\\u5339\\u914D\\u903B\\u8F91...\"));// 获取源数据（开发中心2025年重点工作跟踪-填写表）\nconst sourceData=await this.module6_getSourceData();if(!sourceData||sourceData.length===0){console.warn('源数据为空，跳过数据匹配');return;}if(!this.module6_realData[tableName]){console.warn(\"\\u76EE\\u6807\\u8868 \".concat(tableName,\" \\u4E0D\\u5B58\\u5728\"));return;}let totalMatched=0;let totalProcessed=0;// 处理关键指标和重点工作\n['keyIndicators','keyWork'].forEach(viewType=>{if(!this.module6_realData[tableName][viewType]){console.log(\"\\u89C6\\u56FE\\u7C7B\\u578B \".concat(viewType,\" \\u4E0D\\u5B58\\u5728\\u4E8E\\u8868 \").concat(tableName,\" \\u4E2D\"));return;}console.log(\"\\u5904\\u7406 \".concat(viewType,\" \\u89C6\\u56FE\\uFF0C\\u5171 \").concat(this.module6_realData[tableName][viewType].length,\" \\u884C\\u6570\\u636E\"));this.module6_realData[tableName][viewType].forEach((targetRow,rowIndex)=>{totalProcessed++;// 确保目标行有指标字段\nif(!targetRow.指标){console.warn(\"\\u7B2C \".concat(rowIndex,\" \\u884C\\u7F3A\\u5C11\\u6307\\u6807\\u5B57\\u6BB5\"));return;}// 查找源数据中匹配的行（根据指标名称匹配）\nconst matchingSourceRow=sourceData.find(sourceRow=>{return sourceRow.相关指标或方向&&this.module6_isIndicatorMatch(targetRow.指标,sourceRow.相关指标或方向);});if(matchingSourceRow){totalMatched++;console.log(\"\\u2713 \\u5339\\u914D\\u6210\\u529F [\".concat(rowIndex,\"]: \").concat(targetRow.指标,\" <-> \").concat(matchingSourceRow.相关指标或方向));// 复制所有月份的工作计划和完成情况，并设置为只读\nfor(let month=1;month<=12;month++){var _targetRow$scoreField;const planField=\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\");const completeField=\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\");const scoreField=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");// 处理工作计划\nthis.module6_applyCellData(targetRow,planField,matchingSourceRow[planField],true);// 处理完成情况\nthis.module6_applyCellData(targetRow,completeField,matchingSourceRow[completeField],true);// 评分字段保持可编辑（通常由用户填写）\nthis.module6_applyCellData(targetRow,scoreField,(_targetRow$scoreField=targetRow[scoreField])===null||_targetRow$scoreField===void 0?void 0:_targetRow$scoreField.value,false);}// 标记该行为已匹配\ntargetRow._isMatched=true;targetRow._matchedSource=matchingSourceRow.相关指标或方向;}else{console.log(\"\\u2717 \\u672A\\u627E\\u5230\\u5339\\u914D [\".concat(rowIndex,\"]: \").concat(targetRow.指标));// 未匹配的数据保持可编辑，使用目标表原始数据\nfor(let month=1;month<=12;month++){var _targetRow$planField,_targetRow$completeFi,_targetRow$scoreField2;const planField=\"\".concat(month,\"\\u6708\\u5DE5\\u4F5C\\u8BA1\\u5212\");const completeField=\"\".concat(month,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\");const scoreField=\"\".concat(month,\"\\u6708\\u8BC4\\u5206\");// 保持原始数据并设置为可编辑\nthis.module6_applyCellData(targetRow,planField,(_targetRow$planField=targetRow[planField])===null||_targetRow$planField===void 0?void 0:_targetRow$planField.value,false);this.module6_applyCellData(targetRow,completeField,(_targetRow$completeFi=targetRow[completeField])===null||_targetRow$completeFi===void 0?void 0:_targetRow$completeFi.value,false);this.module6_applyCellData(targetRow,scoreField,(_targetRow$scoreField2=targetRow[scoreField])===null||_targetRow$scoreField2===void 0?void 0:_targetRow$scoreField2.value,false);}// 标记该行为未匹配\ntargetRow._isMatched=false;}});});console.log(\"\\u6570\\u636E\\u5339\\u914D\\u5B8C\\u6210: \".concat(tableName,\" - \\u603B\\u8BA1\\u5904\\u7406 \").concat(totalProcessed,\" \\u884C\\uFF0C\\u6210\\u529F\\u5339\\u914D \").concat(totalMatched,\" \\u884C (\").concat((totalMatched/totalProcessed*100).toFixed(1),\"%)\"));}catch(error){console.error('应用数据匹配失败:',error);throw error;}}// 应用单元格数据的辅助方法\nmodule6_applyCellData(targetRow,fieldName,sourceValue,isReadOnly){// 确保单元格数据结构正确\nif(!targetRow[fieldName]||typeof targetRow[fieldName]!=='object'){targetRow[fieldName]={value:'',isReadOnly:false};}// 设置值和只读状态\nif(sourceValue!==undefined&&sourceValue!==null){targetRow[fieldName].value=sourceValue;}targetRow[fieldName].isReadOnly=isReadOnly;// 添加数据来源标识\ntargetRow[fieldName]._dataSource=isReadOnly?'source':'target';}// 判断指标是否匹配 - 优化版本\nmodule6_isIndicatorMatch(targetIndicator,sourceIndicator){if(!targetIndicator||!sourceIndicator)return false;// 数据预处理：统一格式\nconst normalizeText=text=>{return text.replace(/\\r\\n|\\n|\\r/g,'')// 移除换行符\n.replace(/\\s+/g,'')// 移除所有空格\n.replace(/[（）()]/g,'')// 移除括号\n.replace(/[、，,]/g,'')// 移除分隔符\n.toLowerCase();// 转为小写\n};const cleanTarget=normalizeText(targetIndicator);const cleanSource=normalizeText(sourceIndicator);// 1. 精确匹配（最高优先级）\nif(cleanTarget===cleanSource){console.log(\"\\u7CBE\\u786E\\u5339\\u914D: \".concat(targetIndicator,\" === \").concat(sourceIndicator));return true;}// 2. 完全包含匹配\nif(cleanTarget.includes(cleanSource)||cleanSource.includes(cleanTarget)){console.log(\"\\u5305\\u542B\\u5339\\u914D: \".concat(targetIndicator,\" <-> \").concat(sourceIndicator));return true;}// 3. 移除部门后缀后的匹配\nconst departmentSuffixes=['金属橡胶件','空簧','系统','客户技术','工艺模具','仿真','特装','技术研究与发展','车端','属地化','车体新材料'];let targetWithoutDept=cleanTarget;let sourceWithoutDept=cleanSource;// 移除部门后缀\ndepartmentSuffixes.forEach(dept=>{const normalizedDept=normalizeText(dept);if(targetWithoutDept.endsWith(normalizedDept)){targetWithoutDept=targetWithoutDept.slice(0,-normalizedDept.length);}if(sourceWithoutDept.endsWith(normalizedDept)){sourceWithoutDept=sourceWithoutDept.slice(0,-normalizedDept.length);}});// 移除部门后缀后的匹配\nif(targetWithoutDept&&sourceWithoutDept&&targetWithoutDept.length>2&&sourceWithoutDept.length>2){if(targetWithoutDept===sourceWithoutDept){console.log(\"\\u90E8\\u95E8\\u540E\\u7F00\\u5339\\u914D: \".concat(targetIndicator,\" <-> \").concat(sourceIndicator));return true;}if(targetWithoutDept.includes(sourceWithoutDept)||sourceWithoutDept.includes(targetWithoutDept)){console.log(\"\\u90E8\\u95E8\\u540E\\u7F00\\u5305\\u542B\\u5339\\u914D: \".concat(targetIndicator,\" <-> \").concat(sourceIndicator));return true;}}// 4. 关键词匹配（提高阈值）\nconst extractKeywords=text=>{return text.split(/[（）()、，,\\s]/g).filter(k=>k.length>=3)// 提高关键词最小长度\n.map(k=>normalizeText(k)).filter(k=>k.length>=2);// 再次过滤\n};const targetKeywords=extractKeywords(targetIndicator);const sourceKeywords=extractKeywords(sourceIndicator);// 计算匹配的关键词数量\nlet matchedKeywords=0;const totalKeywords=Math.max(targetKeywords.length,sourceKeywords.length);targetKeywords.forEach(tk=>{sourceKeywords.forEach(sk=>{if(tk===sk||tk.length>3&&sk.length>3&&(tk.includes(sk)||sk.includes(tk))){matchedKeywords++;}});});// 需要至少50%的关键词匹配\nconst matchRatio=matchedKeywords/totalKeywords;if(matchRatio>=0.5&&matchedKeywords>=1){console.log(\"\\u5173\\u952E\\u8BCD\\u5339\\u914D: \".concat(targetIndicator,\" <-> \").concat(sourceIndicator,\" (\\u5339\\u914D\\u7387: \").concat((matchRatio*100).toFixed(1),\"%)\"));return true;}// 5. 特殊指标匹配规则\nconst specialMatches=[{target:['命中率','一次命中'],source:['命中率','一次命中']},{target:['推荐选型','选型'],source:['推荐选型','选型']},{target:['工程师占比','工程师'],source:['工程师占比','工程师']},{target:['产值提升','产值'],source:['产值提升','产值']},{target:['费用管控','费用'],source:['费用管控','费用']}];for(const rule of specialMatches){const targetMatch=rule.target.some(keyword=>cleanTarget.includes(normalizeText(keyword)));const sourceMatch=rule.source.some(keyword=>cleanSource.includes(normalizeText(keyword)));if(targetMatch&&sourceMatch){console.log(\"\\u7279\\u6B8A\\u89C4\\u5219\\u5339\\u914D: \".concat(targetIndicator,\" <-> \").concat(sourceIndicator));return true;}}return false;}// 获取源数据 - 优化版本\nasync module6_getSourceData(){try{// 1. 优先从API获取真实Excel数据\nconsole.log('正在从API获取源Excel数据...');const response=await fetch('http://localhost:3001/api/source-excel-data');if(response.ok){const result=await response.json();if(result.data&&Array.isArray(result.data)&&result.data.length>0){console.log(\"\\u4ECEAPI\\u6210\\u529F\\u83B7\\u53D6\\u6E90\\u6570\\u636E: \".concat(result.data.length,\" \\u6761\\u8BB0\\u5F55\"));return result.data;}else{console.warn('API返回的源数据为空或格式不正确');}}else{console.warn(\"API\\u8BF7\\u6C42\\u5931\\u8D25\\uFF0C\\u72B6\\u6001\\u7801: \".concat(response.status));}}catch(error){console.error('从API获取源数据失败:',error);}// 2. 如果API失败，尝试从本地JSON文件读取\ntry{console.log('尝试从本地JSON文件读取源数据...');const sourceDataModule=await import('../data/sourceData.json');if(sourceDataModule.default&&sourceDataModule.default.data){console.log(\"\\u4ECE\\u672C\\u5730\\u6587\\u4EF6\\u6210\\u529F\\u83B7\\u53D6\\u6E90\\u6570\\u636E: \".concat(sourceDataModule.default.data.length,\" \\u6761\\u8BB0\\u5F55\"));return sourceDataModule.default.data;}}catch(error){console.error('从本地文件读取源数据失败:',error);}// 3. 最后回退到硬编码的模拟数据\nconsole.log('使用硬编码的模拟数据...');return[{\"相关指标或方向\":\"人均开发产值提升\",\"2月工作计划\":\"制定2025年开发计划\",\"2月完成情况\":\"已完成初步方案\",\"3月工作计划\":\"优化开发流程\",\"3月完成情况\":\"流程优化完成50%\"},{\"相关指标或方向\":\"一次命中率提升\",\"2月工作计划\":\"91%\",\"2月完成情况\":\"88.8%=32/36，完成试制36个,一次命中32个。3个工艺原因，1个设计原因。设计命中率：35/36=97.3%工艺命中率：33/36=91.6%\",\"3月工作计划\":\"91%\",\"3月完成情况\":\"81.13%=43/53完成试制53个。一次命中43个。未一次命中10个（详见清单），5个工艺原因，5个设计原因。设计命中率：48/53=90.6%工艺命中率：48/53=90.6%\",\"4月工作计划\":\"91%\",\"4月完成情况\":\"79.37%=50/63完成试制63个。一次命中50个。未一次命中13个（详见清单），7个工艺原因，6个设计原因。设计命中率：57/63=90.4%工艺命中率：56/63=88.8%\",\"5月工作计划\":\"91%\",\"5月完成情况\":\"82.5%=33/40完成试制40个。一次命中33个。未一次命中7个（详见清单），4个工艺原因，3个设计原因。设计命中率：36/40=90%工艺命中率：35/40=87.5%\",\"6月工作计划\":\"91%\",\"6月完成情况\":\"85.7%=30/35完成试制35个。一次命中30个。未一次命中5个（详见清单），3个工艺原因，2个设计原因。设计命中率：32/35=91.4%工艺命中率：31/35=88.6%\",\"7月工作计划\":\"91%\",\"7月完成情况\":\"87.5%=28/32完成试制32个。一次命中28个。未一次命中4个（详见清单），2个工艺原因，2个设计原因。设计命中率：30/32=93.8%工艺命中率：29/32=90.6%\",\"8月工作计划\":\"91%\",\"8月完成情况\":\"89.5%=34/38完成试制38个。一次命中34个。未一次命中4个（详见清单），2个工艺原因，2个设计原因。设计命中率：36/38=94.7%工艺命中率：35/38=92.1%\",\"9月工作计划\":\"91%\",\"9月完成情况\":\"91.2%=31/34完成试制34个。一次命中31个。未一次命中3个（详见清单），2个工艺原因，1个设计原因。设计命中率：33/34=97.1%工艺命中率：32/34=94.1%\",\"10月工作计划\":\"91%\",\"10月完成情况\":\"92.3%=36/39完成试制39个。一次命中36个。未一次命中3个（详见清单），2个工艺原因，1个设计原因。设计命中率：38/39=97.4%工艺命中率：37/39=94.9%\",\"11月工作计划\":\"91%\",\"11月完成情况\":\"93.1%=27/29完成试制29个。一次命中27个。未一次命中2个（详见清单），1个工艺原因，1个设计原因。设计命中率：28/29=96.6%工艺命中率：27/29=93.1%\",\"12月工作计划\":\"91%\",\"12月完成情况\":\"94.1%=32/34完成试制34个。一次命中32个。未一次命中2个（详见清单），1个工艺原因，1个设计原因。设计命中率：33/34=97.1%工艺命中率：32/34=94.1%\"},{\"相关指标或方向\":\"推荐选型提升\",\"2月工作计划\":\"提升选型准确率\",\"2月完成情况\":\"选型准确率达到预期目标\",\"3月工作计划\":\"继续优化选型流程\",\"3月完成情况\":\"流程优化进行中\",\"4月工作计划\":\"62%\",\"4月完成情况\":\"64.4%=29/45开发产品共计45个,其中间接推荐选型26个,直接推荐选型3个。\",\"5月工作计划\":\"62%\",\"5月完成情况\":\"58.3%=21/36开发产品共计36个,其中间接推荐选型18个,直接推荐选型3个。\",\"6月工作计划\":\"62%\",\"6月完成情况\":\"61.5%=24/39开发产品共计39个,其中间接推荐选型21个,直接推荐选型3个。\",\"7月工作计划\":\"62%\",\"7月完成情况\":\"63.6%=28/44开发产品共计44个,其中间接推荐选型25个,直接推荐选型3个。\",\"8月工作计划\":\"62%\",\"8月完成情况\":\"65.2%=30/46开发产品共计46个,其中间接推荐选型27个,直接推荐选型3个。\",\"9月工作计划\":\"62%\",\"9月完成情况\":\"66.7%=32/48开发产品共计48个,其中间接推荐选型29个,直接推荐选型3个。\",\"10月工作计划\":\"62%\",\"10月完成情况\":\"67.4%=31/46开发产品共计46个,其中间接推荐选型28个,直接推荐选型3个。\",\"11月工作计划\":\"62%\",\"11月完成情况\":\"68.2%=30/44开发产品共计44个,其中间接推荐选型27个,直接推荐选型3个。\",\"12月工作计划\":\"62%\",\"12月完成情况\":\"69.0%=29/42开发产品共计42个,其中间接推荐选型26个,直接推荐选型3个。\"},{\"相关指标或方向\":\"悬挂工程师占比\",\"2月工作计划\":\"提升工程师技能水平\",\"2月完成情况\":\"培训计划执行中\",\"3月工作计划\":\"完成技能评估\",\"3月完成情况\":\"评估完成80%\",\"4月工作计划\":\"≥7人\",\"4月完成情况\":\"当前6人，计划招聘1人\",\"5月工作计划\":\"≥7人\",\"5月完成情况\":\"招聘进行中，预计月底到岗\",\"6月工作计划\":\"≥7人\",\"6月完成情况\":\"新工程师已到岗，团队规模达到7人\",\"7月工作计划\":\"≥7人\",\"7月完成情况\":\"团队稳定，技能培训持续进行\",\"8月工作计划\":\"≥7人\",\"8月完成情况\":\"工程师技能水平显著提升\",\"9月工作计划\":\"≥7人\",\"9月完成情况\":\"团队协作效率提升\",\"10月工作计划\":\"≥7人\",\"10月完成情况\":\"完成年度技能评估\",\"11月工作计划\":\"≥7人\",\"11月完成情况\":\"制定下年度培训计划\",\"12月工作计划\":\"≥7人\",\"12月完成情况\":\"年度目标达成\"},{\"相关指标或方向\":\"国际化开发工程师占比\",\"2月工作计划\":\"国际化培训计划\",\"2月完成情况\":\"培训材料准备完成\",\"3月工作计划\":\"开始培训实施\",\"3月完成情况\":\"培训进行中\",\"4月工作计划\":\"≥50%\",\"4月完成情况\":\"当前45%，培训持续进行\",\"5月工作计划\":\"≥50%\",\"5月完成情况\":\"培训效果显著，达到48%\",\"6月工作计划\":\"≥50%\",\"6月完成情况\":\"目标达成，国际化工程师占比达到52%\",\"7月工作计划\":\"≥50%\",\"7月完成情况\":\"保持稳定，持续优化\",\"8月工作计划\":\"≥50%\",\"8月完成情况\":\"国际化能力进一步提升\",\"9月工作计划\":\"≥50%\",\"9月完成情况\":\"完成国际化项目评估\",\"10月工作计划\":\"≥50%\",\"10月完成情况\":\"制定下阶段国际化策略\",\"11月工作计划\":\"≥50%\",\"11月完成情况\":\"国际化培训体系完善\",\"12月工作计划\":\"≥50%\",\"12月完成情况\":\"年度国际化目标达成\"},{\"相关指标或方向\":\"产品线毛利率\",\"2月工作计划\":\"分析成本结构\",\"2月完成情况\":\"成本分析报告完成\",\"3月工作计划\":\"制定优化方案\",\"3月完成情况\":\"方案制定中\",\"4月工作计划\":\"≥10%\",\"4月完成情况\":\"当前9%，成本控制持续优化\",\"5月工作计划\":\"≥10%\",\"5月完成情况\":\"成本控制效果显著，达到10%\",\"6月工作计划\":\"≥10%\",\"6月完成情况\":\"目标达成，产品线毛利率达到12%\",\"7月工作计划\":\"≥10%\",\"7月完成情况\":\"保持稳定，持续提升\",\"8月工作计划\":\"≥10%\",\"8月完成情况\":\"产品线成本控制显著提升\",\"9月工作计划\":\"≥10%\",\"9月完成情况\":\"完成成本分析报告\",\"10月工作计划\":\"≥10%\",\"10月完成情况\":\"制定下阶段成本优化策略\",\"11月工作计划\":\"≥10%\",\"11月完成情况\":\"成本优化体系完善\",\"12月工作计划\":\"≥10%\",\"12月完成情况\":\"年度成本目标达成\"},{\"相关指标或方向\":\"降本指标\",\"2月工作计划\":\"识别降本机会\",\"2月完成情况\":\"机会识别完成\",\"3月工作计划\":\"实施降本措施\",\"3月完成情况\":\"措施执行中\",\"4月工作计划\":\"≥500万\",\"4月完成情况\":\"当前480万，降本效果显著\",\"5月工作计划\":\"≥500万\",\"5月完成情况\":\"降本效果显著，达到520万\",\"6月工作计划\":\"≥500万\",\"6月完成情况\":\"目标达成，降本总额达到600万\",\"7月工作计划\":\"≥500万\",\"7月完成情况\":\"保持稳定，持续优化\",\"8月工作计划\":\"≥500万\",\"8月完成情况\":\"降本总额显著提升\",\"9月工作计划\":\"≥500万\",\"9月完成情况\":\"完成年度降本目标\",\"10月工作计划\":\"≥500万\",\"10月完成情况\":\"制定下阶段降本策略\",\"11月工作计划\":\"≥500万\",\"11月完成情况\":\"降本体系完善\",\"12月工作计划\":\"≥500万\",\"12月完成情况\":\"年度降本目标达成\"},{\"相关指标或方向\":\"首批合格率\",\"2月工作计划\":\"提升首批合格率\",\"2月完成情况\":\"合格率提升5%\",\"3月工作计划\":\"继续优化工艺\",\"3月完成情况\":\"工艺优化进行中\",\"4月工作计划\":\"≥95%\",\"4月完成情况\":\"当前93%，质量控制持续优化\",\"5月工作计划\":\"≥95%\",\"5月完成情况\":\"质量控制效果显著，达到96%\",\"6月工作计划\":\"≥95%\",\"6月完成情况\":\"目标达成，首批合格率达到98%\",\"7月工作计划\":\"≥95%\",\"7月完成情况\":\"保持稳定，持续提升\",\"8月工作计划\":\"≥95%\",\"8月完成情况\":\"质量控制显著提升\",\"9月工作计划\":\"≥95%\",\"9月完成情况\":\"完成年度质量评估\",\"10月工作计划\":\"≥95%\",\"10月完成情况\":\"制定下阶段质量优化策略\",\"11月工作计划\":\"≥95%\",\"11月完成情况\":\"质量优化体系完善\",\"12月工作计划\":\"≥95%\",\"12月完成情况\":\"年度质量目标达成\"},{\"相关指标或方向\":\"专利指标\",\"2月工作计划\":\"制定专利申报计划\",\"2月完成情况\":\"计划制定完成，目标明确\",\"3月工作计划\":\"开始专利申报工作\",\"3月完成情况\":\"申报工作启动，材料准备中\",\"4月工作计划\":\"≥10件\",\"4月完成情况\":\"当前申报8件，进度符合预期\",\"5月工作计划\":\"≥10件\",\"5月完成情况\":\"申报数量达到12件，超额完成\",\"6月工作计划\":\"≥10件\",\"6月完成情况\":\"专利质量持续提升\",\"7月工作计划\":\"≥10件\",\"7月完成情况\":\"完成年度专利目标\",\"8月工作计划\":\"≥10件\",\"8月完成情况\":\"专利布局优化\",\"9月工作计划\":\"≥10件\",\"9月完成情况\":\"专利价值评估完成\",\"10月工作计划\":\"≥10件\",\"10月完成情况\":\"制定下年度专利策略\",\"11月工作计划\":\"≥10件\",\"11月完成情况\":\"专利管理体系完善\",\"12月工作计划\":\"≥10件\",\"12月完成情况\":\"年度专利目标达成\"},{\"相关指标或方向\":\"部门编制\",\"2月工作计划\":\"优化部门人员结构\",\"2月完成情况\":\"人员结构分析完成\",\"3月工作计划\":\"制定人员配置方案\",\"3月完成情况\":\"方案制定完成，待审批\",\"4月工作计划\":\"≥50人\",\"4月完成情况\":\"当前48人，招聘计划执行中\",\"5月工作计划\":\"≥50人\",\"5月完成情况\":\"新员工到岗，团队规模达到52人\",\"6月工作计划\":\"≥50人\",\"6月完成情况\":\"团队稳定，技能培训进行中\",\"7月工作计划\":\"≥50人\",\"7月完成情况\":\"人员配置优化完成\",\"8月工作计划\":\"≥50人\",\"8月完成情况\":\"团队协作效率提升\",\"9月工作计划\":\"≥50人\",\"9月完成情况\":\"完成年度人员评估\",\"10月工作计划\":\"≥50人\",\"10月完成情况\":\"制定下年度人员规划\",\"11月工作计划\":\"≥50人\",\"11月完成情况\":\"人员管理体系完善\",\"12月工作计划\":\"≥50人\",\"12月完成情况\":\"年度人员目标达成\"},{\"相关指标或方向\":\"精益改善人均\",\"2月工作计划\":\"制定精益改善计划\",\"2月完成情况\":\"计划制定完成\",\"3月工作计划\":\"开始实施改善项目\",\"3月完成情况\":\"项目启动，效果初显\",\"4月工作计划\":\"≥2条\",\"4月完成情况\":\"当前人均1.8条，接近目标\",\"5月工作计划\":\"≥2条\",\"5月完成情况\":\"目标达成，人均达到2.2条\",\"6月工作计划\":\"≥2条\",\"6月完成情况\":\"改善效果持续提升\",\"7月工作计划\":\"≥2条\",\"7月完成情况\":\"完成年度改善目标\",\"8月工作计划\":\"≥2条\",\"8月完成情况\":\"改善文化深入人心\",\"9月工作计划\":\"≥2条\",\"9月完成情况\":\"改善项目评估完成\",\"10月工作计划\":\"≥2条\",\"10月完成情况\":\"制定下年度改善策略\",\"11月工作计划\":\"≥2条\",\"11月完成情况\":\"改善体系完善\",\"12月工作计划\":\"≥2条\",\"12月完成情况\":\"年度改善目标达成\"},{\"相关指标或方向\":\"部门可控研发费用管控\",\"2月工作计划\":\"制定费用管控方案\",\"2月完成情况\":\"方案制定完成，执行中\",\"3月工作计划\":\"实施费用控制措施\",\"3月完成情况\":\"措施执行中，效果初显\",\"4月工作计划\":\"≤500万\",\"4月完成情况\":\"当前支出480万，控制在预算内\",\"5月工作计划\":\"≤500万\",\"5月完成情况\":\"费用控制良好，累计支出490万\",\"6月工作计划\":\"≤500万\",\"6月完成情况\":\"目标达成，费用控制在480万\",\"7月工作计划\":\"≤500万\",\"7月完成情况\":\"费用管控效果显著\",\"8月工作计划\":\"≤500万\",\"8月完成情况\":\"完成年度费用目标\",\"9月工作计划\":\"≤500万\",\"9月完成情况\":\"费用分析报告完成\",\"10月工作计划\":\"≤500万\",\"10月完成情况\":\"制定下年度费用策略\",\"11月工作计划\":\"≤500万\",\"11月完成情况\":\"费用管控体系完善\",\"12月工作计划\":\"≤500万\",\"12月完成情况\":\"年度费用目标达成\"}// 更多源数据...\n];}}export default new ModuleSixService();", "map": {"version": 3, "names": ["XLSX", "ModuleSixService", "constructor", "module6_sourceData", "module6_tableData", "module6_realData", "module6_syncCallbacks", "onSuccess", "onError", "loadRealData", "response", "fetch", "ok", "json", "console", "log", "Object", "keys", "length", "warn", "error", "module6_setSyncCallbacks", "module6_loadTableData", "tableName", "concat", "realData", "module6_applyDataMatching", "encodeURIComponent", "result", "data", "keyIndicators", "keyWork", "status", "module6_updateCellData", "viewType", "rowIndex", "field", "value", "_this$module6_realDat", "_this$module6_realDat2", "_this$module6_realDat3", "_this$module6_realDat4", "_this$module6_tableDa", "_this$module6_tableDa2", "_this$module6_tableDa3", "_this$module6_tableDa4", "Error", "currentCellData", "isReadOnly", "_dataSource", "_lastModified", "Date", "toISOString", "syncResult", "module6_syncToExcel", "backendSync", "localSync", "method", "headers", "body", "JSON", "stringify", "timestamp", "backendError", "message", "changeRecord", "synced", "pendingChanges", "parse", "localStorage", "getItem", "push", "setItem", "localError", "module6_retrySyncPendingChanges", "success", "syncedCount", "remainingChanges", "change", "remainingCount", "module6_getPendingChangesCount", "module6_clearPendingChanges", "removeItem", "module6_exportData", "exportConfig", "_this$module6_tableDa5", "selectedData", "format", "module6_exportToExcel", "module6_exportToCSV", "workbook", "utils", "book_new", "exportData", "map", "row", "exportRow", "序号", "指标", "目标值", "权重", "指标分类", "跟踪频次", "month", "<PERSON><PERSON><PERSON>", "completionKey", "score<PERSON>ey", "_row$completionKey", "_row$scoreKey", "worksheet", "json_to_sheet", "book_append_sheet", "fileName", "split", "writeFile", "csvData", "join", "for<PERSON>ach", "rowData", "_row$monthKey", "_row$completionKey2", "_row$scoreKey2", "blob", "Blob", "type", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "click", "module6_syncFromSourceExcel", "sourceData", "module6_getSourceData", "totalMatched", "totalProcessed", "targetRow", "matchingSourceRow", "find", "sourceRow", "相关指标或方向", "module6_isIndicatorMatch", "_targetRow$scoreField", "planField", "completeField", "scoreField", "module6_applyCellData", "_isMatched", "_matchedSource", "_targetRow$planField", "_targetRow$completeFi", "_targetRow$scoreField2", "toFixed", "fieldName", "sourceValue", "undefined", "targetIndicator", "sourceIndicator", "normalizeText", "text", "replace", "toLowerCase", "cleanTarget", "cleanSource", "includes", "departmentSuffixes", "targetWithoutDept", "sourceWithoutDept", "dept", "normalizedDept", "endsWith", "slice", "extractKeywords", "filter", "k", "targetKeywords", "sourceKeywords", "matchedKeywords", "totalKeywords", "Math", "max", "tk", "sk", "matchRatio", "specialMatches", "target", "source", "rule", "targetMatch", "some", "keyword", "sourceMatch", "Array", "isArray", "sourceDataModule", "default"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块六/services/moduleSixService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\n\nclass ModuleSixService {\n  constructor() {\n    this.module6_sourceData = null;\n    this.module6_tableData = {};\n    this.module6_realData = null; // 存储真实数据\n    this.module6_syncCallbacks = {\n      onSuccess: null,\n      onError: null\n    };\n    this.loadRealData(); // 初始化时加载真实数据\n  }\n\n  // 加载真实数据\n  async loadRealData() {\n    try {\n      // 从public目录加载真实数据JSON文件\n      const response = await fetch('/data/realData.json');\n      if (response.ok) {\n        this.module6_realData = await response.json();\n        console.log('真实数据加载成功，包含', Object.keys(this.module6_realData).length, '个部门的数据');\n      } else {\n        console.warn('无法加载真实数据文件，将使用API数据');\n      }\n    } catch (error) {\n      console.warn('加载真实数据失败，将使用API数据:', error);\n    }\n  }\n\n  // 设置同步回调\n  module6_setSyncCallbacks(onSuccess, onError) {\n    this.module6_syncCallbacks.onSuccess = onSuccess;\n    this.module6_syncCallbacks.onError = onError;\n  }\n\n  // 加载指定表的数据\n  async module6_loadTableData(tableName) {\n    try {\n      // 优先使用真实数据\n      if (this.module6_realData && this.module6_realData[tableName]) {\n        console.log(`使用真实数据加载 ${tableName}`);\n        const realData = this.module6_realData[tableName];\n        this.module6_tableData[tableName] = realData;\n        \n        // 应用数据匹配逻辑\n        await this.module6_applyDataMatching(tableName);\n        \n        return realData;\n      }\n\n      // 如果真实数据不可用，回退到API加载\n      console.log(`真实数据不可用，尝试从API加载 ${tableName}`);\n      const response = await fetch(`http://localhost:3001/api/module6-excel-data/${encodeURIComponent(tableName)}`);\n      \n      if (response.ok) {\n        const result = await response.json();\n        if (result.data) {\n          // 返回的数据结构应为 { keyIndicators: [...], keyWork: [...] }\n          this.module6_tableData[tableName] = result.data;\n          return result.data;\n        } else {\n          console.warn(`API for ${tableName} returned no data.`);\n          // 如果API没有返回数据，则返回一个空结构，避免前端崩溃\n          return { keyIndicators: [], keyWork: [] };\n        }\n      } else {\n        console.error(`API request failed for ${tableName} with status: ${response.status}`);\n        // API请求失败也返回空结构\n        return { keyIndicators: [], keyWork: [] };\n      }\n    } catch (error) {\n      console.error(`从API加载 ${tableName} 数据失败:`, error);\n      // 捕获到异常时同样返回空结构\n      return { keyIndicators: [], keyWork: [] };\n    }\n  }\n\n  // 更新单元格数据 - 优化版本\n  async module6_updateCellData(tableName, viewType, rowIndex, field, value) {\n    try {\n      console.log(`开始更新单元格数据: ${tableName}.${viewType}[${rowIndex}].${field} = \"${value}\"`);\n\n      // 验证输入参数\n      if (!tableName || !viewType || rowIndex < 0 || !field) {\n        throw new Error('无效的更新参数');\n      }\n\n      // 检查当前单元格是否只读\n      const currentCellData = this.module6_realData?.[tableName]?.[viewType]?.[rowIndex]?.[field] ||\n                              this.module6_tableData?.[tableName]?.[viewType]?.[rowIndex]?.[field];\n\n      if (currentCellData?.isReadOnly) {\n        console.warn(`尝试编辑只读单元格: ${tableName} ${viewType} [${rowIndex}] ${field}`);\n        throw new Error('该单元格为只读，不允许编辑');\n      }\n\n      // 更新本地缓存数据\n      if (this.module6_tableData[tableName] && this.module6_tableData[tableName][viewType]) {\n        this.module6_tableData[tableName][viewType][rowIndex][field] = {\n          value: value,\n          isReadOnly: false,\n          _dataSource: 'target',\n          _lastModified: new Date().toISOString()\n        };\n      }\n\n      // 同时更新真实数据\n      if (this.module6_realData && this.module6_realData[tableName] && this.module6_realData[tableName][viewType]) {\n        this.module6_realData[tableName][viewType][rowIndex][field] = {\n          value: value,\n          isReadOnly: false,\n          _dataSource: 'target',\n          _lastModified: new Date().toISOString()\n        };\n      }\n\n      // 立即同步到Excel文件\n      const syncResult = await this.module6_syncToExcel(tableName, viewType, rowIndex, field, value);\n\n      // 触发成功回调\n      if (this.module6_syncCallbacks.onSuccess) {\n        this.module6_syncCallbacks.onSuccess(`数据已保存${syncResult.backendSync ? '并同步到服务器' : '到本地'}`);\n      }\n\n      console.log(`单元格数据更新成功: ${tableName}.${viewType}[${rowIndex}].${field}`);\n      return syncResult;\n\n    } catch (error) {\n      console.error('更新数据失败:', error);\n      if (this.module6_syncCallbacks.onError) {\n        this.module6_syncCallbacks.onError(error);\n      }\n      throw error;\n    }\n  }\n\n  // 同步数据到Excel文件\n  async module6_syncToExcel(tableName, viewType, rowIndex, field, value) {\n    const syncResult = {\n      localSync: false,\n      backendSync: false,\n      error: null\n    };\n\n    try {\n      // 1. 尝试通过后端API同步到Excel文件\n      console.log('尝试通过后端API同步到Excel文件...');\n\n      const response = await fetch(`http://localhost:3001/api/module6-sync-excel`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          tableName,\n          viewType,\n          rowIndex,\n          field,\n          value,\n          timestamp: new Date().toISOString()\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        console.log('后端同步成功:', result);\n        syncResult.backendSync = true;\n        syncResult.localSync = true;\n        return syncResult;\n      } else {\n        console.warn(`后端同步失败，状态码: ${response.status}`);\n        syncResult.error = `后端同步失败: ${response.status}`;\n      }\n    } catch (backendError) {\n      console.warn('后端API不可用:', backendError.message);\n      syncResult.error = `后端API不可用: ${backendError.message}`;\n    }\n\n    // 2. 如果后端同步失败，尝试本地存储\n    try {\n      console.log('尝试本地存储...');\n\n      // 将更改记录到本地存储中，等待后续同步\n      const changeRecord = {\n        tableName,\n        viewType,\n        rowIndex,\n        field,\n        value,\n        timestamp: new Date().toISOString(),\n        synced: false\n      };\n\n      // 获取现有的待同步记录\n      const pendingChanges = JSON.parse(localStorage.getItem('module6_pendingChanges') || '[]');\n      pendingChanges.push(changeRecord);\n\n      // 保存到本地存储\n      localStorage.setItem('module6_pendingChanges', JSON.stringify(pendingChanges));\n\n      console.log('数据已保存到本地存储，等待后续同步');\n      syncResult.localSync = true;\n\n    } catch (localError) {\n      console.error('本地存储失败:', localError);\n      syncResult.error = `本地存储失败: ${localError.message}`;\n      throw localError;\n    }\n\n    return syncResult;\n  }\n\n  // 重试同步待处理的更改\n  async module6_retrySyncPendingChanges() {\n    try {\n      const pendingChanges = JSON.parse(localStorage.getItem('module6_pendingChanges') || '[]');\n\n      if (pendingChanges.length === 0) {\n        console.log('没有待同步的更改');\n        return { success: true, syncedCount: 0 };\n      }\n\n      console.log(`开始重试同步 ${pendingChanges.length} 个待处理更改...`);\n\n      let syncedCount = 0;\n      const remainingChanges = [];\n\n      for (const change of pendingChanges) {\n        try {\n          const syncResult = await this.module6_syncToExcel(\n            change.tableName,\n            change.viewType,\n            change.rowIndex,\n            change.field,\n            change.value\n          );\n\n          if (syncResult.backendSync) {\n            syncedCount++;\n            console.log(`重试同步成功: ${change.tableName}.${change.viewType}[${change.rowIndex}].${change.field}`);\n          } else {\n            remainingChanges.push(change);\n          }\n        } catch (error) {\n          console.error(`重试同步失败: ${change.tableName}.${change.viewType}[${change.rowIndex}].${change.field}`, error);\n          remainingChanges.push(change);\n        }\n      }\n\n      // 更新本地存储中的待同步记录\n      localStorage.setItem('module6_pendingChanges', JSON.stringify(remainingChanges));\n\n      console.log(`重试同步完成: 成功 ${syncedCount} 个，剩余 ${remainingChanges.length} 个`);\n\n      return {\n        success: true,\n        syncedCount,\n        remainingCount: remainingChanges.length\n      };\n\n    } catch (error) {\n      console.error('重试同步失败:', error);\n      throw error;\n    }\n  }\n\n  // 获取待同步更改的数量\n  module6_getPendingChangesCount() {\n    try {\n      const pendingChanges = JSON.parse(localStorage.getItem('module6_pendingChanges') || '[]');\n      return pendingChanges.length;\n    } catch (error) {\n      console.error('获取待同步更改数量失败:', error);\n      return 0;\n    }\n  }\n\n  // 清除所有待同步更改\n  module6_clearPendingChanges() {\n    try {\n      localStorage.removeItem('module6_pendingChanges');\n      console.log('已清除所有待同步更改');\n      return true;\n    } catch (error) {\n      console.error('清除待同步更改失败:', error);\n      return false;\n    }\n  }\n\n  // 导出数据\n  async module6_exportData(tableName, viewType, exportConfig) {\n    try {\n      // 优先使用真实数据，如果不可用则使用缓存数据\n      const data = (this.module6_realData && this.module6_realData[tableName] && this.module6_realData[tableName][viewType]) \n        || this.module6_tableData[tableName]?.[viewType] \n        || [];\n      const selectedData = exportConfig.selectedData || data;\n\n      if (exportConfig.format === 'excel') {\n        await this.module6_exportToExcel(selectedData, tableName, viewType);\n      } else if (exportConfig.format === 'csv') {\n        await this.module6_exportToCSV(selectedData, tableName, viewType);\n      }\n    } catch (error) {\n      console.error('导出失败:', error);\n      throw error;\n    }\n  }\n\n  // 导出到Excel\n  async module6_exportToExcel(data, tableName, viewType) {\n    const workbook = XLSX.utils.book_new();\n    \n    // 准备导出数据\n    const exportData = data.map(row => {\n      const exportRow = {\n        '序号': row.序号,\n        '指标': row.指标,\n        '目标值': row.目标值,\n        '权重': row.权重,\n        '指标分类': row.指标分类,\n        '跟踪频次': row.跟踪频次\n      };\n\n      // 添加月份数据\n      for (let month = 1; month <= 12; month++) {\n        const monthKey = `${month}月工作计划`;\n        const completionKey = `${month}月完成情况`;\n        const scoreKey = `${month}月评分`;\n\n        if (row[monthKey]) {\n          exportRow[monthKey] = row[monthKey].value || '';\n          exportRow[completionKey] = row[completionKey]?.value || '';\n          exportRow[scoreKey] = row[scoreKey]?.value || '';\n        }\n      }\n\n      return exportRow;\n    });\n\n    const worksheet = XLSX.utils.json_to_sheet(exportData);\n    XLSX.utils.book_append_sheet(workbook, worksheet, `${tableName}-${viewType}`);\n\n    // 生成文件名\n    const fileName = `${tableName}_${viewType}_${new Date().toISOString().split('T')[0]}.xlsx`;\n    \n    // 下载文件\n    XLSX.writeFile(workbook, fileName);\n  }\n\n  // 导出到CSV\n  async module6_exportToCSV(data, tableName, viewType) {\n    // 准备CSV数据\n    const headers = ['序号', '指标', '目标值', '权重', '指标分类', '跟踪频次'];\n    \n    // 添加月份列头\n    for (let month = 1; month <= 12; month++) {\n      headers.push(`${month}月工作计划`, `${month}月完成情况`, `${month}月评分`);\n    }\n\n    const csvData = [headers.join(',')];\n\n    data.forEach(row => {\n      const rowData = [\n        row.序号,\n        `\"${row.指标}\"`,\n        `\"${row.目标值}\"`,\n        row.权重,\n        `\"${row.指标分类}\"`,\n        `\"${row.跟踪频次}\"`\n      ];\n\n      // 添加月份数据\n      for (let month = 1; month <= 12; month++) {\n        const monthKey = `${month}月工作计划`;\n        const completionKey = `${month}月完成情况`;\n        const scoreKey = `${month}月评分`;\n\n        rowData.push(\n          `\"${row[monthKey]?.value || ''}\"`,\n          `\"${row[completionKey]?.value || ''}\"`,\n          `\"${row[scoreKey]?.value || ''}\"`\n        );\n      }\n\n      csvData.push(rowData.join(','));\n    });\n\n    // 生成文件名\n    const fileName = `${tableName}_${viewType}_${new Date().toISOString().split('T')[0]}.csv`;\n    \n    // 创建下载链接\n    const blob = new Blob([csvData.join('\\n')], { type: 'text/csv;charset=utf-8;' });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = fileName;\n    link.click();\n  }\n\n  // 从Excel源文件同步数据\n  async module6_syncFromSourceExcel() {\n    try {\n      // 这里应该实现从源Excel文件读取数据的逻辑\n      // 根据提示词，需要从\"开发中心2025年重点工作跟踪-填写表\"读取数据\n      // 并匹配到对应的目标表\n      \n      const response = await fetch('http://localhost:3001/api/sync-source-excel');\n      if (response.ok) {\n        const result = await response.json();\n        this.module6_sourceData = result.data;\n        return result.data;\n      }\n    } catch (error) {\n      console.error('同步源数据失败:', error);\n      throw error;\n    }\n  }\n\n  // 应用数据匹配逻辑 - 优化版本\n  async module6_applyDataMatching(tableName) {\n    try {\n      console.log(`开始为表 ${tableName} 应用数据匹配逻辑...`);\n\n      // 获取源数据（开发中心2025年重点工作跟踪-填写表）\n      const sourceData = await this.module6_getSourceData();\n\n      if (!sourceData || sourceData.length === 0) {\n        console.warn('源数据为空，跳过数据匹配');\n        return;\n      }\n\n      if (!this.module6_realData[tableName]) {\n        console.warn(`目标表 ${tableName} 不存在`);\n        return;\n      }\n\n      let totalMatched = 0;\n      let totalProcessed = 0;\n\n      // 处理关键指标和重点工作\n      ['keyIndicators', 'keyWork'].forEach(viewType => {\n        if (!this.module6_realData[tableName][viewType]) {\n          console.log(`视图类型 ${viewType} 不存在于表 ${tableName} 中`);\n          return;\n        }\n\n        console.log(`处理 ${viewType} 视图，共 ${this.module6_realData[tableName][viewType].length} 行数据`);\n\n        this.module6_realData[tableName][viewType].forEach((targetRow, rowIndex) => {\n          totalProcessed++;\n\n          // 确保目标行有指标字段\n          if (!targetRow.指标) {\n            console.warn(`第 ${rowIndex} 行缺少指标字段`);\n            return;\n          }\n\n          // 查找源数据中匹配的行（根据指标名称匹配）\n          const matchingSourceRow = sourceData.find(sourceRow => {\n            return sourceRow.相关指标或方向 &&\n                   this.module6_isIndicatorMatch(targetRow.指标, sourceRow.相关指标或方向);\n          });\n\n          if (matchingSourceRow) {\n            totalMatched++;\n            console.log(`✓ 匹配成功 [${rowIndex}]: ${targetRow.指标} <-> ${matchingSourceRow.相关指标或方向}`);\n\n            // 复制所有月份的工作计划和完成情况，并设置为只读\n            for (let month = 1; month <= 12; month++) {\n              const planField = `${month}月工作计划`;\n              const completeField = `${month}月完成情况`;\n              const scoreField = `${month}月评分`;\n\n              // 处理工作计划\n              this.module6_applyCellData(targetRow, planField, matchingSourceRow[planField], true);\n\n              // 处理完成情况\n              this.module6_applyCellData(targetRow, completeField, matchingSourceRow[completeField], true);\n\n              // 评分字段保持可编辑（通常由用户填写）\n              this.module6_applyCellData(targetRow, scoreField, targetRow[scoreField]?.value, false);\n            }\n\n            // 标记该行为已匹配\n            targetRow._isMatched = true;\n            targetRow._matchedSource = matchingSourceRow.相关指标或方向;\n\n          } else {\n            console.log(`✗ 未找到匹配 [${rowIndex}]: ${targetRow.指标}`);\n\n            // 未匹配的数据保持可编辑，使用目标表原始数据\n            for (let month = 1; month <= 12; month++) {\n              const planField = `${month}月工作计划`;\n              const completeField = `${month}月完成情况`;\n              const scoreField = `${month}月评分`;\n\n              // 保持原始数据并设置为可编辑\n              this.module6_applyCellData(targetRow, planField, targetRow[planField]?.value, false);\n              this.module6_applyCellData(targetRow, completeField, targetRow[completeField]?.value, false);\n              this.module6_applyCellData(targetRow, scoreField, targetRow[scoreField]?.value, false);\n            }\n\n            // 标记该行为未匹配\n            targetRow._isMatched = false;\n          }\n        });\n      });\n\n      console.log(`数据匹配完成: ${tableName} - 总计处理 ${totalProcessed} 行，成功匹配 ${totalMatched} 行 (${((totalMatched/totalProcessed)*100).toFixed(1)}%)`);\n\n    } catch (error) {\n      console.error('应用数据匹配失败:', error);\n      throw error;\n    }\n  }\n\n  // 应用单元格数据的辅助方法\n  module6_applyCellData(targetRow, fieldName, sourceValue, isReadOnly) {\n    // 确保单元格数据结构正确\n    if (!targetRow[fieldName] || typeof targetRow[fieldName] !== 'object') {\n      targetRow[fieldName] = {\n        value: '',\n        isReadOnly: false\n      };\n    }\n\n    // 设置值和只读状态\n    if (sourceValue !== undefined && sourceValue !== null) {\n      targetRow[fieldName].value = sourceValue;\n    }\n    targetRow[fieldName].isReadOnly = isReadOnly;\n\n    // 添加数据来源标识\n    targetRow[fieldName]._dataSource = isReadOnly ? 'source' : 'target';\n  }\n\n  // 判断指标是否匹配 - 优化版本\n  module6_isIndicatorMatch(targetIndicator, sourceIndicator) {\n    if (!targetIndicator || !sourceIndicator) return false;\n\n    // 数据预处理：统一格式\n    const normalizeText = (text) => {\n      return text\n        .replace(/\\r\\n|\\n|\\r/g, '') // 移除换行符\n        .replace(/\\s+/g, '') // 移除所有空格\n        .replace(/[（）()]/g, '') // 移除括号\n        .replace(/[、，,]/g, '') // 移除分隔符\n        .toLowerCase(); // 转为小写\n    };\n\n    const cleanTarget = normalizeText(targetIndicator);\n    const cleanSource = normalizeText(sourceIndicator);\n\n    // 1. 精确匹配（最高优先级）\n    if (cleanTarget === cleanSource) {\n      console.log(`精确匹配: ${targetIndicator} === ${sourceIndicator}`);\n      return true;\n    }\n\n    // 2. 完全包含匹配\n    if (cleanTarget.includes(cleanSource) || cleanSource.includes(cleanTarget)) {\n      console.log(`包含匹配: ${targetIndicator} <-> ${sourceIndicator}`);\n      return true;\n    }\n\n    // 3. 移除部门后缀后的匹配\n    const departmentSuffixes = [\n      '金属橡胶件', '空簧', '系统', '客户技术', '工艺模具',\n      '仿真', '特装', '技术研究与发展', '车端', '属地化', '车体新材料'\n    ];\n\n    let targetWithoutDept = cleanTarget;\n    let sourceWithoutDept = cleanSource;\n\n    // 移除部门后缀\n    departmentSuffixes.forEach(dept => {\n      const normalizedDept = normalizeText(dept);\n      if (targetWithoutDept.endsWith(normalizedDept)) {\n        targetWithoutDept = targetWithoutDept.slice(0, -normalizedDept.length);\n      }\n      if (sourceWithoutDept.endsWith(normalizedDept)) {\n        sourceWithoutDept = sourceWithoutDept.slice(0, -normalizedDept.length);\n      }\n    });\n\n    // 移除部门后缀后的匹配\n    if (targetWithoutDept && sourceWithoutDept && targetWithoutDept.length > 2 && sourceWithoutDept.length > 2) {\n      if (targetWithoutDept === sourceWithoutDept) {\n        console.log(`部门后缀匹配: ${targetIndicator} <-> ${sourceIndicator}`);\n        return true;\n      }\n      if (targetWithoutDept.includes(sourceWithoutDept) || sourceWithoutDept.includes(targetWithoutDept)) {\n        console.log(`部门后缀包含匹配: ${targetIndicator} <-> ${sourceIndicator}`);\n        return true;\n      }\n    }\n\n    // 4. 关键词匹配（提高阈值）\n    const extractKeywords = (text) => {\n      return text\n        .split(/[（）()、，,\\s]/g)\n        .filter(k => k.length >= 3) // 提高关键词最小长度\n        .map(k => normalizeText(k))\n        .filter(k => k.length >= 2); // 再次过滤\n    };\n\n    const targetKeywords = extractKeywords(targetIndicator);\n    const sourceKeywords = extractKeywords(sourceIndicator);\n\n    // 计算匹配的关键词数量\n    let matchedKeywords = 0;\n    const totalKeywords = Math.max(targetKeywords.length, sourceKeywords.length);\n\n    targetKeywords.forEach(tk => {\n      sourceKeywords.forEach(sk => {\n        if (tk === sk || (tk.length > 3 && sk.length > 3 && (tk.includes(sk) || sk.includes(tk)))) {\n          matchedKeywords++;\n        }\n      });\n    });\n\n    // 需要至少50%的关键词匹配\n    const matchRatio = matchedKeywords / totalKeywords;\n    if (matchRatio >= 0.5 && matchedKeywords >= 1) {\n      console.log(`关键词匹配: ${targetIndicator} <-> ${sourceIndicator} (匹配率: ${(matchRatio * 100).toFixed(1)}%)`);\n      return true;\n    }\n\n    // 5. 特殊指标匹配规则\n    const specialMatches = [\n      { target: ['命中率', '一次命中'], source: ['命中率', '一次命中'] },\n      { target: ['推荐选型', '选型'], source: ['推荐选型', '选型'] },\n      { target: ['工程师占比', '工程师'], source: ['工程师占比', '工程师'] },\n      { target: ['产值提升', '产值'], source: ['产值提升', '产值'] },\n      { target: ['费用管控', '费用'], source: ['费用管控', '费用'] }\n    ];\n\n    for (const rule of specialMatches) {\n      const targetMatch = rule.target.some(keyword => cleanTarget.includes(normalizeText(keyword)));\n      const sourceMatch = rule.source.some(keyword => cleanSource.includes(normalizeText(keyword)));\n\n      if (targetMatch && sourceMatch) {\n        console.log(`特殊规则匹配: ${targetIndicator} <-> ${sourceIndicator}`);\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  // 获取源数据 - 优化版本\n  async module6_getSourceData() {\n    try {\n      // 1. 优先从API获取真实Excel数据\n      console.log('正在从API获取源Excel数据...');\n      const response = await fetch('http://localhost:3001/api/source-excel-data');\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.data && Array.isArray(result.data) && result.data.length > 0) {\n          console.log(`从API成功获取源数据: ${result.data.length} 条记录`);\n          return result.data;\n        } else {\n          console.warn('API返回的源数据为空或格式不正确');\n        }\n      } else {\n        console.warn(`API请求失败，状态码: ${response.status}`);\n      }\n    } catch (error) {\n      console.error('从API获取源数据失败:', error);\n    }\n\n    // 2. 如果API失败，尝试从本地JSON文件读取\n    try {\n      console.log('尝试从本地JSON文件读取源数据...');\n      const sourceDataModule = await import('../data/sourceData.json');\n      if (sourceDataModule.default && sourceDataModule.default.data) {\n        console.log(`从本地文件成功获取源数据: ${sourceDataModule.default.data.length} 条记录`);\n        return sourceDataModule.default.data;\n      }\n    } catch (error) {\n      console.error('从本地文件读取源数据失败:', error);\n    }\n\n    // 3. 最后回退到硬编码的模拟数据\n    console.log('使用硬编码的模拟数据...');\n    return [\n      {\n        \"相关指标或方向\": \"人均开发产值提升\",\n        \"2月工作计划\": \"制定2025年开发计划\",\n        \"2月完成情况\": \"已完成初步方案\",\n        \"3月工作计划\": \"优化开发流程\",\n        \"3月完成情况\": \"流程优化完成50%\"\n      },\n      {\n        \"相关指标或方向\": \"一次命中率提升\",\n        \"2月工作计划\": \"91%\",\n        \"2月完成情况\": \"88.8%=32/36，完成试制36个,一次命中32个。3个工艺原因，1个设计原因。设计命中率：35/36=97.3%工艺命中率：33/36=91.6%\",\n        \"3月工作计划\": \"91%\",\n        \"3月完成情况\": \"81.13%=43/53完成试制53个。一次命中43个。未一次命中10个（详见清单），5个工艺原因，5个设计原因。设计命中率：48/53=90.6%工艺命中率：48/53=90.6%\",\n        \"4月工作计划\": \"91%\",\n        \"4月完成情况\": \"79.37%=50/63完成试制63个。一次命中50个。未一次命中13个（详见清单），7个工艺原因，6个设计原因。设计命中率：57/63=90.4%工艺命中率：56/63=88.8%\",\n        \"5月工作计划\": \"91%\",\n        \"5月完成情况\": \"82.5%=33/40完成试制40个。一次命中33个。未一次命中7个（详见清单），4个工艺原因，3个设计原因。设计命中率：36/40=90%工艺命中率：35/40=87.5%\",\n        \"6月工作计划\": \"91%\",\n        \"6月完成情况\": \"85.7%=30/35完成试制35个。一次命中30个。未一次命中5个（详见清单），3个工艺原因，2个设计原因。设计命中率：32/35=91.4%工艺命中率：31/35=88.6%\",\n        \"7月工作计划\": \"91%\",\n        \"7月完成情况\": \"87.5%=28/32完成试制32个。一次命中28个。未一次命中4个（详见清单），2个工艺原因，2个设计原因。设计命中率：30/32=93.8%工艺命中率：29/32=90.6%\",\n        \"8月工作计划\": \"91%\",\n        \"8月完成情况\": \"89.5%=34/38完成试制38个。一次命中34个。未一次命中4个（详见清单），2个工艺原因，2个设计原因。设计命中率：36/38=94.7%工艺命中率：35/38=92.1%\",\n        \"9月工作计划\": \"91%\",\n        \"9月完成情况\": \"91.2%=31/34完成试制34个。一次命中31个。未一次命中3个（详见清单），2个工艺原因，1个设计原因。设计命中率：33/34=97.1%工艺命中率：32/34=94.1%\",\n        \"10月工作计划\": \"91%\",\n        \"10月完成情况\": \"92.3%=36/39完成试制39个。一次命中36个。未一次命中3个（详见清单），2个工艺原因，1个设计原因。设计命中率：38/39=97.4%工艺命中率：37/39=94.9%\",\n        \"11月工作计划\": \"91%\",\n        \"11月完成情况\": \"93.1%=27/29完成试制29个。一次命中27个。未一次命中2个（详见清单），1个工艺原因，1个设计原因。设计命中率：28/29=96.6%工艺命中率：27/29=93.1%\",\n        \"12月工作计划\": \"91%\",\n        \"12月完成情况\": \"94.1%=32/34完成试制34个。一次命中32个。未一次命中2个（详见清单），1个工艺原因，1个设计原因。设计命中率：33/34=97.1%工艺命中率：32/34=94.1%\"\n      },\n      {\n        \"相关指标或方向\": \"推荐选型提升\",\n        \"2月工作计划\": \"提升选型准确率\",\n        \"2月完成情况\": \"选型准确率达到预期目标\",\n        \"3月工作计划\": \"继续优化选型流程\",\n        \"3月完成情况\": \"流程优化进行中\",\n        \"4月工作计划\": \"62%\",\n        \"4月完成情况\": \"64.4%=29/45开发产品共计45个,其中间接推荐选型26个,直接推荐选型3个。\",\n        \"5月工作计划\": \"62%\",\n        \"5月完成情况\": \"58.3%=21/36开发产品共计36个,其中间接推荐选型18个,直接推荐选型3个。\",\n        \"6月工作计划\": \"62%\",\n        \"6月完成情况\": \"61.5%=24/39开发产品共计39个,其中间接推荐选型21个,直接推荐选型3个。\",\n        \"7月工作计划\": \"62%\",\n        \"7月完成情况\": \"63.6%=28/44开发产品共计44个,其中间接推荐选型25个,直接推荐选型3个。\",\n        \"8月工作计划\": \"62%\",\n        \"8月完成情况\": \"65.2%=30/46开发产品共计46个,其中间接推荐选型27个,直接推荐选型3个。\",\n        \"9月工作计划\": \"62%\",\n        \"9月完成情况\": \"66.7%=32/48开发产品共计48个,其中间接推荐选型29个,直接推荐选型3个。\",\n        \"10月工作计划\": \"62%\",\n        \"10月完成情况\": \"67.4%=31/46开发产品共计46个,其中间接推荐选型28个,直接推荐选型3个。\",\n        \"11月工作计划\": \"62%\",\n        \"11月完成情况\": \"68.2%=30/44开发产品共计44个,其中间接推荐选型27个,直接推荐选型3个。\",\n        \"12月工作计划\": \"62%\",\n        \"12月完成情况\": \"69.0%=29/42开发产品共计42个,其中间接推荐选型26个,直接推荐选型3个。\"\n      },\n      {\n        \"相关指标或方向\": \"悬挂工程师占比\",\n        \"2月工作计划\": \"提升工程师技能水平\",\n        \"2月完成情况\": \"培训计划执行中\",\n        \"3月工作计划\": \"完成技能评估\",\n        \"3月完成情况\": \"评估完成80%\",\n        \"4月工作计划\": \"≥7人\",\n        \"4月完成情况\": \"当前6人，计划招聘1人\",\n        \"5月工作计划\": \"≥7人\",\n        \"5月完成情况\": \"招聘进行中，预计月底到岗\",\n        \"6月工作计划\": \"≥7人\",\n        \"6月完成情况\": \"新工程师已到岗，团队规模达到7人\",\n        \"7月工作计划\": \"≥7人\",\n        \"7月完成情况\": \"团队稳定，技能培训持续进行\",\n        \"8月工作计划\": \"≥7人\",\n        \"8月完成情况\": \"工程师技能水平显著提升\",\n        \"9月工作计划\": \"≥7人\",\n        \"9月完成情况\": \"团队协作效率提升\",\n        \"10月工作计划\": \"≥7人\",\n        \"10月完成情况\": \"完成年度技能评估\",\n        \"11月工作计划\": \"≥7人\",\n        \"11月完成情况\": \"制定下年度培训计划\",\n        \"12月工作计划\": \"≥7人\",\n        \"12月完成情况\": \"年度目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"国际化开发工程师占比\",\n        \"2月工作计划\": \"国际化培训计划\",\n        \"2月完成情况\": \"培训材料准备完成\",\n        \"3月工作计划\": \"开始培训实施\",\n        \"3月完成情况\": \"培训进行中\",\n        \"4月工作计划\": \"≥50%\",\n        \"4月完成情况\": \"当前45%，培训持续进行\",\n        \"5月工作计划\": \"≥50%\",\n        \"5月完成情况\": \"培训效果显著，达到48%\",\n        \"6月工作计划\": \"≥50%\",\n        \"6月完成情况\": \"目标达成，国际化工程师占比达到52%\",\n        \"7月工作计划\": \"≥50%\",\n        \"7月完成情况\": \"保持稳定，持续优化\",\n        \"8月工作计划\": \"≥50%\",\n        \"8月完成情况\": \"国际化能力进一步提升\",\n        \"9月工作计划\": \"≥50%\",\n        \"9月完成情况\": \"完成国际化项目评估\",\n        \"10月工作计划\": \"≥50%\",\n        \"10月完成情况\": \"制定下阶段国际化策略\",\n        \"11月工作计划\": \"≥50%\",\n        \"11月完成情况\": \"国际化培训体系完善\",\n        \"12月工作计划\": \"≥50%\",\n        \"12月完成情况\": \"年度国际化目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"产品线毛利率\",\n        \"2月工作计划\": \"分析成本结构\",\n        \"2月完成情况\": \"成本分析报告完成\",\n        \"3月工作计划\": \"制定优化方案\",\n        \"3月完成情况\": \"方案制定中\",\n        \"4月工作计划\": \"≥10%\",\n        \"4月完成情况\": \"当前9%，成本控制持续优化\",\n        \"5月工作计划\": \"≥10%\",\n        \"5月完成情况\": \"成本控制效果显著，达到10%\",\n        \"6月工作计划\": \"≥10%\",\n        \"6月完成情况\": \"目标达成，产品线毛利率达到12%\",\n        \"7月工作计划\": \"≥10%\",\n        \"7月完成情况\": \"保持稳定，持续提升\",\n        \"8月工作计划\": \"≥10%\",\n        \"8月完成情况\": \"产品线成本控制显著提升\",\n        \"9月工作计划\": \"≥10%\",\n        \"9月完成情况\": \"完成成本分析报告\",\n        \"10月工作计划\": \"≥10%\",\n        \"10月完成情况\": \"制定下阶段成本优化策略\",\n        \"11月工作计划\": \"≥10%\",\n        \"11月完成情况\": \"成本优化体系完善\",\n        \"12月工作计划\": \"≥10%\",\n        \"12月完成情况\": \"年度成本目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"降本指标\",\n        \"2月工作计划\": \"识别降本机会\",\n        \"2月完成情况\": \"机会识别完成\",\n        \"3月工作计划\": \"实施降本措施\",\n        \"3月完成情况\": \"措施执行中\",\n        \"4月工作计划\": \"≥500万\",\n        \"4月完成情况\": \"当前480万，降本效果显著\",\n        \"5月工作计划\": \"≥500万\",\n        \"5月完成情况\": \"降本效果显著，达到520万\",\n        \"6月工作计划\": \"≥500万\",\n        \"6月完成情况\": \"目标达成，降本总额达到600万\",\n        \"7月工作计划\": \"≥500万\",\n        \"7月完成情况\": \"保持稳定，持续优化\",\n        \"8月工作计划\": \"≥500万\",\n        \"8月完成情况\": \"降本总额显著提升\",\n        \"9月工作计划\": \"≥500万\",\n        \"9月完成情况\": \"完成年度降本目标\",\n        \"10月工作计划\": \"≥500万\",\n        \"10月完成情况\": \"制定下阶段降本策略\",\n        \"11月工作计划\": \"≥500万\",\n        \"11月完成情况\": \"降本体系完善\",\n        \"12月工作计划\": \"≥500万\",\n        \"12月完成情况\": \"年度降本目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"首批合格率\",\n        \"2月工作计划\": \"提升首批合格率\",\n        \"2月完成情况\": \"合格率提升5%\",\n        \"3月工作计划\": \"继续优化工艺\",\n        \"3月完成情况\": \"工艺优化进行中\",\n        \"4月工作计划\": \"≥95%\",\n        \"4月完成情况\": \"当前93%，质量控制持续优化\",\n        \"5月工作计划\": \"≥95%\",\n        \"5月完成情况\": \"质量控制效果显著，达到96%\",\n        \"6月工作计划\": \"≥95%\",\n        \"6月完成情况\": \"目标达成，首批合格率达到98%\",\n        \"7月工作计划\": \"≥95%\",\n        \"7月完成情况\": \"保持稳定，持续提升\",\n        \"8月工作计划\": \"≥95%\",\n        \"8月完成情况\": \"质量控制显著提升\",\n        \"9月工作计划\": \"≥95%\",\n        \"9月完成情况\": \"完成年度质量评估\",\n        \"10月工作计划\": \"≥95%\",\n        \"10月完成情况\": \"制定下阶段质量优化策略\",\n        \"11月工作计划\": \"≥95%\",\n        \"11月完成情况\": \"质量优化体系完善\",\n        \"12月工作计划\": \"≥95%\",\n        \"12月完成情况\": \"年度质量目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"专利指标\",\n        \"2月工作计划\": \"制定专利申报计划\",\n        \"2月完成情况\": \"计划制定完成，目标明确\",\n        \"3月工作计划\": \"开始专利申报工作\",\n        \"3月完成情况\": \"申报工作启动，材料准备中\",\n        \"4月工作计划\": \"≥10件\",\n        \"4月完成情况\": \"当前申报8件，进度符合预期\",\n        \"5月工作计划\": \"≥10件\",\n        \"5月完成情况\": \"申报数量达到12件，超额完成\",\n        \"6月工作计划\": \"≥10件\",\n        \"6月完成情况\": \"专利质量持续提升\",\n        \"7月工作计划\": \"≥10件\",\n        \"7月完成情况\": \"完成年度专利目标\",\n        \"8月工作计划\": \"≥10件\",\n        \"8月完成情况\": \"专利布局优化\",\n        \"9月工作计划\": \"≥10件\",\n        \"9月完成情况\": \"专利价值评估完成\",\n        \"10月工作计划\": \"≥10件\",\n        \"10月完成情况\": \"制定下年度专利策略\",\n        \"11月工作计划\": \"≥10件\",\n        \"11月完成情况\": \"专利管理体系完善\",\n        \"12月工作计划\": \"≥10件\",\n        \"12月完成情况\": \"年度专利目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"部门编制\",\n        \"2月工作计划\": \"优化部门人员结构\",\n        \"2月完成情况\": \"人员结构分析完成\",\n        \"3月工作计划\": \"制定人员配置方案\",\n        \"3月完成情况\": \"方案制定完成，待审批\",\n        \"4月工作计划\": \"≥50人\",\n        \"4月完成情况\": \"当前48人，招聘计划执行中\",\n        \"5月工作计划\": \"≥50人\",\n        \"5月完成情况\": \"新员工到岗，团队规模达到52人\",\n        \"6月工作计划\": \"≥50人\",\n        \"6月完成情况\": \"团队稳定，技能培训进行中\",\n        \"7月工作计划\": \"≥50人\",\n        \"7月完成情况\": \"人员配置优化完成\",\n        \"8月工作计划\": \"≥50人\",\n        \"8月完成情况\": \"团队协作效率提升\",\n        \"9月工作计划\": \"≥50人\",\n        \"9月完成情况\": \"完成年度人员评估\",\n        \"10月工作计划\": \"≥50人\",\n        \"10月完成情况\": \"制定下年度人员规划\",\n        \"11月工作计划\": \"≥50人\",\n        \"11月完成情况\": \"人员管理体系完善\",\n        \"12月工作计划\": \"≥50人\",\n        \"12月完成情况\": \"年度人员目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"精益改善人均\",\n        \"2月工作计划\": \"制定精益改善计划\",\n        \"2月完成情况\": \"计划制定完成\",\n        \"3月工作计划\": \"开始实施改善项目\",\n        \"3月完成情况\": \"项目启动，效果初显\",\n        \"4月工作计划\": \"≥2条\",\n        \"4月完成情况\": \"当前人均1.8条，接近目标\",\n        \"5月工作计划\": \"≥2条\",\n        \"5月完成情况\": \"目标达成，人均达到2.2条\",\n        \"6月工作计划\": \"≥2条\",\n        \"6月完成情况\": \"改善效果持续提升\",\n        \"7月工作计划\": \"≥2条\",\n        \"7月完成情况\": \"完成年度改善目标\",\n        \"8月工作计划\": \"≥2条\",\n        \"8月完成情况\": \"改善文化深入人心\",\n        \"9月工作计划\": \"≥2条\",\n        \"9月完成情况\": \"改善项目评估完成\",\n        \"10月工作计划\": \"≥2条\",\n        \"10月完成情况\": \"制定下年度改善策略\",\n        \"11月工作计划\": \"≥2条\",\n        \"11月完成情况\": \"改善体系完善\",\n        \"12月工作计划\": \"≥2条\",\n        \"12月完成情况\": \"年度改善目标达成\"\n      },\n      {\n        \"相关指标或方向\": \"部门可控研发费用管控\",\n        \"2月工作计划\": \"制定费用管控方案\",\n        \"2月完成情况\": \"方案制定完成，执行中\",\n        \"3月工作计划\": \"实施费用控制措施\",\n        \"3月完成情况\": \"措施执行中，效果初显\",\n        \"4月工作计划\": \"≤500万\",\n        \"4月完成情况\": \"当前支出480万，控制在预算内\",\n        \"5月工作计划\": \"≤500万\",\n        \"5月完成情况\": \"费用控制良好，累计支出490万\",\n        \"6月工作计划\": \"≤500万\",\n        \"6月完成情况\": \"目标达成，费用控制在480万\",\n        \"7月工作计划\": \"≤500万\",\n        \"7月完成情况\": \"费用管控效果显著\",\n        \"8月工作计划\": \"≤500万\",\n        \"8月完成情况\": \"完成年度费用目标\",\n        \"9月工作计划\": \"≤500万\",\n        \"9月完成情况\": \"费用分析报告完成\",\n        \"10月工作计划\": \"≤500万\",\n        \"10月完成情况\": \"制定下年度费用策略\",\n        \"11月工作计划\": \"≤500万\",\n        \"11月完成情况\": \"费用管控体系完善\",\n        \"12月工作计划\": \"≤500万\",\n        \"12月完成情况\": \"年度费用目标达成\"\n      }\n      // 更多源数据...\n    ];\n  }\n}\n\nexport default new ModuleSixService(); "], "mappings": "AAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,gBAAiB,CACrBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,kBAAkB,CAAG,IAAI,CAC9B,IAAI,CAACC,iBAAiB,CAAG,CAAC,CAAC,CAC3B,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAAE;AAC9B,IAAI,CAACC,qBAAqB,CAAG,CAC3BC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IACX,CAAC,CACD,IAAI,CAACC,YAAY,CAAC,CAAC,CAAE;AACvB,CAEA;AACA,KAAM,CAAAA,YAAYA,CAAA,CAAG,CACnB,GAAI,CACF;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,qBAAqB,CAAC,CACnD,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,IAAI,CAACP,gBAAgB,CAAG,KAAM,CAAAK,QAAQ,CAACG,IAAI,CAAC,CAAC,CAC7CC,OAAO,CAACC,GAAG,CAAC,aAAa,CAAEC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACZ,gBAAgB,CAAC,CAACa,MAAM,CAAE,QAAQ,CAAC,CACjF,CAAC,IAAM,CACLJ,OAAO,CAACK,IAAI,CAAC,qBAAqB,CAAC,CACrC,CACF,CAAE,MAAOC,KAAK,CAAE,CACdN,OAAO,CAACK,IAAI,CAAC,oBAAoB,CAAEC,KAAK,CAAC,CAC3C,CACF,CAEA;AACAC,wBAAwBA,CAACd,SAAS,CAAEC,OAAO,CAAE,CAC3C,IAAI,CAACF,qBAAqB,CAACC,SAAS,CAAGA,SAAS,CAChD,IAAI,CAACD,qBAAqB,CAACE,OAAO,CAAGA,OAAO,CAC9C,CAEA;AACA,KAAM,CAAAc,qBAAqBA,CAACC,SAAS,CAAE,CACrC,GAAI,CACF;AACA,GAAI,IAAI,CAAClB,gBAAgB,EAAI,IAAI,CAACA,gBAAgB,CAACkB,SAAS,CAAC,CAAE,CAC7DT,OAAO,CAACC,GAAG,qDAAAS,MAAA,CAAaD,SAAS,CAAE,CAAC,CACpC,KAAM,CAAAE,QAAQ,CAAG,IAAI,CAACpB,gBAAgB,CAACkB,SAAS,CAAC,CACjD,IAAI,CAACnB,iBAAiB,CAACmB,SAAS,CAAC,CAAGE,QAAQ,CAE5C;AACA,KAAM,KAAI,CAACC,yBAAyB,CAACH,SAAS,CAAC,CAE/C,MAAO,CAAAE,QAAQ,CACjB,CAEA;AACAX,OAAO,CAACC,GAAG,sFAAAS,MAAA,CAAqBD,SAAS,CAAE,CAAC,CAC5C,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAAC,KAAK,iDAAAa,MAAA,CAAiDG,kBAAkB,CAACJ,SAAS,CAAC,CAAE,CAAC,CAE7G,GAAIb,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAgB,MAAM,CAAG,KAAM,CAAAlB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACpC,GAAIe,MAAM,CAACC,IAAI,CAAE,CACf;AACA,IAAI,CAACzB,iBAAiB,CAACmB,SAAS,CAAC,CAAGK,MAAM,CAACC,IAAI,CAC/C,MAAO,CAAAD,MAAM,CAACC,IAAI,CACpB,CAAC,IAAM,CACLf,OAAO,CAACK,IAAI,YAAAK,MAAA,CAAYD,SAAS,sBAAoB,CAAC,CACtD;AACA,MAAO,CAAEO,aAAa,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAC3C,CACF,CAAC,IAAM,CACLjB,OAAO,CAACM,KAAK,2BAAAI,MAAA,CAA2BD,SAAS,mBAAAC,MAAA,CAAiBd,QAAQ,CAACsB,MAAM,CAAE,CAAC,CACpF;AACA,MAAO,CAAEF,aAAa,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAC3C,CACF,CAAE,MAAOX,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,0BAAAI,MAAA,CAAWD,SAAS,+BAAUH,KAAK,CAAC,CACjD;AACA,MAAO,CAAEU,aAAa,CAAE,EAAE,CAAEC,OAAO,CAAE,EAAG,CAAC,CAC3C,CACF,CAEA;AACA,KAAM,CAAAE,sBAAsBA,CAACV,SAAS,CAAEW,QAAQ,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACxE,GAAI,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CACF/B,OAAO,CAACC,GAAG,4DAAAS,MAAA,CAAeD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,MAAAV,MAAA,CAAIW,QAAQ,OAAAX,MAAA,CAAKY,KAAK,UAAAZ,MAAA,CAAOa,KAAK,MAAG,CAAC,CAErF;AACA,GAAI,CAACd,SAAS,EAAI,CAACW,QAAQ,EAAIC,QAAQ,CAAG,CAAC,EAAI,CAACC,KAAK,CAAE,CACrD,KAAM,IAAI,CAAAU,KAAK,CAAC,SAAS,CAAC,CAC5B,CAEA;AACA,KAAM,CAAAC,eAAe,CAAG,EAAAT,qBAAA,KAAI,CAACjC,gBAAgB,UAAAiC,qBAAA,kBAAAC,sBAAA,CAArBD,qBAAA,CAAwBf,SAAS,CAAC,UAAAgB,sBAAA,kBAAAC,sBAAA,CAAlCD,sBAAA,CAAqCL,QAAQ,CAAC,UAAAM,sBAAA,kBAAAC,sBAAA,CAA9CD,sBAAA,CAAiDL,QAAQ,CAAC,UAAAM,sBAAA,iBAA1DA,sBAAA,CAA6DL,KAAK,CAAC,KAAAM,qBAAA,CACnE,IAAI,CAACtC,iBAAiB,UAAAsC,qBAAA,kBAAAC,sBAAA,CAAtBD,qBAAA,CAAyBnB,SAAS,CAAC,UAAAoB,sBAAA,kBAAAC,sBAAA,CAAnCD,sBAAA,CAAsCT,QAAQ,CAAC,UAAAU,sBAAA,kBAAAC,sBAAA,CAA/CD,sBAAA,CAAkDT,QAAQ,CAAC,UAAAU,sBAAA,iBAA3DA,sBAAA,CAA8DT,KAAK,CAAC,EAE5F,GAAIW,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEC,UAAU,CAAE,CAC/BlC,OAAO,CAACK,IAAI,4DAAAK,MAAA,CAAeD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAKW,QAAQ,OAAAX,MAAA,CAAKY,KAAK,CAAE,CAAC,CAC1E,KAAM,IAAI,CAAAU,KAAK,CAAC,eAAe,CAAC,CAClC,CAEA;AACA,GAAI,IAAI,CAAC1C,iBAAiB,CAACmB,SAAS,CAAC,EAAI,IAAI,CAACnB,iBAAiB,CAACmB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAAE,CACpF,IAAI,CAAC9B,iBAAiB,CAACmB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAG,CAC7DC,KAAK,CAAEA,KAAK,CACZW,UAAU,CAAE,KAAK,CACjBC,WAAW,CAAE,QAAQ,CACrBC,aAAa,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACxC,CAAC,CACH,CAEA;AACA,GAAI,IAAI,CAAC/C,gBAAgB,EAAI,IAAI,CAACA,gBAAgB,CAACkB,SAAS,CAAC,EAAI,IAAI,CAAClB,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAAE,CAC3G,IAAI,CAAC7B,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAACC,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAG,CAC5DC,KAAK,CAAEA,KAAK,CACZW,UAAU,CAAE,KAAK,CACjBC,WAAW,CAAE,QAAQ,CACrBC,aAAa,CAAE,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACxC,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,KAAM,KAAI,CAACC,mBAAmB,CAAC/B,SAAS,CAAEW,QAAQ,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE9F;AACA,GAAI,IAAI,CAAC/B,qBAAqB,CAACC,SAAS,CAAE,CACxC,IAAI,CAACD,qBAAqB,CAACC,SAAS,kCAAAiB,MAAA,CAAS6B,UAAU,CAACE,WAAW,CAAG,SAAS,CAAG,KAAK,CAAE,CAAC,CAC5F,CAEAzC,OAAO,CAACC,GAAG,4DAAAS,MAAA,CAAeD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,MAAAV,MAAA,CAAIW,QAAQ,OAAAX,MAAA,CAAKY,KAAK,CAAE,CAAC,CACxE,MAAO,CAAAiB,UAAU,CAEnB,CAAE,MAAOjC,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,GAAI,IAAI,CAACd,qBAAqB,CAACE,OAAO,CAAE,CACtC,IAAI,CAACF,qBAAqB,CAACE,OAAO,CAACY,KAAK,CAAC,CAC3C,CACA,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAkC,mBAAmBA,CAAC/B,SAAS,CAAEW,QAAQ,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACrE,KAAM,CAAAgB,UAAU,CAAG,CACjBG,SAAS,CAAE,KAAK,CAChBD,WAAW,CAAE,KAAK,CAClBnC,KAAK,CAAE,IACT,CAAC,CAED,GAAI,CACF;AACAN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CAErC,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAC,KAAK,gDAAiD,CAC3E8C,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBtC,SAAS,CACTW,QAAQ,CACRC,QAAQ,CACRC,KAAK,CACLC,KAAK,CACLyB,SAAS,CAAE,GAAI,CAAAX,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CACpC,CAAC,CACH,CAAC,CAAC,CAEF,GAAI1C,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAgB,MAAM,CAAG,KAAM,CAAAlB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACpCC,OAAO,CAACC,GAAG,CAAC,SAAS,CAAEa,MAAM,CAAC,CAC9ByB,UAAU,CAACE,WAAW,CAAG,IAAI,CAC7BF,UAAU,CAACG,SAAS,CAAG,IAAI,CAC3B,MAAO,CAAAH,UAAU,CACnB,CAAC,IAAM,CACLvC,OAAO,CAACK,IAAI,kEAAAK,MAAA,CAAgBd,QAAQ,CAACsB,MAAM,CAAE,CAAC,CAC9CqB,UAAU,CAACjC,KAAK,0CAAAI,MAAA,CAAcd,QAAQ,CAACsB,MAAM,CAAE,CACjD,CACF,CAAE,MAAO+B,YAAY,CAAE,CACrBjD,OAAO,CAACK,IAAI,CAAC,WAAW,CAAE4C,YAAY,CAACC,OAAO,CAAC,CAC/CX,UAAU,CAACjC,KAAK,uCAAAI,MAAA,CAAgBuC,YAAY,CAACC,OAAO,CAAE,CACxD,CAEA;AACA,GAAI,CACFlD,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC,CAExB;AACA,KAAM,CAAAkD,YAAY,CAAG,CACnB1C,SAAS,CACTW,QAAQ,CACRC,QAAQ,CACRC,KAAK,CACLC,KAAK,CACLyB,SAAS,CAAE,GAAI,CAAAX,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CACnCc,MAAM,CAAE,KACV,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGP,IAAI,CAACQ,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAI,IAAI,CAAC,CACzFH,cAAc,CAACI,IAAI,CAACN,YAAY,CAAC,CAEjC;AACAI,YAAY,CAACG,OAAO,CAAC,wBAAwB,CAAEZ,IAAI,CAACC,SAAS,CAACM,cAAc,CAAC,CAAC,CAE9ErD,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC,CAChCsC,UAAU,CAACG,SAAS,CAAG,IAAI,CAE7B,CAAE,MAAOiB,UAAU,CAAE,CACnB3D,OAAO,CAACM,KAAK,CAAC,SAAS,CAAEqD,UAAU,CAAC,CACpCpB,UAAU,CAACjC,KAAK,0CAAAI,MAAA,CAAciD,UAAU,CAACT,OAAO,CAAE,CAClD,KAAM,CAAAS,UAAU,CAClB,CAEA,MAAO,CAAApB,UAAU,CACnB,CAEA;AACA,KAAM,CAAAqB,+BAA+BA,CAAA,CAAG,CACtC,GAAI,CACF,KAAM,CAAAP,cAAc,CAAGP,IAAI,CAACQ,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAI,IAAI,CAAC,CAEzF,GAAIH,cAAc,CAACjD,MAAM,GAAK,CAAC,CAAE,CAC/BJ,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,CACvB,MAAO,CAAE4D,OAAO,CAAE,IAAI,CAAEC,WAAW,CAAE,CAAE,CAAC,CAC1C,CAEA9D,OAAO,CAACC,GAAG,yCAAAS,MAAA,CAAW2C,cAAc,CAACjD,MAAM,4CAAY,CAAC,CAExD,GAAI,CAAA0D,WAAW,CAAG,CAAC,CACnB,KAAM,CAAAC,gBAAgB,CAAG,EAAE,CAE3B,IAAK,KAAM,CAAAC,MAAM,GAAI,CAAAX,cAAc,CAAE,CACnC,GAAI,CACF,KAAM,CAAAd,UAAU,CAAG,KAAM,KAAI,CAACC,mBAAmB,CAC/CwB,MAAM,CAACvD,SAAS,CAChBuD,MAAM,CAAC5C,QAAQ,CACf4C,MAAM,CAAC3C,QAAQ,CACf2C,MAAM,CAAC1C,KAAK,CACZ0C,MAAM,CAACzC,KACT,CAAC,CAED,GAAIgB,UAAU,CAACE,WAAW,CAAE,CAC1BqB,WAAW,EAAE,CACb9D,OAAO,CAACC,GAAG,0CAAAS,MAAA,CAAYsD,MAAM,CAACvD,SAAS,MAAAC,MAAA,CAAIsD,MAAM,CAAC5C,QAAQ,MAAAV,MAAA,CAAIsD,MAAM,CAAC3C,QAAQ,OAAAX,MAAA,CAAKsD,MAAM,CAAC1C,KAAK,CAAE,CAAC,CACnG,CAAC,IAAM,CACLyC,gBAAgB,CAACN,IAAI,CAACO,MAAM,CAAC,CAC/B,CACF,CAAE,MAAO1D,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,0CAAAI,MAAA,CAAYsD,MAAM,CAACvD,SAAS,MAAAC,MAAA,CAAIsD,MAAM,CAAC5C,QAAQ,MAAAV,MAAA,CAAIsD,MAAM,CAAC3C,QAAQ,OAAAX,MAAA,CAAKsD,MAAM,CAAC1C,KAAK,EAAIhB,KAAK,CAAC,CAC1GyD,gBAAgB,CAACN,IAAI,CAACO,MAAM,CAAC,CAC/B,CACF,CAEA;AACAT,YAAY,CAACG,OAAO,CAAC,wBAAwB,CAAEZ,IAAI,CAACC,SAAS,CAACgB,gBAAgB,CAAC,CAAC,CAEhF/D,OAAO,CAACC,GAAG,uDAAAS,MAAA,CAAeoD,WAAW,+BAAApD,MAAA,CAASqD,gBAAgB,CAAC3D,MAAM,WAAI,CAAC,CAE1E,MAAO,CACLyD,OAAO,CAAE,IAAI,CACbC,WAAW,CACXG,cAAc,CAAEF,gBAAgB,CAAC3D,MACnC,CAAC,CAEH,CAAE,MAAOE,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA4D,8BAA8BA,CAAA,CAAG,CAC/B,GAAI,CACF,KAAM,CAAAb,cAAc,CAAGP,IAAI,CAACQ,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAI,IAAI,CAAC,CACzF,MAAO,CAAAH,cAAc,CAACjD,MAAM,CAC9B,CAAE,MAAOE,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACpC,MAAO,EAAC,CACV,CACF,CAEA;AACA6D,2BAA2BA,CAAA,CAAG,CAC5B,GAAI,CACFZ,YAAY,CAACa,UAAU,CAAC,wBAAwB,CAAC,CACjDpE,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC,CACzB,MAAO,KAAI,CACb,CAAE,MAAOK,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,MAAO,MAAK,CACd,CACF,CAEA;AACA,KAAM,CAAA+D,kBAAkBA,CAAC5D,SAAS,CAAEW,QAAQ,CAAEkD,YAAY,CAAE,CAC1D,GAAI,KAAAC,sBAAA,CACF;AACA,KAAM,CAAAxD,IAAI,CAAI,IAAI,CAACxB,gBAAgB,EAAI,IAAI,CAACA,gBAAgB,CAACkB,SAAS,CAAC,EAAI,IAAI,CAAClB,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,IAAAmD,sBAAA,CAChH,IAAI,CAACjF,iBAAiB,CAACmB,SAAS,CAAC,UAAA8D,sBAAA,iBAAjCA,sBAAA,CAAoCnD,QAAQ,CAAC,GAC7C,EAAE,CACP,KAAM,CAAAoD,YAAY,CAAGF,YAAY,CAACE,YAAY,EAAIzD,IAAI,CAEtD,GAAIuD,YAAY,CAACG,MAAM,GAAK,OAAO,CAAE,CACnC,KAAM,KAAI,CAACC,qBAAqB,CAACF,YAAY,CAAE/D,SAAS,CAAEW,QAAQ,CAAC,CACrE,CAAC,IAAM,IAAIkD,YAAY,CAACG,MAAM,GAAK,KAAK,CAAE,CACxC,KAAM,KAAI,CAACE,mBAAmB,CAACH,YAAY,CAAE/D,SAAS,CAAEW,QAAQ,CAAC,CACnE,CACF,CAAE,MAAOd,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAoE,qBAAqBA,CAAC3D,IAAI,CAAEN,SAAS,CAAEW,QAAQ,CAAE,CACrD,KAAM,CAAAwD,QAAQ,CAAG1F,IAAI,CAAC2F,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,KAAM,CAAAC,UAAU,CAAGhE,IAAI,CAACiE,GAAG,CAACC,GAAG,EAAI,CACjC,KAAM,CAAAC,SAAS,CAAG,CAChB,IAAI,CAAED,GAAG,CAACE,EAAE,CACZ,IAAI,CAAEF,GAAG,CAACG,EAAE,CACZ,KAAK,CAAEH,GAAG,CAACI,GAAG,CACd,IAAI,CAAEJ,GAAG,CAACK,EAAE,CACZ,MAAM,CAAEL,GAAG,CAACM,IAAI,CAChB,MAAM,CAAEN,GAAG,CAACO,IACd,CAAC,CAED;AACA,IAAK,GAAI,CAAAC,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,EAAE,CAAEA,KAAK,EAAE,CAAE,CACxC,KAAM,CAAAC,QAAQ,IAAAhF,MAAA,CAAM+E,KAAK,kCAAO,CAChC,KAAM,CAAAE,aAAa,IAAAjF,MAAA,CAAM+E,KAAK,kCAAO,CACrC,KAAM,CAAAG,QAAQ,IAAAlF,MAAA,CAAM+E,KAAK,sBAAK,CAE9B,GAAIR,GAAG,CAACS,QAAQ,CAAC,CAAE,KAAAG,kBAAA,CAAAC,aAAA,CACjBZ,SAAS,CAACQ,QAAQ,CAAC,CAAGT,GAAG,CAACS,QAAQ,CAAC,CAACnE,KAAK,EAAI,EAAE,CAC/C2D,SAAS,CAACS,aAAa,CAAC,CAAG,EAAAE,kBAAA,CAAAZ,GAAG,CAACU,aAAa,CAAC,UAAAE,kBAAA,iBAAlBA,kBAAA,CAAoBtE,KAAK,GAAI,EAAE,CAC1D2D,SAAS,CAACU,QAAQ,CAAC,CAAG,EAAAE,aAAA,CAAAb,GAAG,CAACW,QAAQ,CAAC,UAAAE,aAAA,iBAAbA,aAAA,CAAevE,KAAK,GAAI,EAAE,CAClD,CACF,CAEA,MAAO,CAAA2D,SAAS,CAClB,CAAC,CAAC,CAEF,KAAM,CAAAa,SAAS,CAAG7G,IAAI,CAAC2F,KAAK,CAACmB,aAAa,CAACjB,UAAU,CAAC,CACtD7F,IAAI,CAAC2F,KAAK,CAACoB,iBAAiB,CAACrB,QAAQ,CAAEmB,SAAS,IAAArF,MAAA,CAAKD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,CAAE,CAAC,CAE7E;AACA,KAAM,CAAA8E,QAAQ,IAAAxF,MAAA,CAAMD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,MAAAV,MAAA,CAAI,GAAI,CAAA2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAO,CAE1F;AACAjH,IAAI,CAACkH,SAAS,CAACxB,QAAQ,CAAEsB,QAAQ,CAAC,CACpC,CAEA;AACA,KAAM,CAAAvB,mBAAmBA,CAAC5D,IAAI,CAAEN,SAAS,CAAEW,QAAQ,CAAE,CACnD;AACA,KAAM,CAAAwB,OAAO,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,IAAI,CAAE,MAAM,CAAE,MAAM,CAAC,CAEzD;AACA,IAAK,GAAI,CAAA6C,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,EAAE,CAAEA,KAAK,EAAE,CAAE,CACxC7C,OAAO,CAACa,IAAI,IAAA/C,MAAA,CAAI+E,KAAK,sCAAA/E,MAAA,CAAY+E,KAAK,sCAAA/E,MAAA,CAAY+E,KAAK,sBAAK,CAAC,CAC/D,CAEA,KAAM,CAAAY,OAAO,CAAG,CAACzD,OAAO,CAAC0D,IAAI,CAAC,GAAG,CAAC,CAAC,CAEnCvF,IAAI,CAACwF,OAAO,CAACtB,GAAG,EAAI,CAClB,KAAM,CAAAuB,OAAO,CAAG,CACdvB,GAAG,CAACE,EAAE,MAAAzE,MAAA,CACFuE,GAAG,CAACG,EAAE,YAAA1E,MAAA,CACNuE,GAAG,CAACI,GAAG,OACXJ,GAAG,CAACK,EAAE,MAAA5E,MAAA,CACFuE,GAAG,CAACM,IAAI,YAAA7E,MAAA,CACRuE,GAAG,CAACO,IAAI,OACb,CAED;AACA,IAAK,GAAI,CAAAC,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,EAAE,CAAEA,KAAK,EAAE,CAAE,KAAAgB,aAAA,CAAAC,mBAAA,CAAAC,cAAA,CACxC,KAAM,CAAAjB,QAAQ,IAAAhF,MAAA,CAAM+E,KAAK,kCAAO,CAChC,KAAM,CAAAE,aAAa,IAAAjF,MAAA,CAAM+E,KAAK,kCAAO,CACrC,KAAM,CAAAG,QAAQ,IAAAlF,MAAA,CAAM+E,KAAK,sBAAK,CAE9Be,OAAO,CAAC/C,IAAI,MAAA/C,MAAA,CACN,EAAA+F,aAAA,CAAAxB,GAAG,CAACS,QAAQ,CAAC,UAAAe,aAAA,iBAAbA,aAAA,CAAelF,KAAK,GAAI,EAAE,YAAAb,MAAA,CAC1B,EAAAgG,mBAAA,CAAAzB,GAAG,CAACU,aAAa,CAAC,UAAAe,mBAAA,iBAAlBA,mBAAA,CAAoBnF,KAAK,GAAI,EAAE,YAAAb,MAAA,CAC/B,EAAAiG,cAAA,CAAA1B,GAAG,CAACW,QAAQ,CAAC,UAAAe,cAAA,iBAAbA,cAAA,CAAepF,KAAK,GAAI,EAAE,MAChC,CAAC,CACH,CAEA8E,OAAO,CAAC5C,IAAI,CAAC+C,OAAO,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC,CACjC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAJ,QAAQ,IAAAxF,MAAA,CAAMD,SAAS,MAAAC,MAAA,CAAIU,QAAQ,MAAAV,MAAA,CAAI,GAAI,CAAA2B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC6D,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAM,CAEzF;AACA,KAAM,CAAAS,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACR,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAE,CAAEQ,IAAI,CAAE,yBAA0B,CAAC,CAAC,CAChF,KAAM,CAAAC,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC,CACrCG,IAAI,CAACM,QAAQ,CAAGnB,QAAQ,CACxBa,IAAI,CAACO,KAAK,CAAC,CAAC,CACd,CAEA;AACA,KAAM,CAAAC,2BAA2BA,CAAA,CAAG,CAClC,GAAI,CACF;AACA;AACA;AAEA,KAAM,CAAA3H,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAC3E,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAgB,MAAM,CAAG,KAAM,CAAAlB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACpC,IAAI,CAACV,kBAAkB,CAAGyB,MAAM,CAACC,IAAI,CACrC,MAAO,CAAAD,MAAM,CAACC,IAAI,CACpB,CACF,CAAE,MAAOT,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAM,yBAAyBA,CAACH,SAAS,CAAE,CACzC,GAAI,CACFT,OAAO,CAACC,GAAG,6BAAAS,MAAA,CAASD,SAAS,wDAAc,CAAC,CAE5C;AACA,KAAM,CAAA+G,UAAU,CAAG,KAAM,KAAI,CAACC,qBAAqB,CAAC,CAAC,CAErD,GAAI,CAACD,UAAU,EAAIA,UAAU,CAACpH,MAAM,GAAK,CAAC,CAAE,CAC1CJ,OAAO,CAACK,IAAI,CAAC,cAAc,CAAC,CAC5B,OACF,CAEA,GAAI,CAAC,IAAI,CAACd,gBAAgB,CAACkB,SAAS,CAAC,CAAE,CACrCT,OAAO,CAACK,IAAI,uBAAAK,MAAA,CAAQD,SAAS,uBAAM,CAAC,CACpC,OACF,CAEA,GAAI,CAAAiH,YAAY,CAAG,CAAC,CACpB,GAAI,CAAAC,cAAc,CAAG,CAAC,CAEtB;AACA,CAAC,eAAe,CAAE,SAAS,CAAC,CAACpB,OAAO,CAACnF,QAAQ,EAAI,CAC/C,GAAI,CAAC,IAAI,CAAC7B,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAAE,CAC/CpB,OAAO,CAACC,GAAG,6BAAAS,MAAA,CAASU,QAAQ,qCAAAV,MAAA,CAAUD,SAAS,WAAI,CAAC,CACpD,OACF,CAEAT,OAAO,CAACC,GAAG,iBAAAS,MAAA,CAAOU,QAAQ,+BAAAV,MAAA,CAAS,IAAI,CAACnB,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAAChB,MAAM,uBAAM,CAAC,CAE3F,IAAI,CAACb,gBAAgB,CAACkB,SAAS,CAAC,CAACW,QAAQ,CAAC,CAACmF,OAAO,CAAC,CAACqB,SAAS,CAAEvG,QAAQ,GAAK,CAC1EsG,cAAc,EAAE,CAEhB;AACA,GAAI,CAACC,SAAS,CAACxC,EAAE,CAAE,CACjBpF,OAAO,CAACK,IAAI,WAAAK,MAAA,CAAMW,QAAQ,+CAAU,CAAC,CACrC,OACF,CAEA;AACA,KAAM,CAAAwG,iBAAiB,CAAGL,UAAU,CAACM,IAAI,CAACC,SAAS,EAAI,CACrD,MAAO,CAAAA,SAAS,CAACC,OAAO,EACjB,IAAI,CAACC,wBAAwB,CAACL,SAAS,CAACxC,EAAE,CAAE2C,SAAS,CAACC,OAAO,CAAC,CACvE,CAAC,CAAC,CAEF,GAAIH,iBAAiB,CAAE,CACrBH,YAAY,EAAE,CACd1H,OAAO,CAACC,GAAG,qCAAAS,MAAA,CAAYW,QAAQ,QAAAX,MAAA,CAAMkH,SAAS,CAACxC,EAAE,UAAA1E,MAAA,CAAQmH,iBAAiB,CAACG,OAAO,CAAE,CAAC,CAErF;AACA,IAAK,GAAI,CAAAvC,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,EAAE,CAAEA,KAAK,EAAE,CAAE,KAAAyC,qBAAA,CACxC,KAAM,CAAAC,SAAS,IAAAzH,MAAA,CAAM+E,KAAK,kCAAO,CACjC,KAAM,CAAA2C,aAAa,IAAA1H,MAAA,CAAM+E,KAAK,kCAAO,CACrC,KAAM,CAAA4C,UAAU,IAAA3H,MAAA,CAAM+E,KAAK,sBAAK,CAEhC;AACA,IAAI,CAAC6C,qBAAqB,CAACV,SAAS,CAAEO,SAAS,CAAEN,iBAAiB,CAACM,SAAS,CAAC,CAAE,IAAI,CAAC,CAEpF;AACA,IAAI,CAACG,qBAAqB,CAACV,SAAS,CAAEQ,aAAa,CAAEP,iBAAiB,CAACO,aAAa,CAAC,CAAE,IAAI,CAAC,CAE5F;AACA,IAAI,CAACE,qBAAqB,CAACV,SAAS,CAAES,UAAU,EAAAH,qBAAA,CAAEN,SAAS,CAACS,UAAU,CAAC,UAAAH,qBAAA,iBAArBA,qBAAA,CAAuB3G,KAAK,CAAE,KAAK,CAAC,CACxF,CAEA;AACAqG,SAAS,CAACW,UAAU,CAAG,IAAI,CAC3BX,SAAS,CAACY,cAAc,CAAGX,iBAAiB,CAACG,OAAO,CAEtD,CAAC,IAAM,CACLhI,OAAO,CAACC,GAAG,2CAAAS,MAAA,CAAaW,QAAQ,QAAAX,MAAA,CAAMkH,SAAS,CAACxC,EAAE,CAAE,CAAC,CAErD;AACA,IAAK,GAAI,CAAAK,KAAK,CAAG,CAAC,CAAEA,KAAK,EAAI,EAAE,CAAEA,KAAK,EAAE,CAAE,KAAAgD,oBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CACxC,KAAM,CAAAR,SAAS,IAAAzH,MAAA,CAAM+E,KAAK,kCAAO,CACjC,KAAM,CAAA2C,aAAa,IAAA1H,MAAA,CAAM+E,KAAK,kCAAO,CACrC,KAAM,CAAA4C,UAAU,IAAA3H,MAAA,CAAM+E,KAAK,sBAAK,CAEhC;AACA,IAAI,CAAC6C,qBAAqB,CAACV,SAAS,CAAEO,SAAS,EAAAM,oBAAA,CAAEb,SAAS,CAACO,SAAS,CAAC,UAAAM,oBAAA,iBAApBA,oBAAA,CAAsBlH,KAAK,CAAE,KAAK,CAAC,CACpF,IAAI,CAAC+G,qBAAqB,CAACV,SAAS,CAAEQ,aAAa,EAAAM,qBAAA,CAAEd,SAAS,CAACQ,aAAa,CAAC,UAAAM,qBAAA,iBAAxBA,qBAAA,CAA0BnH,KAAK,CAAE,KAAK,CAAC,CAC5F,IAAI,CAAC+G,qBAAqB,CAACV,SAAS,CAAES,UAAU,EAAAM,sBAAA,CAAEf,SAAS,CAACS,UAAU,CAAC,UAAAM,sBAAA,iBAArBA,sBAAA,CAAuBpH,KAAK,CAAE,KAAK,CAAC,CACxF,CAEA;AACAqG,SAAS,CAACW,UAAU,CAAG,KAAK,CAC9B,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEFvI,OAAO,CAACC,GAAG,0CAAAS,MAAA,CAAYD,SAAS,iCAAAC,MAAA,CAAWiH,cAAc,2CAAAjH,MAAA,CAAWgH,YAAY,cAAAhH,MAAA,CAAO,CAAEgH,YAAY,CAACC,cAAc,CAAE,GAAG,EAAEiB,OAAO,CAAC,CAAC,CAAC,MAAI,CAAC,CAE5I,CAAE,MAAOtI,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,WAAW,CAAEA,KAAK,CAAC,CACjC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAgI,qBAAqBA,CAACV,SAAS,CAAEiB,SAAS,CAAEC,WAAW,CAAE5G,UAAU,CAAE,CACnE;AACA,GAAI,CAAC0F,SAAS,CAACiB,SAAS,CAAC,EAAI,MAAO,CAAAjB,SAAS,CAACiB,SAAS,CAAC,GAAK,QAAQ,CAAE,CACrEjB,SAAS,CAACiB,SAAS,CAAC,CAAG,CACrBtH,KAAK,CAAE,EAAE,CACTW,UAAU,CAAE,KACd,CAAC,CACH,CAEA;AACA,GAAI4G,WAAW,GAAKC,SAAS,EAAID,WAAW,GAAK,IAAI,CAAE,CACrDlB,SAAS,CAACiB,SAAS,CAAC,CAACtH,KAAK,CAAGuH,WAAW,CAC1C,CACAlB,SAAS,CAACiB,SAAS,CAAC,CAAC3G,UAAU,CAAGA,UAAU,CAE5C;AACA0F,SAAS,CAACiB,SAAS,CAAC,CAAC1G,WAAW,CAAGD,UAAU,CAAG,QAAQ,CAAG,QAAQ,CACrE,CAEA;AACA+F,wBAAwBA,CAACe,eAAe,CAAEC,eAAe,CAAE,CACzD,GAAI,CAACD,eAAe,EAAI,CAACC,eAAe,CAAE,MAAO,MAAK,CAEtD;AACA,KAAM,CAAAC,aAAa,CAAIC,IAAI,EAAK,CAC9B,MAAO,CAAAA,IAAI,CACRC,OAAO,CAAC,aAAa,CAAE,EAAE,CAAE;AAAA,CAC3BA,OAAO,CAAC,MAAM,CAAE,EAAE,CAAE;AAAA,CACpBA,OAAO,CAAC,SAAS,CAAE,EAAE,CAAE;AAAA,CACvBA,OAAO,CAAC,QAAQ,CAAE,EAAE,CAAE;AAAA,CACtBC,WAAW,CAAC,CAAC,CAAE;AACpB,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGJ,aAAa,CAACF,eAAe,CAAC,CAClD,KAAM,CAAAO,WAAW,CAAGL,aAAa,CAACD,eAAe,CAAC,CAElD;AACA,GAAIK,WAAW,GAAKC,WAAW,CAAE,CAC/BvJ,OAAO,CAACC,GAAG,8BAAAS,MAAA,CAAUsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,CAAE,CAAC,CAC9D,MAAO,KAAI,CACb,CAEA;AACA,GAAIK,WAAW,CAACE,QAAQ,CAACD,WAAW,CAAC,EAAIA,WAAW,CAACC,QAAQ,CAACF,WAAW,CAAC,CAAE,CAC1EtJ,OAAO,CAACC,GAAG,8BAAAS,MAAA,CAAUsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,CAAE,CAAC,CAC9D,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAQ,kBAAkB,CAAG,CACzB,OAAO,CAAE,IAAI,CAAE,IAAI,CAAE,MAAM,CAAE,MAAM,CACnC,IAAI,CAAE,IAAI,CAAE,SAAS,CAAE,IAAI,CAAE,KAAK,CAAE,OAAO,CAC5C,CAED,GAAI,CAAAC,iBAAiB,CAAGJ,WAAW,CACnC,GAAI,CAAAK,iBAAiB,CAAGJ,WAAW,CAEnC;AACAE,kBAAkB,CAAClD,OAAO,CAACqD,IAAI,EAAI,CACjC,KAAM,CAAAC,cAAc,CAAGX,aAAa,CAACU,IAAI,CAAC,CAC1C,GAAIF,iBAAiB,CAACI,QAAQ,CAACD,cAAc,CAAC,CAAE,CAC9CH,iBAAiB,CAAGA,iBAAiB,CAACK,KAAK,CAAC,CAAC,CAAE,CAACF,cAAc,CAACzJ,MAAM,CAAC,CACxE,CACA,GAAIuJ,iBAAiB,CAACG,QAAQ,CAACD,cAAc,CAAC,CAAE,CAC9CF,iBAAiB,CAAGA,iBAAiB,CAACI,KAAK,CAAC,CAAC,CAAE,CAACF,cAAc,CAACzJ,MAAM,CAAC,CACxE,CACF,CAAC,CAAC,CAEF;AACA,GAAIsJ,iBAAiB,EAAIC,iBAAiB,EAAID,iBAAiB,CAACtJ,MAAM,CAAG,CAAC,EAAIuJ,iBAAiB,CAACvJ,MAAM,CAAG,CAAC,CAAE,CAC1G,GAAIsJ,iBAAiB,GAAKC,iBAAiB,CAAE,CAC3C3J,OAAO,CAACC,GAAG,0CAAAS,MAAA,CAAYsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,CAAE,CAAC,CAChE,MAAO,KAAI,CACb,CACA,GAAIS,iBAAiB,CAACF,QAAQ,CAACG,iBAAiB,CAAC,EAAIA,iBAAiB,CAACH,QAAQ,CAACE,iBAAiB,CAAC,CAAE,CAClG1J,OAAO,CAACC,GAAG,sDAAAS,MAAA,CAAcsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,CAAE,CAAC,CAClE,MAAO,KAAI,CACb,CACF,CAEA;AACA,KAAM,CAAAe,eAAe,CAAIb,IAAI,EAAK,CAChC,MAAO,CAAAA,IAAI,CACRhD,KAAK,CAAC,cAAc,CAAC,CACrB8D,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9J,MAAM,EAAI,CAAC,CAAE;AAAA,CAC3B4E,GAAG,CAACkF,CAAC,EAAIhB,aAAa,CAACgB,CAAC,CAAC,CAAC,CAC1BD,MAAM,CAACC,CAAC,EAAIA,CAAC,CAAC9J,MAAM,EAAI,CAAC,CAAC,CAAE;AACjC,CAAC,CAED,KAAM,CAAA+J,cAAc,CAAGH,eAAe,CAAChB,eAAe,CAAC,CACvD,KAAM,CAAAoB,cAAc,CAAGJ,eAAe,CAACf,eAAe,CAAC,CAEvD;AACA,GAAI,CAAAoB,eAAe,CAAG,CAAC,CACvB,KAAM,CAAAC,aAAa,CAAGC,IAAI,CAACC,GAAG,CAACL,cAAc,CAAC/J,MAAM,CAAEgK,cAAc,CAAChK,MAAM,CAAC,CAE5E+J,cAAc,CAAC5D,OAAO,CAACkE,EAAE,EAAI,CAC3BL,cAAc,CAAC7D,OAAO,CAACmE,EAAE,EAAI,CAC3B,GAAID,EAAE,GAAKC,EAAE,EAAKD,EAAE,CAACrK,MAAM,CAAG,CAAC,EAAIsK,EAAE,CAACtK,MAAM,CAAG,CAAC,GAAKqK,EAAE,CAACjB,QAAQ,CAACkB,EAAE,CAAC,EAAIA,EAAE,CAAClB,QAAQ,CAACiB,EAAE,CAAC,CAAE,CAAE,CACzFJ,eAAe,EAAE,CACnB,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAM,UAAU,CAAGN,eAAe,CAAGC,aAAa,CAClD,GAAIK,UAAU,EAAI,GAAG,EAAIN,eAAe,EAAI,CAAC,CAAE,CAC7CrK,OAAO,CAACC,GAAG,oCAAAS,MAAA,CAAWsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,2BAAAvI,MAAA,CAAU,CAACiK,UAAU,CAAG,GAAG,EAAE/B,OAAO,CAAC,CAAC,CAAC,MAAI,CAAC,CACxG,MAAO,KAAI,CACb,CAEA;AACA,KAAM,CAAAgC,cAAc,CAAG,CACrB,CAAEC,MAAM,CAAE,CAAC,KAAK,CAAE,MAAM,CAAC,CAAEC,MAAM,CAAE,CAAC,KAAK,CAAE,MAAM,CAAE,CAAC,CACpD,CAAED,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAC,CAAEC,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAE,CAAC,CAClD,CAAED,MAAM,CAAE,CAAC,OAAO,CAAE,KAAK,CAAC,CAAEC,MAAM,CAAE,CAAC,OAAO,CAAE,KAAK,CAAE,CAAC,CACtD,CAAED,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAC,CAAEC,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAE,CAAC,CAClD,CAAED,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAC,CAAEC,MAAM,CAAE,CAAC,MAAM,CAAE,IAAI,CAAE,CAAC,CACnD,CAED,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAH,cAAc,CAAE,CACjC,KAAM,CAAAI,WAAW,CAAGD,IAAI,CAACF,MAAM,CAACI,IAAI,CAACC,OAAO,EAAI5B,WAAW,CAACE,QAAQ,CAACN,aAAa,CAACgC,OAAO,CAAC,CAAC,CAAC,CAC7F,KAAM,CAAAC,WAAW,CAAGJ,IAAI,CAACD,MAAM,CAACG,IAAI,CAACC,OAAO,EAAI3B,WAAW,CAACC,QAAQ,CAACN,aAAa,CAACgC,OAAO,CAAC,CAAC,CAAC,CAE7F,GAAIF,WAAW,EAAIG,WAAW,CAAE,CAC9BnL,OAAO,CAACC,GAAG,0CAAAS,MAAA,CAAYsI,eAAe,UAAAtI,MAAA,CAAQuI,eAAe,CAAE,CAAC,CAChE,MAAO,KAAI,CACb,CACF,CAEA,MAAO,MAAK,CACd,CAEA;AACA,KAAM,CAAAxB,qBAAqBA,CAAA,CAAG,CAC5B,GAAI,CACF;AACAzH,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAClC,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,6CAA6C,CAAC,CAE3E,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAgB,MAAM,CAAG,KAAM,CAAAlB,QAAQ,CAACG,IAAI,CAAC,CAAC,CACpC,GAAIe,MAAM,CAACC,IAAI,EAAIqK,KAAK,CAACC,OAAO,CAACvK,MAAM,CAACC,IAAI,CAAC,EAAID,MAAM,CAACC,IAAI,CAACX,MAAM,CAAG,CAAC,CAAE,CACvEJ,OAAO,CAACC,GAAG,yDAAAS,MAAA,CAAiBI,MAAM,CAACC,IAAI,CAACX,MAAM,uBAAM,CAAC,CACrD,MAAO,CAAAU,MAAM,CAACC,IAAI,CACpB,CAAC,IAAM,CACLf,OAAO,CAACK,IAAI,CAAC,mBAAmB,CAAC,CACnC,CACF,CAAC,IAAM,CACLL,OAAO,CAACK,IAAI,yDAAAK,MAAA,CAAiBd,QAAQ,CAACsB,MAAM,CAAE,CAAC,CACjD,CACF,CAAE,MAAOZ,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,cAAc,CAAEA,KAAK,CAAC,CACtC,CAEA;AACA,GAAI,CACFN,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAClC,KAAM,CAAAqL,gBAAgB,CAAG,KAAM,OAAM,CAAC,yBAAyB,CAAC,CAChE,GAAIA,gBAAgB,CAACC,OAAO,EAAID,gBAAgB,CAACC,OAAO,CAACxK,IAAI,CAAE,CAC7Df,OAAO,CAACC,GAAG,8EAAAS,MAAA,CAAkB4K,gBAAgB,CAACC,OAAO,CAACxK,IAAI,CAACX,MAAM,uBAAM,CAAC,CACxE,MAAO,CAAAkL,gBAAgB,CAACC,OAAO,CAACxK,IAAI,CACtC,CACF,CAAE,MAAOT,KAAK,CAAE,CACdN,OAAO,CAACM,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACvC,CAEA;AACAN,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC,CAC5B,MAAO,CACL,CACE,SAAS,CAAE,UAAU,CACrB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,WACZ,CAAC,CACD,CACE,SAAS,CAAE,SAAS,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,8EAA8E,CACxF,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,6FAA6F,CACvG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,6FAA6F,CACvG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,yFAAyF,CACnG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,2FAA2F,CACrG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,2FAA2F,CACrG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,2FAA2F,CACrG,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,2FAA2F,CACrG,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,2FAA2F,CACtG,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,2FAA2F,CACtG,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,2FACb,CAAC,CACD,CACE,SAAS,CAAE,QAAQ,CACnB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,4CAA4C,CACtD,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,4CAA4C,CACvD,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,4CAA4C,CACvD,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,4CACb,CAAC,CACD,CACE,SAAS,CAAE,SAAS,CACpB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,kBAAkB,CAC5B,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,QACb,CAAC,CACD,CACE,SAAS,CAAE,YAAY,CACvB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,oBAAoB,CAC9B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,WAAW,CACrB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,YAAY,CACvB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,WACb,CAAC,CACD,CACE,SAAS,CAAE,QAAQ,CACnB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,gBAAgB,CAC1B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,kBAAkB,CAC5B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,aAAa,CACxB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,MAAM,CACjB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,OAAO,CAClB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,gBAAgB,CAC1B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,gBAAgB,CAC1B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,aAAa,CACxB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,MAAM,CACjB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,gBAAgB,CAC1B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,MAAM,CACjB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,MAAM,CACjB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,QAAQ,CACnB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,KAAK,CACf,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,KAAK,CAChB,SAAS,CAAE,UACb,CAAC,CACD,CACE,SAAS,CAAE,YAAY,CACvB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,gBAAgB,CAC1B,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,UACb,CACA;AAAA,CACD,CACH,CACF,CAEA,cAAe,IAAI,CAAAd,gBAAgB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}