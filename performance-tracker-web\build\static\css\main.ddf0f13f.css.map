{"version": 3, "file": "static/css/main.ddf0f13f.css", "mappings": "sHACA,EAGE,qBAAsB,CADtB,SAEF,CAEA,OALE,QAeF,CAVA,KAIE,kCAAmC,CACnC,iCAAkC,CAClC,gFAAqF,CACrF,UAAc,CALd,4JACgF,CAKhF,gBAAiB,CACjB,iBACF,CAEA,KACE,0CACF,CAGA,oBACE,SACF,CAEA,0BACE,oBACF,CAEA,0BACE,iDAAoD,CACpD,iBACF,CAEA,gCACE,iDACF,CAaA,gBACE,MAAW,6BAA6C,CACxD,IAAM,6BAA6C,CACrD,CCtDA,KAEE,gBAAiB,CACjB,iBAAkB,CAFlB,iBAGF,CAGA,gBAQE,kBAAmB,CAFnB,gFAAqF,CACrF,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YACF,CAEA,iBAEE,4BAA6B,CAD7B,iBAEF,CAEA,yBAIE,aAAc,CAHd,8BAAkC,CAClC,cAAe,CACf,eAAgB,CAOhB,kBAAmB,CADnB,mBAAqB,CAJrB,oEAMF,CAEA,6BAGE,aAAc,CAFd,+BAAmC,CACnC,gBAAiB,CAEjB,kBAAmB,CACnB,kBAAmB,CACnB,UACF,CAEA,kBAGE,oBAAqC,CACrC,iBAAkB,CAFlB,UAAW,CAIX,kBAAmB,CADnB,eAAgB,CAEhB,iBAAkB,CANlB,WAOF,CAEA,cAIE,6CAA8C,CAF9C,yDAA6D,CAC7D,yBAA0B,CAE1B,iBAAkB,CAJlB,WAKF,CAEA,uBACE,GAEE,0BAA2B,CAD3B,OAEF,CACA,IAEE,0BAA2B,CAD3B,SAEF,CACA,GAEE,uBAAyB,CADzB,UAEF,CACF,CAEA,gBAKE,uCAAwC,CAFxC,UAAc,CAFd,+BAAmC,CACnC,gBAAiB,CAEjB,UAEF,CAGA,yBACE,yBACE,cAAe,CACf,kBACF,CAEA,6BACE,cAAe,CACf,kBACF,CAEA,kBACE,WACF,CACF,CAEA,yBACE,yBACE,gBAAiB,CACjB,kBACF,CAEA,6BACE,eAAiB,CACjB,kBACF,CAEA,kBACE,WACF,CACF,CCvHA,UAIE,YAAa,CACb,qBAAsB,CAJtB,gBAAiB,CAEjB,eAAgB,CADhB,iBAIF,CAGA,sBAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,UAME,mCAAoC,CAFpC,kBAAmB,CACnB,iBAAkB,CAElB,6BAA2C,CAJ3C,UAAW,CAFX,iBAAkB,CAClB,SAMF,CAEA,sBAGE,kBAAmB,CACnB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,uBAGE,mBAAoB,CACpB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,uBAGE,oBAAqB,CACrB,sBAAuB,CAFvB,QAAS,CADT,OAIF,CAEA,iBACE,GAAiD,SAAU,CAAtD,qCAAwD,CAC7D,IAAM,SAAY,CAClB,IAAM,SAAY,CAClB,GAAuD,SAAU,CAA1D,6CAA4D,CACrE,CAEA,YAUE,sCAAuC,CAJvC,oGAEsE,CACtE,yBAA0B,CAJ1B,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAOF,CAQA,cACE,iBAAkB,CAElB,UAAW,CAEX,gBAAiB,CAHjB,QAAS,CAET,UAEF,CAEA,cAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,eAAgB,CAChB,8BACF,CAEA,eAGE,aAAc,CACd,cAAe,CACf,UACF,CAGA,kCARE,+BAAmC,CACnC,eAoBF,CAbA,mBACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAQlB,0BAAwC,CAVxC,aAAc,CAOd,cAAe,CAFf,eAAgB,CAIhB,eAAgB,CANhB,gBAAiB,CAKjB,uBAGF,CAEA,yBAEE,+BAA6C,CAD7C,0BAEF,CAEA,4BACE,2CAA8C,CAC9C,UAAW,CACX,kBAAmB,CACnB,UACF,CAEA,kCAEE,0BAAwC,CADxC,cAEF,CAGA,aAEE,sBAAuB,CAEvB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,iBAEE,aAAc,CADd,gBAEF,CAEA,sBAUE,4BAA6B,CAN7B,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAMhB,kBAAmB,CADnB,kBAAmB,CAHnB,iDAMF,CAEA,WAOE,qCAAuC,CAHvC,aAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAGhB,kBAAmB,CADnB,kBAGF,CAEA,eAKE,qCAAuC,CAFvC,qDAAqE,CADrE,UAAW,CAEX,gBAAiB,CAHjB,WAKF,CAEA,oBAKE,qCAAuC,CAFvC,WAA+B,CAF/B,+BAAmC,CACnC,gBAAiB,CAEjB,kBAEF,CAGA,iBAGE,aAAS,CAFT,YAAa,CAQb,QAAO,CANP,QAAS,CADT,wDAA2D,CAI3D,aAAc,CADd,gBAAiB,CADjB,mBAAoB,CAIpB,iBAAkB,CADlB,SAGF,CAEA,UAUE,gDAAkD,CADlD,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAEnB,cAAe,CAGf,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CADlB,uBAKF,CAEA,gBAEE,yBAA0B,CAC1B,mDAEiC,CAJjC,2BAKF,CAEA,2BACE,SACF,CAEA,WAME,mEAA4E,CAD5E,QAAS,CAFT,MAAO,CAOP,qBAAsB,CAHtB,SAAU,CAEV,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,2BAGF,CASA,WAEE,sBAAuB,CADvB,gBAAiB,CAEjB,0BACF,CAEA,2BACE,mBACF,CAEA,aAIE,kBAAmB,CAHnB,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,UACF,CAEA,cAEE,kBAAmB,CADnB,eAEF,CAEA,YAIE,UAAc,CAFd,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CADhB,iBAEF,CAEA,2BARE,+BAgBF,CARA,eAGE,kBAAmB,CADnB,cAAe,CAIf,kBAAmB,CAFnB,kBAAmB,CAGnB,UAAY,CAFZ,wBAGF,CAEA,kBAGE,eAA+B,CAF/B,+BAAmC,CACnC,gBAAkB,CAElB,eACF,CAEA,aAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,SAAU,CACV,0BAA2B,CAC3B,uBACF,CAEA,6BACE,SAAU,CACV,uBACF,CAEA,YAGE,kBAAmB,CAFnB,+BAAmC,CACnC,cAAe,CAEf,eACF,CAEA,OAGE,4CAA6C,CAD7C,kBAAmB,CADnB,gBAGF,CAEA,sBACE,MAAW,uBAA0B,CACrC,IAAM,yBAA4B,CACpC,CAGA,YAKE,oBAA8B,CAC9B,8BAA4C,CAL5C,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,YAAa,CAIb,iBAAkB,CADlB,SAEF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,cAGE,WAA+B,CAF/B,+BAAmC,CACnC,eAEF,CAEA,cAGE,aAAc,CAFd,8BAAkC,CAClC,eAAiB,CAEjB,eACF,CAEA,qBACE,uCACF,CAGA,0BACE,iBAEE,QAAS,CADT,wDAA2D,CAE3D,mBACF,CACF,CAEA,yBACE,YACE,gBAAiB,CACjB,kBACF,CAEA,WACE,gBACF,CAEA,iBAEE,QAAS,CADT,yBAA0B,CAE1B,mBACF,CAEA,UACE,YACF,CAEA,YACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,cAGE,YAAa,CAFb,eAAgB,CAChB,iBAEF,CACF,CAEA,yBACE,YACE,cAAe,CACf,kBACF,CAEA,WACE,gBACF,CAEA,aACE,sBACF,CAEA,iBACE,mBACF,CAEA,UACE,YACF,CACF,CCraA,uBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,qBAUE,mCAAqC,CATrC,kDAA6D,CAC7D,0BAA0C,CAC1C,kBAAmB,CAMnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAJhB,SAAU,CACV,SAMF,CAEA,wBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,sBAEE,kBAAmB,CAInB,oBAAqC,CADrC,iCAAiD,CAJjD,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAEA,yBAEE,UAAc,CACd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAJhB,QAKF,CAEA,WAWE,kBAAmB,CARnB,UAAc,CAOd,YAAa,CANb,cAAe,CAKf,WAAY,CAGZ,sBAAuB,CANvB,WAAY,CAEZ,UAMF,CAEA,iBACE,oBAEF,CAEA,uBAEE,eAAgB,CAChB,eAAgB,CAFhB,YAGF,CAEA,mBACE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,qBAEE,aAAc,CACd,gBAAkB,CAFlB,QAGF,CAEA,kBACE,kBACF,CAEA,aAOE,oBAAqC,CANrC,2BAA2C,CAC3C,kBAAmB,CAGnB,cAAe,CAFf,iBAAkB,CAClB,iBAAkB,CAElB,uBAEF,CAEA,mBAEE,oBAAmC,CADnC,oBAAqB,CAErB,0BACF,CAEA,aACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAEA,eAEE,UAAc,CACd,+BAAmC,CAFnC,YAGF,CAEA,aAEE,qBAA0C,CAD1C,gBAAkB,CAElB,yBACF,CAEA,oBACE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,sBAEE,aAAc,CACd,eAAiB,CAFjB,YAGF,CAEA,eAEE,iBAAkB,CAElB,eAAiB,CACjB,eAAgB,CAFhB,kBAAmB,CAFnB,iBAKF,CAEA,uBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,qBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,oBACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,uBAKE,oBAAqC,CADrC,8BAA8C,CAH9C,YAAa,CACb,QAAS,CACT,iBAGF,CAEA,wBAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAPf,QAAO,CAIP,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,YACE,oBAAoC,CAEpC,sBAA0C,CAD1C,UAEF,CAEA,iCACE,gBAAoC,CACpC,0BACF,CAEA,YACE,iDAAoD,CACpD,aACF,CAEA,iCAEE,+BAA6C,CAD7C,0BAEF,CAEA,0CAEE,kBAAmB,CADnB,UAAY,CAEZ,wBACF,CAGA,0CACE,SACF,CAEA,gDACE,oBAAoC,CACpC,iBACF,CAEA,gDACE,oBAAkC,CAClC,iBACF,CAEA,sDACE,oBACF,CAGA,yBACE,qBAEE,WAAY,CADZ,SAEF,CAEA,sBACE,iBACF,CAEA,uBACE,YACF,CAEA,uBAEE,qBAAsB,CADtB,iBAEF,CAEA,aACE,iBACF,CAEA,aACE,gBACF,CACF,CC5QA,aAEE,gFAAqF,CACrF,UAAc,CACd,+BAAmC,CAHnC,gBAIF,CAGA,aASE,kCAA2B,CAA3B,0BAA2B,CAF3B,gBAA8B,CAC9B,iCAA+C,CAF/C,iBAIF,CAEA,aAEE,iDAAoD,CAEpD,iBAAkB,CAClB,UAAW,CACX,+BAAmC,CACnC,eAAiB,CANjB,gBAUF,CAEA,mBAEE,+BACF,CAGA,cAWE,gBACF,CAQA,eAGE,kBAAmB,CACnB,kBACF,CAEA,yBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,QAAS,CAGT,eAAgB,CAJhB,iBAAkB,CAKlB,sBAAuB,CAHvB,8BAA4C,CAC5C,kBAGF,CAEA,eAGE,cAAiB,CAEjB,iBACF,CAGA,0BAME,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,OAAQ,CAGR,WAAY,CATZ,iBAAkB,CAElB,UAAW,CADX,oBAAqB,CAErB,0BAA2B,CAK3B,UAEF,CAEA,yBAKE,uCAAwC,CAFxC,kBAAmB,CACnB,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAGA,+BASE,kBAAmB,CAPnB,iBAAkB,CAMlB,YAAa,CALb,cAAe,CACf,eAAgB,CAGhB,WAAY,CANZ,gBAAiB,CAIjB,uBAAyB,CACzB,kBAIF,CAEA,uCACE,2DAA+D,CAE/D,oCAAmD,CACnD,sCAAqD,CAFrD,oBAGF,CAEA,qCACE,8BAA6C,CAE7C,oCAAmD,CADnD,uBAEF,CAEA,uCACE,2DAA+D,CAE/D,oCAAmD,CACnD,sCAAqD,CAFrD,oBAGF,CAGA,eAKE,gBAA8B,CAJ9B,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,iBAEF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,OACF,CAEA,iBAIE,aAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,8BACF,CAEA,gBAEE,WAA+B,CAD/B,eAEF,CAGA,cAGE,aAAc,CADd,gBAAiB,CADjB,sBAGF,CAGA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,aAWE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAR3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CAEnB,cAAe,CAKf,YAAa,CAEb,QAAS,CAJT,eAAgB,CAJhB,YAAa,CAGb,iBAAkB,CADlB,uBAOF,CAEA,uCAIE,oBAAqC,CADrC,iCAAkC,CAElC,6DAE+B,CAL/B,0BAMF,CAEA,UAEE,sBAAuB,CADvB,cAAe,CAEf,0BACF,CAEA,2DAEE,mBACF,CAEA,aACE,QAAO,CACP,eACF,CAEA,WAQE,kBAAmB,CAJnB,UAAc,CAGd,YAAa,CANb,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAMhB,sBAAuB,CAJvB,iBAAkB,CAClB,iBAIF,CAEA,WACE,eAAiB,CAEjB,UACF,CAEA,sBAJE,0BAQF,CAJA,WACE,cAAe,CAEf,6BACF,CAGA,eAKE,6BAA+B,CAJ/B,oBAAqC,CAGrC,0BAAwC,CAFxC,kBAAmB,CACnB,YAGF,CAEA,cAME,iCAAiD,CAJjD,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,+BANE,kBAAmB,CAFnB,YAiBF,CATA,iBAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAMjB,sBAAuB,CAJvB,QAAS,CACT,iBAIF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,WAEE,aAAc,CADd,eAAiB,CAEjB,UACF,CAEA,aAEE,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAClB,aAAc,CAGd,cAAe,CAFf,+BAAmC,CACnC,eAAiB,CANjB,gBAAiB,CAQjB,uBACF,CAEA,mBACE,oBACF,CAGA,eASE,kBAAmB,CAJnB,oBAAkC,CAElC,0BAAwC,CADxC,iBAAkB,CALlB,YAAa,CAOb,eAAiB,CANjB,QAAS,CAQT,6BAA8B,CAP9B,kBAAmB,CACnB,iBAOF,CAEA,oBACE,aAAc,CACd,eACF,CAGA,wBAIE,oBAAmC,CAGnC,0BAAwC,CADxC,iBAAkB,CALlB,sBAAyB,CAEzB,wCAA6C,CAK7C,0BAA6B,CAN7B,yBAA2B,CAG3B,gBAIF,CAGA,sBAGE,0BAA0C,CAD1C,iBAAkB,CADlB,eAGF,CAEA,YAGE,gBAA8B,CAD9B,wBAAyB,CAEzB,eAAiB,CAEjB,eAAgB,CADhB,kBAAmB,CAJnB,UAMF,CAGA,wBAA0B,QAAW,CACrC,2BAA6B,SAAY,CACzC,wBAA0B,SAAY,CACtC,wBAA0B,QAAW,CACrC,0BAA4B,SAAY,CAExC,uDAA+B,SAAY,CAE3C,eACE,sDAAmF,CAOnF,+BAAgC,CANhC,UAAc,CAGd,8BAAkC,CAClC,gBAAkB,CAClB,eAAgB,CAJhB,iBAAkB,CAMlB,uBAAgB,CAAhB,eAAgB,CALhB,iBAAkB,CASlB,+BAAyC,CAHzC,KAAM,CAEN,qBAAsB,CADtB,UAGF,CAEA,UACE,uBACF,CAEA,gBACE,oBAAmC,CAEnC,8BAA4C,CAD5C,0BAEF,CAEA,wBACE,oBACF,CAIA,YACE,8BAEF,CAEA,kBACE,8BACF,CAEA,+BACE,sDAAoF,CAIpF,gCAA8C,CAD9C,eAAgB,CAEhB,iBAAkB,CAJlB,iBAAkB,CAClB,qBAIF,CAEA,qCAOE,kDAAwD,CAFxD,QAAS,CAJT,UAAW,CAEX,MAAO,CADP,iBAAkB,CAElB,KAAM,CAEN,SAEF,CAEA,WAOE,oBAAqB,CALrB,iCAAkD,CAClD,gCAAiD,CAKjD,UAAc,CACd,eAAgB,CARhB,gBAAiB,CAIjB,iBAAkB,CADlB,uBAAyB,CAEzB,qBAKF,CAMA,0BACE,oBAAmC,CACnC,sBAAoC,CACpC,kCACF,CAEA,cAKE,oBAAqB,CAFrB,UAAc,CAMd,eAAgB,CALhB,eAAgB,CAEhB,wBAAyB,CAIzB,+BAAyC,CAHzC,kBAAmB,CALnB,UASF,CAEA,eAIE,sBAA6B,CAH7B,iBAAkB,CAClB,eAAgB,CAChB,uBAEF,CAEA,qBACE,oBAAkC,CAClC,sBACF,CAEA,YAGE,gBAA8B,CAC9B,wBAAyB,CACzB,iBAAkB,CAIlB,gBAAkB,CAIlB,eAAgB,CADhB,eAAgB,CAFhB,YAAa,CAJb,eASF,CAEA,kBAEE,oBAAiC,CADjC,6BAEF,CAGA,iBAGE,WAA+B,CAD/B,iBAAkB,CADlB,iBAGF,CAEA,oBAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,kBACF,CAEA,mBACE,cAAe,CACf,kBAAmB,CACnB,UACF,CAGA,SAEE,YAEF,CAGA,qBAGE,kBAAmB,CAGnB,aAAc,CALd,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,gBAEF,CAEA,iBAIE,0BAA6B,CAA7B,wBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAGA,0BACE,cACE,yBACF,CAEA,cACE,sBACF,CAGA,wBAA0B,WAAc,CACxC,0BAA4B,WAAc,CAC5C,CAEA,yBACE,eACE,cAAe,CACf,QACF,CAEA,aACE,qBAAsB,CACtB,QAAS,CACT,kBAAmB,CACnB,iBACF,CAEA,YACE,gBAAiB,CACjB,QACF,CAEA,cAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,YACE,eACF,CAEA,8BAEE,eACF,CAGA,wBAA0B,WAAc,CACxC,0BAA4B,WAAc,CAC5C,CAyBA,gBACE,8BAA8C,CAC9C,sCACF,CAEA,sBACE,8BACF,CAEA,gBACE,oBAAkC,CAGlC,oCAAkD,CADlD,UAAc,CADd,eAGF,CAEA,uBACE,gEAA+F,CAC/F,0CACF,CAEA,6BACE,2DACF,CAGA,cAcE,kBAAmB,CADnB,YAAa,CALb,eAAiB,CAOjB,OAAQ,CAHR,mBAAqB,CARrB,gBAAiB,CAOjB,wBAKF,CAEA,oBAGE,kDAAqD,CADrD,+BAEF,CAEA,qBACE,uBACF,CC7pBA,4BASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,0BACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAAmF,CAEnF,iCAIF,CAEA,iBACE,aAAc,CAGd,8BACF,CA0BA,kBAKE,oBAAiC,CAHjC,0BAAwC,CACxC,kBAAmB,CAFnB,kBAAmB,CAGnB,eAEF,CAEA,iBAKE,kBAAmB,CAJnB,qDAAqF,CAKrF,iCAA+C,CAH/C,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,mBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,gBAAiB,CACjB,eACF,CAEA,wCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,gBACE,aAAc,CACd,6BACF,CAEA,kBACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAwC,CACxC,aAMF,CAEA,kBACE,sDAAoF,CAEpF,+BACF,CAQA,UAEE,iCAA+C,CAD/C,iBAAkB,CAElB,uBACF,CAEA,gBACE,oBACF,CAEA,yBACE,oBAAkC,CAClC,4BACF,CAEA,eAEE,kBAAmB,CAEnB,UACF,CAEA,oCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CACf,QAAS,CAJT,6BAA8B,CAE9B,UAGF,CAEA,WACE,aAAc,CAEd,QAAO,CADP,gBAEF,CAEA,aAGE,oBAAmC,CAGnC,0BAAyC,CADzC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAGF,CAEA,iBACE,iDAAqD,CAIrD,kBAAmB,CAHnB,UAAY,CACZ,gBAAkB,CAGlB,eAAgB,CAFhB,eAAgB,CAGhB,+BACF,CAGA,cAGE,8BAMF,CAEA,YAEE,QAGF,CAqBA,yBAEE,0BAAwC,CACxC,aAKF,CAEA,+BAEE,oBAAqB,CACrB,6BACF,CA+BA,cACE,kDAGF,CAEA,mCAEE,+BACF,CAWA,iEAEE,SACF,CAEA,6EAEE,oBAAiC,CACjC,iBACF,CAEA,6EAEE,kDAAqD,CACrD,iBACF,CAEA,yFAEE,kDACF,CAGA,yBACE,0BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,iBAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,sBACF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CACF,CC5WA,2BACE,oBAAqB,CACrB,iBACF,CAEA,2BAEE,kBAAmB,CAGnB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,aAAc,CAPd,YAAa,CAQb,cAAe,CACf,eAAgB,CAPhB,OAAQ,CACR,gBAOF,CAEA,wBACE,cACF,CAEA,wBACE,kBACF,CAGA,oBAGE,kBAAmB,CAEnB,kDAA6D,CAC7D,UAAc,CAJd,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAOjB,eAAgB,CADhB,iBAEF,CAEA,2BAOE,4HAEgF,CAHhF,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAQF,CAEA,uBAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA0C,CAC1C,kBAAmB,CACnB,wDAEwC,CARxC,eAAgB,CAChB,YAAa,CAQb,iBAAkB,CAVlB,iBAAkB,CAWlB,SACF,CAEA,oBAGE,2CAA4C,CAF5C,cAAe,CACf,kBAEF,CAEA,qBACE,MAAW,uBAA0B,CACrC,IAAM,2BAA8B,CACtC,CAEA,qBAME,6BAAoC,CAFpC,4CAAqD,CACrD,4BAA6B,CAE7B,oBAAqB,CANrB,cAAe,CACf,eAAgB,CAChB,eAKF,CAEA,uBAEE,eAA+B,CAD/B,cAAe,CAGf,eAAgB,CADhB,eAEF,CAEA,oBAGE,oBAAkC,CAGlC,sBAAwC,CADxC,kBAAmB,CAHnB,WAA6B,CAD7B,cAAe,CAGf,iBAGF,CAGA,oBAGE,kBAAmB,CAEnB,kDAA6D,CAC7D,UAAc,CAJd,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAOjB,eAAgB,CADhB,iBAEF,CAEA,2BAOE,4HAEgF,CAHhF,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAQF,CAEA,uBAKE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAAwC,CACxC,kBAAmB,CACnB,2EAGwC,CATxC,eAAgB,CAChB,YAAa,CASb,iBAAkB,CAXlB,iBAAkB,CAYlB,SACF,CAEA,oBAGE,2CAA4C,CAF5C,cAAe,CACf,kBAEF,CAEA,qBACE,MAAW,sBAAyB,CACpC,IAAM,uBAA0B,CAChC,IAAM,sBAAyB,CACjC,CAEA,qBAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAChB,eAAkB,CAElB,8BACF,CAEA,uBAEE,eAA+B,CAD/B,cAAe,CAGf,eAAgB,CADhB,eAEF,CAEA,uBACE,oBAAkC,CAClC,0BAAwC,CACxC,kBAAmB,CAEnB,kBAAmB,CADnB,YAAa,CAEb,eACF,CAEA,oCAGE,cAAe,CADf,kBAEF,CAEA,cACE,eAA6B,CAC7B,eACF,CAEA,sBACE,WACF,CAEA,uBACE,YAAa,CAEb,QAAS,CADT,sBAEF,CAEA,aAEE,+CAA6D,CAE7D,kBAAmB,CAMnB,+BAA6C,CAJ7C,cAAe,CALf,iBAUF,CAEA,mBAEE,2BACF,CAGA,qBAEE,4BAA8B,CAD9B,oBAAuB,CAEvB,6BACF,CAEA,2BAEE,yBAA2B,CAD3B,wBAEF,CAGA,qBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,UAEE,oBAAqC,CAErC,0BAA0C,CAC1C,UAAc,CAJd,iBAMF,CAEA,gBACE,oBAAoC,CACpC,sBACF,CAGA,iBACE,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CAEnB,aAAc,CADd,YAEF,CAEA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,kBACF,CAEA,kBAEE,WAA+B,CAD/B,cAAe,CAGf,mBAAqB,CADrB,wBAEF,CAEA,kBAEE,eAA6B,CAD7B,cAAe,CAEf,eACF,CAEA,iBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,kBACE,YAAa,CACb,cAAe,CACf,OACF,CAEA,iBAEE,oBAAkC,CAIlC,sBAAwC,CADxC,iBAAkB,CAFlB,WAA6B,CAF7B,cAAe,CAGf,eAGF,CAeA,wBAGE,kBAAmB,CAEnB,kDAA6D,CAC7D,UAAc,CAJd,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAOjB,eAAgB,CADhB,iBAEF,CAEA,+BAOE,4HAEgF,CAHhF,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAQF,CAEA,sBAIE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA0C,CAC1C,kBAAmB,CACnB,wDAEwC,CAPxC,YAAa,CAQb,iBAAkB,CATlB,iBAAkB,CAUlB,SACF,CAEA,sBAME,qCAAsC,CAFtC,0BAA6B,CAA7B,qBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,oBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,mBAEE,WAA+B,CAD/B,cAAe,CAEf,eACF,CAGA,yBACE,oEAIE,WAAY,CADZ,iBAEF,CAEA,0CAEE,cACF,CAEA,8CAEE,cACF,CAEA,wCAEE,cACF,CAEA,sBAEE,WAAY,CADZ,UAEF,CAEA,mBACE,cACF,CACF,CCvZA,yBAEE,gFAAqF,CACrF,UAAc,CAEd,+BAAmC,CAJnC,gBAAiB,CAGjB,YAEF,CAiBA,cAKE,iBAMF,CAyBA,eAIE,YAGF,CAEA,iBAIE,kBAEF,CAcA,eACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAHhB,gBAIF,CAEA,qBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAGA,kBAIE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAHnB,QAAS,CAET,iBAGF,CAGA,sBAME,kBAAmB,CADnB,iBAIF,CAEA,aAIE,kBAAmB,CAInB,eAAiB,CAHjB,gBAQF,CAgBA,oBAGE,gBAAiB,CACjB,eAIF,CAEA,iBAIE,kBAAmB,CAOnB,+BAA8C,CAN9C,iBAAkB,CAOlB,kBACF,CAEA,uBAGE,iDAAoD,CADpD,+BAA8C,CAD9C,0BAGF,CAEA,SACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAFnB,UAAW,CAMX,cAAe,CAFf,+BAAmC,CACnC,eAAgB,CAFhB,gBAAiB,CAIjB,uBACF,CAEA,8BAEE,+BAA6C,CAD7C,qBAEF,CAEA,kBACE,oBAAoC,CACpC,eAA+B,CAC/B,kBACF,CAEA,gBACE,aAAc,CAEd,gBAAiB,CACjB,eAEF,CASA,uBAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,+BAAmC,CACnC,eAAgB,CAJhB,iBAAkB,CAMlB,uBACF,CAEA,aACE,iDAAoD,CACpD,UACF,CAEA,UACE,iDAAoD,CACpD,UACF,CAEA,mCAEE,+BAA6C,CAD7C,0BAEF,CAGA,0BAaE,eAAgB,CADhB,iBAEF,CAGA,iDATE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAQnB,+BAA6C,CAJ7C,YAAa,CAGb,QAAS,CADT,6BAA8B,CAJ9B,kBAAmB,CADnB,YAyBF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,YAAa,CAFb,sBAAuB,CAGvB,iBAAkB,CAFlB,WAGF,CAEA,oBAGE,WAAY,CACZ,kBAAmB,CAHnB,iBAAkB,CAClB,UAGF,CAEA,eAQE,wCAAyC,CAFzC,yDAA6D,CAC7D,2BAEF,CAEA,6BANE,sBAA6B,CAD7B,iBAAkB,CADlB,WAAY,CAFZ,iBAAkB,CAClB,UAeF,CAEA,QAEE,6CAA8C,CAD9C,4BAEF,CAEA,QAEE,qDAAsD,CADtD,8BAEF,CAEA,QAEE,+CAAgD,CADhD,+BAEF,CAEA,cAOE,sDAAmF,CAEnF,6BAA2C,CAH3C,WAAY,CADZ,UAKF,CAEA,0BAJE,iBAAkB,CALlB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAkBF,CAVA,YASE,4CAA6C,CAF7C,kBAAmB,CADnB,WAAY,CADZ,UAKF,CAEA,WAME,WAAY,CAHZ,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAChC,UAEF,CAEA,aAKE,wCAAyC,CADzC,gDAA4D,CAD5D,UAAW,CAFX,iBAAkB,CAClB,SAIF,CAEA,qBACE,mBAAqB,CACrB,wBACF,CAEA,qBACE,kBAAmB,CACnB,wBACF,CAEA,WAEE,QAAS,CAGT,WAAY,CAFZ,MAAO,CAGP,UAAY,CALZ,iBAAkB,CAGlB,OAGF,CAEA,WAKE,sCAAuC,CAHvC,qDAAqE,CACrE,UAAW,CAFX,iBAAkB,CAGlB,UAEF,CAEA,uBAAkC,kBAAmB,CAA3B,KAA6B,CACvD,wBAAoC,mBAAqB,CAA/B,OAAiC,CAC3D,wBAAqC,kBAAmB,CAA9B,QAAgC,CAC1D,wBAAqC,oBAAqB,CAAhC,QAAkC,CAG5D,sBAGE,kBAAmB,CAFnB,YAAa,CAIb,QAAO,CAHP,QAAS,CAET,sBAEF,CAGA,eAGE,kBACF,CAEA,WACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAGnB,cAAe,CAFf,iBAMF,CAEA,kBAOE,uDAAsF,CADtF,WAAY,CAFZ,UAAW,CADX,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,wBACE,SACF,CAEA,iBAGE,sBAAoC,CADpC,+BAA6C,CAD7C,0BAGF,CAEA,aAGE,aAAc,CADd,eAAgB,CAEhB,iBAAkB,CAElB,8BACF,CAEA,WAEE,aAAc,CACd,eAAgB,CAChB,UACF,CAGA,iBAEE,sDAAmF,CACnF,0BAAwC,CAFxC,cAAe,CAGf,uBACF,CAEA,uBACE,sDAAmF,CACnF,sBAAoC,CACpC,+BAA6C,CAC7C,sCACF,CAEA,8BACE,YAAc,CACd,8BACF,CAEA,4BACE,UACF,CAEA,aAEE,oBAAqB,CADrB,gBAAiB,CAEjB,6BACF,CAEA,oCACE,oBACF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,YAEE,QAAS,CACT,kBAGF,CAEA,WAEE,0BAAwC,CACxC,kBAAmB,CACnB,iBAEF,CAEA,+BAEE,iBAAkB,CADlB,iBAEF,CAEA,YACE,aAIF,CAEA,YACE,UAAc,CACd,gBAEF,CAGA,0BACE,YACF,CAEA,iBACE,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CACnB,kBAAmB,CACnB,eACF,CAEA,cACE,qDAAkF,CAGlF,iCAA+C,CAD/C,cAAe,CADf,iBAAkB,CAGlB,uBACF,CAEA,oBACE,qDACF,CAEA,aAEE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAIb,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,eACE,aAAc,CACd,cAAe,CACf,6BACF,CAGA,sBAGE,sDAAmF,CAGnF,0BAAwC,CADxC,kBAAmB,CAGnB,6BAA2C,CAP3C,UAAc,CAQd,oBAAqB,CAPrB,eAAgB,CAQhB,YAAa,CANb,eAAgB,CAGhB,6BAA2C,CAI3C,uBACF,CAEA,4BAEE,sDAAmF,CACnF,sBAAoC,CAEpC,6BAA2C,CAJ3C,UAAc,CAGd,8BAA4C,CAE5C,qBACF,CAGA,iBAEE,YACF,CAEA,gBAGE,oBAAqC,CADrC,wBAAyB,CAEzB,kBAAmB,CACnB,eAAgB,CAJhB,UAKF,CAEA,mBACE,qDAAkF,CAMlF,iCAA+C,CAL/C,UAAc,CAId,eAAiB,CADjB,eAAgB,CAFhB,iBAAkB,CAKlB,uBAAgB,CAAhB,eAAgB,CAJhB,iBAAkB,CAKlB,KAAM,CACN,UACF,CAEA,mBAEE,iCAAiD,CADjD,gBAAiB,CAEjB,iBAAkB,CAElB,uBAAyB,CADzB,qBAEF,CAEA,mBACE,oBACF,CAIA,UAAY,WAAc,CAC1B,eAAiB,WAAc,CAC/B,kBAAoB,WAAc,CAClC,YAAc,WAAc,CAC5B,YAAc,WAAc,CAC5B,iBAAmB,WAAc,CACjC,eAAiB,UAAa,CAC9B,oCAAuC,WAAc,CAGrD,oBACE,cAAe,CACf,iBACF,CAEA,0BACE,oBAAkC,CAClC,0BACF,CAEA,YAEE,wBAAyB,CAOzB,qBAAsB,CAJtB,eAKF,CAEA,kBAEE,oBAAqB,CACrB,4BACF,CAEA,cAGE,eACF,CAGA,YACE,oBAAoC,CACpC,+BACF,CAEA,gBACE,oBAAmC,CACnC,+BACF,CAGA,mBAME,gFAAqF,CACrF,UAAc,CAFd,gBAGF,CAEA,iBAIE,0BAA6B,CAA7B,wBAIF,CAQA,uBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,4BACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,sBACE,MAEE,SAAU,CADV,uCAEF,CACA,IAEE,UAAY,CADZ,yCAEF,CACF,CAEA,qBACE,GAEE,SAAU,CADV,oCAEF,CACA,IAEE,SAAU,CADV,kCAEF,CACA,GAEE,SAAU,CADV,mCAEF,CACF,CAEA,qBACE,GAEE,SAAU,CADV,2BAEF,CACA,IACE,SACF,CACA,GAEE,SAAU,CADV,0BAEF,CACF,CAGA,SAIE,gBACF,CAGA,0BACE,0BAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,iBAGE,kBAAmB,CADnB,WAAY,CAEZ,sBAAuB,CAHvB,UAIF,CAEA,oBAEE,WAAY,CACZ,eAAgB,CAChB,iBAAkB,CAHlB,UAIF,CAEA,sBAEE,cAAe,CACf,QAAS,CAFT,sBAGF,CAEA,kBAGE,kBAAmB,CAFnB,qBAAsB,CACtB,QAEF,CAEA,sBAEE,sBAAuB,CADvB,UAEF,CAEA,iBAEE,eAAgB,CADhB,UAEF,CAEA,uBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,eAEE,cAAe,CACf,QAAS,CAFT,sBAGF,CAEA,iBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,kBACE,qBAAsB,CACtB,QACF,CAEA,YAEE,kBAAmB,CADnB,qBAEF,CACF,CAEA,yBACE,yBACE,YACF,CAEA,YACE,gBACF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,eACE,OACF,CAEA,cAEE,qBAAsB,CADtB,OAEF,CAEA,aAEE,iBAAkB,CADlB,OAEF,CAEA,WACE,cAAe,CACf,iBACF,CAEA,aACE,gBACF,CAEA,aAEE,eAAiB,CADjB,gBAEF,CAEA,oBACE,cAAe,CACf,cACF,CAEA,iBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,oBAEE,WAAY,CACZ,iBAAkB,CAFlB,UAGF,CAEA,sBACE,QACF,CAEA,eACE,YACF,CAEA,gBAEE,cAAe,CADf,sBAEF,CAEA,gBACE,eACF,CAEA,sCAEE,eACF,CACF,CAiBA,0FACE,iDACF,CAGA,wBAWE,kBAAmB,CAMnB,2CAA4C,CAV5C,0DAA8D,CAD9D,iBAAkB,CAOlB,qDAE0C,CAP1C,cAAe,CAEf,YAAa,CALb,WAAY,CAOZ,sBAAuB,CATvB,SAAU,CAFV,cAAe,CACf,SAAU,CAcV,uBAAyB,CAZzB,UAAW,CAKX,YASF,CAEA,8BAKE,cAAe,CAHf,0DAE0C,CAH1C,qCAKF,CAEA,+BAQE,0DAA8D,CAD9D,iBAAkB,CADlB,WAAY,CALZ,UAAW,CASX,gBAAiB,CANjB,SAAU,CAOV,UAAY,CATZ,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAMT,UAGF,CAEA,UAEE,kBAAmB,CADnB,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CACvB,UAEF,CAEA,aAEE,UAAW,CAEX,gDAAqD,CAHrD,cAAe,CAEf,+BAEF,CAEA,aAKE,gBAA8B,CAU9B,0BAAwC,CAPxC,kBAAmB,CAFnB,aAAc,CAGd,cAAe,CACf,eAAgB,CAPhB,SAAU,CASV,SAAU,CALV,gBAAiB,CANjB,iBAAkB,CAClB,OAAQ,CAER,0BAA2B,CAU3B,uBAAyB,CADzB,iBAAkB,CAFlB,kBAKF,CAEA,2CACE,SAAU,CACV,kBACF,CAEA,qBACE,MACE,qDAGF,CACA,IACE,0DAGF,CACF,CAGA,0BAeE,qCAAuC,CAJvC,kCAA2B,CAA3B,0BAA2B,CAJ3B,oBAAkC,CAClC,0BAAwC,CACxC,kBAAmB,CAGnB,mDAEiC,CAXjC,QAAS,CAGT,gBAAiB,CALjB,cAAe,CACf,OAAQ,CAER,8BAAgC,CAChC,WAAY,CAKZ,YAMF,CAEA,0BACE,GACE,SAAU,CACV,wCACF,CACA,GACE,SAAU,CACV,uCACF,CACF,CAEA,qBAGE,kBAAmB,CAEnB,iCAA+C,CAJ/C,YAAa,CACb,6BAA8B,CAE9B,YAEF,CAEA,wBACE,aAAc,CACd,8BAAkC,CAClC,gBAAiB,CACjB,QAAS,CACT,8BACF,CAEA,iBAWE,kBAAmB,CAVnB,oBAAkC,CAElC,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAOd,cAAe,CACf,YAAa,CAHb,cAAe,CACf,eAAiB,CAFjB,WAAY,CAMZ,sBAAuB,CACvB,uBAAyB,CARzB,UASF,CAEA,uBACE,oBAAkC,CAClC,oBACF,CAEA,sBACE,YACF,CAEA,gBACE,YAAa,CACb,kBAAmB,CACnB,cAAe,CACf,OAAQ,CAGR,kBAAmB,CAFnB,gBAAiB,CACjB,eAEF,CAEA,eACE,oBAAqC,CACrC,0BAAwC,CACxC,iBAAkB,CAUlB,qBAAsB,CATtB,UAAc,CAKd,cAAe,CAGf,6BAA4B,CAA5B,WAA4B,CAA5B,aAA4B,CAN5B,+BAAmC,CACnC,gBAAkB,CAClB,eAAgB,CAHhB,gBAAiB,CAMjB,iBAAkB,CADlB,uBAIF,CAEA,qBACE,oBAAmC,CACnC,oBAAqB,CACrB,yBACF,CAEA,wBACE,qDAAkF,CAClF,oBAAqB,CAErB,+BAA6C,CAD7C,aAEF,CAEA,gBACE,YAAa,CACb,OAAQ,CACR,6BACF,CAEA,oCAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAPf,QAAO,CAIP,+BAAmC,CAEnC,gBAAkB,CADlB,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,kBACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAEA,wBACE,oBAAkC,CAClC,0BACF,CAEA,kBACE,iDAAoD,CAEpD,wBAAyB,CADzB,UAEF,CAEA,wBACE,iDAAoD,CAEpD,+BAA6C,CAD7C,0BAEF,CAGA,sBAQE,kBAAmB,CACnB,wCAA0C,CAR1C,qDAAoF,CACpF,0BAAwC,CACxC,kBAAmB,CAGnB,YAAa,CACb,6BAA8B,CAF9B,kBAAmB,CADnB,iBAMF,CAEA,6BACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,oBACE,aAAc,CAGd,gBAAiB,CACjB,6BACF,CAEA,sCANE,+BAAmC,CACnC,eAeF,CAVA,kBACE,oBAAkC,CAElC,wBAAyB,CACzB,iBAAkB,CAFlB,aAAc,CAMd,cAAe,CAHf,gBAAiB,CAIjB,uBACF,CAEA,wBACE,oBAAkC,CAClC,qBACF,CAGA,mCACE,SACF,CAEA,yCACE,oBAAoC,CACpC,iBACF,CAEA,yCACE,iDAAoD,CACpD,iBACF,CAEA,+CACE,iDACF,CAGA,yBACE,wBAEE,WAAY,CAEZ,SAAU,CADV,SAAU,CAFV,UAIF,CAEA,aACE,cACF,CAEA,0BAEE,eAAgB,CADhB,SAEF,CAEA,aAEE,cAAe,CADf,SAEF,CACF,CAKA,sBAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAGA,uBAoBE,kBAAmB,CAnBnB,0DAA8D,CAE9D,WAAY,CACZ,kBAAmB,CAWnB,mEAGwC,CAhBxC,UAAW,CAOX,cAAe,CAUf,YAAa,CAbb,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAchB,OAAQ,CAER,sBAAuB,CATvB,kBAAmB,CAQnB,eAAgB,CAVhB,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CAElB,wBAAyB,CAHzB,uBAcF,CAEA,8BAOE,uDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,mDACE,SACF,CAEA,4CAME,0DAA8D,CAJ9D,yEAGoC,CAJpC,sCAMF,CAEA,8BACE,sCAAuC,CACvC,uBACF,CAEA,gCACE,oBAAoC,CAIpC,sDAEwC,CALxC,eAA+B,CAC/B,kBAAmB,CACnB,cAIF,CAEA,uCACE,YACF,CAGA,uBAME,uCAAwC,CADxC,0BAAsB,CADtB,iBAAkB,CAClB,qBAAsB,CAHtB,WAAY,CADZ,UAMF,CAEA,sBACE,GACE,uBACF,CACF,CAGA,6BAYE,yCAA0C,CAL1C,iFACuD,CAGvD,yBAA0B,CAF1B,kBAAmB,CAHnB,WAAY,CALZ,UAAW,CAGX,SAAU,CASV,SAAU,CAXV,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAWT,2BAA6B,CAJ7B,UAKF,CAEA,kDACE,UACF,CAEA,0BACE,GACE,yBACF,CACA,IACE,4BACF,CACA,GACE,yBACF,CACF,CAGA,uBAIE,QAAO,CACP,aACF,CAEA,2CANE,kBAAmB,CADnB,YAAa,CAEb,sBAgBF,CAXA,oBAIE,cAAe,CAMf,iDAAsD,CAPtD,YAAa,CAFb,iBAAkB,CAKlB,0CAAiD,CADjD,wBAAiB,CAAjB,gBAAiB,CAHjB,WASF,CAEA,0BAEE,iDAAsD,CADtD,0BAEF,CAEA,wBAaE,kCAA2B,CAA3B,0BAA2B,CAN3B,oEAGgC,CAEhC,0BAAwC,CAGxC,sDAEyC,CANzC,sFAA0F,CAA1F,8EAA0F,CAN1F,YAAa,CAFb,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAIR,8BAAgC,CAQhC,0CAAiD,CAVjD,WAcF,CAEA,kDACE,oEAGgC,CAEhC,sDAE0C,CAH1C,uDAIF,CAEA,iBASE,yCAA0C,CAC1C,mEAGkB,CANlB,0BAAyC,CACzC,sFAA0F,CAA1F,8EAA0F,CAH1F,YAAa,CAFb,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAIR,8BAAgC,CAFhC,WAUF,CAWA,6EACE,YACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,qBAME,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAIZ,sBAAuB,CANvB,iBAAkB,CAClB,UAAW,CAMX,UACF,CAEA,gBAGE,WAAY,CACZ,iBAAkB,CAHlB,iBAAkB,CAClB,UAGF,CAEA,gBAWE,yCAA0C,CAL1C,+DAG0B,CAC1B,kBAAmB,CALnB,WAAY,CAOZ,gBAAiB,CATjB,SAAU,CAFV,iBAAkB,CAGlB,UAAW,CAFX,QAWF,CAEA,WAGE,4CAA6C,CAF7C,iBAAkB,CAClB,SAEF,CAEA,sBACE,MACE,uBACF,CACA,IACE,0BACF,CACF,CAEA,WACE,YACF,CAEA,qBACE,GAEE,SAAU,CADV,KAEF,CACA,IACE,SACF,CACA,IACE,SACF,CACA,GAEE,SAAU,CADV,QAEF,CACF,CAEA,aACE,iBACF,CAEA,cAQE,6BAAoC,CAFpC,yDAA6D,CAC7D,4BAA6B,CAE7B,oBAAqB,CARrB,aAAc,CACd,eAAiB,CACjB,eAAgB,CAEhB,iBAAkB,CADlB,6BAMF,CAEA,kBAKE,WAAY,CAJZ,iBAAkB,CAElB,WAAY,CADZ,SAAU,CAEV,UAEF,CAEA,WAKE,2CAA4C,CAF5C,kBAAmB,CACnB,iBAAkB,CAElB,6BAA2C,CAJ3C,WAAY,CADZ,UAMF,CAEA,qBACE,MAEE,SAAU,CADV,kBAEF,CACA,IAEE,UAAY,CADZ,oBAEF,CACF,CAUA,6EAGE,YACF,CAEA,sBACE,MACE,SAAU,CACV,mBACF,CACA,IACE,SAAU,CACV,mBACF,CACF,CAGA,yBACE,sBACE,sBAAuB,CACvB,UACF,CAEA,uBAGE,eAAiB,CAFjB,eAAgB,CAChB,iBAEF,CAEA,oBAEE,YAAa,CADb,WAEF,CAEA,uBACE,aACF,CACF,CCrqDA,gCASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,8BACE,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAMF,CAEA,iBACE,aAAc,CAGd,8BACF,CA0BA,iBACE,oBAAkC,CAClC,0BAIF,CAcA,cACE,aAGF,CAEA,cAEE,oBAAmC,CAGnC,0BAAyC,CAJzC,aAKF,CAEA,oBAEE,0BAAwC,CACxC,aAKF,CAEA,0BAEE,oBAAqB,CACrB,6BACF,CAWA,8CAGE,oBAEF,CAGA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mBAIE,oBAAiC,CAHjC,0BAAwC,CACxC,kBAAmB,CACnB,eAEF,CAEA,kBAKE,kBAAmB,CAJnB,qDAAqF,CAKrF,iCAA+C,CAH/C,YAAa,CACb,6BAA8B,CAF9B,iBAKF,CAEA,oBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,gBAAiB,CACjB,eACF,CAEA,yCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,iBACE,aAAc,CACd,6BACF,CAEA,mBACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAwC,CACxC,aAMF,CAEA,kBACE,sDAAoF,CAEpF,+BACF,CAGA,iBACE,gBAAiB,CACjB,eACF,CAEA,eAEE,iCAA+C,CAD/C,iBAAkB,CAElB,uBACF,CAEA,qBACE,oBACF,CAEA,oBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,UACF,CAEA,yCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,UACF,CAEA,gBACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAEA,uBAOE,qBAAsB,CAJtB,oBAAmC,CAGnC,0BAAyC,CADzC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAIF,CAcA,YAGE,aAGF,CAEA,iBACE,oBAAmC,CAGnC,0BAAyC,CACzC,6BAEF,CAaA,yBAEE,0BAAwC,CACxC,aAKF,CAEA,+BAEE,oBAAqB,CACrB,6BACF,CA+BA,cACE,kDAGF,CAEA,mCAEE,+BACF,CAWA,sEAEE,SACF,CAEA,kFAEE,oBAAiC,CACjC,iBACF,CAEA,kFAEE,kDAAqD,CACrD,iBACF,CAEA,8FAEE,kDACF,CAGA,yBACE,8BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,kBAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YAEE,cAAe,CADf,sBAEF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CC9aA,uBAQE,kBAAmB,CANnB,gFAAqF,CACrF,UAAc,CAGd,YAAa,CACb,qBAAsB,CAFtB,+BAAmC,CAJnC,gBAAiB,CAGjB,YAAa,CAKb,UACF,CAGA,aAIE,kBAAmB,CAEnB,cAAe,CADf,iBAAkB,CAElB,UACF,CAQA,eAEE,QAAO,CADP,iBAEF,CAEA,cACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,gBAAiB,CADjB,eAAgB,CAIhB,QAAS,CANT,iBAAkB,CAKlB,uBAEF,CAEA,oBAEE,+BAA6C,CAD7C,0BAEF,CAEA,YAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,eAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,UACF,CAGA,oCAEE,kBAAmB,CADnB,YAAa,CAEb,WACF,CAEA,yCAOE,kBAAmB,CALnB,kBAAmB,CAInB,YAAa,CAHb,cAAe,CACf,eAAgB,CAChB,WAAY,CAJZ,gBAOF,CAEA,iDACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAEA,+CACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAEA,iDACE,oBAAkC,CAElC,wBAAyB,CADzB,aAEF,CAGA,eAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CASxC,cAAe,CACf,QAAS,CAPT,kBAAmB,CADnB,iBAAkB,CAGlB,UAMF,CAGA,eAEE,kBAAmB,CADnB,YAAa,CAEb,cAAe,CACf,QACF,CAEA,iBAIE,cAAe,CADf,QAAS,CAET,0BACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,oBACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,gBACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAFf,gBAAiB,CAGjB,WACF,CAEA,sBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,uBACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,6BACE,oBAAkC,CAClC,UACF,CAEA,gBACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAEf,eAAgB,CAJhB,gBAAiB,CAKjB,sBAAuB,CACvB,kBAAmB,CAHnB,WAIF,CAEA,sBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,uBACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,6BACE,oBAAkC,CAClC,UACF,CAGA,sBAEE,kBAAmB,CAGnB,oBAAmC,CAGnC,0BAAwC,CADxC,kBAAmB,CAEnB,oCAAkD,CARlD,YAAa,CAGb,QAAS,CADT,sBAAuB,CAGvB,gBAAiB,CAIjB,yBAAkB,CAAlB,iBACF,CAEA,aACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA6C,CAV7C,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,eAAiB,CADjB,eAAgB,CAFhB,gBAAiB,CAMjB,gBAAiB,CADjB,uBAGF,CAEA,kCAGE,iDAAoD,CADpD,+BAA6C,CAD7C,sCAGF,CAEA,sBACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAEA,oBACE,aAAc,CAMd,8BAAkC,CAJlC,gBAAkB,CADlB,eAAgB,CAEhB,cAAe,CACf,iBAAkB,CAClB,6BAEF,CAEA,iBACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA8C,CAV9C,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAMhB,QAAS,CART,iBAAkB,CAMlB,gBAAiB,CADjB,uBAIF,CAEA,uBAEE,+BAA8C,CAD9C,sCAEF,CAUA,YACE,YAAa,CAGb,cAAe,CAFf,QAAS,CAGT,sBAAuB,CAFvB,kBAAmB,CAGnB,UACF,CAEA,WAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CACnB,iBAGF,CAEA,YACE,aAAc,CACd,eAAiB,CACjB,gBACF,CAEA,YACE,aAAc,CAEd,cAAe,CADf,eAEF,CAGA,kBAQE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,iBAAkB,CAIlB,YAAa,CAEb,OAAQ,CACR,eAAgB,CANhB,gBAAiB,CACjB,iBAMF,CAEA,8BACE,aAAc,CACd,eACF,CAEA,8BACE,aAAc,CAEd,cAAe,CADf,eAEF,CAGA,iBAGE,oBAAqC,CACrC,0BAAwC,CAFxC,kBAAmB,CAInB,aAAc,CACd,8BAA+B,CAN/B,eAAgB,CAOhB,eAAgB,CAHhB,UAIF,CAEA,mBAEE,wBAAyB,CACzB,+BAAmC,CAFnC,UAGF,CASA,+CALE,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CACV,WAeF,CAZA,sBACE,8DAA0E,CAI1E,0BAAwC,CAMxC,8BAAwC,CATxC,aAAc,CAKd,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAClB,iBAQF,CAGA,sBAEE,0BAA0C,CAD1C,iBAAkB,CAIlB,iBAAkB,CADlB,iBAAkB,CADlB,qBAGF,CAEA,mBACE,oBAAkC,CAClC,sBACF,CAGA,YAAyB,cAAe,CAA1B,QAA4B,CAE1C,yBAA2B,eAAgB,CAA3B,QAA6B,CAC7C,YAA0B,eAAgB,CAA5B,SAA8B,CAC5C,aAA2B,eAAgB,CAA5B,SAA8B,CAC7C,iBAA8B,eAAgB,CAA3B,QAA6B,CAChD,YAAyB,cAAe,CAA1B,QAA4B,CAC1C,YAAyB,eAAgB,CAA3B,QAA6B,CAC3C,oCAAkD,eAAgB,CAA3B,QAA6B,CAGpE,eACE,cAAe,CACf,uBACF,CAEA,qBACE,oBAAmC,CACnC,oBACF,CAEA,YAEE,oBAAoC,CACpC,wBAAyB,CACzB,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,eAAiB,CAFjB,WAAY,CAGZ,eAAgB,CAChB,iBAAkB,CATlB,UAUF,CAEA,kBAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,cACE,aAAc,CAId,iBAAkB,CAFlB,oBAAqB,CADrB,qBAIF,CAGA,YACE,8BAA6C,CAC7C,gCACF,CAEA,gBACE,8BAA8C,CAC9C,gCACF,CAGA,qBAGE,kBAAmB,CAFnB,YAAa,CAIb,qBAAsB,CACtB,QAAS,CAJT,sBAAuB,CAEvB,gBAGF,CAEA,sCAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,uBACE,aAAc,CACd,gBAAiB,CACjB,iBACF,CAGA,eACE,YAAa,CAGb,cAAe,CAFf,QAAS,CAGT,sBAAuB,CAFvB,kBAAmB,CAGnB,UACF,CAEA,WAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAKnB,eAAgB,CAEhB,eAAgB,CANhB,YAAa,CAKb,iBAAkB,CAJlB,iBAAkB,CAElB,uBAIF,CAEA,kBAQE,mCAAoC,CADpC,oDAAgF,CANhF,UAAW,CAKX,WAAY,CAFZ,SAAU,CAKV,SAAU,CAPV,iBAAkB,CAClB,QAAS,CAOT,2BAA6B,CAL7B,UAMF,CAEA,wBACE,SACF,CAEA,iBAEE,oBAAqB,CACrB,gCAA8C,CAF9C,0BAGF,CAEA,aAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,WACE,UAAc,CACd,eAAiB,CACjB,UACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,sBACF,CAEA,cACE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAOX,cAAe,CAHf,+BAAmC,CAEnC,eAAiB,CADjB,eAAgB,CAFhB,gBAAiB,CAKjB,uBACF,CAEA,oBAEE,+BAA+C,CAD/C,0BAEF,CAGA,0BACE,iBACE,QACF,CAEA,sBACE,cAAe,CACf,QACF,CAEA,eACE,QACF,CAEA,WACE,eACF,CAGA,oCAAmD,eAAgB,CAA5B,SAA8B,CACvE,CAEA,yBACE,uBACE,YACF,CAEA,YACE,gBACF,CAEA,aACE,qBAAsB,CACtB,QACF,CAEA,eACE,OACF,CAEA,cACE,OAAQ,CACR,UACF,CAEA,aACE,OACF,CAEA,WACE,QAAO,CACP,eACF,CAEA,aACE,gBACF,CAEA,aAEE,eAAiB,CADjB,gBAEF,CAEA,oBACE,cACF,CAEA,eACE,YACF,CAEA,iBACE,qBAAsB,CACtB,QACF,CAEA,mBACE,eACF,CAEA,4CAEE,eACF,CAGA,iBACE,8BACF,CACF,CAGA,oCACE,UAAW,CACX,SACF,CAEA,0CACE,oBAAoC,CACpC,iBACF,CAEA,0CACE,oBAAkC,CAClC,iBACF,CAEA,gDACE,oBACF,CAQA,kBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,sBACE,GACE,+CACF,CACA,IACE,gDACF,CACA,GACE,+CACF,CACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,4BAGE,kBAAmB,CAWnB,4CAA6C,CAP7C,kBAAmB,CACnB,0BAAwC,CACxC,iBAAkB,CAGlB,aAAc,CAFd,cAAe,CARf,YAAa,CAWb,cAAe,CAPf,WAAY,CAFZ,sBAAuB,CAHvB,iBAAkB,CAUlB,oDAA4D,CAN5D,UAUF,CAEA,mCAWE,0CAA2C,CAF3C,sBAAyB,CAFzB,iBAAkB,CAElB,wBAAyB,CAHzB,WAAY,CALZ,UAAW,CAGX,SAAU,CAFV,iBAAkB,CAGlB,UAAW,CAFX,QAAS,CAOT,8BAEF,CAEA,kCAGE,oBAAkC,CADlC,sBAAoC,CADpC,qBAGF,CAEA,yCACE,qBACF,CAEA,yCACE,6BACF,CAEA,+CACE,uBACF,CAEA,0CAYE,kBAAmB,CARnB,kDAAqD,CAWrD,0BAA0C,CAT1C,iBAAkB,CAQlB,6BAA6C,CAT7C,UAAY,CAMZ,YAAa,CAFb,cAAe,CACf,eAAiB,CAFjB,WAAY,CAKZ,sBAAuB,CAZvB,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAeN,6BAA+B,CAV/B,UAAW,CASX,SAEF,CAGA,SAGE,WAA+B,CAC/B,gBAAiB,CAFjB,YAAa,CAGb,UACF,CAGA,sCAiBE,kCAAoC,CAPpC,kCAA2B,CAA3B,0BAA2B,CAH3B,sDAAiF,CACjF,sBAAwC,CACxC,kBAAmB,CAEnB,uEAGwC,CAXxC,QAAS,CAGT,gBAAiB,CAUjB,eAAgB,CAfhB,cAAe,CACf,OAAQ,CAER,8BAAgC,CAChC,WAAY,CAUZ,YAGF,CAEA,uBACE,GACE,SAAU,CACV,wCACF,CACA,GACE,SAAU,CACV,uCACF,CACF,CAEA,iCAGE,kBAAmB,CAEnB,qDAAkF,CAClF,6BAA+C,CAL/C,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAEA,oCAEE,UAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAAS,CAIT,8BACF,CAEA,6BAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAMZ,iBAAkB,CALlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAGf,WAAY,CAIZ,sBAAuB,CACvB,uBAAyB,CANzB,UAOF,CAEA,mCACE,oBAAoC,CACpC,uBACF,CAEA,kCACE,YAAa,CACb,qBAAsB,CAEtB,gBAAiB,CACjB,iBAAkB,CAFlB,mBAGF,CAEA,4BAIE,YAAa,CAHb,QAAO,CAIP,kBAAmB,CACnB,cAAe,CACf,OAAQ,CACR,kBAAmB,CANnB,eAAgB,CAChB,gBAMF,CAEA,2BACE,oBAAkC,CAClC,0BAAwC,CAGxC,iBAAkB,CAMlB,qBAAsB,CARtB,UAAc,CAGd,cAAe,CAIf,6BAA4B,CAA5B,WAA4B,CAA5B,aAA4B,CAF5B,gBAAkB,CAJlB,gBAAiB,CAKjB,iBAAkB,CAFlB,uBAKF,CAEA,iCACE,gBAAkC,CAClC,sBAAoC,CACpC,6BACF,CAEA,oCACE,sDAAmF,CACnF,iBAAqB,CAErB,yBAA2C,CAD3C,UAEF,CAEA,4BAEE,YAAa,CADb,aAAc,CAEd,OACF,CAEA,4DAIE,WAAY,CADZ,iBAAkB,CAElB,cAAe,CAJf,QAAO,CAKP,gBAAkB,CAClB,eAAgB,CALhB,iBAAkB,CAMlB,uBACF,CAEA,8BACE,oBAAoC,CAEpC,0BAA0C,CAD1C,aAEF,CAEA,oCACE,oBAAoC,CACpC,6BACF,CAEA,8BACE,kDAAqD,CAErD,0BAAwC,CADxC,UAEF,CAEA,oCAEE,+BAA6C,CAD7C,0BAEF,CAGA,qDACE,SACF,CAEA,2DACE,oBAAkC,CAClC,iBACF,CAEA,2DACE,oBAAkC,CAClC,iBACF,CAEA,iEACE,oBACF,CAGA,wCACE,4BACF,CC38BA,8BASE,kBAAmB,CAEnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,4BACE,sDAAmF,CACnF,0BAAyC,CACzC,kBAAmB,CACnB,oDAEwC,CAKxC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cACE,qDAAmF,CAEnF,iCAAgD,CADhD,YAKF,CAEA,iBACE,aAAc,CACd,gBAAiB,CAEjB,8BACF,CAEA,WACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CAEd,cAAe,CADf,gBAAiB,CAEjB,gBAAiB,CAEjB,uBACF,CAEA,iBACE,oBAAkC,CAClC,oBACF,CAUA,iBACE,oBAAmC,CACnC,0BAAyC,CACzC,kBAAmB,CAEnB,kBAAmB,CADnB,YAEF,CAEA,aAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CAFf,QAAS,CACT,kBAEF,CAEA,wBACE,eACF,CAEA,cACE,aAAc,CAEd,cACF,CAEA,cAEE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAHnB,aAAc,CAEd,gBAGF,CAEA,oBACE,oBAAiC,CACjC,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAId,cAAe,CADf,eAAiB,CAFjB,gBAIF,CAEA,0BAEE,oBAAqB,CACrB,6BAA4C,CAF5C,YAGF,CAEA,yBAEE,kBAAmB,CAEnB,aAAc,CACd,cAAe,CAJf,YAAa,CAEb,OAAQ,CAGR,gBACF,CAEA,8CAGE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAGA,gBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,eAIE,oBAAiC,CAHjC,0BAAyC,CACzC,kBAAmB,CACnB,eAEF,CAEA,cACE,qDAAqF,CAKrF,iCAAgD,CAFhD,6BAA8B,CAF9B,iBAKF,CAEA,8BAJE,kBAAmB,CAFnB,YAYF,CANA,gBAGE,cAAe,CACf,gBAAiB,CACjB,eACF,CAEA,qCAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,aACE,aAAc,CACd,6BACF,CAEA,eACE,YAAa,CACb,QACF,CAEA,YACE,sDAAoF,CACpF,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAId,eAAiB,CAHjB,gBAKF,CAEA,kBACE,sDAAoF,CAEpF,+BACF,CAGA,kBACE,gBAAiB,CACjB,eACF,CAEA,gBAEE,iCAAgD,CADhD,iBAAkB,CAElB,uBACF,CAEA,sBACE,oBACF,CAEA,qBAEE,kBAAmB,CACnB,cAAe,CAFf,YAAa,CAGb,UACF,CAEA,0CAIE,oBAAqB,CACrB,cAAe,CAFf,WAAY,CAFZ,iBAAkB,CAClB,UAIF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,UACF,CAEA,mBACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAEA,wBAOE,qBAAsB,CAJtB,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAJnB,aAAc,CACd,gBAAkB,CAElB,eAIF,CAGA,cAME,kBAAmB,CALnB,qDAAgF,CAEhF,8BAA6C,CAI7C,cAAe,CAFf,6BAA8B,CAH9B,YAOF,CAEA,YAGE,aAAc,CAFd,YAAa,CAIb,cAAe,CADf,eAAgB,CAFhB,QAIF,CAEA,iBACE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CAGnB,eAAiB,CAJjB,gBAAiB,CAGjB,6BAEF,CAEA,kBACE,aACF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAGb,eAAgB,CADhB,QAEF,CAEA,yBACE,oBAAiC,CACjC,0BAAyC,CAGzC,iBAAkB,CAFlB,aAAc,CAId,cAAe,CADf,eAAiB,CAFjB,gBAIF,CAEA,+BAEE,oBAAqB,CACrB,6BAA4C,CAF5C,YAGF,CAOA,0BAEE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CARnB,iBAAkB,CAOlB,wBAAyB,CADzB,uBAGF,CAEA,YACE,sDAAmF,CAEnF,0BAAwC,CADxC,aAEF,CAEA,kBACE,sDAAmF,CAEnF,+BAA6C,CAD7C,0BAEF,CAEA,cACE,kDAAqD,CAErD,sBAA6B,CAD7B,UAEF,CAEA,mCAEE,+BAA8C,CAD9C,0BAEF,CAEA,uBACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAGA,uEAEE,SACF,CAEA,mFAEE,oBAAiC,CACjC,iBACF,CAEA,mFAEE,kDAAqD,CACrD,iBACF,CAEA,+FAEE,kDACF,CAGA,yBACE,4BAEE,eAAgB,CADhB,SAEF,CAEA,cACE,YACF,CAEA,iBACE,gBACF,CAEA,eACE,YACF,CAEA,cAGE,sBAAuB,CAFvB,qBAAsB,CACtB,QAEF,CAEA,cAEE,mBAAoB,CADpB,qBAEF,CAEA,YAEE,cAAe,CADf,sBAEF,CAEA,kBACE,iBACF,CAEA,gBACE,sBACF,CAEA,aAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CACF,CC5aA,aAEE,8DAA0E,CAG1E,aAAc,CADd,+CAAsD,CAHtD,gBAAiB,CAEjB,YAGF,CAGA,aAGE,kBAAmB,CAGnB,UAAW,CALX,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,eAAgB,CAEhB,2BACF,CAEA,iBAIE,0BAA6B,CAA7B,wBAA6B,CAI7B,6BAA4C,CAN5C,WAAY,CADZ,UAQF,CAQA,YAME,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,0BAAyC,CAFzC,kBAAmB,CAGnB,6BAA4C,CAR5C,6BAA8B,CAE9B,kBAAmB,CAInB,iBAAkB,CAGlB,iBACF,CAEA,yBAXE,kBAAmB,CAFnB,YAiBF,CAJA,aAGE,QACF,CAEA,aAGE,kDAAqD,CADrD,WAAY,CAGZ,iBAAkB,CADlB,UAAW,CAEX,cAAe,CAIf,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CAUlB,2BAAsC,CAFtC,uBAGF,CAEA,mBAGE,kDAAqD,CADrD,+BAA8C,CAD9C,0BAGF,CAEA,yBACE,UAAW,CAKX,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAKhB,QAAS,CAJT,QAAS,CAGT,iBAAkB,CAGlB,iBAAkB,CALlB,8BAA6C,CAI7C,0BAEF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,0BAKE,kBAAmB,CAJnB,aAAc,CAGd,YAAa,CAFb,cAAe,CACf,WAGF,CAEA,+BAWE,kBAAmB,CANnB,kBAAmB,CAFnB,WAAY,CAOZ,YAAa,CAHb,cAAe,CADf,eAAgB,CAGhB,WAAY,CAGZ,aAAc,CARd,iBAAkB,CAHlB,iBAAkB,CAClB,UAAW,CAMX,wBAKF,CAEA,uCAEE,kDAAqD,CAIrD,0BAAyC,CADzC,iBAAkB,CAElB,4BAA2C,CAN3C,UAAW,CAEX,cAAe,CACf,eAIF,CAEA,uCACE,kDAAqD,CAErD,0BAAyC,CAEzC,4BAA2C,CAH3C,UAAW,CAEX,+BAEF,CAEA,gBAIE,kDAAqD,CADrD,0BAAyC,CAGzC,kBAAmB,CAOnB,+BAA8C,CAR9C,UAAW,CAEX,cAAe,CAIf,eAAgB,CAVhB,iBAAkB,CAOlB,uBASF,CAEA,iCAJE,kBAAmB,CALnB,iCAA0B,CAA1B,yBAA0B,CAI1B,YAAa,CALb,cAAe,CAPf,WAAY,CAcZ,sBAAuB,CAJvB,+BAuBF,CAhBA,iBAIE,4DAAgE,CADhE,oCAAoD,CAGpD,kBAAmB,CADnB,oBAWF,CAEA,sBACE,kDAAqD,CACrD,oBAAqB,CAErB,6BAA4C,CAD5C,0BAEF,CAEA,uBACE,4DAAgE,CAChE,8BAAgC,CAEhC,6BAA4C,CAD5C,0BAEF,CAGA,sEAEE,4DAAgE,CAEhE,oCAAoD,CADpD,oBAEF,CAEA,kFAEE,4DAAgE,CAChE,8BACF,CAGA,kBAME,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,4BAA2C,CAF3C,kBAAmB,CAGnB,6BAA6C,CAN7C,kBAAmB,CAQnB,gBAAiB,CACjB,iBAAkB,CAClB,eAAgB,CANhB,SAAU,CAGV,yBAAkB,CAAlB,iBAIF,CAEA,8BAdE,kBAAmB,CAFnB,YAAa,CACb,sBAgCF,CAjBA,YAIE,kDAA6D,CAD7D,WAAY,CAGZ,kBAAmB,CAMnB,8BAA8C,CAP9C,UAAW,CAEX,cAAe,CAMf,wCAA+C,CAJ/C,cAAe,CACf,eAAgB,CARhB,WAAY,CASZ,YAAa,CAVb,cAAe,CAOf,uBASF,CAEA,qBAGE,kDAA6D,CAE7D,WAAY,CADZ,UAAW,CAFX,kBAAmB,CADnB,UAKF,CAEA,iCACE,kDAA6D,CAE7D,+BAA8C,CAD9C,sCAEF,CAEA,gBAaE,kBAAmB,CAHnB,gBAAuB,CACvB,eAAgB,CAVhB,UAAW,CAWX,YAAa,CAPb,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAYhB,WAAY,CADZ,sBAAuB,CAPvB,oBAAqB,CADrB,aAAc,CAEd,eAAgB,CAChB,iBAAkB,CALlB,6BAYF,CAGA,aAME,iCAA0B,CAA1B,yBAA0B,CAL1B,oBAAiC,CAMjC,0BAAyC,CALzC,kBAAmB,CAEnB,4BAA0C,CAC1C,kBAAmB,CAFnB,YAKF,CAGA,6BAIE,gBAAuB,CADvB,0BAAyC,CADzC,kBAAmB,CAGnB,6BAA4C,CAJ5C,eAKF,CAGA,gBAEE,wBAAyB,CAGzB,aAAc,CAFd,cAAe,CACf,eAAgB,CAHhB,UAKF,CAEA,sBACE,yDAA6D,CAC7D,UACF,CAEA,mBAKE,gCAAgD,CAGhD,UAAW,CAJX,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAClB,iBAAkB,CAOlB,+BAAsC,CAFtC,wBAAyB,CADzB,kBAIF,CAEA,8BACE,iBACF,CAGA,cAEE,iCAAgD,CADhD,uBAEF,CAEA,4BACE,0BACF,CAEA,oBACE,0BAAyC,CAEzC,8BAAwC,CADxC,0BAEF,CAGA,YACE,oCACF,CAEA,kBACE,0BACF,CAEA,qBACE,kCAAwC,CAGxC,aAAc,CAFd,eAAgB,CAChB,qBAEF,CAGA,eAGE,kBAAmB,CADnB,gCAA+C,CAD/C,gBAAiB,CAIjB,iBAAkB,CADlB,qBAEF,CAEA,0BACE,iBACF,CAGA,YAAc,UAAa,CAC3B,eAAiD,UAAW,CAA7B,eAAgB,CAA9B,WAA6C,CAC9D,YAA4B,eAAgB,CAA9B,WAAgC,CAC9C,WAA0B,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAC5D,YAAc,WAAc,CAC5B,cAA8B,eAAgB,CAA9B,WAAgC,CAChD,WAA0B,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAC5D,gBAAkB,WAAc,CAChC,iBAAgC,aAAc,CAAE,eAAgB,CAA7C,UAA+C,CAGlE,UACE,cAAe,CACf,iBACF,CAEA,8BACE,0BACF,CAEA,cAGE,iBAAkB,CAFlB,eAAgB,CAChB,WAAY,CAEZ,uBACF,CAEA,8BACE,0BAAyC,CACzC,yBACF,CAGA,4BACE,8BAA+C,CAG/C,oCAAqD,CAFrD,yBAA0C,CAC1C,4BAA8B,CAE9B,uBACF,CAEA,kCACE,8BAAgD,CAChD,oCAAqD,CACrD,wBACF,CAGA,gCACE,8BAA+C,CAC/C,qBACF,CAGA,aASE,kBAAmB,CANnB,wBAAyB,CACzB,iBAAkB,CAOlB,6BAA4C,CAD5C,aAAc,CAJd,cAAe,CAJf,eAAgB,CAMhB,YAAa,CAHb,WAAY,CAEZ,eAAgB,CANhB,UAWF,CAEA,mBACE,oBACF,CAGA,SAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,WACE,cAAe,CACf,aAAc,CACd,6BACF,CAGA,YAGE,kBAAmB,CAEnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAiC,CAIjC,0BAA0C,CAF1C,kBAAmB,CALnB,YAAa,CACb,wBAAyB,CAKzB,iBAEF,CAEA,YACE,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,6BACF,CAGA,0BACE,gBACE,cACF,CAEA,yCAGE,eACF,CACF,CAEA,yBACE,aACE,YACF,CAEA,YAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAAS,CAET,YACF,CAEA,2BAGE,kBAAmB,CADnB,qBAAsB,CAEtB,QACF,CAEA,kBAKE,kBAAmB,CAJnB,qBAAsB,CACtB,QAAS,CACT,WAAY,CAGZ,aAAc,CAFd,UAGF,CACA,4BAEE,cAAe,CADf,WAAY,CAGZ,YAAa,CADb,cAEF,CACA,gBACE,cAAe,CAEf,YAAa,CADb,cAEF,CAEA,aACE,YACF,CAEA,gBACE,cACF,CAEA,eACE,eACF,CACF,CAcA,uDACE,6BACF,CAGA,gDACE,UAAW,CACX,SACF,CAEA,sDACE,oBAAiC,CACjC,iBACF,CAEA,sDACE,kBAAmB,CAEnB,wBAAyB,CADzB,iBAEF,CAEA,4DACE,kBACF,CAGA,eAGE,kBAAmB,CAKnB,iCAA0B,CAA1B,yBAA0B,CAJ1B,oBAAiC,CAKjC,0BAAyC,CAJzC,kBAAmB,CAKnB,+BAAyC,CATzC,YAAa,CACb,6BAA8B,CAK9B,kBAAmB,CADnB,iBAKF,CAQA,6BAJE,kBAAmB,CADnB,YAAa,CAEb,QAOF,CAGA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,cACE,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,cAAe,CADf,6BAEF,CAEA,4BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,yBAYE,kCAA2B,CAA3B,0BAA2B,CAX3B,sDAAiF,CACjF,0BAAyC,CACzC,iBAAkB,CAUlB,+BAA8C,CAT9C,aAAc,CAId,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAIhB,eAAgB,CANhB,gBAAiB,CAIjB,uBAKF,CAEA,+BACE,sBAAqC,CACrC,+BAA8C,CAC9C,0BACF,CAEA,+BAEE,oBAAqB,CACrB,8BAA6C,CAF7C,YAGF,CAEA,gCACE,kBAAmB,CACnB,aAAc,CACd,WACF,CAKA,uBAEE,kBAAmB,CAMnB,iCAA0B,CAA1B,yBAA0B,CAH1B,oBAAkC,CAElC,0BAAwC,CADxC,kBAAmB,CALnB,YAAa,CAEb,OAAQ,CACR,gBAKF,CAEA,aACE,aAAc,CACd,cAAe,CACf,eACF,CAEA,aACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,6BACF,CAGA,gBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CACT,WAAY,CAFZ,wBAGF,CAKA,yBACE,eAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,6BAGE,kBAAmB,CADnB,qBAAsB,CAEtB,QACF,CAEA,gBAGE,QAAS,CAFT,sBAAuB,CACvB,UAEF,CAEA,wBAEE,eAAgB,CADhB,SAEF,CAEA,8CAGE,iBACF,CACF,CCntBA,6BAUE,kBAAmB,CAEnB,6BAA+B,CAL/B,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAA8B,CAD9B,QAAS,CAGT,YAAa,CACb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CASN,YAEF,CAEA,qBAWE,8BAAgC,CAVhC,kDAAqD,CAWrD,0BAAyC,CARzC,kBAAmB,CAMnB,gDAA8E,CAR9E,aAAc,CAMd,YAAa,CACb,qBAAsB,CANtB,+BAAmC,CAInC,eAAgB,CADhB,eAAgB,CADhB,SAQF,CAGA,cACE,iDAAoD,CAGpD,2BAA4B,CAF5B,UAAW,CACX,iBAKF,CAEA,iBAIE,+BAAmC,CAFnC,cAAe,CACf,eAAgB,CAEhB,8BACF,CAEA,cAGE,UAAW,CAKX,WAAY,CAFZ,SAAU,CACV,UAOF,CAEA,oBACE,gBAAoC,CACpC,uBACF,CAGA,eAGE,QAAO,CAFP,YAGF,CAGA,oBAGE,sDAAqF,CAErF,0BAAyC,CADzC,kBAAmB,CAHnB,kBAAmB,CACnB,YAIF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,WAGE,oBAA8B,CAE9B,0BAAyC,CADzC,iBAAkB,CAFlB,YAAa,CADb,iBAKF,CAEA,YAGE,aAAc,CAFd,aAAc,CACd,cAAe,CAEf,iBACF,CAEA,YAIE,aAAc,CAHd,aAAc,CAId,+BAAmC,CAHnC,cAAe,CACf,eAAgB,CAGhB,8BACF,CAEA,qBACE,aAAc,CACd,8BACF,CAGA,mBACE,kBACF,CAEA,gBAIE,kBACF,CAEA,sBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAAkB,CAIlB,6BACF,CAEA,mBAEE,kDAAqD,CAErD,WAAY,CACZ,iBAAkB,CAFlB,UAAW,CAKX,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CANhB,gBAAiB,CAQjB,uBAEF,CAEA,yBAEE,+BAA8C,CAD9C,0BAEF,CAEA,4BACE,kDACF,CAGA,iBAKE,gBAA8B,CAF9B,0BAAyC,CACzC,iBAAkB,CAHlB,gBAAiB,CACjB,eAIF,CAEA,eAEE,sBAAuB,CAGvB,iCAAgD,CADhD,cAAe,CAHf,YAAa,CAEb,YAAa,CAGb,uBACF,CAEA,0BACE,kBACF,CAEA,qBACE,oBACF,CAEA,oCAIE,oBAAqB,CAHrB,iBAAkB,CAClB,cAAe,CACf,oBAEF,CAEA,gCACE,QACF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,iBACF,CAEA,gBAGE,UAAW,CACX,QAAO,CAHP,cAAe,CACf,eAGF,CAEA,iBAIE,oBAAmC,CAEnC,iBAAkB,CAJlB,aAAc,CADd,cAAe,CAEf,eAAgB,CAEhB,eAEF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,oCAGE,aAAc,CADd,cAEF,CAEA,kBACE,aACF,CAGA,gBAEE,QACF,CAEA,YAEE,kBAAmB,CAGnB,0BAAyC,CACzC,iBAAkB,CAHlB,cAAe,CAFf,YAAa,CAGb,iBAAkB,CAGlB,uBACF,CAEA,kBACE,oBAAmC,CACnC,sBACF,CAEA,8BAEE,oBAAqB,CADrB,gBAEF,CAGA,cACE,eACF,CAEA,UACE,oBAAoC,CACpC,0BAAyC,CACzC,iBAAkB,CAClB,YACF,CAEA,aAEE,aAAc,CACd,cAAe,CAFf,eAGF,CAEA,aAGE,aAAc,CAFd,QAAS,CACT,iBAEF,CAEA,aAEE,cAAe,CADf,iBAEF,CAGA,cAME,gBAA8B,CAC9B,2BAA4B,CAL5B,8BAA6C,CAG7C,QAAS,CAJT,iBAOF,CAEA,gCAGE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAEf,+BAAmC,CAJnC,cAAe,CACf,eAAgB,CAJhB,iBAAkB,CAMlB,uBAEF,CAEA,eACE,oBAAoC,CAEpC,0BAA0C,CAD1C,aAEF,CAEA,qBACE,oBACF,CAEA,iBACE,kDAAqD,CACrD,UAAW,CACX,eACF,CAEA,sCAEE,+BACF,CAcA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,yBACE,qBAEE,eAAgB,CADhB,SAEF,CAEA,YACE,mCACF,CAEA,gBAEE,QACF,CAEA,8BAJE,qBAMF,CACF,CC9XA,uBAQE,kBAAmB,CANnB,gFAAqF,CACrF,UAAc,CAGd,YAAa,CACb,qBAAsB,CAFtB,+BAAmC,CAJnC,gBAAiB,CAGjB,YAAa,CAKb,UACF,CAGA,oCAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,kBAAmB,CAEnB,cAAe,CADf,iBAAkB,CAElB,UACF,CAEA,uCAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,sCAEE,QAAO,CADP,iBAEF,CAEA,uCAKE,iDAAoD,CAEpD,WAAY,CACZ,iBAAkB,CAOlB,0BAAwC,CATxC,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,gBAAiB,CADjB,eAAgB,CARhB,SAAU,CAMV,iBAAkB,CARlB,cAAe,CACf,QAAS,CAYT,uBAAyB,CAVzB,YAYF,CAEA,6CAEE,+BAA+C,CAD/C,0BAEF,CAGA,qCACE,2DAA+D,CAE/D,qBAAuB,CACvB,2BAA6B,CAO7B,oCAAmD,CATnD,uBAAyB,CAOzB,wBAA0B,CAH1B,yCAA8C,CAE9C,0BAA4B,CAD5B,yBAA2B,CAK3B,kBAAoB,CAPpB,2BAA6B,CAK7B,iCAGF,CAEA,2CAEE,yCAA0D,CAD1D,oCAEF,CAEA,mCAGE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAEjB,iBAAkB,CAClB,8BACF,CAEA,sCAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,UACF,CAGA,oCAEE,kBAAmB,CADnB,YAAa,CAEb,WACF,CAEA,yCAOE,kBAAmB,CALnB,kBAAmB,CAInB,YAAa,CAHb,gBAAkB,CAClB,eAAgB,CAChB,WAAY,CAJZ,gBAOF,CAEA,iDACE,2DAA+D,CAE/D,oCAAqD,CACrD,sCAAuD,CAFvD,uBAGF,CAEA,+CACE,8BAA6C,CAE7C,oCAAmD,CADnD,uBAEF,CAEA,iDACE,8BAA+C,CAE/C,oCAAqD,CAGrD,kBAAmB,CAJnB,uBAAyB,CAEzB,cAAe,CACf,gBAEF,CAEA,wCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA+C,CAV/C,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAMhB,QAAS,CART,iBAAkB,CAMlB,gBAAiB,CAGjB,4BAA6B,CAJ7B,uBAKF,CAEA,8CAEE,+BAA+C,CAD/C,yDAEF,CAGA,sCAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CAOnB,6BAA8B,CAL9B,kBAAmB,CADnB,iBAAkB,CAGlB,UAMF,CAGA,4EAPE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CACf,QASF,CAEA,wCAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAAS,CAET,0BACF,CAEA,qCAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,2CACE,aAAc,CAEd,cAAe,CADf,eAEF,CAEA,sCACE,oBAAoC,CACpC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAEd,+BAAmC,CACnC,cAAe,CAFf,gBAAiB,CAGjB,WACF,CAEA,4CAEE,oBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,6CACE,oBAAkC,CAIlC,WAAY,CAHZ,aAAc,CAEd,eAAgB,CADhB,gBAGF,CAEA,mDACE,oBAAkC,CAClC,UACF,CAGA,yCAEE,kBAAmB,CAGnB,oBAAkC,CAElC,0BAAwC,CADxC,kBAAmB,CALnB,YAAa,CAEb,OAAQ,CACR,gBAIF,CAEA,mCACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,mCACE,aAAc,CAEd,cAAe,CADf,eAAgB,CAEhB,6BACF,CAGA,mDAQE,kBAAmB,CANnB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAMlB,cAAe,CAHf,YAAa,CAKb,gBAAiB,CANjB,WAAY,CAGZ,sBAAuB,CAIvB,gBAAiB,CAZjB,iBAAkB,CAUlB,uBAAyB,CANzB,UASF,CAEA,yDACE,oBAAkC,CAElC,+BAA6C,CAD7C,oBAEF,CAEA,oCACE,gBACF,CAEA,qCAUE,kBAAmB,CANnB,iDAAoD,CAEpD,iBAAkB,CAQlB,8BAA4C,CAT5C,UAAW,CAIX,YAAa,CAGb,eAAiB,CACjB,eAAgB,CALhB,WAAY,CAGZ,sBAAuB,CAVvB,iBAAkB,CAElB,UAAW,CADX,QAAS,CAKT,UAQF,CAGA,uCAGE,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,iBACF,CAGA,6CAEE,kBAAmB,CAGnB,oBAAmC,CAGnC,0BAAwC,CADxC,kBAAmB,CAEnB,oCAAkD,CARlD,YAAa,CAGb,QAAS,CADT,sBAAuB,CAGvB,iBAAkB,CAIlB,yBAAkB,CAAlB,iBACF,CAEA,oCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA+C,CAV/C,aAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAFhB,gBAAiB,CAMjB,gBAAiB,CADjB,uBAGF,CAEA,yDAGE,iDAAoD,CADpD,+BAA+C,CAD/C,sCAGF,CAEA,6CACE,oBAAoC,CAIpC,eAAgB,CAHhB,eAA+B,CAC/B,kBAAmB,CACnB,cAEF,CAEA,2CACE,aAAc,CAMd,8BAAkC,CAJlC,gBAAiB,CADjB,eAAgB,CAEhB,cAAe,CACf,iBAAkB,CAClB,6BAEF,CAGA,qCACE,iDAAoD,CAEpD,WAAY,CACZ,kBAAmB,CAQnB,+BAA8C,CAV9C,UAAc,CAOd,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAFhB,iBAAkB,CAMlB,gBAAiB,CADjB,uBAGF,CAEA,2CAEE,+BAA8C,CAD9C,sCAEF,CAGA,sCAcE,kCAA2B,CAA3B,0BAA2B,CAT3B,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAQnB,gCAA0C,CAZ1C,QAAS,CAST,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CALhB,SAAU,CAPV,cAAe,CACf,OAAQ,CAER,8BAAgC,CAMhC,SAAU,CADV,YAOF,CAEA,iCAGE,kBAAmB,CAGnB,qDAAkF,CADlF,iCAA+C,CAJ/C,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,oCAEE,aAAc,CACd,8BAAkC,CAClC,gBAAiB,CAHjB,QAAS,CAIT,8BACF,CAEA,6BAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CATlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CALvB,uBAAyB,CACzB,UAMF,CAEA,mCACE,oBAAkC,CAClC,oBACF,CAEA,kCACE,YACF,CAEA,4BAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,kBAAmB,CACnB,gBAAiB,CACjB,eACF,CAEA,2BAEE,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CAKnB,UAAc,CAJd,cAAe,CAGf,eAAgB,CAPhB,iBAAkB,CAMlB,iBAAkB,CADlB,uBAIF,CAEA,iCACE,oBAAkC,CAClC,oBAAqB,CAErB,+BAA6C,CAD7C,0BAEF,CAEA,oCACE,qDAAkF,CAClF,oBAAqB,CACrB,aAAc,CACd,eAAgB,CAChB,6BACF,CAEA,4BACE,YAAa,CAEb,QAAS,CADT,wBAEF,CAEA,4DAGE,WAAY,CACZ,iBAAkB,CAIlB,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAJhB,iBAAkB,CAOlB,uBACF,CAEA,8BACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,oCACE,oBAAkC,CAClC,0BACF,CAEA,8BACE,iDAAoD,CAEpD,sBAA6B,CAD7B,UAEF,CAEA,oCAEE,+BAA6C,CAD7C,0BAEF,CAGA,wCASE,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAA0C,CAF1C,kBAAmB,CAInB,aAAc,CACd,8BAA+B,CAP/B,eAAgB,CAChB,eAAgB,CAIhB,UAIF,CAEA,0CAEE,wBAAyB,CACzB,eAAiB,CAFjB,UAIF,CASA,6FALE,uBAAgB,CAAhB,eAAgB,CAChB,SAAU,CACV,WAiBF,CAdA,6CACE,8DAA0E,CAI1E,0BAA0C,CAM1C,0BAAwC,CATxC,aAAc,CAUd,cAAe,CALf,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAClB,iBAAkB,CASlB,wBAAiB,CAAjB,gBACF,CAEA,wDACE,iBACF,CAEA,mDAEE,kBAAmB,CACnB,uBACF,CAEA,yDAEE,oBACF,CAEA,kCAGE,iBAAkB,CAFlB,YAAa,CAIb,iBAAkB,CADlB,qBAEF,CAEA,6CACE,iBACF,CAGA,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,eAAgB,CAA9B,WAAgC,CACtE,iCAAgD,cAAe,CAA5B,UAA8B,CACjE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAG7E,sCAQE,oBAAqB,CAGrB,gBAAuB,CACvB,sBAA6B,CAT7B,iBAAkB,CAUlB,qBAAsB,CATtB,cAAe,CAEf,aAAc,CAId,eAAgB,CAThB,eAAgB,CAChB,WAAY,CAGZ,uBAAyB,CAIzB,oBAAqB,CAFrB,UAOF,CAEA,2DAGE,gBAAuB,CADvB,2BAA2C,CAE3C,qBACF,CAEA,kFAIE,gBAAuB,CADvB,0BAA0C,CAE1C,6BACF,CAEA,+CAEE,gBAAuB,CAGvB,sBAA6B,CAF7B,kBAAmB,CACnB,UAEF,CAEA,4CAGE,0BAAkC,CAFlC,eAA+B,CAC/B,iBAEF,CAGA,kEACE,iBACF,CAEA,kDAEE,0BAAkC,CADlC,UAEF,CAOA,sGAEE,0BACF,CAGA,wCACE,0BAAkC,CAClC,qBAAuB,CACvB,yBACF,CAGA,yCACE,eAAgB,CAChB,QAAS,CACT,SACF,CAGA,oCAaE,kCAA2B,CAA3B,0BAA2B,CAT3B,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAClB,UAAc,CACd,mBAAoB,CACpB,iBAAkB,CAClB,eAAgB,CARhB,eAAgB,CAUhB,YAAa,CATb,WAAY,CAQZ,eAAgB,CAVhB,UAaF,CAEA,0CACE,oBAAqB,CACrB,6BACF,CAGA,gCAKE,oBAAqC,CACrC,kBAAmB,CAHnB,WAA+B,CAC/B,gBAAiB,CAGjB,aAAc,CALd,YAAa,CADb,iBAOF,CAGA,0CAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,gBAEF,CAWA,qCACE,aAAc,CACd,gBAAiB,CACjB,eACF,CAQA,4CAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,gBAEF,CAEA,wCAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,8CACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,8BACF,CAQA,0BAEE,0CACE,gBACF,CAEA,wDAEE,gBACF,CACF,CAEA,0BAEE,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,eAAgB,CAA9B,WAAgC,CACtE,iCAAiD,cAAe,CAA7B,WAA+B,CAClE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAE7E,sCACE,qBAAsB,CACtB,QACF,CAOA,6EAEE,sBAAuB,CADvB,UAEF,CACF,CAEA,yBACE,uBACE,YACF,CAEA,aAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,eACE,OACF,CAEA,qCAEE,qBAAsB,CADtB,OAEF,CAEA,gBAEE,sBAAuB,CADvB,OAEF,CAEA,wCAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,qCAEE,mBAAoB,CADpB,qBAAsB,CAEtB,OACF,CAEA,6CACE,qBAAsB,CACtB,QACF,CAEA,sCAEE,WAAY,CADZ,SAEF,CAEA,4BACE,yBACF,CAEA,4BACE,qBACF,CAGA,0CACE,gBACF,CAEA,wDAEE,eACF,CAGA,mCAAkD,cAAe,CAA5B,UAA8B,CACnE,oCAAoD,cAAe,CAA7B,WAA+B,CACrE,iCAAgD,cAAe,CAA5B,UAA8B,CACjE,mCAAmD,eAAgB,CAA9B,WAAgC,CACrE,qCAAoD,cAAe,CAA5B,UAA8B,CACrE,iCAAiD,eAAgB,CAA9B,WAAgC,CACnE,wCAAuD,cAAe,CAA5B,UAA8B,CAExE,kFAA2D,eAAgB,CAA9B,WAAgC,CAC/E,CAGA,2DACE,UAAW,CACX,SACF,CAEA,iEACE,oBAAoC,CACpC,iBACF,CAEA,iEACE,oBAAoC,CACpC,iBACF,CAEA,uEACE,oBACF,CAGA,yBACE,wCACE,8BACF,CACF,CAGA,kBAAqB,WAAc,CACnC,qBAAwB,WAAc,CACtC,sBAAyB,WAAc,CACvC,sBAAyB,WAAc,CACvC,wBAA2B,WAAc,CACzC,kBAAqB,WAAc,CACnC,kBAAqB,WAAc,CCz5BnC,wBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,gBAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAMnB,gCAA0C,CAH1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,cAME,qDAAkF,CADlF,iCAA+C,CAD/C,iBAGF,CAEA,iBAIE,aAAc,CAFd,8BAAkC,CAClC,gBAAiB,CAFjB,QAAS,CAIT,8BACF,CAEA,cAUE,kBAAmB,CAEnB,iBAAkB,CATlB,aAAc,CAMd,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CAJvB,UAMF,CAEA,oBACE,oBAEF,CAGA,eAEE,UAAc,CADd,iBAEF,CAGA,mBACE,kBACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,QAIF,CAEA,kBAEE,kBAAmB,CAGnB,aAAc,CADd,cAAe,CAHf,YAAa,CAKb,eAAgB,CAHhB,OAIF,CAEA,uCACE,oBAAqB,CACrB,oBACF,CAEA,YAOE,oBAAqC,CAHrC,0BAAwC,CACxC,kBAAmB,CAJnB,gBAAiB,CAEjB,iBAAkB,CADlB,eAAgB,CAIhB,YAEF,CAEA,eAEE,sBAAuB,CAGvB,cAAe,CAJf,YAAa,CAEb,OAAQ,CACR,aAAc,CAEd,uBACF,CAEA,qBACE,oBAAkC,CAClC,iBAAkB,CAClB,iBAAkB,CAClB,kBACF,CAEA,oCACE,oBAAqB,CAErB,cAAe,CADf,oBAEF,CAEA,WAME,oBAAqB,CALrB,YAAa,CAGb,QAAO,CAFP,qBAAsB,CACtB,OAAQ,CAER,WAAY,CAEZ,wBACF,CAEA,aACE,aAAc,CAGd,aAAc,CADd,eAAiB,CADjB,eAGF,CAEA,YAIE,oBAAqB,CAHrB,UAAc,CAEd,eAAgB,CADhB,eAAgB,CAGhB,wBAAyB,CACzB,kBACF,CAEA,kBACE,aAAc,CAGd,aAAc,CAFd,eAAiB,CACjB,iBAEF,CAGA,gBACE,kBACF,CAEA,mBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,gBACE,YAAa,CACb,QACF,CAEA,eAEE,kBAAmB,CAOnB,oBAAqC,CAHrC,0BAAwC,CACxC,iBAAkB,CAHlB,cAAe,CAHf,YAAa,CAEb,OAAQ,CAER,gBAAiB,CAGjB,uBAEF,CAEA,qBACE,oBAAkC,CAClC,oBACF,CAEA,iCACE,oBAAqB,CACrB,oBACF,CAEA,oBACE,UAAc,CACd,eACF,CAGA,qBACE,kBACF,CAEA,wBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,sBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,mBACE,aAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,oBACE,oBAAiC,CACjC,wBAAyB,CACzB,iBAAkB,CAElB,UAAc,CAGd,cAAe,CAFf,mBAAoB,CACpB,cAAe,CAEf,YAAa,CALb,gBAAiB,CAMjB,uBACF,CAEA,0BACE,oBAAqB,CACrB,6BACF,CAEA,2BACE,oBAAkC,CAClC,UACF,CAEA,eAIE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CALlB,aAAc,CACd,eAAgB,CAChB,iBAIF,CAGA,iBACE,kBACF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,iBAEE,kBAAmB,CAOnB,oBAAqC,CAHrC,0BAAwC,CACxC,iBAAkB,CAHlB,cAAe,CAHf,YAAa,CAEb,OAAQ,CAER,gBAAiB,CAGjB,uBAEF,CAEA,uBACE,oBAAkC,CAClC,oBACF,CAEA,sCACE,oBAAqB,CACrB,oBACF,CAEA,sBACE,UAAc,CACd,eACF,CAGA,iBACE,kBACF,CAEA,oBAEE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAHhB,eAIF,CAEA,cACE,oBAAqC,CACrC,0BAAwC,CACxC,kBAAmB,CACnB,YACF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,aACF,CAEA,eACE,aAAc,CACd,eACF,CAEA,eACE,UAAc,CACd,eACF,CAGA,cAME,oBAAqC,CADrC,8BAA4C,CAJ5C,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,iBAGF,CAEA,eACE,oBAAkC,CAClC,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CAId,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAMlB,uBACF,CAEA,qBACE,oBAAkC,CAClC,0BACF,CAEA,iBACE,iDAAoD,CACpD,WAAY,CACZ,iBAAkB,CAElB,UAAc,CAId,cAAe,CAHf,+BAAmC,CAEnC,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAMlB,uBACF,CAEA,sCAEE,+BAA6C,CAD7C,0BAEF,CAEA,0BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,0BACE,gBAEE,eAAgB,CADhB,SAEF,CAEA,YACE,gBACF,CACF,CAEA,yBACE,gBAEE,WAAY,CACZ,cAAe,CAFf,SAGF,CAMA,6BACE,YACF,CAEA,cACE,YAEF,CAMA,oDACE,qBACF,CAEA,YACE,gBACF,CAEA,eACE,aACF,CAEA,WACE,OACF,CACF,CAGA,+BACE,SACF,CAEA,qCACE,oBAAkC,CAClC,iBACF,CAEA,qCACE,sDAA2F,CAE3F,0BAAwC,CADxC,iBAEF,CAEA,2CACE,sDACF,CAEA,mCACE,UACF,CAEA,yCACE,oBAAkC,CAClC,iBACF,CAEA,yCACE,sDAA2F,CAE3F,0BAAwC,CADxC,iBAEF,CAEA,+CACE,sDACF,CC5eA,oBAME,gFAAqF,CADrF,QAAS,CAET,UAAc,CAGd,YAAa,CACb,qBAAsB,CAHtB,+BAAmC,CALnC,MAAO,CAMP,eAAgB,CARhB,iBAAkB,CAGlB,OAAQ,CAFR,KAUF,CAGA,+BAKE,WAAY,CAFZ,MAAO,CAGP,mBAAoB,CALpB,cAAe,CACf,KAAM,CAEN,UAAW,CAGX,SACF,CAEA,iDAME,+CAAgD,CAFhD,eAAmB,CACnB,iBAAkB,CAFlB,UAAW,CAFX,iBAAkB,CAClB,SAKF,CAEA,6DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,8DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,8DAGE,kBAAmB,CADnB,QAAS,CADT,OAGF,CAEA,mDAUE,8CAA+C,CAJ/C,oGAEqE,CACrE,yBAA0B,CAJ1B,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAOF,CAGA,2BAEE,kBAAmB,CAKnB,kCAA2B,CAA3B,0BAA2B,CAF3B,gBAA8B,CAC9B,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAAkB,CAIlB,iBAAkB,CAClB,UACF,CAEA,qBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,QAEF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAGb,QAAO,CADP,sBAEF,CAEA,sBAIE,QAAO,CADP,wBAEF,CAEA,qBAEE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBAEF,CAEA,2BAEE,+BAA6C,CAD7C,0BAEF,CAEA,sBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,QAAS,CADT,8BAEF,CAQA,6CAJE,kBAAmB,CADnB,YAuBF,CAlBA,uBAKE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,eAAiB,CACjB,eAAgB,CARhB,OAAQ,CAaR,sBAAuB,CADvB,eAAgB,CAXhB,iBAAkB,CAalB,iBAAkB,CAJlB,uBAKF,CAEA,6BAEE,+BAA6C,CAD7C,0BAEF,CAGA,+BACE,8CAAoD,CACpD,+BACF,CAEA,qCACE,+BACF,CAEA,6BACE,8CAAoD,CACpD,+BACF,CAEA,mCACE,+BACF,CAEA,+BACE,8CAAoD,CACpD,+BACF,CAEA,qCACE,+BACF,CAGA,sBACE,eAAiB,CACjB,eAAgB,CAEhB,UAAY,CADZ,6BAEF,CAEA,mDACE,SAAU,CACV,oBACF,CAGA,kCAEE,yDAA0D,CAD1D,8CAEF,CAEA,wDACE,iDACF,CAGA,gCACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,iCACE,MAEE,+BAA6C,CAD7C,uBAEF,CACA,IAEE,+BAA6C,CAD7C,0BAEF,CACF,CAGA,qBAEE,kBAAmB,CAEnB,UAAc,CAHd,YAAa,CAIb,eAAiB,CACjB,eAAgB,CAHhB,OAIF,CAEA,0BAIE,+CAAgD,CADhD,iBAAkB,CADlB,UAAW,CADX,SAIF,CAYA,oGACE,eAAgB,CAChB,yBACF,CAGA,wBAME,gBAA8B,CAC9B,iCAA+C,CAJ/C,6BAA8B,CAO9B,eAAgB,CALhB,YAAa,CAGb,iBAAkB,CAClB,UAEF,CAQA,4EAjBE,kBAAmB,CADnB,YAAa,CAGb,QAsBF,CAPA,wBAGE,wBAAyB,CAGzB,iBAAkB,CAFlB,UAGF,CAEA,8BAEE,UAAc,CADd,gBAAiB,CAEjB,eAAgB,CAChB,kBACF,CAEA,wBAEE,gBAA8B,CAC9B,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAGd,cAAe,CAFf,+BAAmC,CACnC,cAAe,CAGf,eAAgB,CAThB,iBAAkB,CAQlB,uBAEF,CAEA,8BAEE,iBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,+BACE,kBAAmB,CACnB,UACF,CAGA,4BAIE,YAAa,CADb,QAAO,CAEP,qBAAsB,CAEtB,QAAS,CACT,aAAc,CAFd,SAAU,CALV,iBAAkB,CAClB,UAOF,CAGA,wBAOE,oBAA8B,CAC9B,kBAAmB,CAInB,+BAAyC,CAPzC,aAAc,CAMd,QAAS,CATT,6BAA8B,CAI9B,kBAAmB,CAGnB,YAIF,CAGA,gDAbE,kBAAmB,CAOnB,0BAAwC,CATxC,YAiCF,CAlBA,wBAKE,gBAAuB,CAEvB,iBAAkB,CAClB,eAA+B,CAI/B,cAAe,CAHf,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CARhB,QAAS,CAcT,sBAAuB,CADvB,eAAgB,CADhB,eAAgB,CAXhB,iBAAkB,CAUlB,iBAAkB,CADlB,uBAKF,CAEA,+BAOE,uDAAoF,CANpF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,qCACE,SACF,CAEA,+BACE,qDAAkF,CAGlF,sBAAoC,CADpC,+BAA6C,CAD7C,UAGF,CAEA,8BAGE,sBAAoC,CAFpC,UAAc,CACd,0BAEF,CAEA,sBAEE,8CAAmD,CADnD,gBAEF,CAEA,uBACE,eAAgB,CAChB,8BAA4C,CAC5C,kBACF,CAGA,wBAEE,kBAAmB,CAEnB,gBAAuB,CAGvB,0BAAwC,CAFxC,iBAAkB,CAJlB,YAAa,CAEb,QAAS,CAGT,iBAAkB,CAElB,uBACF,CAEA,+CAYE,kBAAmB,CATnB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAGd,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAaF,CAEA,qDACE,oBAAkC,CAClC,sBAAoC,CACpC,oBACF,CAEA,+CAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAFlB,8BAGF,CAOA,2BAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAItB,QAAS,CAFT,sBAAuB,CACvB,iBAEF,CAEA,yBAME,yCAA0C,CAF1C,0BAA6B,CAC7B,iBAAkB,CADlB,qBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,sBACE,UAAc,CACd,gBAAiB,CACjB,eACF,CAGA,yBACE,MAEE,UAAY,CADZ,oCAEF,CACA,IAEE,SAAU,CADV,0CAEF,CACF,CAEA,4BACE,GACE,sBACF,CACA,GACE,8BACF,CACF,CAEA,yBACE,MACE,SAAU,CACV,kBACF,CACA,IACE,UAAY,CACZ,oBACF,CACF,CAUA,4BACE,WACF,CAEA,0CACE,oBACF,CAEA,kCACE,oBACF,CAGA,+CAEE,oBAAmC,CACnC,eACF,CAeA,6BACE,cAAe,CACf,oCACF,CAEA,mCACE,oBACF,CAEA,6BACE,oBAAoC,CACpC,WACF,CAGA,4CAEE,UAAW,CADX,SAEF,CAEA,kDACE,oBAA8B,CAC9B,iBACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,wDACE,oBACF,CAGA,0BACE,sBACE,cACF,CAEA,wBACE,qBAAsB,CAGtB,cAAe,CAFf,QAAS,CACT,YAEF,CAEA,wBACE,eAAgB,CAChB,iBACF,CACF,CAEA,yBACE,2BACE,qBAAsB,CACtB,QAAS,CACT,iBACF,CAEA,qBACE,qBAAsB,CACtB,QACF,CAEA,sBACE,gBAAiB,CACjB,iBACF,CAEA,wBACE,qBAAsB,CACtB,QACF,CAEA,wBACE,eACF,CAEA,4BAEE,QAAS,CADT,SAEF,CACF,CAGA,sCAGE,gBAA8B,CAE9B,0BAAwC,CADxC,kBAAmB,CAHnB,aAAc,CACd,YAAa,CAIb,iBAAkB,CAClB,SACF,CAGA,mCAGE,UAAc,CACd,+BAAmC,CAFnC,iBAAkB,CADlB,iBAIF,CAEA,sCACE,cAAe,CAEf,eAAgB,CAEhB,kBAAmB,CAHnB,kBAAmB,CAEnB,wBAEF,CAEA,qCACE,cAAe,CAEf,eAAgB,CADhB,UAEF,CAGA,2BAYE,kBAAmB,CAVnB,sDAA2F,CAC3F,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAId,cAAe,CAEf,YAAa,CALb,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAKhB,OAAQ,CAIR,QAAS,CAFT,kBAAmB,CAdnB,iBAAkB,CAelB,iBAAkB,CAFlB,wBAAyB,CAIzB,OAAQ,CACR,8BAAgC,CAThC,uBAUF,CAEA,iCACE,sDAA2F,CAC3F,iBAAqB,CAErB,+BAA6C,CAD7C,+CAEF,CAEA,oCAEE,kBAAmB,CADnB,UAAY,CAEZ,8BACF,CAEA,kBACE,cACF,CAGA,qCAUE,kBAAmB,CAEnB,qCAAuC,CALvC,iCAA0B,CAA1B,yBAA0B,CAD1B,oBAA+B,CAD/B,QAAS,CAQT,cAAe,CALf,YAAa,CACb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CASN,YAGF,CAEA,6BAeE,4DAAmE,CAVnE,8DAA0E,CAE1E,qBAAyB,CADzB,eAAgB,CAEhB,4EAGwC,CAKxC,cAAe,CAJf,YAAa,CACb,qBAAsB,CAXtB,YAAa,CAEb,eAAgB,CADhB,cAAe,CAcf,YAAa,CAHb,eAAgB,CAbhB,WAiBF,CAEA,sBAOE,kCAA2B,CAA3B,0BAA2B,CAF3B,sDAA6F,CAC7F,iCAA+C,CAJ/C,sBAAuB,CAOvB,eAAgB,CALhB,gBAAiB,CAIjB,iBAEF,CAEA,6BAOE,uDAAoF,CANpF,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAKF,CAEA,qBACE,UAAc,CAQd,QAAO,CAPP,+BAAmC,CACnC,cAAe,CAIf,kBAAmB,CAFnB,aAAc,CAGd,iBAAkB,CAFlB,wBAOF,CAEA,8CAJE,kBAAmB,CADnB,YAAa,CANb,eAAgB,CAQhB,sBAsBF,CAnBA,yBAGE,sDAAwF,CACxF,sBAAsC,CACtC,iBAAkB,CAClB,aAAc,CAGd,cAAe,CAFf,cAAe,CALf,WAAY,CAeZ,eAAgB,CAHhB,iBAAkB,CAElB,UAAW,CADX,QAAS,CALT,+CAAsD,CATtD,UAAW,CAiBX,UACF,CAEA,gCAOE,wDAA6E,CAC7E,iBAAkB,CAPlB,UAAW,CAKX,QAAS,CAFT,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAMR,8BAAgC,CAChC,uBAAyB,CALzB,OAMF,CAEA,+BACE,sDAAyF,CACzF,oBAAqB,CAGrB,yBAAyC,CADzC,aAAc,CADd,kCAGF,CAEA,sCAEE,WAAY,CADZ,UAEF,CAEA,gCACE,kCACF,CAEA,uBAKE,YAAa,CAJb,QAAO,CAKP,qBAAsB,CAHtB,eAAgB,CADhB,SAAU,CAEV,iBAGF,CAEA,0CACE,SACF,CAEA,gDACE,oBAAkC,CAClC,iBACF,CAEA,gDACE,sDAA2F,CAE3F,0BAAwC,CADxC,iBAEF,CAEA,sDACE,sDACF,CAGA,0BACE,GAEE,+BAA0B,CAA1B,uBAA0B,CAD1B,SAEF,CACA,GAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,SAEF,CACF,CAEA,6BACE,GAGE,gBAAiB,CAFjB,SAAU,CACV,oDAEF,CACA,IAGE,gBAAiB,CAFjB,UAAY,CACZ,qDAEF,CACA,GAGE,cAAiB,CAFjB,SAAU,CACV,8CAEF,CACF,CAGA,2BACE,GAEE,iCAA0B,CAA1B,yBAA0B,CAD1B,SAEF,CACA,GAEE,+BAA0B,CAA1B,uBAA0B,CAD1B,SAEF,CACF,CAEA,gCACE,GAGE,cAAiB,CAFjB,SAAU,CACV,8CAEF,CACA,GAGE,gBAAiB,CAFjB,SAAU,CACV,mDAEF,CACF,CAGA,0BACE,6BAGE,eAAgB,CADhB,YAAa,CADb,WAGF,CAEA,sBAEE,eAAgB,CADhB,iBAEF,CAEA,qBAME,kBAAmB,CADnB,YAAa,CADb,QAAO,CAHP,cAAe,CAMf,sBAAuB,CAJvB,aAAc,CADd,iBAMF,CAEA,uBAGE,YAAa,CACb,qBAAsB,CAFtB,eAAgB,CADhB,SAIF,CAEA,yBAKE,cAAe,CADf,WAAY,CAFZ,UAAW,CADX,QAAS,CAET,UAGF,CACF,CAEA,yBACE,wBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,4BACE,sBACF,CAEA,wBAEE,cAAe,CADf,sBAAuB,CAEvB,iBACF,CAEA,2BACE,eAAgB,CAChB,cACF,CAEA,iCACE,0BACF,CAEA,oCACE,cACF,CAEA,6BAIE,WAAY,CADZ,eAAgB,CADhB,YAAa,CAIb,eAAgB,CADhB,cAAe,CAJf,WAMF,CAEA,sBAEE,eAAgB,CADhB,iBAEF,CAEA,qBACE,cAAe,CACf,kBACF,CAEA,yBAGE,cAAe,CADf,WAAY,CAGZ,UAAW,CADX,OAAQ,CAHR,UAKF,CAEA,uBAGE,YAAa,CACb,qBAAsB,CAFtB,eAAgB,CADhB,SAIF,CAEA,2BACE,cAAe,CACf,iBACF,CACF,CC9/BA,wBAEE,kBAAmB,CAGnB,gBAA8B,CAC9B,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAGA,0BAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,uBAYE,kBAAmB,CATnB,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CASlB,+BAA6C,CAR7C,UAAW,CAGX,cAAe,CAEf,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAcF,CAEA,6BAEE,+BAA6C,CAD7C,0BAEF,CAEA,uBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAGhB,eAAgB,CAChB,iBAAkB,CAFlB,8BAGF,CAiBA,0BAEE,+BAA6C,CAD7C,0BAEF,CAKA,yBAGE,kBAAmB,CACnB,+BAAyC,CAHzC,QAAO,CAIP,kBAAwB,CAHxB,eAIF,CAGA,mBAKE,gBAA8B,CAK9B,qBAAuB,CAPvB,uBAAyB,CACzB,gBAAiB,CAEjB,+BAAmC,CACnC,gBAAkB,CAKlB,sBAAwB,CAJxB,iBAAkB,CANlB,4BAA8B,CAD9B,UAYF,CAGA,yBAEE,qDACF,CAEA,sBAgBE,kCAA2B,CAA3B,0BAA2B,CAD3B,8BAA0C,CAO1C,2BAA4B,CAZ5B,WAAkB,CAAlB,iCAAkB,CAUlB,8BAAwC,CAfxC,UAAc,CADd,eAAgB,CAehB,eAAgB,CAlBhB,iBAAkB,CAWlB,iCAA2B,CAA3B,yBAA2B,CAV3B,iBAAkB,CAelB,6BAA2C,CAJ3C,eAAiB,CAVjB,qBAAsB,CAetB,kBAAmB,CAJnB,sBASF,CAWA,qEAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,0BAA4B,CAD5B,oBAEF,CAEA,sBACE,+DAA+F,CAC/F,eAAiB,CACjB,eACF,CAUA,4EAEE,yBAA2B,CAD3B,yBAA2B,CAE3B,qBACF,CAGA,sCAEE,wBAA0B,CAD1B,wBAA0B,CAE1B,oBACF,CAGA,4BACE,iCAA+C,CAC/C,uBACF,CAGA,uCACE,4BACF,CAEA,kCACE,oBAAkC,CAClC,sBACF,CAEA,0CACE,oBACF,CAEA,gDACE,oBACF,CAGA,sBAQE,oBAAqB,CAHrB,WAAY,CAOZ,WAAY,CAFZ,eAAgB,CAChB,eAAgB,CAEhB,eAAgB,CANhB,eAAgB,CANhB,gBAAiB,CAKjB,iBAAkB,CAJlB,iBAAkB,CAClB,qBAAsB,CAMtB,oBAKF,CAGA,uBACE,oBAAkC,CAElC,UAAc,CAQd,eAAiB,CATjB,eAAgB,CAIhB,wBAA0B,CAD1B,wBAA0B,CAK1B,0BAA4B,CAF5B,iBAAkB,CAClB,qBAAsB,CAFtB,oBAKF,CAEA,wBACE,oBAAmC,CAGnC,eASF,CAEA,6CANE,oBAAqB,CAErB,eAAgB,CAJhB,yBAA2B,CAD3B,yBAA2B,CAH3B,iBAAkB,CADlB,eAAgB,CAUhB,qBAAsB,CAFtB,oBAAqB,CAFrB,qBAkBF,CAEA,qBAEE,UAAc,CADd,eAAgB,CAIhB,wBAA0B,CAD1B,wBAA0B,CAK1B,0BAA4B,CAF5B,iBAAkB,CAClB,qBAAsB,CAFtB,oBAIF,CAEA,uBAKE,oBAAqB,CAErB,eAAgB,CAJhB,yBAA2B,CAD3B,yBAA2B,CAO3B,iBAAkB,CADlB,qBAAsB,CAFtB,oBAAqB,CAFrB,qBAMF,CAEA,wBAGE,eAAgB,CADhB,cAAe,CAKf,gBAAiB,CAFjB,iBAAkB,CAClB,qBAAsB,CAFtB,WAIF,CAGA,oBASE,oBAAqB,CAJrB,cAAe,CAMf,eAAgB,CARhB,eAAgB,CAShB,eAAgB,CAVhB,eAAgB,CAMhB,iBAAkB,CAJlB,iBAAkB,CASlB,iBAAkB,CAPlB,uBAAyB,CACzB,qBAAsB,CAGtB,oBAIF,CAGA,wEAGE,yBAA2B,CAD3B,yBAA2B,CAG3B,2BAA6B,CAD7B,qBAEF,CAGA,oCAEE,wBAA0B,CAD1B,wBAA0B,CAG1B,0BAA4B,CAD5B,oBAEF,CAGA,6BACE,oBAAmC,CACnC,+BACF,CAEA,mCACE,oBAAmC,CACnC,mCAAiD,CACjD,qBACF,CAGA,6BACE,oBAAqC,CAGrC,+BAA+C,CAF/C,WAA+B,CAC/B,kBAEF,CAEA,mCACE,oBACF,CAGA,4BACE,0BACF,CAEA,8BACE,8BACF,CAGA,4BAME,oBAA8B,CAC9B,iBAAkB,CAHlB,eAAiB,CAKjB,WAAY,CAJZ,UAAY,CAJZ,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAMR,UAAW,CAKX,SACF,CAGA,kDANE,kBAAmB,CADnB,YAAa,CAEb,sBAmBF,CAdA,sBASE,eAAgC,CAIhC,qBAAsB,CALtB,eAAiB,CALjB,eAAgB,CADhB,eAAgB,CAIhB,wBAAyB,CACzB,iBAAkB,CAFlB,oBAAqB,CAJrB,UAAW,CAGX,qBAUF,CAGA,mBAEE,aAAc,CADd,iBAEF,CAEA,8BACE,eACF,CAEA,yBACE,YACF,CAGA,2BACE,gBAAiB,CACjB,eAAgB,CAGhB,iBAAkB,CADlB,+BAAmD,CADnD,oBAGF,CAEA,8CACE,SACF,CAEA,oDACE,gBAA8B,CAC9B,iBACF,CAEA,oDACE,oBAAkC,CAClC,iBACF,CAEA,0DACE,oBACF,CAGA,sCAEE,eAA6B,CAD7B,aAAc,CAEd,eACF,CAGA,mCAEE,eAA+B,CAD/B,WAAY,CAEZ,iBACF,CAGA,oBAGE,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CACd,+BAAmC,CACnC,eAAiB,CANjB,eAAgB,CAQhB,YAAa,CACb,WAAY,CAFZ,iBAAkB,CAGlB,uBAAyB,CAXzB,UAYF,CAEA,0BACE,oBAAkC,CAClC,iBAAqB,CACrB,6BAA2C,CAC3C,qBACF,CAEA,uBAgBE,oBAAqB,CAZrB,oBAAkC,CAClC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CACd,+BAAmC,CACnC,eAAiB,CAIjB,eAAgB,CAVhB,gBAAiB,CADjB,eAAgB,CAQhB,YAAa,CACb,WAAY,CACZ,eAAgB,CAEhB,uBAAyB,CACzB,oBAAqB,CAdrB,UAgBF,CAEA,6BACE,oBAAkC,CAClC,iBAAqB,CACrB,6BAA2C,CAC3C,qBACF,CAGA,0CACE,SACF,CAEA,gDACE,gBAA8B,CAC9B,iBACF,CAEA,gDACE,oBAAkC,CAClC,iBACF,CAEA,sDACE,oBACF,CAGA,sBAOE,kBAAmB,CANnB,gBAA8B,CAC9B,0BAAwC,CACxC,iBAAkB,CAGlB,YAAa,CAGb,cAAe,CACf,QAAS,CAFT,6BAA8B,CAH9B,kBAAmB,CADnB,iBAOF,CAEA,8CAGE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,4CAEE,UAAc,CAEd,eAAiB,CADjB,eAEF,CAEA,yFAKE,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAFf,eAAiB,CACjB,eAAgB,CAJhB,gBAAiB,CAMjB,uBACF,CAEA,mBACE,iDAAoD,CACpD,UACF,CAEA,yBACE,iDAAoD,CACpD,0BACF,CAEA,6CAEE,iDAAoD,CACpD,UACF,CAEA,yDAEE,iDAAoD,CACpD,0BACF,CAEA,yBACE,iDAAoD,CACpD,UACF,CAEA,+BACE,iDAAoD,CACpD,0BACF,CAGA,uBAQE,kBAAmB,CAGnB,iCAA0B,CAA1B,yBAA0B,CAL1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAEF,CAEA,qBACE,8DAA0E,CAC1E,0BAAwC,CACxC,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAKF,CAEA,sBACE,oBAAkC,CAClC,iBAKF,CAEA,yBAGE,+BAEF,CAEA,qBACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,UAAc,CAEd,cAAe,CADf,gBAAiB,CAEjB,WAAY,CAEZ,uBACF,CAEA,2BACE,oBAAkC,CAClC,oBACF,CAEA,uBAEE,gBAAiB,CADjB,YAGF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,oBACE,oBAAgC,CAChC,0BAAsC,CACtC,iBAAkB,CAClB,YACF,CAEA,qBACE,UAAc,CAEd,eAAiB,CADjB,eAAgB,CAEhB,iBACF,CAEA,uBACE,aAAc,CACd,eAAgB,CAChB,iBACF,CAEA,oBACE,WAA+B,CAC/B,eAAiB,CACjB,iBACF,CAEA,qBACE,WAA+B,CAC/B,eAAiB,CACjB,iBACF,CAEA,sBAKE,QAAS,CAHT,iBAKF,CAEA,8CAGE,WAAY,CACZ,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAHhB,iBAAkB,CAKlB,uBACF,CAEA,uBACE,iDAAoD,CACpD,UACF,CAEA,6BACE,iDAAoD,CACpD,0BACF,CAEA,uBACE,iDAAoD,CACpD,UACF,CAEA,6BACE,iDAAoD,CACpD,0BACF,CAGA,qBACE,oBAAkC,CAClC,0BACF,CAEA,2BAOE,uDAAoF,CADpF,QAAS,CALT,UAAW,CAGX,MAAO,CAIP,mBAAoB,CANpB,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAGA,0BACE,wBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,0BACE,sBACF,CAEA,oBACE,iBACF,CAEA,mBACE,gBACF,CAEA,4CAEE,eACF,CAYA,4CAKE,eAAiB,CAHjB,cAAe,CADf,cAAe,CAGf,eAAgB,CADhB,UAGF,CAEA,sBAEE,eAAiB,CADjB,eAEF,CAEA,oBACE,eACF,CACF,CAEA,yBACE,wBACE,iBACF,CAEA,uBACE,cAAe,CACf,eACF,CAEA,uBAGE,cAAe,CADf,WAAY,CADZ,UAGF,CAEA,oBAEE,eAAiB,CADjB,iBAEF,CAEA,mBACE,eACF,CAEA,4CAEE,eACF,CAYA,4CAKE,gBAAkB,CAHlB,cAAe,CADf,cAAe,CAGf,eAAgB,CADhB,UAGF,CAEA,6CAEE,gBACF,CAEA,sBAEE,gBAAkB,CADlB,cAEF,CAEA,oBACE,cACF,CAEA,0BACE,eAAiB,CACjB,WACF,CACF,CC/1BA,8BAQE,kBAAmB,CAGnB,kCAA2B,CAA3B,0BAA2B,CAL3B,gBAA8B,CAC9B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CANvB,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAMX,YAEF,CAEA,sBASE,2CAA6C,CAR7C,sDAAmF,CACnF,0BAAwC,CACxC,kBAAmB,CAKnB,gCAA0C,CAF1C,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAHhB,SAMF,CAGA,sBAEE,kBAAmB,CAGnB,qDAAkF,CAClF,iCAA+C,CAL/C,YAAa,CAEb,6BAA8B,CAC9B,iBAGF,CAEA,yBAIE,UAAc,CAHd,8BAAkC,CAClC,gBAAiB,CACjB,eAAgB,CAEhB,QAAS,CACT,8BACF,CAEA,mBAYE,kBAAmB,CATnB,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CASlB,+BAA6C,CAR7C,UAAW,CAGX,cAAe,CAEf,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CANhB,WAAY,CAWZ,sBAAuB,CAHvB,uBAAyB,CATzB,UAcF,CAEA,yBAEE,+BAA6C,CAD7C,oBAEF,CAGA,uBAEE,gBAAiB,CACjB,eAAgB,CAFhB,YAGF,CAGA,2BACE,kBACF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAIb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAC9B,kBAGF,CAEA,2BAIE,UAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAEhB,QAAS,CACT,6BACF,CAEA,4BAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,QAEF,CAEA,0BAEE,kBAAmB,CAEnB,UAAc,CAEd,cAAe,CALf,YAAa,CAIb,eAAiB,CAFjB,OAAQ,CAIR,yBACF,CAEA,gCACE,UACF,CAEA,+CAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,yBAKE,oBAAkC,CAElC,0BAAwC,CADxC,iBAAkB,CALlB,UAAc,CACd,eAAiB,CACjB,eAAgB,CAChB,eAIF,CAGA,yBAKE,oBAA8B,CAF9B,0BAAwC,CACxC,iBAAkB,CAHlB,gBAAiB,CACjB,eAAgB,CAIhB,YACF,CAEA,4CACE,SACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,kDACE,oBAAkC,CAClC,iBACF,CAEA,wDACE,oBACF,CAEA,wBAEE,kBAAmB,CAOnB,iBAAkB,CAJlB,UAAc,CAEd,cAAe,CANf,YAAa,CAKb,eAAiB,CAHjB,QAAS,CAOT,iBAAkB,CANlB,gBAAiB,CAIjB,uBAGF,CAEA,8BACE,oBAAkC,CAClC,yBACF,CAEA,mCACE,eACF,CAEA,6CAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,wBACE,QAAO,CACP,eACF,CAGA,wBACE,kBACF,CAEA,2BAIE,UAAc,CAHd,+BAAmC,CACnC,gBAAiB,CACjB,eAAgB,CAEhB,eAAkB,CAClB,6BACF,CAEA,wBACE,YAAa,CAEb,cAAe,CADf,QAEF,CAEA,uBAEE,kBAAmB,CAGnB,oBAA8B,CAC9B,0BAAwC,CACxC,kBAAmB,CACnB,UAAc,CAEd,cAAe,CATf,YAAa,CAWb,QAAO,CAHP,cAAe,CANf,QAAS,CAUT,eAAgB,CAThB,iBAAkB,CAOlB,uBAGF,CAEA,6BAEE,oBAAkC,CADlC,iBAAqB,CAGrB,+BAA6C,CAD7C,0BAEF,CAEA,yCAGE,iBAAqB,CACrB,cAAe,CAFf,WAAY,CADZ,UAIF,CAEA,qBAEE,8CAAmD,CADnD,gBAEF,CAEA,qBAEE,QAAO,CADP,eAEF,CAGA,sBAEE,kBAAmB,CAInB,oBAA8B,CAC9B,8BAA4C,CAN5C,YAAa,CAGb,QAAS,CADT,wBAAyB,CAEzB,iBAGF,CAEA,oBAEE,gBAAuB,CACvB,0BAA0C,CAC1C,iBAAkB,CAClB,UAAc,CAId,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBACF,CAEA,0BAEE,oBAAoC,CADpC,iBAEF,CAEA,6BAEE,kBAAmB,CADnB,UAEF,CAEA,oBAEE,8CAAoD,CACpD,WAAY,CACZ,iBAAkB,CAOlB,+BAA6C,CAN7C,UAAW,CAIX,cAAe,CAHf,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CASlB,uBAEF,CAEA,yCAEE,+BAA6C,CAD7C,0BAEF,CAEA,6BAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,gCACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAGA,yBACE,sBAEE,eAAgB,CADhB,SAEF,CAEA,sBACE,iBACF,CAEA,yBACE,gBACF,CAEA,uBACE,YACF,CAEA,wBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,4BAEE,6BAA8B,CAD9B,UAEF,CAEA,wBACE,qBACF,CAEA,uBACE,cACF,CAEA,sBAEE,qBAAsB,CACtB,QAAS,CAFT,iBAGF,CAEA,wCAGE,iBAAkB,CADlB,UAEF,CACF,CCzXA,4BAEE,8DAA0E,CAI1E,0BAAwC,CAHxC,kBAAmB,CAEnB,+BAA6C,CAK7C,YAAa,CADb,QAAO,CAEP,qBAAsB,CAPtB,QAAS,CAIT,eAAgB,CAPhB,WAAY,CAMZ,iBAKF,CAGA,mCAOE,4HAEiF,CAHjF,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,mBAAoB,CARpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAQN,SACF,CAGA,gCAYE,kBAAmB,CANnB,oBAAmC,CAEnC,0BAAwC,CADxC,kBAAmB,CANnB,YAAa,CAYb,aAAc,CAXd,cAAe,CACf,QAAS,CAQT,6BAA8B,CAP9B,iBAAkB,CAClB,gBAAiB,CAIjB,iBAAkB,CAClB,YAIF,CAEA,uBAGE,kBAAmB,CAFnB,YAAa,CAKb,QAAO,CAJP,kBAAmB,CAEnB,QAAS,CACT,eAEF,CAEA,uBACE,UAAc,CAGd,+BAAmC,CAFnC,cAAe,CACf,eAAgB,CAGhB,kBAAmB,CAEnB,cAAe,CAHf,wBAAyB,CAEzB,kBAEF,CAEA,iGAKE,oBAAiC,CACjC,0BAAwC,CACxC,iBAAkB,CAClB,UAAc,CAKd,cAAe,CAEf,QAAO,CANP,+BAAmC,CACnC,cAAe,CACf,eAAgB,CAGhB,eAAgB,CAVhB,gBAAiB,CAQjB,uBAIF,CAEA,yHAIE,iBAAqB,CACrB,6BAA2C,CAC3C,0BACF,CAEA,yHAKE,iBAAqB,CACrB,6BAA2C,CAF3C,YAGF,CAEA,6HAIE,kBAAmB,CACnB,UAAc,CACd,YACF,CAGA,sBAIE,2BAA4B,CAE5B,mBAAoB,CALpB,iBAAkB,CAClB,YAKF,CAEA,4BACE,YACF,CAGA,kCACE,aACF,CAGA,6BACE,4BAA8B,CAC9B,oBAAyB,CACzB,0BACF,CAGA,iCAEE,QAAO,CADP,iBAAkB,CAElB,aACF,CAEA,2BAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAG9B,eAAgB,CADhB,UAEF,CAEA,wBACE,6BACF,CAEA,6BACE,wBACF,CAEA,4BAiBE,6CAA+C,CAZ/C,kBAAmB,CACnB,0BAAwC,CACxC,iBAAkB,CAKlB,2BAAyC,CATzC,SAAU,CAMV,gBAAiB,CACjB,eAAgB,CAChB,YAAa,CAVb,cAAe,CACf,QAAS,CAYT,uBAAwB,CAVxB,WAAY,CAWZ,qBAAsB,CAPtB,aAUF,CAGA,kCACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,8BAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,gBAAiB,CAEjB,oCACF,CAEA,oCACE,0BACF,CAEA,oCAEE,UAAc,CACd,+BAAmC,CACnC,cAAe,CAHf,gBAIF,CAEA,uBAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAKb,0BAA2B,CAH3B,gBAAiB,CAEjB,oCAEF,CAEA,6BACE,0BACF,CAEA,6BAEE,UAAc,CAGd,cAAe,CAFf,+BAAmC,CACnC,cAAe,CAHf,gBAKF,CAEA,yBACE,uBAAgB,CAAhB,eAAgB,CAGhB,wBAA6B,CAC7B,0BAAwC,CACxC,iBAAkB,CAClB,cAAe,CAJf,WAAY,CAKZ,iBAAkB,CAClB,uBAAyB,CAPzB,UAQF,CAEA,iCACE,qBAAyB,CACzB,iBACF,CAEA,uCAME,aAAc,CALd,WAAY,CAMZ,cAAe,CACf,eAAiB,CAJjB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAIF,CAKA,yBAKE,gBAA8B,CAE9B,0BAAwC,CADxC,kBAAmB,CAInB,qCAA+C,CAC/C,YAAa,CAVb,QAAO,CAWP,qBAAsB,CATtB,QAAS,CADT,YAAa,CAEb,WAAY,CAIZ,iBAAkB,CAClB,SAIF,CAMA,gDACE,gCACE,cAAe,CACf,sBACF,CAEA,uBAEE,2BAA0B,CAA1B,WAA0B,CAA1B,aAA0B,CAD1B,eAEF,CAEA,yBAEE,QAAO,CAEP,YAAa,CACb,WACF,CACF,CAGA,yBACE,4BAEE,aAAc,CADd,YAEF,CAEA,gCAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAAS,CAET,YACF,CAEA,uBAIE,kBAAmB,CAFnB,SAAU,CACV,kBAAmB,CAFnB,cAIF,CAEA,uBACE,cAAe,CACf,cACF,CAEA,iGAIE,cAAe,CAEf,eAAgB,CADhB,WAEF,CAEA,yBAEE,QAAO,CAIP,cAAiB,CAFjB,YAAa,CACb,WAEF,CACF,CAEA,yBACE,yBAEE,QAAO,CAEP,YAAa,CACb,WACF,CAEA,uBACE,cAAe,CACf,cACF,CAEA,iGAKE,cAAe,CACf,cAAe,CAFf,gBAGF,CACF,CAGA,uBAGE,kBAAmB,CAEnB,UAAc,CAJd,YAAa,CAMb,+BAAmC,CADnC,cAAe,CAFf,YAAa,CAFb,sBAMF,CAEA,6BAOE,yCAA0C,CAF1C,0BAA6B,CAC7B,iBAAkB,CADlB,qBAA6B,CAJ7B,UAAW,CAEX,WAAY,CAKZ,gBAAiB,CANjB,UAOF,CAEA,wBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,gCACE,kBAAmB,CAInB,kBAAkB,CADlB,qBAAuB,CAGvB,yBAA2B,CAD3B,sBAAwB,CAExB,4BAA8B,CAN9B,uBAAyB,CACzB,oBAMF,CAEA,sCACE,6BACF,CAGA,6BAKE,sBAAwB,CADxB,kBAAkB,CAElB,+BAAiC,CAJjC,qBAAuB,CAKvB,yBAA2B,CAJ3B,sBAAwB,CAFxB,oBAOF,CAGA,gCACE,6CACF,CAEA,kCACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CCrbA,oBAQE,kBAAmB,CAGnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAA8B,CAL9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CASP,SAAU,CAXV,cAAe,CAGf,OAAQ,CAFR,KAAM,CAWN,2BAA6B,CAP7B,YAQF,CAEA,4BACE,SACF,CAGA,uBAME,eAAgB,CAChB,mBACF,CAEA,6CALE,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAmBF,CAXA,sBAUE,sCAAuC,CAJvC,oGAEqE,CACrE,yBAEF,CAEA,oBACE,GAAK,sBAA4B,CACjC,GAAO,8BAAkC,CAC3C,CAEA,2BAUE,gDAAiD,CAJjD,qLAGgF,CAJhF,QAAS,CAFT,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KASF,CAEA,yBACE,MAAW,UAAY,CAAE,kBAAqB,CAC9C,IAAM,UAAY,CAAE,oBAAuB,CAC7C,CAGA,sBAIE,SAAU,CAHV,iBAAkB,CAElB,oCAAsC,CAEtC,0CAAiD,CAHjD,SAIF,CAEA,+BAEE,SAAU,CADV,gCAEF,CAGA,iBAaE,0CAA2C,CAX3C,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAkC,CAElC,sBAAwC,CACxC,kBAAmB,CAKnB,2EAGwC,CALxC,cAAe,CAFf,YAAa,CAGb,iBAAkB,CAFlB,WAQF,CAEA,oBACE,MAAW,2EAG+B,CAC1C,IAAM,uEAGqC,CAC7C,CAGA,mBAIE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,WAA+B,CAE/B,cAAe,CADf,cAAe,CAEf,WAAY,CARZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAST,uBAAyB,CACzB,UACF,CAEA,yBACE,oBAAkC,CAClC,aAAc,CACd,oBACF,CAGA,mBAEE,kBAAmB,CADnB,iBAEF,CAEA,WAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,sBAAuB,CAEvB,kBACF,CAEA,gBAEE,2CAA4C,CAD5C,cAEF,CAEA,qBACE,MAAW,kBAAqB,CAChC,IAAM,oBAAuB,CAC/B,CAEA,gBAME,6BAAoC,CAFpC,4CAAqD,CACrD,4BAA6B,CAE7B,oBAAqB,CANrB,cAAe,CACf,eAAgB,CAMhB,kBAAmB,CALnB,QAMF,CAEA,eACE,WAA+B,CAC/B,cAAe,CAEf,kBAAmB,CADnB,QAEF,CAGA,oBASE,kBAAmB,CAEnB,oCAAsC,CAVtC,oBAAkC,CAClC,0BAAwC,CACxC,kBAAmB,CAGnB,aAAc,CAEd,YAAa,CADb,cAAe,CAGf,OAAQ,CALR,kBAAmB,CADnB,iBAQF,CAEA,sBACE,MAAW,uBAA0B,CACrC,IAAM,0BAA6B,CACnC,IAAM,yBAA4B,CACpC,CAEA,iBACE,cACF,CAGA,iBACE,kBACF,CAEA,sBAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,iBAIE,WAA6B,CAD7B,cAAe,CADf,SAAU,CADV,iBAAkB,CAIlB,SACF,CAEA,YAUE,kCAA2B,CAA3B,0BAA2B,CAN3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CACnB,UAAc,CACd,cAAe,CANf,WAAY,CACZ,cAAsB,CAMtB,uBAAyB,CARzB,UAUF,CAEA,kBAGE,oBAAqC,CADrC,sBAAoC,CAEpC,yBAA2C,CAH3C,YAIF,CAEA,yBACE,WACF,CAEA,sBAGE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,WAA6B,CAE7B,cAAe,CADf,cAAe,CAEf,WAAY,CAPZ,iBAAkB,CAClB,UAAW,CAQX,uBAAyB,CACzB,SACF,CAEA,4BACE,oBAAkC,CAClC,UAAc,CACd,oBACF,CAGA,qBACE,kBACF,CAEA,qBAEE,kBAAmB,CAInB,WAA+B,CAF/B,cAAe,CAHf,YAAa,CAIb,cAAe,CAFf,QAIF,CAEA,eACE,YACF,CAEA,sBAKE,oBAAqC,CAFrC,0BAAwC,CACxC,iBAAkB,CAFlB,WAAY,CAIZ,iBAAkB,CAClB,uBAAyB,CANzB,UAOF,CAEA,6CACE,+CAAqD,CACrD,iBACF,CAEA,mDAME,UAAc,CALd,WAAY,CAMZ,cAAe,CACf,eAAiB,CAJjB,QAAS,CAFT,iBAAkB,CAClB,OAAQ,CAER,8BAIF,CAGA,mBAYE,kBAAmB,CATnB,+CAA6D,CAC7D,WAAY,CACZ,kBAAmB,CAYnB,+BAA6C,CAX7C,UAAc,CAGd,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAMhB,QAAS,CAZT,WAAY,CAWZ,sBAAuB,CAKvB,kBAAmB,CAFnB,eAAgB,CADhB,iBAAkB,CALlB,uBAAyB,CATzB,UAkBF,CAEA,0BAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,gCACE,SACF,CAEA,yBAEE,2BAA6C,CAD7C,0BAEF,CAEA,0BACE,uBACF,CAEA,2BACE,kBAAmB,CACnB,UACF,CAEA,sBAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,qBAA6B,CAF7B,WAAY,CADZ,UAMF,CAQA,kBAEE,kBAAmB,CADnB,iBAEF,CAEA,sBACE,eAAgB,CAChB,WAAY,CAOZ,iBAAkB,CANlB,WAA6B,CAE7B,cAAe,CADf,cAAe,CAIf,gBAAiB,CAFjB,oBAAqB,CACrB,uBAGF,CAEA,4BACE,oBAAkC,CAClC,UAAc,CACd,8BACF,CAGA,iBACE,8BAA4C,CAE5C,YAAa,CACb,qBAAsB,CACtB,OAAQ,CAHR,gBAIF,CAEA,eAEE,kBAAmB,CAGnB,eAA+B,CAJ/B,YAAa,CAGb,cAAe,CADf,OAGF,CAEA,eACE,cACF,CAGA,yBACE,iBAIE,kBAAmB,CADnB,WAAY,CAFZ,iBAAkB,CAClB,UAGF,CAEA,gBACE,cACF,CAOA,+BAEE,cAAe,CADf,WAEF,CACF,CCtbA,gBAKE,cAAe,CAFf,SAAU,CAFV,cAAe,CACf,QAAS,CAET,YAEF,CAGA,sBAQE,kBAAmB,CAJnB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAoC,CAEpC,sBAA0C,CAC1C,iBAAkB,CAKlB,eAA+B,CAC/B,cAAe,CALf,YAAa,CAGb,cAAe,CARf,WAAY,CAOZ,sBAAuB,CAIvB,uBAAyB,CAZzB,UAaF,CAEA,4BACE,gBAAoC,CACpC,sBAAoC,CACpC,UAAc,CACd,oBACF,CAGA,uBAOE,kBAAmB,CAJnB,+CAA6D,CAC7D,0BAAwC,CACxC,iBAAkB,CAMlB,UAAc,CACd,cAAe,CANf,YAAa,CAGb,cAAe,CACf,eAAgB,CARhB,WAAY,CAMZ,sBAAuB,CAMvB,iBAAkB,CADlB,uBAAyB,CAZzB,UAcF,CAEA,6BAEE,2BAA6C,CAD7C,oBAEF,CAEA,6BAOE,eAAmB,CACnB,qBAAyB,CACzB,iBAAkB,CANlB,WAAY,CAFZ,UAAW,CAKX,WAAY,CAJZ,iBAAkB,CAElB,UAAW,CACX,UAKF,CAGA,oBAYE,4BAA8B,CAN9B,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAkC,CAElC,sBAAwC,CACxC,kBAAmB,CACnB,mDAEiC,CARjC,MAAO,CAFP,iBAAkB,CAClB,QAAS,CAET,WAAY,CASZ,YACF,CAEA,qBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,sBAEE,kBAAmB,CAEnB,iCAAiD,CAHjD,YAAa,CAEb,YAEF,CAEA,sBAOE,kBAAmB,CAJnB,+CAA6D,CAC7D,0BAAwC,CACxC,iBAAkB,CAMlB,UAAc,CALd,YAAa,CAGb,cAAe,CACf,eAAgB,CARhB,WAAY,CAMZ,sBAAuB,CAIvB,iBAAkB,CAXlB,UAYF,CAEA,oBACE,QACF,CAEA,oBAGE,UAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,oBAEE,WAA6B,CAD7B,cAAe,CAGf,mBAAqB,CADrB,wBAEF,CAEA,oBACE,cACF,CAEA,oBAGE,kBAAmB,CAEnB,eAAgB,CAChB,WAAY,CACZ,WAA+B,CAE/B,cAAe,CAPf,YAAa,CAMb,cAAe,CAJf,iBAAkB,CAOlB,eAAgB,CADhB,uBAAyB,CATzB,UAWF,CAEA,0BACE,oBAAkC,CAClC,UACF,CAEA,iCACE,oBAAkC,CAClC,aACF,CAEA,oBAEE,cAAe,CADf,iBAAkB,CAGlB,iBAAkB,CADlB,UAEF,CAEA,uBAEE,oBAAoC,CADpC,UAAW,CAEX,YACF,CAGA,mBAgBE,oBAAqB,CANrB,kBAAmB,CAEnB,sCAAwC,CACxC,kCAA2B,CAA3B,0BAA2B,CAC3B,0BAA0C,CAT1C,kBAAmB,CACnB,UAAc,CAGd,YAAa,CAFb,eAAgB,CAIhB,QAAS,CAIT,eAAgB,CAXhB,iBAAkB,CAHlB,cAAe,CAEf,UAAW,CADX,QAAS,CAMT,aASF,CAEA,2BACE,oBAAkC,CAClC,+BACF,CAEA,yBACE,oBAAkC,CAClC,+BACF,CAEA,+BACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBAEE,aAAc,CADd,cAEF,CAEA,wBACE,cAAe,CACf,eACF,CAGA,yBACE,gBAEE,SAAU,CADV,QAEF,CAEA,oBAEE,eAAgB,CADhB,wBAEF,CAEA,mBAEE,SAAU,CACV,cAAe,CAFf,UAGF,CACF,CCrPA,iBAEE,kDAA6D,CAC7D,UAAc,CAFd,gBAAiB,CAKjB,iBAAkB,CAFlB,YAAa,CACb,iBAEF,CAGA,wBAOE,qLAGiF,CAJjF,QAAS,CALT,UAAW,CAGX,MAAO,CAOP,mBAAoB,CATpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,SACF,CAEA,mBACE,iBAAkB,CAClB,SACF,CAGA,aAGE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA0C,CAC1C,kBAAmB,CAEnB,wDAEwC,CARxC,kBAAmB,CAKnB,iBAIF,CAEA,gBAKE,WAAY,CAHZ,6BAA8B,CAE9B,kBAAmB,CAEnB,cAAe,CACf,iBACF,CAEA,cAGE,QACF,CAEA,YAIE,uCAAwC,CAFxC,UAAc,CADd,cAAe,CAEf,8BAEF,CAEA,iBACE,MAAW,kBAAqB,CAChC,IAAM,qBAAwB,CAChC,CAEA,iBAOE,6BAAoC,CAFpC,4CAAqD,CACrD,4BAA6B,CAE7B,oBAAqB,CAPrB,cAAe,CACf,eAAgB,CAEhB,kBAKF,CAEA,YAOE,kCAA2B,CAA3B,0BAA2B,CAJ3B,oBAAkC,CAGlC,sBAAwC,CADxC,kBAAmB,CAHnB,WAA+B,CAD/B,cAAe,CAGf,gBAIF,CAGA,gBAGE,kBAAmB,CACnB,WACF,CAEA,eAcE,kCAA2B,CAA3B,0BAA2B,CAP3B,kBAAmB,CAEnB,eAAgB,CALhB,WAAY,CASZ,eAAgB,CARhB,cAAe,CAOf,iBAAkB,CADlB,0CAIF,CAEA,sBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,4BACE,SACF,CAEA,oBACE,+CAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,sBACE,kDAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,sBACE,kDAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,qBAEE,+BAAyC,CADzC,0BAEF,CAGA,iBAGE,kBAAmB,CAFnB,YAAa,CAGb,cAAe,CAFf,QAAS,CAGT,WAAY,CACZ,iBAAkB,CAClB,UACF,CAEA,YAEE,QAAO,CAEP,WAAY,CADZ,eAAgB,CAFhB,iBAIF,CAEA,aAKE,WAA6B,CAC7B,cAAe,CAJf,SAAU,CADV,iBAAkB,CAElB,OAAQ,CACR,0BAGF,CAEA,cAUE,kCAA2B,CAA3B,0BAA2B,CAN3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CACnB,UAAc,CACd,cAAe,CANf,WAAY,CACZ,qBAAsB,CAMtB,uBAAyB,CARzB,UAUF,CAEA,oBAGE,oBAAqC,CADrC,sBAAoC,CAEpC,yBAA2C,CAH3C,YAIF,CAEA,2BACE,WACF,CAEA,iBAIE,kBAAmB,CAHnB,YAAa,CACb,QAAS,CACT,WAEF,CAEA,eAUE,kCAA2B,CAA3B,0BAA2B,CAP3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CACnB,UAAc,CAEd,cAAe,CADf,cAAe,CANf,WAAY,CAUZ,eAAgB,CAThB,cAAe,CAOf,uBAGF,CAEA,qBAGE,oBAAqC,CADrC,sBAAoC,CADpC,YAGF,CAEA,sBACE,kBAAmB,CACnB,UACF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,eACF,CAGA,WAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA0C,CAC1C,kBAAmB,CAKnB,oDAEwC,CAHxC,eAAgB,CAHhB,YAAa,CAEb,iBAAkB,CADlB,0CAMF,CAEA,kBAOE,sDAA6D,CAN7D,UAAW,CAKX,UAAW,CAFX,MAAO,CAIP,SAAU,CANV,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAMN,2BACF,CAEA,iBAEE,sBAAoC,CACpC,uEAGwC,CALxC,0BAMF,CAEA,wBACE,SACF,CAEA,oBAEE,sBAAoC,CADpC,UAEF,CAGA,aAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,6BAA8B,CAE9B,kBACF,CAMA,kCAHE,iBAiBF,CAdA,aAME,kBAAmB,CAFnB,+CAAqD,CADrD,iBAAkB,CASlB,yBAA2C,CAJ3C,UAAc,CAHd,YAAa,CAIb,cAAe,CACf,eAAgB,CARhB,WAAY,CAKZ,sBAAuB,CAIvB,0BAA4C,CAV5C,UAaF,CAEA,kBAEE,WAAY,CADZ,iBAAkB,CAElB,SACF,CAEA,YAKE,6CAA8C,CAD9C,0BAAuC,CADvC,iBAAkB,CADlB,WAAY,CADZ,UAKF,CAEA,mBACE,kBAAmB,CACnB,6BACF,CAEA,qBACE,kBAAmB,CACnB,6BACF,CAEA,uBACE,MAAW,UAAc,CACzB,IAAM,SAAY,CACpB,CAEA,WAKE,eACF,CAEA,6BANE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OAYF,CARA,kBAOE,kBAAmB,CADnB,sBAAuB,CADvB,iBAGF,CAEA,UAIE,UAAc,CAHd,cAAe,CACf,eAIF,CAEA,sBAHE,eAAgB,CAFhB,QAcF,CATA,YAEE,kBAAmB,CAGnB,eAA+B,CAJ/B,YAAa,CAKb,cAAe,CAFf,OAAQ,CADR,sBAMF,CAEA,YACE,WACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAGb,aAAc,CADd,wBAEF,CAEA,YAQE,kCAA2B,CAA3B,0BAA2B,CAC3B,sBAA0C,CAP1C,kBAAmB,CACnB,cAAe,CAIf,mBAAqB,CANrB,gBAAiB,CAKjB,wBAIF,CAGA,cACE,kBACF,CAEA,WAGE,aAAS,CAAT,QAAS,CADT,6BAEF,CAGA,wBAIE,0BAA2C,CAD3C,kBAAmB,CAFnB,YAAa,CAKb,QAAS,CADT,YAGF,CAEA,mCAHE,kBAAmB,CALnB,oBAgBF,CARA,WAOE,0BAA2C,CAD3C,kBAAmB,CAHnB,QAAS,CACT,YAIF,CAEA,iCAGE,kBAAmB,CAInB,oBAAqC,CAErC,0BAA2C,CAD3C,iBAAkB,CANlB,YAAa,CAGb,QAAO,CADP,QAAS,CAET,gBAAiB,CAIjB,uBACF,CAEA,6CAEE,oBAAqC,CACrC,sBACF,CAEA,WACE,WAA6B,CAC7B,cAAe,CAEf,iBAAkB,CADlB,UAEF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,YAEE,eAA+B,CAD/B,cAAe,CAGf,mBAAqB,CADrB,wBAEF,CAEA,YAEE,WAA+B,CAD/B,cAAe,CAEf,eACF,CAGA,cACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,YAAa,CACb,OAEF,CAEA,oCAHE,0BAKF,CAEA,wBACE,sBACF,CAEA,qBACE,wBACF,CAEA,YAEE,kBAAmB,CAWnB,kCAA2B,CAA3B,0BAA2B,CAR3B,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CARf,YAAa,CAMb,cAAe,CACf,eAAgB,CALhB,OAAQ,CAYR,sBAAuB,CADvB,cAAe,CAFf,eAAgB,CARhB,iBAAkB,CAOlB,iBAAkB,CADlB,uBAMF,CAEA,mBAOE,mDAAsF,CANtF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,KAAM,CAKN,wBAA0B,CAH1B,UAIF,CAEA,yBACE,SACF,CAEA,iBACE,oBAAkC,CAElC,0BAAwC,CADxC,UAEF,CAEA,iBACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,mBACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,kBACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,wBACE,oBAAkC,CAElC,0BAAwC,CADxC,UAEF,CAEA,mBACE,oBAAkC,CAElC,0BAAwC,CADxC,aAEF,CAEA,kBAEE,2BAAyC,CADzC,0BAEF,CAEA,qBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAEA,iBACE,cAAe,CACf,eACF,CAGA,mBAGE,kBAAmB,CAGnB,WAA+B,CAL/B,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,iBAEF,CAEA,iBAME,iCAAkC,CAFlC,0BAA6B,CAC7B,iBAAkB,CADlB,qBAA6B,CAF7B,WAAY,CAKZ,kBAAmB,CANnB,UAOF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,aAGE,kBAAmB,CAGnB,WAA+B,CAL/B,YAAa,CACb,qBAAsB,CAKtB,gBAAmB,CAHnB,sBAAuB,CACvB,iBAGF,CAEA,YAEE,eAA6B,CAD7B,cAAe,CAEf,kBACF,CAGA,eAUE,kBAAmB,CAEnB,yBAA2B,CAL3B,kCAA2B,CAA3B,0BAA2B,CAD3B,gBAA8B,CAD9B,QAAS,CAIT,YAAa,CAEb,sBAAuB,CARvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAMN,YAKF,CAOA,eAUE,0BAA4B,CAR5B,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAkC,CAElC,sBAAwC,CACxC,kBAAmB,CAOnB,wDAEwC,CALxC,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,YAAa,CAEb,UAOF,CAEA,mBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,cAGE,kBAAmB,CAGnB,iCAA+C,CAL/C,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,iBACE,UAAc,CACd,cAAe,CACf,eAAgB,CAChB,QAAS,CACT,8BACF,CAEA,cACE,eAAgB,CAChB,WAAY,CAKZ,iBAAkB,CAJlB,WAA+B,CAE/B,cAAe,CADf,cAAe,CAEf,WAAY,CAEZ,uBACF,CAEA,oBACE,oBAAkC,CAClC,aAAc,CACd,oBACF,CAQA,cACE,cACF,CAEA,UACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,iBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAWA,yCAPE,WAA+B,CAC/B,cAAe,CAGf,eAUF,CAPA,uBACE,aAAc,CAGd,eAAgB,CAChB,iBAEF,CAEA,yBAWE,kCAA2B,CAA3B,0BAA2B,CAN3B,oBAAqC,CACrC,0BAA0C,CAC1C,kBAAmB,CACnB,UAAc,CACd,cAAe,CANf,WAAY,CACZ,cAAe,CAMf,uBAAyB,CARzB,UAUF,CAGA,yBACE,oBAAkC,CAClC,0BAAwC,CACxC,UACF,CAEA,+BACE,oBAAkC,CAClC,sBAAoC,CACpC,6BACF,CAEA,gCACE,oBAAkC,CAClC,UACF,CAEA,qCAIE,oBAAqC,CADrC,sBAAoC,CAEpC,yBAA2C,CAH3C,YAIF,CAEA,yBACE,WACF,CAEA,gBAEE,kBAAmB,CAInB,WAA+B,CAF/B,cAAe,CAHf,YAAa,CAIb,cAAe,CAFf,QAIF,CAEA,qCAGE,iBAAqB,CADrB,WAAY,CADZ,UAGF,CAEA,eAME,8BAA4C,CAL5C,YAAa,CACb,QAAS,CACT,wBAAyB,CACzB,eAAgB,CAChB,gBAEF,CAEA,8BAIE,WAAY,CACZ,kBAAmB,CAGnB,cAAe,CAFf,cAAe,CACf,eAAgB,CALhB,WAAY,CACZ,cAAe,CAMf,uBACF,CAEA,eACE,oBAAoC,CAEpC,sBAA0C,CAD1C,WAEF,CAEA,eACE,+CAA6D,CAE7D,+BAA6C,CAD7C,UAEF,CAEA,0CAEE,0BACF,CAEA,wBAEE,kBAAmB,CADnB,UAAY,CAEZ,cACF,CAGA,cAUE,kBAAmB,CAEnB,+BAAiC,CACjC,kCAA2B,CAA3B,0BAA2B,CAC3B,0BAA0C,CAT1C,kBAAmB,CACnB,UAAc,CAGd,YAAa,CAFb,eAAgB,CAIhB,QAAS,CAPT,iBAAkB,CAHlB,cAAe,CAEf,UAAW,CADX,QAAS,CAMT,YAOF,CAEA,sBACE,oBAAkC,CAClC,+BACF,CAEA,oBACE,oBAAkC,CAClC,+BACF,CAEA,wBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,yBACE,iBACE,YACF,CAEA,YAEE,QAAS,CADT,yBAEF,CAEA,gBAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,gBAEE,cAAe,CADf,sBAEF,CAEA,iBAEE,mBAAoB,CADpB,qBAEF,CAEA,YACE,cACF,CAEA,iBACE,sBACF,CAEA,cACE,OACF,CAEA,cACE,cAAe,CACf,sBACF,CAEA,YACE,cAAe,CACf,gBACF,CAGA,aAEE,kBAAmB,CADnB,qBAAsB,CAGtB,QAAS,CADT,iBAEF,CAEA,kBACE,OACF,CAEA,sBAEE,sBAAuB,CADvB,OAEF,CAEA,qBACE,OACF,CAGA,wBACE,qBAAsB,CACtB,QAAS,CACT,YACF,CAEA,iCAGE,sBAAuB,CADvB,UAEF,CAGA,eACE,cAAe,CAEf,YAAa,CADb,UAEF,CAEA,UACE,qBAAsB,CACtB,QACF,CAEA,iBACE,UACF,CAEA,eACE,qBAAsB,CACtB,QACF,CAEA,8BAEE,UACF,CACF,CCn/BA,mBAEE,kDAA6D,CAC7D,aAAc,CAFd,gBAAiB,CAGjB,YACF,CAGA,aAME,iCAAiD,CAJjD,6BAA8B,CAE9B,kBAAmB,CACnB,cAEF,CAEA,6BANE,kBAAmB,CAFnB,YAYF,CAJA,gBAGE,QACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAOA,6BAHE,aAAc,CADd,cAUF,CANA,iBAGE,eAAgB,CAChB,QAAS,CACT,+BACF,CAEA,gBACE,YAAa,CACb,QACF,CAEA,eAEE,kBAAmB,CAGnB,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CARf,YAAa,CAMb,cAAe,CACf,eAAgB,CALhB,OAAQ,CACR,iBAAkB,CAOlB,oBAAqB,CADrB,uBAEF,CAEA,oBACE,oBAAoC,CAEpC,sBAA0C,CAD1C,aAAc,CAEd,iBACF,CAEA,0BACE,gBAAoC,CACpC,0BACF,CAGA,kBACE,YAAa,CACb,QAAS,CAET,aAAc,CADd,gBAEF,CAGA,kBAEE,oBAAqC,CAIrC,0BAA0C,CAH1C,kBAAmB,CAEnB,0BAAmB,CAAnB,kBAAmB,CADnB,YAAa,CAHb,WAMF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,UAEE,kBAAmB,CAInB,gBAAuB,CADvB,WAAY,CAGZ,iBAAkB,CADlB,aAAc,CAEd,cAAe,CARf,YAAa,CAUb,cAAe,CACf,eAAgB,CAThB,QAAS,CACT,iBAAkB,CASlB,eAAgB,CAHhB,uBAAyB,CAIzB,UACF,CAEA,gBACE,oBAAmC,CACnC,aAAc,CACd,yBACF,CAEA,iBACE,kDAA6D,CAE7D,+BAA8C,CAD9C,UAEF,CAEA,UACE,cAAe,CAEf,iBAAkB,CADlB,UAEF,CAGA,eAEE,oBAAqC,CAGrC,0BAA0C,CAF1C,kBAAmB,CAFnB,QAAO,CAGP,YAEF,CAEA,cACE,6BACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,YAGE,iCAAiD,CAFjD,kBAAmB,CACnB,mBAAoB,CAEpB,iBACF,CAEA,eACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,cACF,CAEA,cACE,aAAc,CACd,cAAe,CACf,QACF,CAGA,eACE,eACF,CAEA,cAGE,gBAA8B,CAE9B,0BAA0C,CAD1C,kBAAmB,CAHnB,kBAAmB,CACnB,YAIF,CAEA,iBAME,iCAAgD,CALhD,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,eAAkB,CAClB,mBAEF,CAEA,YACE,kBACF,CAEA,kBAGE,aAAc,CAFd,aAAc,CAId,cAAe,CADf,eAAgB,CAFhB,iBAIF,CAEA,qCAME,oBAAoC,CAFpC,sBAA0C,CAC1C,iBAAkB,CAElB,aAAc,CACd,cAAe,CALf,iBAAkB,CAMlB,uBAAyB,CAPzB,UAQF,CAEA,iDAKE,oBAAqC,CAFrC,oBAAqB,CACrB,8BAA6C,CAF7C,YAIF,CAEA,+BACE,aACF,CAGA,WAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,iBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAEhB,mBAAqB,CADrB,wBAEF,CAEA,YAIE,oBAA8B,CAE9B,0BAA0C,CAD1C,iBAAkB,CAJlB,aAAc,CACd,cAAe,CACf,gBAIF,CAEA,YACE,kDAA6D,CAC7D,UAGF,CAEA,0BAJE,eAAgB,CAChB,iBAMF,CAEA,qBACE,oBAAkC,CAElC,sBAAoC,CADpC,aAEF,CAEA,uBACE,oBAAkC,CAElC,sBAAoC,CADpC,aAEF,CAGA,iBAGE,kBAAmB,CAEnB,gBAA8B,CAE9B,0BAA0C,CAD1C,iBAAkB,CALlB,YAAa,CAQb,qBAAsB,CAEtB,QAAS,CATT,sBAAuB,CAMvB,kBAAmB,CAJnB,YAAa,CAMb,iBAEF,CAEA,iBACE,QAAO,CACP,iBACF,CAEA,oBACE,aAAc,CACd,cAAe,CACf,eAAgB,CAChB,cACF,CAEA,mBACE,aAAc,CACd,cAAe,CACf,QACF,CAEA,oBACE,aAAc,CACd,iBACF,CAEA,aAIE,oBAAoC,CAFpC,sBAA0C,CAC1C,iBAAkB,CAElB,aAAc,CACd,cAAe,CACf,eAAgB,CANhB,gBAOF,CAGA,oBACE,4BAA8B,CAC9B,oBAAyB,CAEzB,wBAA0B,CAD1B,0BAEF,CAGA,mBAIE,oBAAmC,CAFnC,oBAAqB,CACrB,8BAA6C,CAF7C,YAIF,CAGA,aACE,2BAA4B,CAE5B,mBACF,CAGA,QAEE,oBAAqB,CAErB,WAAY,CAHZ,iBAAkB,CAElB,UAEF,CAEA,cAGE,QAAS,CAFT,SAAU,CACV,OAEF,CAEA,QAOE,sBAA0C,CAE1C,kBAAmB,CAHnB,QAAS,CAJT,cAAe,CAEf,MAAO,CACP,OAAQ,CAFR,KAOF,CAEA,uBAXE,iBAAkB,CAOlB,cAcF,CAVA,eAOE,qBAAuB,CAEvB,iBAAkB,CAHlB,UAAW,CAJX,UAAW,CACX,WAAY,CAEZ,QAAS,CADT,UAMF,CAEA,sBACE,wBACF,CAEA,6BACE,0BACF,CAGA,cAME,8BAA8C,CAL9C,YAAa,CACb,QAAS,CACT,wBAAyB,CACzB,eAAgB,CAChB,gBAEF,CAEA,KASE,kBAAmB,CAPnB,WAAY,CACZ,iBAAkB,CAGlB,cAAe,CAEf,YAAa,CAJb,cAAe,CACf,eAAgB,CAKhB,OAAQ,CATR,iBAAkB,CAMlB,uBAIF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,aACE,kDAA6D,CAE7D,+BAA8C,CAD9C,UAEF,CAEA,kCAEE,+BAA8C,CAD9C,0BAEF,CAGA,OAEE,kBAAmB,CAGnB,iBAAkB,CAJlB,YAAa,CAMb,cAAe,CACf,eAAgB,CALhB,QAAS,CAGT,kBAAmB,CAFnB,iBAKF,CAEA,aACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,eACE,oBAAkC,CAClC,0BAAwC,CACxC,aACF,CAEA,YACE,cACF,CAGA,yBACE,kBACE,qBAAsB,CACtB,QACF,CAEA,kBACE,UACF,CAEA,aACE,kBAAmB,CAEnB,QAAS,CADT,eAEF,CAEA,UAEE,eAAgB,CADhB,kBAEF,CAEA,WACE,yBACF,CAEA,iBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,oBACE,aAAc,CACd,UACF,CAEA,cACE,sBACF,CACF,CAEA,yBACE,mBACE,YACF,CAEA,eACE,YACF,CAEA,cACE,YACF,CACF,CC5gBA,qBACE,gFAAqF,CACrF,UACF,CASA,qDACE,kDAA6D,CAC7D,UACF,CAOA,mEACE,oBAA+B,CAC/B,0BACF,CAEA,+BACE,gBAAoC,CACpC,0BACF,CAEA,kCACE,gBAAoC,CACpC,0BACF,CAEA,2BACE,UACF,CAEA,iCACE,oBAAmC,CACnC,aACF,CAEA,kCACE,kDAA6D,CAC7D,UACF,CAEA,qGAGE,oBAAoC,CAEpC,sBAAoC,CADpC,UAEF,CAEA,qCACE,yBAA8B,CAC9B,oBACF,CAEA,6BACE,gBAAoC,CAEpC,0BAAoC,CADpC,UAEF,CAEA,gCACE,aACF,CAEA,+BACE,UACF,CAEA,qCACE,UACF,CAEA,oCACE,UACF,CAEA,kCACE,aACF,CAGA,mCACE,gBACE,gFAAqF,CACrF,UACF,CAEA,oCACE,4BAA8B,CAC9B,oBACF,CACF,CAEA,oCAME,mDACE,kDAA6D,CAC7D,UACF,CAOA,iEACE,oBAA+B,CAC/B,0BACF,CAEA,8BACE,gBAAoC,CACpC,0BACF,CAEA,iCACE,gBAAoC,CACpC,0BACF,CAEA,0BACE,UACF,CAEA,gCACE,oBAAmC,CACnC,aACF,CAEA,iCACE,kDAA6D,CAC7D,UACF,CAEA,kGAGE,oBAAoC,CAEpC,sBAAoC,CADpC,UAEF,CAEA,oCACE,yBAA8B,CAC9B,oBACF,CAEA,4BACE,gBAAoC,CAEpC,0BAAoC,CADpC,UAEF,CAEA,+BACE,aACF,CAEA,8BACE,UACF,CAEA,oCACE,UACF,CAEA,mCACE,UACF,CAEA,iCACE,aACF,CACF,CAGA,KACE,6CACF,CAEA,2JAUE,mEACF", "sources": ["styles/index.css", "styles/App.css", "模块一/styles/HomePage.css", "components/FileSelector.css", "模块一/styles/WorkTarget.css", "模块一/components/SelectiveDownloadModal.css", "auth/styles/PermissionGuard.css", "styles/WorkTracking.css", "模块二/components/WorkTrackingDownloadModal.css", "模块三/styles/WorldClass.css", "模块三/components/WorldClassDownloadModal.css", "模块四/styles/MonthlyKPI.css", "模块四/components/KPISelectiveDownloadModal.css", "模块五/styles/ProjectOne.css", "模块五/styles/ProjectOneDownloadModal.css", "模块六/styles/ModuleSix.css", "模块六/styles/KPITable.css", "模块六/styles/ExportModal.css", "模块六/styles/DataVisualization.css", "auth/styles/Login.css", "auth/styles/LoginManager.css", "auth/styles/UserManagement.css", "auth/styles/PersonalSettings.css", "styles/themes.css"], "sourcesContent": ["/* 全局重置 */\r\n* {\r\n  margin: 0;\r\n  padding: 0;\r\n  box-sizing: border-box;\r\n}\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: '<PERSON><PERSON><PERSON>', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\r\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  min-height: 100vh;\r\n  overflow-x: hidden;\r\n}\r\n\r\ncode {\r\n  font-family: 'Orbitron', 'Courier New', monospace;\r\n}\r\n\r\n/* 滚动条样式 */\r\n::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n  border-radius: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n}\r\n\r\n/* 通用动画 */\r\n@keyframes fadeIn {\r\n  from { opacity: 0; transform: translateY(20px); }\r\n  to { opacity: 1; transform: translateY(0); }\r\n}\r\n\r\n@keyframes slideIn {\r\n  from { opacity: 0; transform: translateX(-20px); }\r\n  to { opacity: 1; transform: translateX(0); }\r\n}\r\n\r\n@keyframes glow {\r\n  0%, 100% { box-shadow: 0 0 20px rgba(32, 255, 77, 0.3); }\r\n  50% { box-shadow: 0 0 30px rgba(32, 255, 77, 0.6); }\r\n} ", "/* App主容器 */\n.App {\n  text-align: center;\n  min-height: 100vh;\n  position: relative;\n}\n\n/* 加载界面 */\n.loading-screen {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(ellipse at center, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  text-align: center;\n  animation: fadeIn 1s ease-out;\n}\n\n.loading-logo .logo-text {\n  font-family: 'Orbitron', monospace;\n  font-size: 3rem;\n  font-weight: 900;\n  color: #20ff4d;\n  text-shadow: \n    0 0 10px rgba(32, 255, 77, 0.8),\n    0 0 20px rgba(32, 255, 77, 0.6),\n    0 0 30px rgba(32, 255, 77, 0.4);\n  margin-bottom: 0.5rem;\n  letter-spacing: 3px;\n}\n\n.loading-logo .logo-subtitle {\n  font-family: '<PERSON><PERSON>ni', sans-serif;\n  font-size: 1.2rem;\n  color: #00d4aa;\n  letter-spacing: 8px;\n  margin-bottom: 3rem;\n  opacity: 0.8;\n}\n\n.loading-progress {\n  width: 300px;\n  height: 4px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 2px;\n  overflow: hidden;\n  margin: 0 auto 2rem;\n  position: relative;\n}\n\n.progress-bar {\n  height: 100%;\n  background: linear-gradient(90deg, #20ff4d, #00d4aa, #20ff4d);\n  background-size: 200% 100%;\n  animation: progressBar 2s ease-in-out infinite;\n  border-radius: 2px;\n}\n\n@keyframes progressBar {\n  0% {\n    width: 0%;\n    background-position: 200% 0;\n  }\n  50% {\n    width: 70%;\n    background-position: 100% 0;\n  }\n  100% {\n    width: 100%;\n    background-position: 0% 0;\n  }\n}\n\n.loading-status {\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1.1rem;\n  color: #ffffff;\n  opacity: 0.7;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .loading-logo .logo-text {\n    font-size: 2rem;\n    letter-spacing: 2px;\n  }\n  \n  .loading-logo .logo-subtitle {\n    font-size: 1rem;\n    letter-spacing: 4px;\n  }\n  \n  .loading-progress {\n    width: 250px;\n  }\n}\n\n@media (max-width: 480px) {\n  .loading-logo .logo-text {\n    font-size: 1.5rem;\n    letter-spacing: 1px;\n  }\n  \n  .loading-logo .logo-subtitle {\n    font-size: 0.9rem;\n    letter-spacing: 2px;\n  }\n  \n  .loading-progress {\n    width: 200px;\n  }\n} ", "/* 首页主容器 */\r\n.homepage {\r\n  min-height: 100vh;\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 背景动画 */\r\n.background-animation {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.particle {\r\n  position: absolute;\r\n  width: 4px;\r\n  height: 4px;\r\n  background: #20ff4d;\r\n  border-radius: 50%;\r\n  animation: float 20s infinite linear;\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.particle:nth-child(1) {\r\n  top: 20%;\r\n  left: 10%;\r\n  animation-delay: 0s;\r\n  animation-duration: 25s;\r\n}\r\n\r\n.particle:nth-child(2) {\r\n  top: 60%;\r\n  left: 80%;\r\n  animation-delay: -5s;\r\n  animation-duration: 30s;\r\n}\r\n\r\n.particle:nth-child(3) {\r\n  top: 80%;\r\n  left: 30%;\r\n  animation-delay: -10s;\r\n  animation-duration: 35s;\r\n}\r\n\r\n@keyframes float {\r\n  0% { transform: translateY(0px) translateX(0px); opacity: 0; }\r\n  10% { opacity: 1; }\r\n  90% { opacity: 1; }\r\n  100% { transform: translateY(-100vh) translateX(50px); opacity: 0; }\r\n}\r\n\r\n.grid-lines {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-image: \r\n    linear-gradient(rgba(32, 255, 77, 0.03) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(32, 255, 77, 0.03) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  animation: gridMove 20s linear infinite;\r\n}\r\n\r\n@keyframes gridMove {\r\n  0% { transform: translate(0, 0); }\r\n  100% { transform: translate(50px, 50px); }\r\n}\r\n\r\n/* 顶部时间显示 */\r\n.time-display {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  z-index: 10;\r\n  text-align: right;\r\n}\r\n\r\n.current-time {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.2rem;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\r\n}\r\n\r\n.system-status {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: #00d4aa;\r\n  margin-top: 5px;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 文件选择器按钮 */\r\n.file-selector-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #1a1a2e;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  margin-top: 10px;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.file-selector-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.file-selector-btn.disabled {\r\n  background: linear-gradient(45deg, #666, #888);\r\n  color: #ccc;\r\n  cursor: not-allowed;\r\n  opacity: 0.6;\r\n}\r\n\r\n.file-selector-btn.disabled:hover {\r\n  transform: none;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n/* 主标题区域 */\r\n.main-header {\r\n  text-align: center;\r\n  padding: 80px 20px 60px;\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n\r\n.title-container {\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.homepage .main-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 3.5rem;\r\n  font-weight: 900;\r\n  color: #ffffff;\r\n  text-shadow:\r\n    0 0 20px rgba(32, 255, 77, 0.6),\r\n    0 0 40px rgba(32, 255, 77, 0.3);\r\n  margin-bottom: 1rem;\r\n  letter-spacing: 4px;\r\n  animation: fadeIn 1s ease-out;\r\n}\r\n\r\n.sub-title {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.8rem;\r\n  font-weight: 600;\r\n  color: #00d4aa;\r\n  margin-bottom: 2rem;\r\n  letter-spacing: 2px;\r\n  animation: fadeIn 1s ease-out 0.3s both;\r\n}\r\n\r\n.title-divider {\r\n  width: 200px;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, transparent, #20ff4d, transparent);\r\n  margin: 2rem auto;\r\n  animation: fadeIn 1s ease-out 0.6s both;\r\n}\r\n\r\n.system-description {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  letter-spacing: 1px;\r\n  animation: fadeIn 1s ease-out 0.9s both;\r\n}\r\n\r\n/* 导航网格 */\r\n.navigation-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\r\n  gap: 30px;\r\n  padding: 0 40px 40px;\r\n  max-width: 1400px;\r\n  margin: 0 auto;\r\n  z-index: 1;\r\n  position: relative;\r\n  flex: 1;\r\n}\r\n\r\n.nav-card {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(32, 255, 77, 0.2);\r\n  border-radius: 15px;\r\n  padding: 30px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  animation: slideIn 0.6s ease-out var(--delay) both;\r\n}\r\n\r\n.nav-card:hover {\r\n  transform: translateY(-10px);\r\n  border-color: var(--color);\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.3),\r\n    0 0 30px rgba(32, 255, 77, 0.2);\r\n}\r\n\r\n.nav-card:hover .card-glow {\r\n  opacity: 1;\r\n}\r\n\r\n.card-glow {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: radial-gradient(circle at center, var(--color), transparent 70%);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n  pointer-events: none;\r\n  mix-blend-mode: screen;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-icon {\r\n  font-size: 2.5rem;\r\n  filter: grayscale(100%);\r\n  transition: filter 0.3s ease;\r\n}\r\n\r\n.nav-card:hover .card-icon {\r\n  filter: grayscale(0%);\r\n}\r\n\r\n.card-number {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: var(--color);\r\n  opacity: 0.6;\r\n}\r\n\r\n.card-content {\r\n  text-align: left;\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.card-title {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.4rem;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  margin-bottom: 8px;\r\n  line-height: 1.3;\r\n}\r\n\r\n.card-subtitle {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  color: var(--color);\r\n  margin-bottom: 12px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  opacity: 0.8;\r\n}\r\n\r\n.card-description {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.95rem;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  line-height: 1.5;\r\n}\r\n\r\n.card-footer {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  opacity: 0;\r\n  transform: translateY(10px);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-card:hover .card-footer {\r\n  opacity: 1;\r\n  transform: translateY(0);\r\n}\r\n\r\n.enter-text {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  color: var(--color);\r\n  font-weight: 600;\r\n}\r\n\r\n.arrow {\r\n  font-size: 1.2rem;\r\n  color: var(--color);\r\n  animation: arrowPulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes arrowPulse {\r\n  0%, 100% { transform: translateX(0); }\r\n  50% { transform: translateX(5px); }\r\n}\r\n\r\n/* 底部状态栏 */\r\n.status-bar {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 40px;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-top: 1px solid rgba(32, 255, 77, 0.2);\r\n  z-index: 1;\r\n  position: relative;\r\n}\r\n\r\n.status-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-label {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.status-value {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 0.9rem;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-value.online {\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .navigation-grid {\r\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\r\n    gap: 25px;\r\n    padding: 0 30px 30px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .main-title {\r\n    font-size: 2.5rem;\r\n    letter-spacing: 2px;\r\n  }\r\n  \r\n  .sub-title {\r\n    font-size: 1.4rem;\r\n  }\r\n  \r\n  .navigation-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n    padding: 0 20px 20px;\r\n  }\r\n  \r\n  .nav-card {\r\n    padding: 25px;\r\n  }\r\n  \r\n  .status-bar {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    text-align: center;\r\n  }\r\n  \r\n  .time-display {\r\n    position: static;\r\n    text-align: center;\r\n    padding: 20px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .main-title {\r\n    font-size: 2rem;\r\n    letter-spacing: 1px;\r\n  }\r\n  \r\n  .sub-title {\r\n    font-size: 1.2rem;\r\n  }\r\n  \r\n  .main-header {\r\n    padding: 60px 15px 40px;\r\n  }\r\n  \r\n  .navigation-grid {\r\n    padding: 0 15px 15px;\r\n  }\r\n  \r\n  .nav-card {\r\n    padding: 20px;\r\n  }\r\n} ", "/* FileSelector.css - 文件选择器组件样式 */\r\n\r\n.file-selector-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.7);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.file-selector-modal {\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 15px;\r\n  padding: 0;\r\n  width: 90%;\r\n  max-width: 500px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\r\n  animation: modalSlideIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-50px) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n.file-selector-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  background: rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.file-selector-header h3 {\r\n  margin: 0;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.3rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ffffff;\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  padding: 5px;\r\n  border-radius: 50%;\r\n  width: 35px;\r\n  height: 35px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.file-selector-content {\r\n  padding: 25px;\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n.current-file-info {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.current-file-info p {\r\n  margin: 0;\r\n  color: #00d4aa;\r\n  font-size: 0.95rem;\r\n}\r\n\r\n.file-upload-area {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.upload-zone {\r\n  border: 2px dashed rgba(255, 255, 255, 0.3);\r\n  border-radius: 12px;\r\n  padding: 40px 20px;\r\n  text-align: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  background: rgba(255, 255, 255, 0.02);\r\n}\r\n\r\n.upload-zone:hover {\r\n  border-color: #00d4aa;\r\n  background: rgba(0, 212, 170, 0.05);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.upload-icon {\r\n  font-size: 3rem;\r\n  margin-bottom: 15px;\r\n  opacity: 0.7;\r\n}\r\n\r\n.upload-zone p {\r\n  margin: 5px 0;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n}\r\n\r\n.upload-hint {\r\n  font-size: 0.85rem;\r\n  color: rgba(255, 255, 255, 0.6) !important;\r\n  margin-top: 10px !important;\r\n}\r\n\r\n.selected-file-info {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  border-radius: 8px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.selected-file-info p {\r\n  margin: 5px 0;\r\n  color: #20ff4d;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.upload-status {\r\n  padding: 12px 15px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.upload-status.success {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  color: #20ff4d;\r\n}\r\n\r\n.upload-status.error {\r\n  background: rgba(255, 87, 87, 0.1);\r\n  border: 1px solid rgba(255, 87, 87, 0.3);\r\n  color: #ff5757;\r\n}\r\n\r\n.upload-status.info {\r\n  background: rgba(255, 193, 7, 0.1);\r\n  border: 1px solid rgba(255, 193, 7, 0.3);\r\n  color: #ffc107;\r\n}\r\n\r\n.file-selector-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  padding: 20px 25px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n  background: rgba(255, 255, 255, 0.02);\r\n}\r\n\r\n.cancel-btn, .upload-btn {\r\n  flex: 1;\r\n  padding: 12px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cancel-btn {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: #ffffff;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.cancel-btn:hover:not(:disabled) {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-1px);\r\n}\r\n\r\n.upload-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #1a1a2e;\r\n}\r\n\r\n.upload-btn:hover:not(:disabled) {\r\n  transform: translateY(-1px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.cancel-btn:disabled, .upload-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none !important;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.file-selector-content::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 212, 170, 0.5);\r\n  border-radius: 3px;\r\n}\r\n\r\n.file-selector-content::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(0, 212, 170, 0.7);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .file-selector-modal {\r\n    width: 95%;\r\n    margin: 20px;\r\n  }\r\n  \r\n  .file-selector-header {\r\n    padding: 15px 20px;\r\n  }\r\n  \r\n  .file-selector-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .file-selector-actions {\r\n    padding: 15px 20px;\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .upload-zone {\r\n    padding: 30px 15px;\r\n  }\r\n  \r\n  .upload-icon {\r\n    font-size: 2.5rem;\r\n  }\r\n} ", "/* 工作目标管理责任书页面样式 - 优化版 */\n.work-target {\n  min-height: 100vh;\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n}\n\n/* 页面头部 */\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20px;\n  position: relative;\n  padding: 10px 15px;\n  background: rgba(0, 0, 0, 0.4);\n  border-bottom: 2px solid rgba(32, 255, 77, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.back-button {\n  padding: 8px 16px;\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\n  border: none;\n  border-radius: 6px;\n  color: #000;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.back-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(32, 255, 77, 0.3);\n}\n\n/* 返回首页按钮样式 */\n.back-btn-top {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: none;\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  order: -1;\n  margin-left: 20px; /* 右移20px */\n}\n\n.back-btn-top:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n/* 头部中心区域样式 */\n.header-center {\n  text-align: center;\n  flex: 1;\n  margin-right: 100px; /* 为右侧同步状态留出空间 */\n  padding-right: 20px; /* 额外的右侧内边距 */\n}\n\n.work-target .page-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 2.2rem;\n  font-weight: 700;\n  color: #00d4aa;\n  text-align: center;\n  margin: 0;\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.page-subtitle {\n  font-size: 1rem;\n  color: #20ff4d;\n  margin: 5px 0 0 0;\n  opacity: 0.8;\n  text-align: center;\n}\n\n/* 模块一独立的已同步图标样式 */\n.page-header .sync-status {\n  position: absolute;\n  top: calc(50% + 10px); /* 上移5px：从15px改为10px */\n  right: 15px;\n  transform: translateY(-50%);\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  font-size: 18px;\n  z-index: 10;\n  height: 40px; /* 与返回首页按钮高度一致 */\n}\n\n.work-target .status-dot {\n  width: 6px;\n  height: 6px;\n  background: #20ff4d;\n  border-radius: 50%;\n  animation: pulse 2s ease-in-out infinite;\n}\n\n/* 模块一状态指示器样式 - 绿色系主题 */\n.page-header .status-indicator {\n  padding: 5px 12px;\n  border-radius: 6px;\n  font-size: 20px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  white-space: nowrap;\n  height: 40px; /* 与返回首页按钮高度一致 */\n  display: flex;\n  align-items: center;\n}\n\n.page-header .status-indicator.success {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d) !important; /* 与返回首页按钮相同的渐变背景 */\n  color: #000 !important; /* 黑色文字，与返回首页按钮一致 */\n  border: 1px solid rgba(0, 212, 170, 0.6) !important;\n  box-shadow: 0 0 8px rgba(0, 212, 170, 0.4) !important;\n}\n\n.page-header .status-indicator.error {\n  background: rgba(255, 77, 77, 0.2) !important;\n  color: #ff4d4d !important;\n  border: 1px solid rgba(255, 77, 77, 0.4) !important;\n}\n\n.page-header .status-indicator.pending {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d) !important;\n  color: #000 !important;\n  border: 1px solid rgba(0, 212, 170, 0.6) !important;\n  box-shadow: 0 0 8px rgba(0, 212, 170, 0.4) !important;\n}\n\n/* 数据概览 */\n.data-overview {\n  display: flex;\n  justify-content: center;\n  gap: 30px;\n  padding: 15px 30px;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.overview-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 5px;\n}\n\n.overview-number {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.8rem;\n  font-weight: 700;\n  color: #20ff4d;\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.5);\n}\n\n.overview-label {\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n/* 主要内容区域 */\n.content-area {\n  padding: 20px 30px 30px;\n  max-width: 1600px;\n  margin: 0 auto;\n}\n\n/* 三个大标题区域 */\n.section-tabs {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 15px;\n  margin-bottom: 25px;\n}\n\n.section-tab {\n  background: rgba(255, 255, 255, 0.05);\n  border: 2px solid rgba(255, 255, 255, 0.1);\n  border-radius: 10px;\n  padding: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.section-tab:hover,\n.section-tab.active {\n  transform: translateY(-3px);\n  border-color: var(--section-color);\n  background: rgba(255, 255, 255, 0.08);\n  box-shadow: \n    0 8px 25px rgba(0, 0, 0, 0.3),\n    0 0 20px var(--section-color);\n}\n\n.tab-icon {\n  font-size: 2rem;\n  filter: grayscale(100%);\n  transition: filter 0.3s ease;\n}\n\n.section-tab:hover .tab-icon,\n.section-tab.active .tab-icon {\n  filter: grayscale(0%);\n}\n\n.tab-content {\n  flex: 1;\n  text-align: left;\n}\n\n.tab-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.1rem;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 5px;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.tab-count {\n  font-size: 0.8rem;\n  color: var(--section-color);\n  opacity: 0.8;\n}\n\n.tab-arrow {\n  font-size: 1rem;\n  color: var(--section-color);\n  transition: transform 0.3s ease;\n}\n\n/* 表格区域 */\n.table-section {\n  background: rgba(255, 255, 255, 0.03);\n  border-radius: 10px;\n  padding: 20px;\n  border: 1px solid rgba(32, 255, 77, 0.2);\n  animation: fadeIn 0.5s ease-out;\n}\n\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.table-header h3 {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.2rem;\n  color: #20ff4d;\n  margin: 0;\n  text-align: center;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.table-actions {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.edit-hint {\n  font-size: 0.8rem;\n  color: #00d4aa;\n  opacity: 0.8;\n}\n\n.refresh-btn {\n  padding: 6px 12px;\n  background: rgba(32, 255, 77, 0.2);\n  border: 1px solid #20ff4d;\n  border-radius: 4px;\n  color: #20ff4d;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.refresh-btn:hover {\n  background: rgba(32, 255, 77, 0.3);\n}\n\n/* 表格概要信息 */\n.table-summary {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 15px;\n  padding: 10px 15px;\n  background: rgba(32, 255, 77, 0.1);\n  border-radius: 6px;\n  border: 1px solid rgba(32, 255, 77, 0.3);\n  font-size: 0.9rem;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.table-summary span {\n  color: #20ff4d;\n  font-weight: 500;\n}\n\n/* 重点工作说明内联样式 */\n.key-work-notice-inline {\n  color: #ffa500 !important;\n  font-weight: 600 !important;\n  font-family: 'Orbitron', monospace !important;\n  background: rgba(255, 165, 0, 0.15);\n  padding: 4px 12px;\n  border-radius: 4px;\n  border: 1px solid rgba(255, 165, 0, 0.4);\n  font-size: 0.85rem !important;\n}\n\n/* 数据表格 - 优化列宽 */\n.data-table-container {\n  overflow-x: auto;\n  border-radius: 6px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.data-table {\n  width: 100%;\n  border-collapse: collapse;\n  background: rgba(0, 0, 0, 0.2);\n  font-size: 0.9rem; /* 增大字体提升可读性 */\n  table-layout: fixed;\n  line-height: 1.5; /* 增加行高 */\n}\n\n/* 优化的列宽设置 */\n.data-table .col-number { width: 5%; }\n.data-table .col-indicator { width: 15%; }\n.data-table .col-target { width: 30%; }\n.data-table .col-weight { width: 8%; }\n.data-table .col-standard { width: 22%; }\n.data-table .col-category { width: 10%; }\n.data-table .col-responsible { width: 10%; }\n\n.data-table th {\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.4), rgba(32, 255, 77, 0.3)); /* 增强背景对比度 */\n  color: #ffffff;\n  padding: 12px 10px; /* 增加内边距 */\n  text-align: center;\n  font-family: 'Orbitron', monospace;\n  font-size: 0.85rem; /* 稍微增大表头字体 */\n  font-weight: 700; /* 增强字体粗细 */\n  border-bottom: 2px solid #20ff4d;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  vertical-align: middle;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); /* 添加文字阴影增强可读性 */\n}\n\n.data-row {\n  transition: all 0.3s ease;\n}\n\n.data-row:hover {\n  background: rgba(32, 255, 77, 0.12); /* 增强悬停效果 */\n  transform: translateY(-1px); /* 轻微上移效果 */\n  box-shadow: 0 2px 8px rgba(32, 255, 77, 0.2); /* 添加阴影 */\n}\n\n.data-row:nth-child(even) {\n  background: rgba(255, 255, 255, 0.04); /* 增强斑马纹对比度 */\n}\n\n/* 正确的合并单元格样式及内容居中 */\n\n.merged-row {\n  background: rgba(32, 255, 77, 0.03) !important;\n  /* border-left: 3px solid rgba(32, 255, 77, 0.3); // 可以移除或调整 */\n}\n\n.merged-row:hover {\n  background: rgba(32, 255, 77, 0.1) !important;\n}\n\n.data-cell.merged-cell-content { /* 应用于具有rowSpan的<td> */\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.15), rgba(32, 255, 77, 0.1));\n  text-align: center;     /* 水平居中 */\n  vertical-align: middle; /* 垂直居中 */\n  font-weight: 600;\n  border-right: 2px solid rgba(32, 255, 77, 0.4);\n  position: relative;\n}\n\n.data-cell.merged-cell-content::after { /* 强调合并单元格的左边框 */\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: linear-gradient(to bottom, #20ff4d, #00d4aa);\n}\n\n.data-cell {\n  padding: 10px 8px; /* 增加内边距提升可读性 */\n  border-bottom: 1px solid rgba(255, 255, 255, 0.15); /* 增强边框对比度 */\n  border-right: 1px solid rgba(255, 255, 255, 0.08);\n  transition: all 0.2s ease;\n  text-align: center;        /* 添加：水平居中 */\n  vertical-align: middle;    /* 修改：从top改为middle，垂直居中 */\n  word-wrap: break-word;\n  color: #ffffff; /* 确保文字颜色 */\n  line-height: 1.6; /* 增加行高 */\n  /* white-space: pre-wrap; // 如果使用<br/>则不再严格需要，但保留无害 */\n}\n\n.data-cell.editable {\n  cursor: pointer;\n}\n\n.data-cell.editable:hover {\n  background: rgba(32, 255, 77, 0.18); /* 增强悬停背景 */\n  border-color: rgba(32, 255, 77, 0.3); /* 添加边框高亮 */\n  box-shadow: inset 0 0 8px rgba(32, 255, 77, 0.2); /* 内阴影效果 */\n}\n\n.cell-content {\n  display: block; /* 使span占据整个单元格，方便点击和对齐 */\n  width: 100%;\n  color: #ffffff;\n  line-height: 1.6; /* 增加行高使多行文本更舒适 */\n  word-wrap: break-word;\n  overflow-wrap: break-word;\n  white-space: normal; /* 允许自动换行，并通过<br/>强制换行 */\n  text-align: center;        /* 添加：确保内容居中显示 */\n  font-weight: 500; /* 稍微增加字体粗细 */\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 添加文字阴影增强可读性 */\n}\n\n.editable-cell {\n  border-radius: 3px;\n  padding: 4px 6px;\n  transition: all 0.2s ease;\n  border: 1px solid transparent;\n}\n\n.editable-cell:hover {\n  background: rgba(32, 255, 77, 0.1);\n  border-color: rgba(32, 255, 77, 0.3);\n}\n\n.cell-input { /* 修改为textarea */\n  width: 100%;\n  box-sizing: border-box; /* 确保padding和border不增加总宽度 */\n  background: rgba(0, 0, 0, 0.8);\n  border: 2px solid #20ff4d;\n  border-radius: 3px;\n  padding: 6px 8px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.85rem;\n  outline: none;\n  resize: vertical; /* 允许用户调整高度 */\n  min-height: 30px; /* 最小高度 */\n  line-height: 1.4;\n  text-align: center;        /* 添加：编辑时也居中显示 */\n}\n\n.cell-input:focus {\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.5);\n  background: rgba(10, 10, 20, 0.9); /* 深一点的背景色 */\n}\n\n/* 欢迎消息 */\n.welcome-message {\n  text-align: center;\n  padding: 40px 20px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.welcome-message h3 {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.3rem;\n  color: #20ff4d;\n  margin-bottom: 10px;\n}\n\n.welcome-message p {\n  font-size: 1rem;\n  margin-bottom: 20px;\n  opacity: 0.8;\n}\n\n/* 无数据状态 */\n.no-data {\n  text-align: center;\n  padding: 30px;\n  color: rgba(255, 255, 255, 0.6);\n}\n\n/* 加载状态 */\n.work-target-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 100vh;\n  color: #20ff4d;\n}\n\n.loading-spinner {\n  width: 30px;\n  height: 30px;\n  border: 2px solid rgba(32, 255, 77, 0.3);\n  border-top: 2px solid #20ff4d;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 15px;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .section-tabs {\n    grid-template-columns: 1fr;\n  }\n  \n  .content-area {\n    padding: 15px 20px 25px;\n  }\n  \n  /* 调整表格列宽 */\n  .data-table .col-target { width: 200px; }\n  .data-table .col-standard { width: 180px; }\n}\n\n@media (max-width: 768px) {\n  .data-overview {\n    flex-wrap: wrap;\n    gap: 15px;\n  }\n  \n  .page-header {\n    flex-direction: column;\n    gap: 10px;\n    margin-bottom: 20px;\n    padding: 10px 15px;\n  }\n  \n  .page-title {\n    font-size: 1.2rem;\n    margin: 0;\n  }\n  \n  .table-header {\n    flex-direction: column;\n    gap: 10px;\n    align-items: flex-start;\n  }\n  \n  .data-table {\n    font-size: 0.8rem; /* 稍微增大移动端字体 */\n  }\n\n  .data-table th,\n  .data-table td {\n    padding: 8px 6px; /* 增加移动端内边距 */\n  }\n  \n  /* 移动端列宽调整 */\n  .data-table .col-target { width: 150px; }\n  .data-table .col-standard { width: 120px; }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from { \n    opacity: 0; \n    transform: translateY(15px); \n  }\n  to { \n    opacity: 1; \n    transform: translateY(0); \n  }\n}\n\n@keyframes pulse {\n  0%, 100% { opacity: 0.6; }\n  50% { opacity: 1; }\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 新材级重点工作突出显示样式 - 使用与提示文字相同的橙色 #ffa500 */\n.highlight-work {\n  background: rgba(255, 165, 0, 0.15) !important; /* #ffa500 的半透明版本，与提示文字颜色一致 */\n  border-left: 4px solid #ffa500 !important;\n}\n\n.highlight-work:hover {\n  background: rgba(255, 165, 0, 0.25) !important; /* 悬停时稍微加深 */\n}\n\n.highlight-cell {\n  background: rgba(255, 165, 0, 0.2);\n  font-weight: 600;\n  color: #ffffff; /* 白色文字确保在橙色背景上可读 */\n  box-shadow: inset 0 0 0 1px rgba(255, 165, 0, 0.4);\n}\n\n.highlight-merged-cell {\n  background: linear-gradient(135deg, rgba(255, 165, 0, 0.3), rgba(255, 165, 0, 0.15)) !important;\n  border-right: 2px solid rgba(255, 165, 0, 0.6) !important;\n}\n\n.highlight-merged-cell::after {\n  background: linear-gradient(to bottom, #ffa500, #ff8c00) !important; /* 橙色渐变 */\n}\n\n/* 下载按钮样式 */\n.download-btn {\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\n  border: none;\n  color: #000;\n  padding: 8px 16px;\n  border-radius: 6px;\n  cursor: pointer;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.download-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(77, 208, 255, 0.4);\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\n}\n\n.download-btn:active {\n  transform: translateY(0);\n} ", "/* 选择性下载模态框样式 */\n.selective-download-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.selective-download-modal {\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\n  border: 2px solid rgba(32, 255, 77, 0.3);\n  border-radius: 15px;\n  box-shadow: \n    0 20px 40px rgba(0, 0, 0, 0.6),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  width: 90%;\n  max-width: 900px;\n  max-height: 80vh;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n/* 模态框头部 */\n.modal-header {\n  background: linear-gradient(90deg, rgba(32, 255, 77, 0.1), rgba(77, 255, 200, 0.1));\n  padding: 20px;\n  border-bottom: 1px solid rgba(32, 255, 77, 0.2);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-header h2 {\n  color: #20ff4d;\n  font-size: 1.5rem;\n  margin: 0;\n  text-shadow: 0 0 10px rgba(32, 255, 77, 0.6);\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #ff4757;\n  font-size: 1.8rem;\n  cursor: pointer;\n  padding: 5px 10px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(255, 71, 87, 0.2);\n  transform: scale(1.1);\n}\n\n/* 模态框内容 */\n.modal-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: 20px;\n}\n\n/* 分类部分 */\n.category-section {\n  margin-bottom: 25px;\n  border: 1px solid rgba(32, 255, 77, 0.2);\n  border-radius: 10px;\n  overflow: hidden;\n  background: rgba(15, 15, 35, 0.5);\n}\n\n.category-header {\n  background: linear-gradient(90deg, rgba(32, 255, 77, 0.15), rgba(77, 255, 200, 0.15));\n  padding: 15px 20px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid rgba(32, 255, 77, 0.2);\n}\n\n.category-checkbox {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  font-size: 1.1rem;\n  font-weight: 600;\n}\n\n.category-checkbox input[type=\"checkbox\"] {\n  margin-right: 12px;\n  width: 18px;\n  height: 18px;\n  accent-color: #20ff4d;\n  cursor: pointer;\n}\n\n.category-title {\n  color: #20ff4d;\n  text-shadow: 0 0 5px rgba(32, 255, 77, 0.6);\n}\n\n.category-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.action-btn {\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.2), rgba(77, 255, 200, 0.2));\n  border: 1px solid rgba(32, 255, 77, 0.3);\n  color: #20ff4d;\n  padding: 6px 12px;\n  border-radius: 5px;\n  cursor: pointer;\n  font-size: 0.9rem;\n  transition: all 0.3s ease;\n}\n\n.action-btn:hover {\n  background: linear-gradient(135deg, rgba(32, 255, 77, 0.3), rgba(77, 255, 200, 0.3));\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(32, 255, 77, 0.3);\n}\n\n/* 项目列表 */\n.items-list {\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.item-row {\n  padding: 12px 20px;\n  border-bottom: 1px solid rgba(32, 255, 77, 0.1);\n  transition: all 0.3s ease;\n}\n\n.item-row:hover {\n  background: rgba(32, 255, 77, 0.05);\n}\n\n.item-row.highlight-item {\n  background: rgba(255, 165, 0, 0.1);\n  border-left: 3px solid #ffa500;\n}\n\n.item-checkbox {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  width: 100%;\n}\n\n.item-checkbox input[type=\"checkbox\"] {\n  margin-right: 12px;\n  width: 16px;\n  height: 16px;\n  accent-color: #20ff4d;\n  cursor: pointer;\n}\n\n.item-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  flex-wrap: wrap;\n  gap: 10px;\n}\n\n.item-name {\n  color: #e8e8e8;\n  font-size: 0.95rem;\n  flex: 1;\n}\n\n.item-weight {\n  color: #4dd0ff;\n  font-size: 0.85rem;\n  background: rgba(77, 208, 255, 0.1);\n  padding: 2px 8px;\n  border-radius: 12px;\n  border: 1px solid rgba(77, 208, 255, 0.3);\n}\n\n.highlight-badge {\n  background: linear-gradient(135deg, #ffa500, #ff8c00);\n  color: white;\n  font-size: 0.75rem;\n  padding: 3px 8px;\n  border-radius: 12px;\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n}\n\n/* 模态框底部 */\n.modal-footer {\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\n  padding: 20px;\n  border-top: 1px solid rgba(32, 255, 77, 0.2);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 15px;\n}\n\n.statistics {\n  display: flex;\n  gap: 20px;\n  color: #4dd0ff;\n  font-weight: 600;\n}\n\n.statistics span {\n  background: rgba(77, 208, 255, 0.1);\n  padding: 8px 15px;\n  border-radius: 20px;\n  border: 1px solid rgba(77, 208, 255, 0.3);\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\n}\n\n.format-selection {\n  color: #e8e8e8;\n}\n\n.format-selection label {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  font-weight: 600;\n}\n\n.format-selection select {\n  background: rgba(15, 15, 35, 0.8);\n  border: 1px solid rgba(32, 255, 77, 0.3);\n  color: #20ff4d;\n  padding: 8px 12px;\n  border-radius: 5px;\n  font-size: 0.9rem;\n  cursor: pointer;\n}\n\n.format-selection select:focus {\n  outline: none;\n  border-color: #20ff4d;\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.3);\n}\n\n.action-buttons {\n  display: flex;\n  gap: 15px;\n}\n\n.cancel-btn, .download-btn {\n  padding: 12px 25px;\n  border: none;\n  border-radius: 8px;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n}\n\n.cancel-btn {\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\n  color: #ff4757;\n  border: 1px solid rgba(255, 71, 87, 0.5);\n}\n\n.cancel-btn:hover {\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\n}\n\n.download-btn {\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\n  color: #000;\n  border: 1px solid transparent;\n}\n\n.download-btn:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 20px rgba(32, 255, 77, 0.4);\n}\n\n.download-btn:disabled {\n  background: rgba(128, 128, 128, 0.3);\n  color: rgba(255, 255, 255, 0.5);\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n/* 滚动条样式 */\n.modal-content::-webkit-scrollbar,\n.items-list::-webkit-scrollbar {\n  width: 8px;\n}\n\n.modal-content::-webkit-scrollbar-track,\n.items-list::-webkit-scrollbar-track {\n  background: rgba(15, 15, 35, 0.5);\n  border-radius: 4px;\n}\n\n.modal-content::-webkit-scrollbar-thumb,\n.items-list::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\n  border-radius: 4px;\n}\n\n.modal-content::-webkit-scrollbar-thumb:hover,\n.items-list::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .selective-download-modal {\n    width: 95%;\n    max-height: 90vh;\n  }\n  \n  .modal-header {\n    padding: 15px;\n  }\n  \n  .modal-header h2 {\n    font-size: 1.3rem;\n  }\n  \n  .modal-content {\n    padding: 15px;\n  }\n  \n  .category-header {\n    flex-direction: column;\n    gap: 10px;\n    align-items: flex-start;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n    align-items: stretch;\n  }\n  \n  .statistics {\n    justify-content: center;\n  }\n  \n  .format-selection {\n    text-align: center;\n  }\n  \n  .action-buttons {\n    justify-content: center;\n  }\n} ", "/* 权限守卫组件样式 */\n\n/* 权限不足提示 */\n.permission-denied-wrapper {\n  display: inline-block;\n  position: relative;\n}\n\n.permission-denied-content {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: rgba(255, 69, 58, 0.1);\n  border: 1px solid rgba(255, 69, 58, 0.3);\n  border-radius: 8px;\n  color: #ff453a;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.permission-denied-icon {\n  font-size: 14px;\n}\n\n.permission-denied-text {\n  white-space: nowrap;\n}\n\n/* 需要登录的页面 */\n.auth-required-page {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  color: #ffffff;\n  position: relative;\n  overflow: hidden;\n}\n\n.auth-required-page::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: \n    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.auth-required-content {\n  text-align: center;\n  max-width: 500px;\n  padding: 40px;\n  background: rgba(255, 255, 255, 0.03);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 20px;\n  box-shadow: \n    0 20px 40px rgba(0, 0, 0, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  position: relative;\n  z-index: 1;\n}\n\n.auth-required-icon {\n  font-size: 80px;\n  margin-bottom: 20px;\n  animation: iconFloat 3s ease-in-out infinite;\n}\n\n@keyframes iconFloat {\n  0%, 100% { transform: translateY(0); }\n  50% { transform: translateY(-10px); }\n}\n\n.auth-required-title {\n  font-size: 32px;\n  font-weight: 300;\n  margin: 0 0 15px 0;\n  background: linear-gradient(135deg, #ffffff, #00ffff);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.auth-required-message {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.7);\n  margin: 0 0 20px 0;\n  line-height: 1.5;\n}\n\n.auth-required-hint {\n  font-size: 14px;\n  color: rgba(0, 255, 255, 0.8);\n  background: rgba(0, 255, 255, 0.1);\n  padding: 12px 20px;\n  border-radius: 10px;\n  border: 1px solid rgba(0, 255, 255, 0.2);\n}\n\n/* 页面访问被拒绝 */\n.page-access-denied {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  color: #ffffff;\n  position: relative;\n  overflow: hidden;\n}\n\n.page-access-denied::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: \n    radial-gradient(circle at 20% 80%, rgba(255, 69, 58, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(255, 149, 0, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.access-denied-content {\n  text-align: center;\n  max-width: 600px;\n  padding: 40px;\n  background: rgba(255, 255, 255, 0.03);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 69, 58, 0.2);\n  border-radius: 20px;\n  box-shadow: \n    0 20px 40px rgba(0, 0, 0, 0.3),\n    0 0 30px rgba(255, 69, 58, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  position: relative;\n  z-index: 1;\n}\n\n.access-denied-icon {\n  font-size: 80px;\n  margin-bottom: 20px;\n  animation: iconShake 2s ease-in-out infinite;\n}\n\n@keyframes iconShake {\n  0%, 100% { transform: rotate(0deg); }\n  25% { transform: rotate(-5deg); }\n  75% { transform: rotate(5deg); }\n}\n\n.access-denied-title {\n  font-size: 32px;\n  font-weight: 300;\n  margin: 0 0 15px 0;\n  color: #ff453a;\n  text-shadow: 0 0 20px rgba(255, 69, 58, 0.3);\n}\n\n.access-denied-message {\n  font-size: 18px;\n  color: rgba(255, 255, 255, 0.7);\n  margin: 0 0 25px 0;\n  line-height: 1.5;\n}\n\n.access-denied-details {\n  background: rgba(255, 69, 58, 0.1);\n  border: 1px solid rgba(255, 69, 58, 0.2);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 25px;\n  text-align: left;\n}\n\n.current-role,\n.required-permissions {\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.current-role {\n  color: rgba(255, 149, 0, 0.9);\n  font-weight: 600;\n}\n\n.required-permissions {\n  color: rgba(255, 255, 255, 0.6);\n}\n\n.access-denied-actions {\n  display: flex;\n  justify-content: center;\n  gap: 15px;\n}\n\n.back-button {\n  padding: 12px 24px;\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\n  border: none;\n  border-radius: 10px;\n  color: #ffffff;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);\n}\n\n.back-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(0, 255, 255, 0.4);\n}\n\n/* 禁用按钮样式 */\n.permission-disabled {\n  opacity: 0.5 !important;\n  cursor: not-allowed !important;\n  pointer-events: auto !important;\n}\n\n.permission-disabled:hover {\n  transform: none !important;\n  box-shadow: none !important;\n}\n\n/* 过滤导航 */\n.filtered-navigation {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.nav-item {\n  padding: 10px 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 8px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  color: #ffffff;\n  transition: all 0.3s ease;\n}\n\n.nav-item:hover {\n  background: rgba(255, 255, 255, 0.1);\n  border-color: rgba(0, 255, 255, 0.3);\n}\n\n/* 权限信息显示 */\n.permission-info {\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  padding: 15px;\n  margin: 10px 0;\n}\n\n.permission-role {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 10px;\n}\n\n.permission-label {\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.6);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.permission-value {\n  font-size: 14px;\n  color: rgba(0, 255, 255, 0.9);\n  font-weight: 600;\n}\n\n.permission-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.permission-items {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 5px;\n}\n\n.permission-item {\n  font-size: 11px;\n  background: rgba(0, 255, 255, 0.1);\n  color: rgba(0, 255, 255, 0.8);\n  padding: 4px 8px;\n  border-radius: 6px;\n  border: 1px solid rgba(0, 255, 255, 0.2);\n}\n\n/* 动画效果 */\n@keyframes slideInRight {\n  from {\n    opacity: 0;\n    transform: translateX(100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n/* 认证加载状态 */\n.auth-loading-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\n  color: #ffffff;\n  position: relative;\n  overflow: hidden;\n}\n\n.auth-loading-container::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background:\n    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.1) 0%, transparent 50%);\n  pointer-events: none;\n}\n\n.auth-loading-content {\n  text-align: center;\n  padding: 40px;\n  background: rgba(255, 255, 255, 0.03);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 20px;\n  box-shadow:\n    0 20px 40px rgba(0, 0, 0, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  position: relative;\n  z-index: 1;\n}\n\n.auth-loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(0, 255, 255, 0.3);\n  border-top: 3px solid #00ffff;\n  border-radius: 50%;\n  animation: authSpin 1s linear infinite;\n  margin: 0 auto 20px;\n}\n\n@keyframes authSpin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.auth-loading-text {\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.8);\n  font-weight: 500;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .auth-required-content,\n  .access-denied-content,\n  .auth-loading-content {\n    padding: 30px 20px;\n    margin: 20px;\n  }\n\n  .auth-required-title,\n  .access-denied-title {\n    font-size: 24px;\n  }\n\n  .auth-required-message,\n  .access-denied-message {\n    font-size: 16px;\n  }\n\n  .auth-required-icon,\n  .access-denied-icon {\n    font-size: 60px;\n  }\n\n  .auth-loading-spinner {\n    width: 40px;\n    height: 40px;\n  }\n\n  .auth-loading-text {\n    font-size: 14px;\n  }\n}\n", "/* WorkTracking.css - 重点工作跟踪页面样式 */\r\n\r\n.work-tracking-container {\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  padding: 15px;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n/* 页面头部 - 优化布局 */\r\n.page-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding: 10px 0;\r\n}\r\n\r\n.header-center {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.back-btn-top {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 10px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  order: -1;\r\n}\r\n\r\n.back-btn-top:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.page-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 2.2rem;\r\n  color: #00d4aa;\r\n  margin-bottom: 8px;\r\n  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.page-subtitle {\r\n  font-size: 1rem;\r\n  color: #20ff4d;\r\n  margin-bottom: 0;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 全局sync-status和status-indicator样式已移除，现在使用模块特定的样式 */\r\n\r\n/* 控制面板 */\r\n.control-panel {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 25px;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 30px;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-group label {\r\n  color: #00d4aa;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n\r\n.type-selector {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  padding: 8px 15px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  min-width: 150px;\r\n}\r\n\r\n.type-selector:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 月份导航 */\r\n.month-navigation {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(0, 212, 170, 0.1);\r\n  padding: 10px 20px;\r\n  border-radius: 25px;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 新月份导航样式 */\r\n.month-navigation-new {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: rgba(0, 212, 170, 0.08);\r\n  padding: 12px 20px;\r\n  border-radius: 25px;\r\n  border: 1px solid rgba(0, 212, 170, 0.4);\r\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\r\n}\r\n\r\n.nav-btn-new {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 18px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: none;\r\n  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.nav-btn-new:hover:not(:disabled) {\r\n  transform: scale(1.05) translateY(-1px);\r\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n}\r\n\r\n.nav-btn-new:disabled {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.3);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.current-months-new {\r\n  color: #00d4aa;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n  min-width: 100px;\r\n  text-align: center;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\r\n  font-family: 'Orbitron', monospace;\r\n}\r\n\r\n.refresh-btn-new {\r\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 12px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(77, 208, 255, 0.2);\r\n  white-space: nowrap;\r\n}\r\n\r\n.refresh-btn-new:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(77, 208, 255, 0.4);\r\n  background: linear-gradient(45deg, #00d4aa, #4dd0ff);\r\n}\r\n\r\n.nav-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 20px;\r\n  padding: 8px 15px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.nav-btn:hover:not(:disabled) {\r\n  transform: scale(1.05);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.nav-btn:disabled {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.3);\r\n  cursor: not-allowed;\r\n}\r\n\r\n.current-months {\r\n  color: #00d4aa;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n  min-width: 120px;\r\n  text-align: center;\r\n}\r\n\r\n/* 操作按钮 */\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.refresh-btn, .back-btn {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn {\r\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\r\n  color: #000;\r\n}\r\n\r\n.back-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n}\r\n\r\n.refresh-btn:hover, .back-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 统一控制面板 V2 - 优化美观布局 */\r\n.unified-control-panel-v2 {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 30px;\r\n  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n/* 统一控制面板 - 美观布局 (保留兼容) */\r\n.unified-control-panel {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  backdrop-filter: blur(10px);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 30px;\r\n  box-shadow: 0 8px 32px rgba(0, 212, 170, 0.1);\r\n}\r\n\r\n/* 高科技装饰元素 */\r\n.tech-decoration {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 120px;\r\n  height: 100px;\r\n  position: relative;\r\n}\r\n\r\n.hologram-container {\r\n  position: relative;\r\n  width: 80px;\r\n  height: 80px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.hologram-ring {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  border: 2px solid transparent;\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  background-clip: padding-box;\r\n  animation: rotate-ring 4s linear infinite;\r\n}\r\n\r\n.ring-segment {\r\n  position: absolute;\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 50%;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.ring-1 {\r\n  border-top: 2px solid #00d4aa;\r\n  animation: rotate-segment-1 2s linear infinite;\r\n}\r\n\r\n.ring-2 {\r\n  border-right: 2px solid #20ff4d;\r\n  animation: rotate-segment-2 3s linear infinite reverse;\r\n}\r\n\r\n.ring-3 {\r\n  border-bottom: 2px solid #4dd0ff;\r\n  animation: rotate-segment-3 2.5s linear infinite;\r\n}\r\n\r\n.central-core {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 30px;\r\n  height: 30px;\r\n  background: radial-gradient(circle, rgba(0, 212, 170, 0.8), rgba(32, 255, 77, 0.4));\r\n  border-radius: 50%;\r\n  box-shadow: 0 0 20px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.core-pulse {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 20px;\r\n  height: 20px;\r\n  background: #00d4aa;\r\n  border-radius: 50%;\r\n  animation: pulse-core 2s ease-in-out infinite;\r\n}\r\n\r\n.core-data {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.data-stream {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 8px;\r\n  background: linear-gradient(to bottom, #20ff4d, transparent);\r\n  animation: data-flow 1.5s linear infinite;\r\n}\r\n\r\n.data-stream.delay-1 {\r\n  animation-delay: 0.5s;\r\n  transform: rotate(120deg);\r\n}\r\n\r\n.data-stream.delay-2 {\r\n  animation-delay: 1s;\r\n  transform: rotate(240deg);\r\n}\r\n\r\n.tech-grid {\r\n  position: absolute;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 20px;\r\n  opacity: 0.3;\r\n}\r\n\r\n.grid-line {\r\n  position: absolute;\r\n  background: linear-gradient(90deg, transparent, #00d4aa, transparent);\r\n  height: 1px;\r\n  width: 100%;\r\n  animation: grid-scan 3s linear infinite;\r\n}\r\n\r\n.grid-line:nth-child(1) { top: 0; animation-delay: 0s; }\r\n.grid-line:nth-child(2) { top: 5px; animation-delay: 0.5s; }\r\n.grid-line:nth-child(3) { top: 10px; animation-delay: 1s; }\r\n.grid-line:nth-child(4) { top: 15px; animation-delay: 1.5s; }\r\n\r\n/* 中间统计区域 */\r\n.stats-section-center {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n}\r\n\r\n/* 左侧统计区域 (保留兼容) */\r\n.stats-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n}\r\n\r\n.stat-card {\r\n  background: linear-gradient(145deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\r\n  border: 1px solid rgba(0, 212, 170, 0.4);\r\n  border-radius: 12px;\r\n  padding: 15px 20px;\r\n  text-align: center;\r\n  min-width: 80px;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.stat-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);\r\n  transition: left 0.6s ease;\r\n}\r\n\r\n.stat-card:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.stat-card:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(0, 212, 170, 0.3);\r\n  border-color: rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.stat-number {\r\n  font-size: 1.8rem;\r\n  font-weight: 700;\r\n  color: #00d4aa;\r\n  margin-bottom: 5px;\r\n  font-family: 'Orbitron', monospace;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.stat-text {\r\n  font-size: 0.9rem;\r\n  color: #20ff4d;\r\n  font-weight: 500;\r\n  opacity: 0.9;\r\n}\r\n\r\n/* 全部展开/折叠卡片特殊样式 */\r\n.toggle-all-card {\r\n  cursor: pointer;\r\n  background: linear-gradient(145deg, rgba(255, 165, 0, 0.1), rgba(255, 215, 0, 0.1));\r\n  border: 1px solid rgba(255, 165, 0, 0.4);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.toggle-all-card:hover {\r\n  background: linear-gradient(145deg, rgba(255, 165, 0, 0.2), rgba(255, 215, 0, 0.2));\r\n  border-color: rgba(255, 165, 0, 0.6);\r\n  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);\r\n  transform: translateY(-3px) scale(1.05);\r\n}\r\n\r\n.toggle-all-card .stat-number {\r\n  color: #ffa500;\r\n  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);\r\n}\r\n\r\n.toggle-all-card .stat-text {\r\n  color: #ffd700;\r\n}\r\n\r\n.toggle-icon {\r\n  font-size: 1.5rem;\r\n  display: inline-block;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n.toggle-all-card:hover .toggle-icon {\r\n  transform: scale(1.2);\r\n}\r\n\r\n/* 右侧控制区域 */\r\n.controls-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n/* 数据统计 */\r\n.data-stats {\r\n  display: flex;\r\n  gap: 30px;\r\n  margin-bottom: 25px;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.stat-item {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  border-radius: 12px;\r\n  padding: 15px 25px;\r\n  text-align: center;\r\n}\r\n\r\n.data-stats-compact .stat-item {\r\n  padding: 10px 15px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.stat-label {\r\n  color: #20ff4d;\r\n  font-size: 0.9rem;\r\n  display: block;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stat-value {\r\n  color: #ffffff;\r\n  font-size: 1.4rem;\r\n  font-weight: 700;\r\n}\r\n\r\n/* 工作类型分组 */\r\n.tracking-table-container {\r\n  space-y: 20px;\r\n}\r\n\r\n.work-type-group {\r\n  background: rgba(255, 255, 255, 0.03);\r\n  border: 1px solid rgba(0, 212, 170, 0.2);\r\n  border-radius: 15px;\r\n  margin-bottom: 20px;\r\n  overflow: hidden;\r\n}\r\n\r\n.group-header {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.2), rgba(32, 255, 77, 0.1));\r\n  padding: 15px 25px;\r\n  cursor: pointer;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.group-header:hover {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));\r\n}\r\n\r\n.group-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  color: #00d4aa;\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n}\r\n\r\n.collapse-icon {\r\n  color: #20ff4d;\r\n  font-size: 1rem;\r\n  transition: transform 0.3s ease;\r\n}\r\n\r\n/* 项目数量高亮显示 */\r\n.item-count-highlight {\r\n  color: #ffd700;\r\n  font-weight: 800;\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 165, 0, 0.1));\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(255, 215, 0, 0.4);\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.6);\r\n  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\r\n  display: inline-block;\r\n  margin: 0 2px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.item-count-highlight:hover {\r\n  color: #ffff00;\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 165, 0, 0.2));\r\n  border-color: rgba(255, 215, 0, 0.6);\r\n  text-shadow: 0 0 12px rgba(255, 215, 0, 0.8);\r\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.5);\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* 表格容器 */\r\n.table-container {\r\n  overflow-x: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.tracking-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  background: rgba(255, 255, 255, 0.02);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n}\r\n\r\n.tracking-table th {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.2));\r\n  color: #ffffff;\r\n  padding: 15px 10px;\r\n  text-align: center;\r\n  font-weight: 700;\r\n  font-size: 0.9rem;\r\n  border-bottom: 2px solid rgba(0, 212, 170, 0.5);\r\n  position: sticky;\r\n  top: 0;\r\n  z-index: 10;\r\n}\r\n\r\n.tracking-table td {\r\n  padding: 12px 8px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  text-align: center;\r\n  vertical-align: middle;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.data-row:hover td {\r\n  background: rgba(0, 212, 170, 0.05);\r\n}\r\n\r\n/* 列宽控制 */\r\n.col-number { width: 60px; }\r\n.col-type { width: 120px; }\r\n.col-indicator { width: 150px; }\r\n.col-total-target { width: 120px; }\r\n.col-target { width: 150px; }\r\n.col-method { width: 200px; }\r\n.col-responsible { width: 100px; }\r\n.col-frequency { width: 80px; }\r\n.col-month-plan, .col-month-complete { width: 150px; }\r\n\r\n/* 可编辑单元格 */\r\n.data-cell.editable {\r\n  cursor: pointer;\r\n  position: relative;\r\n}\r\n\r\n.data-cell.editable:hover {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.cell-input {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 2px solid #00d4aa;\r\n  border-radius: 4px;\r\n  color: #ffffff;\r\n  padding: 5px 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  width: 100%;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.cell-input:focus {\r\n  outline: none;\r\n  border-color: #20ff4d;\r\n  box-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.cell-content {\r\n  display: block;\r\n  word-break: break-word;\r\n  line-height: 1.4;\r\n}\r\n\r\n/* 月份列特殊样式 */\r\n.month-plan {\r\n  background: rgba(77, 208, 255, 0.05);\r\n  border-left: 3px solid rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.month-complete {\r\n  background: rgba(32, 255, 77, 0.05);\r\n  border-left: 3px solid rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n}\r\n\r\n.loading-spinner {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: 3px solid rgba(0, 212, 170, 0.3);\r\n  border-top: 3px solid #00d4aa;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 高科技元素动画 */\r\n@keyframes rotate-ring {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-1 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-2 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes rotate-segment-3 {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes pulse-core {\r\n  0%, 100% { \r\n    transform: translate(-50%, -50%) scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% { \r\n    transform: translate(-50%, -50%) scale(1.2);\r\n    opacity: 0.8;\r\n  }\r\n}\r\n\r\n@keyframes data-flow {\r\n  0% { \r\n    transform: translateY(-10px) scale(1);\r\n    opacity: 0;\r\n  }\r\n  50% { \r\n    transform: translateY(0) scale(1.2);\r\n    opacity: 1;\r\n  }\r\n  100% { \r\n    transform: translateY(10px) scale(1);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n@keyframes grid-scan {\r\n  0% { \r\n    transform: translateX(-100%);\r\n    opacity: 0;\r\n  }\r\n  50% { \r\n    opacity: 1;\r\n  }\r\n  100% { \r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n/* 无数据状态 */\r\n.no-data {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  font-size: 1.2rem;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .unified-control-panel-v2 {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .tech-decoration {\r\n    width: 100%;\r\n    height: 60px;\r\n    flex-direction: row;\r\n    justify-content: center;\r\n  }\r\n\r\n  .hologram-container {\r\n    width: 60px;\r\n    height: 60px;\r\n    margin-bottom: 0;\r\n    margin-right: 20px;\r\n  }\r\n\r\n  .stats-section-center {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n  }\r\n\r\n  .controls-section {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: center;\r\n  }\r\n\r\n  .month-navigation-new {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n\r\n  .refresh-btn-new {\r\n    width: 100%;\r\n    max-width: 300px;\r\n  }\r\n\r\n  .unified-control-panel {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .stats-section {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .filter-controls {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .month-navigation {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .data-stats {\r\n    flex-direction: column;\r\n    align-items: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .work-tracking-container {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .page-title {\r\n    font-size: 1.8rem;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .header-center {\r\n    order: 1;\r\n  }\r\n\r\n  .back-btn-top {\r\n    order: 0;\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .sync-status {\r\n    order: 2;\r\n    align-self: center;\r\n  }\r\n\r\n  .stat-card {\r\n    min-width: 60px;\r\n    padding: 10px 15px;\r\n  }\r\n\r\n  .stat-number {\r\n    font-size: 1.5rem;\r\n  }\r\n\r\n  .nav-btn-new {\r\n    padding: 6px 12px;\r\n    font-size: 0.8rem;\r\n  }\r\n\r\n  .current-months-new {\r\n    font-size: 1rem;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .tech-decoration {\r\n    width: 100%;\r\n    height: 50px;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .hologram-container {\r\n    width: 40px;\r\n    height: 40px;\r\n    margin-right: 10px;\r\n  }\r\n\r\n  .stats-section-center {\r\n    gap: 10px;\r\n  }\r\n  \r\n  .control-panel {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .tracking-table {\r\n    font-size: 0.8rem;\r\n  }\r\n  \r\n  .tracking-table th,\r\n  .tracking-table td {\r\n    padding: 8px 5px;\r\n  }\r\n}\r\n\r\n/* 自定义滚动条 */\r\n.table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  border-radius: 4px;\r\n}\r\n\r\n.table-container::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n}\r\n\r\n/* 负责人筛选圆球按钮样式 */\r\n.responsible-filter-orb {\r\n  position: fixed;\r\n  top: 120px;\r\n  left: 30px;\r\n  width: 70px;\r\n  height: 70px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  cursor: pointer;\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: \r\n    0 8px 32px rgba(0, 212, 170, 0.4),\r\n    inset 0 2px 8px rgba(255, 255, 255, 0.2);\r\n  transition: all 0.3s ease;\r\n  animation: orb-pulse 2s infinite ease-in-out;\r\n}\r\n\r\n.responsible-filter-orb:hover {\r\n  transform: scale(1.1) translateY(-3px);\r\n  box-shadow: \r\n    0 12px 40px rgba(0, 212, 170, 0.6),\r\n    inset 0 2px 8px rgba(255, 255, 255, 0.3);\r\n  animation: none;\r\n}\r\n\r\n.responsible-filter-orb::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  z-index: -1;\r\n  filter: blur(8px);\r\n  opacity: 0.7;\r\n}\r\n\r\n.orb-icon {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.filter-icon {\r\n  font-size: 28px;\r\n  color: #000;\r\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\r\n  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.8));\r\n}\r\n\r\n.orb-tooltip {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 85px;\r\n  transform: translateY(-50%);\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: #00d4aa;\r\n  padding: 8px 12px;\r\n  border-radius: 20px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  white-space: nowrap;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  transition: all 0.3s ease;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.responsible-filter-orb:hover .orb-tooltip {\r\n  opacity: 1;\r\n  visibility: visible;\r\n}\r\n\r\n@keyframes orb-pulse {\r\n  0%, 100% {\r\n    box-shadow: \r\n      0 8px 32px rgba(0, 212, 170, 0.4),\r\n      inset 0 2px 8px rgba(255, 255, 255, 0.2);\r\n  }\r\n  50% {\r\n    box-shadow: \r\n      0 12px 40px rgba(0, 212, 170, 0.6),\r\n      inset 0 2px 8px rgba(255, 255, 255, 0.3);\r\n  }\r\n}\r\n\r\n/* 负责人筛选面板样式 */\r\n.responsible-filter-panel {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  width: 400px;\r\n  max-height: 500px;\r\n  background: rgba(26, 26, 46, 0.95);\r\n  border: 2px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 20px;\r\n  z-index: 2000;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: \r\n    0 20px 60px rgba(0, 0, 0, 0.3),\r\n    0 0 40px rgba(0, 212, 170, 0.2);\r\n  animation: panel-slide-in 0.3s ease-out;\r\n}\r\n\r\n@keyframes panel-slide-in {\r\n  from {\r\n    opacity: 0;\r\n    transform: translate(-50%, -60%) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translate(-50%, -50%) scale(1);\r\n  }\r\n}\r\n\r\n.filter-panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.filter-panel-header h3 {\r\n  color: #00d4aa;\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.4rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.close-panel-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n  border-radius: 50%;\r\n  width: 35px;\r\n  height: 35px;\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-panel-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.filter-panel-content {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-options {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n  gap: 6px;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.filter-option {\r\n  background: rgba(255, 255, 255, 0.08);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  padding: 8px 12px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.95rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  flex: 0 0 calc(33.33% - 4px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.filter-option:hover {\r\n  background: rgba(0, 212, 170, 0.15);\r\n  border-color: #00d4aa;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.filter-option.selected {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.3));\r\n  border-color: #00d4aa;\r\n  color: #00d4aa;\r\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.filter-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: space-between;\r\n}\r\n\r\n.clear-filter-btn, .apply-filter-btn {\r\n  flex: 1;\r\n  padding: 10px 16px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 0.95rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-filter-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n}\r\n\r\n.clear-filter-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.apply-filter-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: 1px solid #00d4aa;\r\n}\r\n\r\n.apply-filter-btn:hover {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 筛选状态横幅样式 */\r\n.filter-status-banner {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.15), rgba(32, 255, 77, 0.15));\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 15px;\r\n  padding: 15px 20px;\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  animation: banner-slide-down 0.3s ease-out;\r\n}\r\n\r\n@keyframes banner-slide-down {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.filter-status-text {\r\n  color: #00d4aa;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.clear-status-btn {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  color: #ff5757;\r\n  border: 1px solid #ff5757;\r\n  border-radius: 8px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.clear-status-btn:hover {\r\n  background: rgba(255, 87, 87, 0.4);\r\n  transform: scale(1.05);\r\n}\r\n\r\n/* 滚动条样式 */\r\n.filter-options::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  border-radius: 3px;\r\n}\r\n\r\n.filter-options::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .responsible-filter-orb {\r\n    width: 60px;\r\n    height: 60px;\r\n    top: 100px;\r\n    left: 20px;\r\n  }\r\n  \r\n  .filter-icon {\r\n    font-size: 24px;\r\n  }\r\n  \r\n  .responsible-filter-panel {\r\n    width: 90%;\r\n    max-width: 350px;\r\n  }\r\n  \r\n  .orb-tooltip {\r\n    left: 75px;\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n/* ==================== 选择性下载功能样式 ==================== */\r\n\r\n/* 操作按钮组样式 */\r\n.action-buttons-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n/* 下载按钮样式 */\r\n.download-btn-tracking {\r\n  background: linear-gradient(135deg, #ff6b6b, #ffa726, #42a5f5);\r\n  color: #000;\r\n  border: none;\r\n  border-radius: 12px;\r\n  padding: 12px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 700;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  box-shadow: \r\n    0 4px 15px rgba(255, 107, 107, 0.3),\r\n    0 2px 8px rgba(0, 0, 0, 0.2),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  min-width: 140px;\r\n  justify-content: center;\r\n}\r\n\r\n.download-btn-tracking::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled)::before {\r\n  left: 100%;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled) {\r\n  transform: translateY(-3px) scale(1.05);\r\n  box-shadow: \r\n    0 8px 25px rgba(255, 107, 107, 0.4),\r\n    0 4px 15px rgba(255, 167, 38, 0.3),\r\n    0 2px 10px rgba(66, 165, 245, 0.2);\r\n  background: linear-gradient(135deg, #ff8a80, #ffcc02, #64b5f6);\r\n}\r\n\r\n.download-btn-tracking:active {\r\n  transform: translateY(-1px) scale(1.02);\r\n  transition: all 0.1s ease;\r\n}\r\n\r\n.download-btn-tracking:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: \r\n    0 2px 8px rgba(0, 0, 0, 0.1),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.download-btn-tracking:disabled::before {\r\n  display: none;\r\n}\r\n\r\n/* 下载按钮中的小loading动画 */\r\n.loading-spinner-small {\r\n  width: 14px;\r\n  height: 14px;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 50%;\r\n  border-top-color: #000;\r\n  animation: spin-small 1s linear infinite;\r\n}\r\n\r\n@keyframes spin-small {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n/* 科技感强化 - 为下载按钮添加科技边框效果 */\r\n.download-btn-tracking::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: -2px;\r\n  left: -2px;\r\n  right: -2px;\r\n  bottom: -2px;\r\n  background: linear-gradient(45deg, \r\n    #00d4aa, #20ff4d, #4dd0ff, #ff6b6b, #ffa726, #00d4aa);\r\n  border-radius: 14px;\r\n  z-index: -1;\r\n  background-size: 300% 300%;\r\n  animation: gradient-shift 3s ease infinite;\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.download-btn-tracking:hover:not(:disabled)::after {\r\n  opacity: 0.7;\r\n}\r\n\r\n@keyframes gradient-shift {\r\n  0% {\r\n    background-position: 0% 50%;\r\n  }\r\n  50% {\r\n    background-position: 100% 50%;\r\n  }\r\n  100% {\r\n    background-position: 0% 50%;\r\n  }\r\n}\r\n\r\n/* 高科技过滤模块 - 重新设计了一个科技感的筛选按钮 */\r\n.filter-section-center {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n  margin: 0 20px;\r\n}\r\n\r\n.tech-filter-module {\r\n  position: relative;\r\n  width: 120px;\r\n  height: 120px;\r\n  cursor: pointer;\r\n  user-select: none;\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  filter: drop-shadow(0 4px 12px rgba(0, 212, 170, 0.3));\r\n}\r\n\r\n.tech-filter-module:hover {\r\n  transform: translateY(-2px);\r\n  filter: drop-shadow(0 8px 20px rgba(0, 212, 170, 0.4));\r\n}\r\n\r\n.tech-module-background {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 100px;\r\n  height: 100px;\r\n  transform: translate(-50%, -50%);\r\n  background: linear-gradient(135deg, \r\n    rgba(0, 212, 170, 0.15) 0%, \r\n    rgba(32, 255, 77, 0.1) 50%, \r\n    rgba(77, 208, 255, 0.08) 100%);\r\n  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);\r\n  border: 2px solid rgba(0, 212, 170, 0.6);\r\n  backdrop-filter: blur(10px);\r\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: \r\n    0 0 20px rgba(0, 212, 170, 0.3),\r\n    inset 0 0 20px rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.tech-filter-module:hover .tech-module-background {\r\n  background: linear-gradient(135deg, \r\n    rgba(0, 212, 170, 0.25) 0%, \r\n    rgba(32, 255, 77, 0.2) 50%, \r\n    rgba(77, 208, 255, 0.15) 100%);\r\n  transform: translate(-50%, -50%) scale(1.08) rotate(5deg);\r\n  box-shadow: \r\n    0 0 30px rgba(0, 212, 170, 0.5),\r\n    inset 0 0 30px rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.circuit-pattern {\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 110px;\r\n  height: 110px;\r\n  transform: translate(-50%, -50%);\r\n  border: 1px solid rgba(77, 208, 255, 0.4);\r\n  clip-path: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);\r\n  animation: rotate-ring 15s linear infinite;\r\n  background: linear-gradient(45deg, \r\n    transparent 48%, \r\n    rgba(77, 208, 255, 0.2) 50%, \r\n    transparent 52%);\r\n}\r\n\r\n.circuit-pattern::before,\r\n.circuit-pattern::after {\r\n  display: none;\r\n}\r\n\r\n.energy-flow {\r\n  display: none;\r\n}\r\n\r\n.hologram-border {\r\n  display: none;\r\n}\r\n\r\n@keyframes hologram-scan {\r\n  0% {\r\n    transform: rotate(0deg);\r\n  }\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.tech-module-content {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 10;\r\n}\r\n\r\n.icon-container {\r\n  position: relative;\r\n  width: 60px;\r\n  height: 60px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.icon-glow-ring {\r\n  position: absolute;\r\n  top: -5px;\r\n  left: -5px;\r\n  right: -5px;\r\n  bottom: -5px;\r\n  background: linear-gradient(45deg, \r\n    rgba(0, 212, 170, 0.3),\r\n    rgba(32, 255, 77, 0.2),\r\n    rgba(77, 208, 255, 0.3));\r\n  border-radius: 12px;\r\n  animation: pulse 2.5s ease-in-out infinite;\r\n  filter: blur(2px);\r\n}\r\n\r\n.tech-icon {\r\n  position: relative;\r\n  z-index: 2;\r\n  animation: icon-float 4s ease-in-out infinite;\r\n}\r\n\r\n@keyframes icon-float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-3px);\r\n  }\r\n}\r\n\r\n.scan-line {\r\n  display: none;\r\n}\r\n\r\n@keyframes scan-move {\r\n  0% {\r\n    top: 0%;\r\n    opacity: 0;\r\n  }\r\n  10% {\r\n    opacity: 1;\r\n  }\r\n  90% {\r\n    opacity: 1;\r\n  }\r\n  100% {\r\n    top: 100%;\r\n    opacity: 0;\r\n  }\r\n}\r\n\r\n.module-info {\r\n  position: relative;\r\n}\r\n\r\n.module-title {\r\n  color: #00d4aa;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.6);\r\n  margin-bottom: 5px;\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d, #4dd0ff);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.active-indicator {\r\n  position: absolute;\r\n  top: -15px;\r\n  right: -10px;\r\n  width: 12px;\r\n  height: 12px;\r\n}\r\n\r\n.pulse-dot {\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #20ff4d;\r\n  border-radius: 50%;\r\n  animation: pulse-dot 1s ease-in-out infinite;\r\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.8);\r\n}\r\n\r\n@keyframes pulse-dot {\r\n  0%, 100% {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    transform: scale(1.3);\r\n    opacity: 0.7;\r\n  }\r\n}\r\n\r\n.tech-accent-lines {\r\n  display: none;\r\n}\r\n\r\n.accent-line {\r\n  display: none;\r\n}\r\n\r\n.accent-line-1,\r\n.accent-line-2,\r\n.accent-line-3 {\r\n  display: none;\r\n}\r\n\r\n@keyframes line-sweep {\r\n  0%, 100% {\r\n    opacity: 0;\r\n    transform: scaleX(0);\r\n  }\r\n  50% {\r\n    opacity: 1;\r\n    transform: scaleX(1);\r\n  }\r\n}\r\n\r\n/* 响应式调整 - 下载按钮 */\r\n@media (max-width: 768px) {\r\n  .action-buttons-group {\r\n    justify-content: center;\r\n    width: 100%;\r\n  }\r\n  \r\n  .download-btn-tracking {\r\n    min-width: 120px;\r\n    padding: 10px 16px;\r\n    font-size: 0.9rem;\r\n  }\r\n  \r\n  .tech-filter-module {\r\n    width: 100px;\r\n    height: 100px;\r\n  }\r\n  \r\n  .filter-section-center {\r\n    margin: 0 10px;\r\n  }\r\n} ", "/* 模块二选择性下载模态框样式 */\r\n.work-tracking-download-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.work-tracking-download-modal {\r\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\r\n  border: 2px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 15px;\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.6),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  width: 90%;\r\n  max-width: 900px;\r\n  max-height: 85vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模态框头部 */\r\n.modal-header {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.1), rgba(77, 208, 255, 0.1));\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #00d4aa;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff4757;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 71, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 模态框内容 */\r\n.modal-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n/* 当前筛选条件显示 */\r\n.current-filters {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  color: #00d4aa;\r\n  font-weight: 600;\r\n  min-width: 80px;\r\n}\r\n\r\n.filter-value {\r\n  color: #4dd0ff;\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 4px 10px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.filter-item select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 6px 10px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.filter-item select:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.include-filter-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #e8e8e8;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.include-filter-checkbox input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 工作类型部分 */\r\n.work-types-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.work-type-section {\r\n  border: 1px solid rgba(0, 212, 170, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(15, 15, 35, 0.5);\r\n}\r\n\r\n.work-type-header {\r\n  background: linear-gradient(90deg, rgba(0, 212, 170, 0.15), rgba(77, 208, 255, 0.15));\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.work-type-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.work-type-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n.work-type-title {\r\n  color: #00d4aa;\r\n  text-shadow: 0 0 5px rgba(0, 212, 170, 0.6);\r\n}\r\n\r\n.work-type-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.2), rgba(77, 208, 255, 0.2));\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 6px 12px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.3), rgba(77, 208, 255, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n/* 工作项目列表 */\r\n.work-items-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.work-item-row {\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.work-item-row:hover {\r\n  background: rgba(0, 212, 170, 0.05);\r\n}\r\n\r\n.work-item-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.work-item-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #00d4aa;\r\n  cursor: pointer;\r\n}\r\n\r\n.work-item-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  width: 100%;\r\n}\r\n\r\n.work-item-name {\r\n  color: #e8e8e8;\r\n  font-size: 0.95rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.work-item-responsible {\r\n  color: #4dd0ff;\r\n  font-size: 0.85rem;\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  align-self: flex-start;\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(0, 212, 170, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.statistics {\r\n  display: flex;\r\n  gap: 15px;\r\n  color: #4dd0ff;\r\n  font-weight: 600;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.statistics span {\r\n  background: rgba(77, 208, 255, 0.1);\r\n  padding: 8px 15px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.format-selection {\r\n  color: #e8e8e8;\r\n}\r\n\r\n.format-selection label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-selection select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  color: #00d4aa;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.format-selection select:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.cancel-btn, .download-btn {\r\n  padding: 12px 25px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\r\n  color: #ff4757;\r\n  border: 1px solid rgba(255, 71, 87, 0.5);\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #00d4aa, #4dd0ff);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.download-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n.download-btn:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.modal-content::-webkit-scrollbar,\r\n.work-items-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-track,\r\n.work-items-list::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb,\r\n.work-items-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #00d4aa, #4dd0ff);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb:hover,\r\n.work-items-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #4dd0ff, #00d4aa);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .work-tracking-download-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .modal-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .modal-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .work-type-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modal-footer {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .statistics {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .format-selection {\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filter-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n} ", "/* WorldClass.css - 对标世界一流举措页面样式 */\n\n.world-class-container {\n  min-height: 100vh;\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n  padding: 15px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  width: 100%;\n}\n\n/* 页面头部样式 - 复用模块一设计 */\n.page-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20px;\n  position: relative;\n  padding: 10px 0;\n  width: 100%;\n}\n\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.header-center {\n  text-align: center;\n  flex: 1;\n}\n\n.back-btn-top {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\n  font-weight: 600;\n  font-size: 1.1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  order: -1;\n}\n\n.back-btn-top:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n.page-title {\n  font-family: 'Orbitron', monospace;\n  font-size: 2.2rem;\n  color: #00d4aa;\n  margin-bottom: 8px;\n  text-shadow: 0 0 20px rgba(0, 212, 170, 0.5);\n}\n\n.page-subtitle {\n  font-size: 1rem;\n  color: #20ff4d;\n  margin-bottom: 0;\n  opacity: 0.8;\n}\n\n/* 模块三独立的已同步图标样式 */\n.world-class-container .sync-status {\n  display: flex;\n  align-items: center;\n  height: 48px; /* 与返回首页按钮高度一致 */\n}\n\n.world-class-container .status-indicator {\n  padding: 8px 16px;\n  border-radius: 15px;\n  font-size: 1rem;\n  font-weight: 600;\n  height: 40px;\n  display: flex;\n  align-items: center;\n}\n\n.world-class-container .status-indicator.success {\n  background: rgba(0, 212, 170, 0.2);\n  color: #00d4aa;\n  border: 1px solid #00d4aa;\n}\n\n.world-class-container .status-indicator.error {\n  background: rgba(255, 87, 87, 0.2);\n  color: #ff5757;\n  border: 1px solid #ff5757;\n}\n\n.world-class-container .status-indicator.pending {\n  background: rgba(255, 193, 7, 0.2);\n  color: #ffc107;\n  border: 1px solid #ffc107;\n}\n\n/* 控制面板样式 */\n.control-panel {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 15px;\n  padding: 15px 20px;\n  margin-bottom: 25px;\n  backdrop-filter: blur(10px);\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n/* 左侧控件容器 */\n.controls-left {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 20px;\n}\n\n.filter-controls {\n  display: flex;\n  align-items: center;\n  gap: 30px;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n}\n\n.filter-group {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.filter-group label {\n  color: #00d4aa;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n.level-selector {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  padding: 8px 15px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  width: 130px;\n}\n\n.level-selector:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.level-selector option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #00d4aa;\n  padding: 8px 15px;\n  font-weight: 600;\n  border: none;\n}\n\n.level-selector option:hover {\n  background: rgba(0, 212, 170, 0.2);\n  color: #ffffff;\n}\n\n.value-selector {\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid rgba(0, 212, 170, 0.5);\n  border-radius: 8px;\n  color: #ffffff;\n  padding: 8px 15px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  width: 280px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.value-selector:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.value-selector option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #00d4aa;\n  padding: 8px 15px;\n  font-weight: 600;\n  border: none;\n}\n\n.value-selector option:hover {\n  background: rgba(0, 212, 170, 0.2);\n  color: #ffffff;\n}\n\n/* 月份导航样式 - 参考模块二设计但保持独立 */\n.month-navigation-new {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n  background: rgba(0, 212, 170, 0.08);\n  padding: 8px 16px;\n  border-radius: 20px;\n  border: 1px solid rgba(0, 212, 170, 0.4);\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\n  width: fit-content;\n}\n\n.nav-btn-new {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  color: #000;\n  border: none;\n  border-radius: 15px;\n  padding: 6px 12px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(0, 212, 170, 0.2);\n}\n\n.nav-btn-new:hover:not(:disabled) {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);\n  background: linear-gradient(45deg, #20ff4d, #00d4aa);\n}\n\n.nav-btn-new:disabled {\n  background: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.3);\n  cursor: not-allowed;\n  transform: none;\n  box-shadow: none;\n}\n\n.current-months-new {\n  color: #00d4aa;\n  font-weight: 700;\n  font-size: 0.95rem;\n  min-width: 80px;\n  text-align: center;\n  text-shadow: 0 0 8px rgba(0, 212, 170, 0.3);\n  font-family: 'Orbitron', monospace;\n}\n\n.refresh-btn-new {\n  background: linear-gradient(45deg, #4dd0ff, #00d4aa);\n  color: #000;\n  border: none;\n  border-radius: 15px;\n  padding: 10px 20px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-shadow: none;\n  box-shadow: 0 4px 12px rgba(77, 208, 255, 0.2);\n  order: -1;\n}\n\n.refresh-btn-new:hover {\n  transform: scale(1.05) translateY(-1px);\n  box-shadow: 0 6px 20px rgba(77, 208, 255, 0.4);\n}\n\n/* 右侧按钮容器 */\n.action-buttons {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n}\n\n/* 数据统计样式 */\n.data-stats {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 20px;\n  flex-wrap: wrap;\n  justify-content: center;\n  width: 100%;\n}\n\n.stat-item {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 10px;\n  padding: 10px 15px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n}\n\n.stat-label {\n  color: #00d4aa;\n  font-size: 0.9rem;\n  margin-right: 8px;\n}\n\n.stat-value {\n  color: #4dd0ff;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n/* 内联统计样式 - 与筛选框同行显示 */\n.stat-item-inline {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  padding: 8px 15px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  min-width: 120px;\n}\n\n.stat-item-inline .stat-label {\n  color: #00d4aa;\n  font-size: 0.9rem;\n}\n\n.stat-item-inline .stat-value {\n  color: #4dd0ff;\n  font-weight: 600;\n  font-size: 1rem;\n}\n\n/* 表格容器样式 - 最大化显示 */\n.table-container {\n  overflow-x: auto;\n  border-radius: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  width: 100%;\n  margin: 0 auto;\n  max-height: calc(100vh - 200px);\n  overflow-y: auto;\n}\n\n.world-class-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n/* 表头固定样式 - 强化固定效果 */\n.world-class-table thead {\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n}\n\n.world-class-table th {\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #00d4aa;\n  padding: 15px 10px;\n  text-align: center;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  font-weight: 700;\n  font-size: 1rem;\n  position: sticky;\n  top: -20px;\n  z-index: 100;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n}\n\n/* 表格样式更新 - 数据居中显示 */\n.world-class-table td {\n  padding: 12px 10px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  vertical-align: middle;\n  text-align: center;\n  position: relative;\n}\n\n.data-row:hover td {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: rgba(0, 212, 170, 0.3);\n}\n\n/* 列宽设置 - 自适应屏幕宽度 */\n.col-number { width: 4%; min-width: 60px; } /* 序号列 - 二字宽度 */\n.col-level { width: 8%; min-width: 120px; } /* 相关指标或方向列 - 分级展示用 */\n.col-criteria { width: 8%; min-width: 120px; } /* 工作准则列 - 缩小一点 */\n.col-target { width: 13%; min-width: 180px; } /* 2025年目标列 - 稍微缩小一点 */\n.col-measure { width: 15%; min-width: 200px; } /* 2025年举措列 - 详细内容显示 */\n.col-responsible { width: 8%; min-width: 100px; } /* 负责人列 - 再扩大一点点 */\n.col-weight { width: 4%; min-width: 50px; } /* 权重列 - 二字宽 */\n.col-remark { width: 8%; min-width: 120px; } /* 备注列 - 缩小一点点 */\n.col-month-plan, .col-month-complete { width: 9%; min-width: 140px; } /* 月度计划和完成情况列 - 增加宽度 */\n\n/* 可编辑单元格样式 */\n.editable-cell {\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.editable-cell:hover {\n  background: rgba(77, 208, 255, 0.1);\n  border-color: #4dd0ff;\n}\n\n.cell-input {\n  width: 100%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 1px solid #4dd0ff;\n  border-radius: 4px;\n  color: #ffffff;\n  padding: 8px;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  resize: vertical;\n  text-align: center;\n}\n\n.cell-input:focus {\n  outline: none;\n  border-color: #00d4aa;\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\n}\n\n.cell-content {\n  display: block;\n  word-break: break-word;\n  white-space: pre-wrap;\n  min-height: 20px;\n  text-align: center;\n}\n\n/* 月份列样式 */\n.month-plan {\n  background: rgba(32, 255, 77, 0.1) !important;\n  border-color: rgba(32, 255, 77, 0.3) !important;\n}\n\n.month-complete {\n  background: rgba(77, 208, 255, 0.1) !important;\n  border-color: rgba(77, 208, 255, 0.3) !important;\n}\n\n/* 加载状态样式更新 */\n.world-class-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.world-class-loading .loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 3px solid rgba(0, 212, 170, 0.3);\n  border-top: 3px solid #00d4aa;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.world-class-loading p {\n  color: #00d4aa;\n  font-size: 1.1rem;\n  text-align: center;\n}\n\n/* 统计信息样式 */\n.stats-section {\n  display: flex;\n  gap: 20px;\n  margin-bottom: 25px;\n  flex-wrap: wrap;\n  justify-content: center;\n  width: 100%;\n}\n\n.stat-card {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 15px;\n  padding: 20px;\n  text-align: center;\n  backdrop-filter: blur(10px);\n  transition: all 0.3s ease;\n  min-width: 150px;\n  position: relative;\n  overflow: hidden;\n}\n\n.stat-card::before {\n  content: '';\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: conic-gradient(transparent, rgba(0, 212, 170, 0.1), transparent 30%);\n  animation: rotate 6s linear infinite;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.stat-card:hover::before {\n  opacity: 1;\n}\n\n.stat-card:hover {\n  transform: translateY(-5px);\n  border-color: #00d4aa;\n  box-shadow: 0 10px 30px rgba(0, 212, 170, 0.2);\n}\n\n.stat-number {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.8rem;\n  color: #4dd0ff;\n  margin-bottom: 8px;\n  text-shadow: 0 0 15px rgba(77, 208, 255, 0.5);\n}\n\n.stat-text {\n  color: #ffffff;\n  font-size: 0.9rem;\n  opacity: 0.8;\n}\n\n/* 下载功能样式 */\n.download-controls {\n  display: flex;\n  gap: 15px;\n  align-items: center;\n  justify-content: center;\n}\n\n.download-btn {\n  background: linear-gradient(45deg, #ff6b6b, #ff8e53);\n  color: #fff;\n  border: none;\n  border-radius: 6px;\n  padding: 5px 10px;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 0.8rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.download-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .filter-controls {\n    gap: 20px;\n  }\n  \n  .month-navigation-new {\n    flex-wrap: wrap;\n    gap: 10px;\n  }\n  \n  .stats-section {\n    gap: 15px;\n  }\n  \n  .stat-card {\n    min-width: 120px;\n  }\n  \n  /* 在中等屏幕上调整列宽 */\n  .col-month-plan, .col-month-complete { width: 10%; min-width: 120px; }\n}\n\n@media (max-width: 768px) {\n  .world-class-container {\n    padding: 10px;\n  }\n  \n  .page-title {\n    font-size: 1.8rem;\n  }\n  \n  .page-header {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .header-center {\n    order: 1;\n  }\n  \n  .back-btn-top {\n    order: 2;\n    width: 100%;\n  }\n  \n  .sync-status {\n    order: 3;\n  }\n  \n  .stat-card {\n    flex: 1;\n    min-width: 100px;\n  }\n  \n  .stat-number {\n    font-size: 1.5rem;\n  }\n  \n  .nav-btn-new {\n    padding: 6px 12px;\n    font-size: 0.8rem;\n  }\n  \n  .current-months-new {\n    font-size: 1rem;\n  }\n  \n  .control-panel {\n    padding: 15px;\n  }\n  \n  .filter-controls {\n    flex-direction: column;\n    gap: 15px;\n  }\n  \n  .world-class-table {\n    font-size: 0.9rem;\n  }\n  \n  .world-class-table th,\n  .world-class-table td {\n    padding: 8px 5px;\n  }\n  \n  /* 移动端表格高度调整 */\n  .table-container {\n    max-height: calc(100vh - 280px);\n  }\n}\n\n/* 滚动条样式 */\n.table-container::-webkit-scrollbar {\n  height: 8px;\n  width: 8px;\n}\n\n.table-container::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n}\n\n.table-container::-webkit-scrollbar-thumb {\n  background: rgba(0, 212, 170, 0.5);\n  border-radius: 4px;\n}\n\n.table-container::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 212, 170, 0.7);\n}\n\n/* 动画定义 */\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n@keyframes rotate {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 新的高科技感筛选按钮样式 */\n@keyframes pulse-glow {\n  0% {\n    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);\n  }\n  50% {\n    box-shadow: 0 0 15px rgba(0, 212, 170, 0.6), 0 0 25px rgba(0, 212, 170, 0.5);\n  }\n  100% {\n    box-shadow: 0 0 5px rgba(0, 212, 170, 0.4), 0 0 10px rgba(0, 212, 170, 0.3);\n  }\n}\n\n@keyframes rotate-border {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n.responsible-filter-btn-new {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 55px;\n  height: 55px;\n  background: #1a1a2e;\n  border: 2px solid rgba(0, 212, 170, 0.5);\n  border-radius: 50%;\n  cursor: pointer;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  color: #00d4aa;\n  font-size: 22px;\n  animation: pulse-glow 4s infinite ease-in-out;\n}\n\n.responsible-filter-btn-new::before {\n  content: '';\n  position: absolute;\n  top: -3px;\n  left: -3px;\n  right: -3px;\n  bottom: -3px;\n  border-radius: 50%;\n  border: 2px solid transparent;\n  border-top-color: #20ff4d;\n  transition: transform 1s linear;\n  animation: rotate-border 2s linear infinite;\n}\n\n.responsible-filter-btn-new:hover {\n  transform: scale(1.15);\n  border-color: rgba(32, 255, 77, 0.8);\n  background: rgba(32, 255, 77, 0.1);\n}\n\n.responsible-filter-btn-new:hover::before {\n  animation-duration: 1s;\n}\n\n.responsible-filter-btn-new .filter-icon {\n  transition: transform 0.3s ease;\n}\n\n.responsible-filter-btn-new:hover .filter-icon {\n  transform: rotate(15deg);\n}\n\n.responsible-filter-btn-new .filter-badge {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background: linear-gradient(135deg, #ff6b6b, #ee5a52);\n  color: white;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  font-size: 11px;\n  font-weight: bold;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  z-index: 3;\n  transform: translate(20%, -20%);\n}\n\n/* 空数据状态 */\n.no-data {\n  text-align: center;\n  padding: 50px;\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 1.1rem;\n  width: 100%;\n}\n\n/* 负责人筛选面板样式（复用模块二设计） */\n.world-class-responsible-filter-panel {\n  position: fixed;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 320px;\n  max-height: 500px;\n  background: linear-gradient(135deg, rgba(0, 20, 40, 0.98), rgba(0, 40, 60, 0.98));\n  border: 2px solid rgba(0, 255, 204, 0.4);\n  border-radius: 15px;\n  backdrop-filter: blur(25px);\n  box-shadow: \n    0 15px 50px rgba(0, 0, 0, 0.4),\n    0 0 40px rgba(0, 255, 204, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  z-index: 2000;\n  overflow: hidden;\n  animation: fadeInScale 0.3s ease-out;\n}\n\n@keyframes fadeInScale {\n  0% {\n    opacity: 0;\n    transform: translate(-50%, -50%) scale(0.8);\n  }\n  100% {\n    opacity: 1;\n    transform: translate(-50%, -50%) scale(1);\n  }\n}\n\n.world-class-filter-panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  background: linear-gradient(90deg, rgba(0, 255, 204, 0.1), rgba(0, 184, 148, 0.1));\n  border-bottom: 1px solid rgba(0, 255, 204, 0.2);\n}\n\n.world-class-filter-panel-header h3 {\n  margin: 0;\n  color: #00ffcc;\n  font-size: 16px;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(0, 255, 204, 0.5);\n}\n\n.world-class-close-panel-btn {\n  background: none;\n  border: none;\n  color: #ff6b6b;\n  font-size: 24px;\n  cursor: pointer;\n  width: 30px;\n  height: 30px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.3s ease;\n}\n\n.world-class-close-panel-btn:hover {\n  background: rgba(255, 107, 107, 0.2);\n  transform: rotate(90deg);\n}\n\n.world-class-filter-panel-content {\n  display: flex;\n  flex-direction: column;\n  padding: 0 20px 20px 20px;\n  max-height: 400px;\n  overflow-y: hidden;\n}\n\n.world-class-filter-options {\n  flex: 1;\n  overflow-y: auto;\n  padding-top: 20px;\n  display: flex;\n  flex-direction: row;\n  flex-wrap: wrap;\n  gap: 6px;\n  margin-bottom: 15px;\n}\n\n.world-class-filter-option {\n  background: rgba(0, 255, 204, 0.1);\n  border: 1px solid rgba(0, 255, 204, 0.3);\n  color: #ffffff;\n  padding: 8px 12px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 0.95rem;\n  text-align: center;\n  flex: 0 0 calc(33.33% - 4px);\n  box-sizing: border-box;\n}\n\n.world-class-filter-option:hover {\n  background: rgba(0, 255, 204, 0.2);\n  border-color: rgba(0, 255, 204, 0.5);\n  box-shadow: 0 0 15px rgba(0, 255, 204, 0.3);\n}\n\n.world-class-filter-option.selected {\n  background: linear-gradient(135deg, rgba(0, 255, 204, 0.3), rgba(0, 184, 148, 0.3));\n  border-color: #00ffcc;\n  color: #00ffcc;\n  box-shadow: 0 0 20px rgba(0, 255, 204, 0.4);\n}\n\n.world-class-filter-actions {\n  flex-shrink: 0;\n  display: flex;\n  gap: 8px;\n}\n\n.world-class-clear-filter-btn, .world-class-apply-filter-btn {\n  flex: 1;\n  padding: 10px 16px;\n  border-radius: 8px;\n  border: none;\n  cursor: pointer;\n  font-size: 0.95rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.world-class-clear-filter-btn {\n  background: rgba(255, 107, 107, 0.2);\n  color: #ff6b6b;\n  border: 1px solid rgba(255, 107, 107, 0.3);\n}\n\n.world-class-clear-filter-btn:hover {\n  background: rgba(255, 107, 107, 0.3);\n  box-shadow: 0 0 15px rgba(255, 107, 107, 0.3);\n}\n\n.world-class-apply-filter-btn {\n  background: linear-gradient(135deg, #00d4aa, #00b894);\n  color: white;\n  border: 1px solid rgba(0, 255, 204, 0.3);\n}\n\n.world-class-apply-filter-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 20px rgba(0, 212, 170, 0.4);\n}\n\n/* 自定义滚动条 */\n.world-class-filter-panel-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-track {\n  background: rgba(0, 255, 204, 0.1);\n  border-radius: 3px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-thumb {\n  background: rgba(0, 255, 204, 0.5);\n  border-radius: 3px;\n}\n\n.world-class-filter-panel-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 255, 204, 0.7);\n}\n\n/* 刷新数据按钮特定样式 */\n.world-class-container .refresh-btn-new {\n  margin-right: 120px !important;\n}", "/* 模块三选择性下载模态框样式 - 复用模块一和模块二的高科技风格 */\r\n.world-class-download-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(5px);\r\n}\r\n\r\n.world-class-download-modal {\r\n  background: linear-gradient(135deg, rgba(15, 15, 35, 0.95), rgba(25, 25, 55, 0.95));\r\n  border: 2px solid rgba(77, 208, 255, 0.3);\r\n  border-radius: 15px;\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.6),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  width: 90%;\r\n  max-width: 900px;\r\n  max-height: 85vh;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 模态框头部 */\r\n.modal-header {\r\n  background: linear-gradient(90deg, rgba(77, 208, 255, 0.1), rgba(32, 255, 77, 0.1));\r\n  padding: 20px;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #4dd0ff;\r\n  font-size: 1.5rem;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(77, 208, 255, 0.6);\r\n}\r\n\r\n.close-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff4757;\r\n  font-size: 1.8rem;\r\n  cursor: pointer;\r\n  padding: 5px 10px;\r\n  border-radius: 50%;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-btn:hover {\r\n  background: rgba(255, 71, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 模态框内容 */\r\n.modal-content {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n/* 当前筛选条件显示 */\r\n.current-filters {\r\n  background: rgba(77, 208, 255, 0.1);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  border-radius: 10px;\r\n  padding: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  margin-bottom: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.filter-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.filter-label {\r\n  color: #4dd0ff;\r\n  font-weight: 600;\r\n  min-width: 80px;\r\n}\r\n\r\n.filter-value {\r\n  color: #20ff4d;\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 4px 10px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n.filter-item select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 6px 10px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.filter-item select:focus {\r\n  outline: none;\r\n  border-color: #4dd0ff;\r\n  box-shadow: 0 0 10px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.include-filter-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n  color: #e8e8e8;\r\n  cursor: pointer;\r\n  margin-left: 15px;\r\n}\r\n\r\n.include-filter-checkbox input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n/* 层级部分 */\r\n.levels-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.level-section {\r\n  border: 1px solid rgba(77, 208, 255, 0.2);\r\n  border-radius: 10px;\r\n  overflow: hidden;\r\n  background: rgba(15, 15, 35, 0.5);\r\n}\r\n\r\n.level-header {\r\n  background: linear-gradient(90deg, rgba(77, 208, 255, 0.15), rgba(32, 255, 77, 0.15));\r\n  padding: 15px 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.2);\r\n}\r\n\r\n.level-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n.level-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n.level-title {\r\n  color: #4dd0ff;\r\n  text-shadow: 0 0 5px rgba(77, 208, 255, 0.6);\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.action-btn {\r\n  background: linear-gradient(135deg, rgba(77, 208, 255, 0.2), rgba(32, 255, 77, 0.2));\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 6px 12px;\r\n  border-radius: 5px;\r\n  cursor: pointer;\r\n  font-size: 0.9rem;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.action-btn:hover {\r\n  background: linear-gradient(135deg, rgba(77, 208, 255, 0.3), rgba(32, 255, 77, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n/* 层级项目列表 */\r\n.level-items-list {\r\n  max-height: 250px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.level-item-row {\r\n  padding: 12px 20px;\r\n  border-bottom: 1px solid rgba(77, 208, 255, 0.1);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.level-item-row:hover {\r\n  background: rgba(77, 208, 255, 0.05);\r\n}\r\n\r\n.level-item-checkbox {\r\n  display: flex;\r\n  align-items: center;\r\n  cursor: pointer;\r\n  width: 100%;\r\n}\r\n\r\n.level-item-checkbox input[type=\"checkbox\"] {\r\n  margin-right: 12px;\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #4dd0ff;\r\n  cursor: pointer;\r\n}\r\n\r\n.level-item-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 5px;\r\n  width: 100%;\r\n}\r\n\r\n.level-item-target {\r\n  color: #e8e8e8;\r\n  font-size: 0.95rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.level-item-responsible {\r\n  color: #20ff4d;\r\n  font-size: 0.85rem;\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 2px 8px;\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  align-self: flex-start;\r\n}\r\n\r\n/* 模态框底部 */\r\n.modal-footer {\r\n  background: linear-gradient(90deg, rgba(15, 15, 35, 0.9), rgba(25, 25, 55, 0.9));\r\n  padding: 20px;\r\n  border-top: 1px solid rgba(77, 208, 255, 0.2);\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.statistics {\r\n  display: flex;\r\n  gap: 15px;\r\n  color: #20ff4d;\r\n  font-weight: 600;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.statistics span {\r\n  background: rgba(32, 255, 77, 0.1);\r\n  padding: 8px 15px;\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(32, 255, 77, 0.3);\r\n  text-shadow: 0 0 5px rgba(32, 255, 77, 0.6);\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.format-selection {\r\n  color: #e8e8e8;\r\n}\r\n\r\n.format-selection label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  font-weight: 600;\r\n}\r\n\r\n.format-selection select {\r\n  background: rgba(15, 15, 35, 0.8);\r\n  border: 1px solid rgba(77, 208, 255, 0.3);\r\n  color: #4dd0ff;\r\n  padding: 8px 12px;\r\n  border-radius: 5px;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n}\r\n\r\n.format-selection select:focus {\r\n  outline: none;\r\n  border-color: #4dd0ff;\r\n  box-shadow: 0 0 10px rgba(77, 208, 255, 0.3);\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.cancel-btn, .download-btn {\r\n  padding: 12px 25px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n}\r\n\r\n.cancel-btn {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 99, 99, 0.2));\r\n  color: #ff4757;\r\n  border: 1px solid rgba(255, 71, 87, 0.5);\r\n}\r\n\r\n.cancel-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 71, 87, 0.3), rgba(255, 99, 99, 0.3));\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);\r\n}\r\n\r\n.download-btn {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.download-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 20px rgba(77, 208, 255, 0.4);\r\n}\r\n\r\n.download-btn:disabled {\r\n  background: rgba(128, 128, 128, 0.3);\r\n  color: rgba(255, 255, 255, 0.5);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n/* 滚动条样式 */\r\n.modal-content::-webkit-scrollbar,\r\n.level-items-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-track,\r\n.level-items-list::-webkit-scrollbar-track {\r\n  background: rgba(15, 15, 35, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb,\r\n.level-items-list::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, #4dd0ff, #20ff4d);\r\n  border-radius: 4px;\r\n}\r\n\r\n.modal-content::-webkit-scrollbar-thumb:hover,\r\n.level-items-list::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, #20ff4d, #4dd0ff);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .world-class-download-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .modal-header {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .modal-header h2 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .modal-content {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .level-header {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .modal-footer {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .statistics {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .format-selection {\r\n    text-align: center;\r\n  }\r\n  \r\n  .action-buttons {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .filter-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 8px;\r\n  }\r\n} ", "@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');\n\n/* 模块四 - KPI跟踪仪表板样式 */\n\n.monthly-kpi {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  padding: 20px;\n  font-family: 'Rajdhani', 'Microsoft YaHei', sans-serif;\n  color: #e0e0e0;\n}\n\n/* 加载状态 */\n.kpi-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 80vh;\n  color: #fff;\n  text-shadow: 0 0 5px #ff4dff;\n}\n\n.loading-spinner {\n  width: 50px;\n  height: 50px;\n  border: 4px solid rgba(255, 77, 255, 0.3);\n  border-top: 4px solid #ff4dff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n  margin-bottom: 20px;\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.5);\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 头部区域 */\n.kpi-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  background: rgba(15, 15, 35, 0.5);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 15px 25px;\n  border: 1px solid rgba(77, 200, 255, 0.3);\n  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);\n  position: relative;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.back-button {\n  padding: 10px 20px;\n  border: none;\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  border-radius: 8px;\n  cursor: pointer;\n  font-size: 18px;\n  font-weight: 600;\n  transition: all 0.3s ease;\n  font-family: 'Orbitron', sans-serif;\n  text-shadow: 0 1px 3px rgba(0,0,0,0.4);\n}\n\n.back-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff);\n}\n\n.monthly-kpi .page-title {\n  color: #fff;\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0;\n  text-shadow: 0 0 10px rgba(255, 77, 255, 0.8);\n  font-family: 'Orbitron', sans-serif;\n  position: absolute;\n  left: 50%;\n  transform: translateX(-50%);\n  text-align: center;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n/* 模块四独立的已同步图标样式 */\n.monthly-kpi .sync-status {\n  color: #e0e0e0;\n  font-size: 14px;\n  height: 44px; /* 与返回首页按钮高度一致 */\n  display: flex;\n  align-items: center;\n}\n\n.monthly-kpi .status-indicator {\n  position: relative;\n  right: auto;\n  bottom: auto;\n  padding: 10px 20px;\n  border-radius: 12px;\n  font-weight: 600;\n  font-size: 18px;\n  text-transform: uppercase;\n  height: 44px; /* 与返回首页按钮高度一致 */\n  display: flex;\n  align-items: center;\n  margin-left: 0;\n}\n\n.monthly-kpi .status-indicator.success {\n  color: #fff;\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  font-size: 18px;\n  font-weight: 600;\n  border-radius: 8px;\n  border: 1px solid rgba(77, 200, 255, 0.5);\n  box-shadow: 0 0 8px rgba(77, 200, 255, 0.4);\n}\n\n.monthly-kpi .status-indicator.pending {\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff);\n  color: #fff;\n  border: 1px solid rgba(255, 77, 255, 0.5);\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\n  box-shadow: 0 0 8px rgba(255, 77, 255, 0.4);\n}\n\n.refresh-button {\n  padding: 10px 20px;\n  height: 44px;\n  border: 1px solid rgba(77, 200, 255, 0.6);\n  background: linear-gradient(135deg, #4dc8ff, #8b5cf6);\n  color: #fff;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n  backdrop-filter: blur(5px);\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\n  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.download-button {\n  padding: 10px 20px;\n  height: 44px;\n  border: 1px solid rgba(77, 200, 255, 0.5) !important;\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff) !important;\n  color: #fff !important;\n  border-radius: 12px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 14px;\n  backdrop-filter: blur(5px);\n  font-weight: 600;\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.refresh-button:hover {\n  background: linear-gradient(135deg, #8b5cf6, #4dc8ff);\n  border-color: #8b5cf6;\n  transform: translateY(-2px);\n  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);\n}\n\n.download-button:hover {\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff) !important;\n  border-color: #4dc8ff !important;\n  transform: translateY(-2px);\n  box-shadow: 0 0 15px rgba(77, 200, 255, 0.4);\n}\n\n/* 高优先级选择器确保下载按钮颜色正确显示 */\n.monthly-kpi-container .download-button,\n.page-header .download-button {\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff) !important;\n  color: #fff !important;\n  border: 1px solid rgba(77, 200, 255, 0.5) !important;\n}\n\n.monthly-kpi-container .download-button:hover,\n.page-header .download-button:hover {\n  background: linear-gradient(135deg, #ff4dff, #4dc8ff) !important;\n  border-color: #4dc8ff !important;\n}\n\n/* 月份导航区域 */\n.month-navigation {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 20px;\n  background: rgba(15, 15, 35, 0.7);\n  backdrop-filter: blur(10px);\n  border-radius: 32px;\n  padding: 0;\n  border: 1.5px solid rgba(255, 107, 77, 0.3);\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.08);\n  width: fit-content;\n  margin-left: auto;\n  margin-right: auto;\n  min-width: 340px;\n}\n\n.nav-button {\n  padding: 0 20px;\n  height: 44px;\n  border: none;\n  background: linear-gradient(135deg, #4dc8ff 0%, #ff4dff 100%);\n  color: #fff;\n  border-radius: 32px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 15px;\n  font-weight: 700;\n  margin: 0 8px;\n  box-shadow: 0 2px 8px rgba(255, 77, 255, 0.08);\n  font-family: 'Rajdhani', 'Orbitron', sans-serif;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-button:disabled {\n  opacity: 0.4;\n  cursor: not-allowed;\n  background: linear-gradient(135deg, #3a3a5e 0%, #1a1a2e 100%);\n  color: #aaa;\n  border: none;\n}\n\n.nav-button:hover:not(:disabled) {\n  background: linear-gradient(135deg, #ff4dff 0%, #4dc8ff 100%);\n  transform: translateY(-1px) scale(1.04);\n  box-shadow: 0 4px 16px rgba(77, 200, 255, 0.3);\n}\n\n.current-months {\n  color: #fff;\n  font-size: 20px;\n  font-weight: 700;\n  text-shadow: 0 0 8px rgba(255, 107, 77, 0.7);\n  font-family: 'Orbitron', sans-serif;\n  margin: 0 16px;\n  letter-spacing: 1.5px;\n  min-width: 110px;\n  text-align: center;\n  background: transparent;\n  border-radius: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 48px;\n}\n\n/* 内容区域 */\n.kpi-content {\n  background: rgba(15, 15, 35, 0.6);\n  border-radius: 15px;\n  padding: 25px;\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);\n  margin-bottom: 20px;\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 77, 255, 0.1);\n}\n\n/* 表格容器 */\n.module4_kpi-table-container {\n  overflow-x: auto;\n  border-radius: 12px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  background: transparent;\n  box-shadow: 0 0 20px rgba(77, 200, 255, 0.1);\n}\n\n/* 表格样式 */\n.kpi-data-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 14px;\n  line-height: 1.5;\n  color: #e0e0e0;\n}\n\n.kpi-data-table thead {\n  background: linear-gradient(90deg, #4dc8ff, #ff4dff, #ff6b4d);\n  color: #fff;\n}\n\n.kpi-data-table th {\n  padding: 15px 10px;\n  text-align: center;\n  font-weight: 600;\n  font-size: 14px;\n  border-right: 1px solid rgba(255, 255, 255, 0.3);\n  white-space: nowrap;\n  text-transform: uppercase;\n  color: #fff;\n  text-shadow: 0 1px 2px rgba(0,0,0,0.3);\n}\n\n.kpi-data-table th:last-child {\n  border-right: none;\n}\n\n/* 表格行样式 */\n.kpi-data-row {\n  transition: all 0.2s ease;\n  border-bottom: 1px solid rgba(255, 77, 255, 0.1);\n}\n\n.kpi-data-row:nth-child(even) {\n  background-color: rgba(255, 255, 255, 0.03);\n}\n\n.kpi-data-row:hover {\n  background-color: rgba(255, 77, 255, 0.1);\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 合并单元格样式 */\n.merged-row {\n  background-color: rgba(255, 255, 255, 0.05) !important;\n}\n\n.merged-start-row {\n  background-color: rgba(255, 107, 77, 0.1);\n}\n\n.merged-cell-content {\n  background-color: transparent !important;\n  font-weight: 600;\n  vertical-align: middle;\n  color: #ff6b4d;\n}\n\n/* 表格单元格 */\n.kpi-data-cell {\n  padding: 12px 8px;\n  border-right: 1px solid rgba(255, 77, 255, 0.1);\n  border-bottom: none; /* Handled by row */\n  vertical-align: middle;\n  text-align: center;\n}\n\n.kpi-data-cell:last-child {\n  border-right: none;\n}\n\n/* 列宽控制 */\n.col-number { width: 60px; }\n.col-indicator { width: 180px; text-align: left; color: #fff; }\n.col-target { width: 250px; text-align: left; }\n.col-score { width: 80px; color: #ff6b4d; font-weight: 600; }\n.col-method { width: 150px; }\n.col-standard { width: 200px; text-align: left; }\n.col-month { width: 80px; color: #ff4dff; font-weight: 600; }\n.col-completion { width: 180px; }\n.col-score-month { width: 80px; color: #ff6b4d; font-weight: 600; }\n\n/* 可编辑单元格 */\n.editable {\n  cursor: pointer;\n  position: relative;\n}\n\n.editable:hover .cell-content {\n  background-color: rgba(255, 107, 77, 0.2);\n}\n\n.cell-content {\n  min-height: 20px;\n  padding: 4px;\n  border-radius: 4px;\n  transition: all 0.2s ease;\n}\n\n.cell-content.clickable:hover {\n  background-color: rgba(255, 77, 255, 0.1);\n  border: 1px dashed #ff4dff;\n}\n\n/* 只读单元格样式 */\n.cell-content.readonly-cell {\n  background: rgba(128, 128, 128, 0.1) !important;\n  color: rgba(255, 255, 255, 0.7) !important;\n  cursor: not-allowed !important;\n  border: 1px solid rgba(128, 128, 128, 0.3) !important;\n  transition: all 0.3s ease;\n}\n\n.cell-content.readonly-cell:hover {\n  background: rgba(128, 128, 128, 0.15) !important;\n  border: 1px solid rgba(128, 128, 128, 0.4) !important;\n  transform: none !important;\n}\n\n/* 只读列的表头样式 */\n.col-completion.readonly-header {\n  background: rgba(128, 128, 128, 0.2) !important;\n  color: rgba(255, 255, 255, 0.8) !important;\n}\n\n/* 单元格编辑器 */\n.cell-editor {\n  width: 100%;\n  min-height: 60px;\n  border: 2px solid #ff4dff;\n  border-radius: 4px;\n  padding: 6px;\n  font-size: 13px;\n  resize: vertical;\n  outline: none;\n  background: #1a1a2e;\n  color: #e0e0e0;\n  box-shadow: 0 0 10px rgba(255, 77, 255, 0.3);\n}\n\n.cell-editor:focus {\n  border-color: #ff6b4d;\n}\n\n/* 无数据状态 */\n.no-data {\n  text-align: center;\n  padding: 60px 20px;\n  color: #a0a0a0;\n}\n\n.no-data p {\n  font-size: 16px;\n  margin: 10px 0;\n  text-shadow: 0 0 3px rgba(255, 77, 255, 0.5);\n}\n\n/* 底部统计 */\n.kpi-footer {\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  background: rgba(15, 15, 35, 0.4);\n  backdrop-filter: blur(10px);\n  border-radius: 12px;\n  padding: 15px 20px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.data-stats {\n  color: #fff;\n  font-size: 14px;\n  font-weight: 500;\n  text-shadow: 0 0 2px rgba(255, 255, 255, 0.5);\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .kpi-data-table {\n    font-size: 12px;\n  }\n  \n  .col-indicator,\n  .col-target,\n  .col-standard {\n    min-width: 150px;\n  }\n}\n\n@media (max-width: 768px) {\n  .monthly-kpi {\n    padding: 10px;\n  }\n  \n  .kpi-header {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n    padding: 15px;\n  }\n  \n  .header-left,\n  .header-right {\n    flex-direction: column;\n    align-items: center;\n    gap: 10px;\n  }\n  \n  .month-navigation {\n    flex-direction: column;\n    gap: 10px;\n    min-width: 0;\n    width: 100%;\n    border-radius: 20px;\n    padding: 6px 0;\n  }\n  .nav-button, .current-months {\n    height: 40px;\n    font-size: 15px;\n    min-width: 80px;\n    margin: 0 4px;\n  }\n  .current-months {\n    font-size: 16px;\n    min-width: 80px;\n    margin: 0 8px;\n  }\n  \n  .kpi-content {\n    padding: 15px;\n  }\n  \n  .kpi-data-table {\n    font-size: 11px;\n  }\n  \n  .kpi-data-cell {\n    padding: 8px 4px;\n  }\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.kpi-content, .kpi-header, .month-navigation, .kpi-footer {\n  animation: fadeIn 0.6s ease-out;\n}\n\n/* 滚动条样式 */\n.module4_kpi-table-container::-webkit-scrollbar {\n  height: 8px;\n  width: 8px;\n}\n\n.module4_kpi-table-container::-webkit-scrollbar-track {\n  background: rgba(15, 15, 35, 0.5);\n  border-radius: 4px;\n}\n\n.module4_kpi-table-container::-webkit-scrollbar-thumb {\n  background: #ff4dff;\n  border-radius: 4px;\n  border: 1px solid #ff6b4d;\n}\n\n.module4_kpi-table-container::-webkit-scrollbar-thumb:hover {\n  background: #ff6b4d;\n}\n\n/* 控制面板样式 */\n.control-panel {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: rgba(15, 15, 35, 0.6);\n  border-radius: 15px;\n  padding: 20px 25px;\n  margin-bottom: 20px;\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n}\n\n.control-left {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n.control-right {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n}\n\n/* 指标类型选择区域 */\n.indicator-type-section {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.filter-label {\n  color: #a78bfa;\n  font-size: 16px;\n  font-weight: 600;\n  text-shadow: 0 0 5px rgba(167, 139, 250, 0.3);\n  min-width: 70px;\n}\n\n.indicator-selector-wrapper {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.indicator-type-selector {\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.9), rgba(22, 33, 62, 0.9));\n  border: 1px solid rgba(77, 200, 255, 0.3);\n  border-radius: 8px;\n  color: #4dc8ff;\n  padding: 8px 16px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-family: 'Rajdhani', sans-serif;\n  min-width: 120px;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 2px 10px rgba(77, 200, 255, 0.1);\n}\n\n.indicator-type-selector:hover {\n  border-color: rgba(77, 200, 255, 0.6);\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.indicator-type-selector:focus {\n  outline: none;\n  border-color: #4dc8ff;\n  box-shadow: 0 0 0 2px rgba(77, 200, 255, 0.2);\n}\n\n.indicator-type-selector option {\n  background: #1a1a2e;\n  color: #4dc8ff;\n  padding: 8px;\n}\n\n\n\n/* 当前显示统计 */\n.current-display-stats {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  backdrop-filter: blur(5px);\n}\n\n.stats-label {\n  color: #00d4aa;\n  font-size: 16px;\n  font-weight: 500;\n}\n\n.stats-value {\n  color: #20ff4d;\n  font-size: 16px;\n  font-weight: 700;\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\n}\n\n/* 操作按钮组 */\n.action-buttons {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  gap: 15px;\n  height: 44px;\n}\n\n\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .control-panel {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n\n  .control-left,\n  .control-right {\n    flex-direction: column;\n    align-items: center;\n    gap: 10px;\n  }\n\n  .action-buttons {\n    justify-content: center;\n    width: 100%;\n    gap: 12px;\n  }\n\n  .indicator-filter-panel {\n    width: 95%;\n    max-height: 90vh;\n  }\n\n  .filter-header,\n  .filter-content,\n  .filter-footer {\n    padding: 15px 20px;\n  }\n}", "@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');\n\n/* KPI选择性下载模态框样式 */\n\n.kpi-selective-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  backdrop-filter: blur(10px);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.kpi-selective-modal {\n  background: linear-gradient(180deg, #16213e, #0f0f23);\n  color: #e0e0e0;\n  font-family: 'Rajdhani', sans-serif;\n  border-radius: 16px;\n  width: 90%;\n  max-width: 900px;\n  max-height: 90vh;\n  display: flex;\n  flex-direction: column;\n  box-shadow: 0 0 40px rgba(77, 200, 255, 0.4), 0 0 20px rgba(255, 77, 255, 0.2);\n  animation: slideUp 0.3s ease-out;\n  border: 1px solid rgba(77, 200, 255, 0.3);\n}\n\n/* 头部 */\n.modal-header {\n  background: linear-gradient(90deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  padding: 20px 25px;\n  border-radius: 16px 16px 0 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 700;\n  font-family: 'Orbitron', sans-serif;\n  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);\n}\n\n.close-button {\n  background: none;\n  border: none;\n  color: #fff;\n  font-size: 24px;\n  cursor: pointer;\n  padding: 0;\n  width: 30px;\n  height: 30px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-button:hover {\n  background: rgba(255, 255, 255, 0.2);\n  transform: rotate(90deg);\n}\n\n/* 内容区域 */\n.modal-content {\n  padding: 25px;\n  overflow-y: auto;\n  flex: 1;\n}\n\n/* 统计信息区域 */\n.statistics-section {\n  margin-bottom: 25px;\n  padding: 20px;\n  background: linear-gradient(135deg, rgba(77, 200, 255, 0.1), rgba(255, 77, 255, 0.1));\n  border-radius: 12px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 15px;\n}\n\n.stat-item {\n  text-align: center;\n  padding: 15px;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 8px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n}\n\n.stat-label {\n  display: block;\n  font-size: 14px;\n  color: #b0b0b0;\n  margin-bottom: 5px;\n}\n\n.stat-value {\n  display: block;\n  font-size: 24px;\n  font-weight: 700;\n  color: #4dc8ff;\n  font-family: 'Orbitron', sans-serif;\n  text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);\n}\n\n.stat-value.selected {\n  color: #ff4dff;\n  text-shadow: 0 0 10px rgba(255, 77, 255, 0.5);\n}\n\n/* 选择区域 */\n.selection-section {\n  margin-bottom: 25px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.selection-section h4 {\n  margin: 0 0 15px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #4dc8ff;\n  text-shadow: 0 0 5px rgba(77, 200, 255, 0.3);\n}\n\n.select-all-button {\n  padding: 8px 16px;\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  border: none;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n.select-all-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);\n}\n\n.select-all-button.selected {\n  background: linear-gradient(135deg, #ff4dff, #ff6b4d);\n}\n\n/* 指标列表 */\n.indicators-list {\n  max-height: 300px;\n  overflow-y: auto;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.checkbox-item {\n  display: flex;\n  align-items: flex-start;\n  padding: 15px;\n  cursor: pointer;\n  border-bottom: 1px solid rgba(77, 200, 255, 0.1);\n  transition: all 0.3s ease;\n}\n\n.checkbox-item:last-child {\n  border-bottom: none;\n}\n\n.checkbox-item:hover {\n  background: rgba(77, 200, 255, 0.1);\n}\n\n.checkbox-item input[type=\"checkbox\"] {\n  margin-right: 12px;\n  margin-top: 2px;\n  transform: scale(1.2);\n  accent-color: #4dc8ff;\n}\n\n.indicator-item .indicator-info {\n  flex: 1;\n}\n\n.indicator-main {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  margin-bottom: 5px;\n}\n\n.indicator-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #fff;\n  flex: 1;\n}\n\n.indicator-score {\n  font-size: 14px;\n  color: #ff4dff;\n  font-weight: 600;\n  background: rgba(255, 77, 255, 0.1);\n  padding: 2px 8px;\n  border-radius: 4px;\n}\n\n.indicator-details {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n}\n\n.indicator-target,\n.indicator-method {\n  font-size: 13px;\n  color: #b0b0b0;\n}\n\n.indicator-target {\n  color: #4dc8ff;\n}\n\n/* 格式选择 */\n.format-options {\n  display: flex;\n  gap: 20px;\n}\n\n.radio-item {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 10px 15px;\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n}\n\n.radio-item:hover {\n  background: rgba(77, 200, 255, 0.1);\n  border-color: rgba(77, 200, 255, 0.4);\n}\n\n.radio-item input[type=\"radio\"] {\n  margin-right: 8px;\n  accent-color: #4dc8ff;\n}\n\n/* 信息区域 */\n.info-section {\n  margin-top: 20px;\n}\n\n.info-box {\n  background: rgba(77, 200, 255, 0.05);\n  border: 1px solid rgba(77, 200, 255, 0.2);\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.info-box h5 {\n  margin: 0 0 10px 0;\n  color: #4dc8ff;\n  font-size: 16px;\n}\n\n.info-box ul {\n  margin: 0;\n  padding-left: 20px;\n  color: #b0b0b0;\n}\n\n.info-box li {\n  margin-bottom: 5px;\n  font-size: 14px;\n}\n\n/* 底部按钮 */\n.modal-footer {\n  padding: 20px 25px;\n  border-top: 1px solid rgba(77, 200, 255, 0.2);\n  display: flex;\n  justify-content: flex-end;\n  gap: 15px;\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 0 0 16px 16px;\n}\n\n.cancel-button,\n.download-button {\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n.cancel-button {\n  background: rgba(128, 128, 128, 0.3);\n  color: #e0e0e0;\n  border: 1px solid rgba(128, 128, 128, 0.5);\n}\n\n.cancel-button:hover {\n  background: rgba(128, 128, 128, 0.5);\n}\n\n.download-button {\n  background: linear-gradient(135deg, #4dc8ff, #ff4dff);\n  color: #fff;\n  min-width: 150px;\n}\n\n.download-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(77, 200, 255, 0.4);\n}\n\n.download-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 动画 */\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideUp {\n  from {\n    transform: translateY(50px);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .kpi-selective-modal {\n    width: 95%;\n    max-height: 95vh;\n  }\n  \n  .stats-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  \n  .format-options {\n    flex-direction: column;\n    gap: 10px;\n  }\n  \n  .modal-footer {\n    flex-direction: column;\n  }\n}\n", "/* ProjectOne.css - 1号项目责任状页面样式 */\r\n\r\n.project-one-container {\r\n  min-height: 100vh;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  padding: 15px;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n/* 页面头部样式 - 复用模块三设计 */\r\n.project-one-container .page-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  position: relative;\r\n  padding: 10px 0;\r\n  width: 100%;\r\n}\r\n\r\n.project-one-container .header-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.project-one-container .header-center {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.project-one-container .back-btn-fixed {\r\n  position: fixed;\r\n  top: 20px;\r\n  left: 20px;\r\n  z-index: 1000;\r\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd);\r\n  color: #1a1a2e;\r\n  border: none;\r\n  border-radius: 8px;\r\n  padding: 12px 24px;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1.1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.project-one-container .back-btn-fixed:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(167, 139, 250, 0.4);\r\n}\r\n\r\n/* 返回首页按钮样式 - 紫蓝色主题 - 提高特异性 */\r\n.project-one-container .back-btn-top {\r\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd) !important;\r\n  color: #1a1a2e !important;\r\n  border: none !important;\r\n  border-radius: 8px !important;\r\n  padding: 12px 24px !important;\r\n  font-family: 'Rajdhani', sans-serif !important;\r\n  font-weight: 600 !important;\r\n  font-size: 1.1rem !important;\r\n  cursor: pointer !important;\r\n  transition: all 0.3s ease !important;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;\r\n  order: -1 !important;\r\n}\r\n\r\n.project-one-container .back-btn-top:hover {\r\n  transform: translateY(-2px) !important;\r\n  box-shadow: 0 5px 15px rgba(167, 139, 250, 0.4) !important;\r\n}\r\n\r\n.project-one-container .page-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 2.2rem;\r\n  color: #a78bfa;\r\n  margin-bottom: 8px;\r\n  text-shadow: 0 0 20px rgba(167, 139, 250, 0.5);\r\n}\r\n\r\n.project-one-container .page-subtitle {\r\n  font-size: 1rem;\r\n  color: #c4b5fd;\r\n  margin-bottom: 0;\r\n  opacity: 0.8;\r\n}\r\n\r\n/* 模块五独立的已同步图标样式 - 紫蓝色主题 */\r\n.project-one-container .sync-status {\r\n  display: flex;\r\n  align-items: center;\r\n  height: 48px; /* 与返回首页按钮高度一致 */\r\n}\r\n\r\n.project-one-container .status-indicator {\r\n  padding: 8px 16px;\r\n  border-radius: 15px;\r\n  font-size: 0.95rem;\r\n  font-weight: 600;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.project-one-container .status-indicator.success {\r\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd) !important;\r\n  color: #1a1a2e !important;\r\n  border: 1px solid rgba(167, 139, 250, 0.6) !important;\r\n  box-shadow: 0 0 8px rgba(167, 139, 250, 0.4) !important;\r\n}\r\n\r\n.project-one-container .status-indicator.error {\r\n  background: rgba(255, 87, 87, 0.1) !important;\r\n  color: #ff5757 !important;\r\n  border: 1px solid rgba(255, 87, 87, 0.3) !important;\r\n}\r\n\r\n.project-one-container .status-indicator.pending {\r\n  background: rgba(196, 181, 253, 0.1) !important;\r\n  color: #c4b5fd !important;\r\n  border: 1px solid rgba(196, 181, 253, 0.3) !important;\r\n  font-size: 1rem;\r\n  padding: 4px 12px;\r\n  border-radius: 12px;\r\n}\r\n\r\n.project-one-container .refresh-btn-new {\r\n  background: linear-gradient(45deg, #c4b5fd, #a78bfa);\r\n  color: #1a1a2e;\r\n  border: none;\r\n  border-radius: 15px;\r\n  padding: 10px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: none;\r\n  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);\r\n  order: -1;\r\n  transform: translateX(-120px);\r\n}\r\n\r\n.project-one-container .refresh-btn-new:hover {\r\n  transform: translateX(-120px) scale(1.05) translateY(-1px);\r\n  box-shadow: 0 6px 20px rgba(167, 139, 250, 0.4);\r\n}\r\n\r\n/* 控制面板样式 */\r\n.project-one-container .control-panel {\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(167, 139, 250, 0.3);\r\n  border-radius: 15px;\r\n  padding: 15px 20px;\r\n  margin-bottom: 25px;\r\n  backdrop-filter: blur(10px);\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n}\r\n\r\n/* 左侧控件容器 */\r\n.project-one-container .controls-left {\r\n  display: flex;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n}\r\n\r\n.project-one-container .filter-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 30px;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.project-one-container .filter-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.project-one-container .filter-group label {\r\n  color: #a78bfa;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n}\r\n\r\n.project-one-container .type-selector {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  padding: 8px 15px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  width: 250px;\r\n}\r\n\r\n.project-one-container .type-selector:focus {\r\n  outline: none;\r\n  border-color: #00d4aa;\r\n  box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.project-one-container .type-selector option {\r\n  background: rgba(26, 26, 46, 0.95);\r\n  color: #00d4aa;\r\n  padding: 8px 15px;\r\n  font-weight: 600;\r\n  border: none;\r\n}\r\n\r\n.project-one-container .type-selector option:hover {\r\n  background: rgba(0, 212, 170, 0.2);\r\n  color: #ffffff;\r\n}\r\n\r\n/* 统计信息内联显示 */\r\n.project-one-container .stat-item-inline {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px 16px;\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border-radius: 20px;\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.project-one-container .stat-label {\r\n  color: #a78bfa;\r\n  font-weight: 600;\r\n  font-size: 0.9rem;\r\n}\r\n\r\n.project-one-container .stat-value {\r\n  color: #20ff4d;\r\n  font-weight: 700;\r\n  font-size: 1rem;\r\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.3);\r\n}\r\n\r\n/* 负责人筛选按钮 */\r\n.project-one-container .responsible-filter-btn-new {\r\n  position: relative;\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 50%;\r\n  width: 60px;\r\n  height: 60px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 1.4rem;\r\n  margin-left: 30px;\r\n}\r\n\r\n.project-one-container .responsible-filter-btn-new:hover {\r\n  background: rgba(0, 212, 170, 0.2);\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n.project-one-container .filter-icon {\r\n  font-size: 1.4rem;\r\n}\r\n\r\n.project-one-container .filter-badge {\r\n  position: absolute;\r\n  top: -5px;\r\n  right: -5px;\r\n  background: linear-gradient(45deg, #ff4757, #ff6b7a);\r\n  color: #fff;\r\n  border-radius: 50%;\r\n  width: 20px;\r\n  height: 20px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 0.7rem;\r\n  font-weight: 700;\r\n  box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);\r\n}\r\n\r\n/* 右侧按钮容器 */\r\n.project-one-container .action-buttons {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n  margin-left: -60px;\r\n}\r\n\r\n/* 月份导航样式 - 参考模块三设计但保持独立 */\r\n.project-one-container .month-navigation-new {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 18px;\r\n  background: rgba(0, 212, 170, 0.08);\r\n  padding: 12px 20px;\r\n  border-radius: 24px;\r\n  border: 1px solid rgba(0, 212, 170, 0.4);\r\n  box-shadow: inset 0 2px 8px rgba(0, 212, 170, 0.1);\r\n  width: fit-content;\r\n}\r\n\r\n.project-one-container .nav-btn-new {\r\n  background: linear-gradient(45deg, #a78bfa, #c4b5fd);\r\n  color: #1a1a2e;\r\n  border: none;\r\n  border-radius: 18px;\r\n  padding: 8px 16px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: none;\r\n  box-shadow: 0 4px 12px rgba(167, 139, 250, 0.2);\r\n}\r\n\r\n.project-one-container .nav-btn-new:hover:not(:disabled) {\r\n  transform: scale(1.05) translateY(-1px);\r\n  box-shadow: 0 6px 20px rgba(167, 139, 250, 0.4);\r\n  background: linear-gradient(45deg, #c4b5fd, #a78bfa);\r\n}\r\n\r\n.project-one-container .nav-btn-new:disabled {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.3);\r\n  cursor: not-allowed;\r\n  transform: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.project-one-container .current-months-new {\r\n  color: #a78bfa;\r\n  font-weight: 700;\r\n  font-size: 1.1rem;\r\n  min-width: 90px;\r\n  text-align: center;\r\n  text-shadow: 0 0 8px rgba(167, 139, 250, 0.3);\r\n  font-family: 'Orbitron', monospace;\r\n}\r\n\r\n/* 下载按钮 */\r\n.project-one-container .download-btn {\r\n  background: linear-gradient(45deg, #8b5cf6, #a78bfa);\r\n  color: #ffffff;\r\n  border: none;\r\n  border-radius: 15px;\r\n  padding: 10px 20px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-shadow: none;\r\n  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);\r\n}\r\n\r\n.project-one-container .download-btn:hover {\r\n  transform: scale(1.05) translateY(-1px);\r\n  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);\r\n}\r\n\r\n/* 负责人筛选面板样式 */\r\n.project-one-responsible-filter-panel {\r\n  position: fixed;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\r\n  border: 1px solid rgba(0, 212, 170, 0.5);\r\n  border-radius: 20px;\r\n  padding: 0;\r\n  z-index: 1000;\r\n  width: 90%;\r\n  max-width: 500px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(20px);\r\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\r\n}\r\n\r\n.project-one-filter-panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 20px 25px;\r\n  border-bottom: 1px solid rgba(0, 212, 170, 0.3);\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\r\n}\r\n\r\n.project-one-filter-panel-header h3 {\r\n  margin: 0;\r\n  color: #00d4aa;\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.3rem;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\r\n}\r\n\r\n.project-one-close-panel-btn {\r\n  background: none;\r\n  border: none;\r\n  color: #ff5757;\r\n  font-size: 2rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  width: 40px;\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 50%;\r\n}\r\n\r\n.project-one-close-panel-btn:hover {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.project-one-filter-panel-content {\r\n  padding: 25px;\r\n}\r\n\r\n.project-one-filter-options {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\r\n  gap: 12px;\r\n  margin-bottom: 25px;\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.project-one-filter-option {\r\n  padding: 12px 16px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(0, 212, 170, 0.3);\r\n  border-radius: 10px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-align: center;\r\n  font-weight: 500;\r\n  color: #ffffff;\r\n}\r\n\r\n.project-one-filter-option:hover {\r\n  background: rgba(0, 212, 170, 0.1);\r\n  border-color: #00d4aa;\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 212, 170, 0.2);\r\n}\r\n\r\n.project-one-filter-option.selected {\r\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.3), rgba(32, 255, 77, 0.3));\r\n  border-color: #20ff4d;\r\n  color: #20ff4d;\r\n  font-weight: 700;\r\n  text-shadow: 0 0 8px rgba(32, 255, 77, 0.5);\r\n}\r\n\r\n.project-one-filter-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 15px;\r\n}\r\n\r\n.project-one-clear-filter-btn,\r\n.project-one-apply-filter-btn {\r\n  padding: 10px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-weight: 600;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.project-one-clear-filter-btn {\r\n  background: rgba(255, 87, 87, 0.1);\r\n  color: #ff5757;\r\n  border: 1px solid rgba(255, 87, 87, 0.5);\r\n}\r\n\r\n.project-one-clear-filter-btn:hover {\r\n  background: rgba(255, 87, 87, 0.2);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.project-one-apply-filter-btn {\r\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\r\n  color: #000;\r\n  border: 1px solid transparent;\r\n}\r\n\r\n.project-one-apply-filter-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\r\n}\r\n\r\n/* 表格容器样式 - 参考模块三设计，支持滚动功能 */\r\n.project-one-container .table-container {\r\n  overflow-x: auto;\r\n  overflow-y: auto;\r\n  border-radius: 15px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(167, 139, 250, 0.3);\r\n  width: 100%;\r\n  margin: 0 auto;\r\n  max-height: calc(100vh - 200px);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.project-one-container .project-one-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 0.9rem;\r\n  /* 移除min-width限制，允许表格完全适配容器 */\r\n}\r\n\r\n/* 表头固定样式 - 参考模块三设计，强化固定效果 */\r\n.project-one-container .project-one-table thead {\r\n  position: sticky;\r\n  top: -20px;\r\n  z-index: 100;\r\n}\r\n\r\n.project-one-container .project-one-table th {\r\n  background: linear-gradient(135deg, #2a1a4a 0%, #1e1e3f 50%, #0f0f2a 100%);\r\n  color: #a78bfa;\r\n  padding: 15px 10px;\r\n  text-align: center;\r\n  border: 1px solid rgba(167, 139, 250, 0.3);\r\n  font-weight: 700;\r\n  font-size: 1rem;\r\n  position: sticky;\r\n  top: -20px;\r\n  z-index: 100;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);\r\n  cursor: default;\r\n  user-select: none;\r\n}\r\n\r\n.project-one-container .project-one-table th:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.project-one-container .project-one-table tbody tr {\r\n  /* 移除底部分隔线，创造更简洁的视觉效果 */\r\n  border-bottom: none;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.project-one-container .project-one-table tbody tr:hover {\r\n  /* 保持轻微的悬停效果 */\r\n  background: rgba(0, 212, 170, 0.08);\r\n}\r\n\r\n.project-one-container .data-cell {\r\n  padding: 12px;\r\n  /* 移除边框，创造更简洁的视觉效果 */\r\n  border-right: none;\r\n  vertical-align: middle; /* 垂直居中对齐 */\r\n  position: relative;\r\n}\r\n\r\n.project-one-container .data-cell:last-child {\r\n  border-right: none;\r\n}\r\n\r\n/* 优化后的列宽设置 - 适配1366px屏幕无滚动 */\r\n.project-one-container .col-number { width: 40px; min-width: 40px; }\r\n.project-one-container .col-problem { width: 124px; min-width: 112px; } /* 第二列再减少一个字符宽度 */\r\n.project-one-container .col-type { width: 96px; min-width: 78px; } /* 第三列再增加一个字符宽度 */\r\n.project-one-container .col-target { width: 180px; min-width: 160px; }\r\n.project-one-container .col-deadline { width: 65px; min-width: 55px; }\r\n.project-one-container .col-form { width: 130px; min-width: 110px; }\r\n.project-one-container .col-responsible { width: 90px; min-width: 90px; } /* 保持三个字正常显示 */\r\n.project-one-container .col-month-plan { width: 140px; min-width: 120px; }\r\n.project-one-container .col-month-complete { width: 140px; min-width: 120px; }\r\n\r\n/* 可编辑单元格样式 - 优化多行文本显示 */\r\n.project-one-container .editable-cell {\r\n  min-height: 40px;\r\n  padding: 8px;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: block;\r\n  width: 100%;\r\n  word-wrap: break-word;\r\n  white-space: pre-wrap;\r\n  line-height: 1.4;\r\n  background: transparent;\r\n  border: 2px solid transparent;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.project-one-container .editable-cell:hover:not(.readonly) {\r\n  /* 悬停时显示柔和的虚线边框 - 只在最外层显示 */\r\n  border: 2px dashed rgba(167, 139, 250, 0.4);\r\n  background: transparent;\r\n  transform: scale(1.02);\r\n}\r\n\r\n.editable-cell:focus:not(.readonly),\r\n.project-one-container .editable-cell.editing {\r\n  /* 编辑时显示实线边框 - 只在最外层显示 */\r\n  border: 2px solid rgba(167, 139, 250, 0.6);\r\n  background: transparent;\r\n  box-shadow: 0 0 15px rgba(167, 139, 250, 0.2);\r\n}\r\n\r\n.project-one-container .editable-cell.readonly {\r\n  /* 只读单元格保持完全透明 */\r\n  background: transparent;\r\n  cursor: not-allowed;\r\n  opacity: 0.7;\r\n  border: 2px solid transparent;\r\n}\r\n\r\n.project-one-container .editable-cell.empty {\r\n  color: rgba(255, 255, 255, 0.5);\r\n  font-style: italic;\r\n  background: transparent !important;\r\n}\r\n\r\n/* 只对可编辑的空单元格显示占位符，只读单元格保持完全空白 */\r\n.project-one-container .editable-cell.empty:not(.readonly):before {\r\n  content: '点击编辑...';\r\n}\r\n\r\n.project-one-container .editable-cell.has-content {\r\n  color: #ffffff;\r\n  background: transparent !important;\r\n}\r\n\r\n.project-one-container .editable-cell.month-plan {\r\n  /* 月份计划单元格完全透明 */\r\n  background: transparent !important;\r\n}\r\n\r\n.project-one-container .editable-cell.month-complete {\r\n  /* 月份完成单元格完全透明 */\r\n  background: transparent !important;\r\n}\r\n\r\n/* 确保多行文本内部元素无背景色和边框 */\r\n.project-one-container .editable-cell * {\r\n  background: transparent !important;\r\n  border: none !important;\r\n  box-shadow: none !important;\r\n}\r\n\r\n/* 确保换行符不产生额外的样式 */\r\n.project-one-container .editable-cell br {\r\n  line-height: 1.4;\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n/* 单元格编辑器 */\r\n.project-one-container .cell-editor {\r\n  width: 100%;\r\n  min-height: 60px;\r\n  padding: 8px;\r\n  background: rgba(42, 26, 74, 0.95);\r\n  border: 2px solid #a78bfa;\r\n  border-radius: 6px;\r\n  color: #ffffff;\r\n  font-family: inherit;\r\n  font-size: inherit;\r\n  line-height: 1.4;\r\n  resize: vertical;\r\n  outline: none;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.project-one-container .cell-editor:focus {\r\n  border-color: #c4b5fd;\r\n  box-shadow: 0 0 15px rgba(167, 139, 250, 0.4);\r\n}\r\n\r\n/* 无数据提示 */\r\n.project-one-container .no-data {\r\n  text-align: center;\r\n  padding: 40px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  font-size: 1.1rem;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border-radius: 15px;\r\n  margin: 20px 0;\r\n}\r\n\r\n/* 加载状态 */\r\n.project-one-container .loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  gap: 20px;\r\n}\r\n\r\n.project-one-container .loading-spinner {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: 3px solid rgba(0, 212, 170, 0.3);\r\n  border-top: 3px solid #00d4aa;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.project-one-container .loading-text {\r\n  color: #00d4aa;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 加载状态样式 */\r\n.project-one-container .project-one-loading {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  min-height: 400px;\r\n  gap: 20px;\r\n}\r\n\r\n.project-one-container .loading-spinner {\r\n  width: 50px;\r\n  height: 50px;\r\n  border: 3px solid rgba(0, 212, 170, 0.3);\r\n  border-top: 3px solid #00d4aa;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n.project-one-container .project-one-loading p {\r\n  color: #00d4aa;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.3);\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 响应式设计 - 优化后无需水平滚动 */\r\n@media (max-width: 1366px) {\r\n  /* 针对1366px及以下屏幕优化 */\r\n  .project-one-container .project-one-table {\r\n    font-size: 0.85rem;\r\n  }\r\n\r\n  .project-one-table th,\r\n  .project-one-container .data-cell {\r\n    padding: 10px 8px;\r\n  }\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  /* 进一步压缩列宽 */\r\n  .project-one-container .col-number { width: 45px; min-width: 45px; }\r\n  .project-one-container .col-problem { width: 144px; min-width: 132px; } /* 第二列再减少一个字符宽度 */\r\n  .project-one-container .col-type { width: 106px; min-width: 88px; } /* 第三列再增加一个字符宽度 */\r\n  .project-one-container .col-target { width: 200px; min-width: 180px; }\r\n  .project-one-container .col-deadline { width: 70px; min-width: 60px; }\r\n  .project-one-container .col-form { width: 140px; min-width: 120px; }\r\n  .project-one-container .col-responsible { width: 85px; min-width: 85px; }\r\n  .project-one-container .col-month-plan { width: 150px; min-width: 130px; }\r\n  .project-one-container .col-month-complete { width: 150px; min-width: 130px; }\r\n\r\n  .project-one-container .control-panel {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n  }\r\n\r\n  .project-one-container .controls-left {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n\r\n  .project-one-container .action-buttons {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .project-one-container {\r\n    padding: 10px;\r\n  }\r\n\r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .header-center {\r\n    order: 1;\r\n  }\r\n\r\n  .project-one-container .back-btn-top {\r\n    order: 0;\r\n    align-self: flex-start;\r\n  }\r\n\r\n  .header-actions {\r\n    order: 2;\r\n    justify-content: center;\r\n  }\r\n\r\n  .project-one-container .filter-controls {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .project-one-container .filter-group {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n    gap: 8px;\r\n  }\r\n\r\n  .project-one-container .month-navigation-new {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n\r\n  .project-one-responsible-filter-panel {\r\n    width: 95%;\r\n    margin: 10px;\r\n  }\r\n\r\n  .project-one-filter-options {\r\n    grid-template-columns: 1fr;\r\n  }\r\n\r\n  .project-one-filter-actions {\r\n    flex-direction: column;\r\n  }\r\n\r\n  /* 移动端表格优化 */\r\n  .project-one-container .project-one-table {\r\n    font-size: 0.75rem;\r\n  }\r\n\r\n  .project-one-table th,\r\n  .project-one-container .data-cell {\r\n    padding: 6px 4px;\r\n  }\r\n\r\n  /* 移动端列宽进一步优化 */\r\n  .project-one-container .col-number { width: 35px; min-width: 35px; }\r\n  .project-one-container .col-problem { width: 104px; min-width: 92px; } /* 第二列再减少一个字符宽度 */\r\n  .project-one-container .col-type { width: 86px; min-width: 68px; } /* 第三列再增加一个字符宽度 */\r\n  .project-one-container .col-target { width: 160px; min-width: 140px; }\r\n  .project-one-container .col-deadline { width: 60px; min-width: 50px; }\r\n  .project-one-container .col-form { width: 120px; min-width: 100px; }\r\n  .project-one-container .col-responsible { width: 70px; min-width: 70px; }\r\n  .project-one-container .col-month-plan { width: 120px; min-width: 100px; }\r\n  .project-one-container .col-month-complete { width: 120px; min-width: 100px; }\r\n}\r\n\r\n/* 滚动条样式 - 紫蓝色主题 */\r\n.project-one-container .table-container::-webkit-scrollbar {\r\n  height: 8px;\r\n  width: 8px;\r\n}\r\n\r\n.project-one-container .table-container::-webkit-scrollbar-track {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.project-one-container .table-container::-webkit-scrollbar-thumb {\r\n  background: rgba(167, 139, 250, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.project-one-container .table-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(167, 139, 250, 0.7);\r\n}\r\n\r\n/* 移动端表格高度调整 */\r\n@media (max-width: 768px) {\r\n  .project-one-container .table-container {\r\n    max-height: calc(100vh - 280px);\r\n  }\r\n}\r\n\r\n/* 图标样式 */\r\n.icon-back::before { content: '←'; }\r\n.icon-refresh::before { content: '↻'; }\r\n.icon-download::before { content: '↓'; }\r\n.icon-arrow.up::before { content: '▲'; }\r\n.icon-arrow.down::before { content: '▼'; }\r\n.icon-prev::before { content: '‹'; }\r\n.icon-next::before { content: '›'; }\r\n", "/* ProjectOneDownloadModal.css - 1号项目责任状下载模态框样式 */\n\n.download-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  backdrop-filter: blur(5px);\n}\n\n.download-modal {\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 20px;\n  width: 95%;\n  max-width: 900px;\n  max-height: 90vh;\n  overflow-y: auto;\n  backdrop-filter: blur(20px);\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n}\n\n/* 模态框头部 */\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 18px 25px;\n  border-bottom: 1px solid rgba(0, 212, 170, 0.2);\n  background: linear-gradient(45deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1));\n}\n\n.modal-header h3 {\n  margin: 0;\n  font-family: 'Orbitron', monospace;\n  font-size: 1.5rem;\n  color: #00d4aa;\n  text-shadow: 0 0 10px rgba(0, 212, 170, 0.5);\n}\n\n.close-button {\n  background: none;\n  border: none;\n  color: #ff5757;\n  font-size: 2rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n}\n\n.close-button:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: scale(1.1);\n}\n\n/* 模态框内容 */\n.modal-content {\n  padding: 20px 25px;\n  color: #ffffff;\n}\n\n/* 选择区域 */\n.selection-section {\n  margin-bottom: 20px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.section-header h4 {\n  margin: 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.select-all-label {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  color: #20ff4d;\n  font-weight: 600;\n}\n\n.select-all-label input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.items-list {\n  max-height: 300px;\n  overflow-y: auto;\n  overflow-x: hidden;\n  border: 1px solid rgba(0, 212, 170, 0.2);\n  border-radius: 10px;\n  padding: 12px;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.item-checkbox {\n  display: flex;\n  align-items: flex-start;\n  gap: 8px;\n  padding: 8px 0;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.item-checkbox:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 5px;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n\n.item-checkbox input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.1);\n  margin-top: 2px;\n}\n\n.item-info {\n  display: flex;\n  flex-direction: column;\n  gap: 3px;\n  flex: 1;\n  min-width: 0;\n  word-wrap: break-word;\n  overflow-wrap: break-word;\n}\n\n.item-number {\n  color: #00d4aa;\n  font-weight: 600;\n  font-size: 0.9rem;\n  flex-shrink: 0;\n}\n\n.item-title {\n  color: #ffffff;\n  line-height: 1.4;\n  font-weight: 500;\n  word-wrap: break-word;\n  overflow-wrap: break-word;\n  white-space: normal;\n}\n\n.item-responsible {\n  color: #20ff4d;\n  font-size: 0.9rem;\n  font-style: italic;\n  flex-shrink: 0;\n}\n\n/* 格式选择 */\n.format-section {\n  margin-bottom: 20px;\n}\n\n.format-section h4 {\n  margin: 0 0 10px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.format-options {\n  display: flex;\n  gap: 15px;\n}\n\n.format-option {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  cursor: pointer;\n  padding: 8px 12px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.format-option:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: #00d4aa;\n}\n\n.format-option input[type=\"radio\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.format-option span {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 月份范围选择 */\n.month-range-section {\n  margin-bottom: 20px;\n}\n\n.month-range-section h4 {\n  margin: 0 0 10px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.month-range-controls {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.range-input {\n  display: flex;\n  flex-direction: column;\n  gap: 6px;\n}\n\n.range-input label {\n  color: #20ff4d;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.range-input select {\n  background: rgba(26, 26, 46, 0.8);\n  border: 1px solid #00d4aa;\n  border-radius: 8px;\n  padding: 8px 12px;\n  color: #ffffff;\n  font-family: inherit;\n  font-size: 1rem;\n  cursor: pointer;\n  outline: none;\n  transition: all 0.3s ease;\n}\n\n.range-input select:focus {\n  border-color: #20ff4d;\n  box-shadow: 0 0 10px rgba(32, 255, 77, 0.3);\n}\n\n.range-input select option {\n  background: rgba(26, 26, 46, 0.95);\n  color: #ffffff;\n}\n\n.range-preview {\n  color: #20ff4d;\n  font-weight: 600;\n  padding: 10px 15px;\n  background: rgba(32, 255, 77, 0.1);\n  border: 1px solid rgba(32, 255, 77, 0.3);\n  border-radius: 8px;\n}\n\n/* 其他选项 */\n.options-section {\n  margin-bottom: 20px;\n}\n\n.options-section h4 {\n  margin: 0 0 10px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.option-checkbox {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  padding: 8px 12px;\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 8px;\n  transition: all 0.3s ease;\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.option-checkbox:hover {\n  background: rgba(0, 212, 170, 0.1);\n  border-color: #00d4aa;\n}\n\n.option-checkbox input[type=\"checkbox\"] {\n  accent-color: #00d4aa;\n  transform: scale(1.2);\n}\n\n.option-checkbox span {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 预览信息 */\n.preview-section {\n  margin-bottom: 15px;\n}\n\n.preview-section h4 {\n  margin: 0 0 10px 0;\n  color: #00d4aa;\n  font-size: 1.2rem;\n  font-weight: 600;\n}\n\n.preview-info {\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(0, 212, 170, 0.3);\n  border-radius: 10px;\n  padding: 15px;\n}\n\n.preview-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 6px 0;\n}\n\n.preview-label {\n  color: #20ff4d;\n  font-weight: 600;\n}\n\n.preview-value {\n  color: #ffffff;\n  font-weight: 500;\n}\n\n/* 模态框底部 */\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  padding: 18px 25px;\n  border-top: 1px solid rgba(0, 212, 170, 0.2);\n  background: rgba(255, 255, 255, 0.02);\n}\n\n.cancel-button {\n  background: rgba(255, 87, 87, 0.1);\n  border: 1px solid #ff5757;\n  border-radius: 8px;\n  padding: 10px 20px;\n  color: #ff5757;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.cancel-button:hover {\n  background: rgba(255, 87, 87, 0.2);\n  transform: translateY(-2px);\n}\n\n.download-button {\n  background: linear-gradient(45deg, #00d4aa, #20ff4d);\n  border: none;\n  border-radius: 8px;\n  padding: 10px 20px;\n  color: #000000;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 600;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.download-button:hover:not(:disabled) {\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(0, 212, 170, 0.4);\n}\n\n.download-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  transform: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .download-modal {\n    width: 98%;\n    max-width: 950px;\n  }\n\n  .items-list {\n    max-height: 250px;\n  }\n}\n\n@media (max-width: 768px) {\n  .download-modal {\n    width: 98%;\n    margin: 10px;\n    max-width: none;\n  }\n\n  .modal-header {\n    padding: 15px;\n  }\n\n  .modal-content {\n    padding: 15px;\n  }\n\n  .modal-footer {\n    padding: 15px;\n    flex-direction: column;\n  }\n\n  .format-options {\n    flex-direction: column;\n  }\n\n  .month-range-controls {\n    flex-direction: column;\n  }\n\n  .items-list {\n    max-height: 200px;\n  }\n\n  .item-checkbox {\n    padding: 8px 0;\n  }\n\n  .item-info {\n    gap: 6px;\n  }\n}\n\n/* 自定义滚动条样式 */\n.items-list::-webkit-scrollbar {\n  width: 8px;\n}\n\n.items-list::-webkit-scrollbar-track {\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 4px;\n}\n\n.items-list::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.6) 0%, rgba(32, 255, 77, 0.4) 100%);\n  border-radius: 4px;\n  border: 1px solid rgba(0, 212, 170, 0.2);\n}\n\n.items-list::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.8) 0%, rgba(32, 255, 77, 0.6) 100%);\n}\n\n.download-modal::-webkit-scrollbar {\n  width: 10px;\n}\n\n.download-modal::-webkit-scrollbar-track {\n  background: rgba(0, 212, 170, 0.1);\n  border-radius: 5px;\n}\n\n.download-modal::-webkit-scrollbar-thumb {\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.6) 0%, rgba(32, 255, 77, 0.4) 100%);\n  border-radius: 5px;\n  border: 1px solid rgba(0, 212, 170, 0.2);\n}\n\n.download-modal::-webkit-scrollbar-thumb:hover {\n  background: linear-gradient(135deg, rgba(0, 212, 170, 0.8) 0%, rgba(32, 255, 77, 0.6) 100%);\n}\n", "/* 模块六主页面样式 - 高科技风格 + 金黄色主题 */\r\n.module6_module-six {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  color: #ffffff;\r\n  font-family: '<PERSON><PERSON><PERSON>', sans-serif;\r\n  overflow: hidden; /* Prevent double scrollbars */\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n/* 背景动画 */\r\n.module6_module-six-background {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.module6_module-six-background .module6_particle {\r\n  position: absolute;\r\n  width: 2px;\r\n  height: 2px;\r\n  background: #ffd700;\r\n  border-radius: 50%;\r\n  animation: module6_float 6s ease-in-out infinite;\r\n}\r\n\r\n.module6_module-six-background .module6_particle:nth-child(1) {\r\n  top: 20%;\r\n  left: 10%;\r\n  animation-delay: 0s;\r\n}\r\n\r\n.module6_module-six-background .module6_particle:nth-child(2) {\r\n  top: 60%;\r\n  left: 80%;\r\n  animation-delay: 2s;\r\n}\r\n\r\n.module6_module-six-background .module6_particle:nth-child(3) {\r\n  top: 80%;\r\n  left: 30%;\r\n  animation-delay: 4s;\r\n}\r\n\r\n.module6_module-six-background .module6_grid-lines {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background-image: \r\n    linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),\r\n    linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px);\r\n  background-size: 50px 50px;\r\n  animation: module6_gridMove 20s linear infinite;\r\n}\r\n\r\n/* 顶部导航栏 */\r\n.module6_module-six-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 15px 30px;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border-bottom: 2px solid rgba(255, 215, 0, 0.5);\r\n  backdrop-filter: blur(15px);\r\n  position: relative;\r\n  z-index: 10;\r\n}\r\n\r\n.module6_header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n  flex: 1;\r\n}\r\n\r\n.module6_header-center {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 2;\r\n}\r\n\r\n.module6_header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  flex: 1;\r\n}\r\n\r\n.module6_back-button {\r\n  padding: 10px 20px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 8px;\r\n  color: #000;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_back-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_module-title {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 2.4rem;\r\n  font-weight: 700;\r\n  color: #ffd700;\r\n  text-shadow: 0 0 15px rgba(255, 215, 0, 0.6);\r\n  margin: 0;\r\n}\r\n\r\n.module6_header-right {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n/* 状态按钮样式（类似返回首页按钮） */\r\n.module6_status-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 10px 20px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 8px;\r\n  color: #000;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n  min-width: 140px;\r\n  justify-content: center;\r\n  position: relative;\r\n}\r\n\r\n.module6_status-button:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n/* 不同状态的颜色变体 - 统一使用黄色主题 */\r\n.module6_status-button.success {\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_status-button.success:hover {\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_status-button.error {\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_status-button.error:hover {\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_status-button.syncing {\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_status-button.syncing:hover {\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n/* 刷新图标样式 */\r\n.module6_refresh-icon {\r\n  font-size: 0.8rem;\r\n  margin-left: 5px;\r\n  transition: transform 0.3s ease;\r\n  opacity: 0.8;\r\n}\r\n\r\n.module6_status-button:hover .module6_refresh-icon {\r\n  opacity: 1;\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 刷新中状态样式 */\r\n.module6_status-button.refreshing {\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  animation: module6_refresh-pulse 1.5s ease-in-out infinite;\r\n}\r\n\r\n.module6_status-button.refreshing .module6_refresh-icon {\r\n  animation: module6_refresh-spin 1s linear infinite;\r\n}\r\n\r\n/* 刷新动画 */\r\n@keyframes module6_refresh-spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n@keyframes module6_refresh-pulse {\r\n  0%, 100% { \r\n    transform: translateY(0);\r\n    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n  }\r\n  50% { \r\n    transform: translateY(-1px);\r\n    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n  }\r\n}\r\n\r\n/* 保留原有的状态指示器样式 */\r\n.module6_sync-status {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #ffd700;\r\n  font-size: 0.9rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.module6_status-indicator {\r\n  width: 8px;\r\n  height: 8px;\r\n  border-radius: 50%;\r\n  animation: module6_pulse 2s ease-in-out infinite;\r\n}\r\n\r\n.module6_status-indicator.success {\r\n  background: #000;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.module6_status-indicator.error {\r\n  background: #000;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n.module6_status-indicator.syncing {\r\n  background: #000;\r\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.6);\r\n}\r\n\r\n/* 表选择器 */\r\n.module6_table-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 15px;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.4);\r\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\r\n  position: relative;\r\n  z-index: 10;\r\n  min-height: 60px;\r\n}\r\n\r\n.module6_table-select-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.module6_action-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  width: 100%;\r\n  gap: 15px;\r\n  position: relative;\r\n}\r\n\r\n.module6_table-selector label {\r\n  font-size: 1.3rem;\r\n  color: #ffd700;\r\n  font-weight: 700;\r\n  white-space: nowrap;\r\n}\r\n\r\n.module6_table-dropdown {\r\n  padding: 10px 15px;\r\n  background: rgba(0, 0, 0, 0.6);\r\n  border: 2px solid rgba(255, 215, 0, 0.5);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  min-width: 200px;\r\n}\r\n\r\n.module6_table-dropdown:focus {\r\n  outline: none;\r\n  border-color: #ffd700;\r\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_table-dropdown option {\r\n  background: #1a1a2e;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 主要内容区域 */\r\n.module6_module-six-content {\r\n  position: relative;\r\n  z-index: 10;\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 0;\r\n  margin: 0;\r\n  overflow: auto;\r\n}\r\n\r\n/* 合并到一行的统一头部布局 */\r\n.module6_unified-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 15px 15px 10px 15px;\r\n  flex-shrink: 0;\r\n  margin-bottom: 15px;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  border-radius: 12px;\r\n  padding: 15px;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n  gap: 20px;\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 左侧和右侧的指标/工作选择器 */\r\n.module6_header-section {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 15px 25px;\r\n  background: transparent;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 8px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  min-width: 180px;\r\n  justify-content: center;\r\n}\r\n\r\n.module6_header-section::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.module6_header-section:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.module6_header-section.active {\r\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\r\n  color: #ffd700;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.2);\r\n  border-color: rgba(255, 215, 0, 0.6);\r\n}\r\n\r\n.module6_header-section:hover {\r\n  color: #ffd700;\r\n  transform: translateY(-2px);\r\n  border-color: rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_section-icon {\r\n  font-size: 1.3rem;\r\n  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));\r\n}\r\n\r\n.module6_section-title {\r\n  font-weight: 700;\r\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\r\n  white-space: nowrap;\r\n}\r\n\r\n/* 月份选择器 */\r\n.module6_month-selector {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  background: transparent;\r\n  border-radius: 8px;\r\n  padding: 12px 20px;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.module6_month-selector .module6_month-nav-btn {\r\n  width: 35px;\r\n  height: 35px;\r\n  background: rgba(255, 215, 0, 0.2);\r\n  border: 1px solid rgba(255, 215, 0, 0.5);\r\n  border-radius: 6px;\r\n  color: #ffd700;\r\n  font-size: 1rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.module6_month-selector .module6_month-nav-btn:hover {\r\n  background: rgba(255, 215, 0, 0.3);\r\n  border-color: rgba(255, 215, 0, 0.7);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.module6_month-selector .module6_month-display {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.1rem;\r\n  font-weight: 700;\r\n  color: #ffd700;\r\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\r\n  min-width: 100px;\r\n  text-align: center;\r\n}\r\n\r\n\r\n\r\n\r\n\r\n/* 加载状态 */\r\n.module6_loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 20px;\r\n  gap: 20px;\r\n}\r\n\r\n.module6_loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid rgba(255, 215, 0, 0.3);\r\n  border-top: 3px solid #ffd700;\r\n  border-radius: 50%;\r\n  animation: module6_spin 1s linear infinite;\r\n}\r\n\r\n.module6_loading-text {\r\n  color: #ffd700;\r\n  font-size: 1.1rem;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes module6_float {\r\n  0%, 100% {\r\n    transform: translateY(0px) rotate(0deg);\r\n    opacity: 0.7;\r\n  }\r\n  50% {\r\n    transform: translateY(-20px) rotate(180deg);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n@keyframes module6_gridMove {\r\n  0% {\r\n    transform: translate(0, 0);\r\n  }\r\n  100% {\r\n    transform: translate(50px, 50px);\r\n  }\r\n}\r\n\r\n@keyframes module6_pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n    transform: scale(1);\r\n  }\r\n  50% {\r\n    opacity: 0.7;\r\n    transform: scale(1.1);\r\n  }\r\n}\r\n\r\n@keyframes module6_spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 表格样式优化 - 移除重复定义，使用KPITable.css中的样式 */\r\n\r\n/* 减少行间距 */\r\n.module6_kpi-table tbody tr {\r\n  height: 40px;\r\n}\r\n\r\n.module6_kpi-table tbody tr:nth-child(even) {\r\n  background: rgba(255, 255, 255, 0.02);\r\n}\r\n\r\n.module6_kpi-table tbody tr:hover {\r\n  background: rgba(255, 215, 0, 0.05);\r\n}\r\n\r\n/* 特殊列样式 - 保持与KPITable.css一致 */\r\n.module6_sequence-cell,\r\n.module6_indicator-cell {\r\n  background: rgba(255, 215, 0, 0.05);\r\n  font-weight: 600;\r\n}\r\n\r\n/* 序号列特定样式覆盖 - 优化宽度以容纳\"序号\"两个字 */\r\n.module6_sequence-cell {\r\n  min-width: 50px !important;\r\n  max-width: 55px !important;\r\n  width: 55px !important;\r\n  padding: 12px 4px !important;\r\n}\r\n\r\n.module6_month-cell {\r\n  /* 优化后的月份列宽度 */\r\n  min-width: 200px;\r\n}\r\n\r\n.module6_month-cell.editable {\r\n  cursor: pointer;\r\n  transition: background-color 0.2s ease;\r\n}\r\n\r\n.module6_month-cell.editable:hover {\r\n  background: rgba(255, 215, 0, 0.1);\r\n}\r\n\r\n.module6_month-cell.readonly {\r\n  background: rgba(128, 128, 128, 0.1);\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n/* 滚动条样式 */\r\n.module6_table-container::-webkit-scrollbar {\r\n  width: 8px;\r\n  height: 8px;\r\n}\r\n\r\n.module6_table-container::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_table-container::-webkit-scrollbar-thumb {\r\n  background: rgba(255, 215, 0, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_table-container::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(255, 215, 0, 0.7);\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .module6_module-title {\r\n    font-size: 2rem;\r\n  }\r\n  \r\n  .module6_unified-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    padding: 15px;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .module6_header-section {\r\n    min-width: 160px;\r\n    padding: 12px 20px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .module6_module-six-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    padding: 15px 20px;\r\n  }\r\n  \r\n  .module6_header-left {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_module-title {\r\n    font-size: 1.6rem;\r\n    text-align: center;\r\n  }\r\n  \r\n  .module6_table-selector {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_table-dropdown {\r\n    min-width: 150px;\r\n  }\r\n  \r\n  .module6_module-six-content {\r\n    padding: 0;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n/* 数据可视化容器样式 */\r\n.module6_data-visualization-container {\r\n  margin: 20px 0;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 15px;\r\n  border: 1px solid rgba(255, 215, 0, 0.2);\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* 数据可视化占位符样式 */\r\n.module6_visualization-placeholder {\r\n  text-align: center;\r\n  padding: 60px 20px;\r\n  color: #ffd700;\r\n  font-family: 'Rajdhani', sans-serif;\r\n}\r\n\r\n.module6_visualization-placeholder h3 {\r\n  font-size: 24px;\r\n  margin-bottom: 15px;\r\n  font-weight: 700;\r\n  text-transform: uppercase;\r\n  letter-spacing: 2px;\r\n}\r\n\r\n.module6_visualization-placeholder p {\r\n  font-size: 16px;\r\n  opacity: 0.8;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 数据可视化按钮样式 */\r\n.module6_visualization-btn {\r\n  padding: 12px 20px;\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.2) 100%);\r\n  border: 2px solid rgba(255, 215, 0, 0.5);\r\n  border-radius: 8px;\r\n  color: #ffd700;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  text-transform: uppercase;\r\n  letter-spacing: 1px;\r\n  position: absolute;\r\n  left: 45%;\r\n  top: 50%;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.module6_visualization-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2) 0%, rgba(255, 215, 0, 0.3) 100%);\r\n  border-color: #ffd700;\r\n  transform: translate(-50%, -50%) translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_visualization-btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: translate(-50%, -50%);\r\n}\r\n\r\n.module6_btn-icon {\r\n  font-size: 20px;\r\n}\r\n\r\n/* 数据可视化弹窗模态框样式 */\r\n.module6_visualization-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.85);\r\n  backdrop-filter: blur(8px);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 9999;\r\n  animation: module6_fadeIn 0.4s ease-out;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_visualization-modal {\r\n  width: 100vw; /* 扩大到全屏宽度 */\r\n  height: 100vh; /* 扩大到全屏高度 */\r\n  max-width: none; /* 移除最大宽度限制 */\r\n  max-height: none; /* 移除最大高度限制 */\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\r\n  border-radius: 0; /* 移除圆角，充分利用边缘 */\r\n  border: 3px solid #ffd700;\r\n  box-shadow:\r\n    0 25px 80px rgba(255, 215, 0, 0.4),\r\n    0 0 0 1px rgba(255, 215, 0, 0.1),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  animation: module6_slideInUp 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\r\n  cursor: default;\r\n  outline: none;\r\n}\r\n\r\n.module6_modal-header {\r\n  display: flex;\r\n  justify-content: center; /* 标题居中显示 */\r\n  align-items: center;\r\n  padding: 8px 35px; /* 最小化垂直padding，为下方内容预留更多空间 */\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.08) 100%);\r\n  border-bottom: 2px solid rgba(255, 215, 0, 0.4);\r\n  backdrop-filter: blur(10px);\r\n  position: relative;\r\n  min-height: 45px; /* 最小化标题行高度 */\r\n}\r\n\r\n.module6_modal-header::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 1px;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.6), transparent);\r\n}\r\n\r\n.module6_modal-title {\r\n  color: #ffd700;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 26px; /* 调大二号字体 */\r\n  font-weight: 700;\r\n  margin: 0 auto; /* 使用margin auto实现水平居中 */\r\n  text-transform: uppercase;\r\n  letter-spacing: 2px;\r\n  text-align: center; /* 确保标题文本居中 */\r\n  flex: 1; /* 让标题占据剩余空间 */\r\n  display: flex; /* 使用flex布局 */\r\n  align-items: center; /* 垂直居中 */\r\n  justify-content: center; /* 水平居中 */\r\n}\r\n\r\n.module6_modal-close-btn {\r\n  width: 40px; /* 稍微减小尺寸 */\r\n  height: 40px;\r\n  background: linear-gradient(135deg, rgba(255, 0, 0, 0.1) 0%, rgba(255, 0, 0, 0.05) 100%);\r\n  border: 2px solid rgba(255, 0, 0, 0.4);\r\n  border-radius: 50%;\r\n  color: #ff6b6b;\r\n  font-size: 24px; /* 稍微减小字体 */\r\n  font-weight: bold;\r\n  cursor: pointer;\r\n  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute; /* 改为绝对定位 */\r\n  top: 15px; /* 定位到右上角 */\r\n  right: 20px;\r\n  overflow: hidden;\r\n  z-index: 10; /* 确保在最上层 */\r\n}\r\n\r\n.module6_modal-close-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 0;\r\n  height: 0;\r\n  background: radial-gradient(circle, rgba(255, 0, 0, 0.3) 0%, transparent 70%);\r\n  border-radius: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.module6_modal-close-btn:hover {\r\n  background: linear-gradient(135deg, rgba(255, 0, 0, 0.25) 0%, rgba(255, 0, 0, 0.15) 100%);\r\n  border-color: #ff4757;\r\n  transform: rotate(90deg) scale(1.1);\r\n  color: #ff3742;\r\n  box-shadow: 0 0 20px rgba(255, 0, 0, 0.4);\r\n}\r\n\r\n.module6_modal-close-btn:hover::before {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.module6_modal-close-btn:active {\r\n  transform: rotate(90deg) scale(0.95);\r\n}\r\n\r\n.module6_modal-content {\r\n  flex: 1; /* 使用flex: 1占用剩余空间，移除固定高度计算 */\r\n  padding: 0; /* 完全移除padding，最大化利用全屏空间 */\r\n  overflow: hidden; /* 改为hidden，让内容完全填充容器 */\r\n  position: relative;\r\n  display: flex; /* 添加flex布局 */\r\n  flex-direction: column; /* 垂直方向布局 */\r\n}\r\n\r\n.module6_modal-content::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.module6_modal-content::-webkit-scrollbar-track {\r\n  background: rgba(255, 215, 0, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_modal-content::-webkit-scrollbar-thumb {\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.6) 0%, rgba(255, 215, 0, 0.4) 100%);\r\n  border-radius: 4px;\r\n  border: 1px solid rgba(255, 215, 0, 0.2);\r\n}\r\n\r\n.module6_modal-content::-webkit-scrollbar-thumb:hover {\r\n  background: linear-gradient(135deg, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0.6) 100%);\r\n}\r\n\r\n/* 弹窗动画 */\r\n@keyframes module6_fadeIn {\r\n  0% {\r\n    opacity: 0;\r\n    backdrop-filter: blur(0px);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    backdrop-filter: blur(8px);\r\n  }\r\n}\r\n\r\n@keyframes module6_slideInUp {\r\n  0% {\r\n    opacity: 0;\r\n    transform: translateY(60px) scale(0.85) rotateX(10deg);\r\n    filter: blur(5px);\r\n  }\r\n  50% {\r\n    opacity: 0.8;\r\n    transform: translateY(-10px) scale(1.02) rotateX(0deg);\r\n    filter: blur(2px);\r\n  }\r\n  100% {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1) rotateX(0deg);\r\n    filter: blur(0px);\r\n  }\r\n}\r\n\r\n/* 弹窗关闭动画 */\r\n@keyframes module6_fadeOut {\r\n  0% {\r\n    opacity: 1;\r\n    backdrop-filter: blur(8px);\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n    backdrop-filter: blur(0px);\r\n  }\r\n}\r\n\r\n@keyframes module6_slideOutDown {\r\n  0% {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1) rotateX(0deg);\r\n    filter: blur(0px);\r\n  }\r\n  100% {\r\n    opacity: 0;\r\n    transform: translateY(40px) scale(0.9) rotateX(-5deg);\r\n    filter: blur(3px);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 1200px) {\r\n  .module6_visualization-modal {\r\n    width: 100vw; /* 保持全屏宽度 */\r\n    height: 100vh; /* 保持全屏高度 */\r\n    border-radius: 0; /* 保持无圆角 */\r\n  }\r\n\r\n  .module6_modal-header {\r\n    padding: 12px 20px; /* 进一步减少padding */\r\n    min-height: 50px;\r\n  }\r\n\r\n  .module6_modal-title {\r\n    font-size: 26px; /* 设置字体为26px */\r\n    text-align: center; /* 居中显示 */\r\n    margin: 0 auto; /* 使用margin auto实现水平居中 */\r\n    flex: 1; /* 让标题占据剩余空间 */\r\n    display: flex; /* 使用flex布局 */\r\n    align-items: center; /* 垂直居中 */\r\n    justify-content: center; /* 水平居中 */\r\n  }\r\n\r\n  .module6_modal-content {\r\n    padding: 0; /* 完全移除padding，最大化空间利用 */\r\n    overflow: hidden; /* 改为hidden，让内容完全填充容器 */\r\n    display: flex; /* 添加flex布局 */\r\n    flex-direction: column; /* 垂直方向布局 */\r\n  }\r\n\r\n  .module6_modal-close-btn {\r\n    top: 12px;\r\n    right: 15px;\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 20px;\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .module6_table-selector {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n\r\n  .module6_table-select-group {\r\n    justify-content: center;\r\n  }\r\n\r\n  .module6_action-buttons {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n    position: relative;\r\n  }\r\n  \r\n  .module6_visualization-btn {\r\n    position: static;\r\n    transform: none;\r\n  }\r\n  \r\n  .module6_visualization-btn:hover {\r\n    transform: translateY(-2px);\r\n  }\r\n  \r\n  .module6_visualization-btn:disabled {\r\n    transform: none;\r\n  }\r\n\r\n  .module6_visualization-modal {\r\n    width: 100vw;\r\n    height: 100vh;\r\n    border-radius: 0;\r\n    border: none;\r\n    max-width: none;\r\n    max-height: none;\r\n  }\r\n\r\n  .module6_modal-header {\r\n    padding: 10px 15px; /* 进一步减少移动端padding */\r\n    min-height: 45px;\r\n  }\r\n\r\n  .module6_modal-title {\r\n    font-size: 16px; /* 移动端更小的字体 */\r\n    letter-spacing: 1px;\r\n  }\r\n\r\n  .module6_modal-close-btn {\r\n    width: 35px;\r\n    height: 35px;\r\n    font-size: 20px;\r\n    top: 8px;\r\n    right: 10px;\r\n  }\r\n\r\n  .module6_modal-content {\r\n    padding: 0; /* 移动端也完全移除padding，最大化空间利用 */\r\n    overflow: hidden; /* 改为hidden，让内容完全填充容器 */\r\n    display: flex; /* 添加flex布局 */\r\n    flex-direction: column; /* 垂直方向布局 */\r\n  }\r\n\r\n  .module6_visualization-btn {\r\n    font-size: 12px;\r\n    padding: 10px 16px;\r\n  }\r\n}", "/* KPI表格样式 - 模块六 */\n\n/* 表格控制区域 */\n.module6_table-controls {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20px 30px;\n  background: rgba(0, 0, 0, 0.6);\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n}\n\n/* 月份导航 */\n.module6_month-navigation {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n}\n\n.module6_month-nav-btn {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-size: 1.2rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_month-nav-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n.module6_month-display {\n  font-family: 'Orbitron', monospace;\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #ffd700;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\n  min-width: 120px;\n  text-align: center;\n}\n\n/* 导出按钮 */\n.module6_export-btn {\n  padding: 12px 24px;\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\n  border: none;\n  border-radius: 8px;\n  color: #000;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 1rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\n}\n\n.module6_export-btn:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\n}\n\n\n\n/* 表格容器 - 处理滚动 */\n.module6_table-container {\n  flex: 1;\n  overflow-y: auto;\n  border-radius: 12px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  margin: 0 15px 15px 15px;\n}\n\n/* 主表格 - 合并容器样式，修复表头固定，移除外边框 */\n.module6_kpi-table {\n  width: 100%;\n  table-layout: fixed !important;\n  border-collapse: separate;\n  border-spacing: 0;\n  background: rgba(0, 0, 0, 0.4);\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.95rem;\n  position: relative;\n  /* 确保表格本身没有外边框 */\n  border: none !important;\n  /* 移除所有可能的边框和outline */\n  outline: none !important;\n}\n\n/* 表头 - 修复固定表头 */\n.module6_kpi-table thead {\n  /* 移除thead的sticky，只在th上设置 */\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\n}\n\n.module6_kpi-table th {\n  padding: 15px 12px;\n  text-align: center;\n  vertical-align: middle;\n  font-weight: 700;\n  color: #ffd700;\n  border-bottom: 2px solid rgba(255, 215, 0, 0.4);\n  /* 移除所有其他边框 */\n  border-top: none;\n  border-left: none;\n  border-right: none;\n  /* 强化sticky定位 - 确保表头固定 */\n  position: sticky !important;\n  top: 0 !important;\n  z-index: 1000 !important;\n  background: rgba(0, 0, 0, 0.95) !important;\n  backdrop-filter: blur(10px);\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\n  white-space: nowrap;\n  min-width: 100px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);\n  /* 确保背景完全覆盖 */\n  background-clip: padding-box;\n}\n\n/* 序号列表头样式 - 限制宽度 */\n.module6_kpi-table th:first-child {\n  min-width: 50px !important;\n  max-width: 55px !important;\n  width: 55px !important;\n  padding: 15px 4px !important;\n}\n\n/* 权重列表头样式 - 限制宽度（第4列） */\n.module6_kpi-table th:nth-child(4) {\n  min-width: 50px !important;\n  max-width: 55px !important;\n  width: 55px !important;\n  padding: 15px 4px !important;\n}\n\n.module6_month-header {\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05)) !important;\n  font-size: 0.9rem;\n  min-width: 120px;\n}\n\n/* 月份工作计划列表头 - 适当缩小宽度 */\n.module6_month-header:nth-child(3n+7) { /* 工作计划列 */\n  min-width: 150px !important;\n  max-width: 180px !important;\n  width: 160px !important;\n}\n\n/* 月份完成情况列表头 - 适当缩小宽度 */\n.module6_month-header:nth-child(3n+8) { /* 完成情况列 */\n  min-width: 150px !important;\n  max-width: 180px !important;\n  width: 160px !important;\n}\n\n/* 月份评分列表头 - 最小宽度，刚好显示\"12月评分\" */\n.module6_month-header:nth-child(3n+9) { /* 评分列 */\n  min-width: 80px !important;\n  max-width: 90px !important;\n  width: 85px !important;\n}\n\n/* 表格行 - 确保最后一行没有边框 */\n.module6_kpi-table tbody tr {\n  border-bottom: 1px solid rgba(255, 215, 0, 0.2);\n  transition: all 0.3s ease;\n}\n\n/* 移除最后一行的下边框，防止外边框线 */\n.module6_kpi-table tbody tr:last-child {\n  border-bottom: none !important;\n}\n\n.module6_kpi-table tbody tr:hover {\n  background: rgba(255, 215, 0, 0.1);\n  transform: scale(1.001);\n}\n\n.module6_kpi-table tbody tr:nth-child(even) {\n  background: rgba(255, 215, 0, 0.05);\n}\n\n.module6_kpi-table tbody tr:nth-child(even):hover {\n  background: rgba(255, 215, 0, 0.15);\n}\n\n/* 表格单元格 - 垂直居中优化，移除重复边框 */\n.module6_kpi-table td {\n  padding: 12px 8px;\n  text-align: center;\n  vertical-align: middle;\n  /* 移除所有边框 */\n  border: none;\n  position: relative;\n  min-width: 100px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  max-width: 300px;\n  height: auto;\n  min-height: 50px;\n}\n\n/* 固定列样式 - 垂直居中优化，优化序号列宽度 */\n.module6_sequence-cell {\n  background: rgba(255, 215, 0, 0.1);\n  font-weight: 700;\n  color: #ffd700;\n  /* 序号列宽度优化 - 仅能容纳\"序号\"两个字的最小宽度 */\n  min-width: 50px !important;\n  max-width: 55px !important;\n  width: 55px !important;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 4px !important;\n  font-size: 0.9rem;\n}\n\n.module6_indicator-cell {\n  background: rgba(255, 215, 0, 0.05);\n  text-align: left;\n  padding: 12px 15px;\n  font-weight: 600;\n  /* 指标列宽度 - 显示8个汉字 */\n  min-width: 160px !important;\n  max-width: 200px !important;\n  width: 180px !important;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n}\n\n.module6_target-cell {\n  text-align: left;\n  padding: 12px 15px;\n  /* 目标值列宽度 - 显示8个汉字 */\n  min-width: 160px !important;\n  max-width: 200px !important;\n  width: 180px !important;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n}\n\n.module6_weight-cell {\n  font-weight: 700;\n  color: #ffd700;\n  /* 权重列宽度优化 - 与序号列相同的最小宽度 */\n  min-width: 50px !important;\n  max-width: 55px !important;\n  width: 55px !important;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 4px !important;\n}\n\n.module6_category-cell {\n  /* 指标分类列宽度 - 显示6个汉字 */\n  min-width: 120px !important;\n  max-width: 140px !important;\n  width: 130px !important;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.4;\n  vertical-align: middle;\n  text-align: center;\n}\n\n.module6_frequency-cell {\n  /* 频次列保持紧凑 */\n  min-width: 90px;\n  max-width: 110px;\n  width: 100px;\n  text-align: center;\n  vertical-align: middle;\n  padding: 12px 6px;\n}\n\n/* 月份单元格 - 垂直居中优化，扩大显示空间 */\n.module6_month-cell {\n  /* 由于序号列减小，月份列可以适当扩大 */\n  min-width: 200px;\n  max-width: 380px;\n  position: relative;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  vertical-align: middle;\n  padding: 12px 15px;\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  line-height: 1.5;\n  min-height: 60px;\n  text-align: center;\n}\n\n/* 月份工作计划和完成情况单元格 - 适当缩小宽度 */\n.module6_month-cell:nth-child(3n+7), /* 工作计划列 */\n.module6_month-cell:nth-child(3n+8) { /* 完成情况列 */\n  min-width: 150px !important;\n  max-width: 180px !important;\n  width: 160px !important;\n  padding: 12px 15px !important;\n}\n\n/* 月份评分单元格 - 最小宽度，刚好显示评分数字 */\n.module6_month-cell:nth-child(3n+9) { /* 评分列 */\n  min-width: 80px !important;\n  max-width: 90px !important;\n  width: 85px !important;\n  padding: 12px 8px !important;\n}\n\n/* 可编辑单元格样式 */\n.module6_month-cell.editable {\n  background: rgba(255, 215, 0, 0.05);\n  border-left: 3px solid rgba(255, 215, 0, 0.3);\n}\n\n.module6_month-cell.editable:hover {\n  background: rgba(255, 215, 0, 0.15);\n  box-shadow: inset 0 0 10px rgba(255, 215, 0, 0.2);\n  transform: scale(1.02);\n}\n\n/* 只读单元格样式 */\n.module6_month-cell.readonly {\n  background: rgba(128, 128, 128, 0.15);\n  color: rgba(255, 255, 255, 0.8);\n  cursor: not-allowed;\n  border-left: 3px solid rgba(128, 128, 128, 0.5);\n}\n\n.module6_month-cell.readonly:hover {\n  background: rgba(128, 128, 128, 0.2);\n}\n\n/* 匹配状态指示 */\n.module6_month-cell.matched {\n  border-top: 2px solid rgba(0, 255, 0, 0.4);\n}\n\n.module6_month-cell.unmatched {\n  border-top: 2px solid rgba(255, 165, 0, 0.4);\n}\n\n/* 只读指示器 */\n.module6_readonly-indicator {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  font-size: 0.8rem;\n  opacity: 0.7;\n  background: rgba(0, 0, 0, 0.3);\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 5;\n}\n\n/* 单元格内容容器 - 垂直居中优化 */\n.module6_cell-content {\n  width: 100%;\n  min-height: 24px;\n  line-height: 1.5;\n  word-break: break-word;\n  white-space: pre-wrap;\n  overflow-wrap: break-word;\n  text-align: center;\n  font-size: 0.9rem;\n  color: rgba(255, 255, 255, 0.95);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-direction: column;\n}\n\n/* 单元格行显示 */\n.module6_cell-line {\n  margin-bottom: 4px;\n  display: block;\n}\n\n.module6_cell-line:last-child {\n  margin-bottom: 0;\n}\n\n.module6_cell-line:empty {\n  display: none;\n}\n\n/* 长内容显示 */\n.module6_cell-long-content {\n  max-height: 150px;\n  overflow-y: auto;\n  scrollbar-width: thin;\n  scrollbar-color: rgba(255, 215, 0, 0.5) transparent;\n  padding-right: 4px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-thumb {\n  background: rgba(255, 215, 0, 0.5);\n  border-radius: 3px;\n}\n\n.module6_cell-long-content::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 215, 0, 0.7);\n}\n\n/* 内容截断提示 */\n.module6_cell-content.truncated::after {\n  content: \"...\";\n  color: rgba(255, 215, 0, 0.7);\n  font-weight: bold;\n}\n\n/* 空内容占位符 */\n.module6_cell-content:empty::before {\n  content: \"-\";\n  color: rgba(255, 255, 255, 0.3);\n  font-style: italic;\n}\n\n/* 输入框和文本域样式 - 优化版本 */\n.module6_cell-input {\n  width: 100%;\n  min-height: 30px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 6px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  text-align: center;\n  outline: none;\n  padding: 8px;\n  transition: all 0.3s ease;\n}\n\n.module6_cell-input:focus {\n  background: rgba(255, 215, 0, 0.2);\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);\n  transform: scale(1.02);\n}\n\n.module6_cell-textarea {\n  width: 100%;\n  min-height: 60px;\n  max-height: 120px;\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 6px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 0.9rem;\n  outline: none;\n  padding: 8px;\n  resize: vertical;\n  line-height: 1.4;\n  transition: all 0.3s ease;\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n\n.module6_cell-textarea:focus {\n  background: rgba(255, 215, 0, 0.2);\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);\n  transform: scale(1.02);\n}\n\n/* 文本域滚动条样式 */\n.module6_cell-textarea::-webkit-scrollbar {\n  width: 6px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 3px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-thumb {\n  background: rgba(255, 215, 0, 0.5);\n  border-radius: 3px;\n}\n\n.module6_cell-textarea::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 215, 0, 0.7);\n}\n\n/* 同步状态工具栏 */\n.module6_sync-toolbar {\n  background: rgba(0, 0, 0, 0.8);\n  border: 1px solid rgba(255, 215, 0, 0.3);\n  border-radius: 8px;\n  padding: 12px 16px;\n  margin-bottom: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.module6_pending-changes,\n.module6_sync-errors {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.module6_pending-count,\n.module6_error-count {\n  color: #ffd700;\n  font-weight: 600;\n  font-size: 0.9rem;\n}\n\n.module6_retry-btn,\n.module6_clear-btn,\n.module6_clear-errors-btn,\n.module6_show-errors-btn {\n  padding: 6px 12px;\n  border: none;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.module6_retry-btn {\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n}\n\n.module6_retry-btn:hover {\n  background: linear-gradient(45deg, #45a049, #3d8b40);\n  transform: translateY(-1px);\n}\n\n.module6_clear-btn,\n.module6_clear-errors-btn {\n  background: linear-gradient(45deg, #f44336, #da190b);\n  color: white;\n}\n\n.module6_clear-btn:hover,\n.module6_clear-errors-btn:hover {\n  background: linear-gradient(45deg, #da190b, #c62828);\n  transform: translateY(-1px);\n}\n\n.module6_show-errors-btn {\n  background: linear-gradient(45deg, #2196F3, #1976D2);\n  color: white;\n}\n\n.module6_show-errors-btn:hover {\n  background: linear-gradient(45deg, #1976D2, #1565C0);\n  transform: translateY(-1px);\n}\n\n/* 错误模态框 */\n.module6_modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  backdrop-filter: blur(5px);\n}\n\n.module6_error-modal {\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 12px;\n  width: 90%;\n  max-width: 600px;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);\n}\n\n.module6_modal-header {\n  background: rgba(255, 215, 0, 0.1);\n  padding: 16px 20px;\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.module6_modal-header h3 {\n  color: #ffd700;\n  margin: 0;\n  font-family: 'Rajdhani', sans-serif;\n  font-weight: 700;\n}\n\n.module6_modal-close {\n  background: none;\n  border: none;\n  color: #ffd700;\n  font-size: 1.2rem;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: all 0.3s ease;\n}\n\n.module6_modal-close:hover {\n  background: rgba(255, 215, 0, 0.2);\n  transform: scale(1.1);\n}\n\n.module6_modal-content {\n  padding: 20px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.module6_error-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.module6_error-item {\n  background: rgba(255, 0, 0, 0.1);\n  border: 1px solid rgba(255, 0, 0, 0.3);\n  border-radius: 8px;\n  padding: 12px;\n}\n\n.module6_error-field {\n  color: #ffd700;\n  font-weight: 600;\n  font-size: 0.9rem;\n  margin-bottom: 4px;\n}\n\n.module6_error-message {\n  color: #ff6b6b;\n  font-weight: 500;\n  margin-bottom: 4px;\n}\n\n.module6_error-time {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 0.8rem;\n  margin-bottom: 4px;\n}\n\n.module6_error-value {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 0.8rem;\n  font-style: italic;\n}\n\n.module6_modal-footer {\n  background: rgba(0, 0, 0, 0.3);\n  padding: 16px 20px;\n  border-top: 1px solid rgba(255, 215, 0, 0.3);\n  display: flex;\n  gap: 12px;\n  justify-content: flex-end;\n}\n\n.module6_retry-all-btn,\n.module6_clear-all-btn {\n  padding: 10px 20px;\n  border: none;\n  border-radius: 6px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.module6_retry-all-btn {\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n}\n\n.module6_retry-all-btn:hover {\n  background: linear-gradient(45deg, #45a049, #3d8b40);\n  transform: translateY(-1px);\n}\n\n.module6_clear-all-btn {\n  background: linear-gradient(45deg, #f44336, #da190b);\n  color: white;\n}\n\n.module6_clear-all-btn:hover {\n  background: linear-gradient(45deg, #da190b, #c62828);\n  transform: translateY(-1px);\n}\n\n/* 合并单元格样式 */\n.module6_merged-cell {\n  background: rgba(255, 215, 0, 0.1);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n}\n\n.module6_merged-cell::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);\n  pointer-events: none;\n}\n\n/* 响应式设计 */\n@media (max-width: 1200px) {\n  .module6_table-controls {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n  }\n  \n  .module6_month-navigation {\n    justify-content: center;\n  }\n  \n  .module6_export-btn {\n    align-self: center;\n  }\n  \n  .module6_kpi-table {\n    font-size: 0.85rem;\n  }\n  \n  .module6_kpi-table th,\n  .module6_kpi-table td {\n    padding: 8px 6px;\n  }\n  \n  /* 序号列响应式优化 */\n  .module6_sequence-cell {\n    min-width: 45px;\n    max-width: 50px;\n    width: 50px;\n    padding: 8px 2px;\n    font-size: 0.8rem;\n  }\n  \n  /* 权重列响应式优化 */\n  .module6_weight-cell {\n    min-width: 45px;\n    max-width: 50px;\n    width: 50px;\n    padding: 8px 2px;\n    font-size: 0.8rem;\n  }\n  \n  .module6_month-header {\n    min-width: 100px;\n    font-size: 0.8rem;\n  }\n  \n  .module6_month-cell {\n    min-width: 100px;\n  }\n}\n\n@media (max-width: 768px) {\n  .module6_table-controls {\n    padding: 15px 20px;\n  }\n  \n  .module6_month-display {\n    font-size: 1rem;\n    min-width: 100px;\n  }\n  \n  .module6_month-nav-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 1rem;\n  }\n  \n  .module6_export-btn {\n    padding: 10px 20px;\n    font-size: 0.9rem;\n  }\n  \n  .module6_kpi-table {\n    font-size: 0.8rem;\n  }\n  \n  .module6_kpi-table th,\n  .module6_kpi-table td {\n    padding: 6px 4px;\n  }\n  \n  /* 序号列移动端优化 */\n  .module6_sequence-cell {\n    min-width: 40px;\n    max-width: 45px;\n    width: 45px;\n    padding: 6px 2px;\n    font-size: 0.75rem;\n  }\n  \n  /* 权重列移动端优化 */\n  .module6_weight-cell {\n    min-width: 40px;\n    max-width: 45px;\n    width: 45px;\n    padding: 6px 2px;\n    font-size: 0.75rem;\n  }\n  \n  .module6_indicator-cell,\n  .module6_target-cell {\n    padding-left: 8px;\n  }\n  \n  .module6_month-header {\n    min-width: 80px;\n    font-size: 0.75rem;\n  }\n  \n  .module6_month-cell {\n    min-width: 80px;\n  }\n  \n  .module6_month-cell input {\n    font-size: 0.8rem;\n    padding: 3px;\n  }\n}", "/* 导出模态框样式 - 模块六 */\r\n.module6_export-modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  z-index: 1000;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.module6_export-modal {\r\n  background: linear-gradient(135deg, rgba(26, 26, 46, 0.95), rgba(22, 33, 62, 0.95));\r\n  border: 2px solid rgba(255, 215, 0, 0.5);\r\n  border-radius: 16px;\r\n  width: 90%;\r\n  max-width: 600px;\r\n  max-height: 80vh;\r\n  overflow: hidden;\r\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\r\n  animation: module6_modalSlideIn 0.3s ease-out;\r\n}\r\n\r\n/* 模态框头部 */\r\n.module6_modal-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 20px 30px;\r\n  background: linear-gradient(45deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));\r\n  border-bottom: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_modal-header h3 {\r\n  font-family: 'Orbitron', monospace;\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: #ffd700;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_close-btn {\r\n  width: 32px;\r\n  height: 32px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 50%;\r\n  color: #000;\r\n  font-size: 1.2rem;\r\n  font-weight: 700;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_close-btn:hover {\r\n  transform: scale(1.1);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n/* 模态框内容 */\r\n.module6_modal-content {\r\n  padding: 10px;\r\n  max-height: 120vh;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* 选择区域 */\r\n.module6_selection-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.module6_section-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  margin-bottom: 20px;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n}\r\n\r\n.module6_section-header h4 {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: #ffd700;\r\n  margin: 0;\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_selection-controls {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.module6_select-all-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  color: #ffffff;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.module6_select-all-label:hover {\r\n  color: #ffd700;\r\n}\r\n\r\n.module6_select-all-label input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_selection-count {\r\n  color: #ffd700;\r\n  font-size: 0.9rem;\r\n  font-weight: 600;\r\n  padding: 4px 8px;\r\n  background: rgba(255, 215, 0, 0.1);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n/* 指标列表 */\r\n.module6_indicators-list {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  border: 1px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 8px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  padding: 10px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar {\r\n  width: 8px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-track {\r\n  background: rgba(255, 215, 0, 0.1);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-thumb {\r\n  background: rgba(255, 215, 0, 0.5);\r\n  border-radius: 4px;\r\n}\r\n\r\n.module6_indicators-list::-webkit-scrollbar-thumb:hover {\r\n  background: rgba(255, 215, 0, 0.7);\r\n}\r\n\r\n.module6_indicator-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 8px 12px;\r\n  color: #ffffff;\r\n  font-size: 0.9rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  border-radius: 6px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.module6_indicator-item:hover {\r\n  background: rgba(255, 215, 0, 0.1);\r\n  transform: translateX(5px);\r\n}\r\n\r\n.module6_indicator-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.module6_indicator-item input[type=\"checkbox\"] {\r\n  width: 16px;\r\n  height: 16px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_indicator-text {\r\n  flex: 1;\r\n  font-weight: 500;\r\n}\r\n\r\n/* 格式选择区域 */\r\n.module6_format-section {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.module6_format-section h4 {\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1.2rem;\r\n  font-weight: 600;\r\n  color: #ffd700;\r\n  margin: 0 0 20px 0;\r\n  text-shadow: 0 0 8px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_format-options {\r\n  display: flex;\r\n  gap: 20px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.module6_format-option {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  padding: 15px 20px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border: 2px solid rgba(255, 215, 0, 0.3);\r\n  border-radius: 10px;\r\n  color: #ffffff;\r\n  font-size: 1rem;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  flex: 1;\r\n  min-width: 200px;\r\n}\r\n\r\n.module6_format-option:hover {\r\n  border-color: #ffd700;\r\n  background: rgba(255, 215, 0, 0.1);\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.2);\r\n}\r\n\r\n.module6_format-option input[type=\"radio\"] {\r\n  width: 18px;\r\n  height: 18px;\r\n  accent-color: #ffd700;\r\n  cursor: pointer;\r\n}\r\n\r\n.module6_format-icon {\r\n  font-size: 1.5rem;\r\n  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));\r\n}\r\n\r\n.module6_format-text {\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n/* 模态框底部 */\r\n.module6_modal-footer {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  gap: 15px;\r\n  padding: 20px 30px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-top: 1px solid rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_cancel-btn {\r\n  padding: 12px 24px;\r\n  background: transparent;\r\n  border: 2px solid rgba(255, 255, 255, 0.3);\r\n  border-radius: 8px;\r\n  color: #ffffff;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.module6_cancel-btn:hover {\r\n  border-color: #ffffff;\r\n  background: rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.module6_cancel-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.module6_export-btn {\r\n  padding: 12px 24px;\r\n  background: linear-gradient(45deg, #ffd700, #ffed4e);\r\n  border: none;\r\n  border-radius: 8px;\r\n  color: #000;\r\n  font-family: 'Rajdhani', sans-serif;\r\n  font-size: 1rem;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);\r\n}\r\n\r\n.module6_export-btn:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5);\r\n}\r\n\r\n.module6_export-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n/* 动画 */\r\n@keyframes module6_modalSlideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(-50px) scale(0.9);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0) scale(1);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .module6_export-modal {\r\n    width: 95%;\r\n    max-height: 90vh;\r\n  }\r\n  \r\n  .module6_modal-header {\r\n    padding: 15px 20px;\r\n  }\r\n  \r\n  .module6_modal-header h3 {\r\n    font-size: 1.3rem;\r\n  }\r\n  \r\n  .module6_modal-content {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .module6_section-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_selection-controls {\r\n    width: 100%;\r\n    justify-content: space-between;\r\n  }\r\n  \r\n  .module6_format-options {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .module6_format-option {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .module6_modal-footer {\r\n    padding: 15px 20px;\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .module6_cancel-btn,\r\n  .module6_export-btn {\r\n    width: 100%;\r\n    padding: 15px 20px;\r\n  }\r\n} ", "/* 数据可视化组件样式 - 高科技风格 + 金黄色主题 */\n.module6_data-visualization {\n  padding: 2px; /* 最小化padding，最大化利用弹窗空间 */\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  border-radius: 15px;\n  margin: 0; /* 移除margin，紧贴弹窗边界 */\n  box-shadow: 0 8px 32px rgba(255, 215, 0, 0.1);\n  border: 1px solid rgba(255, 215, 0, 0.2);\n  position: relative;\n  overflow: hidden;\n  flex: 1; /* 使用flex: 1占用剩余空间 */\n  display: flex;\n  flex-direction: column;\n}\n\n/* 背景装饰效果 */\n.module6_data-visualization::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: \n    radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.05) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);\n  pointer-events: none;\n  z-index: 0;\n}\n\n/* 控制面板 */\n.module6_visualization-controls {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n  margin-bottom: 3px; /* 最小化底部间距 */\n  padding: 6px 10px; /* 最小化padding */\n  background: rgba(255, 215, 0, 0.05);\n  border-radius: 12px;\n  border: 1px solid rgba(255, 215, 0, 0.2);\n  position: relative;\n  z-index: 1000; /* 提高控制面板的z-index，但仍低于下拉菜单 */\n  justify-content: space-between;\n  align-items: center;\n  flex-shrink: 0; /* 防止控制面板被压缩 */\n}\n\n.module6_control-group {\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  gap: 10px;\n  min-width: 200px;\n  flex: 1;\n}\n\n.module6_control-label {\n  color: #ffd700;\n  font-size: 18px; /* 调大二号字体 */\n  font-weight: 600;\n  font-family: 'Rajdhani', sans-serif;\n  text-transform: uppercase;\n  letter-spacing: 1px;\n  white-space: nowrap;\n  min-width: 80px;\n}\n\n.module6_month-select,\n.module6_chart-type-select,\n.module6_sort-select,\n.module6_department-select {\n  padding: 8px 12px;\n  background: rgba(26, 26, 46, 0.8);\n  border: 2px solid rgba(255, 215, 0, 0.3);\n  border-radius: 8px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 17px; /* 调大二号字体 */\n  font-weight: 500;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  min-width: 120px;\n  flex: 1;\n}\n\n.module6_month-select:hover,\n.module6_chart-type-select:hover,\n.module6_sort-select:hover,\n.module6_department-select:hover {\n  border-color: #ffd700;\n  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);\n  transform: translateY(-2px);\n}\n\n.module6_month-select:focus,\n.module6_chart-type-select:focus,\n.module6_sort-select:focus,\n.module6_department-select:focus {\n  outline: none;\n  border-color: #ffd700;\n  box-shadow: 0 0 20px rgba(255, 215, 0, 0.4);\n}\n\n.module6_month-select option,\n.module6_chart-type-select option,\n.module6_sort-select option,\n.module6_department-select option {\n  background: #1a1a2e;\n  color: #ffffff;\n  padding: 10px;\n}\n\n/* 确保月份选择下拉菜单正常显示 */\n.module6_month-select {\n  position: relative;\n  z-index: 1001; /* 确保月份选择下拉菜单有足够的z-index */\n  /* 确保下拉菜单在所有浏览器中正常显示 */\n  -webkit-appearance: menulist;\n  -moz-appearance: menulist;\n  appearance: menulist;\n}\n\n.module6_month-select:focus {\n  z-index: 1002; /* 聚焦时提高z-index */\n}\n\n/* 修复Chrome和Safari中的下拉菜单显示问题 */\n.module6_month-select::-ms-expand {\n  display: block;\n}\n\n/* 确保下拉菜单选项正常显示 */\n.module6_month-select option {\n  background: #1a1a2e !important;\n  color: #ffffff !important;\n  padding: 8px 12px !important;\n}\n\n/* 自定义带复选框的下拉菜单样式 */\n.module6_custom-select-container {\n  position: relative;\n  flex: 1;\n  z-index: 10000; /* 确保下拉菜单容器有足够高的z-index */\n}\n\n.module6_department-select {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n  text-align: left;\n}\n\n.module6_dropdown-arrow {\n  transition: transform 0.3s ease;\n}\n\n.module6_dropdown-arrow.open {\n  transform: rotate(180deg);\n}\n\n.module6_custom-select-menu {\n  position: fixed; /* 使用fixed定位，避免被父容器影响 */\n  top: auto; /* 将通过JavaScript动态设置 */\n  left: auto; /* 将通过JavaScript动态设置 */\n  width: 255px;\n  background: #1a1a2e;\n  border: 2px solid rgba(255, 215, 0, 0.5);\n  border-radius: 8px;\n  z-index: 99999; /* 提高z-index值，确保在所有元素之上 */\n  max-height: 500px; /* 缩短最大高度，与输入框长度一致 */\n  overflow-y: auto; /* 允许滚动 */\n  padding: 10px;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.8);\n  /* 确保下拉菜单不被父容器的overflow影响 */\n  transform: translateZ(0);\n  will-change: transform;\n  /* 添加动画效果 */\n  animation: module6_dropdownFadeIn 0.2s ease-out;\n}\n\n/* 下拉菜单动画 */\n@keyframes module6_dropdownFadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.module6_custom-select-option {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.module6_custom-select-option:hover {\n  background-color: rgba(255, 215, 0, 0.1);\n}\n\n.module6_custom-select-option label {\n  margin-left: 10px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 17px; /* 调大二号字体 */\n}\n\n.module6_checkbox-item {\n  display: flex;\n  align-items: center;\n  padding: 8px 12px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  justify-content: flex-start;\n}\n\n.module6_checkbox-item:hover {\n  background-color: rgba(255, 215, 0, 0.1);\n}\n\n.module6_checkbox-item label {\n  margin-left: 10px;\n  color: #ffffff;\n  font-family: 'Rajdhani', sans-serif;\n  font-size: 15px; /* 放大二号字体 */\n  cursor: pointer;\n}\n\n.module6_custom-checkbox {\n  appearance: none;\n  width: 16px;\n  height: 16px;\n  background-color: transparent;\n  border: 2px solid rgba(255, 215, 0, 0.5);\n  border-radius: 4px;\n  cursor: pointer;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.module6_custom-checkbox:checked {\n  background-color: #ffd700;\n  border-color: #ffd700;\n}\n\n.module6_custom-checkbox:checked::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: #1a1a2e;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 部门筛选功能样式 - 已统一为下拉框样式 */\n\n/* 图表容器 */\n.module6_chart-container {\n  flex: 1; /* 占用剩余的全部空间 */\n  min-height: 0; /* 重置最小高度，让flex布局生效 */\n  margin: 0; /* 移除margin */\n  padding: 2px; /* 最小化padding，最大化图表显示区域 */\n  background: rgba(0, 0, 0, 0.2);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 215, 0, 0.2);\n  position: relative;\n  z-index: 1; /* 保持较低的z-index，确保不会遮挡下拉菜单 */\n  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.3);\n  display: flex;\n  flex-direction: column;\n}\n\n\n\n/* 响应式设计 */\n/* 平板端 (768px - 1200px) */\n@media (max-width: 1200px) and (min-width: 769px) {\n  .module6_visualization-controls {\n    flex-wrap: wrap;\n    justify-content: center;\n  }\n\n  .module6_control-group {\n    min-width: 180px;\n    flex: 0 1 calc(50% - 10px);\n  }\n\n  .module6_chart-container {\n    /* 移除固定高度，使用flex布局 */\n    flex: 1;\n\n    min-height: 0; /* 重置最小高度，让flex布局生效 */\n    padding: 2px; /* 最小化padding */\n  }\n}\n\n/* 移动端 (768px及以下) */\n@media (max-width: 768px) {\n  .module6_data-visualization {\n    padding: 15px;\n    margin: 15px 0;\n  }\n\n  .module6_visualization-controls {\n    flex-direction: column;\n    gap: 15px;\n    align-items: stretch;\n    padding: 15px;\n  }\n\n  .module6_control-group {\n    min-width: auto;\n    flex: none;\n    flex-direction: row;\n    align-items: center;\n  }\n\n  .module6_control-label {\n    font-size: 12px;\n    min-width: 70px;\n  }\n\n  .module6_month-select,\n  .module6_chart-type-select,\n  .module6_sort-select,\n  .module6_department-select {\n    font-size: 12px;\n    padding: 8px;\n    min-width: 100px;\n  }\n\n  .module6_chart-container {\n    /* 移除固定高度，使用flex布局 */\n    flex: 1;\n\n    min-height: 0; /* 重置最小高度，让flex布局生效 */\n    padding: 2px; /* 最小化padding */\n    margin: 2px 0 0 0; /* 最小化margin */\n  }\n}\n\n@media (max-width: 480px) {\n  .module6_chart-container {\n    /* 移除固定高度，使用flex布局 */\n    flex: 1;\n\n    min-height: 0; /* 重置最小高度，让flex布局生效 */\n    padding: 2px; /* 最小化padding */\n  }\n  \n  .module6_control-label {\n    font-size: 15px; /* 调大二号字体 */\n    min-width: 60px;\n  }\n\n  .module6_month-select,\n  .module6_chart-type-select,\n  .module6_sort-select,\n  .module6_department-select {\n    padding: 6px 10px;\n    font-size: 15px; /* 调大二号字体 */\n    min-width: 80px;\n  }\n}\n\n/* 加载动画 */\n.module6_chart-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 300px;\n  color: #ffd700;\n  font-size: 18px;\n  font-family: 'Rajdhani', sans-serif;\n}\n\n.module6_chart-loading::after {\n  content: '';\n  width: 30px;\n  height: 30px;\n  border: 3px solid rgba(255, 215, 0, 0.3);\n  border-top: 3px solid #ffd700;\n  border-radius: 50%;\n  animation: module6_spin 1s linear infinite;\n  margin-left: 15px;\n}\n\n@keyframes module6_spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 图表悬停效果增强 */\n.module6_chart-container canvas {\n  border-radius: 10px;\n  transition: all 0.3s ease;\n  width: 100% !important; /* 确保图表宽度填充容器 */\n  height: 100% !important; /* 强制高度为100% */\n  flex: 1 !important; /* 使用flex: 1让canvas填充容器 */\n  min-height: 0 !important; /* 让flex布局生效 */\n  max-height: none !important; /* 移除最大高度限制 */\n  object-fit: contain !important; /* 确保内容适应容器 */\n}\n\n.module6_chart-container:hover canvas {\n  box-shadow: 0 0 25px rgba(255, 215, 0, 0.2);\n}\n\n/* 确保图表容器内的Chart.js容器正确填充 */\n.module6_chart-container > div {\n  width: 100% !important;\n  height: 100% !important; /* 强制高度为100% */\n  min-height: 0 !important; /* 重置最小高度，让flex布局生效 */\n  flex: 1 !important; /* 让Chart.js容器也使用flex布局 */\n  display: flex !important; /* 确保Chart.js容器使用flex布局 */\n  flex-direction: column !important; /* 垂直方向布局 */\n  max-height: none !important; /* 移除最大高度限制 */\n}\n\n/* 控制面板动画效果 */\n.module6_visualization-controls {\n  animation: module6_slideInFromTop 0.6s ease-out;\n}\n\n@keyframes module6_slideInFromTop {\n  from {\n    opacity: 0;\n    transform: translateY(-30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n\n", "/* 登录界面样式 - 科技感赛博朋克风格 */\n\n/* 登录遮罩层 */\n.auth-login-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: rgba(0, 0, 0, 0.9);\n  backdrop-filter: blur(10px);\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.auth-login-overlay.visible {\n  opacity: 1;\n}\n\n/* 背景效果 */\n.auth-login-background {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  pointer-events: none;\n}\n\n.auth-background-grid {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image: \n    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),\n    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);\n  background-size: 50px 50px;\n  animation: gridMove 20s linear infinite;\n}\n\n@keyframes gridMove {\n  0% { transform: translate(0, 0); }\n  100% { transform: translate(50px, 50px); }\n}\n\n.auth-background-particles {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: \n    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.15) 0%, transparent 50%),\n    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.15) 0%, transparent 50%),\n    radial-gradient(circle at 40% 40%, rgba(255, 149, 0, 0.1) 0%, transparent 50%);\n  animation: particleFloat 15s ease-in-out infinite;\n}\n\n@keyframes particleFloat {\n  0%, 100% { opacity: 0.3; transform: scale(1); }\n  50% { opacity: 0.6; transform: scale(1.1); }\n}\n\n/* 登录容器 */\n.auth-login-container {\n  position: relative;\n  z-index: 1;\n  transform: translateY(50px) scale(0.9);\n  opacity: 0;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.auth-login-container.slide-in {\n  transform: translateY(0) scale(1);\n  opacity: 1;\n}\n\n/* 登录表单卡片 */\n.auth-login-form {\n  background: rgba(10, 10, 10, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(0, 255, 255, 0.2);\n  border-radius: 20px;\n  padding: 40px;\n  width: 450px;\n  max-width: 90vw;\n  position: relative;\n  box-shadow: \n    0 25px 50px rgba(0, 0, 0, 0.5),\n    0 0 50px rgba(0, 255, 255, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  animation: formGlow 3s ease-in-out infinite;\n}\n\n@keyframes formGlow {\n  0%, 100% { box-shadow: \n    0 25px 50px rgba(0, 0, 0, 0.5),\n    0 0 50px rgba(0, 255, 255, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1); }\n  50% { box-shadow: \n    0 25px 50px rgba(0, 0, 0, 0.5),\n    0 0 60px rgba(0, 255, 255, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.15); }\n}\n\n/* 关闭按钮 */\n.auth-close-button {\n  position: absolute;\n  top: 15px;\n  right: 15px;\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 20px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n  z-index: 10;\n}\n\n.auth-close-button:hover {\n  background: rgba(255, 69, 58, 0.1);\n  color: #ff453a;\n  transform: scale(1.1);\n}\n\n/* 登录头部 */\n.auth-login-header {\n  text-align: center;\n  margin-bottom: 30px;\n}\n\n.auth-logo {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 15px;\n  margin-bottom: 10px;\n}\n\n.auth-logo-icon {\n  font-size: 40px;\n  animation: iconPulse 2s ease-in-out infinite;\n}\n\n@keyframes iconPulse {\n  0%, 100% { transform: scale(1); }\n  50% { transform: scale(1.1); }\n}\n\n.auth-logo-text {\n  font-size: 28px;\n  font-weight: 300;\n  margin: 0;\n  background: linear-gradient(135deg, #ffffff, #00ffff);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  letter-spacing: 2px;\n}\n\n.auth-subtitle {\n  color: rgba(255, 255, 255, 0.6);\n  font-size: 14px;\n  margin: 0;\n  letter-spacing: 1px;\n}\n\n/* 错误提示 */\n.auth-error-message {\n  background: rgba(255, 69, 58, 0.1);\n  border: 1px solid rgba(255, 69, 58, 0.3);\n  border-radius: 10px;\n  padding: 12px 16px;\n  margin-bottom: 20px;\n  color: #ff453a;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  animation: errorShake 0.5s ease-in-out;\n}\n\n@keyframes errorShake {\n  0%, 100% { transform: translateX(0); }\n  25% { transform: translateX(-5px); }\n  75% { transform: translateX(5px); }\n}\n\n.auth-error-icon {\n  font-size: 16px;\n}\n\n/* 表单组 */\n.auth-form-group {\n  margin-bottom: 20px;\n}\n\n.auth-input-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.auth-input-icon {\n  position: absolute;\n  left: 16px;\n  font-size: 16px;\n  color: rgba(0, 255, 255, 0.6);\n  z-index: 2;\n}\n\n.auth-input {\n  width: 100%;\n  height: 55px;\n  padding: 0 50px 0 50px;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 12px;\n  color: #ffffff;\n  font-size: 16px;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n}\n\n.auth-input:focus {\n  outline: none;\n  border-color: rgba(0, 255, 255, 0.5);\n  background: rgba(255, 255, 255, 0.08);\n  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);\n}\n\n.auth-input::placeholder {\n  color: rgba(255, 255, 255, 0.4);\n}\n\n.auth-password-toggle {\n  position: absolute;\n  right: 16px;\n  background: none;\n  border: none;\n  color: rgba(0, 255, 255, 0.6);\n  font-size: 18px;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n  z-index: 2;\n}\n\n.auth-password-toggle:hover {\n  background: rgba(0, 255, 255, 0.1);\n  color: #00ffff;\n  transform: scale(1.1);\n}\n\n/* 复选框样式 */\n.auth-checkbox-group {\n  margin-bottom: 25px;\n}\n\n.auth-checkbox-label {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  cursor: pointer;\n  font-size: 14px;\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.auth-checkbox {\n  display: none;\n}\n\n.auth-checkbox-custom {\n  width: 20px;\n  height: 20px;\n  border: 2px solid rgba(0, 255, 255, 0.3);\n  border-radius: 4px;\n  background: rgba(255, 255, 255, 0.05);\n  position: relative;\n  transition: all 0.3s ease;\n}\n\n.auth-checkbox:checked + .auth-checkbox-custom {\n  background: linear-gradient(135deg, #00ffff, #0080ff);\n  border-color: #00ffff;\n}\n\n.auth-checkbox:checked + .auth-checkbox-custom::after {\n  content: '✓';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  color: #ffffff;\n  font-size: 12px;\n  font-weight: bold;\n}\n\n/* 登录按钮 */\n.auth-login-button {\n  width: 100%;\n  height: 55px;\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\n  border: none;\n  border-radius: 12px;\n  color: #ffffff;\n  font-size: 16px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 10px;\n  position: relative;\n  overflow: hidden;\n  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);\n  margin-bottom: 20px;\n}\n\n.auth-login-button::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  transition: left 0.5s ease;\n}\n\n.auth-login-button:hover::before {\n  left: 100%;\n}\n\n.auth-login-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 8px 25px rgba(0, 255, 255, 0.4);\n}\n\n.auth-login-button:active {\n  transform: translateY(0);\n}\n\n.auth-login-button.loading {\n  cursor: not-allowed;\n  opacity: 0.8;\n}\n\n.auth-loading-spinner {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-top: 2px solid #ffffff;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* 忘记密码链接 */\n.auth-form-footer {\n  text-align: center;\n  margin-bottom: 20px;\n}\n\n.auth-forgot-password {\n  background: none;\n  border: none;\n  color: rgba(0, 255, 255, 0.8);\n  font-size: 14px;\n  cursor: pointer;\n  text-decoration: none;\n  transition: all 0.3s ease;\n  padding: 8px 16px;\n  border-radius: 6px;\n}\n\n.auth-forgot-password:hover {\n  background: rgba(0, 255, 255, 0.1);\n  color: #00ffff;\n  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);\n}\n\n/* 登录提示 */\n.auth-login-tips {\n  border-top: 1px solid rgba(0, 255, 255, 0.1);\n  padding-top: 20px;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.auth-tip-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: rgba(255, 255, 255, 0.5);\n}\n\n.auth-tip-icon {\n  font-size: 14px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .auth-login-form {\n    padding: 30px 20px;\n    width: 100%;\n    margin: 20px;\n    border-radius: 15px;\n  }\n  \n  .auth-logo-text {\n    font-size: 24px;\n  }\n  \n  .auth-input {\n    height: 50px;\n    font-size: 14px;\n  }\n  \n  .auth-login-button {\n    height: 50px;\n    font-size: 14px;\n  }\n}\n", "/* 登录管理器样式 - 小型透明图标 */\n\n.auth-mini-icon {\n  position: fixed;\n  top: 20px;\n  left: 20px;\n  z-index: 1000;\n  cursor: pointer;\n}\n\n/* 登录图标 - 未登录状态 */\n.auth-login-icon-mini {\n  width: 32px;\n  height: 32px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.auth-login-icon-mini:hover {\n  background: rgba(255, 255, 255, 0.2);\n  border-color: rgba(0, 255, 255, 0.5);\n  color: #00ffff;\n  transform: scale(1.1);\n}\n\n/* 用户头像 - 已登录状态 */\n.auth-user-avatar-mini {\n  width: 32px;\n  height: 32px;\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\n  border: 2px solid rgba(0, 255, 255, 0.3);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  font-weight: 600;\n  color: #ffffff;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  position: relative;\n}\n\n.auth-user-avatar-mini:hover {\n  transform: scale(1.1);\n  box-shadow: 0 4px 15px rgba(0, 255, 255, 0.4);\n}\n\n.auth-user-avatar-mini::after {\n  content: '';\n  position: absolute;\n  bottom: -2px;\n  right: -2px;\n  width: 10px;\n  height: 10px;\n  background: #00ff88;\n  border: 2px solid #ffffff;\n  border-radius: 50%;\n}\n\n/* 用户下拉菜单 */\n.auth-user-dropdown {\n  position: absolute;\n  top: 40px;\n  left: 0;\n  width: 280px;\n  background: rgba(10, 10, 10, 0.95);\n  backdrop-filter: blur(20px);\n  border: 1px solid rgba(0, 255, 255, 0.2);\n  border-radius: 15px;\n  box-shadow: \n    0 10px 30px rgba(0, 0, 0, 0.3),\n    0 0 20px rgba(0, 255, 255, 0.1);\n  animation: slideDown 0.3s ease;\n  z-index: 1001;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.auth-dropdown-header {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n\n.auth-dropdown-avatar {\n  width: 50px;\n  height: 50px;\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\n  border: 2px solid rgba(0, 255, 255, 0.3);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 20px;\n  font-weight: 600;\n  color: #ffffff;\n  margin-right: 15px;\n}\n\n.auth-dropdown-info {\n  flex: 1;\n}\n\n.auth-dropdown-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #ffffff;\n  margin-bottom: 4px;\n}\n\n.auth-dropdown-role {\n  font-size: 12px;\n  color: rgba(0, 255, 255, 0.8);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.auth-dropdown-menu {\n  padding: 10px 0;\n}\n\n.auth-dropdown-item {\n  width: 100%;\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  background: none;\n  border: none;\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 14px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  text-align: left;\n}\n\n.auth-dropdown-item:hover {\n  background: rgba(0, 255, 255, 0.1);\n  color: #ffffff;\n}\n\n.auth-dropdown-item.logout:hover {\n  background: rgba(255, 69, 58, 0.1);\n  color: #ff453a;\n}\n\n.auth-dropdown-icon {\n  margin-right: 12px;\n  font-size: 16px;\n  width: 20px;\n  text-align: center;\n}\n\n.auth-dropdown-divider {\n  height: 1px;\n  background: rgba(255, 255, 255, 0.1);\n  margin: 8px 0;\n}\n\n/* 通知样式 */\n.auth-notification {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  padding: 15px 20px;\n  border-radius: 10px;\n  color: #ffffff;\n  font-weight: 500;\n  z-index: 10000;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  animation: notificationSlideIn 0.3s ease;\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  max-width: 300px;\n  word-wrap: break-word;\n}\n\n.auth-notification.success {\n  background: rgba(52, 199, 89, 0.9);\n  box-shadow: 0 4px 20px rgba(52, 199, 89, 0.3);\n}\n\n.auth-notification.error {\n  background: rgba(255, 69, 58, 0.9);\n  box-shadow: 0 4px 20px rgba(255, 69, 58, 0.3);\n}\n\n@keyframes notificationSlideIn {\n  from {\n    opacity: 0;\n    transform: translateX(100%);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n.auth-notification-icon {\n  font-size: 16px;\n  flex-shrink: 0;\n}\n\n.auth-notification-text {\n  font-size: 14px;\n  line-height: 1.4;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .auth-mini-icon {\n    top: 15px;\n    left: 15px;\n  }\n  \n  .auth-user-dropdown {\n    width: calc(100vw - 30px);\n    max-width: 300px;\n  }\n  \n  .auth-notification {\n    right: 10px;\n    left: 10px;\n    max-width: none;\n  }\n}\n", "/* 用户管理页面基础样式 */\r\n.user-management {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);\r\n  color: #ffffff;\r\n  padding: 30px;\r\n  position: relative;\r\n  overflow-x: hidden;\r\n}\r\n\r\n/* 背景动画效果 */\r\n.user-management::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: \r\n    radial-gradient(circle at 20% 80%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),\r\n    radial-gradient(circle at 80% 20%, rgba(128, 0, 255, 0.1) 0%, transparent 50%),\r\n    radial-gradient(circle at 40% 40%, rgba(255, 149, 0, 0.05) 0%, transparent 50%);\r\n  pointer-events: none;\r\n  z-index: 0;\r\n}\r\n\r\n.user-management > * {\r\n  position: relative;\r\n  z-index: 1;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  margin-bottom: 40px;\r\n  background: rgba(255, 255, 255, 0.03);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 20px;\r\n  padding: 30px 10px; /* 左右留10px间距 */\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.3),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  height: 60px; /* 固定高度确保水平对齐 */\r\n  padding: 0 15px; /* 左右各15px的间距调整 */\r\n  position: relative; /* 为子元素定位提供参考 */\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.title-icon {\r\n  font-size: 36px;\r\n  color: #00ffff;\r\n  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);\r\n  animation: pulse 2s ease-in-out infinite;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% { transform: scale(1); }\r\n  50% { transform: scale(1.05); }\r\n}\r\n\r\n.header-title h1 {\r\n  font-size: 36px;\r\n  font-weight: 300;\r\n  margin: 0;\r\n  letter-spacing: 2px;\r\n  background: linear-gradient(135deg, #ffffff, #00ffff);\r\n  -webkit-background-clip: text;\r\n  -webkit-text-fill-color: transparent;\r\n  background-clip: text;\r\n}\r\n\r\n.user-count {\r\n  font-size: 18px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  background: rgba(0, 255, 255, 0.1);\r\n  padding: 8px 16px;\r\n  border-radius: 25px;\r\n  border: 1px solid rgba(0, 255, 255, 0.2);\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n/* 头部操作按钮 */\r\n.header-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  align-items: center;\r\n  height: 60px; /* 与header-content高度一致 */\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  height: 50px; /* 保持按钮高度 */\r\n  padding: 0 25px;\r\n  border: none;\r\n  border-radius: 12px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.action-button::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.action-button:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.action-button.back {\r\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\r\n  color: #ffffff;\r\n  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);\r\n}\r\n\r\n.action-button.logout {\r\n  background: linear-gradient(135deg, #ff453a 0%, #ff6b6b 100%);\r\n  color: #ffffff;\r\n  box-shadow: 0 4px 20px rgba(255, 69, 58, 0.3);\r\n}\r\n\r\n.action-button.create {\r\n  background: linear-gradient(135deg, #34c759 0%, #4cd964 100%);\r\n  color: #ffffff;\r\n  box-shadow: 0 4px 20px rgba(52, 199, 89, 0.3);\r\n}\r\n\r\n.action-button:hover {\r\n  transform: translateY(-3px);\r\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 搜索和过滤区域 */\r\n.filters-section {\r\n  display: flex;\r\n  gap: 20px;\r\n  align-items: center;\r\n  flex-wrap: wrap;\r\n  height: 60px; /* 与header-content高度一致 */\r\n  position: relative;\r\n  right: 15px; /* 向左移动15px */\r\n}\r\n\r\n.search-box {\r\n  position: relative;\r\n  flex: 1;\r\n  min-width: 300px;\r\n  height: 50px; /* 与按钮高度一致 */\r\n}\r\n\r\n.search-icon {\r\n  position: absolute;\r\n  left: 20px;\r\n  top: 50%;\r\n  transform: translateY(-50%);\r\n  color: rgba(0, 255, 255, 0.6);\r\n  font-size: 16px;\r\n}\r\n\r\n.search-input {\r\n  width: 100%;\r\n  height: 50px; /* 与按钮高度一致 */\r\n  padding: 0 60px 0 55px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n.search-input:focus {\r\n  outline: none;\r\n  border-color: rgba(0, 255, 255, 0.5);\r\n  background: rgba(255, 255, 255, 0.08);\r\n  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);\r\n}\r\n\r\n.search-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.filter-controls {\r\n  display: flex;\r\n  gap: 15px;\r\n  height: 50px; /* 与按钮高度一致 */\r\n  align-items: center;\r\n}\r\n\r\n.filter-select {\r\n  height: 50px; /* 与按钮高度一致 */\r\n  padding: 0 20px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  color: #ffffff;\r\n  font-size: 14px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n  min-width: 150px;\r\n}\r\n\r\n.filter-select:focus {\r\n  outline: none;\r\n  border-color: rgba(0, 255, 255, 0.5);\r\n  background: rgba(255, 255, 255, 0.08);\r\n}\r\n\r\n.filter-select option {\r\n  background: #1a1a1a;\r\n  color: #ffffff;\r\n}\r\n\r\n/* 用户网格布局 */\r\n.users-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));\r\n  gap: 25px;\r\n  margin-top: 20px;\r\n}\r\n\r\n/* 用户卡片 */\r\n.user-card {\r\n  background: rgba(255, 255, 255, 0.03);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 20px;\r\n  padding: 25px;\r\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n  position: relative;\r\n  overflow: hidden;\r\n  box-shadow: \r\n    0 10px 30px rgba(0, 0, 0, 0.2),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.user-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 3px;\r\n  background: linear-gradient(90deg, #00ffff, #0080ff, #8000ff);\r\n  opacity: 0;\r\n  transition: opacity 0.3s ease;\r\n}\r\n\r\n.user-card:hover {\r\n  transform: translateY(-5px);\r\n  border-color: rgba(0, 255, 255, 0.3);\r\n  box-shadow: \r\n    0 20px 40px rgba(0, 0, 0, 0.3),\r\n    0 0 30px rgba(0, 255, 255, 0.1),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.user-card:hover::before {\r\n  opacity: 1;\r\n}\r\n\r\n.user-card.inactive {\r\n  opacity: 0.6;\r\n  border-color: rgba(255, 69, 58, 0.3);\r\n}\r\n\r\n/* 卡片头部 */\r\n.card-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  gap: 25px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.user-avatar-section {\r\n  position: relative;\r\n}\r\n\r\n.user-avatar {\r\n  width: 90px;\r\n  height: 90px;\r\n  border-radius: 50%;\r\n  background: linear-gradient(135deg, #00ffff, #0080ff);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #ffffff;\r\n  font-size: 36px;\r\n  font-weight: 700;\r\n  text-shadow: 0 0 15px rgba(0, 255, 255, 0.6);\r\n  box-shadow: 0 0 25px rgba(0, 255, 255, 0.4);\r\n  position: relative;\r\n}\r\n\r\n.status-indicator {\r\n  position: absolute;\r\n  bottom: 15px;\r\n  right: 5px;\r\n}\r\n\r\n.status-dot {\r\n  width: 20px;\r\n  height: 20px;\r\n  border-radius: 50%;\r\n  border: 3px solid rgba(10, 10, 10, 0.8);\r\n  animation: statusPulse 2s ease-in-out infinite;\r\n}\r\n\r\n.status-dot.active {\r\n  background: #34c759;\r\n  box-shadow: 0 0 10px rgba(52, 199, 89, 0.5);\r\n}\r\n\r\n.status-dot.inactive {\r\n  background: #ff453a;\r\n  box-shadow: 0 0 10px rgba(255, 69, 58, 0.5);\r\n}\r\n\r\n@keyframes statusPulse {\r\n  0%, 100% { opacity: 0.8; }\r\n  50% { opacity: 1; }\r\n}\r\n\r\n.user-info {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  max-width: 100px;\r\n}\r\n\r\n.user-info-center {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n  text-align: center;\r\n  justify-content: center;\r\n  align-items: center; /* 确保水平居中 */\r\n}\r\n\r\n.username {\r\n  font-size: 22px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n  color: #ffffff;\r\n  line-height: 1.2;\r\n}\r\n\r\n.user-email {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center; /* 水平居中 */\r\n  gap: 8px;\r\n  color: rgba(255, 255, 255, 0.7);\r\n  font-size: 15px;\r\n  margin: 0;\r\n  line-height: 1.2;\r\n}\r\n\r\n.email-icon {\r\n  color: rgba(0, 255, 255, 0.6);\r\n}\r\n\r\n.role-badge-container {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: flex-end;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.role-badge {\r\n  padding: 8px 16px;\r\n  border-radius: 20px;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #ffffff;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n  backdrop-filter: blur(10px);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n/* 卡片内容 */\r\n.card-content {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 15px;\r\n}\r\n\r\n/* 统一信息容器 */\r\n.info-unified-container {\r\n  display: flex;\r\n  background: rgba(255, 255, 255, 0.03);\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(255, 255, 255, 0.05);\r\n  padding: 15px;\r\n  gap: 20px;\r\n  align-items: center;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 12px;\r\n  background: rgba(255, 255, 255, 0.03);\r\n  border-radius: 10px;\r\n  border: 1px solid rgba(255, 255, 255, 0.05);\r\n}\r\n\r\n.info-item-left,\r\n.info-item-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  flex: 1;\r\n  padding: 8px 12px;\r\n  background: rgba(255, 255, 255, 0.02);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(255, 255, 255, 0.03);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.info-item-left:hover,\r\n.info-item-right:hover {\r\n  background: rgba(255, 255, 255, 0.04);\r\n  border-color: rgba(0, 255, 255, 0.1);\r\n}\r\n\r\n.info-icon {\r\n  color: rgba(0, 255, 255, 0.6);\r\n  font-size: 16px;\r\n  width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n.info-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 2px;\r\n}\r\n\r\n.info-label {\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.5);\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 13px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-weight: 500;\r\n}\r\n\r\n/* 卡片操作按钮 */\r\n.card-actions {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.action-group {\r\n  display: flex;\r\n  gap: 8px;\r\n  justify-content: flex-start;\r\n}\r\n\r\n.action-group.primary {\r\n  justify-content: flex-start;\r\n}\r\n\r\n.action-group.secondary {\r\n  justify-content: center;\r\n}\r\n\r\n.action-group.danger {\r\n  justify-content: flex-end;\r\n}\r\n\r\n.action-btn {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 10px 16px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n  backdrop-filter: blur(10px);\r\n  min-width: 80px;\r\n  justify-content: center;\r\n}\r\n\r\n.action-btn::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n  transition: left 0.5s ease;\r\n}\r\n\r\n.action-btn:hover::before {\r\n  left: 100%;\r\n}\r\n\r\n.action-btn.view {\r\n  background: rgba(0, 255, 255, 0.1);\r\n  color: #00ffff;\r\n  border: 1px solid rgba(0, 255, 255, 0.3);\r\n}\r\n\r\n.action-btn.edit {\r\n  background: rgba(255, 149, 0, 0.1);\r\n  color: #ff9500;\r\n  border: 1px solid rgba(255, 149, 0, 0.3);\r\n}\r\n\r\n.action-btn.toggle {\r\n  background: rgba(52, 199, 89, 0.1);\r\n  color: #34c759;\r\n  border: 1px solid rgba(52, 199, 89, 0.3);\r\n}\r\n\r\n.action-btn.reset {\r\n  background: rgba(128, 0, 255, 0.1);\r\n  color: #8000ff;\r\n  border: 1px solid rgba(128, 0, 255, 0.3);\r\n}\r\n\r\n.action-btn.permissions {\r\n  background: rgba(0, 255, 255, 0.1);\r\n  color: #00ffff;\r\n  border: 1px solid rgba(0, 255, 255, 0.3);\r\n}\r\n\r\n.action-btn.delete {\r\n  background: rgba(255, 69, 58, 0.1);\r\n  color: #ff453a;\r\n  border: 1px solid rgba(255, 69, 58, 0.3);\r\n}\r\n\r\n.action-btn:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.action-btn:disabled {\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n.action-btn span {\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 60px 20px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n}\r\n\r\n.loading-spinner {\r\n  width: 40px;\r\n  height: 40px;\r\n  border: 3px solid rgba(0, 255, 255, 0.3);\r\n  border-top: 3px solid #00ffff;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 80px 20px;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  grid-column: 1 / -1;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 64px;\r\n  color: rgba(0, 255, 255, 0.3);\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 模态框样式 */\r\n.modal-overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background: rgba(0, 0, 0, 0.8);\r\n  backdrop-filter: blur(10px);\r\n  z-index: 1000;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  animation: fadeIn 0.3s ease;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from { opacity: 0; }\r\n  to { opacity: 1; }\r\n}\r\n\r\n.modal-content {\r\n  background: rgba(10, 10, 10, 0.95);\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(0, 255, 255, 0.2);\r\n  border-radius: 20px;\r\n  padding: 20px;\r\n  max-width: 890px;\r\n  width: 90vw;\r\n  max-height: 70vh;\r\n  overflow-y: auto;\r\n  animation: slideIn 0.3s ease;\r\n  box-shadow:\r\n    0 25px 50px rgba(0, 0, 0, 0.5),\r\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: scale(0.9) translateY(-20px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: scale(1) translateY(0);\r\n  }\r\n}\r\n\r\n.modal-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding-bottom: 15px;\r\n  border-bottom: 1px solid rgba(0, 255, 255, 0.1);\r\n}\r\n\r\n.modal-header h2 {\r\n  color: #ffffff;\r\n  font-size: 24px;\r\n  font-weight: 300;\r\n  margin: 0;\r\n  text-shadow: 0 0 10px rgba(0, 255, 255, 0.3);\r\n}\r\n\r\n.close-button {\r\n  background: none;\r\n  border: none;\r\n  color: rgba(255, 255, 255, 0.6);\r\n  font-size: 24px;\r\n  cursor: pointer;\r\n  padding: 8px;\r\n  border-radius: 8px;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.close-button:hover {\r\n  background: rgba(255, 69, 58, 0.1);\r\n  color: #ff453a;\r\n  transform: scale(1.1);\r\n}\r\n\r\n/* 表单样式 */\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 紧凑表单样式 */\r\n.compact-form {\r\n  padding: 0 10px;\r\n}\r\n\r\n.form-row {\r\n  display: flex;\r\n  gap: 15px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group-half {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  text-align: left;\r\n}\r\n\r\n.form-group-half label {\r\n  display: block;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  margin-bottom: 8px;\r\n  text-align: left;\r\n}\r\n\r\n.form-input,\r\n.form-select {\r\n  width: 100%;\r\n  height: 50px;\r\n  padding: 0 20px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  border-radius: 12px;\r\n  color: #ffffff;\r\n  font-size: 16px;\r\n  transition: all 0.3s ease;\r\n  backdrop-filter: blur(10px);\r\n}\r\n\r\n/* 角色下拉菜单特殊样式 - 蓝色背景 */\r\n.form-select.role-select {\r\n  background: rgba(0, 123, 255, 0.3); /* 蓝色背景 */\r\n  border: 1px solid rgba(0, 123, 255, 0.5);\r\n  color: #ffffff;\r\n}\r\n\r\n.form-select.role-select:focus {\r\n  background: rgba(0, 123, 255, 0.4);\r\n  border-color: rgba(0, 123, 255, 0.7);\r\n  box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);\r\n}\r\n\r\n.form-select.role-select option {\r\n  background: rgba(0, 123, 255, 0.9);\r\n  color: #ffffff;\r\n}\r\n\r\n.form-input:focus,\r\n.form-select:focus {\r\n  outline: none;\r\n  border-color: rgba(0, 255, 255, 0.5);\r\n  background: rgba(255, 255, 255, 0.08);\r\n  box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);\r\n}\r\n\r\n.form-input::placeholder {\r\n  color: rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.checkbox-label {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  cursor: pointer;\r\n  font-size: 14px;\r\n  color: rgba(255, 255, 255, 0.8);\r\n}\r\n\r\n.checkbox-label input[type=\"checkbox\"] {\r\n  width: 20px;\r\n  height: 20px;\r\n  accent-color: #00ffff;\r\n}\r\n\r\n.modal-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: flex-end;\r\n  margin-top: 20px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid rgba(0, 255, 255, 0.1);\r\n}\r\n\r\n.cancel-button,\r\n.submit-button {\r\n  height: 45px;\r\n  padding: 0 25px;\r\n  border: none;\r\n  border-radius: 10px;\r\n  font-size: 14px;\r\n  font-weight: 600;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.cancel-button {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: rgba(255, 255, 255, 0.8);\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n}\r\n\r\n.submit-button {\r\n  background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);\r\n  color: #ffffff;\r\n  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.3);\r\n}\r\n\r\n.cancel-button:hover,\r\n.submit-button:hover {\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.submit-button:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n  transform: none;\r\n}\r\n\r\n/* 通知样式 */\r\n.notification {\r\n  position: fixed;\r\n  top: 20px;\r\n  right: 20px;\r\n  padding: 15px 20px;\r\n  border-radius: 10px;\r\n  color: #ffffff;\r\n  font-weight: 500;\r\n  z-index: 1001;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  animation: slideInRight 0.3s ease;\r\n  backdrop-filter: blur(20px);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.notification.success {\r\n  background: rgba(52, 199, 89, 0.9);\r\n  box-shadow: 0 4px 20px rgba(52, 199, 89, 0.3);\r\n}\r\n\r\n.notification.error {\r\n  background: rgba(255, 69, 58, 0.9);\r\n  box-shadow: 0 4px 20px rgba(255, 69, 58, 0.3);\r\n}\r\n\r\n@keyframes slideInRight {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateX(100%);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateX(0);\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .user-management {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .users-grid {\r\n    grid-template-columns: 1fr;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .header-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .header-actions {\r\n    justify-content: center;\r\n    flex-wrap: wrap;\r\n  }\r\n  \r\n  .filters-section {\r\n    flex-direction: column;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .search-box {\r\n    min-width: auto;\r\n  }\r\n  \r\n  .filter-controls {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .card-actions {\r\n    gap: 8px;\r\n  }\r\n  \r\n  .action-group {\r\n    flex-wrap: wrap;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .action-btn {\r\n    min-width: 70px;\r\n    padding: 8px 12px;\r\n  }\r\n  \r\n  /* 移动端用户卡片头部布局调整 */\r\n  .card-header {\r\n    flex-direction: column;\r\n    align-items: center;\r\n    text-align: center;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .user-info-center {\r\n    order: 2;\r\n  }\r\n  \r\n  .role-badge-container {\r\n    order: 3;\r\n    justify-content: center;\r\n  }\r\n  \r\n  .user-avatar-section {\r\n    order: 1;\r\n  }\r\n  \r\n  /* 移动端信息容器布局调整 */\r\n  .info-unified-container {\r\n    flex-direction: column;\r\n    gap: 12px;\r\n    padding: 12px;\r\n  }\r\n  \r\n  .info-item-left,\r\n  .info-item-right {\r\n    width: 100%;\r\n    justify-content: center;\r\n  }\r\n  \r\n  /* 移动端模态框布局调整 */\r\n  .modal-content {\r\n    max-width: 95vw;\r\n    width: 95vw;\r\n    padding: 15px;\r\n  }\r\n  \r\n  .form-row {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .form-group-half {\r\n    width: 100%;\r\n  }\r\n  \r\n  .modal-actions {\r\n    flex-direction: column;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .cancel-button,\r\n  .submit-button {\r\n    width: 100%;\r\n  }\r\n}", "/* 个人设置页面样式 */\r\n.personal-settings {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);\r\n  color: #e0e6ed;\r\n  padding: 20px;\r\n}\r\n\r\n/* 页面头部 */\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding: 20px 0;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.header-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.title-icon {\r\n  font-size: 28px;\r\n  color: #4ecdc4;\r\n}\r\n\r\n.header-title h1 {\r\n  color: #4ecdc4;\r\n  font-size: 28px;\r\n  font-weight: 600;\r\n  margin: 0;\r\n  text-shadow: 0 2px 4px rgba(78, 205, 196, 0.3);\r\n}\r\n\r\n.header-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.action-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 12px 20px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  text-decoration: none;\r\n}\r\n\r\n.action-button.back {\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: #e0e6ed;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  margin-right: 20px; /* 向左移动20px，避免贴边显示 */\r\n}\r\n\r\n.action-button.back:hover {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n/* 主要内容区域 */\r\n.settings-content {\r\n  display: flex;\r\n  gap: 30px;\r\n  max-width: 1200px;\r\n  margin: 0 auto;\r\n}\r\n\r\n/* 侧边栏导航 */\r\n.settings-sidebar {\r\n  width: 250px;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  height: fit-content;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.sidebar-nav {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.nav-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 15px 20px;\r\n  border: none;\r\n  background: transparent;\r\n  color: #b8c5d1;\r\n  border-radius: 8px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  text-align: left;\r\n  width: 100%;\r\n}\r\n\r\n.nav-item:hover {\r\n  background: rgba(78, 205, 196, 0.1);\r\n  color: #4ecdc4;\r\n  transform: translateX(5px);\r\n}\r\n\r\n.nav-item.active {\r\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);\r\n}\r\n\r\n.nav-icon {\r\n  font-size: 18px;\r\n  width: 20px;\r\n  text-align: center;\r\n}\r\n\r\n/* 设置主内容区域 */\r\n.settings-main {\r\n  flex: 1;\r\n  background: rgba(255, 255, 255, 0.05);\r\n  border-radius: 12px;\r\n  padding: 30px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.settings-tab {\r\n  animation: fadeIn 0.3s ease-out;\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(10px);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n\r\n.tab-header {\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\r\n  text-align: center; /* 居中显示 */\r\n}\r\n\r\n.tab-header h2 {\r\n  color: #4ecdc4;\r\n  font-size: 28px; /* 增大4号字体 */\r\n  font-weight: 600;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.tab-header p {\r\n  color: #b8c5d1;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  margin: 0;\r\n}\r\n\r\n/* 表单样式 */\r\n.settings-form {\r\n  max-width: 600px;\r\n}\r\n\r\n.form-section {\r\n  margin-bottom: 40px;\r\n  padding: 25px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 12px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.form-section h3 {\r\n  color: #4ecdc4;\r\n  font-size: 22px; /* 增大4号字体 */\r\n  font-weight: 600;\r\n  margin: 0 0 20px 0;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid rgba(78, 205, 196, 0.2);\r\n}\r\n\r\n.form-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.form-group label {\r\n  display: block;\r\n  margin-bottom: 8px;\r\n  color: #e0e6ed;\r\n  font-weight: 500;\r\n  font-size: 18px; /* 增大4号字体 */\r\n}\r\n\r\n.form-group input,\r\n.form-group select {\r\n  width: 100%;\r\n  padding: 12px 16px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 8px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: #e0e6ed;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.form-group input:focus,\r\n.form-group select:focus {\r\n  outline: none;\r\n  border-color: #4ecdc4;\r\n  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);\r\n  background: rgba(255, 255, 255, 0.15);\r\n}\r\n\r\n.form-group input::placeholder {\r\n  color: #8a9ba8;\r\n}\r\n\r\n/* 信息网格 */\r\n.info-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n  gap: 20px;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.info-item label {\r\n  color: #b8c5d1;\r\n  font-size: 16px; /* 增大4号字体 */\r\n  font-weight: 500;\r\n  text-transform: uppercase;\r\n  letter-spacing: 0.5px;\r\n}\r\n\r\n.info-value {\r\n  color: #e0e6ed;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  padding: 8px 12px;\r\n  background: rgba(0, 0, 0, 0.3);\r\n  border-radius: 6px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.role-badge {\r\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\r\n  color: white;\r\n  font-weight: 600;\r\n  text-align: center;\r\n}\r\n\r\n.status-badge {\r\n  text-align: center;\r\n  font-weight: 600;\r\n}\r\n\r\n.status-badge.active {\r\n  background: rgba(76, 175, 80, 0.2);\r\n  color: #4caf50;\r\n  border-color: rgba(76, 175, 80, 0.3);\r\n}\r\n\r\n.status-badge.inactive {\r\n  background: rgba(244, 67, 54, 0.2);\r\n  color: #f44336;\r\n  border-color: rgba(244, 67, 54, 0.3);\r\n}\r\n\r\n/* 偏好设置项 */\r\n.preference-item {\r\n  display: flex;\r\n  justify-content: center; /* 居中显示 */\r\n  align-items: center;\r\n  padding: 20px;\r\n  background: rgba(0, 0, 0, 0.2);\r\n  border-radius: 8px;\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  margin-bottom: 15px;\r\n  flex-direction: column; /* 垂直排列 */\r\n  text-align: center; /* 文本居中 */\r\n  gap: 15px; /* 增加间距 */\r\n}\r\n\r\n.preference-info {\r\n  flex: 1;\r\n  text-align: center; /* 信息部分居中 */\r\n}\r\n\r\n.preference-info h4 {\r\n  color: #e0e6ed;\r\n  font-size: 20px; /* 增大4号字体 */\r\n  font-weight: 600;\r\n  margin: 0 0 5px 0;\r\n}\r\n\r\n.preference-info p {\r\n  color: #b8c5d1;\r\n  font-size: 17px; /* 增大4号字体 */\r\n  margin: 0;\r\n}\r\n\r\n.preference-control {\r\n  margin-left: 0; /* 移除左边距，因为现在是垂直布局 */\r\n  text-align: center; /* 控件居中 */\r\n}\r\n\r\n.form-select {\r\n  padding: 8px 12px;\r\n  border: 1px solid rgba(255, 255, 255, 0.2);\r\n  border-radius: 6px;\r\n  background: rgba(255, 255, 255, 0.1);\r\n  color: #e0e6ed;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  min-width: 150px;\r\n}\r\n\r\n/* 修复下拉菜单选项样式 - 确保文字清晰可见 */\r\n.form-select option {\r\n  background: #4ecdc4 !important; /* 蓝色背景 */\r\n  color: #ffffff !important; /* 白色文字 */\r\n  padding: 8px 12px !important;\r\n  font-size: 16px !important;\r\n}\r\n\r\n/* 针对不同浏览器的兼容性处理 */\r\n.form-select:focus {\r\n  outline: none;\r\n  border-color: #4ecdc4;\r\n  box-shadow: 0 0 0 2px rgba(78, 205, 196, 0.2);\r\n  background: rgba(78, 205, 196, 0.1);\r\n}\r\n\r\n/* 确保下拉菜单在所有浏览器中正常显示 */\r\n.form-select {\r\n  -webkit-appearance: menulist;\r\n  -moz-appearance: menulist;\r\n  appearance: menulist;\r\n}\r\n\r\n/* 开关样式 */\r\n.switch {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 50px;\r\n  height: 24px;\r\n}\r\n\r\n.switch input {\r\n  opacity: 0;\r\n  width: 0;\r\n  height: 0;\r\n}\r\n\r\n.slider {\r\n  position: absolute;\r\n  cursor: pointer;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: rgba(255, 255, 255, 0.2);\r\n  transition: 0.3s;\r\n  border-radius: 24px;\r\n}\r\n\r\n.slider:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 18px;\r\n  width: 18px;\r\n  left: 3px;\r\n  bottom: 3px;\r\n  background-color: white;\r\n  transition: 0.3s;\r\n  border-radius: 50%;\r\n}\r\n\r\ninput:checked + .slider {\r\n  background-color: #4ecdc4;\r\n}\r\n\r\ninput:checked + .slider:before {\r\n  transform: translateX(26px);\r\n}\r\n\r\n/* 按钮样式 */\r\n.form-actions {\r\n  display: flex;\r\n  gap: 15px;\r\n  justify-content: flex-end;\r\n  margin-top: 30px;\r\n  padding-top: 20px;\r\n  border-top: 1px solid rgba(255, 255, 255, 0.1);\r\n}\r\n\r\n.btn {\r\n  padding: 12px 24px;\r\n  border: none;\r\n  border-radius: 8px;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  font-weight: 500;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.btn:disabled {\r\n  opacity: 0.6;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.btn-primary {\r\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\r\n  color: white;\r\n  box-shadow: 0 4px 12px rgba(78, 205, 196, 0.3);\r\n}\r\n\r\n.btn-primary:hover:not(:disabled) {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 6px 16px rgba(78, 205, 196, 0.4);\r\n}\r\n\r\n/* 警告和成功消息 */\r\n.alert {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  padding: 15px 20px;\r\n  border-radius: 8px;\r\n  margin-bottom: 20px;\r\n  font-size: 18px; /* 增大4号字体 */\r\n  font-weight: 500;\r\n}\r\n\r\n.alert-error {\r\n  background: rgba(244, 67, 54, 0.1);\r\n  border: 1px solid rgba(244, 67, 54, 0.3);\r\n  color: #f44336;\r\n}\r\n\r\n.alert-success {\r\n  background: rgba(76, 175, 80, 0.1);\r\n  border: 1px solid rgba(76, 175, 80, 0.3);\r\n  color: #4caf50;\r\n}\r\n\r\n.alert-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .settings-content {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .settings-sidebar {\r\n    width: 100%;\r\n  }\r\n  \r\n  .sidebar-nav {\r\n    flex-direction: row;\r\n    overflow-x: auto;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .nav-item {\r\n    white-space: nowrap;\r\n    min-width: 120px;\r\n  }\r\n  \r\n  .info-grid {\r\n    grid-template-columns: 1fr;\r\n  }\r\n  \r\n  .preference-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .preference-control {\r\n    margin-left: 0;\r\n    width: 100%;\r\n  }\r\n  \r\n  .form-actions {\r\n    justify-content: center;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .personal-settings {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .settings-main {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .form-section {\r\n    padding: 15px;\r\n  }\r\n}\r\n", "/* 全局主题样式文件 */\n\n/* 默认深色主题 */\nbody, body.theme-dark {\n  background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n  color: #ffffff;\n}\n\n/* 浅色主题 */\nbody.theme-light {\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  color: #333333;\n}\n\n/* 浅色主题下的组件样式调整 */\nbody.theme-light .personal-settings {\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n  color: #333333;\n}\n\nbody.theme-light .settings-sidebar {\n  background: rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nbody.theme-light .settings-main {\n  background: rgba(0, 0, 0, 0.05);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nbody.theme-light .form-section {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nbody.theme-light .preference-item {\n  background: rgba(255, 255, 255, 0.6);\n  border: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nbody.theme-light .nav-item {\n  color: #555555;\n}\n\nbody.theme-light .nav-item:hover {\n  background: rgba(78, 205, 196, 0.1);\n  color: #4ecdc4;\n}\n\nbody.theme-light .nav-item.active {\n  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\n  color: white;\n}\n\nbody.theme-light .form-group input,\nbody.theme-light .form-group select,\nbody.theme-light .form-select {\n  background: rgba(255, 255, 255, 0.9);\n  color: #333333;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n}\n\nbody.theme-light .form-select option {\n  background: #ffffff !important;\n  color: #333333 !important;\n}\n\nbody.theme-light .info-value {\n  background: rgba(255, 255, 255, 0.8);\n  color: #333333;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n}\n\nbody.theme-light .tab-header h2 {\n  color: #4ecdc4;\n}\n\nbody.theme-light .tab-header p {\n  color: #666666;\n}\n\nbody.theme-light .preference-info h4 {\n  color: #333333;\n}\n\nbody.theme-light .preference-info p {\n  color: #666666;\n}\n\nbody.theme-light .form-section h3 {\n  color: #4ecdc4;\n}\n\n/* 自动主题 - 跟随系统设置 */\n@media (prefers-color-scheme: dark) {\n  body.theme-auto {\n    background: radial-gradient(ellipse at bottom, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);\n    color: #ffffff;\n  }\n  \n  body.theme-auto .form-select option {\n    background: #4ecdc4 !important;\n    color: #ffffff !important;\n  }\n}\n\n@media (prefers-color-scheme: light) {\n  body.theme-auto {\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n    color: #333333;\n  }\n  \n  body.theme-auto .personal-settings {\n    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\n    color: #333333;\n  }\n  \n  body.theme-auto .settings-sidebar {\n    background: rgba(0, 0, 0, 0.05);\n    border: 1px solid rgba(0, 0, 0, 0.1);\n  }\n  \n  body.theme-auto .settings-main {\n    background: rgba(0, 0, 0, 0.05);\n    border: 1px solid rgba(0, 0, 0, 0.1);\n  }\n  \n  body.theme-auto .form-section {\n    background: rgba(255, 255, 255, 0.8);\n    border: 1px solid rgba(0, 0, 0, 0.1);\n  }\n  \n  body.theme-auto .preference-item {\n    background: rgba(255, 255, 255, 0.6);\n    border: 1px solid rgba(0, 0, 0, 0.1);\n  }\n  \n  body.theme-auto .nav-item {\n    color: #555555;\n  }\n  \n  body.theme-auto .nav-item:hover {\n    background: rgba(78, 205, 196, 0.1);\n    color: #4ecdc4;\n  }\n  \n  body.theme-auto .nav-item.active {\n    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);\n    color: white;\n  }\n  \n  body.theme-auto .form-group input,\n  body.theme-auto .form-group select,\n  body.theme-auto .form-select {\n    background: rgba(255, 255, 255, 0.9);\n    color: #333333;\n    border: 1px solid rgba(0, 0, 0, 0.2);\n  }\n  \n  body.theme-auto .form-select option {\n    background: #ffffff !important;\n    color: #333333 !important;\n  }\n  \n  body.theme-auto .info-value {\n    background: rgba(255, 255, 255, 0.8);\n    color: #333333;\n    border: 1px solid rgba(0, 0, 0, 0.1);\n  }\n  \n  body.theme-auto .tab-header h2 {\n    color: #4ecdc4;\n  }\n  \n  body.theme-auto .tab-header p {\n    color: #666666;\n  }\n  \n  body.theme-auto .preference-info h4 {\n    color: #333333;\n  }\n  \n  body.theme-auto .preference-info p {\n    color: #666666;\n  }\n  \n  body.theme-auto .form-section h3 {\n    color: #4ecdc4;\n  }\n}\n\n/* 主题切换动画 */\nbody {\n  transition: background 0.3s ease, color 0.3s ease;\n}\n\n.personal-settings,\n.settings-sidebar,\n.settings-main,\n.form-section,\n.preference-item,\n.nav-item,\n.form-group input,\n.form-group select,\n.form-select,\n.info-value {\n  transition: background 0.3s ease, color 0.3s ease, border-color 0.3s ease;\n}\n"], "names": [], "sourceRoot": ""}