{"ast": null, "code": "import _objectWithoutProperties from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"rowSpan\",\"isMergedStart\",\"isMergedCell\"];import*as XLSX from'xlsx';class KPIService{constructor(){this.workbook=null;this.data=[];this.syncCallbacks={onSuccess:null,onError:null};}// 加载KPI数据\nasync loadKPIData(){try{// 首先尝试从后端API加载真实数据（从\"月度重点KPI\"工作表）\nconsole.log('正在从\"月度重点KPI\"工作表加载数据...');const response=await fetch('http://localhost:3001/api/monthly-kpi-data');if(response.ok){const result=await response.json();if(result.data&&result.data.length>0){this.data=result.data;this.lastModified=new Date(result.lastModified);console.log('从\"月度重点KPI\"工作表成功加载数据:',this.data);return this.data;}else{console.warn('月度重点KPI工作表数据不完整');}}else{console.warn('月度重点KPI API响应失败，状态码:',response.status);}}catch(error){console.error('从月度重点KPI工作表加载数据失败:',error);}// 如果API失败，使用本地静态数据\nconsole.log('使用本地静态KPI数据');this.data=this.getStaticKPIData();return this.data;}// 获取静态KPI数据（模拟\"月度重点KPI\"工作表数据）\ngetStaticKPIData(){return[{序号:1,指标:'产品线毛利率',目标值:'橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',分值:6,'统计方式&口径':'财务部门统计',考核标准:'完成目标得满分，未完成按比例得分。','4月':'4月','4月完成情况':'','4月得分':'','5月':'5月','5月完成情况':'','5月得分':''},{序号:2,指标:'开发降本（万元）',目标值:'≥5000',分值:8,'统计方式&口径':'成本管控部门统计',考核标准:'完成目标得满分，未完成按比例得分。','4月':'4月','4月完成情况':'','4月得分':'','5月':'5月','5月完成情况':'','5月得分':''}];}// 更新数据 - 实现双向同步\nasync updateData(index,field,value){if(this.data[index]){// 更新内存中的数据\nthis.data[index][field]=value;try{// 调用后端API同步到Excel文件\nawait this.syncToExcel(index,field,value);if(this.syncCallbacks.onSuccess){this.syncCallbacks.onSuccess();}}catch(error){console.error('同步到Excel失败:',error);if(this.syncCallbacks.onError){this.syncCallbacks.onError(error);}}}}// 同步到Excel文件（月度重点KPI工作表）\nasync syncToExcel(index,field,value){try{const response=await fetch('http://localhost:3001/api/update-monthly-kpi-data',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({rowIndex:index,field:field,value:value,worksheet:'月度重点KPI',timestamp:new Date().toISOString()})});if(!response.ok){throw new Error(\"\\u540C\\u6B65\\u5230\\u6708\\u5EA6\\u91CD\\u70B9KPI\\u5DE5\\u4F5C\\u8868\\u5931\\u8D25: \".concat(response.status));}const result=await response.json();console.log('月度重点KPI工作表同步成功:',result);}catch(error){console.error('月度重点KPI工作表同步错误:',error);throw error;}}// 转换数据为Excel格式\nconvertDataForExcel(monthPair){const[firstMonth,secondMonth]=monthPair;return this.data.map(row=>{// 过滤掉内部使用的属性\nconst{rowSpan,isMergedStart,isMergedCell}=row,cleanRow=_objectWithoutProperties(row,_excluded);// 只保留固定列和指定月份的列\nconst result={序号:cleanRow.序号,指标:cleanRow.指标,目标值:cleanRow.目标值,分值:cleanRow.分值,'统计方式&口径':cleanRow['统计方式&口径']||cleanRow.统计方式,考核标准:cleanRow.考核标准};// 添加指定月份的列\nresult[\"\".concat(firstMonth,\"\\u6708\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\")];result[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")];result[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")]=cleanRow[\"\".concat(firstMonth,\"\\u6708\\u5F97\\u5206\")];result[\"\".concat(secondMonth,\"\\u6708\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\")];result[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\\u5B8C\\u6210\\u60C5\\u51B5\")];result[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")]=cleanRow[\"\".concat(secondMonth,\"\\u6708\\u5F97\\u5206\")];return result;});}// 设置同步回调\nsetSyncCallbacks(onSuccess,onError){this.syncCallbacks.onSuccess=onSuccess;this.syncCallbacks.onError=onError;}// 获取数据统计\ngetDataStats(){const totalScore=this.data.reduce((sum,item)=>{const score=item.分值;if(score===''||score===null||score===undefined)return sum;return sum+(typeof score==='number'?score:score?Number(score)||0:0);},0);return{totalRecords:this.data.length,totalScore:totalScore,lastModified:this.lastModified};}// 历史追溯功能\nasync getChangeHistory(rowIndex,field){try{const response=await fetch(\"http://localhost:3001/api/monthly-kpi-history?rowIndex=\".concat(rowIndex,\"&field=\").concat(field,\"&worksheet=\\u6708\\u5EA6\\u91CD\\u70B9KPI\"));if(response.ok){const history=await response.json();return history;}else{console.warn('获取月度重点KPI历史记录失败');return[];}}catch(error){console.error('获取月度重点KPI历史记录错误:',error);return[];}}// 批量导入数据\nasync importData(file){try{const formData=new FormData();formData.append('file',file);formData.append('worksheet','月度重点KPI');const response=await fetch('http://localhost:3001/api/import-monthly-kpi-data',{method:'POST',body:formData});if(response.ok){const result=await response.json();if(result.success){// 重新加载数据\nawait this.loadKPIData();return{success:true,message:result.message};}else{return{success:false,message:result.message};}}else{return{success:false,message:'导入月度重点KPI数据失败'};}}catch(error){console.error('导入月度重点KPI数据错误:',error);return{success:false,message:error.message};}}// 验证数据格式\nvalidateData(data){const requiredFields=['序号','指标','目标值','分值','统计方式','考核标准'];for(let i=0;i<data.length;i++){const row=data[i];for(const field of requiredFields){if(!(field in row)){return{valid:false,message:\"\\u7B2C\".concat(i+1,\"\\u884C\\u7F3A\\u5C11\\u5FC5\\u9700\\u5B57\\u6BB5: \").concat(field)};}}}return{valid:true};}}const kpiService=new KPIService();export default kpiService;", "map": {"version": 3, "names": ["XLSX", "KPIService", "constructor", "workbook", "data", "syncCallbacks", "onSuccess", "onError", "loadKPIData", "console", "log", "response", "fetch", "ok", "result", "json", "length", "lastModified", "Date", "warn", "status", "error", "getStaticKPIData", "序号", "指标", "目标值", "分值", "考核标准", "updateData", "index", "field", "value", "syncToExcel", "method", "headers", "body", "JSON", "stringify", "rowIndex", "worksheet", "timestamp", "toISOString", "Error", "concat", "convertDataForExcel", "monthPair", "firstMonth", "second<PERSON><PERSON><PERSON>", "map", "row", "rowSpan", "isMergedStart", "isMergedCell", "cleanRow", "_objectWithoutProperties", "_excluded", "统计方式", "setSyncCallbacks", "getDataStats", "totalScore", "reduce", "sum", "item", "score", "undefined", "Number", "totalRecords", "getChangeHistory", "history", "importData", "file", "formData", "FormData", "append", "success", "message", "validateData", "requiredFields", "i", "valid", "kpiService"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块四/services/kpiService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\n\r\nclass KPIService {\r\n  constructor() {\r\n    this.workbook = null;\r\n    this.data = [];\r\n    this.syncCallbacks = {\r\n      onSuccess: null,\r\n      onError: null\r\n    };\r\n  }\r\n\r\n  // 加载KPI数据\r\n  async loadKPIData() {\r\n    try {\r\n      // 首先尝试从后端API加载真实数据（从\"月度重点KPI\"工作表）\r\n      console.log('正在从\"月度重点KPI\"工作表加载数据...');\r\n      const response = await fetch('http://localhost:3001/api/monthly-kpi-data');\r\n      \r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.data && result.data.length > 0) {\r\n          this.data = result.data;\r\n          this.lastModified = new Date(result.lastModified);\r\n          console.log('从\"月度重点KPI\"工作表成功加载数据:', this.data);\r\n          return this.data;\r\n        } else {\r\n          console.warn('月度重点KPI工作表数据不完整');\r\n        }\r\n      } else {\r\n        console.warn('月度重点KPI API响应失败，状态码:', response.status);\r\n      }\r\n    } catch (error) {\r\n      console.error('从月度重点KPI工作表加载数据失败:', error);\r\n    }\r\n\r\n    // 如果API失败，使用本地静态数据\r\n    console.log('使用本地静态KPI数据');\r\n    this.data = this.getStaticKPIData();\r\n    return this.data;\r\n  }\r\n\r\n  // 获取静态KPI数据（模拟\"月度重点KPI\"工作表数据）\r\n  getStaticKPIData() {\r\n    return [\r\n      {\r\n        序号: 1,\r\n        指标: '产品线毛利率',\r\n        目标值: '橡胶金属≥35.60%、空气弹簧≥50%（达到48.43%不考核）、系统件≥34.31%、属地化≥50%（达到48.59%不考核）、车体新材料≥15%',\r\n        分值: 6,\r\n        '统计方式&口径': '财务部门统计',\r\n        考核标准: '完成目标得满分，未完成按比例得分。',\r\n        '4月': '4月',\r\n        '4月完成情况': '',\r\n        '4月得分': '',\r\n        '5月': '5月', \r\n        '5月完成情况': '',\r\n        '5月得分': ''\r\n      },\r\n      {\r\n        序号: 2,\r\n        指标: '开发降本（万元）',\r\n        目标值: '≥5000',\r\n        分值: 8,\r\n        '统计方式&口径': '成本管控部门统计',\r\n        考核标准: '完成目标得满分，未完成按比例得分。',\r\n        '4月': '4月',\r\n        '4月完成情况': '',\r\n        '4月得分': '',\r\n        '5月': '5月',\r\n        '5月完成情况': '',\r\n        '5月得分': ''\r\n      }\r\n    ];\r\n  }\r\n\r\n  // 更新数据 - 实现双向同步\r\n  async updateData(index, field, value) {\r\n    if (this.data[index]) {\r\n      // 更新内存中的数据\r\n      this.data[index][field] = value;\r\n      \r\n      try {\r\n        // 调用后端API同步到Excel文件\r\n        await this.syncToExcel(index, field, value);\r\n        \r\n        if (this.syncCallbacks.onSuccess) {\r\n          this.syncCallbacks.onSuccess();\r\n        }\r\n      } catch (error) {\r\n        console.error('同步到Excel失败:', error);\r\n        if (this.syncCallbacks.onError) {\r\n          this.syncCallbacks.onError(error);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 同步到Excel文件（月度重点KPI工作表）\r\n  async syncToExcel(index, field, value) {\r\n    try {\r\n      const response = await fetch('http://localhost:3001/api/update-monthly-kpi-data', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          rowIndex: index,\r\n          field: field,\r\n          value: value,\r\n          worksheet: '月度重点KPI',\r\n          timestamp: new Date().toISOString()\r\n        })\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`同步到月度重点KPI工作表失败: ${response.status}`);\r\n      }\r\n\r\n      const result = await response.json();\r\n      console.log('月度重点KPI工作表同步成功:', result);\r\n    } catch (error) {\r\n      console.error('月度重点KPI工作表同步错误:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 转换数据为Excel格式\r\n  convertDataForExcel(monthPair) {\r\n    const [firstMonth, secondMonth] = monthPair;\r\n    \r\n    return this.data.map(row => {\r\n      // 过滤掉内部使用的属性\r\n      const { rowSpan, isMergedStart, isMergedCell, ...cleanRow } = row;\r\n      \r\n      // 只保留固定列和指定月份的列\r\n      const result = {\r\n        序号: cleanRow.序号,\r\n        指标: cleanRow.指标,\r\n        目标值: cleanRow.目标值,\r\n        分值: cleanRow.分值,\r\n        '统计方式&口径': cleanRow['统计方式&口径'] || cleanRow.统计方式,\r\n        考核标准: cleanRow.考核标准,\r\n      };\r\n      \r\n      // 添加指定月份的列\r\n      result[`${firstMonth}月`] = cleanRow[`${firstMonth}月`];\r\n      result[`${firstMonth}月完成情况`] = cleanRow[`${firstMonth}月完成情况`];\r\n      result[`${firstMonth}月得分`] = cleanRow[`${firstMonth}月得分`];\r\n      result[`${secondMonth}月`] = cleanRow[`${secondMonth}月`];\r\n      result[`${secondMonth}月完成情况`] = cleanRow[`${secondMonth}月完成情况`];\r\n      result[`${secondMonth}月得分`] = cleanRow[`${secondMonth}月得分`];\r\n      \r\n      return result;\r\n    });\r\n  }\r\n\r\n  // 设置同步回调\r\n  setSyncCallbacks(onSuccess, onError) {\r\n    this.syncCallbacks.onSuccess = onSuccess;\r\n    this.syncCallbacks.onError = onError;\r\n  }\r\n\r\n  // 获取数据统计\r\n  getDataStats() {\r\n    const totalScore = this.data.reduce((sum, item) => {\r\n      const score = item.分值;\r\n      if (score === '' || score === null || score === undefined) return sum;\r\n      return sum + (typeof score === 'number' ? score : (score ? Number(score) || 0 : 0));\r\n    }, 0);\r\n\r\n    return {\r\n      totalRecords: this.data.length,\r\n      totalScore: totalScore,\r\n      lastModified: this.lastModified\r\n    };\r\n  }\r\n\r\n  // 历史追溯功能\r\n  async getChangeHistory(rowIndex, field) {\r\n    try {\r\n      const response = await fetch(`http://localhost:3001/api/monthly-kpi-history?rowIndex=${rowIndex}&field=${field}&worksheet=月度重点KPI`);\r\n      \r\n      if (response.ok) {\r\n        const history = await response.json();\r\n        return history;\r\n      } else {\r\n        console.warn('获取月度重点KPI历史记录失败');\r\n        return [];\r\n      }\r\n    } catch (error) {\r\n      console.error('获取月度重点KPI历史记录错误:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 批量导入数据\r\n  async importData(file) {\r\n    try {\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('worksheet', '月度重点KPI');\r\n      \r\n      const response = await fetch('http://localhost:3001/api/import-monthly-kpi-data', {\r\n        method: 'POST',\r\n        body: formData\r\n      });\r\n      \r\n      if (response.ok) {\r\n        const result = await response.json();\r\n        if (result.success) {\r\n          // 重新加载数据\r\n          await this.loadKPIData();\r\n          return { success: true, message: result.message };\r\n        } else {\r\n          return { success: false, message: result.message };\r\n        }\r\n      } else {\r\n        return { success: false, message: '导入月度重点KPI数据失败' };\r\n      }\r\n    } catch (error) {\r\n      console.error('导入月度重点KPI数据错误:', error);\r\n      return { success: false, message: error.message };\r\n    }\r\n  }\r\n\r\n  // 验证数据格式\r\n  validateData(data) {\r\n    const requiredFields = ['序号', '指标', '目标值', '分值', '统计方式', '考核标准'];\r\n    \r\n    for (let i = 0; i < data.length; i++) {\r\n      const row = data[i];\r\n      for (const field of requiredFields) {\r\n        if (!(field in row)) {\r\n          return {\r\n            valid: false,\r\n            message: `第${i + 1}行缺少必需字段: ${field}`\r\n          };\r\n        }\r\n      }\r\n    }\r\n    \r\n    return { valid: true };\r\n  }\r\n}\r\n\r\nconst kpiService = new KPIService();\r\nexport default kpiService; "], "mappings": "+RAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,UAAW,CACfC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,IAAI,CAAG,EAAE,CACd,IAAI,CAACC,aAAa,CAAG,CACnBC,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IACX,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,WAAWA,CAAA,CAAG,CAClB,GAAI,CACF;AACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC,CACrC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,4CAA4C,CAAC,CAE1E,GAAID,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACV,IAAI,EAAIU,MAAM,CAACV,IAAI,CAACY,MAAM,CAAG,CAAC,CAAE,CACzC,IAAI,CAACZ,IAAI,CAAGU,MAAM,CAACV,IAAI,CACvB,IAAI,CAACa,YAAY,CAAG,GAAI,CAAAC,IAAI,CAACJ,MAAM,CAACG,YAAY,CAAC,CACjDR,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAE,IAAI,CAACN,IAAI,CAAC,CAC9C,MAAO,KAAI,CAACA,IAAI,CAClB,CAAC,IAAM,CACLK,OAAO,CAACU,IAAI,CAAC,iBAAiB,CAAC,CACjC,CACF,CAAC,IAAM,CACLV,OAAO,CAACU,IAAI,CAAC,sBAAsB,CAAER,QAAQ,CAACS,MAAM,CAAC,CACvD,CACF,CAAE,MAAOC,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC5C,CAEA;AACAZ,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC,CAC1B,IAAI,CAACN,IAAI,CAAG,IAAI,CAACkB,gBAAgB,CAAC,CAAC,CACnC,MAAO,KAAI,CAAClB,IAAI,CAClB,CAEA;AACAkB,gBAAgBA,CAAA,CAAG,CACjB,MAAO,CACL,CACEC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,QAAQ,CACZC,GAAG,CAAE,6EAA6E,CAClFC,EAAE,CAAE,CAAC,CACL,SAAS,CAAE,QAAQ,CACnBC,IAAI,CAAE,mBAAmB,CACzB,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,EAAE,CACZ,MAAM,CAAE,EAAE,CACV,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,EAAE,CACZ,MAAM,CAAE,EACV,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,UAAU,CACdC,GAAG,CAAE,OAAO,CACZC,EAAE,CAAE,CAAC,CACL,SAAS,CAAE,UAAU,CACrBC,IAAI,CAAE,mBAAmB,CACzB,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,EAAE,CACZ,MAAM,CAAE,EAAE,CACV,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,EAAE,CACZ,MAAM,CAAE,EACV,CAAC,CACF,CACH,CAEA;AACA,KAAM,CAAAC,UAAUA,CAACC,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACpC,GAAI,IAAI,CAAC3B,IAAI,CAACyB,KAAK,CAAC,CAAE,CACpB;AACA,IAAI,CAACzB,IAAI,CAACyB,KAAK,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CAE/B,GAAI,CACF;AACA,KAAM,KAAI,CAACC,WAAW,CAACH,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAC,CAE3C,GAAI,IAAI,CAAC1B,aAAa,CAACC,SAAS,CAAE,CAChC,IAAI,CAACD,aAAa,CAACC,SAAS,CAAC,CAAC,CAChC,CACF,CAAE,MAAOe,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,aAAa,CAAEA,KAAK,CAAC,CACnC,GAAI,IAAI,CAAChB,aAAa,CAACE,OAAO,CAAE,CAC9B,IAAI,CAACF,aAAa,CAACE,OAAO,CAACc,KAAK,CAAC,CACnC,CACF,CACF,CACF,CAEA;AACA,KAAM,CAAAW,WAAWA,CAACH,KAAK,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACrC,GAAI,CACF,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mDAAmD,CAAE,CAChFqB,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBC,QAAQ,CAAET,KAAK,CACfC,KAAK,CAAEA,KAAK,CACZC,KAAK,CAAEA,KAAK,CACZQ,SAAS,CAAE,SAAS,CACpBC,SAAS,CAAE,GAAI,CAAAtB,IAAI,CAAC,CAAC,CAACuB,WAAW,CAAC,CACpC,CAAC,CACH,CAAC,CAAC,CAEF,GAAI,CAAC9B,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,IAAI,CAAA6B,KAAK,iFAAAC,MAAA,CAAqBhC,QAAQ,CAACS,MAAM,CAAE,CAAC,CACxD,CAEA,KAAM,CAAAN,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpCN,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAEI,MAAM,CAAC,CACxC,CAAE,MAAOO,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,iBAAiB,CAAEA,KAAK,CAAC,CACvC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAuB,mBAAmBA,CAACC,SAAS,CAAE,CAC7B,KAAM,CAACC,UAAU,CAAEC,WAAW,CAAC,CAAGF,SAAS,CAE3C,MAAO,KAAI,CAACzC,IAAI,CAAC4C,GAAG,CAACC,GAAG,EAAI,CAC1B;AACA,KAAM,CAAEC,OAAO,CAAEC,aAAa,CAAEC,YAA0B,CAAC,CAAGH,GAAG,CAAhBI,QAAQ,CAAAC,wBAAA,CAAKL,GAAG,CAAAM,SAAA,EAEjE;AACA,KAAM,CAAAzC,MAAM,CAAG,CACbS,EAAE,CAAE8B,QAAQ,CAAC9B,EAAE,CACfC,EAAE,CAAE6B,QAAQ,CAAC7B,EAAE,CACfC,GAAG,CAAE4B,QAAQ,CAAC5B,GAAG,CACjBC,EAAE,CAAE2B,QAAQ,CAAC3B,EAAE,CACf,SAAS,CAAE2B,QAAQ,CAAC,SAAS,CAAC,EAAIA,QAAQ,CAACG,IAAI,CAC/C7B,IAAI,CAAE0B,QAAQ,CAAC1B,IACjB,CAAC,CAED;AACAb,MAAM,IAAA6B,MAAA,CAAIG,UAAU,WAAI,CAAGO,QAAQ,IAAAV,MAAA,CAAIG,UAAU,WAAI,CACrDhC,MAAM,IAAA6B,MAAA,CAAIG,UAAU,mCAAQ,CAAGO,QAAQ,IAAAV,MAAA,CAAIG,UAAU,mCAAQ,CAC7DhC,MAAM,IAAA6B,MAAA,CAAIG,UAAU,uBAAM,CAAGO,QAAQ,IAAAV,MAAA,CAAIG,UAAU,uBAAM,CACzDhC,MAAM,IAAA6B,MAAA,CAAII,WAAW,WAAI,CAAGM,QAAQ,IAAAV,MAAA,CAAII,WAAW,WAAI,CACvDjC,MAAM,IAAA6B,MAAA,CAAII,WAAW,mCAAQ,CAAGM,QAAQ,IAAAV,MAAA,CAAII,WAAW,mCAAQ,CAC/DjC,MAAM,IAAA6B,MAAA,CAAII,WAAW,uBAAM,CAAGM,QAAQ,IAAAV,MAAA,CAAII,WAAW,uBAAM,CAE3D,MAAO,CAAAjC,MAAM,CACf,CAAC,CAAC,CACJ,CAEA;AACA2C,gBAAgBA,CAACnD,SAAS,CAAEC,OAAO,CAAE,CACnC,IAAI,CAACF,aAAa,CAACC,SAAS,CAAGA,SAAS,CACxC,IAAI,CAACD,aAAa,CAACE,OAAO,CAAGA,OAAO,CACtC,CAEA;AACAmD,YAAYA,CAAA,CAAG,CACb,KAAM,CAAAC,UAAU,CAAG,IAAI,CAACvD,IAAI,CAACwD,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAK,CACjD,KAAM,CAAAC,KAAK,CAAGD,IAAI,CAACpC,EAAE,CACrB,GAAIqC,KAAK,GAAK,EAAE,EAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAH,GAAG,CACrE,MAAO,CAAAA,GAAG,EAAI,MAAO,CAAAE,KAAK,GAAK,QAAQ,CAAGA,KAAK,CAAIA,KAAK,CAAGE,MAAM,CAACF,KAAK,CAAC,EAAI,CAAC,CAAG,CAAE,CAAC,CACrF,CAAC,CAAE,CAAC,CAAC,CAEL,MAAO,CACLG,YAAY,CAAE,IAAI,CAAC9D,IAAI,CAACY,MAAM,CAC9B2C,UAAU,CAAEA,UAAU,CACtB1C,YAAY,CAAE,IAAI,CAACA,YACrB,CAAC,CACH,CAEA;AACA,KAAM,CAAAkD,gBAAgBA,CAAC7B,QAAQ,CAAER,KAAK,CAAE,CACtC,GAAI,CACF,KAAM,CAAAnB,QAAQ,CAAG,KAAM,CAAAC,KAAK,2DAAA+B,MAAA,CAA2DL,QAAQ,YAAAK,MAAA,CAAUb,KAAK,0CAAoB,CAAC,CAEnI,GAAInB,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAuD,OAAO,CAAG,KAAM,CAAAzD,QAAQ,CAACI,IAAI,CAAC,CAAC,CACrC,MAAO,CAAAqD,OAAO,CAChB,CAAC,IAAM,CACL3D,OAAO,CAACU,IAAI,CAAC,iBAAiB,CAAC,CAC/B,MAAO,EAAE,CACX,CACF,CAAE,MAAOE,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,kBAAkB,CAAEA,KAAK,CAAC,CACxC,MAAO,EAAE,CACX,CACF,CAEA;AACA,KAAM,CAAAgD,UAAUA,CAACC,IAAI,CAAE,CACrB,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,QAAQ,CAAC,CAAC,CAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,CAAEH,IAAI,CAAC,CAC7BC,QAAQ,CAACE,MAAM,CAAC,WAAW,CAAE,SAAS,CAAC,CAEvC,KAAM,CAAA9D,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAAC,mDAAmD,CAAE,CAChFqB,MAAM,CAAE,MAAM,CACdE,IAAI,CAAEoC,QACR,CAAC,CAAC,CAEF,GAAI5D,QAAQ,CAACE,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAAC4D,OAAO,CAAE,CAClB;AACA,KAAM,KAAI,CAAClE,WAAW,CAAC,CAAC,CACxB,MAAO,CAAEkE,OAAO,CAAE,IAAI,CAAEC,OAAO,CAAE7D,MAAM,CAAC6D,OAAQ,CAAC,CACnD,CAAC,IAAM,CACL,MAAO,CAAED,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE7D,MAAM,CAAC6D,OAAQ,CAAC,CACpD,CACF,CAAC,IAAM,CACL,MAAO,CAAED,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,eAAgB,CAAC,CACrD,CACF,CAAE,MAAOtD,KAAK,CAAE,CACdZ,OAAO,CAACY,KAAK,CAAC,gBAAgB,CAAEA,KAAK,CAAC,CACtC,MAAO,CAAEqD,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAEtD,KAAK,CAACsD,OAAQ,CAAC,CACnD,CACF,CAEA;AACAC,YAAYA,CAACxE,IAAI,CAAE,CACjB,KAAM,CAAAyE,cAAc,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,IAAI,CAAE,MAAM,CAAE,MAAM,CAAC,CAEhE,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAG1E,IAAI,CAACY,MAAM,CAAE8D,CAAC,EAAE,CAAE,CACpC,KAAM,CAAA7B,GAAG,CAAG7C,IAAI,CAAC0E,CAAC,CAAC,CACnB,IAAK,KAAM,CAAAhD,KAAK,GAAI,CAAA+C,cAAc,CAAE,CAClC,GAAI,EAAE/C,KAAK,GAAI,CAAAmB,GAAG,CAAC,CAAE,CACnB,MAAO,CACL8B,KAAK,CAAE,KAAK,CACZJ,OAAO,UAAAhC,MAAA,CAAMmC,CAAC,CAAG,CAAC,iDAAAnC,MAAA,CAAYb,KAAK,CACrC,CAAC,CACH,CACF,CACF,CAEA,MAAO,CAAEiD,KAAK,CAAE,IAAK,CAAC,CACxB,CACF,CAEA,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAA/E,UAAU,CAAC,CAAC,CACnC,cAAe,CAAA+E,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}