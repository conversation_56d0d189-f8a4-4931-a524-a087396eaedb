import * as XLSX from 'xlsx';

class WorldClassDownloadService {
  constructor() {
    this.baseURL = '/api';
  }

  // 处理选择性下载
  async handleSelectiveDownload(selectionData) {
    const { selectedData, format, monthRange, monthPairs, statistics } = selectionData;
    
    try {
      switch (format) {
        case 'excel':
          return await this.generateExcel(selectedData, monthRange, monthPairs, statistics);
        case 'pdf':
          return await this.generatePDF(selectedData, monthRange, monthPairs, statistics);
        case 'csv':
          return await this.generateCSV(selectedData, monthRange, monthPairs, statistics);
        default:
          throw new Error(`不支持的格式: ${format}`);
      }
    } catch (error) {
      console.error('下载失败:', error);
      throw error;
    }
  }

  // 生成Excel文件
  async generateExcel(selectedData, monthRange, monthPairs, statistics) {
    try {
      // 创建新的工作簿
      const workbook = XLSX.utils.book_new();
      
      // 创建主数据工作表
      this.createMainWorksheet(workbook, selectedData, monthRange, monthPairs);
      
      // 创建汇总页
      this.createSummarySheet(workbook, selectedData, monthRange, monthPairs, statistics);

      // 生成并下载文件
      const fileName = `对标世界一流举措导出_${this.formatDate(new Date())}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      
      return {
        success: true,
        fileName,
        message: `Excel文件已生成: ${fileName}`
      };
    } catch (error) {
      console.error('Excel生成失败:', error);
      throw error;
    }
  }

  // 创建主数据工作表
  createMainWorksheet(workbook, selectedData, monthRange, monthPairs) {
    // 准备表头
    const headers = [
      '序号', '工作准则', '2025年目标', '2025年举措', '负责人', '权重'
    ];
    
    // 添加月份列 - 根据选择的月份范围
    for (let i = monthRange.start; i <= monthRange.end; i++) {
      const [month1, month2] = monthPairs[i];
      headers.push(`${month1}工作计划`, `${month1}完成情况`);
      headers.push(`${month2}工作计划`, `${month2}完成情况`);
    }
    
    headers.push('备注');
    
    // 准备数据行
    const rows = [headers];
    
    selectedData.forEach((item, index) => {
      const data = item.data;
      const row = [
        index + 1, // 重新编号
        this.formatValue(data.工作准则),
        this.formatValue(data['2025年目标']),
        this.formatValue(data['2025年举措']),
        this.formatValue(data.负责人),
        this.formatValue(data.权重)
      ];
      
      // 添加月份数据
      for (let i = monthRange.start; i <= monthRange.end; i++) {
        const [month1, month2] = monthPairs[i];
        row.push(
          this.formatValue(data[`${month1}工作计划`]),
          this.formatValue(data[`${month1}完成情况`]),
          this.formatValue(data[`${month2}工作计划`]),
          this.formatValue(data[`${month2}完成情况`])
        );
      }
      
      row.push(this.formatValue(data.备注));
      rows.push(row);
    });

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(rows);
    
    // 设置列宽
    const colWidths = [
      { wch: 8 },   // 序号
      { wch: 18 },  // 工作准则（缩小一点）
      { wch: 20 },  // 2025年目标
      { wch: 20 },  // 2025年举措
      { wch: 12 },  // 负责人（扩大一点点）
      { wch: 8 },   // 权重
    ];
    
    // 为每个月份对添加列宽（工作计划和完成情况）
    for (let i = monthRange.start; i <= monthRange.end; i++) {
      colWidths.push({ wch: 20 }); // 工作计划
      colWidths.push({ wch: 20 }); // 完成情况
      colWidths.push({ wch: 20 }); // 工作计划
      colWidths.push({ wch: 20 }); // 完成情况
    }
    
    colWidths.push({ wch: 15 }); // 备注
    
    worksheet['!cols'] = colWidths;

    // 设置表头样式
    this.setHeaderStyle(worksheet, headers.length);
    
    // 添加到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '对标世界一流举措数据');
  }

  // 创建汇总页
  createSummarySheet(workbook, selectedData, monthRange, monthPairs, statistics) {
    const startMonth = monthPairs[monthRange.start]?.[0] || '';
    const endMonth = monthPairs[monthRange.end]?.[1] || '';
    
    const summaryData = [
      ['对标世界一流举措导出汇总', '', '', ''],
      ['导出时间', this.formatDateTime(new Date()), '', ''],
      ['月份范围', `${startMonth} 至 ${endMonth}`, '', ''],
      ['', '', '', ''],
      ['统计项目', '数值', '', ''],
      ['选择项目数', statistics.totalItems, '', ''],
      ['涵盖层级数', statistics.levelCount, '', ''],
      ['', '', '', '']
    ];

    const summaryWorksheet = XLSX.utils.aoa_to_sheet(summaryData);
    
    // 设置列宽
    summaryWorksheet['!cols'] = [
      { wch: 20 },
      { wch: 15 },
      { wch: 15 },
      { wch: 10 }
    ];

    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '导出汇总');
  }

  // 生成CSV文件
  async generateCSV(selectedData, monthRange, monthPairs, statistics) {
    try {
      let csvContent = '\uFEFF'; // UTF-8 BOM for Excel compatibility
      
      // 准备表头
      const headers = [
        '序号', '工作准则', '2025年目标', '2025年举措', '负责人', '权重'
      ];
      
      // 添加月份列
      for (let i = monthRange.start; i <= monthRange.end; i++) {
        const [month1, month2] = monthPairs[i];
        headers.push(`${month1}工作计划`, `${month1}完成情况`);
        headers.push(`${month2}工作计划`, `${month2}完成情况`);
      }
      
      headers.push('备注');
      
      // 添加表头
      csvContent += headers.map(h => this.escapeCsvValue(h)).join(',') + '\n';
      
      // 添加数据行
      selectedData.forEach((item, index) => {
        const data = item.data;
        const row = [
          index + 1,
          this.formatValue(data.工作准则),
          this.formatValue(data['2025年目标']),
          this.formatValue(data['2025年举措']),
          this.formatValue(data.负责人),
          this.formatValue(data.权重)
        ];
        
        // 添加月份数据
        for (let i = monthRange.start; i <= monthRange.end; i++) {
          const [month1, month2] = monthPairs[i];
          row.push(
            this.formatValue(data[`${month1}工作计划`]),
            this.formatValue(data[`${month1}完成情况`]),
            this.formatValue(data[`${month2}工作计划`]),
            this.formatValue(data[`${month2}完成情况`])
          );
        }
        
        row.push(this.formatValue(data.备注));
        
        csvContent += row.map(cell => this.escapeCsvValue(cell)).join(',') + '\n';
      });

      // 创建并下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `对标世界一流举措导出_${this.formatDate(new Date())}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'CSV文件已生成并下载'
      };
    } catch (error) {
      console.error('CSV生成失败:', error);
      throw error;
    }
  }

  // 生成PDF文件（临时实现）
  async generatePDF(selectedData, monthRange, monthPairs, statistics) {
    try {
      console.log('PDF生成功能待实现');
      
      const jsonData = this.prepareDataForExport(selectedData, monthRange, monthPairs, statistics);
      const dataStr = JSON.stringify(jsonData, null, 2);
      const blob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      
      const a = document.createElement('a');
      a.href = url;
      a.download = `对标世界一流举措导出_${this.formatDate(new Date())}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      return {
        success: true,
        message: 'PDF功能开发中，已生成JSON格式文件'
      };
    } catch (error) {
      console.error('PDF生成失败:', error);
      throw error;
    }
  }

  // 工具方法
  formatValue(value) {
    if (value === null || value === undefined || value === '') {
      return '';
    }
    
    if (typeof value === 'object') {
      if (value.hasOwnProperty('v')) return String(value.v);
      if (value.hasOwnProperty('w')) return String(value.w);
      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return String(value.v);
      if (value.text !== undefined) return String(value.text);
      if (value.richText !== undefined) return String(value.richText);
      if (value.value !== undefined) return String(value.value);
      return String(value);
    }
    
    return String(value);
  }

  escapeCsvValue(value) {
    const stringValue = this.formatValue(value);
    
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    
    return stringValue;
  }

  setHeaderStyle(worksheet, colCount) {
    // 设置表头样式（加粗）
    for (let i = 0; i < colCount; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      if (worksheet[cellRef]) {
        worksheet[cellRef].s = {
          font: { bold: true },
          fill: { fgColor: { rgb: "E8F5E8" } }
        };
      }
    }
  }

  formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}${month}${day}`;
  }

  formatDateTime(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  prepareDataForExport(selectedData, monthRange, monthPairs, statistics) {
    const startMonth = monthPairs[monthRange.start]?.[0] || '';
    const endMonth = monthPairs[monthRange.end]?.[1] || '';
    
    return {
      exportInfo: {
        exportTime: this.formatDateTime(new Date()),
        monthRange: `${startMonth} 至 ${endMonth}`,
        totalItems: statistics.totalItems,
        levelCount: statistics.levelCount
      },
      data: selectedData.map(item => item.data)
    };
  }
}

export default new WorldClassDownloadService();
