import React, { useState, useEffect } from 'react';
import '../styles/HomePage.css';

const HomePage = ({ onNavigate }) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const navigationItems = [
    {
      id: 'work-target',
      title: '工作目标管理责任书',
      subtitle: 'Work Target Management',
      icon: '🎯',
      description: '关键指标·质量指标·重点工作管理',
      color: '#20ff4d'
    },
    {
      id: 'work-tracking',
      title: '重点工作跟踪-填写表',
      subtitle: 'Work Tracking Form',
      icon: '📊',
      description: '月度工作计划与完成情况跟踪',
      color: '#00d4aa'
    },
    {
      id: 'world-class',
      title: '对标世界一流举措-提报版',
      subtitle: 'World-Class Benchmarking',
      icon: '🌍',
      description: '世界一流标准对标与举措实施',
      color: '#4dd0ff'
    },
    {
      id: 'monthly-kpi',
      title: '月度重点KPI',
      subtitle: 'Monthly Key Performance Indicators',
      icon: '📈',
      description: 'KPI指标监控与分析评估',
      color: '#ff6b4d'
    },
    {
      id: 'project-one',
      title: '1号项目责任状',
      subtitle: 'Project One Responsibility',
      icon: '🚀',
      description: '重点项目推进与责任落实',
      color: '#ff4dff'
    },
    {
      id: 'module-six',
      title: '开发中心二级部门工作目标管理责任书',
      subtitle: 'Department Target Management',
      icon: '🏢',
      description: '各部门目标管理与绩效考核',
      color: '#ffff4d'
    }
  ];

  const handleNavigation = (itemId) => {
    onNavigate(itemId);
  };

  return (
    <div className="homepage">
      {/* 背景动画 */}
      <div className="background-animation">
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="particle"></div>
        <div className="grid-lines"></div>
      </div>

      {/* 顶部时间显示 */}
      <div className="time-display">
        <div className="current-time">
          {currentTime.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          })}
        </div>
        <div className="system-status">系统运行正常</div>
      </div>

      {/* 主标题区域 */}
      <div className="main-header">
        <div className="title-container">
          <h1 className="main-title">
            智能驱动·质效双升
          </h1>
          <h2 className="sub-title">
            2025开发中心重点项目月度绩效跟踪报告
          </h2>
          <div className="title-divider"></div>
          <p className="system-description">
            含世界一流对标体系 · 智能化数据管理平台
          </p>
        </div>
      </div>

      {/* 导航网格 */}
      <div className="navigation-grid">
        {navigationItems.map((item, index) => (
          <div
            key={item.id}
            className="nav-card"
            style={{ '--delay': `${index * 0.1}s`, '--color': item.color }}
            onClick={() => handleNavigation(item.id)}
          >
            <div className="card-header">
              <div className="card-icon">{item.icon}</div>
              <div className="card-number">{String(index + 1).padStart(2, '0')}</div>
            </div>
            <div className="card-content">
              <h3 className="card-title">{item.title}</h3>
              <p className="card-subtitle">{item.subtitle}</p>
              <p className="card-description">{item.description}</p>
            </div>
            <div className="card-footer">
              <span className="enter-text">进入模块</span>
              <span className="arrow">→</span>
            </div>
            <div className="card-glow"></div>
          </div>
        ))}
      </div>

      {/* 底部状态栏 */}
      <div className="status-bar">
        <div className="status-info">
          <span className="status-label">数据同步状态:</span>
          <span className="status-value online">在线</span>
        </div>
        <div className="status-info">
          <span className="status-label">Excel文件:</span>
          <span className="status-value">已加载</span>
        </div>
        <div className="status-info">
          <span className="status-label">模块数量:</span>
          <span className="status-value">{navigationItems.length}</span>
        </div>
      </div>
    </div>
  );
};

export default HomePage; 