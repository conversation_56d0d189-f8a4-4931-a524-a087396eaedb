import React, { useState, useEffect } from 'react';
import './SelectiveDownloadModal.css';

const SelectiveDownloadModal = ({ isOpen, onClose, data, onDownload }) => {
  const [selectionState, setSelectionState] = useState({
    selectedCategories: new Set(),
    selectedItems: new Set(),
    downloadFormat: 'excel'
  });

  const [statistics, setStatistics] = useState({
    totalItems: 0,
    totalWeight: 0
  });

  // 数据分类配置
  const categories = [
    {
      key: 'keyIndicators',
      title: '关键指标（40分）',
      icon: '🎯'
    },
    {
      key: 'qualityIndicators', 
      title: '质量指标（20分）',
      icon: '⭐'
    },
    {
      key: 'keyWork',
      title: '重点工作（40分）',
      icon: '🚀'
    }
  ];

  useEffect(() => {
    updateStatistics();
  }, [selectionState.selectedItems, data]);

  // 计算统计信息
  const updateStatistics = () => {
    let totalItems = 0;
    let totalWeight = 0;

    categories.forEach(category => {
      const categoryData = data[category.key] || [];
      categoryData.forEach((item, index) => {
        const itemKey = `${category.key}-${index}`;
        if (selectionState.selectedItems.has(itemKey)) {
          totalItems++;
          const weight = item.权重;
          if (weight && typeof weight === 'number') {
            totalWeight += weight;
          } else if (weight && !isNaN(Number(weight))) {
            totalWeight += Number(weight);
          }
        }
      });
    });

    setStatistics({ totalItems, totalWeight });
  };

  // 处理分类选择
  const handleCategorySelect = (categoryKey) => {
    const newSelectedCategories = new Set(selectionState.selectedCategories);
    const newSelectedItems = new Set(selectionState.selectedItems);
    
    if (newSelectedCategories.has(categoryKey)) {
      // 取消选择分类，移除该分类下的所有项目
      newSelectedCategories.delete(categoryKey);
      const categoryData = data[categoryKey] || [];
      categoryData.forEach((_, index) => {
        newSelectedItems.delete(`${categoryKey}-${index}`);
      });
    } else {
      // 选择分类，添加该分类下的所有项目
      newSelectedCategories.add(categoryKey);
      const categoryData = data[categoryKey] || [];
      categoryData.forEach((_, index) => {
        newSelectedItems.add(`${categoryKey}-${index}`);
      });
    }

    setSelectionState({
      ...selectionState,
      selectedCategories: newSelectedCategories,
      selectedItems: newSelectedItems
    });
  };

  // 处理单项选择
  const handleItemSelect = (categoryKey, itemIndex) => {
    const itemKey = `${categoryKey}-${itemIndex}`;
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedCategories = new Set(selectionState.selectedCategories);

    if (newSelectedItems.has(itemKey)) {
      newSelectedItems.delete(itemKey);
      // 检查是否需要取消分类选择
      const categoryData = data[categoryKey] || [];
      const categoryItemsSelected = categoryData.some((_, index) => 
        newSelectedItems.has(`${categoryKey}-${index}`)
      );
      if (!categoryItemsSelected) {
        newSelectedCategories.delete(categoryKey);
      }
    } else {
      newSelectedItems.add(itemKey);
      // 检查是否需要添加分类选择
      const categoryData = data[categoryKey] || [];
      const allCategoryItemsSelected = categoryData.every((_, index) => 
        newSelectedItems.has(`${categoryKey}-${index}`) || index === itemIndex
      );
      if (allCategoryItemsSelected) {
        newSelectedCategories.add(categoryKey);
      }
    }

    setSelectionState({
      ...selectionState,
      selectedCategories: newSelectedCategories,
      selectedItems: newSelectedItems
    });
  };

  // 分类全选
  const handleCategorySelectAll = (categoryKey) => {
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedCategories = new Set(selectionState.selectedCategories);
    
    const categoryData = data[categoryKey] || [];
    categoryData.forEach((_, index) => {
      newSelectedItems.add(`${categoryKey}-${index}`);
    });
    newSelectedCategories.add(categoryKey);

    setSelectionState({
      ...selectionState,
      selectedCategories: newSelectedCategories,
      selectedItems: newSelectedItems
    });
  };

  // 分类反选
  const handleCategoryUnselectAll = (categoryKey) => {
    const newSelectedItems = new Set(selectionState.selectedItems);
    const newSelectedCategories = new Set(selectionState.selectedCategories);
    
    const categoryData = data[categoryKey] || [];
    categoryData.forEach((_, index) => {
      newSelectedItems.delete(`${categoryKey}-${index}`);
    });
    newSelectedCategories.delete(categoryKey);

    setSelectionState({
      ...selectionState,
      selectedCategories: newSelectedCategories,
      selectedItems: newSelectedItems
    });
  };

  // 处理下载
  const handleDownload = () => {
    const selectedData = {
      keyIndicators: [],
      qualityIndicators: [],
      keyWork: []
    };

    // 收集选中的数据
    categories.forEach(category => {
      const categoryData = data[category.key] || [];
      categoryData.forEach((item, index) => {
        const itemKey = `${category.key}-${index}`;
        if (selectionState.selectedItems.has(itemKey)) {
          selectedData[category.key].push({
            index,
            data: item
          });
        }
      });
    });

    onDownload({
      selectedData,
      format: selectionState.downloadFormat,
      statistics
    });
  };

  // 格式化显示值
  const formatDisplayValue = (value) => {
    if (value === null || value === undefined || value === '') return '';
    if (typeof value === 'object') {
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    return String(value);
  };

  if (!isOpen) return null;

  return (
    <div className="selective-download-overlay">
      <div className="selective-download-modal">
        <div className="modal-header">
          <h2>📊 选择要下载的数据</h2>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="modal-content">
          {categories.map(category => {
            const categoryData = data[category.key] || [];
            const isCategorySelected = selectionState.selectedCategories.has(category.key);
            
            return (
              <div key={category.key} className="category-section">
                <div className="category-header">
                  <label className="category-checkbox">
                    <input
                      type="checkbox"
                      checked={isCategorySelected}
                      onChange={() => handleCategorySelect(category.key)}
                    />
                    <span className="category-title">
                      {category.icon} {category.title}
                    </span>
                  </label>
                  <div className="category-actions">
                    <button 
                      className="action-btn"
                      onClick={() => handleCategorySelectAll(category.key)}
                    >
                      全选
                    </button>
                    <button 
                      className="action-btn"
                      onClick={() => handleCategoryUnselectAll(category.key)}
                    >
                      反选
                    </button>
                  </div>
                </div>

                <div className="items-list">
                  {categoryData.map((item, index) => {
                    const itemKey = `${category.key}-${index}`;
                    const isItemSelected = selectionState.selectedItems.has(itemKey);
                    const indicator = formatDisplayValue(item.指标);
                    const weight = item.权重;
                    const isHighlight = category.key === 'keyWork' && (item.序号 === 1 || item.序号 === 19);

                    return (
                      <div key={index} className={`item-row ${isHighlight ? 'highlight-item' : ''}`}>
                        <label className="item-checkbox">
                          <input
                            type="checkbox"
                            checked={isItemSelected}
                            onChange={() => handleItemSelect(category.key, index)}
                          />
                          <span className="item-content">
                            <span className="item-name">{indicator}</span>
                            {weight && (
                              <span className="item-weight">权重: {weight}分</span>
                            )}
                            {isHighlight && (
                              <span className="highlight-badge">新材级重点工作</span>
                            )}
                          </span>
                        </label>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        <div className="modal-footer">
          <div className="statistics">
            <span>已选择: {statistics.totalItems}项</span>
            {statistics.totalWeight > 0 && (
              <span>总权重: {statistics.totalWeight}分</span>
            )}
          </div>

          <div className="format-selection">
            <label>
              下载格式:
              <select 
                value={selectionState.downloadFormat}
                onChange={(e) => setSelectionState({
                  ...selectionState,
                  downloadFormat: e.target.value
                })}
              >
                <option value="excel">Excel格式</option>
                <option value="pdf">PDF格式</option>
                <option value="csv">CSV格式</option>
              </select>
            </label>
          </div>

          <div className="action-buttons">
            <button className="cancel-btn" onClick={onClose}>
              取消
            </button>
            <button 
              className="download-btn"
              onClick={handleDownload}
              disabled={statistics.totalItems === 0}
            >
              {selectionState.downloadFormat.toUpperCase()}下载
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SelectiveDownloadModal; 