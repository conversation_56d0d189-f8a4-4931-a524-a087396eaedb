{"ast": null, "code": "// 国际化语言资源文件\n// 语言资源定义\nconst translations={'zh-CN':{// 个人设置页面\npersonalSettings:'个人设置',personalSettingsDesc:'自定义您的使用体验',// 导航菜单\nprofile:'个人信息',security:'安全设置',preferences:'偏好设置',// 个人信息\nprofileInfo:'个人信息',profileInfoDesc:'管理您的基本信息',username:'用户名',email:'邮箱',role:'角色',status:'状态',lastLogin:'最后登录',createdAt:'创建时间',active:'活跃',inactive:'非活跃',// 角色名称\nsuper_admin:'超级管理员',manager:'管理员',director:'部长',user:'普通用户',// 安全设置\nsecuritySettings:'安全设置',securitySettingsDesc:'保护您的账户安全',currentPassword:'当前密码',newPassword:'新密码',confirmPassword:'确认密码',changePassword:'修改密码',// 偏好设置\npreferencesSettings:'偏好设置',preferencesSettingsDesc:'自定义您的使用体验',interfaceSettings:'界面设置',themeMode:'主题模式',themeDesc:'选择您喜欢的界面主题',languageSettings:'语言设置',languageDesc:'选择界面显示语言',notificationSettings:'通知设置',emailNotifications:'邮件通知',emailNotificationsDesc:'接收重要更新和系统通知',desktopNotifications:'桌面通知',desktopNotificationsDesc:'在桌面显示系统通知',// 主题选项\ndarkMode:'深色模式',lightMode:'浅色模式',autoMode:'跟随系统',// 语言选项\nsimplifiedChinese:'简体中文',english:'English',// 按钮\nsave:'保存',cancel:'取消',update:'更新',back:'返回',// 消息提示\nsaveSuccess:'设置已保存',updateSuccess:'信息更新成功',passwordChangeSuccess:'密码修改成功',saveError:'保存失败',updateError:'更新失败',passwordChangeError:'密码修改失败',// 验证消息\nusernameRequired:'用户名不能为空',emailRequired:'邮箱不能为空',emailInvalid:'邮箱格式不正确',passwordRequired:'密码不能为空',passwordTooShort:'密码长度至少6位',passwordMismatch:'两次输入的密码不一致',currentPasswordRequired:'请输入当前密码'},'en-US':{// Personal Settings Page\npersonalSettings:'Personal Settings',personalSettingsDesc:'Customize your experience',// Navigation Menu\nprofile:'Profile',security:'Security',preferences:'Preferences',// Profile Information\nprofileInfo:'Profile Information',profileInfoDesc:'Manage your basic information',username:'Username',email:'Email',role:'Role',status:'Status',lastLogin:'Last Login',createdAt:'Created At',active:'Active',inactive:'Inactive',// Role Names\nsuper_admin:'Super Admin',manager:'Manager',director:'Director',user:'User',// Security Settings\nsecuritySettings:'Security Settings',securitySettingsDesc:'Protect your account security',currentPassword:'Current Password',newPassword:'New Password',confirmPassword:'Confirm Password',changePassword:'Change Password',// Preferences Settings\npreferencesSettings:'Preferences',preferencesSettingsDesc:'Customize your experience',interfaceSettings:'Interface Settings',themeMode:'Theme Mode',themeDesc:'Choose your preferred interface theme',languageSettings:'Language Settings',languageDesc:'Select interface display language',notificationSettings:'Notification Settings',emailNotifications:'Email Notifications',emailNotificationsDesc:'Receive important updates and system notifications',desktopNotifications:'Desktop Notifications',desktopNotificationsDesc:'Show system notifications on desktop',// Theme Options\ndarkMode:'Dark Mode',lightMode:'Light Mode',autoMode:'Follow System',// Language Options\nsimplifiedChinese:'简体中文',english:'English',// Buttons\nsave:'Save',cancel:'Cancel',update:'Update',back:'Back',// Messages\nsaveSuccess:'Settings saved',updateSuccess:'Information updated successfully',passwordChangeSuccess:'Password changed successfully',saveError:'Save failed',updateError:'Update failed',passwordChangeError:'Password change failed',// Validation Messages\nusernameRequired:'Username is required',emailRequired:'Email is required',emailInvalid:'Invalid email format',passwordRequired:'Password is required',passwordTooShort:'Password must be at least 6 characters',passwordMismatch:'Passwords do not match',currentPasswordRequired:'Please enter current password'}};// 当前语言状态\nlet currentLanguage='zh-CN';// 语言切换函数\nexport const setLanguage=language=>{if(translations[language]){currentLanguage=language;// 保存到本地存储\nlocalStorage.setItem('userLanguage',language);// 设置HTML lang属性\ndocument.documentElement.lang=language;return true;}return false;};// 获取当前语言\nexport const getCurrentLanguage=()=>{return currentLanguage;};// 翻译函数\nexport const t=key=>{const keys=key.split('.');let value=translations[currentLanguage];for(const k of keys){if(value&&typeof value==='object'&&k in value){value=value[k];}else{// 如果找不到翻译，返回key本身\nreturn key;}}return value||key;};// 初始化语言设置\nexport const initLanguage=()=>{// 从本地存储获取保存的语言设置\nconst savedLanguage=localStorage.getItem('userLanguage');if(savedLanguage&&translations[savedLanguage]){setLanguage(savedLanguage);}else{// 默认使用中文\nsetLanguage('zh-CN');}};// 获取所有可用语言\nexport const getAvailableLanguages=()=>{return Object.keys(translations);};// 导出默认对象\nexport default{setLanguage,getCurrentLanguage,t,initLanguage,getAvailableLanguages};", "map": {"version": 3, "names": ["translations", "personalSettings", "personalSettingsDesc", "profile", "security", "preferences", "profileInfo", "profileInfoDesc", "username", "email", "role", "status", "lastLogin", "createdAt", "active", "inactive", "super_admin", "manager", "director", "user", "securitySettings", "securitySettingsDesc", "currentPassword", "newPassword", "confirmPassword", "changePassword", "preferencesSettings", "preferencesSettingsDesc", "interfaceSettings", "themeMode", "themeDesc", "languageSettings", "languageDesc", "notificationSettings", "emailNotifications", "emailNotificationsDesc", "desktopNotifications", "desktopNotificationsDesc", "darkMode", "lightMode", "autoMode", "simplifiedChinese", "english", "save", "cancel", "update", "back", "saveSuccess", "updateSuccess", "passwordChangeSuccess", "saveError", "updateError", "passwordChangeError", "usernameRequired", "emailRequired", "emailInvalid", "passwordRequired", "passwordTooShort", "passwordMismatch", "currentPasswordRequired", "currentLanguage", "setLanguage", "language", "localStorage", "setItem", "document", "documentElement", "lang", "getCurrentLanguage", "t", "key", "keys", "split", "value", "k", "initLanguage", "savedLanguage", "getItem", "getAvailableLanguages", "Object"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/utils/i18n.js"], "sourcesContent": ["// 国际化语言资源文件\n\n// 语言资源定义\nconst translations = {\n  'zh-CN': {\n    // 个人设置页面\n    personalSettings: '个人设置',\n    personalSettingsDesc: '自定义您的使用体验',\n    \n    // 导航菜单\n    profile: '个人信息',\n    security: '安全设置',\n    preferences: '偏好设置',\n    \n    // 个人信息\n    profileInfo: '个人信息',\n    profileInfoDesc: '管理您的基本信息',\n    username: '用户名',\n    email: '邮箱',\n    role: '角色',\n    status: '状态',\n    lastLogin: '最后登录',\n    createdAt: '创建时间',\n    active: '活跃',\n    inactive: '非活跃',\n    \n    // 角色名称\n    super_admin: '超级管理员',\n    manager: '管理员',\n    director: '部长',\n    user: '普通用户',\n    \n    // 安全设置\n    securitySettings: '安全设置',\n    securitySettingsDesc: '保护您的账户安全',\n    currentPassword: '当前密码',\n    newPassword: '新密码',\n    confirmPassword: '确认密码',\n    changePassword: '修改密码',\n    \n    // 偏好设置\n    preferencesSettings: '偏好设置',\n    preferencesSettingsDesc: '自定义您的使用体验',\n    interfaceSettings: '界面设置',\n    themeMode: '主题模式',\n    themeDesc: '选择您喜欢的界面主题',\n    languageSettings: '语言设置',\n    languageDesc: '选择界面显示语言',\n    notificationSettings: '通知设置',\n    emailNotifications: '邮件通知',\n    emailNotificationsDesc: '接收重要更新和系统通知',\n    desktopNotifications: '桌面通知',\n    desktopNotificationsDesc: '在桌面显示系统通知',\n    \n    // 主题选项\n    darkMode: '深色模式',\n    lightMode: '浅色模式',\n    autoMode: '跟随系统',\n    \n    // 语言选项\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    \n    // 按钮\n    save: '保存',\n    cancel: '取消',\n    update: '更新',\n    back: '返回',\n    \n    // 消息提示\n    saveSuccess: '设置已保存',\n    updateSuccess: '信息更新成功',\n    passwordChangeSuccess: '密码修改成功',\n    saveError: '保存失败',\n    updateError: '更新失败',\n    passwordChangeError: '密码修改失败',\n    \n    // 验证消息\n    usernameRequired: '用户名不能为空',\n    emailRequired: '邮箱不能为空',\n    emailInvalid: '邮箱格式不正确',\n    passwordRequired: '密码不能为空',\n    passwordTooShort: '密码长度至少6位',\n    passwordMismatch: '两次输入的密码不一致',\n    currentPasswordRequired: '请输入当前密码'\n  },\n  \n  'en-US': {\n    // Personal Settings Page\n    personalSettings: 'Personal Settings',\n    personalSettingsDesc: 'Customize your experience',\n    \n    // Navigation Menu\n    profile: 'Profile',\n    security: 'Security',\n    preferences: 'Preferences',\n    \n    // Profile Information\n    profileInfo: 'Profile Information',\n    profileInfoDesc: 'Manage your basic information',\n    username: 'Username',\n    email: 'Email',\n    role: 'Role',\n    status: 'Status',\n    lastLogin: 'Last Login',\n    createdAt: 'Created At',\n    active: 'Active',\n    inactive: 'Inactive',\n    \n    // Role Names\n    super_admin: 'Super Admin',\n    manager: 'Manager',\n    director: 'Director',\n    user: 'User',\n    \n    // Security Settings\n    securitySettings: 'Security Settings',\n    securitySettingsDesc: 'Protect your account security',\n    currentPassword: 'Current Password',\n    newPassword: 'New Password',\n    confirmPassword: 'Confirm Password',\n    changePassword: 'Change Password',\n    \n    // Preferences Settings\n    preferencesSettings: 'Preferences',\n    preferencesSettingsDesc: 'Customize your experience',\n    interfaceSettings: 'Interface Settings',\n    themeMode: 'Theme Mode',\n    themeDesc: 'Choose your preferred interface theme',\n    languageSettings: 'Language Settings',\n    languageDesc: 'Select interface display language',\n    notificationSettings: 'Notification Settings',\n    emailNotifications: 'Email Notifications',\n    emailNotificationsDesc: 'Receive important updates and system notifications',\n    desktopNotifications: 'Desktop Notifications',\n    desktopNotificationsDesc: 'Show system notifications on desktop',\n    \n    // Theme Options\n    darkMode: 'Dark Mode',\n    lightMode: 'Light Mode',\n    autoMode: 'Follow System',\n    \n    // Language Options\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    \n    // Buttons\n    save: 'Save',\n    cancel: 'Cancel',\n    update: 'Update',\n    back: 'Back',\n    \n    // Messages\n    saveSuccess: 'Settings saved',\n    updateSuccess: 'Information updated successfully',\n    passwordChangeSuccess: 'Password changed successfully',\n    saveError: 'Save failed',\n    updateError: 'Update failed',\n    passwordChangeError: 'Password change failed',\n    \n    // Validation Messages\n    usernameRequired: 'Username is required',\n    emailRequired: 'Email is required',\n    emailInvalid: 'Invalid email format',\n    passwordRequired: 'Password is required',\n    passwordTooShort: 'Password must be at least 6 characters',\n    passwordMismatch: 'Passwords do not match',\n    currentPasswordRequired: 'Please enter current password'\n  }\n};\n\n// 当前语言状态\nlet currentLanguage = 'zh-CN';\n\n// 语言切换函数\nexport const setLanguage = (language) => {\n  if (translations[language]) {\n    currentLanguage = language;\n    // 保存到本地存储\n    localStorage.setItem('userLanguage', language);\n    // 设置HTML lang属性\n    document.documentElement.lang = language;\n    return true;\n  }\n  return false;\n};\n\n// 获取当前语言\nexport const getCurrentLanguage = () => {\n  return currentLanguage;\n};\n\n// 翻译函数\nexport const t = (key) => {\n  const keys = key.split('.');\n  let value = translations[currentLanguage];\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      // 如果找不到翻译，返回key本身\n      return key;\n    }\n  }\n  \n  return value || key;\n};\n\n// 初始化语言设置\nexport const initLanguage = () => {\n  // 从本地存储获取保存的语言设置\n  const savedLanguage = localStorage.getItem('userLanguage');\n  if (savedLanguage && translations[savedLanguage]) {\n    setLanguage(savedLanguage);\n  } else {\n    // 默认使用中文\n    setLanguage('zh-CN');\n  }\n};\n\n// 获取所有可用语言\nexport const getAvailableLanguages = () => {\n  return Object.keys(translations);\n};\n\n// 导出默认对象\nexport default {\n  setLanguage,\n  getCurrentLanguage,\n  t,\n  initLanguage,\n  getAvailableLanguages\n};\n"], "mappings": "AAAA;AAEA;AACA,KAAM,CAAAA,YAAY,CAAG,CACnB,OAAO,CAAE,CACP;AACAC,gBAAgB,CAAE,MAAM,CACxBC,oBAAoB,CAAE,WAAW,CAEjC;AACAC,OAAO,CAAE,MAAM,CACfC,QAAQ,CAAE,MAAM,CAChBC,WAAW,CAAE,MAAM,CAEnB;AACAC,WAAW,CAAE,MAAM,CACnBC,eAAe,CAAE,UAAU,CAC3BC,QAAQ,CAAE,KAAK,CACfC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IAAI,CACZC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,MAAM,CACjBC,MAAM,CAAE,IAAI,CACZC,QAAQ,CAAE,KAAK,CAEf;AACAC,WAAW,CAAE,OAAO,CACpBC,OAAO,CAAE,KAAK,CACdC,QAAQ,CAAE,IAAI,CACdC,IAAI,CAAE,MAAM,CAEZ;AACAC,gBAAgB,CAAE,MAAM,CACxBC,oBAAoB,CAAE,UAAU,CAChCC,eAAe,CAAE,MAAM,CACvBC,WAAW,CAAE,KAAK,CAClBC,eAAe,CAAE,MAAM,CACvBC,cAAc,CAAE,MAAM,CAEtB;AACAC,mBAAmB,CAAE,MAAM,CAC3BC,uBAAuB,CAAE,WAAW,CACpCC,iBAAiB,CAAE,MAAM,CACzBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,YAAY,CACvBC,gBAAgB,CAAE,MAAM,CACxBC,YAAY,CAAE,UAAU,CACxBC,oBAAoB,CAAE,MAAM,CAC5BC,kBAAkB,CAAE,MAAM,CAC1BC,sBAAsB,CAAE,aAAa,CACrCC,oBAAoB,CAAE,MAAM,CAC5BC,wBAAwB,CAAE,WAAW,CAErC;AACAC,QAAQ,CAAE,MAAM,CAChBC,SAAS,CAAE,MAAM,CACjBC,QAAQ,CAAE,MAAM,CAEhB;AACAC,iBAAiB,CAAE,MAAM,CACzBC,OAAO,CAAE,SAAS,CAElB;AACAC,IAAI,CAAE,IAAI,CACVC,MAAM,CAAE,IAAI,CACZC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,IAAI,CAEV;AACAC,WAAW,CAAE,OAAO,CACpBC,aAAa,CAAE,QAAQ,CACvBC,qBAAqB,CAAE,QAAQ,CAC/BC,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,MAAM,CACnBC,mBAAmB,CAAE,QAAQ,CAE7B;AACAC,gBAAgB,CAAE,SAAS,CAC3BC,aAAa,CAAE,QAAQ,CACvBC,YAAY,CAAE,SAAS,CACvBC,gBAAgB,CAAE,QAAQ,CAC1BC,gBAAgB,CAAE,UAAU,CAC5BC,gBAAgB,CAAE,YAAY,CAC9BC,uBAAuB,CAAE,SAC3B,CAAC,CAED,OAAO,CAAE,CACP;AACA1D,gBAAgB,CAAE,mBAAmB,CACrCC,oBAAoB,CAAE,2BAA2B,CAEjD;AACAC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,WAAW,CAAE,aAAa,CAE1B;AACAC,WAAW,CAAE,qBAAqB,CAClCC,eAAe,CAAE,+BAA+B,CAChDC,QAAQ,CAAE,UAAU,CACpBC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,UAAU,CAEpB;AACAC,WAAW,CAAE,aAAa,CAC1BC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,IAAI,CAAE,MAAM,CAEZ;AACAC,gBAAgB,CAAE,mBAAmB,CACrCC,oBAAoB,CAAE,+BAA+B,CACrDC,eAAe,CAAE,kBAAkB,CACnCC,WAAW,CAAE,cAAc,CAC3BC,eAAe,CAAE,kBAAkB,CACnCC,cAAc,CAAE,iBAAiB,CAEjC;AACAC,mBAAmB,CAAE,aAAa,CAClCC,uBAAuB,CAAE,2BAA2B,CACpDC,iBAAiB,CAAE,oBAAoB,CACvCC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,uCAAuC,CAClDC,gBAAgB,CAAE,mBAAmB,CACrCC,YAAY,CAAE,mCAAmC,CACjDC,oBAAoB,CAAE,uBAAuB,CAC7CC,kBAAkB,CAAE,qBAAqB,CACzCC,sBAAsB,CAAE,oDAAoD,CAC5EC,oBAAoB,CAAE,uBAAuB,CAC7CC,wBAAwB,CAAE,sCAAsC,CAEhE;AACAC,QAAQ,CAAE,WAAW,CACrBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,eAAe,CAEzB;AACAC,iBAAiB,CAAE,MAAM,CACzBC,OAAO,CAAE,SAAS,CAElB;AACAC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QAAQ,CAChBC,IAAI,CAAE,MAAM,CAEZ;AACAC,WAAW,CAAE,gBAAgB,CAC7BC,aAAa,CAAE,kCAAkC,CACjDC,qBAAqB,CAAE,+BAA+B,CACtDC,SAAS,CAAE,aAAa,CACxBC,WAAW,CAAE,eAAe,CAC5BC,mBAAmB,CAAE,wBAAwB,CAE7C;AACAC,gBAAgB,CAAE,sBAAsB,CACxCC,aAAa,CAAE,mBAAmB,CAClCC,YAAY,CAAE,sBAAsB,CACpCC,gBAAgB,CAAE,sBAAsB,CACxCC,gBAAgB,CAAE,wCAAwC,CAC1DC,gBAAgB,CAAE,wBAAwB,CAC1CC,uBAAuB,CAAE,+BAC3B,CACF,CAAC,CAED;AACA,GAAI,CAAAC,eAAe,CAAG,OAAO,CAE7B;AACA,MAAO,MAAM,CAAAC,WAAW,CAAIC,QAAQ,EAAK,CACvC,GAAI9D,YAAY,CAAC8D,QAAQ,CAAC,CAAE,CAC1BF,eAAe,CAAGE,QAAQ,CAC1B;AACAC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAEF,QAAQ,CAAC,CAC9C;AACAG,QAAQ,CAACC,eAAe,CAACC,IAAI,CAAGL,QAAQ,CACxC,MAAO,KAAI,CACb,CACA,MAAO,MAAK,CACd,CAAC,CAED;AACA,MAAO,MAAM,CAAAM,kBAAkB,CAAGA,CAAA,GAAM,CACtC,MAAO,CAAAR,eAAe,CACxB,CAAC,CAED;AACA,MAAO,MAAM,CAAAS,CAAC,CAAIC,GAAG,EAAK,CACxB,KAAM,CAAAC,IAAI,CAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC,CAC3B,GAAI,CAAAC,KAAK,CAAGzE,YAAY,CAAC4D,eAAe,CAAC,CAEzC,IAAK,KAAM,CAAAc,CAAC,GAAI,CAAAH,IAAI,CAAE,CACpB,GAAIE,KAAK,EAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIC,CAAC,GAAI,CAAAD,KAAK,CAAE,CACpDA,KAAK,CAAGA,KAAK,CAACC,CAAC,CAAC,CAClB,CAAC,IAAM,CACL;AACA,MAAO,CAAAJ,GAAG,CACZ,CACF,CAEA,MAAO,CAAAG,KAAK,EAAIH,GAAG,CACrB,CAAC,CAED;AACA,MAAO,MAAM,CAAAK,YAAY,CAAGA,CAAA,GAAM,CAChC;AACA,KAAM,CAAAC,aAAa,CAAGb,YAAY,CAACc,OAAO,CAAC,cAAc,CAAC,CAC1D,GAAID,aAAa,EAAI5E,YAAY,CAAC4E,aAAa,CAAC,CAAE,CAChDf,WAAW,CAACe,aAAa,CAAC,CAC5B,CAAC,IAAM,CACL;AACAf,WAAW,CAAC,OAAO,CAAC,CACtB,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAiB,qBAAqB,CAAGA,CAAA,GAAM,CACzC,MAAO,CAAAC,MAAM,CAACR,IAAI,CAACvE,YAAY,CAAC,CAClC,CAAC,CAED;AACA,cAAe,CACb6D,WAAW,CACXO,kBAAkB,CAClBC,CAAC,CACDM,YAAY,CACZG,qBACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}