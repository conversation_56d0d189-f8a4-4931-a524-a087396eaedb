{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useRef,useCallback}from'react';import'../styles/MonthlyKPI.css';import kpiService from'../services/kpiService';import KPISelectiveDownloadModal from'../components/KPISelectiveDownloadModal';import kpiDownloadService from'../services/kpiDownloadService';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const MonthlyKPI=_ref=>{let{onNavigate}=_ref;// 状态管理\nconst[data,setData]=useState([]);const[filteredData,setFilteredData]=useState([]);const[loading,setLoading]=useState(true);const[editingCell,setEditingCell]=useState(null);const[syncStatus,setSyncStatus]=useState('已同步');const[currentMonthPair,setCurrentMonthPair]=useState([]);const[lastFailedOperation,setLastFailedOperation]=useState(null);const[showSelectiveDownloadModal,setShowSelectiveDownloadModal]=useState(false);const[downloadLoading,setDownloadLoading]=useState(false);// 指标筛选相关状态\nconst[selectedIndicators,setSelectedIndicators]=useState([]);// 数据输入优化相关状态\nconst[tempValues,setTempValues]=useState({});// 临时存储编辑中的值\nconst saveTimeoutRef=useRef(null);const pendingSaveRef=useRef(null);// 获取当前月份对（上月和当月）\nconst getCurrentMonthPair=()=>{const now=new Date();const currentMonth=now.getMonth()+1;// 1-12\nconst previousMonth=currentMonth===1?12:currentMonth-1;return[previousMonth,currentMonth];};useEffect(()=>{// 初始化当前月份对\nsetCurrentMonthPair(getCurrentMonthPair());// 加载数据\nloadData();// 设置同步回调\nkpiService.setSyncCallbacks(()=>setSyncStatus('同步成功'),error=>setSyncStatus('同步失败'));// 清理函数\nreturn()=>{if(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}if(pendingSaveRef.current){var _pendingSaveRef$curre,_pendingSaveRef$curre2;(_pendingSaveRef$curre=(_pendingSaveRef$curre2=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre===void 0?void 0:_pendingSaveRef$curre.call(_pendingSaveRef$curre2);}};},[]);const loadData=async()=>{setLoading(true);try{console.log('开始加载KPI数据...');const kpiData=await kpiService.loadKPIData();console.log('加载的KPI数据:',kpiData);if(kpiData&&Array.isArray(kpiData)){// 处理数据，添加合并单元格逻辑\nconst processedData=processMergedCells(kpiData);setData(processedData);}else{console.error('未获取到有效的KPI数据');setData([]);}}catch(error){console.error('KPI数据加载失败:',error);setData([]);}setLoading(false);};// 处理合并单元格逻辑\nconst processMergedCells=rawData=>{if(!Array.isArray(rawData)||rawData.length===0)return[];const processedData=[...rawData];// 按序号和指标进行合并处理\nfor(let i=0;i<processedData.length;i++){const currentRow=processedData[i];let mergeCount=1;// 向下查找相同的序号和指标\nfor(let j=i+1;j<processedData.length;j++){const nextRow=processedData[j];if(currentRow.序号===nextRow.序号&&currentRow.指标===nextRow.指标){mergeCount++;}else{break;}}// 如果需要合并\nif(mergeCount>1){// 标记第一行为合并开始\nprocessedData[i].isMergedStart=true;processedData[i].rowSpan=mergeCount;// 标记后续行为合并单元格\nfor(let k=i+1;k<i+mergeCount;k++){processedData[k].isMergedCell=true;}// 跳过已处理的行\ni+=mergeCount-1;}}return processedData;};// 强制刷新数据\nconst forceRefresh=async()=>{console.log('强制刷新KPI数据...');setSyncStatus('刷新中...');await loadData();setSyncStatus('刷新完成');};// 应用筛选逻辑\nuseEffect(()=>{applyFilters();},[data,selectedIndicators]);const applyFilters=()=>{let filtered=[...data];// 按指标筛选\nif(selectedIndicators.length>0){filtered=filtered.filter(item=>selectedIndicators.includes(item.指标));}setFilteredData(filtered);};// 获取所有唯一指标\nconst getUniqueIndicators=()=>{const indicators=new Set();data.forEach(item=>{if(item.指标&&item.指标.trim()){indicators.add(item.指标.trim());}});return Array.from(indicators).sort();};// 处理指标选择变化\nconst handleIndicatorSelectChange=event=>{const value=event.target.value;if(value==='all'){setSelectedIndicators([]);}else{setSelectedIndicators([value]);}};// 月份导航 - 前后切换\nconst navigateMonths=direction=>{const[first,second]=currentMonthPair;if(direction==='prev'&&first>2){setCurrentMonthPair([first-1,second-1]);}else if(direction==='next'&&second<12){setCurrentMonthPair([first+1,second+1]);}};// 防抖保存函数\nconst debouncedSave=useCallback(async(rowIndex,field,value)=>{try{setSyncStatus('同步中');// 取消之前的保存操作\nif(pendingSaveRef.current){var _pendingSaveRef$curre3,_pendingSaveRef$curre4;(_pendingSaveRef$curre3=(_pendingSaveRef$curre4=pendingSaveRef.current).abort)===null||_pendingSaveRef$curre3===void 0?void 0:_pendingSaveRef$curre3.call(_pendingSaveRef$curre4);}// 创建新的保存操作\nconst controller=new AbortController();pendingSaveRef.current=controller;// 保存失败操作信息，用于重试\nsetLastFailedOperation({rowIndex,field,value});// 调用双向同步\nawait kpiService.updateData(rowIndex,field,value);// 如果没有被取消，更新状态\nif(!controller.signal.aborted){setSyncStatus('同步成功');setLastFailedOperation(null);setTimeout(()=>setSyncStatus('已同步'),1000);pendingSaveRef.current=null;}}catch(error){if(!error.name==='AbortError'){console.error('保存失败:',error);setSyncStatus('同步失败');}}},[]);// 处理输入变化（实时更新UI，延迟保存）\nconst handleInputChange=(rowIndex,field,value)=>{// 立即更新UI显示\nconst newData=[...data];newData[rowIndex][field]=value;setData(newData);// 存储临时值\nconst key=\"\".concat(rowIndex,\"-\").concat(field);setTempValues(prev=>_objectSpread(_objectSpread({},prev),{},{[key]:value}));// 清除之前的定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);}// 设置新的防抖定时器\nsaveTimeoutRef.current=setTimeout(()=>{debouncedSave(rowIndex,field,value);},800);// 800ms防抖延迟\n};// 处理失焦保存（立即保存）\nconst handleBlurSave=async(rowIndex,field)=>{const key=\"\".concat(rowIndex,\"-\").concat(field);const value=tempValues[key];if(value!==undefined){// 清除防抖定时器\nif(saveTimeoutRef.current){clearTimeout(saveTimeoutRef.current);saveTimeoutRef.current=null;}// 立即保存\nawait debouncedSave(rowIndex,field,value);// 清除临时值\nsetTempValues(prev=>{const newTemp=_objectSpread({},prev);delete newTemp[key];return newTemp;});}};// 兼容原有的handleCellEdit接口\nconst handleCellEdit=async(rowIndex,field,value)=>{handleInputChange(rowIndex,field,value);};// 重试上次失败的操作\nconst retryLastOperation=async()=>{if(!lastFailedOperation)return;const{rowIndex,field,value}=lastFailedOperation;setSyncStatus('同步中');try{await kpiService.updateData(rowIndex,field,value);}catch(error){console.error('重试失败:',error);}};const startEdit=(rowIndex,field)=>{setEditingCell(\"\".concat(rowIndex,\"-\").concat(field));};const finishEdit=()=>{setEditingCell(null);};// 处理选择性下载\nconst handleSelectiveDownload=async selectionData=>{setDownloadLoading(true);try{console.log('开始选择性下载:',selectionData);const result=await kpiDownloadService.handleSelectiveDownload(selectionData);if(result.success){console.log('下载成功:',result.message);alert(\"\\u4E0B\\u8F7D\\u6210\\u529F: \".concat(result.message));}else{throw new Error(result.message||'下载失败');}}catch(error){console.error('下载失败:',error);alert('下载失败: '+error.message);}finally{setDownloadLoading(false);setShowSelectiveDownloadModal(false);}};// 打开选择性下载模态框\nconst openSelectiveDownloadModal=()=>{setShowSelectiveDownloadModal(true);};// 关闭选择性下载模态框\nconst closeSelectiveDownloadModal=()=>{setShowSelectiveDownloadModal(false);};// 获取月份名称\nconst getMonthName=month=>{const monthNames=['','1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月'];return monthNames[month];};// 渲染表格\nconst renderTable=()=>{if(filteredData.length===0){return/*#__PURE__*/_jsxs(\"div\",{className:\"no-data\",children:[/*#__PURE__*/_jsx(\"p\",{children:data.length===0?'此部分暂无数据':'当前筛选条件下没有数据'}),/*#__PURE__*/_jsxs(\"p\",{style:{fontSize:'0.8rem',color:'#666',marginTop:'10px'},children:[\"\\u8C03\\u8BD5\\u4FE1\\u606F: \\u603B\\u6570\\u636E \",data.length,\" \\u6761\\uFF0C\\u7B5B\\u9009\\u540E \",filteredData.length,\" \\u6761\"]})]});}return/*#__PURE__*/_jsx(\"div\",{className:\"module4_kpi-table-container\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"kpi-data-table\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{children:[/*#__PURE__*/_jsx(\"th\",{className:\"col-number\",children:\"\\u5E8F\\u53F7\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-indicator\",children:\"\\u6307\\u6807\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-target\",children:\"\\u76EE\\u6807\\u503C\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-score\",children:\"\\u5206\\u503C(30)\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-method\",children:\"\\u7EDF\\u8BA1\\u65B9\\u5F0F&\\u53E3\\u5F84\"}),/*#__PURE__*/_jsx(\"th\",{className:\"col-standard\",children:\"\\u8003\\u6838\\u6807\\u51C6\"}),currentMonthPair.map(month=>[/*#__PURE__*/_jsxs(\"th\",{className:\"col-month\",children:[getMonthName(month),\"\\u4EFD\"]},\"month-\".concat(month)),/*#__PURE__*/_jsxs(\"th\",{className:\"col-completion\",children:[getMonthName(month),\"\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\"]},\"completion-\".concat(month)),/*#__PURE__*/_jsxs(\"th\",{className:\"col-score-month\",children:[getMonthName(month),\"\\u5F97\\u5206\"]},\"score-\".concat(month))])]})}),/*#__PURE__*/_jsx(\"tbody\",{children:filteredData.map((row,index)=>{const isMergedStart=row.isMergedStart;const isMergedCell=row.isMergedCell;const rowSpan=row.rowSpan||1;return/*#__PURE__*/_jsxs(\"tr\",{className:\"kpi-data-row \".concat(isMergedCell?'merged-row':'',\" \").concat(isMergedStart?'merged-start-row':''),children:[!isMergedCell&&/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-number\",rowSpan:isMergedStart?rowSpan:1,children:/*#__PURE__*/_jsx(\"div\",{className:\"cell-content\",children:row.序号||''})}),!isMergedCell&&/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-indicator editable\",rowSpan:isMergedStart?rowSpan:1,children:renderEditableCell(index,'指标',row.指标||'')}),/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-target editable\",children:renderEditableCell(index,'目标值',row.目标值||'')}),/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-score editable\",children:renderEditableCell(index,'分值',row.分值||'')}),/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-method editable\",children:renderEditableCell(index,'统计方式&口径',row['统计方式&口径']||'')}),/*#__PURE__*/_jsx(\"td\",{className:\"kpi-data-cell col-standard editable\",children:renderEditableCell(index,'考核标准',row.考核标准||'')}),currentMonthPair.map(month=>[/*#__PURE__*/// X月份列\n_jsx(\"td\",{className:\"kpi-data-cell col-month editable\",children:renderEditableCell(index,\"\".concat(month,\"\\u6708\\u4EFD\"),row[\"\".concat(month,\"\\u6708\\u4EFD\")]||'')},\"month-\".concat(month,\"-\").concat(index)),/*#__PURE__*/// X月份完成情况列\n_jsx(\"td\",{className:\"kpi-data-cell col-completion editable\",children:renderEditableCell(index,\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\"),row[\"\".concat(month,\"\\u6708\\u4EFD\\u5B8C\\u6210\\u60C5\\u51B5\")]||'')},\"completion-\".concat(month,\"-\").concat(index)),/*#__PURE__*/// X月得分列\n_jsx(\"td\",{className:\"kpi-data-cell col-score-month editable\",children:renderEditableCell(index,\"\".concat(month,\"\\u6708\\u5F97\\u5206\"),row[\"\".concat(month,\"\\u6708\\u5F97\\u5206\")]||'')},\"score-\".concat(month,\"-\").concat(index))])]},index);})})]})});};// 渲染可编辑单元格\nconst renderEditableCell=(rowIndex,field,value)=>{const cellKey=\"\".concat(rowIndex,\"-\").concat(field);const isEditing=editingCell===cellKey;// 检查是否为只读列（完成情况列）\nconst isReadOnlyField=field.includes('完成情况');const isEditable=!isReadOnlyField;// 处理换行显示\nlet contentToRender=String(value||'');if(contentToRender.includes('\\n')){contentToRender=contentToRender.split('\\n').map((line,i,arr)=>/*#__PURE__*/_jsxs(React.Fragment,{children:[line,i<arr.length-1&&/*#__PURE__*/_jsx(\"br\",{})]},i));}if(isEditing&&isEditable){return/*#__PURE__*/_jsx(\"textarea\",{value:value,onChange:e=>handleInputChange(rowIndex,field,e.target.value),onBlur:()=>{handleBlurSave(rowIndex,field);finishEdit();},onKeyDown:e=>{if(e.key==='Enter'&&e.ctrlKey){handleBlurSave(rowIndex,field);finishEdit();}if(e.key==='Escape'){finishEdit();}},autoFocus:true,className:\"cell-editor\"});}// 确定单元格的CSS类名\nlet cellClasses='cell-content';if(isReadOnlyField){cellClasses+=' readonly-cell';}else if(isEditable){cellClasses+=' clickable';}// 确定提示文本\nlet titleText='';if(isReadOnlyField){titleText='该列不可编辑';}else if(isEditable){titleText='点击编辑';}return/*#__PURE__*/_jsx(\"div\",{onClick:()=>isEditable&&startEdit(rowIndex,field),className:cellClasses,title:titleText,style:isReadOnlyField?{cursor:'not-allowed'}:{},children:contentToRender||''});};if(loading){return/*#__PURE__*/_jsxs(\"div\",{className:\"kpi-loading\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"loading-spinner\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u6B63\\u5728\\u52A0\\u8F7DKPI\\u6570\\u636E...\"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"monthly-kpi\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"kpi-header\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"header-left\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"back-button\",onClick:()=>onNavigate('home'),title:\"\\u8FD4\\u56DE\\u9996\\u9875\",children:\"\\u8FD4\\u56DE\\u9996\\u9875\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"page-title\",children:\"\\u6708\\u5EA6\\u91CD\\u70B9KPI\\u8DDF\\u8E2A\\u4EEA\\u8868\\u677F\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"header-right\",children:/*#__PURE__*/_jsx(\"div\",{className:\"sync-status\",children:/*#__PURE__*/_jsx(\"span\",{className:\"status-indicator \".concat(syncStatus==='已同步'?'success':'pending'),children:syncStatus})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-panel\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"control-left\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"indicator-type-section\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-label\",children:\"\\u6307\\u6807\\u7C7B\\u578B:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"indicator-selector-wrapper\",children:/*#__PURE__*/_jsxs(\"select\",{className:\"indicator-type-selector\",value:selectedIndicators.length===1?selectedIndicators[0]:'all',onChange:handleIndicatorSelectChange,children:[/*#__PURE__*/_jsx(\"option\",{value:\"all\",children:\"\\u5168\\u90E8\\u6307\\u6807\"}),getUniqueIndicators().map((indicator,index)=>/*#__PURE__*/_jsx(\"option\",{value:indicator,children:indicator},index))]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"current-display-stats\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"stats-label\",children:\"\\u5F53\\u524D\\u663E\\u793A:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"stats-value\",children:[filteredData.length,\" \\u9879\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"control-right\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"month-navigation\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"nav-button\",onClick:()=>navigateMonths('prev'),disabled:currentMonthPair[0]<=2,children:\"\\u2190 \\u4E0A\\u4E00\\u7EC4\\u6708\\u4EFD\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"current-months\",children:[\"\\u663E\\u793A\\u6708\\u4EFD: \",getMonthName(currentMonthPair[0]),\" + \",getMonthName(currentMonthPair[1])]}),/*#__PURE__*/_jsx(\"button\",{className:\"nav-button\",onClick:()=>navigateMonths('next'),disabled:currentMonthPair[1]>=12,children:\"\\u4E0B\\u4E00\\u7EC4\\u6708\\u4EFD \\u2192\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"refresh-button\",onClick:forceRefresh,title:\"\\u5237\\u65B0\\u6570\\u636E\",children:\"\\uD83D\\uDD04 \\u5237\\u65B0\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"download-button\",onClick:openSelectiveDownloadModal,title:\"\\u9009\\u62E9\\u6027\\u4E0B\\u8F7D\",children:\"\\uD83D\\uDCE5 \\u4E0B\\u8F7D\"})]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"kpi-content\",children:renderTable()}),/*#__PURE__*/_jsx(\"div\",{className:\"kpi-footer\",children:/*#__PURE__*/_jsx(\"div\",{className:\"data-stats\",children:selectedIndicators.length>0?\"\\u663E\\u793A \".concat(filteredData.length,\" / \").concat(data.length,\" \\u6761KPI\\u8BB0\\u5F55 (\\u5DF2\\u7B5B\\u9009)\"):\"\\u5171 \".concat(data.length,\" \\u6761KPI\\u8BB0\\u5F55\")})}),showSelectiveDownloadModal&&/*#__PURE__*/_jsx(KPISelectiveDownloadModal,{data:data,onDownload:handleSelectiveDownload,onClose:closeSelectiveDownloadModal,loading:downloadLoading})]});};export default MonthlyKPI;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "kpiService", "KPISelectiveDownloadModal", "kpiDownloadService", "jsx", "_jsx", "jsxs", "_jsxs", "MonthlyKPI", "_ref", "onNavigate", "data", "setData", "filteredData", "setFilteredData", "loading", "setLoading", "editingCell", "setEditingCell", "syncStatus", "setSyncStatus", "currentMonthPair", "setCurrentMonthPair", "lastFailedOperation", "setLastFailedOperation", "showSelectiveDownloadModal", "setShowSelectiveDownloadModal", "downloadLoading", "setDownloadLoading", "selectedIndicators", "setSelectedIndicators", "temp<PERSON><PERSON><PERSON>", "set<PERSON>emp<PERSON><PERSON><PERSON>", "saveTimeoutRef", "pendingSaveRef", "getCurrentMonthPair", "now", "Date", "currentMonth", "getMonth", "previousMonth", "loadData", "setSyncCallbacks", "error", "current", "clearTimeout", "_pendingSaveRef$curre", "_pendingSaveRef$curre2", "abort", "call", "console", "log", "kpiData", "loadKPIData", "Array", "isArray", "processedData", "processMergedCells", "rawData", "length", "i", "currentRow", "mergeCount", "j", "nextRow", "序号", "指标", "isMergedStart", "rowSpan", "k", "isMergedCell", "forceRefresh", "applyFilters", "filtered", "filter", "item", "includes", "getUniqueIndicators", "indicators", "Set", "for<PERSON>ach", "trim", "add", "from", "sort", "handleIndicatorSelectChange", "event", "value", "target", "navigateMonths", "direction", "first", "second", "debouncedSave", "rowIndex", "field", "_pendingSaveRef$curre3", "_pendingSaveRef$curre4", "controller", "AbortController", "updateData", "signal", "aborted", "setTimeout", "name", "handleInputChange", "newData", "key", "concat", "prev", "_objectSpread", "handleBlurSave", "undefined", "newTemp", "handleCellEdit", "retryLastOperation", "startEdit", "finishEdit", "handleSelectiveDownload", "selectionData", "result", "success", "message", "alert", "Error", "openSelectiveDownloadModal", "closeSelectiveDownloadModal", "getMonthName", "month", "monthNames", "renderTable", "className", "children", "style", "fontSize", "color", "marginTop", "map", "row", "index", "renderEditableCell", "目标值", "分值", "考核标准", "cellKey", "isEditing", "isReadOnlyField", "isEditable", "contentToRender", "String", "split", "line", "arr", "Fragment", "onChange", "e", "onBlur", "onKeyDown", "ctrl<PERSON>ey", "autoFocus", "cellClasses", "titleText", "onClick", "title", "cursor", "indicator", "disabled", "onDownload", "onClose"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块四/pages/MonthlyKPI.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport '../styles/MonthlyKPI.css';\nimport kpiService from '../services/kpiService';\nimport KPISelectiveDownloadModal from '../components/KPISelectiveDownloadModal';\nimport kpiDownloadService from '../services/kpiDownloadService';\n\nconst MonthlyKPI = ({ onNavigate }) => {\n  // 状态管理\n  const [data, setData] = useState([]);\n  const [filteredData, setFilteredData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [editingCell, setEditingCell] = useState(null);\n  const [syncStatus, setSyncStatus] = useState('已同步');\n  const [currentMonthPair, setCurrentMonthPair] = useState([]);\n  const [lastFailedOperation, setLastFailedOperation] = useState(null);\n  const [showSelectiveDownloadModal, setShowSelectiveDownloadModal] = useState(false);\n  const [downloadLoading, setDownloadLoading] = useState(false);\n\n  // 指标筛选相关状态\n  const [selectedIndicators, setSelectedIndicators] = useState([]);\n\n  // 数据输入优化相关状态\n  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值\n  const saveTimeoutRef = useRef(null);\n  const pendingSaveRef = useRef(null);\n\n  // 获取当前月份对（上月和当月）\n  const getCurrentMonthPair = () => {\n    const now = new Date();\n    const currentMonth = now.getMonth() + 1; // 1-12\n    const previousMonth = currentMonth === 1 ? 12 : currentMonth - 1;\n    return [previousMonth, currentMonth];\n  };\n\n  useEffect(() => {\n    // 初始化当前月份对\n    setCurrentMonthPair(getCurrentMonthPair());\n\n    // 加载数据\n    loadData();\n\n    // 设置同步回调\n    kpiService.setSyncCallbacks(\n      () => setSyncStatus('同步成功'),\n      (error) => setSyncStatus('同步失败')\n    );\n\n    // 清理函数\n    return () => {\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n      }\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n    };\n  }, []);\n\n  const loadData = async () => {\n    setLoading(true);\n    try {\n      console.log('开始加载KPI数据...');\n      const kpiData = await kpiService.loadKPIData();\n      console.log('加载的KPI数据:', kpiData);\n\n      if (kpiData && Array.isArray(kpiData)) {\n        // 处理数据，添加合并单元格逻辑\n        const processedData = processMergedCells(kpiData);\n        setData(processedData);\n      } else {\n        console.error('未获取到有效的KPI数据');\n        setData([]);\n      }\n    } catch (error) {\n      console.error('KPI数据加载失败:', error);\n      setData([]);\n    }\n    setLoading(false);\n  };\n\n  // 处理合并单元格逻辑\n  const processMergedCells = (rawData) => {\n    if (!Array.isArray(rawData) || rawData.length === 0) return [];\n\n    const processedData = [...rawData];\n\n    // 按序号和指标进行合并处理\n    for (let i = 0; i < processedData.length; i++) {\n      const currentRow = processedData[i];\n      let mergeCount = 1;\n\n      // 向下查找相同的序号和指标\n      for (let j = i + 1; j < processedData.length; j++) {\n        const nextRow = processedData[j];\n        if (currentRow.序号 === nextRow.序号 && currentRow.指标 === nextRow.指标) {\n          mergeCount++;\n        } else {\n          break;\n        }\n      }\n\n      // 如果需要合并\n      if (mergeCount > 1) {\n        // 标记第一行为合并开始\n        processedData[i].isMergedStart = true;\n        processedData[i].rowSpan = mergeCount;\n\n        // 标记后续行为合并单元格\n        for (let k = i + 1; k < i + mergeCount; k++) {\n          processedData[k].isMergedCell = true;\n        }\n\n        // 跳过已处理的行\n        i += mergeCount - 1;\n      }\n    }\n\n    return processedData;\n  };\n\n  // 强制刷新数据\n  const forceRefresh = async () => {\n    console.log('强制刷新KPI数据...');\n    setSyncStatus('刷新中...');\n    await loadData();\n    setSyncStatus('刷新完成');\n  };\n\n  // 应用筛选逻辑\n  useEffect(() => {\n    applyFilters();\n  }, [data, selectedIndicators]);\n\n  const applyFilters = () => {\n    let filtered = [...data];\n\n    // 按指标筛选\n    if (selectedIndicators.length > 0) {\n      filtered = filtered.filter(item =>\n        selectedIndicators.includes(item.指标)\n      );\n    }\n\n    setFilteredData(filtered);\n  };\n\n  // 获取所有唯一指标\n  const getUniqueIndicators = () => {\n    const indicators = new Set();\n    data.forEach(item => {\n      if (item.指标 && item.指标.trim()) {\n        indicators.add(item.指标.trim());\n      }\n    });\n    return Array.from(indicators).sort();\n  };\n\n\n\n  // 处理指标选择变化\n  const handleIndicatorSelectChange = (event) => {\n    const value = event.target.value;\n    if (value === 'all') {\n      setSelectedIndicators([]);\n    } else {\n      setSelectedIndicators([value]);\n    }\n  };\n\n  // 月份导航 - 前后切换\n  const navigateMonths = (direction) => {\n    const [first, second] = currentMonthPair;\n    \n    if (direction === 'prev' && first > 2) {\n      setCurrentMonthPair([first - 1, second - 1]);\n    } else if (direction === 'next' && second < 12) {\n      setCurrentMonthPair([first + 1, second + 1]);\n    }\n  };\n\n  // 防抖保存函数\n  const debouncedSave = useCallback(async (rowIndex, field, value) => {\n    try {\n      setSyncStatus('同步中');\n\n      // 取消之前的保存操作\n      if (pendingSaveRef.current) {\n        pendingSaveRef.current.abort?.();\n      }\n\n      // 创建新的保存操作\n      const controller = new AbortController();\n      pendingSaveRef.current = controller;\n\n      // 保存失败操作信息，用于重试\n      setLastFailedOperation({ rowIndex, field, value });\n\n      // 调用双向同步\n      await kpiService.updateData(rowIndex, field, value);\n\n      // 如果没有被取消，更新状态\n      if (!controller.signal.aborted) {\n        setSyncStatus('同步成功');\n        setLastFailedOperation(null);\n        setTimeout(() => setSyncStatus('已同步'), 1000);\n        pendingSaveRef.current = null;\n      }\n    } catch (error) {\n      if (!error.name === 'AbortError') {\n        console.error('保存失败:', error);\n        setSyncStatus('同步失败');\n      }\n    }\n  }, []);\n\n  // 处理输入变化（实时更新UI，延迟保存）\n  const handleInputChange = (rowIndex, field, value) => {\n    // 立即更新UI显示\n    const newData = [...data];\n    newData[rowIndex][field] = value;\n    setData(newData);\n\n    // 存储临时值\n    const key = `${rowIndex}-${field}`;\n    setTempValues(prev => ({ ...prev, [key]: value }));\n\n    // 清除之前的定时器\n    if (saveTimeoutRef.current) {\n      clearTimeout(saveTimeoutRef.current);\n    }\n\n    // 设置新的防抖定时器\n    saveTimeoutRef.current = setTimeout(() => {\n      debouncedSave(rowIndex, field, value);\n    }, 800); // 800ms防抖延迟\n  };\n\n  // 处理失焦保存（立即保存）\n  const handleBlurSave = async (rowIndex, field) => {\n    const key = `${rowIndex}-${field}`;\n    const value = tempValues[key];\n\n    if (value !== undefined) {\n      // 清除防抖定时器\n      if (saveTimeoutRef.current) {\n        clearTimeout(saveTimeoutRef.current);\n        saveTimeoutRef.current = null;\n      }\n\n      // 立即保存\n      await debouncedSave(rowIndex, field, value);\n\n      // 清除临时值\n      setTempValues(prev => {\n        const newTemp = { ...prev };\n        delete newTemp[key];\n        return newTemp;\n      });\n    }\n  };\n\n  // 兼容原有的handleCellEdit接口\n  const handleCellEdit = async (rowIndex, field, value) => {\n    handleInputChange(rowIndex, field, value);\n  };\n\n  // 重试上次失败的操作\n  const retryLastOperation = async () => {\n    if (!lastFailedOperation) return;\n\n    const { rowIndex, field, value } = lastFailedOperation;\n    setSyncStatus('同步中');\n\n    try {\n      await kpiService.updateData(rowIndex, field, value);\n    } catch (error) {\n      console.error('重试失败:', error);\n    }\n  };\n\n  const startEdit = (rowIndex, field) => {\n    setEditingCell(`${rowIndex}-${field}`);\n  };\n\n  const finishEdit = () => {\n    setEditingCell(null);\n  };\n\n  // 处理选择性下载\n  const handleSelectiveDownload = async (selectionData) => {\n    setDownloadLoading(true);\n    try {\n      console.log('开始选择性下载:', selectionData);\n\n      const result = await kpiDownloadService.handleSelectiveDownload(selectionData);\n\n      if (result.success) {\n        console.log('下载成功:', result.message);\n        alert(`下载成功: ${result.message}`);\n      } else {\n        throw new Error(result.message || '下载失败');\n      }\n    } catch (error) {\n      console.error('下载失败:', error);\n      alert('下载失败: ' + error.message);\n    } finally {\n      setDownloadLoading(false);\n      setShowSelectiveDownloadModal(false);\n    }\n  };\n\n  // 打开选择性下载模态框\n  const openSelectiveDownloadModal = () => {\n    setShowSelectiveDownloadModal(true);\n  };\n\n  // 关闭选择性下载模态框\n  const closeSelectiveDownloadModal = () => {\n    setShowSelectiveDownloadModal(false);\n  };\n\n\n\n  // 获取月份名称\n  const getMonthName = (month) => {\n    const monthNames = ['', '1月', '2月', '3月', '4月', '5月', '6月', \n                       '7月', '8月', '9月', '10月', '11月', '12月'];\n    return monthNames[month];\n  };\n\n  // 渲染表格\n  const renderTable = () => {\n    if (filteredData.length === 0) {\n      return (\n        <div className=\"no-data\">\n          <p>{data.length === 0 ? '此部分暂无数据' : '当前筛选条件下没有数据'}</p>\n          <p style={{ fontSize: '0.8rem', color: '#666', marginTop: '10px' }}>\n            调试信息: 总数据 {data.length} 条，筛选后 {filteredData.length} 条\n          </p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"module4_kpi-table-container\">\n        <table className=\"kpi-data-table\">\n          <thead>\n            <tr>\n              <th className=\"col-number\">序号</th>\n              <th className=\"col-indicator\">指标</th>\n              <th className=\"col-target\">目标值</th>\n              <th className=\"col-score\">分值(30)</th>\n              <th className=\"col-method\">统计方式&口径</th>\n              <th className=\"col-standard\">考核标准</th>\n              {/* 动态月份列 - 根据提示词要求显示连续两个月 */}\n              {currentMonthPair.map(month => [\n                <th key={`month-${month}`} className=\"col-month\">{getMonthName(month)}份</th>,\n                <th key={`completion-${month}`} className=\"col-completion\">{getMonthName(month)}份完成情况</th>,\n                <th key={`score-${month}`} className=\"col-score-month\">{getMonthName(month)}得分</th>\n              ])}\n            </tr>\n          </thead>\n          <tbody>\n            {filteredData.map((row, index) => {\n              const isMergedStart = row.isMergedStart;\n              const isMergedCell = row.isMergedCell;\n              const rowSpan = row.rowSpan || 1;\n\n              return (\n                <tr key={index} className={`kpi-data-row ${isMergedCell ? 'merged-row' : ''} ${isMergedStart ? 'merged-start-row' : ''}`}>\n                  {/* 序号列 - 支持合并 */}\n                  {!isMergedCell && (\n                    <td\n                      className=\"kpi-data-cell col-number\"\n                      rowSpan={isMergedStart ? rowSpan : 1}\n                    >\n                      <div className=\"cell-content\">\n                        {row.序号 || ''}\n                      </div>\n                    </td>\n                  )}\n\n                  {/* 指标列 - 支持合并 */}\n                  {!isMergedCell && (\n                    <td\n                      className=\"kpi-data-cell col-indicator editable\"\n                      rowSpan={isMergedStart ? rowSpan : 1}\n                    >\n                      {renderEditableCell(index, '指标', row.指标 || '')}\n                    </td>\n                  )}\n\n                  {/* 目标值列 */}\n                  <td className=\"kpi-data-cell col-target editable\">\n                    {renderEditableCell(index, '目标值', row.目标值 || '')}\n                  </td>\n\n                  {/* 分值列 */}\n                  <td className=\"kpi-data-cell col-score editable\">\n                    {renderEditableCell(index, '分值', row.分值 || '')}\n                  </td>\n\n                  {/* 统计方式&口径列 */}\n                  <td className=\"kpi-data-cell col-method editable\">\n                    {renderEditableCell(index, '统计方式&口径', row['统计方式&口径'] || '')}\n                  </td>\n\n                  {/* 考核标准列 */}\n                  <td className=\"kpi-data-cell col-standard editable\">\n                    {renderEditableCell(index, '考核标准', row.考核标准 || '')}\n                  </td>\n\n                  {/* 动态月份列 */}\n                  {currentMonthPair.map(month => [\n                    // X月份列\n                    <td key={`month-${month}-${index}`} className=\"kpi-data-cell col-month editable\">\n                      {renderEditableCell(index, `${month}月份`, row[`${month}月份`] || '')}\n                    </td>,\n                    // X月份完成情况列\n                    <td key={`completion-${month}-${index}`} className=\"kpi-data-cell col-completion editable\">\n                      {renderEditableCell(index, `${month}月份完成情况`, row[`${month}月份完成情况`] || '')}\n                    </td>,\n                    // X月得分列\n                    <td key={`score-${month}-${index}`} className=\"kpi-data-cell col-score-month editable\">\n                      {renderEditableCell(index, `${month}月得分`, row[`${month}月得分`] || '')}\n                    </td>\n                  ])}\n                </tr>\n              );\n            })}\n          </tbody>\n        </table>\n      </div>\n    );\n  };\n\n  // 渲染可编辑单元格\n  const renderEditableCell = (rowIndex, field, value) => {\n    const cellKey = `${rowIndex}-${field}`;\n    const isEditing = editingCell === cellKey;\n\n    // 检查是否为只读列（完成情况列）\n    const isReadOnlyField = field.includes('完成情况');\n    const isEditable = !isReadOnlyField;\n\n    // 处理换行显示\n    let contentToRender = String(value || '');\n    if (contentToRender.includes('\\n')) {\n      contentToRender = contentToRender.split('\\n').map((line, i, arr) => (\n        <React.Fragment key={i}>{line}{i < arr.length - 1 && <br />}</React.Fragment>\n      ));\n    }\n\n    if (isEditing && isEditable) {\n      return (\n        <textarea\n          value={value}\n          onChange={e => handleInputChange(rowIndex, field, e.target.value)}\n          onBlur={() => {\n            handleBlurSave(rowIndex, field);\n            finishEdit();\n          }}\n          onKeyDown={e => {\n            if (e.key === 'Enter' && e.ctrlKey) {\n              handleBlurSave(rowIndex, field);\n              finishEdit();\n            }\n            if (e.key === 'Escape') {\n              finishEdit();\n            }\n          }}\n          autoFocus\n          className=\"cell-editor\"\n        />\n      );\n    }\n\n    // 确定单元格的CSS类名\n    let cellClasses = 'cell-content';\n    if (isReadOnlyField) {\n      cellClasses += ' readonly-cell';\n    } else if (isEditable) {\n      cellClasses += ' clickable';\n    }\n\n    // 确定提示文本\n    let titleText = '';\n    if (isReadOnlyField) {\n      titleText = '该列不可编辑';\n    } else if (isEditable) {\n      titleText = '点击编辑';\n    }\n\n    return (\n      <div\n        onClick={() => isEditable && startEdit(rowIndex, field)}\n        className={cellClasses}\n        title={titleText}\n        style={isReadOnlyField ? { cursor: 'not-allowed' } : {}}\n      >\n        {contentToRender || ''}\n      </div>\n    );\n  };\n\n\n\n  if (loading) {\n    return (\n      <div className=\"kpi-loading\">\n        <div className=\"loading-spinner\"></div>\n        <p>正在加载KPI数据...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"monthly-kpi\">\n      {/* 头部区域 */}\n      <div className=\"kpi-header\">\n        <div className=\"header-left\">\n          <button\n            className=\"back-button\"\n            onClick={() => onNavigate('home')}\n            title=\"返回首页\"\n          >\n            返回首页\n          </button>\n          <h1 className=\"page-title\">月度重点KPI跟踪仪表板</h1>\n        </div>\n\n        <div className=\"header-right\">\n          <div className=\"sync-status\">\n            <span className={`status-indicator ${syncStatus === '已同步' ? 'success' : 'pending'}`}>\n              {syncStatus}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* 控制面板区域 */}\n      <div className=\"control-panel\">\n        <div className=\"control-left\">\n          {/* 指标选择 */}\n          <div className=\"indicator-type-section\">\n            <span className=\"filter-label\">指标类型:</span>\n            <div className=\"indicator-selector-wrapper\">\n              <select\n                className=\"indicator-type-selector\"\n                value={selectedIndicators.length === 1 ? selectedIndicators[0] : 'all'}\n                onChange={handleIndicatorSelectChange}\n              >\n                <option value=\"all\">全部指标</option>\n                {getUniqueIndicators().map((indicator, index) => (\n                  <option key={index} value={indicator}>\n                    {indicator}\n                  </option>\n                ))}\n              </select>\n            </div>\n          </div>\n\n          {/* 当前显示统计 */}\n          <div className=\"current-display-stats\">\n            <span className=\"stats-label\">当前显示:</span>\n            <span className=\"stats-value\">{filteredData.length} 项</span>\n          </div>\n        </div>\n\n        <div className=\"control-right\">\n          {/* 月份导航 */}\n          <div className=\"month-navigation\">\n            <button\n              className=\"nav-button\"\n              onClick={() => navigateMonths('prev')}\n              disabled={currentMonthPair[0] <= 2}\n            >\n              ← 上一组月份\n            </button>\n\n            <div className=\"current-months\">\n              显示月份: {getMonthName(currentMonthPair[0])} + {getMonthName(currentMonthPair[1])}\n            </div>\n\n            <button\n              className=\"nav-button\"\n              onClick={() => navigateMonths('next')}\n              disabled={currentMonthPair[1] >= 12}\n            >\n              下一组月份 →\n            </button>\n          </div>\n\n          {/* 操作按钮 */}\n          <div className=\"action-buttons\">\n            <button\n              className=\"refresh-button\"\n              onClick={forceRefresh}\n              title=\"刷新数据\"\n            >\n              🔄 刷新数据\n            </button>\n            <button\n              className=\"download-button\"\n              onClick={openSelectiveDownloadModal}\n              title=\"选择性下载\"\n            >\n              📥 下载\n            </button>\n          </div>\n        </div>\n      </div>\n\n\n\n      {/* 数据表格 */}\n      <div className=\"kpi-content\">\n        {renderTable()}\n      </div>\n\n      {/* 底部统计 */}\n      <div className=\"kpi-footer\">\n        <div className=\"data-stats\">\n          {selectedIndicators.length > 0\n            ? `显示 ${filteredData.length} / ${data.length} 条KPI记录 (已筛选)`\n            : `共 ${data.length} 条KPI记录`\n          }\n        </div>\n      </div>\n\n      {/* 选择性下载模态框 */}\n      {showSelectiveDownloadModal && (\n        <KPISelectiveDownloadModal\n          data={data}\n          onDownload={handleSelectiveDownload}\n          onClose={closeSelectiveDownloadModal}\n          loading={downloadLoading}\n        />\n      )}\n    </div>\n  );\n};\n\nexport default MonthlyKPI; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,MAAO,0BAA0B,CACjC,MAAO,CAAAC,UAAU,KAAM,wBAAwB,CAC/C,MAAO,CAAAC,yBAAyB,KAAM,yCAAyC,CAC/E,MAAO,CAAAC,kBAAkB,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhE,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAChC;AACA,KAAM,CAACE,IAAI,CAAEC,OAAO,CAAC,CAAGf,QAAQ,CAAC,EAAE,CAAC,CACpC,KAAM,CAACgB,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACkB,OAAO,CAAEC,UAAU,CAAC,CAAGnB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACoB,WAAW,CAAEC,cAAc,CAAC,CAAGrB,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAACsB,UAAU,CAAEC,aAAa,CAAC,CAAGvB,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACwB,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzB,QAAQ,CAAC,EAAE,CAAC,CAC5D,KAAM,CAAC0B,mBAAmB,CAAEC,sBAAsB,CAAC,CAAG3B,QAAQ,CAAC,IAAI,CAAC,CACpE,KAAM,CAAC4B,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG7B,QAAQ,CAAC,KAAK,CAAC,CACnF,KAAM,CAAC8B,eAAe,CAAEC,kBAAkB,CAAC,CAAG/B,QAAQ,CAAC,KAAK,CAAC,CAE7D;AACA,KAAM,CAACgC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGjC,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAACkC,UAAU,CAAEC,aAAa,CAAC,CAAGnC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAE;AAClD,KAAM,CAAAoC,cAAc,CAAGlC,MAAM,CAAC,IAAI,CAAC,CACnC,KAAM,CAAAmC,cAAc,CAAGnC,MAAM,CAAC,IAAI,CAAC,CAEnC;AACA,KAAM,CAAAoC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,YAAY,CAAGF,GAAG,CAACG,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAE;AACzC,KAAM,CAAAC,aAAa,CAAGF,YAAY,GAAK,CAAC,CAAG,EAAE,CAAGA,YAAY,CAAG,CAAC,CAChE,MAAO,CAACE,aAAa,CAAEF,YAAY,CAAC,CACtC,CAAC,CAEDxC,SAAS,CAAC,IAAM,CACd;AACAwB,mBAAmB,CAACa,mBAAmB,CAAC,CAAC,CAAC,CAE1C;AACAM,QAAQ,CAAC,CAAC,CAEV;AACAxC,UAAU,CAACyC,gBAAgB,CACzB,IAAMtB,aAAa,CAAC,MAAM,CAAC,CAC1BuB,KAAK,EAAKvB,aAAa,CAAC,MAAM,CACjC,CAAC,CAED;AACA,MAAO,IAAM,CACX,GAAIa,cAAc,CAACW,OAAO,CAAE,CAC1BC,YAAY,CAACZ,cAAc,CAACW,OAAO,CAAC,CACtC,CACA,GAAIV,cAAc,CAACU,OAAO,CAAE,KAAAE,qBAAA,CAAAC,sBAAA,CAC1B,CAAAD,qBAAA,EAAAC,sBAAA,CAAAb,cAAc,CAACU,OAAO,EAACI,KAAK,UAAAF,qBAAA,iBAA5BA,qBAAA,CAAAG,IAAA,CAAAF,sBAA+B,CAAC,CAClC,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAN,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BzB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACFkC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAAnD,UAAU,CAACoD,WAAW,CAAC,CAAC,CAC9CH,OAAO,CAACC,GAAG,CAAC,WAAW,CAAEC,OAAO,CAAC,CAEjC,GAAIA,OAAO,EAAIE,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,CAAE,CACrC;AACA,KAAM,CAAAI,aAAa,CAAGC,kBAAkB,CAACL,OAAO,CAAC,CACjDxC,OAAO,CAAC4C,aAAa,CAAC,CACxB,CAAC,IAAM,CACLN,OAAO,CAACP,KAAK,CAAC,cAAc,CAAC,CAC7B/B,OAAO,CAAC,EAAE,CAAC,CACb,CACF,CAAE,MAAO+B,KAAK,CAAE,CACdO,OAAO,CAACP,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC/B,OAAO,CAAC,EAAE,CAAC,CACb,CACAI,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAyC,kBAAkB,CAAIC,OAAO,EAAK,CACtC,GAAI,CAACJ,KAAK,CAACC,OAAO,CAACG,OAAO,CAAC,EAAIA,OAAO,CAACC,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAE9D,KAAM,CAAAH,aAAa,CAAG,CAAC,GAAGE,OAAO,CAAC,CAElC;AACA,IAAK,GAAI,CAAAE,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,aAAa,CAACG,MAAM,CAAEC,CAAC,EAAE,CAAE,CAC7C,KAAM,CAAAC,UAAU,CAAGL,aAAa,CAACI,CAAC,CAAC,CACnC,GAAI,CAAAE,UAAU,CAAG,CAAC,CAElB;AACA,IAAK,GAAI,CAAAC,CAAC,CAAGH,CAAC,CAAG,CAAC,CAAEG,CAAC,CAAGP,aAAa,CAACG,MAAM,CAAEI,CAAC,EAAE,CAAE,CACjD,KAAM,CAAAC,OAAO,CAAGR,aAAa,CAACO,CAAC,CAAC,CAChC,GAAIF,UAAU,CAACI,EAAE,GAAKD,OAAO,CAACC,EAAE,EAAIJ,UAAU,CAACK,EAAE,GAAKF,OAAO,CAACE,EAAE,CAAE,CAChEJ,UAAU,EAAE,CACd,CAAC,IAAM,CACL,MACF,CACF,CAEA;AACA,GAAIA,UAAU,CAAG,CAAC,CAAE,CAClB;AACAN,aAAa,CAACI,CAAC,CAAC,CAACO,aAAa,CAAG,IAAI,CACrCX,aAAa,CAACI,CAAC,CAAC,CAACQ,OAAO,CAAGN,UAAU,CAErC;AACA,IAAK,GAAI,CAAAO,CAAC,CAAGT,CAAC,CAAG,CAAC,CAAES,CAAC,CAAGT,CAAC,CAAGE,UAAU,CAAEO,CAAC,EAAE,CAAE,CAC3Cb,aAAa,CAACa,CAAC,CAAC,CAACC,YAAY,CAAG,IAAI,CACtC,CAEA;AACAV,CAAC,EAAIE,UAAU,CAAG,CAAC,CACrB,CACF,CAEA,MAAO,CAAAN,aAAa,CACtB,CAAC,CAED;AACA,KAAM,CAAAe,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/BrB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B/B,aAAa,CAAC,QAAQ,CAAC,CACvB,KAAM,CAAAqB,QAAQ,CAAC,CAAC,CAChBrB,aAAa,CAAC,MAAM,CAAC,CACvB,CAAC,CAED;AACAtB,SAAS,CAAC,IAAM,CACd0E,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAAC7D,IAAI,CAAEkB,kBAAkB,CAAC,CAAC,CAE9B,KAAM,CAAA2C,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CAAAC,QAAQ,CAAG,CAAC,GAAG9D,IAAI,CAAC,CAExB;AACA,GAAIkB,kBAAkB,CAAC8B,MAAM,CAAG,CAAC,CAAE,CACjCc,QAAQ,CAAGA,QAAQ,CAACC,MAAM,CAACC,IAAI,EAC7B9C,kBAAkB,CAAC+C,QAAQ,CAACD,IAAI,CAACT,EAAE,CACrC,CAAC,CACH,CAEApD,eAAe,CAAC2D,QAAQ,CAAC,CAC3B,CAAC,CAED;AACA,KAAM,CAAAI,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAC5BpE,IAAI,CAACqE,OAAO,CAACL,IAAI,EAAI,CACnB,GAAIA,IAAI,CAACT,EAAE,EAAIS,IAAI,CAACT,EAAE,CAACe,IAAI,CAAC,CAAC,CAAE,CAC7BH,UAAU,CAACI,GAAG,CAACP,IAAI,CAACT,EAAE,CAACe,IAAI,CAAC,CAAC,CAAC,CAChC,CACF,CAAC,CAAC,CACF,MAAO,CAAA3B,KAAK,CAAC6B,IAAI,CAACL,UAAU,CAAC,CAACM,IAAI,CAAC,CAAC,CACtC,CAAC,CAID;AACA,KAAM,CAAAC,2BAA2B,CAAIC,KAAK,EAAK,CAC7C,KAAM,CAAAC,KAAK,CAAGD,KAAK,CAACE,MAAM,CAACD,KAAK,CAChC,GAAIA,KAAK,GAAK,KAAK,CAAE,CACnBzD,qBAAqB,CAAC,EAAE,CAAC,CAC3B,CAAC,IAAM,CACLA,qBAAqB,CAAC,CAACyD,KAAK,CAAC,CAAC,CAChC,CACF,CAAC,CAED;AACA,KAAM,CAAAE,cAAc,CAAIC,SAAS,EAAK,CACpC,KAAM,CAACC,KAAK,CAAEC,MAAM,CAAC,CAAGvE,gBAAgB,CAExC,GAAIqE,SAAS,GAAK,MAAM,EAAIC,KAAK,CAAG,CAAC,CAAE,CACrCrE,mBAAmB,CAAC,CAACqE,KAAK,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAC,CAAC,CAC9C,CAAC,IAAM,IAAIF,SAAS,GAAK,MAAM,EAAIE,MAAM,CAAG,EAAE,CAAE,CAC9CtE,mBAAmB,CAAC,CAACqE,KAAK,CAAG,CAAC,CAAEC,MAAM,CAAG,CAAC,CAAC,CAAC,CAC9C,CACF,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAG7F,WAAW,CAAC,MAAO8F,QAAQ,CAAEC,KAAK,CAAER,KAAK,GAAK,CAClE,GAAI,CACFnE,aAAa,CAAC,KAAK,CAAC,CAEpB;AACA,GAAIc,cAAc,CAACU,OAAO,CAAE,KAAAoD,sBAAA,CAAAC,sBAAA,CAC1B,CAAAD,sBAAA,EAAAC,sBAAA,CAAA/D,cAAc,CAACU,OAAO,EAACI,KAAK,UAAAgD,sBAAA,iBAA5BA,sBAAA,CAAA/C,IAAA,CAAAgD,sBAA+B,CAAC,CAClC,CAEA;AACA,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxCjE,cAAc,CAACU,OAAO,CAAGsD,UAAU,CAEnC;AACA1E,sBAAsB,CAAC,CAAEsE,QAAQ,CAAEC,KAAK,CAAER,KAAM,CAAC,CAAC,CAElD;AACA,KAAM,CAAAtF,UAAU,CAACmG,UAAU,CAACN,QAAQ,CAAEC,KAAK,CAAER,KAAK,CAAC,CAEnD;AACA,GAAI,CAACW,UAAU,CAACG,MAAM,CAACC,OAAO,CAAE,CAC9BlF,aAAa,CAAC,MAAM,CAAC,CACrBI,sBAAsB,CAAC,IAAI,CAAC,CAC5B+E,UAAU,CAAC,IAAMnF,aAAa,CAAC,KAAK,CAAC,CAAE,IAAI,CAAC,CAC5Cc,cAAc,CAACU,OAAO,CAAG,IAAI,CAC/B,CACF,CAAE,MAAOD,KAAK,CAAE,CACd,GAAI,CAACA,KAAK,CAAC6D,IAAI,GAAK,YAAY,CAAE,CAChCtD,OAAO,CAACP,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BvB,aAAa,CAAC,MAAM,CAAC,CACvB,CACF,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAqF,iBAAiB,CAAGA,CAACX,QAAQ,CAAEC,KAAK,CAAER,KAAK,GAAK,CACpD;AACA,KAAM,CAAAmB,OAAO,CAAG,CAAC,GAAG/F,IAAI,CAAC,CACzB+F,OAAO,CAACZ,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGR,KAAK,CAChC3E,OAAO,CAAC8F,OAAO,CAAC,CAEhB;AACA,KAAM,CAAAC,GAAG,IAAAC,MAAA,CAAMd,QAAQ,MAAAc,MAAA,CAAIb,KAAK,CAAE,CAClC/D,aAAa,CAAC6E,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACF,GAAG,EAAGpB,KAAK,EAAG,CAAC,CAElD;AACA,GAAItD,cAAc,CAACW,OAAO,CAAE,CAC1BC,YAAY,CAACZ,cAAc,CAACW,OAAO,CAAC,CACtC,CAEA;AACAX,cAAc,CAACW,OAAO,CAAG2D,UAAU,CAAC,IAAM,CACxCV,aAAa,CAACC,QAAQ,CAAEC,KAAK,CAAER,KAAK,CAAC,CACvC,CAAC,CAAE,GAAG,CAAC,CAAE;AACX,CAAC,CAED;AACA,KAAM,CAAAwB,cAAc,CAAG,KAAAA,CAAOjB,QAAQ,CAAEC,KAAK,GAAK,CAChD,KAAM,CAAAY,GAAG,IAAAC,MAAA,CAAMd,QAAQ,MAAAc,MAAA,CAAIb,KAAK,CAAE,CAClC,KAAM,CAAAR,KAAK,CAAGxD,UAAU,CAAC4E,GAAG,CAAC,CAE7B,GAAIpB,KAAK,GAAKyB,SAAS,CAAE,CACvB;AACA,GAAI/E,cAAc,CAACW,OAAO,CAAE,CAC1BC,YAAY,CAACZ,cAAc,CAACW,OAAO,CAAC,CACpCX,cAAc,CAACW,OAAO,CAAG,IAAI,CAC/B,CAEA;AACA,KAAM,CAAAiD,aAAa,CAACC,QAAQ,CAAEC,KAAK,CAAER,KAAK,CAAC,CAE3C;AACAvD,aAAa,CAAC6E,IAAI,EAAI,CACpB,KAAM,CAAAI,OAAO,CAAAH,aAAA,IAAQD,IAAI,CAAE,CAC3B,MAAO,CAAAI,OAAO,CAACN,GAAG,CAAC,CACnB,MAAO,CAAAM,OAAO,CAChB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,KAAAA,CAAOpB,QAAQ,CAAEC,KAAK,CAAER,KAAK,GAAK,CACvDkB,iBAAiB,CAACX,QAAQ,CAAEC,KAAK,CAAER,KAAK,CAAC,CAC3C,CAAC,CAED;AACA,KAAM,CAAA4B,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,CAAC5F,mBAAmB,CAAE,OAE1B,KAAM,CAAEuE,QAAQ,CAAEC,KAAK,CAAER,KAAM,CAAC,CAAGhE,mBAAmB,CACtDH,aAAa,CAAC,KAAK,CAAC,CAEpB,GAAI,CACF,KAAM,CAAAnB,UAAU,CAACmG,UAAU,CAACN,QAAQ,CAAEC,KAAK,CAAER,KAAK,CAAC,CACrD,CAAE,MAAO5C,KAAK,CAAE,CACdO,OAAO,CAACP,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAAyE,SAAS,CAAGA,CAACtB,QAAQ,CAAEC,KAAK,GAAK,CACrC7E,cAAc,IAAA0F,MAAA,CAAId,QAAQ,MAAAc,MAAA,CAAIb,KAAK,CAAE,CAAC,CACxC,CAAC,CAED,KAAM,CAAAsB,UAAU,CAAGA,CAAA,GAAM,CACvBnG,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAED;AACA,KAAM,CAAAoG,uBAAuB,CAAG,KAAO,CAAAC,aAAa,EAAK,CACvD3F,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CACFsB,OAAO,CAACC,GAAG,CAAC,UAAU,CAAEoE,aAAa,CAAC,CAEtC,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAArH,kBAAkB,CAACmH,uBAAuB,CAACC,aAAa,CAAC,CAE9E,GAAIC,MAAM,CAACC,OAAO,CAAE,CAClBvE,OAAO,CAACC,GAAG,CAAC,OAAO,CAAEqE,MAAM,CAACE,OAAO,CAAC,CACpCC,KAAK,8BAAAf,MAAA,CAAUY,MAAM,CAACE,OAAO,CAAE,CAAC,CAClC,CAAC,IAAM,CACL,KAAM,IAAI,CAAAE,KAAK,CAACJ,MAAM,CAACE,OAAO,EAAI,MAAM,CAAC,CAC3C,CACF,CAAE,MAAO/E,KAAK,CAAE,CACdO,OAAO,CAACP,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7BgF,KAAK,CAAC,QAAQ,CAAGhF,KAAK,CAAC+E,OAAO,CAAC,CACjC,CAAC,OAAS,CACR9F,kBAAkB,CAAC,KAAK,CAAC,CACzBF,6BAA6B,CAAC,KAAK,CAAC,CACtC,CACF,CAAC,CAED;AACA,KAAM,CAAAmG,0BAA0B,CAAGA,CAAA,GAAM,CACvCnG,6BAA6B,CAAC,IAAI,CAAC,CACrC,CAAC,CAED;AACA,KAAM,CAAAoG,2BAA2B,CAAGA,CAAA,GAAM,CACxCpG,6BAA6B,CAAC,KAAK,CAAC,CACtC,CAAC,CAID;AACA,KAAM,CAAAqG,YAAY,CAAIC,KAAK,EAAK,CAC9B,KAAM,CAAAC,UAAU,CAAG,CAAC,EAAE,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CACvC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACzD,MAAO,CAAAA,UAAU,CAACD,KAAK,CAAC,CAC1B,CAAC,CAED;AACA,KAAM,CAAAE,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAIrH,YAAY,CAAC8C,MAAM,GAAK,CAAC,CAAE,CAC7B,mBACEpD,KAAA,QAAK4H,SAAS,CAAC,SAAS,CAAAC,QAAA,eACtB/H,IAAA,MAAA+H,QAAA,CAAIzH,IAAI,CAACgD,MAAM,GAAK,CAAC,CAAG,SAAS,CAAG,aAAa,CAAI,CAAC,cACtDpD,KAAA,MAAG8H,KAAK,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,KAAK,CAAE,MAAM,CAAEC,SAAS,CAAE,MAAO,CAAE,CAAAJ,QAAA,EAAC,+CACxD,CAACzH,IAAI,CAACgD,MAAM,CAAC,kCAAO,CAAC9C,YAAY,CAAC8C,MAAM,CAAC,SACrD,EAAG,CAAC,EACD,CAAC,CAEV,CAEA,mBACEtD,IAAA,QAAK8H,SAAS,CAAC,6BAA6B,CAAAC,QAAA,cAC1C7H,KAAA,UAAO4H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC/B/H,IAAA,UAAA+H,QAAA,cACE7H,KAAA,OAAA6H,QAAA,eACE/H,IAAA,OAAI8H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,cAAE,CAAI,CAAC,cAClC/H,IAAA,OAAI8H,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAI,CAAC,cACrC/H,IAAA,OAAI8H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oBAAG,CAAI,CAAC,cACnC/H,IAAA,OAAI8H,SAAS,CAAC,WAAW,CAAAC,QAAA,CAAC,kBAAM,CAAI,CAAC,cACrC/H,IAAA,OAAI8H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,uCAAO,CAAI,CAAC,cACvC/H,IAAA,OAAI8H,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,0BAAI,CAAI,CAAC,CAErC/G,gBAAgB,CAACoH,GAAG,CAACT,KAAK,EAAI,cAC7BzH,KAAA,OAA2B4H,SAAS,CAAC,WAAW,CAAAC,QAAA,EAAEL,YAAY,CAACC,KAAK,CAAC,CAAC,QAAC,YAAApB,MAAA,CAArDoB,KAAK,CAAoD,CAAC,cAC5EzH,KAAA,OAAgC4H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAEL,YAAY,CAACC,KAAK,CAAC,CAAC,gCAAK,iBAAApB,MAAA,CAA9DoB,KAAK,CAA6D,CAAC,cAC1FzH,KAAA,OAA2B4H,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAEL,YAAY,CAACC,KAAK,CAAC,CAAC,cAAE,YAAApB,MAAA,CAA5DoB,KAAK,CAA2D,CAAC,CACpF,CAAC,EACA,CAAC,CACA,CAAC,cACR3H,IAAA,UAAA+H,QAAA,CACGvH,YAAY,CAAC4H,GAAG,CAAC,CAACC,GAAG,CAAEC,KAAK,GAAK,CAChC,KAAM,CAAAxE,aAAa,CAAGuE,GAAG,CAACvE,aAAa,CACvC,KAAM,CAAAG,YAAY,CAAGoE,GAAG,CAACpE,YAAY,CACrC,KAAM,CAAAF,OAAO,CAAGsE,GAAG,CAACtE,OAAO,EAAI,CAAC,CAEhC,mBACE7D,KAAA,OAAgB4H,SAAS,iBAAAvB,MAAA,CAAkBtC,YAAY,CAAG,YAAY,CAAG,EAAE,MAAAsC,MAAA,CAAIzC,aAAa,CAAG,kBAAkB,CAAG,EAAE,CAAG,CAAAiE,QAAA,EAEtH,CAAC9D,YAAY,eACZjE,IAAA,OACE8H,SAAS,CAAC,0BAA0B,CACpC/D,OAAO,CAAED,aAAa,CAAGC,OAAO,CAAG,CAAE,CAAAgE,QAAA,cAErC/H,IAAA,QAAK8H,SAAS,CAAC,cAAc,CAAAC,QAAA,CAC1BM,GAAG,CAACzE,EAAE,EAAI,EAAE,CACV,CAAC,CACJ,CACL,CAGA,CAACK,YAAY,eACZjE,IAAA,OACE8H,SAAS,CAAC,sCAAsC,CAChD/D,OAAO,CAAED,aAAa,CAAGC,OAAO,CAAG,CAAE,CAAAgE,QAAA,CAEpCQ,kBAAkB,CAACD,KAAK,CAAE,IAAI,CAAED,GAAG,CAACxE,EAAE,EAAI,EAAE,CAAC,CAC5C,CACL,cAGD7D,IAAA,OAAI8H,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CQ,kBAAkB,CAACD,KAAK,CAAE,KAAK,CAAED,GAAG,CAACG,GAAG,EAAI,EAAE,CAAC,CAC9C,CAAC,cAGLxI,IAAA,OAAI8H,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CQ,kBAAkB,CAACD,KAAK,CAAE,IAAI,CAAED,GAAG,CAACI,EAAE,EAAI,EAAE,CAAC,CAC5C,CAAC,cAGLzI,IAAA,OAAI8H,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CQ,kBAAkB,CAACD,KAAK,CAAE,SAAS,CAAED,GAAG,CAAC,SAAS,CAAC,EAAI,EAAE,CAAC,CACzD,CAAC,cAGLrI,IAAA,OAAI8H,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAChDQ,kBAAkB,CAACD,KAAK,CAAE,MAAM,CAAED,GAAG,CAACK,IAAI,EAAI,EAAE,CAAC,CAChD,CAAC,CAGJ1H,gBAAgB,CAACoH,GAAG,CAACT,KAAK,EAAI,cAC7B;AACA3H,IAAA,OAAoC8H,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7EQ,kBAAkB,CAACD,KAAK,IAAA/B,MAAA,CAAKoB,KAAK,iBAAMU,GAAG,IAAA9B,MAAA,CAAIoB,KAAK,iBAAK,EAAI,EAAE,CAAC,WAAApB,MAAA,CADjDoB,KAAK,MAAApB,MAAA,CAAI+B,KAAK,CAE5B,CAAC,cACL;AACAtI,IAAA,OAAyC8H,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACvFQ,kBAAkB,CAACD,KAAK,IAAA/B,MAAA,CAAKoB,KAAK,yCAAUU,GAAG,IAAA9B,MAAA,CAAIoB,KAAK,yCAAS,EAAI,EAAE,CAAC,gBAAApB,MAAA,CADpDoB,KAAK,MAAApB,MAAA,CAAI+B,KAAK,CAEjC,CAAC,cACL;AACAtI,IAAA,OAAoC8H,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CACnFQ,kBAAkB,CAACD,KAAK,IAAA/B,MAAA,CAAKoB,KAAK,uBAAOU,GAAG,IAAA9B,MAAA,CAAIoB,KAAK,uBAAM,EAAI,EAAE,CAAC,WAAApB,MAAA,CADnDoB,KAAK,MAAApB,MAAA,CAAI+B,KAAK,CAE5B,CAAC,CACN,CAAC,GAzDKA,KA0DL,CAAC,CAET,CAAC,CAAC,CACG,CAAC,EACH,CAAC,CACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAC9C,QAAQ,CAAEC,KAAK,CAAER,KAAK,GAAK,CACrD,KAAM,CAAAyD,OAAO,IAAApC,MAAA,CAAMd,QAAQ,MAAAc,MAAA,CAAIb,KAAK,CAAE,CACtC,KAAM,CAAAkD,SAAS,CAAGhI,WAAW,GAAK+H,OAAO,CAEzC;AACA,KAAM,CAAAE,eAAe,CAAGnD,KAAK,CAACnB,QAAQ,CAAC,MAAM,CAAC,CAC9C,KAAM,CAAAuE,UAAU,CAAG,CAACD,eAAe,CAEnC;AACA,GAAI,CAAAE,eAAe,CAAGC,MAAM,CAAC9D,KAAK,EAAI,EAAE,CAAC,CACzC,GAAI6D,eAAe,CAACxE,QAAQ,CAAC,IAAI,CAAC,CAAE,CAClCwE,eAAe,CAAGA,eAAe,CAACE,KAAK,CAAC,IAAI,CAAC,CAACb,GAAG,CAAC,CAACc,IAAI,CAAE3F,CAAC,CAAE4F,GAAG,gBAC7DjJ,KAAA,CAACX,KAAK,CAAC6J,QAAQ,EAAArB,QAAA,EAAUmB,IAAI,CAAE3F,CAAC,CAAG4F,GAAG,CAAC7F,MAAM,CAAG,CAAC,eAAItD,IAAA,QAAK,CAAC,GAAtCuD,CAAuD,CAC7E,CAAC,CACJ,CAEA,GAAIqF,SAAS,EAAIE,UAAU,CAAE,CAC3B,mBACE9I,IAAA,aACEkF,KAAK,CAAEA,KAAM,CACbmE,QAAQ,CAAEC,CAAC,EAAIlD,iBAAiB,CAACX,QAAQ,CAAEC,KAAK,CAAE4D,CAAC,CAACnE,MAAM,CAACD,KAAK,CAAE,CAClEqE,MAAM,CAAEA,CAAA,GAAM,CACZ7C,cAAc,CAACjB,QAAQ,CAAEC,KAAK,CAAC,CAC/BsB,UAAU,CAAC,CAAC,CACd,CAAE,CACFwC,SAAS,CAAEF,CAAC,EAAI,CACd,GAAIA,CAAC,CAAChD,GAAG,GAAK,OAAO,EAAIgD,CAAC,CAACG,OAAO,CAAE,CAClC/C,cAAc,CAACjB,QAAQ,CAAEC,KAAK,CAAC,CAC/BsB,UAAU,CAAC,CAAC,CACd,CACA,GAAIsC,CAAC,CAAChD,GAAG,GAAK,QAAQ,CAAE,CACtBU,UAAU,CAAC,CAAC,CACd,CACF,CAAE,CACF0C,SAAS,MACT5B,SAAS,CAAC,aAAa,CACxB,CAAC,CAEN,CAEA;AACA,GAAI,CAAA6B,WAAW,CAAG,cAAc,CAChC,GAAId,eAAe,CAAE,CACnBc,WAAW,EAAI,gBAAgB,CACjC,CAAC,IAAM,IAAIb,UAAU,CAAE,CACrBa,WAAW,EAAI,YAAY,CAC7B,CAEA;AACA,GAAI,CAAAC,SAAS,CAAG,EAAE,CAClB,GAAIf,eAAe,CAAE,CACnBe,SAAS,CAAG,QAAQ,CACtB,CAAC,IAAM,IAAId,UAAU,CAAE,CACrBc,SAAS,CAAG,MAAM,CACpB,CAEA,mBACE5J,IAAA,QACE6J,OAAO,CAAEA,CAAA,GAAMf,UAAU,EAAI/B,SAAS,CAACtB,QAAQ,CAAEC,KAAK,CAAE,CACxDoC,SAAS,CAAE6B,WAAY,CACvBG,KAAK,CAAEF,SAAU,CACjB5B,KAAK,CAAEa,eAAe,CAAG,CAAEkB,MAAM,CAAE,aAAc,CAAC,CAAG,CAAC,CAAE,CAAAhC,QAAA,CAEvDgB,eAAe,EAAI,EAAE,CACnB,CAAC,CAEV,CAAC,CAID,GAAIrI,OAAO,CAAE,CACX,mBACER,KAAA,QAAK4H,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/H,IAAA,QAAK8H,SAAS,CAAC,iBAAiB,CAAM,CAAC,cACvC9H,IAAA,MAAA+H,QAAA,CAAG,4CAAY,CAAG,CAAC,EAChB,CAAC,CAEV,CAEA,mBACE7H,KAAA,QAAK4H,SAAS,CAAC,aAAa,CAAAC,QAAA,eAE1B7H,KAAA,QAAK4H,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzB7H,KAAA,QAAK4H,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1B/H,IAAA,WACE8H,SAAS,CAAC,aAAa,CACvB+B,OAAO,CAAEA,CAAA,GAAMxJ,UAAU,CAAC,MAAM,CAAE,CAClCyJ,KAAK,CAAC,0BAAM,CAAA/B,QAAA,CACb,0BAED,CAAQ,CAAC,cACT/H,IAAA,OAAI8H,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,2DAAY,CAAI,CAAC,EACzC,CAAC,cAEN/H,IAAA,QAAK8H,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B/H,IAAA,QAAK8H,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B/H,IAAA,SAAM8H,SAAS,qBAAAvB,MAAA,CAAsBzF,UAAU,GAAK,KAAK,CAAG,SAAS,CAAG,SAAS,CAAG,CAAAiH,QAAA,CACjFjH,UAAU,CACP,CAAC,CACJ,CAAC,CACH,CAAC,EACH,CAAC,cAGNZ,KAAA,QAAK4H,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B7H,KAAA,QAAK4H,SAAS,CAAC,cAAc,CAAAC,QAAA,eAE3B7H,KAAA,QAAK4H,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrC/H,IAAA,SAAM8H,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC3C/H,IAAA,QAAK8H,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzC7H,KAAA,WACE4H,SAAS,CAAC,yBAAyB,CACnC5C,KAAK,CAAE1D,kBAAkB,CAAC8B,MAAM,GAAK,CAAC,CAAG9B,kBAAkB,CAAC,CAAC,CAAC,CAAG,KAAM,CACvE6H,QAAQ,CAAErE,2BAA4B,CAAA+C,QAAA,eAEtC/H,IAAA,WAAQkF,KAAK,CAAC,KAAK,CAAA6C,QAAA,CAAC,0BAAI,CAAQ,CAAC,CAChCvD,mBAAmB,CAAC,CAAC,CAAC4D,GAAG,CAAC,CAAC4B,SAAS,CAAE1B,KAAK,gBAC1CtI,IAAA,WAAoBkF,KAAK,CAAE8E,SAAU,CAAAjC,QAAA,CAClCiC,SAAS,EADC1B,KAEL,CACT,CAAC,EACI,CAAC,CACN,CAAC,EACH,CAAC,cAGNpI,KAAA,QAAK4H,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpC/H,IAAA,SAAM8H,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAK,CAAM,CAAC,cAC1C7H,KAAA,SAAM4H,SAAS,CAAC,aAAa,CAAAC,QAAA,EAAEvH,YAAY,CAAC8C,MAAM,CAAC,SAAE,EAAM,CAAC,EACzD,CAAC,EACH,CAAC,cAENpD,KAAA,QAAK4H,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5B7H,KAAA,QAAK4H,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B/H,IAAA,WACE8H,SAAS,CAAC,YAAY,CACtB+B,OAAO,CAAEA,CAAA,GAAMzE,cAAc,CAAC,MAAM,CAAE,CACtC6E,QAAQ,CAAEjJ,gBAAgB,CAAC,CAAC,CAAC,EAAI,CAAE,CAAA+G,QAAA,CACpC,uCAED,CAAQ,CAAC,cAET7H,KAAA,QAAK4H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,EAAC,4BACxB,CAACL,YAAY,CAAC1G,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAG,CAAC0G,YAAY,CAAC1G,gBAAgB,CAAC,CAAC,CAAC,CAAC,EAC3E,CAAC,cAENhB,IAAA,WACE8H,SAAS,CAAC,YAAY,CACtB+B,OAAO,CAAEA,CAAA,GAAMzE,cAAc,CAAC,MAAM,CAAE,CACtC6E,QAAQ,CAAEjJ,gBAAgB,CAAC,CAAC,CAAC,EAAI,EAAG,CAAA+G,QAAA,CACrC,uCAED,CAAQ,CAAC,EACN,CAAC,cAGN7H,KAAA,QAAK4H,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7B/H,IAAA,WACE8H,SAAS,CAAC,gBAAgB,CAC1B+B,OAAO,CAAE3F,YAAa,CACtB4F,KAAK,CAAC,0BAAM,CAAA/B,QAAA,CACb,uCAED,CAAQ,CAAC,cACT/H,IAAA,WACE8H,SAAS,CAAC,iBAAiB,CAC3B+B,OAAO,CAAErC,0BAA2B,CACpCsC,KAAK,CAAC,gCAAO,CAAA/B,QAAA,CACd,2BAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,cAKN/H,IAAA,QAAK8H,SAAS,CAAC,aAAa,CAAAC,QAAA,CACzBF,WAAW,CAAC,CAAC,CACX,CAAC,cAGN7H,IAAA,QAAK8H,SAAS,CAAC,YAAY,CAAAC,QAAA,cACzB/H,IAAA,QAAK8H,SAAS,CAAC,YAAY,CAAAC,QAAA,CACxBvG,kBAAkB,CAAC8B,MAAM,CAAG,CAAC,iBAAAiD,MAAA,CACpB/F,YAAY,CAAC8C,MAAM,QAAAiD,MAAA,CAAMjG,IAAI,CAACgD,MAAM,0DAAAiD,MAAA,CACrCjG,IAAI,CAACgD,MAAM,0BAAS,CAE1B,CAAC,CACH,CAAC,CAGLlC,0BAA0B,eACzBpB,IAAA,CAACH,yBAAyB,EACxBS,IAAI,CAAEA,IAAK,CACX4J,UAAU,CAAEjD,uBAAwB,CACpCkD,OAAO,CAAE1C,2BAA4B,CACrC/G,OAAO,CAAEY,eAAgB,CAC1B,CACF,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}