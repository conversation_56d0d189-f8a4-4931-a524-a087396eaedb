{"ast": null, "code": "import*as XLSX from'xlsx';class WorkTrackingDownloadService{constructor(){this.baseURL='/api';}// 处理选择性下载\nasync handleSelectiveDownload(selectionData){const{selectedData,format,monthRange,responsibleFilter,statistics,monthPairs}=selectionData;try{switch(format){case'excel':return await this.generateExcel(selectedData,monthRange,responsibleFilter,statistics,monthPairs);case'pdf':return await this.generatePDF(selectedData,monthRange,responsibleFilter,statistics,monthPairs);case'csv':return await this.generateCSV(selectedData,monthRange,responsibleFilter,statistics,monthPairs);default:throw new Error(\"\\u4E0D\\u652F\\u6301\\u7684\\u683C\\u5F0F: \".concat(format));}}catch(error){console.error('下载失败:',error);throw error;}}// 生成Excel文件\nasync generateExcel(selectedData,monthRange,responsibleFilter,statistics,monthPairs){try{// 创建新的工作簿\nconst workbook=XLSX.utils.book_new();// 创建统一的工作表，包含所有工作类型数据\nthis.createUnifiedTrackingWorksheet(workbook,selectedData,monthRange,responsibleFilter,statistics,monthPairs);// 生成并下载文件\nconst fileName=\"\\u91CD\\u70B9\\u5DE5\\u4F5C\\u8DDF\\u8E2A\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".xlsx\");XLSX.writeFile(workbook,fileName);return{success:true,fileName,message:\"Excel\\u6587\\u4EF6\\u5DF2\\u751F\\u6210: \".concat(fileName)};}catch(error){console.error('Excel生成失败:',error);throw error;}}// 按工作类型分组数据\ngroupDataByType(selectedData){const grouped={};selectedData.forEach(item=>{const workType=item.data.重点工作类型||'其他';if(!grouped[workType]){grouped[workType]=[];}grouped[workType].push(item);});return grouped;}// 创建统一跟踪工作表（新方法）\ncreateUnifiedTrackingWorksheet(workbook,selectedData,monthRange,responsibleFilter,statistics,monthPairs){var _monthPairs$monthRang,_monthPairs$monthRang2;// 准备基础表头\nconst baseHeaders=['序号','重点工作类型','相关指标或方向','总体目标值','2025年目标','计算方法&2025年举措','负责人','跟踪频次'];// 根据月份范围生成月份列\nconst monthHeaders=[];for(let i=monthRange.start;i<=monthRange.end;i++){if(monthPairs[i]){monthHeaders.push(\"\".concat(monthPairs[i][0],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(monthPairs[i][0],\"\\u5B8C\\u6210\\u60C5\\u51B5\"));monthHeaders.push(\"\".concat(monthPairs[i][1],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(monthPairs[i][1],\"\\u5B8C\\u6210\\u60C5\\u51B5\"));}}const headers=[...baseHeaders,...monthHeaders];// 准备所有数据行\nconst allRows=[];// 添加标题行\nallRows.push(['开发中心重点工作跟踪表','','','','','','','',...monthHeaders.map(()=>'')]);// 添加筛选信息\nconst startMonth=((_monthPairs$monthRang=monthPairs[monthRange.start])===null||_monthPairs$monthRang===void 0?void 0:_monthPairs$monthRang[0])||'';const endMonth=((_monthPairs$monthRang2=monthPairs[monthRange.end])===null||_monthPairs$monthRang2===void 0?void 0:_monthPairs$monthRang2[1])||'';allRows.push([\"\\u6570\\u636E\\u8303\\u56F4: \".concat(startMonth,\" \\u81F3 \").concat(endMonth),'','','','','','','',...monthHeaders.map(()=>'')]);if(responsibleFilter.length>0){allRows.push([\"\\u8D1F\\u8D23\\u4EBA\\u7B5B\\u9009: \".concat(responsibleFilter.join('、')),'','','','','','','',...monthHeaders.map(()=>'')]);}// 按工作类型分组数据\nconst groupedData=this.groupDataByType(selectedData);let hasData=false;Object.entries(groupedData).forEach(_ref=>{let[workType,items]=_ref;if(items&&items.length>0){hasData=true;// 添加工作类型标题行\nallRows.push([\"\\uD83D\\uDCCB \".concat(workType,\" (\").concat(items.length,\"\\u9879)\"),'','','','','','','',...monthHeaders.map(()=>'')]);// 添加表头\nallRows.push([...headers]);// 添加该工作类型的数据\nitems.forEach((item,index)=>{const data=item.data;const row=[this.formatValue(data.序号),this.formatValue(data.重点工作类型),this.formatValue(data.相关指标或方向),this.formatValue(data.总体目标值),this.formatValue(data['2025年目标']),this.formatValue(data['计算方法&2025年举措']),this.formatValue(data.负责人),this.formatValue(data.跟踪频次)];// 添加月份数据\nfor(let i=monthRange.start;i<=monthRange.end;i++){if(monthPairs[i]){const month1=monthPairs[i][0];const month2=monthPairs[i][1];row.push(this.formatValue(data[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.formatValue(data[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.formatValue(data[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]));}}allRows.push(row);});}});// 如果没有数据，添加提示\nif(!hasData){allRows.push(['暂无选择的数据','','','','','','','',...monthHeaders.map(()=>'')]);}else{// 添加总计\nallRows.push(['=== 总计 ===','','','','','','','',...monthHeaders.map(()=>'')]);allRows.push(['总工作项数',\"\".concat(statistics.totalItems,\"\\u9879\"),'',\"\\u5DE5\\u4F5C\\u7C7B\\u578B: \".concat(statistics.workTypeCount,\"\\u79CD\"),'','','','',...monthHeaders.map(()=>'')]);}// 添加导出时间到最后一行\nallRows.push(['导出时间: '+this.formatDateTime(new Date()),'','','','','','','',...monthHeaders.map(()=>'')]);// 创建工作表\nconst worksheet=XLSX.utils.aoa_to_sheet(allRows);// 设置列宽 - 调整为包含所有列\nconst colWidths=[{wch:8},// 序号\n{wch:20},// 重点工作类型\n{wch:35},// 相关指标或方向\n{wch:20},// 总体目标值\n{wch:20},// 2025年目标\n{wch:40},// 计算方法&2025年举措\n{wch:12},// 负责人\n{wch:12},// 跟踪频次\n...monthHeaders.map(()=>({wch:15}))// 月份列\n];worksheet['!cols']=colWidths;// 设置样式\nthis.setUnifiedTrackingWorksheetStyle(worksheet,allRows.length,headers.length);// 添加到工作簿\nXLSX.utils.book_append_sheet(workbook,worksheet,'重点工作跟踪导出');}// 设置统一跟踪工作表样式（新方法）\nsetUnifiedTrackingWorksheetStyle(worksheet,totalRows,colCount){// 设置主标题样式 (第1行)\nfor(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(worksheet[cellRef]){worksheet[cellRef].s={font:{bold:true,size:16,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"00D4AA\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}// 合并主标题单元格\nif(!worksheet['!merges'])worksheet['!merges']=[];worksheet['!merges'].push({s:{r:0,c:0},e:{r:0,c:colCount-1}});// 查找并设置各种样式\nfor(let r=0;r<totalRows;r++){const cellRef=XLSX.utils.encode_cell({r:r,c:0});if(worksheet[cellRef]&&worksheet[cellRef].v){const cellValue=worksheet[cellRef].v.toString();// 工作类型标题样式\nif(cellValue.includes('📋')&&cellValue.includes('项)')){for(let i=0;i<colCount;i++){const titleCellRef=XLSX.utils.encode_cell({r:r,c:i});if(worksheet[titleCellRef]){worksheet[titleCellRef].s={font:{bold:true,size:14,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"4DD0FF\"}},alignment:{horizontal:\"left\",vertical:\"center\"}};}}// 合并工作类型标题\nworksheet['!merges'].push({s:{r:r,c:0},e:{r:r,c:colCount-1}});}// 表头样式\nelse if(cellValue==='序号'){for(let i=0;i<colCount;i++){const headerCellRef=XLSX.utils.encode_cell({r:r,c:i});if(worksheet[headerCellRef]){worksheet[headerCellRef].s={font:{bold:true,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"20FF4D\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}}// 总计样式\nelse if(cellValue.includes('总计')||cellValue.includes('=== 总计 ===')){for(let i=0;i<colCount;i++){const totalCellRef=XLSX.utils.encode_cell({r:r,c:i});if(worksheet[totalCellRef]){worksheet[totalCellRef].s={font:{bold:true,color:{rgb:\"000000\"}},fill:{fgColor:{rgb:\"FFFF4D\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}}// 信息行样式（导出时间、数据范围等）\nelse if(cellValue.includes('导出时间:')||cellValue.includes('数据范围:')||cellValue.includes('负责人筛选:')){for(let i=0;i<colCount;i++){const infoCellRef=XLSX.utils.encode_cell({r:r,c:i});if(worksheet[infoCellRef]){worksheet[infoCellRef].s={font:{bold:true,color:{rgb:\"333333\"}},fill:{fgColor:{rgb:\"E0E0E0\"}},alignment:{horizontal:\"left\",vertical:\"center\"}};}}// 合并信息行\nworksheet['!merges'].push({s:{r:r,c:0},e:{r:r,c:colCount-1}});}}}}// 生成PDF文件（临时实现）\nasync generatePDF(selectedData,monthRange,responsibleFilter,statistics,monthPairs){try{console.log('PDF生成功能待实现');// 临时实现：转为JSON后提示\nconst jsonData=this.prepareDataForExport(selectedData,monthRange,responsibleFilter,statistics,monthPairs);const dataStr=JSON.stringify(jsonData,null,2);const blob=new Blob([dataStr],{type:'application/json'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u91CD\\u70B9\\u5DE5\\u4F5C\\u8DDF\\u8E2A\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".json\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'PDF功能开发中，已生成JSON格式文件'};}catch(error){console.error('PDF生成失败:',error);throw error;}}// 生成CSV文件\nasync generateCSV(selectedData,monthRange,responsibleFilter,statistics,monthPairs){try{var _monthPairs$monthRang3,_monthPairs$monthRang4;let csvContent='\\uFEFF';// UTF-8 BOM for Excel compatibility\n// 添加汇总信息\ncsvContent+='重点工作跟踪选择导出\\n';csvContent+=\"\\u5BFC\\u51FA\\u65F6\\u95F4,\".concat(this.formatDateTime(new Date()),\"\\n\");csvContent+=\"\\u6708\\u4EFD\\u8303\\u56F4,\".concat(((_monthPairs$monthRang3=monthPairs[monthRange.start])===null||_monthPairs$monthRang3===void 0?void 0:_monthPairs$monthRang3[0])||'',\" \\u81F3 \").concat(((_monthPairs$monthRang4=monthPairs[monthRange.end])===null||_monthPairs$monthRang4===void 0?void 0:_monthPairs$monthRang4[1])||'',\"\\n\");if(responsibleFilter.length>0){csvContent+=\"\\u8D1F\\u8D23\\u4EBA\\u7B5B\\u9009,\".concat(responsibleFilter.join('、'),\"\\n\");}csvContent+=\"\\u603B\\u5DE5\\u4F5C\\u9879\\u6570,\".concat(statistics.totalItems,\"\\n\");csvContent+=\"\\u5DE5\\u4F5C\\u7C7B\\u578B\\u6570,\".concat(statistics.workTypeCount,\"\\n\\n\");// 按工作类型分组导出\nconst groupedData=this.groupDataByType(selectedData);Object.entries(groupedData).forEach(_ref2=>{let[workType,items]=_ref2;csvContent+=\"\\n=== \".concat(workType,\" ===\\n\");// 完整表头 - 包含所有必要字段\nconst baseHeaders=['序号','重点工作类型','相关指标或方向','总体目标值','2025年目标','计算方法&2025年举措','负责人','跟踪频次'];const monthHeaders=[];for(let i=monthRange.start;i<=monthRange.end;i++){if(monthPairs[i]){monthHeaders.push(\"\".concat(monthPairs[i][0],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(monthPairs[i][0],\"\\u5B8C\\u6210\\u60C5\\u51B5\"));monthHeaders.push(\"\".concat(monthPairs[i][1],\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"),\"\".concat(monthPairs[i][1],\"\\u5B8C\\u6210\\u60C5\\u51B5\"));}}const headers=[...baseHeaders,...monthHeaders];csvContent+=headers.join(',')+'\\n';// 数据行\nitems.forEach(item=>{const data=item.data;const row=[this.escapeCsvValue(data.序号),this.escapeCsvValue(data.重点工作类型),this.escapeCsvValue(data.相关指标或方向),this.escapeCsvValue(data.总体目标值),this.escapeCsvValue(data['2025年目标']),this.escapeCsvValue(data['计算方法&2025年举措']),this.escapeCsvValue(data.负责人),this.escapeCsvValue(data.跟踪频次)];// 添加月份数据\nfor(let i=monthRange.start;i<=monthRange.end;i++){if(monthPairs[i]){const month1=monthPairs[i][0];const month2=monthPairs[i][1];row.push(this.escapeCsvValue(data[\"\".concat(month1,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.escapeCsvValue(data[\"\".concat(month1,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]),this.escapeCsvValue(data[\"\".concat(month2,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\")]),this.escapeCsvValue(data[\"\".concat(month2,\"\\u5B8C\\u6210\\u60C5\\u51B5\")]));}}csvContent+=row.join(',')+'\\n';});csvContent+='\\n';});// 下载文件\nconst blob=new Blob([csvContent],{type:'text/csv;charset=utf-8;'});const url=URL.createObjectURL(blob);const a=document.createElement('a');a.href=url;a.download=\"\\u91CD\\u70B9\\u5DE5\\u4F5C\\u8DDF\\u8E2A\\u9009\\u62E9\\u5BFC\\u51FA_\".concat(this.formatDate(new Date()),\".csv\");document.body.appendChild(a);a.click();document.body.removeChild(a);URL.revokeObjectURL(url);return{success:true,message:'CSV文件已生成并下载'};}catch(error){console.error('CSV生成失败:',error);throw error;}}// 工具方法：格式化值\nformatValue(value){if(value===null||value===undefined||value===''){return'';}// 处理Excel公式对象\nif(typeof value==='object'){if(value.hasOwnProperty('v'))return value.v;if(value.hasOwnProperty('w'))return value.w;if(value.hasOwnProperty('t')&&value.hasOwnProperty('v'))return value.v;if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);}// 工具方法：CSV值转义\nescapeCsvValue(value){const formattedValue=this.formatValue(value);if(formattedValue.includes(',')||formattedValue.includes('\"')||formattedValue.includes('\\n')){return\"\\\"\".concat(formattedValue.replace(/\"/g,'\"\"'),\"\\\"\");}return formattedValue;}// 工具方法：设置表头样式\nsetHeaderStyle(worksheet,colCount){for(let i=0;i<colCount;i++){const cellRef=XLSX.utils.encode_cell({r:0,c:i});if(!worksheet[cellRef])continue;worksheet[cellRef].s={font:{bold:true,color:{rgb:\"FFFFFF\"}},fill:{fgColor:{rgb:\"00D4AA\"}},alignment:{horizontal:\"center\",vertical:\"center\"}};}}// 工具方法：格式化日期\nformatDate(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');return\"\".concat(year).concat(month).concat(day);}// 工具方法：格式化日期时间\nformatDateTime(date){const year=date.getFullYear();const month=String(date.getMonth()+1).padStart(2,'0');const day=String(date.getDate()).padStart(2,'0');const hours=String(date.getHours()).padStart(2,'0');const minutes=String(date.getMinutes()).padStart(2,'0');return\"\".concat(year,\"-\").concat(month,\"-\").concat(day,\" \").concat(hours,\":\").concat(minutes);}// 准备导出数据\nprepareDataForExport(selectedData,monthRange,responsibleFilter,statistics,monthPairs){var _monthPairs$monthRang5,_monthPairs$monthRang6;return{exportInfo:{timestamp:new Date().toISOString(),totalItems:statistics.totalItems,workTypeCount:statistics.workTypeCount,monthRange:{start:((_monthPairs$monthRang5=monthPairs[monthRange.start])===null||_monthPairs$monthRang5===void 0?void 0:_monthPairs$monthRang5[0])||'',end:((_monthPairs$monthRang6=monthPairs[monthRange.end])===null||_monthPairs$monthRang6===void 0?void 0:_monthPairs$monthRang6[1])||''},responsibleFilter},data:selectedData};}}export default new WorkTrackingDownloadService();", "map": {"version": 3, "names": ["XLSX", "WorkTrackingDownloadService", "constructor", "baseURL", "handleSelectiveDownload", "selectionData", "selectedData", "format", "<PERSON><PERSON><PERSON><PERSON>", "responsibleFilter", "statistics", "monthPairs", "generateExcel", "generatePDF", "generateCSV", "Error", "concat", "error", "console", "workbook", "utils", "book_new", "createUnifiedTrackingWorksheet", "fileName", "formatDate", "Date", "writeFile", "success", "message", "groupDataByType", "grouped", "for<PERSON>ach", "item", "workType", "data", "重点工作类型", "push", "_monthPairs$monthRang", "_monthPairs$monthRang2", "baseHeaders", "monthHeaders", "i", "start", "end", "headers", "allRows", "map", "startMonth", "endMonth", "length", "join", "groupedData", "hasData", "Object", "entries", "_ref", "items", "index", "row", "formatValue", "序号", "相关指标或方向", "总体目标值", "负责人", "跟踪频次", "month1", "month2", "totalItems", "workTypeCount", "formatDateTime", "worksheet", "aoa_to_sheet", "col<PERSON><PERSON><PERSON>", "wch", "setUnifiedTrackingWorksheetStyle", "book_append_sheet", "totalRows", "col<PERSON>ount", "cellRef", "encode_cell", "r", "c", "s", "font", "bold", "size", "color", "rgb", "fill", "fgColor", "alignment", "horizontal", "vertical", "e", "v", "cellValue", "toString", "includes", "titleCellRef", "headerCellRef", "totalCellRef", "infoCellRef", "log", "jsonData", "prepareDataForExport", "dataStr", "JSON", "stringify", "blob", "Blob", "type", "url", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "_monthPairs$monthRang3", "_monthPairs$monthRang4", "csv<PERSON><PERSON>nt", "_ref2", "escapeCsvValue", "value", "undefined", "hasOwnProperty", "w", "text", "richText", "String", "formattedValue", "replace", "setHeaderStyle", "date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "_monthPairs$monthRang5", "_monthPairs$monthRang6", "exportInfo", "timestamp", "toISOString"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块二/services/workTrackingDownloadService.js"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\n\r\nclass WorkTrackingDownloadService {\r\n  constructor() {\r\n    this.baseURL = '/api';\r\n  }\r\n\r\n  // 处理选择性下载\r\n  async handleSelectiveDownload(selectionData) {\r\n    const { selectedData, format, monthRange, responsibleFilter, statistics, monthPairs } = selectionData;\r\n    \r\n    try {\r\n      switch (format) {\r\n        case 'excel':\r\n          return await this.generateExcel(selectedData, monthRange, responsibleFilter, statistics, monthPairs);\r\n        case 'pdf':\r\n          return await this.generatePDF(selectedData, monthRange, responsibleFilter, statistics, monthPairs);\r\n        case 'csv':\r\n          return await this.generateCSV(selectedData, monthRange, responsibleFilter, statistics, monthPairs);\r\n        default:\r\n          throw new Error(`不支持的格式: ${format}`);\r\n      }\r\n    } catch (error) {\r\n      console.error('下载失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成Excel文件\r\n  async generateExcel(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {\r\n    try {\r\n      // 创建新的工作簿\r\n      const workbook = XLSX.utils.book_new();\r\n      \r\n      // 创建统一的工作表，包含所有工作类型数据\r\n      this.createUnifiedTrackingWorksheet(workbook, selectedData, monthRange, responsibleFilter, statistics, monthPairs);\r\n\r\n      // 生成并下载文件\r\n      const fileName = `重点工作跟踪选择导出_${this.formatDate(new Date())}.xlsx`;\r\n      XLSX.writeFile(workbook, fileName);\r\n      \r\n      return {\r\n        success: true,\r\n        fileName,\r\n        message: `Excel文件已生成: ${fileName}`\r\n      };\r\n    } catch (error) {\r\n      console.error('Excel生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 按工作类型分组数据\r\n  groupDataByType(selectedData) {\r\n    const grouped = {};\r\n    selectedData.forEach(item => {\r\n      const workType = item.data.重点工作类型 || '其他';\r\n      if (!grouped[workType]) {\r\n        grouped[workType] = [];\r\n      }\r\n      grouped[workType].push(item);\r\n    });\r\n    return grouped;\r\n  }\r\n\r\n  // 创建统一跟踪工作表（新方法）\r\n  createUnifiedTrackingWorksheet(workbook, selectedData, monthRange, responsibleFilter, statistics, monthPairs) {\r\n    // 准备基础表头\r\n    const baseHeaders = [\r\n      '序号', \r\n      '重点工作类型',\r\n      '相关指标或方向',\r\n      '总体目标值',\r\n      '2025年目标',\r\n      '计算方法&2025年举措',\r\n      '负责人',\r\n      '跟踪频次'\r\n    ];\r\n    \r\n    // 根据月份范围生成月份列\r\n    const monthHeaders = [];\r\n    for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n      if (monthPairs[i]) {\r\n        monthHeaders.push(`${monthPairs[i][0]}工作计划`, `${monthPairs[i][0]}完成情况`);\r\n        monthHeaders.push(`${monthPairs[i][1]}工作计划`, `${monthPairs[i][1]}完成情况`);\r\n      }\r\n    }\r\n    \r\n    const headers = [...baseHeaders, ...monthHeaders];\r\n    \r\n    // 准备所有数据行\r\n    const allRows = [];\r\n    \r\n    // 添加标题行\r\n    allRows.push(['开发中心重点工作跟踪表', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n    \r\n    // 添加筛选信息\r\n    const startMonth = monthPairs[monthRange.start]?.[0] || '';\r\n    const endMonth = monthPairs[monthRange.end]?.[1] || '';\r\n    allRows.push([`数据范围: ${startMonth} 至 ${endMonth}`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n    \r\n    if (responsibleFilter.length > 0) {\r\n      allRows.push([`负责人筛选: ${responsibleFilter.join('、')}`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n    }\r\n    \r\n    // 按工作类型分组数据\r\n    const groupedData = this.groupDataByType(selectedData);\r\n    let hasData = false;\r\n    \r\n    Object.entries(groupedData).forEach(([workType, items]) => {\r\n      if (items && items.length > 0) {\r\n        hasData = true;\r\n        \r\n        // 添加工作类型标题行\r\n        allRows.push([`📋 ${workType} (${items.length}项)`, '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n        \r\n        // 添加表头\r\n        allRows.push([...headers]);\r\n        \r\n        // 添加该工作类型的数据\r\n        items.forEach((item, index) => {\r\n          const data = item.data;\r\n          \r\n          const row = [\r\n            this.formatValue(data.序号),\r\n            this.formatValue(data.重点工作类型),\r\n            this.formatValue(data.相关指标或方向),\r\n            this.formatValue(data.总体目标值),\r\n            this.formatValue(data['2025年目标']),\r\n            this.formatValue(data['计算方法&2025年举措']),\r\n            this.formatValue(data.负责人),\r\n            this.formatValue(data.跟踪频次)\r\n          ];\r\n\r\n          // 添加月份数据\r\n          for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n            if (monthPairs[i]) {\r\n              const month1 = monthPairs[i][0];\r\n              const month2 = monthPairs[i][1];\r\n              \r\n              row.push(\r\n                this.formatValue(data[`${month1}工作计划`]),\r\n                this.formatValue(data[`${month1}完成情况`]),\r\n                this.formatValue(data[`${month2}工作计划`]),\r\n                this.formatValue(data[`${month2}完成情况`])\r\n              );\r\n            }\r\n          }\r\n          \r\n          allRows.push(row);\r\n        });\r\n      }\r\n    });\r\n\r\n    // 如果没有数据，添加提示\r\n    if (!hasData) {\r\n      allRows.push(['暂无选择的数据', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n    } else {\r\n      // 添加总计\r\n      allRows.push(['=== 总计 ===', '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n      allRows.push([\r\n        '总工作项数',\r\n        `${statistics.totalItems}项`,\r\n        '',\r\n        `工作类型: ${statistics.workTypeCount}种`,\r\n        '',\r\n        '',\r\n        '',\r\n        '',\r\n        ...monthHeaders.map(() => '')\r\n      ]);\r\n    }\r\n    \r\n    // 添加导出时间到最后一行\r\n    allRows.push(['导出时间: ' + this.formatDateTime(new Date()), '', '', '', '', '', '', '', ...monthHeaders.map(() => '')]);\r\n\r\n    // 创建工作表\r\n    const worksheet = XLSX.utils.aoa_to_sheet(allRows);\r\n    \r\n    // 设置列宽 - 调整为包含所有列\r\n    const colWidths = [\r\n      { wch: 8 },   // 序号\r\n      { wch: 20 },  // 重点工作类型\r\n      { wch: 35 },  // 相关指标或方向\r\n      { wch: 20 },  // 总体目标值\r\n      { wch: 20 },  // 2025年目标\r\n      { wch: 40 },  // 计算方法&2025年举措\r\n      { wch: 12 },  // 负责人\r\n      { wch: 12 },  // 跟踪频次\r\n      ...monthHeaders.map(() => ({ wch: 15 })) // 月份列\r\n    ];\r\n    worksheet['!cols'] = colWidths;\r\n\r\n    // 设置样式\r\n    this.setUnifiedTrackingWorksheetStyle(worksheet, allRows.length, headers.length);\r\n    \r\n    // 添加到工作簿\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, '重点工作跟踪导出');\r\n  }\r\n\r\n  // 设置统一跟踪工作表样式（新方法）\r\n  setUnifiedTrackingWorksheetStyle(worksheet, totalRows, colCount) {\r\n    // 设置主标题样式 (第1行)\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (worksheet[cellRef]) {\r\n        worksheet[cellRef].s = {\r\n          font: { bold: true, size: 16, color: { rgb: \"FFFFFF\" } },\r\n          fill: { fgColor: { rgb: \"00D4AA\" } },\r\n          alignment: { horizontal: \"center\", vertical: \"center\" }\r\n        };\r\n      }\r\n    }\r\n\r\n    // 合并主标题单元格\r\n    if (!worksheet['!merges']) worksheet['!merges'] = [];\r\n    worksheet['!merges'].push({\r\n      s: { r: 0, c: 0 },\r\n      e: { r: 0, c: colCount - 1 }\r\n    });\r\n\r\n    // 查找并设置各种样式\r\n    for (let r = 0; r < totalRows; r++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: r, c: 0 });\r\n      if (worksheet[cellRef] && worksheet[cellRef].v) {\r\n        const cellValue = worksheet[cellRef].v.toString();\r\n        \r\n        // 工作类型标题样式\r\n        if (cellValue.includes('📋') && cellValue.includes('项)')) {\r\n          for (let i = 0; i < colCount; i++) {\r\n            const titleCellRef = XLSX.utils.encode_cell({ r: r, c: i });\r\n            if (worksheet[titleCellRef]) {\r\n              worksheet[titleCellRef].s = {\r\n                font: { bold: true, size: 14, color: { rgb: \"FFFFFF\" } },\r\n                fill: { fgColor: { rgb: \"4DD0FF\" } },\r\n                alignment: { horizontal: \"left\", vertical: \"center\" }\r\n              };\r\n            }\r\n          }\r\n          // 合并工作类型标题\r\n          worksheet['!merges'].push({\r\n            s: { r: r, c: 0 },\r\n            e: { r: r, c: colCount - 1 }\r\n          });\r\n        }\r\n        \r\n        // 表头样式\r\n        else if (cellValue === '序号') {\r\n          for (let i = 0; i < colCount; i++) {\r\n            const headerCellRef = XLSX.utils.encode_cell({ r: r, c: i });\r\n            if (worksheet[headerCellRef]) {\r\n              worksheet[headerCellRef].s = {\r\n                font: { bold: true, color: { rgb: \"FFFFFF\" } },\r\n                fill: { fgColor: { rgb: \"20FF4D\" } },\r\n                alignment: { horizontal: \"center\", vertical: \"center\" }\r\n              };\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 总计样式\r\n        else if (cellValue.includes('总计') || cellValue.includes('=== 总计 ===')) {\r\n          for (let i = 0; i < colCount; i++) {\r\n            const totalCellRef = XLSX.utils.encode_cell({ r: r, c: i });\r\n            if (worksheet[totalCellRef]) {\r\n              worksheet[totalCellRef].s = {\r\n                font: { bold: true, color: { rgb: \"000000\" } },\r\n                fill: { fgColor: { rgb: \"FFFF4D\" } },\r\n                alignment: { horizontal: \"center\", vertical: \"center\" }\r\n              };\r\n            }\r\n          }\r\n        }\r\n        \r\n        // 信息行样式（导出时间、数据范围等）\r\n        else if (cellValue.includes('导出时间:') || cellValue.includes('数据范围:') || cellValue.includes('负责人筛选:')) {\r\n          for (let i = 0; i < colCount; i++) {\r\n            const infoCellRef = XLSX.utils.encode_cell({ r: r, c: i });\r\n            if (worksheet[infoCellRef]) {\r\n              worksheet[infoCellRef].s = {\r\n                font: { bold: true, color: { rgb: \"333333\" } },\r\n                fill: { fgColor: { rgb: \"E0E0E0\" } },\r\n                alignment: { horizontal: \"left\", vertical: \"center\" }\r\n              };\r\n            }\r\n          }\r\n          // 合并信息行\r\n          worksheet['!merges'].push({\r\n            s: { r: r, c: 0 },\r\n            e: { r: r, c: colCount - 1 }\r\n          });\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n  // 生成PDF文件（临时实现）\r\n  async generatePDF(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {\r\n    try {\r\n      console.log('PDF生成功能待实现');\r\n      \r\n      // 临时实现：转为JSON后提示\r\n      const jsonData = this.prepareDataForExport(selectedData, monthRange, responsibleFilter, statistics, monthPairs);\r\n      const dataStr = JSON.stringify(jsonData, null, 2);\r\n      const blob = new Blob([dataStr], { type: 'application/json' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `重点工作跟踪选择导出_${this.formatDate(new Date())}.json`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'PDF功能开发中，已生成JSON格式文件'\r\n      };\r\n    } catch (error) {\r\n      console.error('PDF生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 生成CSV文件\r\n  async generateCSV(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {\r\n    try {\r\n      let csvContent = '\\uFEFF'; // UTF-8 BOM for Excel compatibility\r\n      \r\n      // 添加汇总信息\r\n      csvContent += '重点工作跟踪选择导出\\n';\r\n      csvContent += `导出时间,${this.formatDateTime(new Date())}\\n`;\r\n      csvContent += `月份范围,${monthPairs[monthRange.start]?.[0] || ''} 至 ${monthPairs[monthRange.end]?.[1] || ''}\\n`;\r\n      if (responsibleFilter.length > 0) {\r\n        csvContent += `负责人筛选,${responsibleFilter.join('、')}\\n`;\r\n      }\r\n      csvContent += `总工作项数,${statistics.totalItems}\\n`;\r\n      csvContent += `工作类型数,${statistics.workTypeCount}\\n\\n`;\r\n\r\n      // 按工作类型分组导出\r\n      const groupedData = this.groupDataByType(selectedData);\r\n      \r\n      Object.entries(groupedData).forEach(([workType, items]) => {\r\n        csvContent += `\\n=== ${workType} ===\\n`;\r\n        \r\n        // 完整表头 - 包含所有必要字段\r\n        const baseHeaders = [\r\n          '序号', \r\n          '重点工作类型',\r\n          '相关指标或方向',\r\n          '总体目标值',\r\n          '2025年目标',\r\n          '计算方法&2025年举措',\r\n          '负责人',\r\n          '跟踪频次'\r\n        ];\r\n        \r\n        const monthHeaders = [];\r\n        \r\n        for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n          if (monthPairs[i]) {\r\n            monthHeaders.push(`${monthPairs[i][0]}工作计划`, `${monthPairs[i][0]}完成情况`);\r\n            monthHeaders.push(`${monthPairs[i][1]}工作计划`, `${monthPairs[i][1]}完成情况`);\r\n          }\r\n        }\r\n        \r\n        const headers = [...baseHeaders, ...monthHeaders];\r\n        csvContent += headers.join(',') + '\\n';\r\n        \r\n        // 数据行\r\n        items.forEach(item => {\r\n          const data = item.data;\r\n          \r\n          const row = [\r\n            this.escapeCsvValue(data.序号),\r\n            this.escapeCsvValue(data.重点工作类型),\r\n            this.escapeCsvValue(data.相关指标或方向),\r\n            this.escapeCsvValue(data.总体目标值),\r\n            this.escapeCsvValue(data['2025年目标']),\r\n            this.escapeCsvValue(data['计算方法&2025年举措']),\r\n            this.escapeCsvValue(data.负责人),\r\n            this.escapeCsvValue(data.跟踪频次)\r\n          ];\r\n\r\n          // 添加月份数据\r\n          for (let i = monthRange.start; i <= monthRange.end; i++) {\r\n            if (monthPairs[i]) {\r\n              const month1 = monthPairs[i][0];\r\n              const month2 = monthPairs[i][1];\r\n              \r\n              row.push(\r\n                this.escapeCsvValue(data[`${month1}工作计划`]),\r\n                this.escapeCsvValue(data[`${month1}完成情况`]),\r\n                this.escapeCsvValue(data[`${month2}工作计划`]),\r\n                this.escapeCsvValue(data[`${month2}完成情况`])\r\n              );\r\n            }\r\n          }\r\n          \r\n          csvContent += row.join(',') + '\\n';\r\n        });\r\n        csvContent += '\\n';\r\n      });\r\n\r\n      // 下载文件\r\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n      const url = URL.createObjectURL(blob);\r\n      \r\n      const a = document.createElement('a');\r\n      a.href = url;\r\n      a.download = `重点工作跟踪选择导出_${this.formatDate(new Date())}.csv`;\r\n      document.body.appendChild(a);\r\n      a.click();\r\n      document.body.removeChild(a);\r\n      URL.revokeObjectURL(url);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'CSV文件已生成并下载'\r\n      };\r\n    } catch (error) {\r\n      console.error('CSV生成失败:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  // 工具方法：格式化值\r\n  formatValue(value) {\r\n    if (value === null || value === undefined || value === '') {\r\n      return '';\r\n    }\r\n    \r\n    // 处理Excel公式对象\r\n    if (typeof value === 'object') {\r\n      if (value.hasOwnProperty('v')) return value.v;\r\n      if (value.hasOwnProperty('w')) return value.w;\r\n      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;\r\n      if (value.text !== undefined) return value.text;\r\n      if (value.richText !== undefined) return value.richText;\r\n      if (value.value !== undefined) return value.value;\r\n      return String(value);\r\n    }\r\n    \r\n    return String(value);\r\n  }\r\n\r\n  // 工具方法：CSV值转义\r\n  escapeCsvValue(value) {\r\n    const formattedValue = this.formatValue(value);\r\n    if (formattedValue.includes(',') || formattedValue.includes('\"') || formattedValue.includes('\\n')) {\r\n      return `\"${formattedValue.replace(/\"/g, '\"\"')}\"`;\r\n    }\r\n    return formattedValue;\r\n  }\r\n\r\n  // 工具方法：设置表头样式\r\n  setHeaderStyle(worksheet, colCount) {\r\n    for (let i = 0; i < colCount; i++) {\r\n      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n      if (!worksheet[cellRef]) continue;\r\n      \r\n      worksheet[cellRef].s = {\r\n        font: { bold: true, color: { rgb: \"FFFFFF\" } },\r\n        fill: { fgColor: { rgb: \"00D4AA\" } },\r\n        alignment: { horizontal: \"center\", vertical: \"center\" }\r\n      };\r\n    }\r\n  }\r\n\r\n  // 工具方法：格式化日期\r\n  formatDate(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    return `${year}${month}${day}`;\r\n  }\r\n\r\n  // 工具方法：格式化日期时间\r\n  formatDateTime(date) {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n  }\r\n\r\n  // 准备导出数据\r\n  prepareDataForExport(selectedData, monthRange, responsibleFilter, statistics, monthPairs) {\r\n    return {\r\n      exportInfo: {\r\n        timestamp: new Date().toISOString(),\r\n        totalItems: statistics.totalItems,\r\n        workTypeCount: statistics.workTypeCount,\r\n        monthRange: {\r\n          start: monthPairs[monthRange.start]?.[0] || '',\r\n          end: monthPairs[monthRange.end]?.[1] || ''\r\n        },\r\n        responsibleFilter\r\n      },\r\n      data: selectedData\r\n    };\r\n  }\r\n}\r\n\r\nexport default new WorkTrackingDownloadService(); "], "mappings": "AAAA,MAAO,GAAK,CAAAA,IAAI,KAAM,MAAM,CAE5B,KAAM,CAAAC,2BAA4B,CAChCC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,MAAM,CACvB,CAEA;AACA,KAAM,CAAAC,uBAAuBA,CAACC,aAAa,CAAE,CAC3C,KAAM,CAAEC,YAAY,CAAEC,MAAM,CAAEC,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAGN,aAAa,CAErG,GAAI,CACF,OAAQE,MAAM,EACZ,IAAK,OAAO,CACV,MAAO,MAAM,KAAI,CAACK,aAAa,CAACN,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACtG,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACE,WAAW,CAACP,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACpG,IAAK,KAAK,CACR,MAAO,MAAM,KAAI,CAACG,WAAW,CAACR,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAC,CACpG,QACE,KAAM,IAAI,CAAAI,KAAK,0CAAAC,MAAA,CAAYT,MAAM,CAAE,CAAC,CACxC,CACF,CAAE,MAAOU,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,OAAO,CAAEA,KAAK,CAAC,CAC7B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAL,aAAaA,CAACN,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAE,CACvF,GAAI,CACF;AACA,KAAM,CAAAQ,QAAQ,CAAGnB,IAAI,CAACoB,KAAK,CAACC,QAAQ,CAAC,CAAC,CAEtC;AACA,IAAI,CAACC,8BAA8B,CAACH,QAAQ,CAAEb,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAC,CAElH;AACA,KAAM,CAAAY,QAAQ,iEAAAP,MAAA,CAAiB,IAAI,CAACQ,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CACjEzB,IAAI,CAAC0B,SAAS,CAACP,QAAQ,CAAEI,QAAQ,CAAC,CAElC,MAAO,CACLI,OAAO,CAAE,IAAI,CACbJ,QAAQ,CACRK,OAAO,yCAAAZ,MAAA,CAAiBO,QAAQ,CAClC,CAAC,CACH,CAAE,MAAON,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,YAAY,CAAEA,KAAK,CAAC,CAClC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAY,eAAeA,CAACvB,YAAY,CAAE,CAC5B,KAAM,CAAAwB,OAAO,CAAG,CAAC,CAAC,CAClBxB,YAAY,CAACyB,OAAO,CAACC,IAAI,EAAI,CAC3B,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAACE,IAAI,CAACC,MAAM,EAAI,IAAI,CACzC,GAAI,CAACL,OAAO,CAACG,QAAQ,CAAC,CAAE,CACtBH,OAAO,CAACG,QAAQ,CAAC,CAAG,EAAE,CACxB,CACAH,OAAO,CAACG,QAAQ,CAAC,CAACG,IAAI,CAACJ,IAAI,CAAC,CAC9B,CAAC,CAAC,CACF,MAAO,CAAAF,OAAO,CAChB,CAEA;AACAR,8BAA8BA,CAACH,QAAQ,CAAEb,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAE,KAAA0B,qBAAA,CAAAC,sBAAA,CAC5G;AACA,KAAM,CAAAC,WAAW,CAAG,CAClB,IAAI,CACJ,QAAQ,CACR,SAAS,CACT,OAAO,CACP,SAAS,CACT,cAAc,CACd,KAAK,CACL,MAAM,CACP,CAED;AACA,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,IAAK,GAAI,CAAAC,CAAC,CAAGjC,UAAU,CAACkC,KAAK,CAAED,CAAC,EAAIjC,UAAU,CAACmC,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,GAAI9B,UAAU,CAAC8B,CAAC,CAAC,CAAE,CACjBD,YAAY,CAACJ,IAAI,IAAApB,MAAA,CAAIL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAAzB,MAAA,CAAWL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,4BAAM,CAAC,CACvED,YAAY,CAACJ,IAAI,IAAApB,MAAA,CAAIL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAAzB,MAAA,CAAWL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,4BAAM,CAAC,CACzE,CACF,CAEA,KAAM,CAAAG,OAAO,CAAG,CAAC,GAAGL,WAAW,CAAE,GAAGC,YAAY,CAAC,CAEjD;AACA,KAAM,CAAAK,OAAO,CAAG,EAAE,CAElB;AACAA,OAAO,CAACT,IAAI,CAAC,CAAC,aAAa,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGI,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CAExF;AACA,KAAM,CAAAC,UAAU,CAAG,EAAAV,qBAAA,CAAA1B,UAAU,CAACH,UAAU,CAACkC,KAAK,CAAC,UAAAL,qBAAA,iBAA5BA,qBAAA,CAA+B,CAAC,CAAC,GAAI,EAAE,CAC1D,KAAM,CAAAW,QAAQ,CAAG,EAAAV,sBAAA,CAAA3B,UAAU,CAACH,UAAU,CAACmC,GAAG,CAAC,UAAAL,sBAAA,iBAA1BA,sBAAA,CAA6B,CAAC,CAAC,GAAI,EAAE,CACtDO,OAAO,CAACT,IAAI,CAAC,8BAAApB,MAAA,CAAU+B,UAAU,aAAA/B,MAAA,CAAMgC,QAAQ,EAAI,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGR,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CAE9G,GAAIrC,iBAAiB,CAACwC,MAAM,CAAG,CAAC,CAAE,CAChCJ,OAAO,CAACT,IAAI,CAAC,oCAAApB,MAAA,CAAWP,iBAAiB,CAACyC,IAAI,CAAC,GAAG,CAAC,EAAI,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGV,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CACpH,CAEA;AACA,KAAM,CAAAK,WAAW,CAAG,IAAI,CAACtB,eAAe,CAACvB,YAAY,CAAC,CACtD,GAAI,CAAA8C,OAAO,CAAG,KAAK,CAEnBC,MAAM,CAACC,OAAO,CAACH,WAAW,CAAC,CAACpB,OAAO,CAACwB,IAAA,EAAuB,IAAtB,CAACtB,QAAQ,CAAEuB,KAAK,CAAC,CAAAD,IAAA,CACpD,GAAIC,KAAK,EAAIA,KAAK,CAACP,MAAM,CAAG,CAAC,CAAE,CAC7BG,OAAO,CAAG,IAAI,CAEd;AACAP,OAAO,CAACT,IAAI,CAAC,iBAAApB,MAAA,CAAOiB,QAAQ,OAAAjB,MAAA,CAAKwC,KAAK,CAACP,MAAM,YAAM,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGT,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CAE9G;AACAD,OAAO,CAACT,IAAI,CAAC,CAAC,GAAGQ,OAAO,CAAC,CAAC,CAE1B;AACAY,KAAK,CAACzB,OAAO,CAAC,CAACC,IAAI,CAAEyB,KAAK,GAAK,CAC7B,KAAM,CAAAvB,IAAI,CAAGF,IAAI,CAACE,IAAI,CAEtB,KAAM,CAAAwB,GAAG,CAAG,CACV,IAAI,CAACC,WAAW,CAACzB,IAAI,CAAC0B,EAAE,CAAC,CACzB,IAAI,CAACD,WAAW,CAACzB,IAAI,CAACC,MAAM,CAAC,CAC7B,IAAI,CAACwB,WAAW,CAACzB,IAAI,CAAC2B,OAAO,CAAC,CAC9B,IAAI,CAACF,WAAW,CAACzB,IAAI,CAAC4B,KAAK,CAAC,CAC5B,IAAI,CAACH,WAAW,CAACzB,IAAI,CAAC,SAAS,CAAC,CAAC,CACjC,IAAI,CAACyB,WAAW,CAACzB,IAAI,CAAC,cAAc,CAAC,CAAC,CACtC,IAAI,CAACyB,WAAW,CAACzB,IAAI,CAAC6B,GAAG,CAAC,CAC1B,IAAI,CAACJ,WAAW,CAACzB,IAAI,CAAC8B,IAAI,CAAC,CAC5B,CAED;AACA,IAAK,GAAI,CAAAvB,CAAC,CAAGjC,UAAU,CAACkC,KAAK,CAAED,CAAC,EAAIjC,UAAU,CAACmC,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,GAAI9B,UAAU,CAAC8B,CAAC,CAAC,CAAE,CACjB,KAAM,CAAAwB,MAAM,CAAGtD,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/B,KAAM,CAAAyB,MAAM,CAAGvD,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAE/BiB,GAAG,CAACtB,IAAI,CACN,IAAI,CAACuB,WAAW,CAACzB,IAAI,IAAAlB,MAAA,CAAIiD,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACN,WAAW,CAACzB,IAAI,IAAAlB,MAAA,CAAIiD,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACN,WAAW,CAACzB,IAAI,IAAAlB,MAAA,CAAIkD,MAAM,6BAAO,CAAC,CACvC,IAAI,CAACP,WAAW,CAACzB,IAAI,IAAAlB,MAAA,CAAIkD,MAAM,6BAAO,CACxC,CAAC,CACH,CACF,CAEArB,OAAO,CAACT,IAAI,CAACsB,GAAG,CAAC,CACnB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAACN,OAAO,CAAE,CACZP,OAAO,CAACT,IAAI,CAAC,CAAC,SAAS,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGI,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CACtF,CAAC,IAAM,CACL;AACAD,OAAO,CAACT,IAAI,CAAC,CAAC,YAAY,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGI,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CACvFD,OAAO,CAACT,IAAI,CAAC,CACX,OAAO,IAAApB,MAAA,CACJN,UAAU,CAACyD,UAAU,WACxB,EAAE,8BAAAnD,MAAA,CACON,UAAU,CAAC0D,aAAa,WACjC,EAAE,CACF,EAAE,CACF,EAAE,CACF,EAAE,CACF,GAAG5B,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAC9B,CAAC,CACJ,CAEA;AACAD,OAAO,CAACT,IAAI,CAAC,CAAC,QAAQ,CAAG,IAAI,CAACiC,cAAc,CAAC,GAAI,CAAA5C,IAAI,CAAC,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAGe,YAAY,CAACM,GAAG,CAAC,IAAM,EAAE,CAAC,CAAC,CAAC,CAErH;AACA,KAAM,CAAAwB,SAAS,CAAGtE,IAAI,CAACoB,KAAK,CAACmD,YAAY,CAAC1B,OAAO,CAAC,CAElD;AACA,KAAM,CAAA2B,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,CAAE,CAAC,CAAI;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,CAAEA,GAAG,CAAE,EAAG,CAAC,CAAG;AACd,GAAGjC,YAAY,CAACM,GAAG,CAAC,KAAO,CAAE2B,GAAG,CAAE,EAAG,CAAC,CAAC,CAAE;AAAA,CAC1C,CACDH,SAAS,CAAC,OAAO,CAAC,CAAGE,SAAS,CAE9B;AACA,IAAI,CAACE,gCAAgC,CAACJ,SAAS,CAAEzB,OAAO,CAACI,MAAM,CAAEL,OAAO,CAACK,MAAM,CAAC,CAEhF;AACAjD,IAAI,CAACoB,KAAK,CAACuD,iBAAiB,CAACxD,QAAQ,CAAEmD,SAAS,CAAE,UAAU,CAAC,CAC/D,CAEA;AACAI,gCAAgCA,CAACJ,SAAS,CAAEM,SAAS,CAAEC,QAAQ,CAAE,CAC/D;AACA,IAAK,GAAI,CAAApC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAqC,OAAO,CAAG9E,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CACtD,GAAI6B,SAAS,CAACQ,OAAO,CAAC,CAAE,CACtBR,SAAS,CAACQ,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,IAAI,CAAE,EAAE,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CACxDC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CAEA;AACA,GAAI,CAACtB,SAAS,CAAC,SAAS,CAAC,CAAEA,SAAS,CAAC,SAAS,CAAC,CAAG,EAAE,CACpDA,SAAS,CAAC,SAAS,CAAC,CAAClC,IAAI,CAAC,CACxB8C,CAAC,CAAE,CAAEF,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CACjBY,CAAC,CAAE,CAAEb,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAEJ,QAAQ,CAAG,CAAE,CAC7B,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAAG,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGJ,SAAS,CAAEI,CAAC,EAAE,CAAE,CAClC,KAAM,CAAAF,OAAO,CAAG9E,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CAAC,CACtD,GAAIX,SAAS,CAACQ,OAAO,CAAC,EAAIR,SAAS,CAACQ,OAAO,CAAC,CAACgB,CAAC,CAAE,CAC9C,KAAM,CAAAC,SAAS,CAAGzB,SAAS,CAACQ,OAAO,CAAC,CAACgB,CAAC,CAACE,QAAQ,CAAC,CAAC,CAEjD;AACA,GAAID,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,CAAE,CACxD,IAAK,GAAI,CAAAxD,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAyD,YAAY,CAAGlG,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CAC3D,GAAI6B,SAAS,CAAC4B,YAAY,CAAC,CAAE,CAC3B5B,SAAS,CAAC4B,YAAY,CAAC,CAAChB,CAAC,CAAG,CAC1BC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEC,IAAI,CAAE,EAAE,CAAEC,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CACxDC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,MAAM,CAAEC,QAAQ,CAAE,QAAS,CACtD,CAAC,CACH,CACF,CACA;AACAtB,SAAS,CAAC,SAAS,CAAC,CAAClC,IAAI,CAAC,CACxB8C,CAAC,CAAE,CAAEF,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CACjBY,CAAC,CAAE,CAAEb,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAEJ,QAAQ,CAAG,CAAE,CAC7B,CAAC,CAAC,CACJ,CAEA;AAAA,IACK,IAAIkB,SAAS,GAAK,IAAI,CAAE,CAC3B,IAAK,GAAI,CAAAtD,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAA0D,aAAa,CAAGnG,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CAC5D,GAAI6B,SAAS,CAAC6B,aAAa,CAAC,CAAE,CAC5B7B,SAAS,CAAC6B,aAAa,CAAC,CAACjB,CAAC,CAAG,CAC3BC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEE,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CACF,CAEA;AAAA,IACK,IAAIG,SAAS,CAACE,QAAQ,CAAC,IAAI,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,YAAY,CAAC,CAAE,CACrE,IAAK,GAAI,CAAAxD,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAA2D,YAAY,CAAGpG,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CAC3D,GAAI6B,SAAS,CAAC8B,YAAY,CAAC,CAAE,CAC3B9B,SAAS,CAAC8B,YAAY,CAAC,CAAClB,CAAC,CAAG,CAC1BC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEE,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CACF,CAEA;AAAA,IACK,IAAIG,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAIF,SAAS,CAACE,QAAQ,CAAC,QAAQ,CAAC,CAAE,CACnG,IAAK,GAAI,CAAAxD,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAA4D,WAAW,CAAGrG,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CAC1D,GAAI6B,SAAS,CAAC+B,WAAW,CAAC,CAAE,CAC1B/B,SAAS,CAAC+B,WAAW,CAAC,CAACnB,CAAC,CAAG,CACzBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEE,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,MAAM,CAAEC,QAAQ,CAAE,QAAS,CACtD,CAAC,CACH,CACF,CACA;AACAtB,SAAS,CAAC,SAAS,CAAC,CAAClC,IAAI,CAAC,CACxB8C,CAAC,CAAE,CAAEF,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAC,CACjBY,CAAC,CAAE,CAAEb,CAAC,CAAEA,CAAC,CAAEC,CAAC,CAAEJ,QAAQ,CAAG,CAAE,CAC7B,CAAC,CAAC,CACJ,CACF,CACF,CACF,CAMA;AACA,KAAM,CAAAhE,WAAWA,CAACP,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAE,CACrF,GAAI,CACFO,OAAO,CAACoF,GAAG,CAAC,YAAY,CAAC,CAEzB;AACA,KAAM,CAAAC,QAAQ,CAAG,IAAI,CAACC,oBAAoB,CAAClG,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAC,CAC/G,KAAM,CAAA8F,OAAO,CAAGC,IAAI,CAACC,SAAS,CAACJ,QAAQ,CAAE,IAAI,CAAE,CAAC,CAAC,CACjD,KAAM,CAAAK,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACJ,OAAO,CAAC,CAAE,CAAEK,IAAI,CAAE,kBAAmB,CAAC,CAAC,CAC9D,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,iEAAAtG,MAAA,CAAiB,IAAI,CAACQ,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,SAAO,CAC7D0F,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACLpF,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,sBACX,CAAC,CACH,CAAE,MAAOX,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAH,WAAWA,CAACR,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAE,CACrF,GAAI,KAAAiH,sBAAA,CAAAC,sBAAA,CACF,GAAI,CAAAC,UAAU,CAAG,QAAQ,CAAE;AAE3B;AACAA,UAAU,EAAI,cAAc,CAC5BA,UAAU,8BAAA9G,MAAA,CAAY,IAAI,CAACqD,cAAc,CAAC,GAAI,CAAA5C,IAAI,CAAC,CAAC,CAAC,MAAI,CACzDqG,UAAU,8BAAA9G,MAAA,CAAY,EAAA4G,sBAAA,CAAAjH,UAAU,CAACH,UAAU,CAACkC,KAAK,CAAC,UAAAkF,sBAAA,iBAA5BA,sBAAA,CAA+B,CAAC,CAAC,GAAI,EAAE,aAAA5G,MAAA,CAAM,EAAA6G,sBAAA,CAAAlH,UAAU,CAACH,UAAU,CAACmC,GAAG,CAAC,UAAAkF,sBAAA,iBAA1BA,sBAAA,CAA6B,CAAC,CAAC,GAAI,EAAE,MAAI,CAC5G,GAAIpH,iBAAiB,CAACwC,MAAM,CAAG,CAAC,CAAE,CAChC6E,UAAU,oCAAA9G,MAAA,CAAaP,iBAAiB,CAACyC,IAAI,CAAC,GAAG,CAAC,MAAI,CACxD,CACA4E,UAAU,oCAAA9G,MAAA,CAAaN,UAAU,CAACyD,UAAU,MAAI,CAChD2D,UAAU,oCAAA9G,MAAA,CAAaN,UAAU,CAAC0D,aAAa,QAAM,CAErD;AACA,KAAM,CAAAjB,WAAW,CAAG,IAAI,CAACtB,eAAe,CAACvB,YAAY,CAAC,CAEtD+C,MAAM,CAACC,OAAO,CAACH,WAAW,CAAC,CAACpB,OAAO,CAACgG,KAAA,EAAuB,IAAtB,CAAC9F,QAAQ,CAAEuB,KAAK,CAAC,CAAAuE,KAAA,CACpDD,UAAU,WAAA9G,MAAA,CAAaiB,QAAQ,UAAQ,CAEvC;AACA,KAAM,CAAAM,WAAW,CAAG,CAClB,IAAI,CACJ,QAAQ,CACR,SAAS,CACT,OAAO,CACP,SAAS,CACT,cAAc,CACd,KAAK,CACL,MAAM,CACP,CAED,KAAM,CAAAC,YAAY,CAAG,EAAE,CAEvB,IAAK,GAAI,CAAAC,CAAC,CAAGjC,UAAU,CAACkC,KAAK,CAAED,CAAC,EAAIjC,UAAU,CAACmC,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,GAAI9B,UAAU,CAAC8B,CAAC,CAAC,CAAE,CACjBD,YAAY,CAACJ,IAAI,IAAApB,MAAA,CAAIL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAAzB,MAAA,CAAWL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,4BAAM,CAAC,CACvED,YAAY,CAACJ,IAAI,IAAApB,MAAA,CAAIL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAAzB,MAAA,CAAWL,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,4BAAM,CAAC,CACzE,CACF,CAEA,KAAM,CAAAG,OAAO,CAAG,CAAC,GAAGL,WAAW,CAAE,GAAGC,YAAY,CAAC,CACjDsF,UAAU,EAAIlF,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CAEtC;AACAM,KAAK,CAACzB,OAAO,CAACC,IAAI,EAAI,CACpB,KAAM,CAAAE,IAAI,CAAGF,IAAI,CAACE,IAAI,CAEtB,KAAM,CAAAwB,GAAG,CAAG,CACV,IAAI,CAACsE,cAAc,CAAC9F,IAAI,CAAC0B,EAAE,CAAC,CAC5B,IAAI,CAACoE,cAAc,CAAC9F,IAAI,CAACC,MAAM,CAAC,CAChC,IAAI,CAAC6F,cAAc,CAAC9F,IAAI,CAAC2B,OAAO,CAAC,CACjC,IAAI,CAACmE,cAAc,CAAC9F,IAAI,CAAC4B,KAAK,CAAC,CAC/B,IAAI,CAACkE,cAAc,CAAC9F,IAAI,CAAC,SAAS,CAAC,CAAC,CACpC,IAAI,CAAC8F,cAAc,CAAC9F,IAAI,CAAC,cAAc,CAAC,CAAC,CACzC,IAAI,CAAC8F,cAAc,CAAC9F,IAAI,CAAC6B,GAAG,CAAC,CAC7B,IAAI,CAACiE,cAAc,CAAC9F,IAAI,CAAC8B,IAAI,CAAC,CAC/B,CAED;AACA,IAAK,GAAI,CAAAvB,CAAC,CAAGjC,UAAU,CAACkC,KAAK,CAAED,CAAC,EAAIjC,UAAU,CAACmC,GAAG,CAAEF,CAAC,EAAE,CAAE,CACvD,GAAI9B,UAAU,CAAC8B,CAAC,CAAC,CAAE,CACjB,KAAM,CAAAwB,MAAM,CAAGtD,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAC/B,KAAM,CAAAyB,MAAM,CAAGvD,UAAU,CAAC8B,CAAC,CAAC,CAAC,CAAC,CAAC,CAE/BiB,GAAG,CAACtB,IAAI,CACN,IAAI,CAAC4F,cAAc,CAAC9F,IAAI,IAAAlB,MAAA,CAAIiD,MAAM,6BAAO,CAAC,CAC1C,IAAI,CAAC+D,cAAc,CAAC9F,IAAI,IAAAlB,MAAA,CAAIiD,MAAM,6BAAO,CAAC,CAC1C,IAAI,CAAC+D,cAAc,CAAC9F,IAAI,IAAAlB,MAAA,CAAIkD,MAAM,6BAAO,CAAC,CAC1C,IAAI,CAAC8D,cAAc,CAAC9F,IAAI,IAAAlB,MAAA,CAAIkD,MAAM,6BAAO,CAC3C,CAAC,CACH,CACF,CAEA4D,UAAU,EAAIpE,GAAG,CAACR,IAAI,CAAC,GAAG,CAAC,CAAG,IAAI,CACpC,CAAC,CAAC,CACF4E,UAAU,EAAI,IAAI,CACpB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAlB,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACiB,UAAU,CAAC,CAAE,CAAEhB,IAAI,CAAE,yBAA0B,CAAC,CAAC,CACxE,KAAM,CAAAC,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC,CAErC,KAAM,CAAAM,CAAC,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACrCF,CAAC,CAACG,IAAI,CAAGN,GAAG,CACZG,CAAC,CAACI,QAAQ,iEAAAtG,MAAA,CAAiB,IAAI,CAACQ,UAAU,CAAC,GAAI,CAAAC,IAAI,CAAC,CAAC,CAAC,QAAM,CAC5D0F,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC,CAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC,CACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC,CAC5BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC,CAExB,MAAO,CACLpF,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,aACX,CAAC,CACH,CAAE,MAAOX,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,UAAU,CAAEA,KAAK,CAAC,CAChC,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACA0C,WAAWA,CAACsE,KAAK,CAAE,CACjB,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKC,SAAS,EAAID,KAAK,GAAK,EAAE,CAAE,CACzD,MAAO,EAAE,CACX,CAEA;AACA,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACnC,CAAC,CAC7C,GAAImC,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACG,CAAC,CAC7C,GAAIH,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,EAAIF,KAAK,CAACE,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAF,KAAK,CAACnC,CAAC,CAC1E,GAAImC,KAAK,CAACI,IAAI,GAAKH,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACI,IAAI,CAC/C,GAAIJ,KAAK,CAACK,QAAQ,GAAKJ,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACK,QAAQ,CACvD,GAAIL,KAAK,CAACA,KAAK,GAAKC,SAAS,CAAE,MAAO,CAAAD,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAM,MAAM,CAACN,KAAK,CAAC,CACtB,CAEA,MAAO,CAAAM,MAAM,CAACN,KAAK,CAAC,CACtB,CAEA;AACAD,cAAcA,CAACC,KAAK,CAAE,CACpB,KAAM,CAAAO,cAAc,CAAG,IAAI,CAAC7E,WAAW,CAACsE,KAAK,CAAC,CAC9C,GAAIO,cAAc,CAACvC,QAAQ,CAAC,GAAG,CAAC,EAAIuC,cAAc,CAACvC,QAAQ,CAAC,GAAG,CAAC,EAAIuC,cAAc,CAACvC,QAAQ,CAAC,IAAI,CAAC,CAAE,CACjG,WAAAjF,MAAA,CAAWwH,cAAc,CAACC,OAAO,CAAC,IAAI,CAAE,IAAI,CAAC,OAC/C,CACA,MAAO,CAAAD,cAAc,CACvB,CAEA;AACAE,cAAcA,CAACpE,SAAS,CAAEO,QAAQ,CAAE,CAClC,IAAK,GAAI,CAAApC,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGoC,QAAQ,CAAEpC,CAAC,EAAE,CAAE,CACjC,KAAM,CAAAqC,OAAO,CAAG9E,IAAI,CAACoB,KAAK,CAAC2D,WAAW,CAAC,CAAEC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAExC,CAAE,CAAC,CAAC,CACtD,GAAI,CAAC6B,SAAS,CAACQ,OAAO,CAAC,CAAE,SAEzBR,SAAS,CAACQ,OAAO,CAAC,CAACI,CAAC,CAAG,CACrBC,IAAI,CAAE,CAAEC,IAAI,CAAE,IAAI,CAAEE,KAAK,CAAE,CAAEC,GAAG,CAAE,QAAS,CAAE,CAAC,CAC9CC,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAEF,GAAG,CAAE,QAAS,CAAE,CAAC,CACpCG,SAAS,CAAE,CAAEC,UAAU,CAAE,QAAQ,CAAEC,QAAQ,CAAE,QAAS,CACxD,CAAC,CACH,CACF,CAEA;AACApE,UAAUA,CAACmH,IAAI,CAAE,CACf,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAGP,MAAM,CAACI,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGV,MAAM,CAACI,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,SAAAhI,MAAA,CAAU4H,IAAI,EAAA5H,MAAA,CAAG8H,KAAK,EAAA9H,MAAA,CAAGiI,GAAG,EAC9B,CAEA;AACA5E,cAAcA,CAACsE,IAAI,CAAE,CACnB,KAAM,CAAAC,IAAI,CAAGD,IAAI,CAACE,WAAW,CAAC,CAAC,CAC/B,KAAM,CAAAC,KAAK,CAAGP,MAAM,CAACI,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,KAAM,CAAAC,GAAG,CAAGV,MAAM,CAACI,IAAI,CAACO,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACnD,KAAM,CAAAG,KAAK,CAAGZ,MAAM,CAACI,IAAI,CAACS,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CACtD,KAAM,CAAAK,OAAO,CAAGd,MAAM,CAACI,IAAI,CAACW,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAC1D,SAAAhI,MAAA,CAAU4H,IAAI,MAAA5H,MAAA,CAAI8H,KAAK,MAAA9H,MAAA,CAAIiI,GAAG,MAAAjI,MAAA,CAAImI,KAAK,MAAAnI,MAAA,CAAIqI,OAAO,EACpD,CAEA;AACA7C,oBAAoBA,CAAClG,YAAY,CAAEE,UAAU,CAAEC,iBAAiB,CAAEC,UAAU,CAAEC,UAAU,CAAE,KAAA4I,sBAAA,CAAAC,sBAAA,CACxF,MAAO,CACLC,UAAU,CAAE,CACVC,SAAS,CAAE,GAAI,CAAAjI,IAAI,CAAC,CAAC,CAACkI,WAAW,CAAC,CAAC,CACnCxF,UAAU,CAAEzD,UAAU,CAACyD,UAAU,CACjCC,aAAa,CAAE1D,UAAU,CAAC0D,aAAa,CACvC5D,UAAU,CAAE,CACVkC,KAAK,CAAE,EAAA6G,sBAAA,CAAA5I,UAAU,CAACH,UAAU,CAACkC,KAAK,CAAC,UAAA6G,sBAAA,iBAA5BA,sBAAA,CAA+B,CAAC,CAAC,GAAI,EAAE,CAC9C5G,GAAG,CAAE,EAAA6G,sBAAA,CAAA7I,UAAU,CAACH,UAAU,CAACmC,GAAG,CAAC,UAAA6G,sBAAA,iBAA1BA,sBAAA,CAA6B,CAAC,CAAC,GAAI,EAC1C,CAAC,CACD/I,iBACF,CAAC,CACDyB,IAAI,CAAE5B,YACR,CAAC,CACH,CACF,CAEA,cAAe,IAAI,CAAAL,2BAA2B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}