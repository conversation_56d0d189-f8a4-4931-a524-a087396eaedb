@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Rajdhani:wght@400;600&display=swap');

/* KPI选择性下载模态框样式 */

.kpi-selective-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.kpi-selective-modal {
  background: linear-gradient(180deg, #16213e, #0f0f23);
  color: #e0e0e0;
  font-family: 'Rajdhani', sans-serif;
  border-radius: 16px;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 40px rgba(77, 200, 255, 0.4), 0 0 20px rgba(255, 77, 255, 0.2);
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(77, 200, 255, 0.3);
}

/* 头部 */
.modal-header {
  background: linear-gradient(90deg, #4dc8ff, #ff4dff);
  color: #fff;
  padding: 20px 25px;
  border-radius: 16px 16px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  font-family: 'Orbitron', sans-serif;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.close-button {
  background: none;
  border: none;
  color: #fff;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: rotate(90deg);
}

/* 内容区域 */
.modal-content {
  padding: 25px;
  overflow-y: auto;
  flex: 1;
}

/* 统计信息区域 */
.statistics-section {
  margin-bottom: 25px;
  padding: 20px;
  background: linear-gradient(135deg, rgba(77, 200, 255, 0.1), rgba(255, 77, 255, 0.1));
  border-radius: 12px;
  border: 1px solid rgba(77, 200, 255, 0.2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  border: 1px solid rgba(77, 200, 255, 0.2);
}

.stat-label {
  display: block;
  font-size: 14px;
  color: #b0b0b0;
  margin-bottom: 5px;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #4dc8ff;
  font-family: 'Orbitron', sans-serif;
  text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);
}

.stat-value.selected {
  color: #ff4dff;
  text-shadow: 0 0 10px rgba(255, 77, 255, 0.5);
}

/* 选择区域 */
.selection-section {
  margin-bottom: 25px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.selection-section h4 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #4dc8ff;
  text-shadow: 0 0 5px rgba(77, 200, 255, 0.3);
}

.select-all-button {
  padding: 8px 16px;
  background: linear-gradient(135deg, #4dc8ff, #ff4dff);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Rajdhani', sans-serif;
}

.select-all-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.4);
}

.select-all-button.selected {
  background: linear-gradient(135deg, #ff4dff, #ff6b4d);
}

/* 指标列表 */
.indicators-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.2);
}

.checkbox-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  cursor: pointer;
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
  transition: all 0.3s ease;
}

.checkbox-item:last-child {
  border-bottom: none;
}

.checkbox-item:hover {
  background: rgba(77, 200, 255, 0.1);
}

.checkbox-item input[type="checkbox"] {
  margin-right: 12px;
  margin-top: 2px;
  transform: scale(1.2);
  accent-color: #4dc8ff;
}

.indicator-item .indicator-info {
  flex: 1;
}

.indicator-main {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 5px;
}

.indicator-name {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
  flex: 1;
}

.indicator-score {
  font-size: 14px;
  color: #ff4dff;
  font-weight: 600;
  background: rgba(255, 77, 255, 0.1);
  padding: 2px 8px;
  border-radius: 4px;
}

.indicator-details {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.indicator-target,
.indicator-method {
  font-size: 13px;
  color: #b0b0b0;
}

.indicator-target {
  color: #4dc8ff;
}

/* 格式选择 */
.format-options {
  display: flex;
  gap: 20px;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 10px 15px;
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.radio-item:hover {
  background: rgba(77, 200, 255, 0.1);
  border-color: rgba(77, 200, 255, 0.4);
}

.radio-item input[type="radio"] {
  margin-right: 8px;
  accent-color: #4dc8ff;
}

/* 信息区域 */
.info-section {
  margin-top: 20px;
}

.info-box {
  background: rgba(77, 200, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 8px;
  padding: 15px;
}

.info-box h5 {
  margin: 0 0 10px 0;
  color: #4dc8ff;
  font-size: 16px;
}

.info-box ul {
  margin: 0;
  padding-left: 20px;
  color: #b0b0b0;
}

.info-box li {
  margin-bottom: 5px;
  font-size: 14px;
}

/* 底部按钮 */
.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid rgba(77, 200, 255, 0.2);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 0 0 16px 16px;
}

.cancel-button,
.download-button {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Rajdhani', sans-serif;
}

.cancel-button {
  background: rgba(128, 128, 128, 0.3);
  color: #e0e0e0;
  border: 1px solid rgba(128, 128, 128, 0.5);
}

.cancel-button:hover {
  background: rgba(128, 128, 128, 0.5);
}

.download-button {
  background: linear-gradient(135deg, #4dc8ff, #ff4dff);
  color: #fff;
  min-width: 150px;
}

.download-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(77, 200, 255, 0.4);
}

.download-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .kpi-selective-modal {
    width: 95%;
    max-height: 95vh;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .format-options {
    flex-direction: column;
    gap: 10px;
  }
  
  .modal-footer {
    flex-direction: column;
  }
}
