import React, { useState, useEffect } from 'react';
import './FileStatusTip.css';

const FileStatusTip = ({ syncStatus, onRetry }) => {
  const [showTip, setShowTip] = useState(false);
  const [tipType, setTipType] = useState('info');

  useEffect(() => {
    if (syncStatus === '同步失败') {
      setShowTip(true);
      setTipType('error');
    } else if (syncStatus === '同步成功') {
      setShowTip(true);
      setTipType('success');
      setTimeout(() => setShowTip(false), 3000);
    } else if (syncStatus === '同步中') {
      setShowTip(true);
      setTipType('loading');
    } else {
      setShowTip(false);
    }
  }, [syncStatus]);

  if (!showTip) return null;

  const getTipContent = () => {
    switch (tipType) {
      case 'error':
        return {
          icon: '⚠️',
          title: '数据保存失败',
          message: 'Excel文件可能被占用',
          suggestions: [
            '关闭Excel文件后重试',
            '等待几秒钟再操作',
            '检查文件是否被其他程序使用'
          ]
        };
      case 'success':
        return {
          icon: '✅',
          title: '数据保存成功',
          message: '数据已同步到Excel文件',
          suggestions: []
        };
      case 'loading':
        return {
          icon: '🔄',
          title: '正在保存数据',
          message: '请稍候...',
          suggestions: []
        };
      default:
        return {
          icon: 'ℹ️',
          title: '提示',
          message: '',
          suggestions: []
        };
    }
  };

  const content = getTipContent();

  return (
    <div className={`file-status-tip ${tipType}`}>
      <div className="tip-header">
        <span className="tip-icon">{content.icon}</span>
        <span className="tip-title">{content.title}</span>
        <button 
          className="tip-close"
          onClick={() => setShowTip(false)}
        >
          ×
        </button>
      </div>
      
      <div className="tip-content">
        <p className="tip-message">{content.message}</p>
        
        {content.suggestions.length > 0 && (
          <div className="tip-suggestions">
            <p><strong>解决方案：</strong></p>
            <ul>
              {content.suggestions.map((suggestion, index) => (
                <li key={index}>{suggestion}</li>
              ))}
            </ul>
          </div>
        )}
        
        {tipType === 'error' && onRetry && (
          <div className="tip-actions">
            <button 
              className="retry-button"
              onClick={onRetry}
            >
              重试保存
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileStatusTip;
