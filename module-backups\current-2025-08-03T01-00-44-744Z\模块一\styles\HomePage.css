/* 首页主容器 */
.homepage {
  min-height: 100vh;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #20ff4d;
  border-radius: 50%;
  animation: float 20s infinite linear;
  box-shadow: 0 0 10px rgba(32, 255, 77, 0.6);
}

.particle:nth-child(1) {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 25s;
}

.particle:nth-child(2) {
  top: 60%;
  left: 80%;
  animation-delay: -5s;
  animation-duration: 30s;
}

.particle:nth-child(3) {
  top: 80%;
  left: 30%;
  animation-delay: -10s;
  animation-duration: 35s;
}

@keyframes float {
  0% { transform: translateY(0px) translateX(0px); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(50px); opacity: 0; }
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(32, 255, 77, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(32, 255, 77, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

/* 顶部时间显示 */
.time-display {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  text-align: right;
}

.current-time {
  font-family: 'Orbitron', monospace;
  font-size: 1.2rem;
  color: #20ff4d;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(32, 255, 77, 0.6);
}

.system-status {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  color: #00d4aa;
  margin-top: 5px;
  opacity: 0.8;
}

/* 主标题区域 */
.main-header {
  text-align: center;
  padding: 80px 20px 60px;
  z-index: 1;
  position: relative;
}

.title-container {
  max-width: 1000px;
  margin: 0 auto;
}

.homepage .main-title {
  font-family: 'Orbitron', monospace;
  font-size: 3.5rem;
  font-weight: 900;
  color: #ffffff;
  text-shadow:
    0 0 20px rgba(32, 255, 77, 0.6),
    0 0 40px rgba(32, 255, 77, 0.3);
  margin-bottom: 1rem;
  letter-spacing: 4px;
  animation: fadeIn 1s ease-out;
}

.sub-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.8rem;
  font-weight: 600;
  color: #00d4aa;
  margin-bottom: 2rem;
  letter-spacing: 2px;
  animation: fadeIn 1s ease-out 0.3s both;
}

.title-divider {
  width: 200px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #20ff4d, transparent);
  margin: 2rem auto;
  animation: fadeIn 1s ease-out 0.6s both;
}

.system-description {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
  animation: fadeIn 1s ease-out 0.9s both;
}

/* 导航网格 */
.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  padding: 0 40px 40px;
  max-width: 1400px;
  margin: 0 auto;
  z-index: 1;
  position: relative;
  flex: 1;
}

.nav-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(32, 255, 77, 0.2);
  border-radius: 15px;
  padding: 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  animation: slideIn 0.6s ease-out var(--delay) both;
}

.nav-card:hover {
  transform: translateY(-10px);
  border-color: var(--color);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 30px rgba(32, 255, 77, 0.2);
}

.nav-card:hover .card-glow {
  opacity: 1;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, var(--color), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  mix-blend-mode: screen;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-icon {
  font-size: 2.5rem;
  filter: grayscale(100%);
  transition: filter 0.3s ease;
}

.nav-card:hover .card-icon {
  filter: grayscale(0%);
}

.card-number {
  font-family: 'Orbitron', monospace;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color);
  opacity: 0.6;
}

.card-content {
  text-align: left;
  margin-bottom: 25px;
}

.card-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8px;
  line-height: 1.3;
}

.card-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  color: var(--color);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

.card-description {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.95rem;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.nav-card:hover .card-footer {
  opacity: 1;
  transform: translateY(0);
}

.enter-text {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1rem;
  color: var(--color);
  font-weight: 600;
}

.arrow {
  font-size: 1.2rem;
  color: var(--color);
  animation: arrowPulse 2s ease-in-out infinite;
}

@keyframes arrowPulse {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5px); }
}

/* 底部状态栏 */
.status-bar {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 20px;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(32, 255, 77, 0.2);
  z-index: 1;
  position: relative;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-family: 'Rajdhani', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.6);
}

.status-value {
  font-family: 'Orbitron', monospace;
  font-size: 0.9rem;
  color: #20ff4d;
  font-weight: 600;
}

.status-value.online {
  animation: pulse 2s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .navigation-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
    padding: 0 30px 30px;
  }
}

@media (max-width: 768px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }
  
  .sub-title {
    font-size: 1.4rem;
  }
  
  .navigation-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px 20px;
  }
  
  .nav-card {
    padding: 25px;
  }
  
  .status-bar {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .time-display {
    position: static;
    text-align: center;
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }
  
  .sub-title {
    font-size: 1.2rem;
  }
  
  .main-header {
    padding: 60px 15px 40px;
  }
  
  .navigation-grid {
    padding: 0 15px 15px;
  }
  
  .nav-card {
    padding: 20px;
  }
} 