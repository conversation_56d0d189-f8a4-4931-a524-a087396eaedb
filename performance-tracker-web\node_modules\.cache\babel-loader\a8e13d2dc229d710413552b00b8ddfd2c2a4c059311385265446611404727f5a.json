{"ast": null, "code": "// 国际化语言资源文件\n\n// 语言资源定义\nconst translations = {\n  'zh-CN': {\n    // 个人设置页面\n    personalSettings: '个人设置',\n    personalSettingsDesc: '自定义您的使用体验',\n    // 导航菜单\n    profile: '个人信息',\n    security: '安全设置',\n    preferences: '偏好设置',\n    // 个人信息\n    profileInfo: '个人信息',\n    profileInfoDesc: '管理您的基本信息',\n    username: '用户名',\n    email: '邮箱',\n    role: '角色',\n    status: '状态',\n    lastLogin: '最后登录',\n    createdAt: '创建时间',\n    active: '活跃',\n    inactive: '非活跃',\n    // 角色名称\n    super_admin: '超级管理员',\n    manager: '管理员',\n    director: '部长',\n    user: '普通用户',\n    // 安全设置\n    securitySettings: '安全设置',\n    securitySettingsDesc: '保护您的账户安全',\n    currentPassword: '当前密码',\n    newPassword: '新密码',\n    confirmPassword: '确认密码',\n    changePassword: '修改密码',\n    // 偏好设置\n    preferencesSettings: '偏好设置',\n    preferencesSettingsDesc: '自定义您的使用体验',\n    interfaceSettings: '界面设置',\n    themeMode: '主题模式',\n    themeDesc: '选择您喜欢的界面主题',\n    languageSettings: '语言设置',\n    languageDesc: '选择界面显示语言',\n    notificationSettings: '通知设置',\n    emailNotifications: '邮件通知',\n    emailNotificationsDesc: '接收重要更新和系统通知',\n    desktopNotifications: '桌面通知',\n    desktopNotificationsDesc: '在桌面显示系统通知',\n    // 主题选项\n    darkMode: '深色模式',\n    lightMode: '浅色模式',\n    autoMode: '跟随系统',\n    // 语言选项\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    // 按钮\n    save: '保存',\n    cancel: '取消',\n    update: '更新',\n    back: '返回',\n    // 消息提示\n    saveSuccess: '设置已保存',\n    updateSuccess: '信息更新成功',\n    passwordChangeSuccess: '密码修改成功',\n    saveError: '保存失败',\n    updateError: '更新失败',\n    passwordChangeError: '密码修改失败',\n    // 验证消息\n    usernameRequired: '用户名不能为空',\n    emailRequired: '邮箱不能为空',\n    emailInvalid: '邮箱格式不正确',\n    passwordRequired: '密码不能为空',\n    passwordTooShort: '密码长度至少6位',\n    passwordMismatch: '两次输入的密码不一致',\n    currentPasswordRequired: '请输入当前密码'\n  },\n  'en-US': {\n    // Personal Settings Page\n    personalSettings: 'Personal Settings',\n    personalSettingsDesc: 'Customize your experience',\n    // Navigation Menu\n    profile: 'Profile',\n    security: 'Security',\n    preferences: 'Preferences',\n    // Profile Information\n    profileInfo: 'Profile Information',\n    profileInfoDesc: 'Manage your basic information',\n    username: 'Username',\n    email: 'Email',\n    role: 'Role',\n    status: 'Status',\n    lastLogin: 'Last Login',\n    createdAt: 'Created At',\n    active: 'Active',\n    inactive: 'Inactive',\n    // Role Names\n    super_admin: 'Super Admin',\n    manager: 'Manager',\n    director: 'Director',\n    user: 'User',\n    // Security Settings\n    securitySettings: 'Security Settings',\n    securitySettingsDesc: 'Protect your account security',\n    currentPassword: 'Current Password',\n    newPassword: 'New Password',\n    confirmPassword: 'Confirm Password',\n    changePassword: 'Change Password',\n    // Preferences Settings\n    preferencesSettings: 'Preferences',\n    preferencesSettingsDesc: 'Customize your experience',\n    interfaceSettings: 'Interface Settings',\n    themeMode: 'Theme Mode',\n    themeDesc: 'Choose your preferred interface theme',\n    languageSettings: 'Language Settings',\n    languageDesc: 'Select interface display language',\n    notificationSettings: 'Notification Settings',\n    emailNotifications: 'Email Notifications',\n    emailNotificationsDesc: 'Receive important updates and system notifications',\n    desktopNotifications: 'Desktop Notifications',\n    desktopNotificationsDesc: 'Show system notifications on desktop',\n    // Theme Options\n    darkMode: 'Dark Mode',\n    lightMode: 'Light Mode',\n    autoMode: 'Follow System',\n    // Language Options\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    // Buttons\n    save: 'Save',\n    cancel: 'Cancel',\n    update: 'Update',\n    back: 'Back',\n    // Messages\n    saveSuccess: 'Settings saved',\n    updateSuccess: 'Information updated successfully',\n    passwordChangeSuccess: 'Password changed successfully',\n    saveError: 'Save failed',\n    updateError: 'Update failed',\n    passwordChangeError: 'Password change failed',\n    // Validation Messages\n    usernameRequired: 'Username is required',\n    emailRequired: 'Email is required',\n    emailInvalid: 'Invalid email format',\n    passwordRequired: 'Password is required',\n    passwordTooShort: 'Password must be at least 6 characters',\n    passwordMismatch: 'Passwords do not match',\n    currentPasswordRequired: 'Please enter current password'\n  }\n};\n\n// 当前语言状态\nlet currentLanguage = 'zh-CN';\n\n// 语言切换函数\nexport const setLanguage = language => {\n  if (translations[language]) {\n    currentLanguage = language;\n    // 保存到本地存储\n    localStorage.setItem('userLanguage', language);\n    // 设置HTML lang属性\n    document.documentElement.lang = language;\n    return true;\n  }\n  return false;\n};\n\n// 获取当前语言\nexport const getCurrentLanguage = () => {\n  return currentLanguage;\n};\n\n// 翻译函数\nexport const t = key => {\n  const keys = key.split('.');\n  let value = translations[currentLanguage];\n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      // 如果找不到翻译，返回key本身\n      return key;\n    }\n  }\n  return value || key;\n};\n\n// 初始化语言设置\nexport const initLanguage = () => {\n  // 从本地存储获取保存的语言设置\n  const savedLanguage = localStorage.getItem('userLanguage');\n  if (savedLanguage && translations[savedLanguage]) {\n    setLanguage(savedLanguage);\n  } else {\n    // 默认使用中文\n    setLanguage('zh-CN');\n  }\n};\n\n// 获取所有可用语言\nexport const getAvailableLanguages = () => {\n  return Object.keys(translations);\n};\n\n// 导出默认对象\nexport default {\n  setLanguage,\n  getCurrentLanguage,\n  t,\n  initLanguage,\n  getAvailableLanguages\n};", "map": {"version": 3, "names": ["translations", "personalSettings", "personalSettingsDesc", "profile", "security", "preferences", "profileInfo", "profileInfoDesc", "username", "email", "role", "status", "lastLogin", "createdAt", "active", "inactive", "super_admin", "manager", "director", "user", "securitySettings", "securitySettingsDesc", "currentPassword", "newPassword", "confirmPassword", "changePassword", "preferencesSettings", "preferencesSettingsDesc", "interfaceSettings", "themeMode", "themeDesc", "languageSettings", "languageDesc", "notificationSettings", "emailNotifications", "emailNotificationsDesc", "desktopNotifications", "desktopNotificationsDesc", "darkMode", "lightMode", "autoMode", "simplifiedChinese", "english", "save", "cancel", "update", "back", "saveSuccess", "updateSuccess", "passwordChangeSuccess", "saveError", "updateError", "passwordChangeError", "usernameRequired", "emailRequired", "emailInvalid", "passwordRequired", "passwordTooShort", "passwordMismatch", "currentPasswordRequired", "currentLanguage", "setLanguage", "language", "localStorage", "setItem", "document", "documentElement", "lang", "getCurrentLanguage", "t", "key", "keys", "split", "value", "k", "initLanguage", "savedLanguage", "getItem", "getAvailableLanguages", "Object"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/utils/i18n.js"], "sourcesContent": ["// 国际化语言资源文件\n\n// 语言资源定义\nconst translations = {\n  'zh-CN': {\n    // 个人设置页面\n    personalSettings: '个人设置',\n    personalSettingsDesc: '自定义您的使用体验',\n    \n    // 导航菜单\n    profile: '个人信息',\n    security: '安全设置',\n    preferences: '偏好设置',\n    \n    // 个人信息\n    profileInfo: '个人信息',\n    profileInfoDesc: '管理您的基本信息',\n    username: '用户名',\n    email: '邮箱',\n    role: '角色',\n    status: '状态',\n    lastLogin: '最后登录',\n    createdAt: '创建时间',\n    active: '活跃',\n    inactive: '非活跃',\n    \n    // 角色名称\n    super_admin: '超级管理员',\n    manager: '管理员',\n    director: '部长',\n    user: '普通用户',\n    \n    // 安全设置\n    securitySettings: '安全设置',\n    securitySettingsDesc: '保护您的账户安全',\n    currentPassword: '当前密码',\n    newPassword: '新密码',\n    confirmPassword: '确认密码',\n    changePassword: '修改密码',\n    \n    // 偏好设置\n    preferencesSettings: '偏好设置',\n    preferencesSettingsDesc: '自定义您的使用体验',\n    interfaceSettings: '界面设置',\n    themeMode: '主题模式',\n    themeDesc: '选择您喜欢的界面主题',\n    languageSettings: '语言设置',\n    languageDesc: '选择界面显示语言',\n    notificationSettings: '通知设置',\n    emailNotifications: '邮件通知',\n    emailNotificationsDesc: '接收重要更新和系统通知',\n    desktopNotifications: '桌面通知',\n    desktopNotificationsDesc: '在桌面显示系统通知',\n    \n    // 主题选项\n    darkMode: '深色模式',\n    lightMode: '浅色模式',\n    autoMode: '跟随系统',\n    \n    // 语言选项\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    \n    // 按钮\n    save: '保存',\n    cancel: '取消',\n    update: '更新',\n    back: '返回',\n    \n    // 消息提示\n    saveSuccess: '设置已保存',\n    updateSuccess: '信息更新成功',\n    passwordChangeSuccess: '密码修改成功',\n    saveError: '保存失败',\n    updateError: '更新失败',\n    passwordChangeError: '密码修改失败',\n    \n    // 验证消息\n    usernameRequired: '用户名不能为空',\n    emailRequired: '邮箱不能为空',\n    emailInvalid: '邮箱格式不正确',\n    passwordRequired: '密码不能为空',\n    passwordTooShort: '密码长度至少6位',\n    passwordMismatch: '两次输入的密码不一致',\n    currentPasswordRequired: '请输入当前密码'\n  },\n  \n  'en-US': {\n    // Personal Settings Page\n    personalSettings: 'Personal Settings',\n    personalSettingsDesc: 'Customize your experience',\n    \n    // Navigation Menu\n    profile: 'Profile',\n    security: 'Security',\n    preferences: 'Preferences',\n    \n    // Profile Information\n    profileInfo: 'Profile Information',\n    profileInfoDesc: 'Manage your basic information',\n    username: 'Username',\n    email: 'Email',\n    role: 'Role',\n    status: 'Status',\n    lastLogin: 'Last Login',\n    createdAt: 'Created At',\n    active: 'Active',\n    inactive: 'Inactive',\n    \n    // Role Names\n    super_admin: 'Super Admin',\n    manager: 'Manager',\n    director: 'Director',\n    user: 'User',\n    \n    // Security Settings\n    securitySettings: 'Security Settings',\n    securitySettingsDesc: 'Protect your account security',\n    currentPassword: 'Current Password',\n    newPassword: 'New Password',\n    confirmPassword: 'Confirm Password',\n    changePassword: 'Change Password',\n    \n    // Preferences Settings\n    preferencesSettings: 'Preferences',\n    preferencesSettingsDesc: 'Customize your experience',\n    interfaceSettings: 'Interface Settings',\n    themeMode: 'Theme Mode',\n    themeDesc: 'Choose your preferred interface theme',\n    languageSettings: 'Language Settings',\n    languageDesc: 'Select interface display language',\n    notificationSettings: 'Notification Settings',\n    emailNotifications: 'Email Notifications',\n    emailNotificationsDesc: 'Receive important updates and system notifications',\n    desktopNotifications: 'Desktop Notifications',\n    desktopNotificationsDesc: 'Show system notifications on desktop',\n    \n    // Theme Options\n    darkMode: 'Dark Mode',\n    lightMode: 'Light Mode',\n    autoMode: 'Follow System',\n    \n    // Language Options\n    simplifiedChinese: '简体中文',\n    english: 'English',\n    \n    // Buttons\n    save: 'Save',\n    cancel: 'Cancel',\n    update: 'Update',\n    back: 'Back',\n    \n    // Messages\n    saveSuccess: 'Settings saved',\n    updateSuccess: 'Information updated successfully',\n    passwordChangeSuccess: 'Password changed successfully',\n    saveError: 'Save failed',\n    updateError: 'Update failed',\n    passwordChangeError: 'Password change failed',\n    \n    // Validation Messages\n    usernameRequired: 'Username is required',\n    emailRequired: 'Email is required',\n    emailInvalid: 'Invalid email format',\n    passwordRequired: 'Password is required',\n    passwordTooShort: 'Password must be at least 6 characters',\n    passwordMismatch: 'Passwords do not match',\n    currentPasswordRequired: 'Please enter current password'\n  }\n};\n\n// 当前语言状态\nlet currentLanguage = 'zh-CN';\n\n// 语言切换函数\nexport const setLanguage = (language) => {\n  if (translations[language]) {\n    currentLanguage = language;\n    // 保存到本地存储\n    localStorage.setItem('userLanguage', language);\n    // 设置HTML lang属性\n    document.documentElement.lang = language;\n    return true;\n  }\n  return false;\n};\n\n// 获取当前语言\nexport const getCurrentLanguage = () => {\n  return currentLanguage;\n};\n\n// 翻译函数\nexport const t = (key) => {\n  const keys = key.split('.');\n  let value = translations[currentLanguage];\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      // 如果找不到翻译，返回key本身\n      return key;\n    }\n  }\n  \n  return value || key;\n};\n\n// 初始化语言设置\nexport const initLanguage = () => {\n  // 从本地存储获取保存的语言设置\n  const savedLanguage = localStorage.getItem('userLanguage');\n  if (savedLanguage && translations[savedLanguage]) {\n    setLanguage(savedLanguage);\n  } else {\n    // 默认使用中文\n    setLanguage('zh-CN');\n  }\n};\n\n// 获取所有可用语言\nexport const getAvailableLanguages = () => {\n  return Object.keys(translations);\n};\n\n// 导出默认对象\nexport default {\n  setLanguage,\n  getCurrentLanguage,\n  t,\n  initLanguage,\n  getAvailableLanguages\n};\n"], "mappings": "AAAA;;AAEA;AACA,MAAMA,YAAY,GAAG;EACnB,OAAO,EAAE;IACP;IACAC,gBAAgB,EAAE,MAAM;IACxBC,oBAAoB,EAAE,WAAW;IAEjC;IACAC,OAAO,EAAE,MAAM;IACfC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE,MAAM;IAEnB;IACAC,WAAW,EAAE,MAAM;IACnBC,eAAe,EAAE,UAAU;IAC3BC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,KAAK;IAEf;IACAC,WAAW,EAAE,OAAO;IACpBC,OAAO,EAAE,KAAK;IACdC,QAAQ,EAAE,IAAI;IACdC,IAAI,EAAE,MAAM;IAEZ;IACAC,gBAAgB,EAAE,MAAM;IACxBC,oBAAoB,EAAE,UAAU;IAChCC,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE,MAAM;IACvBC,cAAc,EAAE,MAAM;IAEtB;IACAC,mBAAmB,EAAE,MAAM;IAC3BC,uBAAuB,EAAE,WAAW;IACpCC,iBAAiB,EAAE,MAAM;IACzBC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,YAAY;IACvBC,gBAAgB,EAAE,MAAM;IACxBC,YAAY,EAAE,UAAU;IACxBC,oBAAoB,EAAE,MAAM;IAC5BC,kBAAkB,EAAE,MAAM;IAC1BC,sBAAsB,EAAE,aAAa;IACrCC,oBAAoB,EAAE,MAAM;IAC5BC,wBAAwB,EAAE,WAAW;IAErC;IACAC,QAAQ,EAAE,MAAM;IAChBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAEhB;IACAC,iBAAiB,EAAE,MAAM;IACzBC,OAAO,EAAE,SAAS;IAElB;IACAC,IAAI,EAAE,IAAI;IACVC,MAAM,EAAE,IAAI;IACZC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IAEV;IACAC,WAAW,EAAE,OAAO;IACpBC,aAAa,EAAE,QAAQ;IACvBC,qBAAqB,EAAE,QAAQ;IAC/BC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,MAAM;IACnBC,mBAAmB,EAAE,QAAQ;IAE7B;IACAC,gBAAgB,EAAE,SAAS;IAC3BC,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE,SAAS;IACvBC,gBAAgB,EAAE,QAAQ;IAC1BC,gBAAgB,EAAE,UAAU;IAC5BC,gBAAgB,EAAE,YAAY;IAC9BC,uBAAuB,EAAE;EAC3B,CAAC;EAED,OAAO,EAAE;IACP;IACA1D,gBAAgB,EAAE,mBAAmB;IACrCC,oBAAoB,EAAE,2BAA2B;IAEjD;IACAC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,aAAa;IAE1B;IACAC,WAAW,EAAE,qBAAqB;IAClCC,eAAe,EAAE,+BAA+B;IAChDC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IAEpB;IACAC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,MAAM;IAEZ;IACAC,gBAAgB,EAAE,mBAAmB;IACrCC,oBAAoB,EAAE,+BAA+B;IACrDC,eAAe,EAAE,kBAAkB;IACnCC,WAAW,EAAE,cAAc;IAC3BC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE,iBAAiB;IAEjC;IACAC,mBAAmB,EAAE,aAAa;IAClCC,uBAAuB,EAAE,2BAA2B;IACpDC,iBAAiB,EAAE,oBAAoB;IACvCC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,uCAAuC;IAClDC,gBAAgB,EAAE,mBAAmB;IACrCC,YAAY,EAAE,mCAAmC;IACjDC,oBAAoB,EAAE,uBAAuB;IAC7CC,kBAAkB,EAAE,qBAAqB;IACzCC,sBAAsB,EAAE,oDAAoD;IAC5EC,oBAAoB,EAAE,uBAAuB;IAC7CC,wBAAwB,EAAE,sCAAsC;IAEhE;IACAC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE,YAAY;IACvBC,QAAQ,EAAE,eAAe;IAEzB;IACAC,iBAAiB,EAAE,MAAM;IACzBC,OAAO,EAAE,SAAS;IAElB;IACAC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IAEZ;IACAC,WAAW,EAAE,gBAAgB;IAC7BC,aAAa,EAAE,kCAAkC;IACjDC,qBAAqB,EAAE,+BAA+B;IACtDC,SAAS,EAAE,aAAa;IACxBC,WAAW,EAAE,eAAe;IAC5BC,mBAAmB,EAAE,wBAAwB;IAE7C;IACAC,gBAAgB,EAAE,sBAAsB;IACxCC,aAAa,EAAE,mBAAmB;IAClCC,YAAY,EAAE,sBAAsB;IACpCC,gBAAgB,EAAE,sBAAsB;IACxCC,gBAAgB,EAAE,wCAAwC;IAC1DC,gBAAgB,EAAE,wBAAwB;IAC1CC,uBAAuB,EAAE;EAC3B;AACF,CAAC;;AAED;AACA,IAAIC,eAAe,GAAG,OAAO;;AAE7B;AACA,OAAO,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EACvC,IAAI9D,YAAY,CAAC8D,QAAQ,CAAC,EAAE;IAC1BF,eAAe,GAAGE,QAAQ;IAC1B;IACAC,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEF,QAAQ,CAAC;IAC9C;IACAG,QAAQ,CAACC,eAAe,CAACC,IAAI,GAAGL,QAAQ;IACxC,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd,CAAC;;AAED;AACA,OAAO,MAAMM,kBAAkB,GAAGA,CAAA,KAAM;EACtC,OAAOR,eAAe;AACxB,CAAC;;AAED;AACA,OAAO,MAAMS,CAAC,GAAIC,GAAG,IAAK;EACxB,MAAMC,IAAI,GAAGD,GAAG,CAACE,KAAK,CAAC,GAAG,CAAC;EAC3B,IAAIC,KAAK,GAAGzE,YAAY,CAAC4D,eAAe,CAAC;EAEzC,KAAK,MAAMc,CAAC,IAAIH,IAAI,EAAE;IACpB,IAAIE,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIC,CAAC,IAAID,KAAK,EAAE;MACpDA,KAAK,GAAGA,KAAK,CAACC,CAAC,CAAC;IAClB,CAAC,MAAM;MACL;MACA,OAAOJ,GAAG;IACZ;EACF;EAEA,OAAOG,KAAK,IAAIH,GAAG;AACrB,CAAC;;AAED;AACA,OAAO,MAAMK,YAAY,GAAGA,CAAA,KAAM;EAChC;EACA,MAAMC,aAAa,GAAGb,YAAY,CAACc,OAAO,CAAC,cAAc,CAAC;EAC1D,IAAID,aAAa,IAAI5E,YAAY,CAAC4E,aAAa,CAAC,EAAE;IAChDf,WAAW,CAACe,aAAa,CAAC;EAC5B,CAAC,MAAM;IACL;IACAf,WAAW,CAAC,OAAO,CAAC;EACtB;AACF,CAAC;;AAED;AACA,OAAO,MAAMiB,qBAAqB,GAAGA,CAAA,KAAM;EACzC,OAAOC,MAAM,CAACR,IAAI,CAACvE,YAAY,CAAC;AAClC,CAAC;;AAED;AACA,eAAe;EACb6D,WAAW;EACXO,kBAAkB;EAClBC,CAAC;EACDM,YAAY;EACZG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}