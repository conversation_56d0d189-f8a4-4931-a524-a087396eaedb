{"ast": null, "code": "class WorldClassService{constructor(){this.baseURL='http://localhost:3001';this.data=[];this.lastModified=null;}// 设置同步回调（复用模块一和模块二的设计）\nsetSyncCallbacks(onSuccess,onError){this.onSyncSuccess=onSuccess;this.onSyncError=onError;}// 加载对标世界一流数据\nasync loadWorldClassData(){try{console.log('正在从API加载对标世界一流数据...');const response=await fetch(\"\".concat(this.baseURL,\"/api/world-class-data\"));if(response.ok){const result=await response.json();if(result.success&&result.data){this.data=result.data;this.lastModified=new Date(result.lastModified);console.log('从Excel文件成功加载数据:',this.data);return this.data;}else{console.warn('API返回的数据格式不正确');return this.getMockData();}}else{console.warn('API响应失败，状态码:',response.status);return this.getMockData();}}catch(error){console.error('从API加载对标世界一流数据失败:',error);return this.getMockData();}}// 获取模拟数据（按照提示词要求的数据结构）\ngetMockData(){console.log('使用模拟数据');this.data=[{序号:1,'相关指标或方向（一级）':'提升平台的效率或效益','相关指标或方向（二级）':'优化业务流程','相关指标或方向（三级）':'数字化转型','2025年目标':'完成业务流程数字化转型，提升效率30%','2025年举措':'实施ERP系统集成，建立数据分析平台，优化审批流程',负责人:'技术部',权重:15,跟踪频次:'月度','2月工作计划':'完成需求调研和系统设计','2月完成情况':'已完成需求调研，系统设计进行中','3月工作计划':'开发核心功能模块','3月完成情况':'核心模块开发完成80%','4月工作计划':'系统集成和测试','4月完成情况':'集成测试通过，用户验收中','5月工作计划':'用户培训和系统上线','5月完成情况':'','6月工作计划':'系统优化和性能调整','6月完成情况':'','7月工作计划':'数据迁移和验证','7月完成情况':'','8月工作计划':'全面推广应用','8月完成情况':'','9月工作计划':'效果评估和改进','9月完成情况':'','10月工作计划':'制定标准化流程','10月完成情况':'','11月工作计划':'培训推广和经验总结','11月完成情况':'','12月工作计划':'年度总结和下年规划','12月完成情况':'','1月工作计划':'新年度计划制定','1月完成情况':'',备注:'重点项目，需要跨部门协作'},{序号:2,'相关指标或方向（一级）':'提升平台的效率或效益','相关指标或方向（二级）':'技术创新','相关指标或方向（三级）':'智能化升级','2025年目标':'建立智能化生产管理系统，降低成本20%','2025年举措':'引入AI算法，建设智能工厂，实施预测性维护',负责人:'生产部',权重:20,跟踪频次:'月度','2月工作计划':'AI算法选型和测试','2月完成情况':'完成3种算法对比测试','3月工作计划':'智能工厂方案设计','3月完成情况':'方案设计完成并通过评审','4月工作计划':'设备采购和安装','4月完成情况':'','5月工作计划':'系统集成和调试','5月完成情况':'','6月工作计划':'试运行和优化','6月完成情况':'','7月工作计划':'正式投产运行','7月完成情况':'','8月工作计划':'效果监测和分析','8月完成情况':'','9月工作计划':'系统优化改进','9月完成情况':'','10月工作计划':'扩大应用范围','10月完成情况':'','11月工作计划':'培训和推广','11月完成情况':'','12月工作计划':'年度效果评估','12月完成情况':'','1月工作计划':'制定扩展计划','1月完成情况':'',备注:'核心技术项目'},{序号:3,'相关指标或方向（一级）':'新产品、新平台、新技术开发','相关指标或方向（二级）':'产品创新','相关指标或方向（三级）':'新材料研发','2025年目标':'开发3款新材料产品，申请5项专利','2025年举措':'建立新材料实验室，与高校合作研发，建立产业化生产线',负责人:'研发中心',权重:25,跟踪频次:'季度','2月工作计划':'实验室建设和设备采购','2月完成情况':'实验室建设完成60%','3月工作计划':'与高校签署合作协议','3月完成情况':'已与3所高校签署合作协议','4月工作计划':'第一款新材料研发','4月完成情况':'','5月工作计划':'产品性能测试','5月完成情况':'','6月工作计划':'第二款新材料研发','6月完成情况':'','7月工作计划':'专利申请准备','7月完成情况':'','8月工作计划':'第三款新材料研发','8月完成情况':'','9月工作计划':'产业化可行性分析','9月完成情况':'','10月工作计划':'生产线设计','10月完成情况':'','11月工作计划':'设备调试和试产','11月完成情况':'','12月工作计划':'产品验证和改进','12月完成情况':'','1月工作计划':'市场推广准备','1月完成情况':'',备注:'创新驱动发展项目'},{序号:4,'相关指标或方向（一级）':'新产品、新平台、新技术开发','相关指标或方向（二级）':'平台建设','相关指标或方向（三级）':'数据平台','2025年目标':'建设统一数据平台，实现数据共享和分析','2025年举措':'搭建大数据平台，建立数据标准，实施数据治理',负责人:'信息中心',权重:18,跟踪频次:'月度','2月工作计划':'数据平台架构设计','2月完成情况':'架构设计完成并通过技术评审','3月工作计划':'基础设施建设','3月完成情况':'服务器采购安装完成','4月工作计划':'数据接入和清洗','4月完成情况':'','5月工作计划':'数据分析工具开发','5月完成情况':'','6月工作计划':'用户界面设计','6月完成情况':'','7月工作计划':'系统测试和优化','7月完成情况':'','8月工作计划':'用户培训和推广','8月完成情况':'','9月工作计划':'数据治理规范制定','9月完成情况':'','10月工作计划':'平台功能扩展','10月完成情况':'','11月工作计划':'数据安全加固','11月完成情况':'','12月工作计划':'年度数据分析报告','12月完成情况':'','1月工作计划':'平台升级规划','1月完成情况':'',备注:'基础支撑平台'},{序号:5,'相关指标或方向（一级）':'质量管理体系优化','相关指标或方向（二级）':'质量标准','相关指标或方向（三级）':'ISO认证','2025年目标':'通过ISO 9001:2015质量管理体系认证','2025年举措':'建立质量管理体系，培训质量管理人员，实施质量改进',负责人:'质量部',权重:12,跟踪频次:'季度','2月工作计划':'质量体系文件编制','2月完成情况':'完成质量手册和程序文件编制','3月工作计划':'质量管理培训','3月完成情况':'培训质量管理人员50人次','4月工作计划':'质量体系试运行','4月完成情况':'','5月工作计划':'内部质量审核','5月完成情况':'','6月工作计划':'管理评审','6月完成情况':'','7月工作计划':'纠正预防措施实施','7月完成情况':'','8月工作计划':'认证机构审核准备','8月完成情况':'','9月工作计划':'第三方审核','9月完成情况':'','10月工作计划':'不符合项整改','10月完成情况':'','11月工作计划':'认证证书获取','11月完成情况':'','12月工作计划':'质量体系持续改进','12月完成情况':'','1月工作计划':'年度质量目标制定','1月完成情况':'',备注:'质量保障基础工作'}];return this.data;}// 更新数据（双向同步到Excel）\nasync updateData(rowIndex,field,value){try{console.log('正在同步对标世界一流数据到Excel:',{rowIndex,field,value});// 更新内存中的数据\nif(this.data[rowIndex]){this.data[rowIndex][field]=value;}// 调用API同步到Excel\nconst response=await fetch(\"\".concat(this.baseURL,\"/api/update-world-class-excel\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({rowIndex,field,value})});if(response.ok){const result=await response.json();if(result.success){console.log('对标世界一流数据同步成功');this.onSyncSuccess&&this.onSyncSuccess();return true;}else{console.error('对标世界一流数据同步失败:',result.message);this.onSyncError&&this.onSyncError(result.message);return false;}}else{console.error('对标世界一流数据同步请求失败，状态码:',response.status);this.onSyncError&&this.onSyncError(\"HTTP \".concat(response.status));return false;}}catch(error){console.error('对标世界一流数据同步错误:',error);this.onSyncError&&this.onSyncError(error.message);return false;}}// 获取层级选项\ngetLevelOptions(level){if(!this.data||this.data.length===0)return[];const field=level==='一级'?'相关指标或方向（一级）':level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';const options=[...new Set(this.data.map(item=>item[field]).filter(val=>val&&String(val).trim()!=='').map(val=>String(val)))];return options;}// 按层级筛选数据\nfilterByLevel(level,value){if(!this.data||this.data.length===0)return[];if(value==='全部')return this.data;const field=level==='一级'?'相关指标或方向（一级）':level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';return this.data.filter(item=>String(item[field]||'').trim()===value);}// 获取月份字段\ngetMonthFields(){const months=['2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月','1月'];const monthFields=[];months.forEach(month=>{monthFields.push(\"\".concat(month,\"\\u5DE5\\u4F5C\\u8BA1\\u5212\"));monthFields.push(\"\".concat(month,\"\\u5B8C\\u6210\\u60C5\\u51B5\"));});return monthFields;}// 数据统计\ngetDataStats(){if(!this.data||this.data.length===0){return{totalItems:0,levelCounts:{一级:0,二级:0,三级:0},totalWeight:0,avgWeight:0};}const levelCounts={一级:0,二级:0,三级:0};let totalWeight=0;this.data.forEach(item=>{// 统计各层级数据量\nif(item['相关指标或方向（一级）'])levelCounts.一级++;if(item['相关指标或方向（二级）'])levelCounts.二级++;if(item['相关指标或方向（三级）'])levelCounts.三级++;// 统计权重\nconst weight=item.权重;if(weight&&!isNaN(Number(weight))){totalWeight+=Number(weight);}});return{totalItems:this.data.length,levelCounts,totalWeight,avgWeight:this.data.length>0?(totalWeight/this.data.length).toFixed(1):0};}// 数据验证（提示词要求的数据验证机制）\nasync validateData(){try{const response=await fetch(\"\".concat(this.baseURL,\"/api/validate-world-class-data\"));if(response.ok){const result=await response.json();return result;}else{return{success:false,message:'数据验证失败'};}}catch(error){console.error('数据验证错误:',error);return{success:false,message:error.message};}}// 导出数据\nasync exportData(){let options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{const response=await fetch(\"\".concat(this.baseURL,\"/api/export-world-class-data\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(options)});if(response.ok){return response.blob();}else{throw new Error('导出数据失败');}}catch(error){console.error('导出数据错误:',error);throw error;}}// 获取数据\ngetData(){return this.data;}// 检查数据是否已加载\nisDataLoaded(){return this.data&&this.data.length>0;}// 获取最后修改时间\ngetLastModified(){return this.lastModified;}}export default new WorldClassService();", "map": {"version": 3, "names": ["WorldClassService", "constructor", "baseURL", "data", "lastModified", "setSyncCallbacks", "onSuccess", "onError", "onSyncSuccess", "onSyncError", "loadWorldClassData", "console", "log", "response", "fetch", "concat", "ok", "result", "json", "success", "Date", "warn", "getMockData", "status", "error", "序号", "负责人", "权重", "跟踪频次", "备注", "updateData", "rowIndex", "field", "value", "method", "headers", "body", "JSON", "stringify", "message", "getLevelOptions", "level", "length", "options", "Set", "map", "item", "filter", "val", "String", "trim", "filterByLevel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "months", "monthFields", "for<PERSON>ach", "month", "push", "getDataStats", "totalItems", "levelCounts", "一级", "二级", "三级", "totalWeight", "avgWeight", "weight", "isNaN", "Number", "toFixed", "validateData", "exportData", "arguments", "undefined", "blob", "Error", "getData", "isDataLoaded", "getLastModified"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块三/services/worldClassService.js"], "sourcesContent": ["class WorldClassService {\n  constructor() {\n    this.baseURL = 'http://localhost:3001';\n    this.data = [];\n    this.lastModified = null;\n  }\n\n  // 设置同步回调（复用模块一和模块二的设计）\n  setSyncCallbacks(onSuccess, onError) {\n    this.onSyncSuccess = onSuccess;\n    this.onSyncError = onError;\n  }\n\n  // 加载对标世界一流数据\n  async loadWorldClassData() {\n    try {\n      console.log('正在从API加载对标世界一流数据...');\n      const response = await fetch(`${this.baseURL}/api/world-class-data`);\n      \n      if (response.ok) {\n        const result = await response.json();\n        if (result.success && result.data) {\n          this.data = result.data;\n          this.lastModified = new Date(result.lastModified);\n          console.log('从Excel文件成功加载数据:', this.data);\n          return this.data;\n        } else {\n          console.warn('API返回的数据格式不正确');\n          return this.getMockData();\n        }\n      } else {\n        console.warn('API响应失败，状态码:', response.status);\n        return this.getMockData();\n      }\n    } catch (error) {\n      console.error('从API加载对标世界一流数据失败:', error);\n      return this.getMockData();\n    }\n  }\n\n  // 获取模拟数据（按照提示词要求的数据结构）\n  getMockData() {\n    console.log('使用模拟数据');\n    this.data = [\n      {\n        序号: 1,\n        '相关指标或方向（一级）': '提升平台的效率或效益',\n        '相关指标或方向（二级）': '优化业务流程',\n        '相关指标或方向（三级）': '数字化转型',\n        '2025年目标': '完成业务流程数字化转型，提升效率30%',\n        '2025年举措': '实施ERP系统集成，建立数据分析平台，优化审批流程',\n        负责人: '技术部',\n        权重: 15,\n        跟踪频次: '月度',\n        '2月工作计划': '完成需求调研和系统设计',\n        '2月完成情况': '已完成需求调研，系统设计进行中',\n        '3月工作计划': '开发核心功能模块',\n        '3月完成情况': '核心模块开发完成80%',\n        '4月工作计划': '系统集成和测试',\n        '4月完成情况': '集成测试通过，用户验收中',\n        '5月工作计划': '用户培训和系统上线',\n        '5月完成情况': '',\n        '6月工作计划': '系统优化和性能调整',\n        '6月完成情况': '',\n        '7月工作计划': '数据迁移和验证',\n        '7月完成情况': '',\n        '8月工作计划': '全面推广应用',\n        '8月完成情况': '',\n        '9月工作计划': '效果评估和改进',\n        '9月完成情况': '',\n        '10月工作计划': '制定标准化流程',\n        '10月完成情况': '',\n        '11月工作计划': '培训推广和经验总结',\n        '11月完成情况': '',\n        '12月工作计划': '年度总结和下年规划',\n        '12月完成情况': '',\n        '1月工作计划': '新年度计划制定',\n        '1月完成情况': '',\n        备注: '重点项目，需要跨部门协作'\n      },\n      {\n        序号: 2,\n        '相关指标或方向（一级）': '提升平台的效率或效益',\n        '相关指标或方向（二级）': '技术创新',\n        '相关指标或方向（三级）': '智能化升级',\n        '2025年目标': '建立智能化生产管理系统，降低成本20%',\n        '2025年举措': '引入AI算法，建设智能工厂，实施预测性维护',\n        负责人: '生产部',\n        权重: 20,\n        跟踪频次: '月度',\n        '2月工作计划': 'AI算法选型和测试',\n        '2月完成情况': '完成3种算法对比测试',\n        '3月工作计划': '智能工厂方案设计',\n        '3月完成情况': '方案设计完成并通过评审',\n        '4月工作计划': '设备采购和安装',\n        '4月完成情况': '',\n        '5月工作计划': '系统集成和调试',\n        '5月完成情况': '',\n        '6月工作计划': '试运行和优化',\n        '6月完成情况': '',\n        '7月工作计划': '正式投产运行',\n        '7月完成情况': '',\n        '8月工作计划': '效果监测和分析',\n        '8月完成情况': '',\n        '9月工作计划': '系统优化改进',\n        '9月完成情况': '',\n        '10月工作计划': '扩大应用范围',\n        '10月完成情况': '',\n        '11月工作计划': '培训和推广',\n        '11月完成情况': '',\n        '12月工作计划': '年度效果评估',\n        '12月完成情况': '',\n        '1月工作计划': '制定扩展计划',\n        '1月完成情况': '',\n        备注: '核心技术项目'\n      },\n      {\n        序号: 3,\n        '相关指标或方向（一级）': '新产品、新平台、新技术开发',\n        '相关指标或方向（二级）': '产品创新',\n        '相关指标或方向（三级）': '新材料研发',\n        '2025年目标': '开发3款新材料产品，申请5项专利',\n        '2025年举措': '建立新材料实验室，与高校合作研发，建立产业化生产线',\n        负责人: '研发中心',\n        权重: 25,\n        跟踪频次: '季度',\n        '2月工作计划': '实验室建设和设备采购',\n        '2月完成情况': '实验室建设完成60%',\n        '3月工作计划': '与高校签署合作协议',\n        '3月完成情况': '已与3所高校签署合作协议',\n        '4月工作计划': '第一款新材料研发',\n        '4月完成情况': '',\n        '5月工作计划': '产品性能测试',\n        '5月完成情况': '',\n        '6月工作计划': '第二款新材料研发',\n        '6月完成情况': '',\n        '7月工作计划': '专利申请准备',\n        '7月完成情况': '',\n        '8月工作计划': '第三款新材料研发',\n        '8月完成情况': '',\n        '9月工作计划': '产业化可行性分析',\n        '9月完成情况': '',\n        '10月工作计划': '生产线设计',\n        '10月完成情况': '',\n        '11月工作计划': '设备调试和试产',\n        '11月完成情况': '',\n        '12月工作计划': '产品验证和改进',\n        '12月完成情况': '',\n        '1月工作计划': '市场推广准备',\n        '1月完成情况': '',\n        备注: '创新驱动发展项目'\n      },\n      {\n        序号: 4,\n        '相关指标或方向（一级）': '新产品、新平台、新技术开发',\n        '相关指标或方向（二级）': '平台建设',\n        '相关指标或方向（三级）': '数据平台',\n        '2025年目标': '建设统一数据平台，实现数据共享和分析',\n        '2025年举措': '搭建大数据平台，建立数据标准，实施数据治理',\n        负责人: '信息中心',\n        权重: 18,\n        跟踪频次: '月度',\n        '2月工作计划': '数据平台架构设计',\n        '2月完成情况': '架构设计完成并通过技术评审',\n        '3月工作计划': '基础设施建设',\n        '3月完成情况': '服务器采购安装完成',\n        '4月工作计划': '数据接入和清洗',\n        '4月完成情况': '',\n        '5月工作计划': '数据分析工具开发',\n        '5月完成情况': '',\n        '6月工作计划': '用户界面设计',\n        '6月完成情况': '',\n        '7月工作计划': '系统测试和优化',\n        '7月完成情况': '',\n        '8月工作计划': '用户培训和推广',\n        '8月完成情况': '',\n        '9月工作计划': '数据治理规范制定',\n        '9月完成情况': '',\n        '10月工作计划': '平台功能扩展',\n        '10月完成情况': '',\n        '11月工作计划': '数据安全加固',\n        '11月完成情况': '',\n        '12月工作计划': '年度数据分析报告',\n        '12月完成情况': '',\n        '1月工作计划': '平台升级规划',\n        '1月完成情况': '',\n        备注: '基础支撑平台'\n      },\n      {\n        序号: 5,\n        '相关指标或方向（一级）': '质量管理体系优化',\n        '相关指标或方向（二级）': '质量标准',\n        '相关指标或方向（三级）': 'ISO认证',\n        '2025年目标': '通过ISO 9001:2015质量管理体系认证',\n        '2025年举措': '建立质量管理体系，培训质量管理人员，实施质量改进',\n        负责人: '质量部',\n        权重: 12,\n        跟踪频次: '季度',\n        '2月工作计划': '质量体系文件编制',\n        '2月完成情况': '完成质量手册和程序文件编制',\n        '3月工作计划': '质量管理培训',\n        '3月完成情况': '培训质量管理人员50人次',\n        '4月工作计划': '质量体系试运行',\n        '4月完成情况': '',\n        '5月工作计划': '内部质量审核',\n        '5月完成情况': '',\n        '6月工作计划': '管理评审',\n        '6月完成情况': '',\n        '7月工作计划': '纠正预防措施实施',\n        '7月完成情况': '',\n        '8月工作计划': '认证机构审核准备',\n        '8月完成情况': '',\n        '9月工作计划': '第三方审核',\n        '9月完成情况': '',\n        '10月工作计划': '不符合项整改',\n        '10月完成情况': '',\n        '11月工作计划': '认证证书获取',\n        '11月完成情况': '',\n        '12月工作计划': '质量体系持续改进',\n        '12月完成情况': '',\n        '1月工作计划': '年度质量目标制定',\n        '1月完成情况': '',\n        备注: '质量保障基础工作'\n      }\n    ];\n    \n    return this.data;\n  }\n\n  // 更新数据（双向同步到Excel）\n  async updateData(rowIndex, field, value) {\n    try {\n      console.log('正在同步对标世界一流数据到Excel:', { rowIndex, field, value });\n      \n      // 更新内存中的数据\n      if (this.data[rowIndex]) {\n        this.data[rowIndex][field] = value;\n      }\n      \n      // 调用API同步到Excel\n      const response = await fetch(`${this.baseURL}/api/update-world-class-excel`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          rowIndex,\n          field,\n          value\n        })\n      });\n\n      if (response.ok) {\n        const result = await response.json();\n        if (result.success) {\n          console.log('对标世界一流数据同步成功');\n          this.onSyncSuccess && this.onSyncSuccess();\n          return true;\n        } else {\n          console.error('对标世界一流数据同步失败:', result.message);\n          this.onSyncError && this.onSyncError(result.message);\n          return false;\n        }\n      } else {\n        console.error('对标世界一流数据同步请求失败，状态码:', response.status);\n        this.onSyncError && this.onSyncError(`HTTP ${response.status}`);\n        return false;\n      }\n    } catch (error) {\n      console.error('对标世界一流数据同步错误:', error);\n      this.onSyncError && this.onSyncError(error.message);\n      return false;\n    }\n  }\n\n  // 获取层级选项\n  getLevelOptions(level) {\n    if (!this.data || this.data.length === 0) return [];\n    \n    const field = level === '一级' ? '相关指标或方向（一级）' : \n                  level === '二级' ? '相关指标或方向（二级）' : \n                  '相关指标或方向（三级）';\n    \n    const options = [...new Set(this.data\n      .map(item => item[field])\n      .filter(val => val && String(val).trim() !== '')\n      .map(val => String(val))\n    )];\n    \n    return options;\n  }\n\n  // 按层级筛选数据\n  filterByLevel(level, value) {\n    if (!this.data || this.data.length === 0) return [];\n    \n    if (value === '全部') return this.data;\n    \n    const field = level === '一级' ? '相关指标或方向（一级）' : \n                  level === '二级' ? '相关指标或方向（二级）' : \n                  '相关指标或方向（三级）';\n\n    return this.data.filter(item => \n      String(item[field] || '').trim() === value\n    );\n  }\n\n  // 获取月份字段\n  getMonthFields() {\n    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月', '1月'];\n    const monthFields = [];\n    \n    months.forEach(month => {\n      monthFields.push(`${month}工作计划`);\n      monthFields.push(`${month}完成情况`);\n    });\n    \n    return monthFields;\n  }\n\n  // 数据统计\n  getDataStats() {\n    if (!this.data || this.data.length === 0) {\n      return {\n        totalItems: 0,\n        levelCounts: { 一级: 0, 二级: 0, 三级: 0 },\n        totalWeight: 0,\n        avgWeight: 0\n      };\n    }\n\n    const levelCounts = { 一级: 0, 二级: 0, 三级: 0 };\n    let totalWeight = 0;\n    \n    this.data.forEach(item => {\n      // 统计各层级数据量\n      if (item['相关指标或方向（一级）']) levelCounts.一级++;\n      if (item['相关指标或方向（二级）']) levelCounts.二级++;\n      if (item['相关指标或方向（三级）']) levelCounts.三级++;\n      \n      // 统计权重\n      const weight = item.权重;\n      if (weight && !isNaN(Number(weight))) {\n        totalWeight += Number(weight);\n      }\n    });\n\n    return {\n      totalItems: this.data.length,\n      levelCounts,\n      totalWeight,\n      avgWeight: this.data.length > 0 ? (totalWeight / this.data.length).toFixed(1) : 0\n    };\n  }\n\n  // 数据验证（提示词要求的数据验证机制）\n  async validateData() {\n    try {\n      const response = await fetch(`${this.baseURL}/api/validate-world-class-data`);\n      \n      if (response.ok) {\n        const result = await response.json();\n        return result;\n      } else {\n        return { success: false, message: '数据验证失败' };\n      }\n    } catch (error) {\n      console.error('数据验证错误:', error);\n      return { success: false, message: error.message };\n    }\n  }\n\n  // 导出数据\n  async exportData(options = {}) {\n    try {\n      const response = await fetch(`${this.baseURL}/api/export-world-class-data`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(options)\n      });\n\n      if (response.ok) {\n        return response.blob();\n      } else {\n        throw new Error('导出数据失败');\n      }\n    } catch (error) {\n      console.error('导出数据错误:', error);\n      throw error;\n    }\n  }\n\n  // 获取数据\n  getData() {\n    return this.data;\n  }\n\n  // 检查数据是否已加载\n  isDataLoaded() {\n    return this.data && this.data.length > 0;\n  }\n\n  // 获取最后修改时间\n  getLastModified() {\n    return this.lastModified;\n  }\n}\n\nexport default new WorldClassService(); "], "mappings": "AAAA,KAAM,CAAAA,iBAAkB,CACtBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAG,uBAAuB,CACtC,IAAI,CAACC,IAAI,CAAG,EAAE,CACd,IAAI,CAACC,YAAY,CAAG,IAAI,CAC1B,CAEA;AACAC,gBAAgBA,CAACC,SAAS,CAAEC,OAAO,CAAE,CACnC,IAAI,CAACC,aAAa,CAAGF,SAAS,CAC9B,IAAI,CAACG,WAAW,CAAGF,OAAO,CAC5B,CAEA;AACA,KAAM,CAAAG,kBAAkBA,CAAA,CAAG,CACzB,GAAI,CACFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC,CAClC,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAACb,OAAO,yBAAuB,CAAC,CAEpE,GAAIW,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACE,OAAO,EAAIF,MAAM,CAACd,IAAI,CAAE,CACjC,IAAI,CAACA,IAAI,CAAGc,MAAM,CAACd,IAAI,CACvB,IAAI,CAACC,YAAY,CAAG,GAAI,CAAAgB,IAAI,CAACH,MAAM,CAACb,YAAY,CAAC,CACjDO,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE,IAAI,CAACT,IAAI,CAAC,CACzC,MAAO,KAAI,CAACA,IAAI,CAClB,CAAC,IAAM,CACLQ,OAAO,CAACU,IAAI,CAAC,eAAe,CAAC,CAC7B,MAAO,KAAI,CAACC,WAAW,CAAC,CAAC,CAC3B,CACF,CAAC,IAAM,CACLX,OAAO,CAACU,IAAI,CAAC,cAAc,CAAER,QAAQ,CAACU,MAAM,CAAC,CAC7C,MAAO,KAAI,CAACD,WAAW,CAAC,CAAC,CAC3B,CACF,CAAE,MAAOE,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,mBAAmB,CAAEA,KAAK,CAAC,CACzC,MAAO,KAAI,CAACF,WAAW,CAAC,CAAC,CAC3B,CACF,CAEA;AACAA,WAAWA,CAAA,CAAG,CACZX,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC,CACrB,IAAI,CAACT,IAAI,CAAG,CACV,CACEsB,EAAE,CAAE,CAAC,CACL,aAAa,CAAE,YAAY,CAC3B,aAAa,CAAE,QAAQ,CACvB,aAAa,CAAE,OAAO,CACtB,SAAS,CAAE,qBAAqB,CAChC,SAAS,CAAE,2BAA2B,CACtCC,GAAG,CAAE,KAAK,CACVC,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,iBAAiB,CAC3B,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,SAAS,CAAE,SAAS,CACpB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,WAAW,CACtB,SAAS,CAAE,EAAE,CACb,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZC,EAAE,CAAE,cACN,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACL,aAAa,CAAE,YAAY,CAC3B,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,OAAO,CACtB,SAAS,CAAE,qBAAqB,CAChC,SAAS,CAAE,uBAAuB,CAClCC,GAAG,CAAE,KAAK,CACVC,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,aAAa,CACvB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZC,EAAE,CAAE,QACN,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACL,aAAa,CAAE,eAAe,CAC9B,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,OAAO,CACtB,SAAS,CAAE,kBAAkB,CAC7B,SAAS,CAAE,2BAA2B,CACtCC,GAAG,CAAE,MAAM,CACXC,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,YAAY,CACtB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,SAAS,CAAE,OAAO,CAClB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,SAAS,CACpB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,SAAS,CACpB,SAAS,CAAE,EAAE,CACb,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZC,EAAE,CAAE,UACN,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACL,aAAa,CAAE,eAAe,CAC9B,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,MAAM,CACrB,SAAS,CAAE,oBAAoB,CAC/B,SAAS,CAAE,uBAAuB,CAClCC,GAAG,CAAE,MAAM,CACXC,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,WAAW,CACrB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,EAAE,CACb,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZC,EAAE,CAAE,QACN,CAAC,CACD,CACEJ,EAAE,CAAE,CAAC,CACL,aAAa,CAAE,UAAU,CACzB,aAAa,CAAE,MAAM,CACrB,aAAa,CAAE,OAAO,CACtB,SAAS,CAAE,yBAAyB,CACpC,SAAS,CAAE,0BAA0B,CACrCC,GAAG,CAAE,KAAK,CACVC,EAAE,CAAE,EAAE,CACNC,IAAI,CAAE,IAAI,CACV,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,eAAe,CACzB,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,cAAc,CACxB,QAAQ,CAAE,SAAS,CACnB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,QAAQ,CAClB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZ,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,EAAE,CACZ,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,QAAQ,CACnB,SAAS,CAAE,EAAE,CACb,SAAS,CAAE,UAAU,CACrB,SAAS,CAAE,EAAE,CACb,QAAQ,CAAE,UAAU,CACpB,QAAQ,CAAE,EAAE,CACZC,EAAE,CAAE,UACN,CAAC,CACF,CAED,MAAO,KAAI,CAAC1B,IAAI,CAClB,CAEA;AACA,KAAM,CAAA2B,UAAUA,CAACC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAE,CACvC,GAAI,CACFtB,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAE,CAAEmB,QAAQ,CAAEC,KAAK,CAAEC,KAAM,CAAC,CAAC,CAE9D;AACA,GAAI,IAAI,CAAC9B,IAAI,CAAC4B,QAAQ,CAAC,CAAE,CACvB,IAAI,CAAC5B,IAAI,CAAC4B,QAAQ,CAAC,CAACC,KAAK,CAAC,CAAGC,KAAK,CACpC,CAEA;AACA,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAACb,OAAO,kCAAiC,CAC3EgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBP,QAAQ,CACRC,KAAK,CACLC,KACF,CAAC,CACH,CAAC,CAAC,CAEF,GAAIpB,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACpC,GAAID,MAAM,CAACE,OAAO,CAAE,CAClBR,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,CAC3B,IAAI,CAACJ,aAAa,EAAI,IAAI,CAACA,aAAa,CAAC,CAAC,CAC1C,MAAO,KAAI,CACb,CAAC,IAAM,CACLG,OAAO,CAACa,KAAK,CAAC,eAAe,CAAEP,MAAM,CAACsB,OAAO,CAAC,CAC9C,IAAI,CAAC9B,WAAW,EAAI,IAAI,CAACA,WAAW,CAACQ,MAAM,CAACsB,OAAO,CAAC,CACpD,MAAO,MAAK,CACd,CACF,CAAC,IAAM,CACL5B,OAAO,CAACa,KAAK,CAAC,qBAAqB,CAAEX,QAAQ,CAACU,MAAM,CAAC,CACrD,IAAI,CAACd,WAAW,EAAI,IAAI,CAACA,WAAW,SAAAM,MAAA,CAASF,QAAQ,CAACU,MAAM,CAAE,CAAC,CAC/D,MAAO,MAAK,CACd,CACF,CAAE,MAAOC,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,eAAe,CAAEA,KAAK,CAAC,CACrC,IAAI,CAACf,WAAW,EAAI,IAAI,CAACA,WAAW,CAACe,KAAK,CAACe,OAAO,CAAC,CACnD,MAAO,MAAK,CACd,CACF,CAEA;AACAC,eAAeA,CAACC,KAAK,CAAE,CACrB,GAAI,CAAC,IAAI,CAACtC,IAAI,EAAI,IAAI,CAACA,IAAI,CAACuC,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAEnD,KAAM,CAAAV,KAAK,CAAGS,KAAK,GAAK,IAAI,CAAG,aAAa,CAC9BA,KAAK,GAAK,IAAI,CAAG,aAAa,CAC9B,aAAa,CAE3B,KAAM,CAAAE,OAAO,CAAG,CAAC,GAAG,GAAI,CAAAC,GAAG,CAAC,IAAI,CAACzC,IAAI,CAClC0C,GAAG,CAACC,IAAI,EAAIA,IAAI,CAACd,KAAK,CAAC,CAAC,CACxBe,MAAM,CAACC,GAAG,EAAIA,GAAG,EAAIC,MAAM,CAACD,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC/CL,GAAG,CAACG,GAAG,EAAIC,MAAM,CAACD,GAAG,CAAC,CACzB,CAAC,CAAC,CAEF,MAAO,CAAAL,OAAO,CAChB,CAEA;AACAQ,aAAaA,CAACV,KAAK,CAAER,KAAK,CAAE,CAC1B,GAAI,CAAC,IAAI,CAAC9B,IAAI,EAAI,IAAI,CAACA,IAAI,CAACuC,MAAM,GAAK,CAAC,CAAE,MAAO,EAAE,CAEnD,GAAIT,KAAK,GAAK,IAAI,CAAE,MAAO,KAAI,CAAC9B,IAAI,CAEpC,KAAM,CAAA6B,KAAK,CAAGS,KAAK,GAAK,IAAI,CAAG,aAAa,CAC9BA,KAAK,GAAK,IAAI,CAAG,aAAa,CAC9B,aAAa,CAE3B,MAAO,KAAI,CAACtC,IAAI,CAAC4C,MAAM,CAACD,IAAI,EAC1BG,MAAM,CAACH,IAAI,CAACd,KAAK,CAAC,EAAI,EAAE,CAAC,CAACkB,IAAI,CAAC,CAAC,GAAKjB,KACvC,CAAC,CACH,CAEA;AACAmB,cAAcA,CAAA,CAAG,CACf,KAAM,CAAAC,MAAM,CAAG,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,IAAI,CAAC,CAC1F,KAAM,CAAAC,WAAW,CAAG,EAAE,CAEtBD,MAAM,CAACE,OAAO,CAACC,KAAK,EAAI,CACtBF,WAAW,CAACG,IAAI,IAAA1C,MAAA,CAAIyC,KAAK,4BAAM,CAAC,CAChCF,WAAW,CAACG,IAAI,IAAA1C,MAAA,CAAIyC,KAAK,4BAAM,CAAC,CAClC,CAAC,CAAC,CAEF,MAAO,CAAAF,WAAW,CACpB,CAEA;AACAI,YAAYA,CAAA,CAAG,CACb,GAAI,CAAC,IAAI,CAACvD,IAAI,EAAI,IAAI,CAACA,IAAI,CAACuC,MAAM,GAAK,CAAC,CAAE,CACxC,MAAO,CACLiB,UAAU,CAAE,CAAC,CACbC,WAAW,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpCC,WAAW,CAAE,CAAC,CACdC,SAAS,CAAE,CACb,CAAC,CACH,CAEA,KAAM,CAAAL,WAAW,CAAG,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3C,GAAI,CAAAC,WAAW,CAAG,CAAC,CAEnB,IAAI,CAAC7D,IAAI,CAACoD,OAAO,CAACT,IAAI,EAAI,CACxB;AACA,GAAIA,IAAI,CAAC,aAAa,CAAC,CAAEc,WAAW,CAACC,EAAE,EAAE,CACzC,GAAIf,IAAI,CAAC,aAAa,CAAC,CAAEc,WAAW,CAACE,EAAE,EAAE,CACzC,GAAIhB,IAAI,CAAC,aAAa,CAAC,CAAEc,WAAW,CAACG,EAAE,EAAE,CAEzC;AACA,KAAM,CAAAG,MAAM,CAAGpB,IAAI,CAACnB,EAAE,CACtB,GAAIuC,MAAM,EAAI,CAACC,KAAK,CAACC,MAAM,CAACF,MAAM,CAAC,CAAC,CAAE,CACpCF,WAAW,EAAII,MAAM,CAACF,MAAM,CAAC,CAC/B,CACF,CAAC,CAAC,CAEF,MAAO,CACLP,UAAU,CAAE,IAAI,CAACxD,IAAI,CAACuC,MAAM,CAC5BkB,WAAW,CACXI,WAAW,CACXC,SAAS,CAAE,IAAI,CAAC9D,IAAI,CAACuC,MAAM,CAAG,CAAC,CAAG,CAACsB,WAAW,CAAG,IAAI,CAAC7D,IAAI,CAACuC,MAAM,EAAE2B,OAAO,CAAC,CAAC,CAAC,CAAG,CAClF,CAAC,CACH,CAEA;AACA,KAAM,CAAAC,YAAYA,CAAA,CAAG,CACnB,GAAI,CACF,KAAM,CAAAzD,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAACb,OAAO,kCAAgC,CAAC,CAE7E,GAAIW,QAAQ,CAACG,EAAE,CAAE,CACf,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CACpC,MAAO,CAAAD,MAAM,CACf,CAAC,IAAM,CACL,MAAO,CAAEE,OAAO,CAAE,KAAK,CAAEoB,OAAO,CAAE,QAAS,CAAC,CAC9C,CACF,CAAE,MAAOf,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,MAAO,CAAEL,OAAO,CAAE,KAAK,CAAEoB,OAAO,CAAEf,KAAK,CAACe,OAAQ,CAAC,CACnD,CACF,CAEA;AACA,KAAM,CAAAgC,UAAUA,CAAA,CAAe,IAAd,CAAA5B,OAAO,CAAA6B,SAAA,CAAA9B,MAAA,IAAA8B,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,CAAC,CAC3B,GAAI,CACF,KAAM,CAAA3D,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAAC,MAAA,CAAI,IAAI,CAACb,OAAO,iCAAgC,CAC1EgC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAACK,OAAO,CAC9B,CAAC,CAAC,CAEF,GAAI9B,QAAQ,CAACG,EAAE,CAAE,CACf,MAAO,CAAAH,QAAQ,CAAC6D,IAAI,CAAC,CAAC,CACxB,CAAC,IAAM,CACL,KAAM,IAAI,CAAAC,KAAK,CAAC,QAAQ,CAAC,CAC3B,CACF,CAAE,MAAOnD,KAAK,CAAE,CACdb,OAAO,CAACa,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/B,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAoD,OAAOA,CAAA,CAAG,CACR,MAAO,KAAI,CAACzE,IAAI,CAClB,CAEA;AACA0E,YAAYA,CAAA,CAAG,CACb,MAAO,KAAI,CAAC1E,IAAI,EAAI,IAAI,CAACA,IAAI,CAACuC,MAAM,CAAG,CAAC,CAC1C,CAEA;AACAoC,eAAeA,CAAA,CAAG,CAChB,MAAO,KAAI,CAAC1E,YAAY,CAC1B,CACF,CAEA,cAAe,IAAI,CAAAJ,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}