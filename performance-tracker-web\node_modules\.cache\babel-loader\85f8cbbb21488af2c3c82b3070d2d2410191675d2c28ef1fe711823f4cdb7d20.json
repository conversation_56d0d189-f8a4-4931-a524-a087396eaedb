{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Department=_ref=>{let{onNavigate}=_ref;return/*#__PURE__*/_jsxs(\"div\",{style:{minHeight:'100vh',padding:'40px',display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center'},children:[/*#__PURE__*/_jsx(\"h1\",{style:{fontFamily:'Orbitron, monospace',fontSize:'2.5rem',color:'#ffff4d',marginBottom:'2rem',textAlign:'center'},children:\"\\u5F00\\u53D1\\u4E2D\\u5FC3\\u4E8C\\u7EA7\\u90E8\\u95E8\\u5DE5\\u4F5C\\u76EE\\u6807\\u7BA1\\u7406\\u8D23\\u4EFB\\u4E66\"}),/*#__PURE__*/_jsx(\"p\",{style:{fontFamily:'<PERSON><PERSON><PERSON>, sans-serif',fontSize:'1.2rem',color:'#ffffff',marginBottom:'3rem',textAlign:'center',maxWidth:'600px'},children:\"\\u6B64\\u6A21\\u5757\\u5C06\\u5C55\\u793A\\u5404\\u90E8\\u95E8\\u76EE\\u6807\\u7BA1\\u7406\\u4E0E\\u7EE9\\u6548\\u8003\\u6838\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onNavigate('home'),style:{padding:'12px 30px',background:'linear-gradient(45deg, #ffff4d, #20ff4d)',border:'none',borderRadius:'8px',color:'#000',fontFamily:'Rajdhani, sans-serif',fontSize:'1.1rem',fontWeight:'600',cursor:'pointer'},children:\"\\u8FD4\\u56DE\\u9996\\u9875\"})]});};export default Department;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "Department", "_ref", "onNavigate", "style", "minHeight", "padding", "display", "flexDirection", "alignItems", "justifyContent", "children", "fontFamily", "fontSize", "color", "marginBottom", "textAlign", "max<PERSON><PERSON><PERSON>", "onClick", "background", "border", "borderRadius", "fontWeight", "cursor"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/pages/Department.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Department = ({ onNavigate }) => {\r\n  return (\r\n    <div style={{ \r\n      minHeight: '100vh', \r\n      padding: '40px',\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      alignItems: 'center',\r\n      justifyContent: 'center'\r\n    }}>\r\n      <h1 style={{\r\n        fontFamily: 'Orbitron, monospace',\r\n        fontSize: '2.5rem',\r\n        color: '#ffff4d',\r\n        marginBottom: '2rem',\r\n        textAlign: 'center'\r\n      }}>\r\n        开发中心二级部门工作目标管理责任书\r\n      </h1>\r\n      <p style={{\r\n        fontFamily: 'Rajdhani, sans-serif',\r\n        fontSize: '1.2rem',\r\n        color: '#ffffff',\r\n        marginBottom: '3rem',\r\n        textAlign: 'center',\r\n        maxWidth: '600px'\r\n      }}>\r\n        此模块将展示各部门目标管理与绩效考核数据\r\n      </p>\r\n      <button \r\n        onClick={() => onNavigate('home')}\r\n        style={{\r\n          padding: '12px 30px',\r\n          background: 'linear-gradient(45deg, #ffff4d, #20ff4d)',\r\n          border: 'none',\r\n          borderRadius: '8px',\r\n          color: '#000',\r\n          fontFamily: 'Rajdhani, sans-serif',\r\n          fontSize: '1.1rem',\r\n          fontWeight: '600',\r\n          cursor: 'pointer'\r\n        }}\r\n      >\r\n        返回首页\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Department; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,UAAU,CAAGC,IAAA,EAAoB,IAAnB,CAAEC,UAAW,CAAC,CAAAD,IAAA,CAChC,mBACEF,KAAA,QAAKI,KAAK,CAAE,CACVC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAC,QAAA,eACAb,IAAA,OAAIM,KAAK,CAAE,CACTQ,UAAU,CAAE,qBAAqB,CACjCC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,QACb,CAAE,CAAAL,QAAA,CAAC,wGAEH,CAAI,CAAC,cACLb,IAAA,MAAGM,KAAK,CAAE,CACRQ,UAAU,CAAE,sBAAsB,CAClCC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,SAAS,CAChBC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,QAAQ,CACnBC,QAAQ,CAAE,OACZ,CAAE,CAAAN,QAAA,CAAC,0HAEH,CAAG,CAAC,cACJb,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAMf,UAAU,CAAC,MAAM,CAAE,CAClCC,KAAK,CAAE,CACLE,OAAO,CAAE,WAAW,CACpBa,UAAU,CAAE,0CAA0C,CACtDC,MAAM,CAAE,MAAM,CACdC,YAAY,CAAE,KAAK,CACnBP,KAAK,CAAE,MAAM,CACbF,UAAU,CAAE,sBAAsB,CAClCC,QAAQ,CAAE,QAAQ,CAClBS,UAAU,CAAE,KAAK,CACjBC,MAAM,CAAE,SACV,CAAE,CAAAZ,QAAA,CACH,0BAED,CAAQ,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}