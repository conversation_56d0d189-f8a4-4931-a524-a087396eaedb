import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../styles/WorldClass.css';
import worldClassService from '../services/worldClassService';
import worldClassDownloadService from '../services/worldClassDownloadService';
import WorldClassDownloadModal from '../components/WorldClassDownloadModal';

const WorldClass = ({ onNavigate }) => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingCell, setEditingCell] = useState(null);
  const [syncStatus, setSyncStatus] = useState('已同步');

  // 数据输入优化相关状态
  const [tempValues, setTempValues] = useState({}); // 临时存储编辑中的值
  const saveTimeoutRef = useRef(null);
  const pendingSaveRef = useRef(null);
  
  // 层级筛选状态
  const [levelFilter, setLevelFilter] = useState({
    level: '一级',
    value: '全部'
  });
  
  // 负责人筛选相关状态（复用模块二功能）
  const [selectedResponsiblePersons, setSelectedResponsiblePersons] = useState([]);
  const [tempSelectedPersons, setTempSelectedPersons] = useState([]); // 临时存储选择的负责人
  const [showResponsibleFilter, setShowResponsibleFilter] = useState(false);
  
  // 月份切换状态 - 从2月开始按月递增，参考模块二的逻辑设置初始显示月份
  const [currentMonthPair, setCurrentMonthPair] = useState(() => {
    // 根据当前月份设置初始显示的月份对
    const currentMonth = new Date().getMonth() + 1; // getMonth()返回0-11，加1得到1-12
    const months = ['2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

    // 找到当前月份在数组中的位置
    let currentIndex = -1;
    months.forEach((month, index) => {
      const monthNum = parseInt(month.replace('月', ''));
      if (monthNum === currentMonth) {
        currentIndex = index;
      }
    });

    // 如果找到当前月份，返回包含当前月份的月份对索引
    if (currentIndex >= 0) {
      // 如果当前月份是第一个月（如2月），则显示第一对
      if (currentIndex === 0) return 0;
      // 否则，尽量让当前月份显示在第一个位置
      return Math.max(0, currentIndex - 1);
    }

    // 如果当前月份不在范围内，默认显示第一对
    return 0;
  });
  const monthPairs = [
    ['2月', '3月'], ['3月', '4月'], ['4月', '5月'],
    ['5月', '6月'], ['6月', '7月'], ['7月', '8月'],
    ['8月', '9月'], ['9月', '10月'], ['10月', '11月'],
    ['11月', '12月']
  ];
  
  // 下载相关状态
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);

  useEffect(() => {
    loadData();

    // 设置同步回调
    worldClassService.setSyncCallbacks(
      () => setSyncStatus('同步成功'),
      (error) => setSyncStatus('同步失败')
    );

    // 清理函数
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }
    };
  }, []);

  useEffect(() => {
    // 应用层级筛选和负责人筛选
    applyFilters();
  }, [data, levelFilter, selectedResponsiblePersons]);

  const loadData = async () => {
    setLoading(true);
    try {
      console.log('开始加载对标世界一流数据...');
      const worldClassData = await worldClassService.loadWorldClassData();
      console.log('加载的数据:', worldClassData);
      
      if (worldClassData) {
        setData(worldClassData);
        console.log('数据设置完成，总共:', worldClassData.length, '项');
        console.log('前3条数据预览:', worldClassData.slice(0, 3).map(item => ({
          工作准则: item.工作准则,
          负责人: item.负责人,
          '2025年目标': item['2025年目标']?.substring(0, 50) + '...'
        })));
      } else {
        console.error('未获取到数据');
      }
    } catch (error) {
      console.error('数据加载失败:', error);
    }
    setLoading(false);
  };

  // 强制刷新数据的方法（复用模块一的设计）
  const forceRefresh = async () => {
    console.log('强制刷新数据...');
    setSyncStatus('刷新中...');
    await loadData();
    setSyncStatus('刷新完成');
  };

  // 获取层级选项
  const getLevelOptions = () => {
    if (!data || data.length === 0) return [];
    
    const field = levelFilter.level === '一级' ? '相关指标或方向（一级）' : 
                  levelFilter.level === '二级' ? '相关指标或方向（二级）' : 
                  '相关指标或方向（三级）';
    
    const options = [...new Set(data
      .map(item => item[field])
      .filter(val => val && String(val).trim() !== '')
      .map(val => String(val))
    )];
    
    return ['全部', ...options];
  };

  // 获取所有负责人列表（基于当前显示的数据而不是全部数据）
  const getAllResponsiblePersons = () => {
    // 使用当前筛选后的数据作为数据源
    const sourceData = filteredData.length > 0 ? filteredData : data;
    if (!sourceData || sourceData.length === 0) return [];
    
    const responsiblePersons = sourceData
      .map(item => item.负责人)
      .filter(person => person && String(person).trim() !== '')
      .reduce((acc, person) => {
        // 处理多个负责人用逗号、括号等分隔的情况
        const persons = String(person).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);
        persons.forEach(p => {
          if (!acc.includes(p)) {
            acc.push(p);
          }
        });
        return acc;
      }, []);
    
    return responsiblePersons.sort();
  };

  // 应用综合筛选（层级 + 负责人）
  const applyFilters = () => {
    if (!data || data.length === 0) {
      setFilteredData([]);
      return;
    }

    let filtered = [...data];

    // 先应用层级筛选
    if (levelFilter.value !== '全部') {
      const field = levelFilter.level === '一级' ? '相关指标或方向（一级）' : 
                    levelFilter.level === '二级' ? '相关指标或方向（二级）' : 
                    '相关指标或方向（三级）';

      filtered = filtered.filter(item => 
        String(item[field] || '').trim() === levelFilter.value
      );
    }

    // 再应用负责人筛选 - 基于已经层级筛选后的数据
    if (selectedResponsiblePersons.length > 0) {
      filtered = filtered.filter(item => {
        if (!item.负责人) return false;
        
        // 将单元格中的负责人字符串拆分为数组，并进行清理
        const itemPersons = String(item.负责人).split(/[,，;；、（）()]/).map(p => p.trim()).filter(p => p);
        
        // 检查是否有任何一个选中的负责人存在于单元格的负责人数组中（精确匹配）
        return selectedResponsiblePersons.some(selectedPerson => 
          itemPersons.includes(selectedPerson)
        );
      });
    }
    
    setFilteredData(filtered);
  };

  // 负责人筛选相关函数
  const toggleResponsibleFilter = () => {
    if (!showResponsibleFilter) {
      // 打开面板时，用当前已应用的筛选初始化临时选择
      setTempSelectedPersons([...selectedResponsiblePersons]);
    }
    setShowResponsibleFilter(!showResponsibleFilter);
  };

  const handleResponsiblePersonSelect = (person) => {
    // 只更新临时选择的负责人状态
    setTempSelectedPersons(prev => {
      if (prev.includes(person)) {
        return prev.filter(p => p !== person);
      } else {
        return [...prev, person];
      }
    });
  };

  const handleClearPanelSelection = () => {
    // 仅清除面板内的临时选择，不关闭面板，以便用户重新选择
    setTempSelectedPersons([]);
  };

  const applyResponsibleFilter = () => {
    // 应用筛选，将临时选择更新到正式的筛选状态
    setSelectedResponsiblePersons([...tempSelectedPersons]);
    setShowResponsibleFilter(false);
  };

  const clearResponsibleFilter = () => {
    // 清除临时选择和已应用的筛选
    setTempSelectedPersons([]);
    setSelectedResponsiblePersons([]);
    setShowResponsibleFilter(false);
  };

  // 月份切换功能（参考模块二设计）
  const navigateMonth = (direction) => {
    if (direction === 'prev' && currentMonthPair > 0) {
      setCurrentMonthPair(currentMonthPair - 1);
    } else if (direction === 'next' && currentMonthPair < monthPairs.length - 1) {
      setCurrentMonthPair(currentMonthPair + 1);
    }
  };

  // 防抖保存函数
  const debouncedSave = useCallback(async (rowIndex, field, value) => {
    try {
      setSyncStatus('同步中...');

      // 取消之前的保存操作
      if (pendingSaveRef.current) {
        pendingSaveRef.current.abort?.();
      }

      // 创建新的保存操作
      const controller = new AbortController();
      pendingSaveRef.current = controller;

      // 调用双向同步
      await worldClassService.updateData(rowIndex, field, value);

      // 如果没有被取消，更新状态
      if (!controller.signal.aborted) {
        setSyncStatus('同步成功');
        setTimeout(() => setSyncStatus('已同步'), 1000);
        pendingSaveRef.current = null;
      }
    } catch (error) {
      if (!error.name === 'AbortError') {
        console.error('保存失败:', error);
        setSyncStatus('同步失败');
      }
    }
  }, []);

  // 处理输入变化（实时更新UI，延迟保存）
  const handleInputChange = (rowIndex, field, value) => {
    // 立即更新UI显示
    const newData = [...data];
    newData[rowIndex][field] = value;
    setData(newData);

    // 存储临时值
    const key = `${rowIndex}-${field}`;
    setTempValues(prev => ({ ...prev, [key]: value }));

    // 清除之前的定时器
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // 设置新的防抖定时器
    saveTimeoutRef.current = setTimeout(() => {
      debouncedSave(rowIndex, field, value);
    }, 800); // 800ms防抖延迟
  };

  // 处理失焦保存（立即保存）
  const handleBlurSave = async (rowIndex, field) => {
    const key = `${rowIndex}-${field}`;
    const value = tempValues[key];

    if (value !== undefined) {
      // 清除防抖定时器
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
        saveTimeoutRef.current = null;
      }

      // 立即保存
      await debouncedSave(rowIndex, field, value);

      // 清除临时值
      setTempValues(prev => {
        const newTemp = { ...prev };
        delete newTemp[key];
        return newTemp;
      });
    }
  };

  // 兼容原有的handleCellEdit接口
  const handleCellEdit = async (rowIndex, field, value) => {
    handleInputChange(rowIndex, field, value);
  };

  const startEdit = (rowIndex, field) => {
    setEditingCell(`${rowIndex}-${field}`);
  };

  const finishEdit = () => {
    setEditingCell(null);
  };

  // 格式化显示值
  const formatDisplayValue = (value) => {
    if (value === null || value === undefined || value === '') return '';
    if (typeof value === 'object') {
      if (value.hasOwnProperty('v')) return value.v;
      if (value.hasOwnProperty('w')) return value.w;
      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;
      if (value.text !== undefined) return value.text;
      if (value.richText !== undefined) return value.richText;
      if (value.value !== undefined) return value.value;
      return String(value);
    }
    return String(value);
  };

  // 渲染可编辑单元格
  const renderEditableCell = (value, rowIndex, field, className = '') => {
    const cellKey = `${rowIndex}-${field}`;
    const isEditing = editingCell === cellKey;
    const displayValue = formatDisplayValue(value);

    // 检查是否为只读列（完成情况列）
    const isReadOnlyField = field.includes('完成情况');
    const isEditable = !['序号'].includes(field) && !isReadOnlyField;

    if (isEditing && isEditable) {
      return (
        <textarea
          value={displayValue}
          onChange={(e) => handleInputChange(rowIndex, field, e.target.value)}
          onBlur={() => {
            handleBlurSave(rowIndex, field);
            finishEdit();
          }}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
              handleBlurSave(rowIndex, field);
              finishEdit();
            }
            if (e.key === 'Escape') {
              finishEdit();
            }
          }}
          className="cell-input"
          rows={displayValue.includes('\n') ? displayValue.split('\n').length + 1 : 2}
          autoFocus
        />
      );
    }

    // 确定单元格的CSS类名
    let cellClasses = `cell-content ${className}`;
    if (isReadOnlyField) {
      cellClasses += ' readonly-cell';
    } else if (isEditable) {
      cellClasses += ' editable-cell';
    }

    // 确定提示文本
    let titleText = '';
    if (isReadOnlyField) {
      titleText = '该列不可编辑';
    } else if (isEditable) {
      titleText = '点击编辑';
    }

    return (
      <span
        className={cellClasses}
        onClick={() => isEditable && startEdit(rowIndex, field)}
        title={titleText}
        style={isReadOnlyField ? { cursor: 'not-allowed' } : {}}
      >
        {displayValue.includes('\n') ?
          displayValue.split('\n').map((line, i, arr) => (
            <React.Fragment key={i}>
              {line}
              {i < arr.length - 1 && <br />}
            </React.Fragment>
          )) :
          displayValue
        }
      </span>
    );
  };

  // 渲染月份列
  const renderMonthColumns = (row, rowIndex) => {
    const [month1, month2] = monthPairs[currentMonthPair];
    
    return (
      <>
        <td className="data-cell col-month-plan">
          {renderEditableCell(row[`${month1}工作计划`], rowIndex, `${month1}工作计划`, 'month-plan')}
        </td>
        <td className="data-cell col-month-complete">
          {renderEditableCell(row[`${month1}完成情况`], rowIndex, `${month1}完成情况`, 'month-complete')}
        </td>
        <td className="data-cell col-month-plan">
          {renderEditableCell(row[`${month2}工作计划`], rowIndex, `${month2}工作计划`, 'month-plan')}
        </td>
        <td className="data-cell col-month-complete">
          {renderEditableCell(row[`${month2}完成情况`], rowIndex, `${month2}完成情况`, 'month-complete')}
        </td>
      </>
    );
  };

  // 下载功能（复用模块一的设计）
  const handleDownload = async (selectionData) => {
    setDownloadLoading(true);
    try {
      console.log('开始下载对标世界一流数据:', selectionData);
      
      // 调用下载服务处理实际下载
      const result = await worldClassDownloadService.handleSelectiveDownload(selectionData);
      
      if (result.success) {
        console.log('下载成功:', result.message);
        alert(`下载成功: ${result.message}`);
      } else {
        throw new Error(result.message || '下载失败');
      }
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败: ' + error.message);
    } finally {
      setDownloadLoading(false);
      setShowDownloadModal(false);
    }
  };

  const openDownloadModal = () => {
    setShowDownloadModal(true);
  };

  const closeDownloadModal = () => {
    setShowDownloadModal(false);
  };

  // 渲染负责人筛选面板
  const renderResponsibleFilterPanel = () => {
    if (!showResponsibleFilter) return null;
    const allPersons = getAllResponsiblePersons();

    return (
      <div className="world-class-responsible-filter-panel">
        <div className="world-class-filter-panel-header">
          <h3>选择负责人</h3>
          <button onClick={toggleResponsibleFilter} className="world-class-close-panel-btn">×</button>
        </div>
        <div className="world-class-filter-panel-content">
          <div className="world-class-filter-options">
            {allPersons.map(person => (
              <div 
                key={person} 
                className={`world-class-filter-option ${tempSelectedPersons.includes(person) ? 'selected' : ''}`}
                onClick={() => handleResponsiblePersonSelect(person)}
              >
                {person}
              </div>
            ))}
          </div>
          <div className="world-class-filter-actions">
            <button onClick={handleClearPanelSelection} className="world-class-clear-filter-btn">
              清除选择
            </button>
            <button onClick={applyResponsibleFilter} className="world-class-apply-filter-btn">
              应用筛选
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="world-class-container">
        <div className="world-class-loading">
          <div className="loading-spinner"></div>
          <p>正在加载对标世界一流数据...</p>
        </div>
      </div>
    );
  }

  const currentData = filteredData.length > 0 ? filteredData : data;
  const [month1, month2] = monthPairs[currentMonthPair];

  // 调试信息
  console.log('渲染数据状态:', {
    原始数据条数: data.length,
    过滤数据条数: filteredData.length,
    当前显示条数: currentData.length,
    当前月份对: `${month1}-${month2}`
  });

  return (
    <div className="world-class-container">
      {/* 负责人筛选面板（复用模块二设计） */}
      {renderResponsibleFilterPanel()}

      {/* 负责人筛选状态提示 */}
      {selectedResponsiblePersons.length > 0 && (
        <div 
          style={{
            background: 'linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(32, 255, 77, 0.1))',
            border: '1px solid rgba(0, 212, 170, 0.3)',
            borderRadius: '12px',
            margin: '20px auto',
            width: '100%',
            maxWidth: 'none',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 20px rgba(0, 212, 170, 0.2)'
          }}
        >
          <div 
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '16px 24px',
              gap: '16px'
            }}
          >
            <div 
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '40px',
                height: '40px',
                background: 'linear-gradient(45deg, #00d4aa, #20ff4d)',
                borderRadius: '50%',
                fontSize: '18px'
              }}
            >
              🔍
            </div>
            <div style={{ flex: 1 }}>
              <div 
                style={{
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.7)',
                  marginBottom: '4px',
                  fontWeight: '500'
                }}
              >
                当前筛选条件
              </div>
              <div 
                style={{
                  fontSize: '16px',
                  color: '#fff',
                  fontWeight: '600'
                }}
              >
                负责人：
                <span style={{ 
                  color: '#20ff4d', 
                  fontWeight: 'bold',
                  textShadow: '0 0 10px rgba(32, 255, 77, 0.6)',
                  margin: '0 8px'
                }}>
                  {selectedResponsiblePersons.join('、')}
                </span>
                <span style={{ 
                  color: '#00d4aa',
                  fontSize: '14px'
                }}>
                  （{filteredData.length}项工作）
                </span>
              </div>
            </div>
            <button 
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                padding: '8px 16px',
                background: 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))',
                border: '1px solid rgba(255, 75, 75, 0.4)',
                borderRadius: '8px',
                color: '#ff6b6b',
                fontSize: '14px',
                fontWeight: '500',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onClick={clearResponsibleFilter}
              onMouseEnter={(e) => {
                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.3), rgba(255, 100, 100, 0.3))';
                e.target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'linear-gradient(45deg, rgba(255, 75, 75, 0.2), rgba(255, 100, 100, 0.2))';
                e.target.style.transform = 'translateY(0)';
              }}
              title="清除筛选条件"
            >
              <span>✕</span>
              <span>清除筛选</span>
            </button>
          </div>
        </div>
      )}
      {/* 页面头部 */}
      <div className="page-header">
        <button 
          className="back-btn-top"
          onClick={() => onNavigate('home')}
        >
          返回首页
        </button>
        
        <div className="header-center">
          <h1 className="page-title">对标世界一流举措-提报版</h1>
          <p className="page-subtitle">三级层级管理·月度跟踪·数据同步</p>
        </div>
        
        <div className="header-actions">
          <button className="refresh-btn-new" onClick={forceRefresh}>
            🔄 刷新数据
          </button>
          <div className="sync-status">
            <span 
              className={`status-indicator ${syncStatus.includes('成功') ? 'success' : syncStatus.includes('失败') ? 'error' : 'pending'}`}
            >
              {syncStatus}
            </span>
          </div>
        </div>
      </div>

      {/* 控制面板 */}
      <div className="control-panel">
        <div className="controls-left">
          <div className="filter-controls">
            <div className="filter-group">
              <label>层级类型:</label>
              <select
                className="level-selector"
                value={levelFilter.level}
                onChange={(e) => {
                  setLevelFilter({
                    level: e.target.value,
                    value: '全部'
                  });
                }}
              >
                <option value="一级">一级类型</option>
                <option value="二级">二级类型</option>
                <option value="三级">三级类型</option>
              </select>
            </div>
            
            <div className="filter-group">
              <label>筛选内容:</label>
              <select
                className="value-selector"
                value={levelFilter.value}
                onChange={(e) => {
                  setLevelFilter({
                    ...levelFilter,
                    value: e.target.value
                  });
                }}
              >
                {getLevelOptions().map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            
            <div className="stat-item-inline">
              <span className="stat-label">当前显示:</span>
              <span className="stat-value">{currentData.length} 项</span>
            </div>
            
            <div 
              className="responsible-filter-btn-new"
              onClick={toggleResponsibleFilter}
            >
              <span className="filter-icon">👥</span>
              {selectedResponsiblePersons.length > 0 && (
                <div className="filter-badge">
                  {selectedResponsiblePersons.length}
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="action-buttons">
          <div className="month-navigation-new">
            <button 
              className="nav-btn-new"
              onClick={() => navigateMonth('prev')}
              disabled={currentMonthPair === 0}
            >
              ◀上个月份对
            </button>
            <span className="current-months-new">
              {month1} / {month2}
            </span>
            <button 
              className="nav-btn-new"
              onClick={() => navigateMonth('next')}
              disabled={currentMonthPair === monthPairs.length - 1}
            >
              下个月份对▶
            </button>
          </div>
          <button 
            className="download-btn" 
            onClick={openDownloadModal}
          >
            📊 选择性下载
          </button>
        </div>
      </div>

      {/* 数据表格 */}
      <div className="table-container">
        <table className="world-class-table">
          <thead>
            <tr>
              <th className="col-number">序号</th>
              <th className="col-criteria">工作准则</th>
              <th className="col-target">2025年目标</th>
              <th className="col-measure">2025年举措</th>
              <th className="col-responsible">负责人</th>
              <th className="col-weight">权重</th>
              <th className="col-month-plan">{month1}工作计划</th>
              <th className="col-month-complete">{month1}完成情况</th>
              <th className="col-month-plan">{month2}工作计划</th>
              <th className="col-month-complete">{month2}完成情况</th>
              <th className="col-remark">备注</th>
            </tr>
          </thead>
          <tbody>
            {currentData.map((row, index) => (
              <tr key={index} className="data-row">
                <td className="data-cell col-number">
                  {index + 1}
                </td>
                <td className="data-cell col-criteria">
                  {renderEditableCell(row.工作准则, index, '工作准则')}
                </td>
                <td className="data-cell col-target">
                  {renderEditableCell(row['2025年目标'], index, '2025年目标')}
                </td>
                <td className="data-cell col-measure">
                  {renderEditableCell(row['2025年举措'], index, '2025年举措')}
                </td>
                <td className="data-cell col-responsible">
                  {renderEditableCell(row.负责人, index, '负责人')}
                </td>
                <td className="data-cell col-weight">
                  {renderEditableCell(row.权重, index, '权重')}
                </td>
                {renderMonthColumns(row, index)}
                <td className="data-cell col-remark">
                  {renderEditableCell(row.备注, index, '备注')}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {currentData.length === 0 && (
        <div className="no-data">
          <p>当前筛选条件下没有数据</p>
        </div>
      )}

      {/* 选择性下载模态框 */}
      <WorldClassDownloadModal
        isOpen={showDownloadModal}
        onClose={closeDownloadModal}
        data={currentData}
        filteredData={filteredData}
        levelFilter={levelFilter}
        currentMonthPair={currentMonthPair}
        monthPairs={monthPairs}
        onDownload={handleDownload}
      />
    </div>
  );
};

export default WorldClass; 