{"ast": null, "code": "import _objectSpread from\"E:/\\u5904\\u7406\\u516C\\u53F8\\u7A0B\\u5E8F/\\u91CD\\u70B9\\u5DE5\\u4F5C\\u7F51\\u9875\\u5316\\u8BBE\\u8BA19.10/performance-tracker-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import'./WorldClassDownloadModal.css';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const WorldClassDownloadModal=_ref=>{let{isOpen,onClose,data,filteredData,levelFilter,currentMonthPair,monthPairs,onDownload}=_ref;const[selectionState,setSelectionState]=useState({selectedLevels:new Set(),selectedItems:new Set(),downloadFormat:'excel',monthRange:{start:0,end:monthPairs.length-1},// 月份对索引范围\nincludeLevelFilter:true});const[statistics,setStatistics]=useState({totalItems:0,levelCount:0,monthsCovered:''});// 获取当前使用的数据（筛选后或原始数据）\nconst getCurrentData=()=>{if(levelFilter.value!=='全部'&&filteredData&&filteredData.length>0){return filteredData;}else if(data&&data.length>0){return data;}else{return[];}};// 按层级分组数据\nconst getGroupedData=()=>{const dataToUse=getCurrentData();if(!dataToUse||dataToUse.length===0){return{};}const levelField=levelFilter.level==='一级'?'相关指标或方向（一级）':levelFilter.level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';const grouped={};dataToUse.forEach(item=>{const level=item[levelField]||'其他';if(!grouped[level]){grouped[level]=[];}grouped[level].push(item);});return grouped;};// 获取所有层级\nconst getLevels=()=>{const grouped=getGroupedData();return Object.keys(grouped);};useEffect(()=>{updateStatistics();},[selectionState.selectedItems,selectionState.monthRange,data,filteredData]);// 计算统计信息\nconst updateStatistics=()=>{var _monthPairs$selection,_monthPairs$selection2;let totalItems=0;const levels=new Set();const dataToUse=getCurrentData();dataToUse.forEach((item,index)=>{const itemKey=\"item-\".concat(index);if(selectionState.selectedItems.has(itemKey)){totalItems++;const levelField=levelFilter.level==='一级'?'相关指标或方向（一级）':levelFilter.level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';if(item[levelField]){levels.add(item[levelField]);}}});// 计算覆盖的月份范围\nconst startMonth=((_monthPairs$selection=monthPairs[selectionState.monthRange.start])===null||_monthPairs$selection===void 0?void 0:_monthPairs$selection[0])||'';const endMonth=((_monthPairs$selection2=monthPairs[selectionState.monthRange.end])===null||_monthPairs$selection2===void 0?void 0:_monthPairs$selection2[1])||'';const monthsCovered=startMonth&&endMonth?\"\".concat(startMonth,\" \\u81F3 \").concat(endMonth):'';setStatistics({totalItems,levelCount:levels.size,monthsCovered});};// 处理层级选择\nconst handleLevelSelect=level=>{const newSelectedLevels=new Set(selectionState.selectedLevels);const newSelectedItems=new Set(selectionState.selectedItems);const grouped=getGroupedData();const dataToUse=getCurrentData();if(newSelectedLevels.has(level)){var _grouped$level;// 取消选择层级，移除该层级下的所有项目\nnewSelectedLevels.delete(level);(_grouped$level=grouped[level])===null||_grouped$level===void 0?void 0:_grouped$level.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedItems.delete(\"item-\".concat(itemIndex));}});}else{var _grouped$level2;// 选择层级，添加该层级下的所有项目\nnewSelectedLevels.add(level);(_grouped$level2=grouped[level])===null||_grouped$level2===void 0?void 0:_grouped$level2.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedItems.add(\"item-\".concat(itemIndex));}});}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedLevels:newSelectedLevels,selectedItems:newSelectedItems}));};// 处理单项选择\nconst handleItemSelect=itemIndex=>{const itemKey=\"item-\".concat(itemIndex);const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedLevels=new Set(selectionState.selectedLevels);const dataToUse=getCurrentData();const item=dataToUse[itemIndex];const levelField=levelFilter.level==='一级'?'相关指标或方向（一级）':levelFilter.level==='二级'?'相关指标或方向（二级）':'相关指标或方向（三级）';const level=(item===null||item===void 0?void 0:item[levelField])||'其他';if(newSelectedItems.has(itemKey)){newSelectedItems.delete(itemKey);// 检查是否需要取消层级选择\nconst grouped=getGroupedData();const levelItems=grouped[level]||[];const levelItemsSelected=levelItems.some(levelItem=>{const levelItemIndex=dataToUse.findIndex(d=>d===levelItem);return levelItemIndex>=0&&newSelectedItems.has(\"item-\".concat(levelItemIndex));});if(!levelItemsSelected){newSelectedLevels.delete(level);}}else{newSelectedItems.add(itemKey);// 检查是否需要添加层级选择\nconst grouped=getGroupedData();const levelItems=grouped[level]||[];const allLevelItemsSelected=levelItems.every(levelItem=>{const levelItemIndex=dataToUse.findIndex(d=>d===levelItem);return levelItemIndex>=0&&(newSelectedItems.has(\"item-\".concat(levelItemIndex))||levelItemIndex===itemIndex);});if(allLevelItemsSelected){newSelectedLevels.add(level);}}setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedLevels:newSelectedLevels,selectedItems:newSelectedItems}));};// 层级全选\nconst handleLevelSelectAll=level=>{var _grouped$level3;const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedLevels=new Set(selectionState.selectedLevels);const grouped=getGroupedData();const dataToUse=getCurrentData();(_grouped$level3=grouped[level])===null||_grouped$level3===void 0?void 0:_grouped$level3.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedItems.add(\"item-\".concat(itemIndex));}});newSelectedLevels.add(level);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedLevels:newSelectedLevels,selectedItems:newSelectedItems}));};// 层级反选\nconst handleLevelUnselectAll=level=>{var _grouped$level4;const newSelectedItems=new Set(selectionState.selectedItems);const newSelectedLevels=new Set(selectionState.selectedLevels);const grouped=getGroupedData();const dataToUse=getCurrentData();(_grouped$level4=grouped[level])===null||_grouped$level4===void 0?void 0:_grouped$level4.forEach(item=>{const itemIndex=dataToUse.findIndex(d=>d===item);if(itemIndex>=0){newSelectedItems.delete(\"item-\".concat(itemIndex));}});newSelectedLevels.delete(level);setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{selectedLevels:newSelectedLevels,selectedItems:newSelectedItems}));};// 处理下载\nconst handleDownload=()=>{const selectedData=[];const dataToUse=getCurrentData();// 收集选中的数据\ndataToUse.forEach((item,index)=>{const itemKey=\"item-\".concat(index);if(selectionState.selectedItems.has(itemKey)){selectedData.push({index,data:item});}});onDownload({selectedData,format:selectionState.downloadFormat,monthRange:selectionState.monthRange,levelFilter:selectionState.includeLevelFilter?levelFilter:null,statistics,monthPairs});};// 格式化显示值\nconst formatDisplayValue=value=>{if(value===null||value===undefined||value==='')return'';if(typeof value==='object'){if(value.hasOwnProperty('v'))return value.v;if(value.hasOwnProperty('w'))return value.w;if(value.hasOwnProperty('t')&&value.hasOwnProperty('v'))return value.v;if(value.text!==undefined)return value.text;if(value.richText!==undefined)return value.richText;if(value.value!==undefined)return value.value;return String(value);}return String(value);};if(!isOpen)return null;const groupedData=getGroupedData();const levels=getLevels();return/*#__PURE__*/_jsx(\"div\",{className:\"world-class-download-overlay\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"world-class-download-modal\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"modal-header\",children:[/*#__PURE__*/_jsx(\"h2\",{children:\"\\uD83C\\uDF0D \\u9009\\u62E9\\u8981\\u4E0B\\u8F7D\\u7684\\u5BF9\\u6807\\u6570\\u636E\"}),/*#__PURE__*/_jsx(\"button\",{className:\"close-btn\",onClick:onClose,children:\"\\xD7\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"current-filters\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"filter-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-label\",children:\"\\uD83D\\uDCC5 \\u6708\\u4EFD\\u8303\\u56F4:\"}),/*#__PURE__*/_jsx(\"select\",{value:selectionState.monthRange.start,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{monthRange:_objectSpread(_objectSpread({},selectionState.monthRange),{},{start:Number(e.target.value)})})),children:monthPairs.map((pair,index)=>/*#__PURE__*/_jsx(\"option\",{value:index,children:pair[0]},index))}),/*#__PURE__*/_jsx(\"span\",{children:\" \\u81F3 \"}),/*#__PURE__*/_jsx(\"select\",{value:selectionState.monthRange.end,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{monthRange:_objectSpread(_objectSpread({},selectionState.monthRange),{},{end:Number(e.target.value)})})),children:monthPairs.map((pair,index)=>/*#__PURE__*/_jsx(\"option\",{value:index,children:pair[1]},index))})]}),levelFilter.value!=='全部'&&/*#__PURE__*/_jsxs(\"div\",{className:\"filter-item\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"filter-label\",children:\"\\uD83C\\uDFAF \\u5F53\\u524D\\u7B5B\\u9009:\"}),/*#__PURE__*/_jsxs(\"span\",{className:\"filter-value\",children:[levelFilter.level,\" - \",levelFilter.value]}),/*#__PURE__*/_jsxs(\"label\",{className:\"include-filter-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:selectionState.includeLevelFilter,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{includeLevelFilter:e.target.checked}))}),\"\\u5E94\\u7528\\u5230\\u4E0B\\u8F7D\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"levels-section\",children:levels.map(level=>{const levelData=groupedData[level]||[];const isLevelSelected=selectionState.selectedLevels.has(level);return/*#__PURE__*/_jsxs(\"div\",{className:\"level-section\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"level-header\",children:[/*#__PURE__*/_jsxs(\"label\",{className:\"level-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isLevelSelected,onChange:()=>handleLevelSelect(level)}),/*#__PURE__*/_jsxs(\"span\",{className:\"level-title\",children:[level,\" (\",levelData.length,\"\\u9879)\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"level-actions\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleLevelSelectAll(level),children:\"\\u5168\\u9009\"}),/*#__PURE__*/_jsx(\"button\",{className:\"action-btn\",onClick:()=>handleLevelUnselectAll(level),children:\"\\u53CD\\u9009\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"level-items-list\",children:levelData.map((item,levelIndex)=>{const dataToUse=getCurrentData();const globalIndex=dataToUse.findIndex(d=>d===item);const itemKey=\"item-\".concat(globalIndex);const isItemSelected=selectionState.selectedItems.has(itemKey);const target=formatDisplayValue(item['2025年目标']);const responsible=formatDisplayValue(item.负责人);return/*#__PURE__*/_jsx(\"div\",{className:\"level-item-row\",children:/*#__PURE__*/_jsxs(\"label\",{className:\"level-item-checkbox\",children:[/*#__PURE__*/_jsx(\"input\",{type:\"checkbox\",checked:isItemSelected,onChange:()=>handleItemSelect(globalIndex)}),/*#__PURE__*/_jsxs(\"span\",{className:\"level-item-content\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"level-item-target\",children:target}),responsible&&/*#__PURE__*/_jsxs(\"span\",{className:\"level-item-responsible\",children:[\"\\u8D1F\\u8D23\\u4EBA: \",responsible]})]})]})},levelIndex);})})]},level);})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"modal-footer\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"statistics\",children:[/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u5DF2\\u9009\\u62E9: \",statistics.totalItems,\"\\u9879\\u76EE\\u6807\"]}),/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u6DB5\\u76D6\\u5C42\\u7EA7: \",statistics.levelCount,\"\\u79CD\"]}),statistics.monthsCovered&&/*#__PURE__*/_jsxs(\"span\",{children:[\"\\u6708\\u4EFD: \",statistics.monthsCovered]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"format-selection\",children:/*#__PURE__*/_jsxs(\"label\",{children:[\"\\u4E0B\\u8F7D\\u683C\\u5F0F:\",/*#__PURE__*/_jsxs(\"select\",{value:selectionState.downloadFormat,onChange:e=>setSelectionState(_objectSpread(_objectSpread({},selectionState),{},{downloadFormat:e.target.value})),children:[/*#__PURE__*/_jsx(\"option\",{value:\"excel\",children:\"Excel\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"pdf\",children:\"PDF\\u683C\\u5F0F\"}),/*#__PURE__*/_jsx(\"option\",{value:\"csv\",children:\"CSV\\u683C\\u5F0F\"})]})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"action-buttons\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"cancel-btn\",onClick:onClose,children:\"\\u53D6\\u6D88\"}),/*#__PURE__*/_jsxs(\"button\",{className:\"download-btn\",onClick:handleDownload,disabled:statistics.totalItems===0,children:[selectionState.downloadFormat.toUpperCase(),\"\\u4E0B\\u8F7D\"]})]})]})]})});};export default WorldClassDownloadModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsx", "_jsx", "jsxs", "_jsxs", "WorldClassDownloadModal", "_ref", "isOpen", "onClose", "data", "filteredData", "levelFilter", "currentMonthPair", "monthPairs", "onDownload", "selectionState", "setSelectionState", "selectedLevels", "Set", "selectedItems", "downloadFormat", "<PERSON><PERSON><PERSON><PERSON>", "start", "end", "length", "includeLevelFilter", "statistics", "setStatistics", "totalItems", "levelCount", "monthsCovered", "getCurrentData", "value", "getGroupedData", "dataToUse", "levelField", "level", "grouped", "for<PERSON>ach", "item", "push", "getLevels", "Object", "keys", "updateStatistics", "_monthPairs$selection", "_monthPairs$selection2", "levels", "index", "itemKey", "concat", "has", "add", "startMonth", "endMonth", "size", "handleLevelSelect", "newSelectedLevels", "newSelectedItems", "_grouped$level", "delete", "itemIndex", "findIndex", "d", "_grouped$level2", "_objectSpread", "handleItemSelect", "levelItems", "levelItemsSelected", "some", "levelItem", "levelItemIndex", "allLevelItemsSelected", "every", "handleLevelSelectAll", "_grouped$level3", "handleLevelUnselectAll", "_grouped$level4", "handleDownload", "selectedData", "format", "formatDisplayValue", "undefined", "hasOwnProperty", "v", "w", "text", "richText", "String", "groupedData", "className", "children", "onClick", "onChange", "e", "Number", "target", "map", "pair", "type", "checked", "levelData", "isLevelSelected", "levelIndex", "globalIndex", "isItemSelected", "responsible", "负责人", "disabled", "toUpperCase"], "sources": ["E:/处理公司程序/重点工作网页化设计9.10/performance-tracker-web/src/模块三/components/WorldClassDownloadModal.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport './WorldClassDownloadModal.css';\r\n\r\nconst WorldClassDownloadModal = ({ \r\n  isOpen, \r\n  onClose, \r\n  data, \r\n  filteredData,\r\n  levelFilter,\r\n  currentMonthPair,\r\n  monthPairs,\r\n  onDownload \r\n}) => {\r\n  const [selectionState, setSelectionState] = useState({\r\n    selectedLevels: new Set(),\r\n    selectedItems: new Set(),\r\n    downloadFormat: 'excel',\r\n    monthRange: { start: 0, end: monthPairs.length - 1 }, // 月份对索引范围\r\n    includeLevelFilter: true\r\n  });\r\n\r\n  const [statistics, setStatistics] = useState({\r\n    totalItems: 0,\r\n    levelCount: 0,\r\n    monthsCovered: ''\r\n  });\r\n\r\n  // 获取当前使用的数据（筛选后或原始数据）\r\n  const getCurrentData = () => {\r\n    if (levelFilter.value !== '全部' && filteredData && filteredData.length > 0) {\r\n      return filteredData;\r\n    } else if (data && data.length > 0) {\r\n      return data;\r\n    } else {\r\n      return [];\r\n    }\r\n  };\r\n\r\n  // 按层级分组数据\r\n  const getGroupedData = () => {\r\n    const dataToUse = getCurrentData();\r\n    if (!dataToUse || dataToUse.length === 0) {\r\n      return {};\r\n    }\r\n    \r\n    const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : \r\n                      levelFilter.level === '二级' ? '相关指标或方向（二级）' : \r\n                      '相关指标或方向（三级）';\r\n    \r\n    const grouped = {};\r\n    dataToUse.forEach(item => {\r\n      const level = item[levelField] || '其他';\r\n      if (!grouped[level]) {\r\n        grouped[level] = [];\r\n      }\r\n      grouped[level].push(item);\r\n    });\r\n    \r\n    return grouped;\r\n  };\r\n\r\n  // 获取所有层级\r\n  const getLevels = () => {\r\n    const grouped = getGroupedData();\r\n    return Object.keys(grouped);\r\n  };\r\n\r\n  useEffect(() => {\r\n    updateStatistics();\r\n  }, [selectionState.selectedItems, selectionState.monthRange, data, filteredData]);\r\n\r\n  // 计算统计信息\r\n  const updateStatistics = () => {\r\n    let totalItems = 0;\r\n    const levels = new Set();\r\n\r\n    const dataToUse = getCurrentData();\r\n    dataToUse.forEach((item, index) => {\r\n      const itemKey = `item-${index}`;\r\n      if (selectionState.selectedItems.has(itemKey)) {\r\n        totalItems++;\r\n        const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : \r\n                          levelFilter.level === '二级' ? '相关指标或方向（二级）' : \r\n                          '相关指标或方向（三级）';\r\n        if (item[levelField]) {\r\n          levels.add(item[levelField]);\r\n        }\r\n      }\r\n    });\r\n\r\n    // 计算覆盖的月份范围\r\n    const startMonth = monthPairs[selectionState.monthRange.start]?.[0] || '';\r\n    const endMonth = monthPairs[selectionState.monthRange.end]?.[1] || '';\r\n    const monthsCovered = startMonth && endMonth ? `${startMonth} 至 ${endMonth}` : '';\r\n\r\n    setStatistics({\r\n      totalItems,\r\n      levelCount: levels.size,\r\n      monthsCovered\r\n    });\r\n  };\r\n\r\n  // 处理层级选择\r\n  const handleLevelSelect = (level) => {\r\n    const newSelectedLevels = new Set(selectionState.selectedLevels);\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    if (newSelectedLevels.has(level)) {\r\n      // 取消选择层级，移除该层级下的所有项目\r\n      newSelectedLevels.delete(level);\r\n      grouped[level]?.forEach(item => {\r\n        const itemIndex = dataToUse.findIndex(d => d === item);\r\n        if (itemIndex >= 0) {\r\n          newSelectedItems.delete(`item-${itemIndex}`);\r\n        }\r\n      });\r\n    } else {\r\n      // 选择层级，添加该层级下的所有项目\r\n      newSelectedLevels.add(level);\r\n      grouped[level]?.forEach(item => {\r\n        const itemIndex = dataToUse.findIndex(d => d === item);\r\n        if (itemIndex >= 0) {\r\n          newSelectedItems.add(`item-${itemIndex}`);\r\n        }\r\n      });\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedLevels: newSelectedLevels,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 处理单项选择\r\n  const handleItemSelect = (itemIndex) => {\r\n    const itemKey = `item-${itemIndex}`;\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedLevels = new Set(selectionState.selectedLevels);\r\n\r\n    const dataToUse = getCurrentData();\r\n    const item = dataToUse[itemIndex];\r\n    const levelField = levelFilter.level === '一级' ? '相关指标或方向（一级）' : \r\n                      levelFilter.level === '二级' ? '相关指标或方向（二级）' : \r\n                      '相关指标或方向（三级）';\r\n    const level = item?.[levelField] || '其他';\r\n\r\n    if (newSelectedItems.has(itemKey)) {\r\n      newSelectedItems.delete(itemKey);\r\n      // 检查是否需要取消层级选择\r\n      const grouped = getGroupedData();\r\n      const levelItems = grouped[level] || [];\r\n      const levelItemsSelected = levelItems.some(levelItem => {\r\n        const levelItemIndex = dataToUse.findIndex(d => d === levelItem);\r\n        return levelItemIndex >= 0 && newSelectedItems.has(`item-${levelItemIndex}`);\r\n      });\r\n      if (!levelItemsSelected) {\r\n        newSelectedLevels.delete(level);\r\n      }\r\n    } else {\r\n      newSelectedItems.add(itemKey);\r\n      // 检查是否需要添加层级选择\r\n      const grouped = getGroupedData();\r\n      const levelItems = grouped[level] || [];\r\n      const allLevelItemsSelected = levelItems.every(levelItem => {\r\n        const levelItemIndex = dataToUse.findIndex(d => d === levelItem);\r\n        return levelItemIndex >= 0 && (newSelectedItems.has(`item-${levelItemIndex}`) || levelItemIndex === itemIndex);\r\n      });\r\n      if (allLevelItemsSelected) {\r\n        newSelectedLevels.add(level);\r\n      }\r\n    }\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedLevels: newSelectedLevels,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 层级全选\r\n  const handleLevelSelectAll = (level) => {\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedLevels = new Set(selectionState.selectedLevels);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    grouped[level]?.forEach(item => {\r\n      const itemIndex = dataToUse.findIndex(d => d === item);\r\n      if (itemIndex >= 0) {\r\n        newSelectedItems.add(`item-${itemIndex}`);\r\n      }\r\n    });\r\n    newSelectedLevels.add(level);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedLevels: newSelectedLevels,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 层级反选\r\n  const handleLevelUnselectAll = (level) => {\r\n    const newSelectedItems = new Set(selectionState.selectedItems);\r\n    const newSelectedLevels = new Set(selectionState.selectedLevels);\r\n    \r\n    const grouped = getGroupedData();\r\n    const dataToUse = getCurrentData();\r\n    \r\n    grouped[level]?.forEach(item => {\r\n      const itemIndex = dataToUse.findIndex(d => d === item);\r\n      if (itemIndex >= 0) {\r\n        newSelectedItems.delete(`item-${itemIndex}`);\r\n      }\r\n    });\r\n    newSelectedLevels.delete(level);\r\n\r\n    setSelectionState({\r\n      ...selectionState,\r\n      selectedLevels: newSelectedLevels,\r\n      selectedItems: newSelectedItems\r\n    });\r\n  };\r\n\r\n  // 处理下载\r\n  const handleDownload = () => {\r\n    const selectedData = [];\r\n    const dataToUse = getCurrentData();\r\n    \r\n    // 收集选中的数据\r\n    dataToUse.forEach((item, index) => {\r\n      const itemKey = `item-${index}`;\r\n      if (selectionState.selectedItems.has(itemKey)) {\r\n        selectedData.push({\r\n          index,\r\n          data: item\r\n        });\r\n      }\r\n    });\r\n\r\n    onDownload({\r\n      selectedData,\r\n      format: selectionState.downloadFormat,\r\n      monthRange: selectionState.monthRange,\r\n      levelFilter: selectionState.includeLevelFilter ? levelFilter : null,\r\n      statistics,\r\n      monthPairs\r\n    });\r\n  };\r\n\r\n  // 格式化显示值\r\n  const formatDisplayValue = (value) => {\r\n    if (value === null || value === undefined || value === '') return '';\r\n    if (typeof value === 'object') {\r\n      if (value.hasOwnProperty('v')) return value.v;\r\n      if (value.hasOwnProperty('w')) return value.w;\r\n      if (value.hasOwnProperty('t') && value.hasOwnProperty('v')) return value.v;\r\n      if (value.text !== undefined) return value.text;\r\n      if (value.richText !== undefined) return value.richText;\r\n      if (value.value !== undefined) return value.value;\r\n      return String(value);\r\n    }\r\n    return String(value);\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  const groupedData = getGroupedData();\r\n  const levels = getLevels();\r\n\r\n  return (\r\n    <div className=\"world-class-download-overlay\">\r\n      <div className=\"world-class-download-modal\">\r\n        <div className=\"modal-header\">\r\n          <h2>🌍 选择要下载的对标数据</h2>\r\n          <button className=\"close-btn\" onClick={onClose}>×</button>\r\n        </div>\r\n\r\n        <div className=\"modal-content\">\r\n          {/* 筛选条件显示 */}\r\n          <div className=\"current-filters\">\r\n            <div className=\"filter-item\">\r\n              <span className=\"filter-label\">📅 月份范围:</span>\r\n              <select\r\n                value={selectionState.monthRange.start}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  monthRange: { ...selectionState.monthRange, start: Number(e.target.value) }\r\n                })}\r\n              >\r\n                {monthPairs.map((pair, index) => (\r\n                  <option key={index} value={index}>{pair[0]}</option>\r\n                ))}\r\n              </select>\r\n              <span> 至 </span>\r\n              <select\r\n                value={selectionState.monthRange.end}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  monthRange: { ...selectionState.monthRange, end: Number(e.target.value) }\r\n                })}\r\n              >\r\n                {monthPairs.map((pair, index) => (\r\n                  <option key={index} value={index}>{pair[1]}</option>\r\n                ))}\r\n              </select>\r\n            </div>\r\n            \r\n            {levelFilter.value !== '全部' && (\r\n              <div className=\"filter-item\">\r\n                <span className=\"filter-label\">🎯 当前筛选:</span>\r\n                <span className=\"filter-value\">{levelFilter.level} - {levelFilter.value}</span>\r\n                <label className=\"include-filter-checkbox\">\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={selectionState.includeLevelFilter}\r\n                    onChange={(e) => setSelectionState({\r\n                      ...selectionState,\r\n                      includeLevelFilter: e.target.checked\r\n                    })}\r\n                  />\r\n                  应用到下载\r\n                </label>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* 层级选择 */}\r\n          <div className=\"levels-section\">\r\n            {levels.map(level => {\r\n              const levelData = groupedData[level] || [];\r\n              const isLevelSelected = selectionState.selectedLevels.has(level);\r\n              \r\n              return (\r\n                <div key={level} className=\"level-section\">\r\n                  <div className=\"level-header\">\r\n                    <label className=\"level-checkbox\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        checked={isLevelSelected}\r\n                        onChange={() => handleLevelSelect(level)}\r\n                      />\r\n                      <span className=\"level-title\">\r\n                        {level} ({levelData.length}项)\r\n                      </span>\r\n                    </label>\r\n                    <div className=\"level-actions\">\r\n                      <button \r\n                        className=\"action-btn\"\r\n                        onClick={() => handleLevelSelectAll(level)}\r\n                      >\r\n                        全选\r\n                      </button>\r\n                      <button \r\n                        className=\"action-btn\"\r\n                        onClick={() => handleLevelUnselectAll(level)}\r\n                      >\r\n                        反选\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"level-items-list\">\r\n                    {levelData.map((item, levelIndex) => {\r\n                      const dataToUse = getCurrentData();\r\n                      const globalIndex = dataToUse.findIndex(d => d === item);\r\n                      const itemKey = `item-${globalIndex}`;\r\n                      const isItemSelected = selectionState.selectedItems.has(itemKey);\r\n                      \r\n                      const target = formatDisplayValue(item['2025年目标']);\r\n                      const responsible = formatDisplayValue(item.负责人);\r\n\r\n                      return (\r\n                        <div key={levelIndex} className=\"level-item-row\">\r\n                          <label className=\"level-item-checkbox\">\r\n                            <input\r\n                              type=\"checkbox\"\r\n                              checked={isItemSelected}\r\n                              onChange={() => handleItemSelect(globalIndex)}\r\n                            />\r\n                            <span className=\"level-item-content\">\r\n                              <span className=\"level-item-target\">{target}</span>\r\n                              {responsible && (\r\n                                <span className=\"level-item-responsible\">负责人: {responsible}</span>\r\n                              )}\r\n                            </span>\r\n                          </label>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"modal-footer\">\r\n          <div className=\"statistics\">\r\n            <span>已选择: {statistics.totalItems}项目标</span>\r\n            <span>涵盖层级: {statistics.levelCount}种</span>\r\n            {statistics.monthsCovered && (\r\n              <span>月份: {statistics.monthsCovered}</span>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"format-selection\">\r\n            <label>\r\n              下载格式:\r\n              <select \r\n                value={selectionState.downloadFormat}\r\n                onChange={(e) => setSelectionState({\r\n                  ...selectionState,\r\n                  downloadFormat: e.target.value\r\n                })}\r\n              >\r\n                <option value=\"excel\">Excel格式</option>\r\n                <option value=\"pdf\">PDF格式</option>\r\n                <option value=\"csv\">CSV格式</option>\r\n              </select>\r\n            </label>\r\n          </div>\r\n\r\n          <div className=\"action-buttons\">\r\n            <button className=\"cancel-btn\" onClick={onClose}>\r\n              取消\r\n            </button>\r\n            <button \r\n              className=\"download-btn\"\r\n              onClick={handleDownload}\r\n              disabled={statistics.totalItems === 0}\r\n            >\r\n              {selectionState.downloadFormat.toUpperCase()}下载\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WorldClassDownloadModal; "], "mappings": "+MAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEvC,KAAM,CAAAC,uBAAuB,CAAGC,IAAA,EAS1B,IAT2B,CAC/BC,MAAM,CACNC,OAAO,CACPC,IAAI,CACJC,YAAY,CACZC,WAAW,CACXC,gBAAgB,CAChBC,UAAU,CACVC,UACF,CAAC,CAAAR,IAAA,CACC,KAAM,CAACS,cAAc,CAAEC,iBAAiB,CAAC,CAAGjB,QAAQ,CAAC,CACnDkB,cAAc,CAAE,GAAI,CAAAC,GAAG,CAAC,CAAC,CACzBC,aAAa,CAAE,GAAI,CAAAD,GAAG,CAAC,CAAC,CACxBE,cAAc,CAAE,OAAO,CACvBC,UAAU,CAAE,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAEV,UAAU,CAACW,MAAM,CAAG,CAAE,CAAC,CAAE;AACtDC,kBAAkB,CAAE,IACtB,CAAC,CAAC,CAEF,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG5B,QAAQ,CAAC,CAC3C6B,UAAU,CAAE,CAAC,CACbC,UAAU,CAAE,CAAC,CACbC,aAAa,CAAE,EACjB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,GAAIpB,WAAW,CAACqB,KAAK,GAAK,IAAI,EAAItB,YAAY,EAAIA,YAAY,CAACc,MAAM,CAAG,CAAC,CAAE,CACzE,MAAO,CAAAd,YAAY,CACrB,CAAC,IAAM,IAAID,IAAI,EAAIA,IAAI,CAACe,MAAM,CAAG,CAAC,CAAE,CAClC,MAAO,CAAAf,IAAI,CACb,CAAC,IAAM,CACL,MAAO,EAAE,CACX,CACF,CAAC,CAED;AACA,KAAM,CAAAwB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,SAAS,CAAGH,cAAc,CAAC,CAAC,CAClC,GAAI,CAACG,SAAS,EAAIA,SAAS,CAACV,MAAM,GAAK,CAAC,CAAE,CACxC,MAAO,CAAC,CAAC,CACX,CAEA,KAAM,CAAAW,UAAU,CAAGxB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC3CzB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1C,aAAa,CAE/B,KAAM,CAAAC,OAAO,CAAG,CAAC,CAAC,CAClBH,SAAS,CAACI,OAAO,CAACC,IAAI,EAAI,CACxB,KAAM,CAAAH,KAAK,CAAGG,IAAI,CAACJ,UAAU,CAAC,EAAI,IAAI,CACtC,GAAI,CAACE,OAAO,CAACD,KAAK,CAAC,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,CAAG,EAAE,CACrB,CACAC,OAAO,CAACD,KAAK,CAAC,CAACI,IAAI,CAACD,IAAI,CAAC,CAC3B,CAAC,CAAC,CAEF,MAAO,CAAAF,OAAO,CAChB,CAAC,CAED;AACA,KAAM,CAAAI,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAJ,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,MAAO,CAAAS,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAC7B,CAAC,CAEDrC,SAAS,CAAC,IAAM,CACd4C,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,CAAC7B,cAAc,CAACI,aAAa,CAAEJ,cAAc,CAACM,UAAU,CAAEZ,IAAI,CAAEC,YAAY,CAAC,CAAC,CAEjF;AACA,KAAM,CAAAkC,gBAAgB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,sBAAA,CAC7B,GAAI,CAAAlB,UAAU,CAAG,CAAC,CAClB,KAAM,CAAAmB,MAAM,CAAG,GAAI,CAAA7B,GAAG,CAAC,CAAC,CAExB,KAAM,CAAAgB,SAAS,CAAGH,cAAc,CAAC,CAAC,CAClCG,SAAS,CAACI,OAAO,CAAC,CAACC,IAAI,CAAES,KAAK,GAAK,CACjC,KAAM,CAAAC,OAAO,SAAAC,MAAA,CAAWF,KAAK,CAAE,CAC/B,GAAIjC,cAAc,CAACI,aAAa,CAACgC,GAAG,CAACF,OAAO,CAAC,CAAE,CAC7CrB,UAAU,EAAE,CACZ,KAAM,CAAAO,UAAU,CAAGxB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC3CzB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1C,aAAa,CAC/B,GAAIG,IAAI,CAACJ,UAAU,CAAC,CAAE,CACpBY,MAAM,CAACK,GAAG,CAACb,IAAI,CAACJ,UAAU,CAAC,CAAC,CAC9B,CACF,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAkB,UAAU,CAAG,EAAAR,qBAAA,CAAAhC,UAAU,CAACE,cAAc,CAACM,UAAU,CAACC,KAAK,CAAC,UAAAuB,qBAAA,iBAA3CA,qBAAA,CAA8C,CAAC,CAAC,GAAI,EAAE,CACzE,KAAM,CAAAS,QAAQ,CAAG,EAAAR,sBAAA,CAAAjC,UAAU,CAACE,cAAc,CAACM,UAAU,CAACE,GAAG,CAAC,UAAAuB,sBAAA,iBAAzCA,sBAAA,CAA4C,CAAC,CAAC,GAAI,EAAE,CACrE,KAAM,CAAAhB,aAAa,CAAGuB,UAAU,EAAIC,QAAQ,IAAAJ,MAAA,CAAMG,UAAU,aAAAH,MAAA,CAAMI,QAAQ,EAAK,EAAE,CAEjF3B,aAAa,CAAC,CACZC,UAAU,CACVC,UAAU,CAAEkB,MAAM,CAACQ,IAAI,CACvBzB,aACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAA0B,iBAAiB,CAAIpB,KAAK,EAAK,CACnC,KAAM,CAAAqB,iBAAiB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,cAAc,CAAC,CAChE,KAAM,CAAAyC,gBAAgB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAE9D,KAAM,CAAAkB,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGH,cAAc,CAAC,CAAC,CAElC,GAAI0B,iBAAiB,CAACN,GAAG,CAACf,KAAK,CAAC,CAAE,KAAAuB,cAAA,CAChC;AACAF,iBAAiB,CAACG,MAAM,CAACxB,KAAK,CAAC,CAC/B,CAAAuB,cAAA,CAAAtB,OAAO,CAACD,KAAK,CAAC,UAAAuB,cAAA,iBAAdA,cAAA,CAAgBrB,OAAO,CAACC,IAAI,EAAI,CAC9B,KAAM,CAAAsB,SAAS,CAAG3B,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKxB,IAAI,CAAC,CACtD,GAAIsB,SAAS,EAAI,CAAC,CAAE,CAClBH,gBAAgB,CAACE,MAAM,SAAAV,MAAA,CAASW,SAAS,CAAE,CAAC,CAC9C,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,KAAAG,eAAA,CACL;AACAP,iBAAiB,CAACL,GAAG,CAAChB,KAAK,CAAC,CAC5B,CAAA4B,eAAA,CAAA3B,OAAO,CAACD,KAAK,CAAC,UAAA4B,eAAA,iBAAdA,eAAA,CAAgB1B,OAAO,CAACC,IAAI,EAAI,CAC9B,KAAM,CAAAsB,SAAS,CAAG3B,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKxB,IAAI,CAAC,CACtD,GAAIsB,SAAS,EAAI,CAAC,CAAE,CAClBH,gBAAgB,CAACN,GAAG,SAAAF,MAAA,CAASW,SAAS,CAAE,CAAC,CAC3C,CACF,CAAC,CAAC,CACJ,CAEA7C,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,cAAc,CAAEwC,iBAAiB,CACjCtC,aAAa,CAAEuC,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAQ,gBAAgB,CAAIL,SAAS,EAAK,CACtC,KAAM,CAAAZ,OAAO,SAAAC,MAAA,CAAWW,SAAS,CAAE,CACnC,KAAM,CAAAH,gBAAgB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAsC,iBAAiB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,cAAc,CAAC,CAEhE,KAAM,CAAAiB,SAAS,CAAGH,cAAc,CAAC,CAAC,CAClC,KAAM,CAAAQ,IAAI,CAAGL,SAAS,CAAC2B,SAAS,CAAC,CACjC,KAAM,CAAA1B,UAAU,CAAGxB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC3CzB,WAAW,CAACyB,KAAK,GAAK,IAAI,CAAG,aAAa,CAC1C,aAAa,CAC/B,KAAM,CAAAA,KAAK,CAAG,CAAAG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAGJ,UAAU,CAAC,GAAI,IAAI,CAExC,GAAIuB,gBAAgB,CAACP,GAAG,CAACF,OAAO,CAAC,CAAE,CACjCS,gBAAgB,CAACE,MAAM,CAACX,OAAO,CAAC,CAChC;AACA,KAAM,CAAAZ,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAkC,UAAU,CAAG9B,OAAO,CAACD,KAAK,CAAC,EAAI,EAAE,CACvC,KAAM,CAAAgC,kBAAkB,CAAGD,UAAU,CAACE,IAAI,CAACC,SAAS,EAAI,CACtD,KAAM,CAAAC,cAAc,CAAGrC,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKO,SAAS,CAAC,CAChE,MAAO,CAAAC,cAAc,EAAI,CAAC,EAAIb,gBAAgB,CAACP,GAAG,SAAAD,MAAA,CAASqB,cAAc,CAAE,CAAC,CAC9E,CAAC,CAAC,CACF,GAAI,CAACH,kBAAkB,CAAE,CACvBX,iBAAiB,CAACG,MAAM,CAACxB,KAAK,CAAC,CACjC,CACF,CAAC,IAAM,CACLsB,gBAAgB,CAACN,GAAG,CAACH,OAAO,CAAC,CAC7B;AACA,KAAM,CAAAZ,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAkC,UAAU,CAAG9B,OAAO,CAACD,KAAK,CAAC,EAAI,EAAE,CACvC,KAAM,CAAAoC,qBAAqB,CAAGL,UAAU,CAACM,KAAK,CAACH,SAAS,EAAI,CAC1D,KAAM,CAAAC,cAAc,CAAGrC,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKO,SAAS,CAAC,CAChE,MAAO,CAAAC,cAAc,EAAI,CAAC,GAAKb,gBAAgB,CAACP,GAAG,SAAAD,MAAA,CAASqB,cAAc,CAAE,CAAC,EAAIA,cAAc,GAAKV,SAAS,CAAC,CAChH,CAAC,CAAC,CACF,GAAIW,qBAAqB,CAAE,CACzBf,iBAAiB,CAACL,GAAG,CAAChB,KAAK,CAAC,CAC9B,CACF,CAEApB,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,cAAc,CAAEwC,iBAAiB,CACjCtC,aAAa,CAAEuC,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAgB,oBAAoB,CAAItC,KAAK,EAAK,KAAAuC,eAAA,CACtC,KAAM,CAAAjB,gBAAgB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAsC,iBAAiB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,cAAc,CAAC,CAEhE,KAAM,CAAAoB,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGH,cAAc,CAAC,CAAC,CAElC,CAAA4C,eAAA,CAAAtC,OAAO,CAACD,KAAK,CAAC,UAAAuC,eAAA,iBAAdA,eAAA,CAAgBrC,OAAO,CAACC,IAAI,EAAI,CAC9B,KAAM,CAAAsB,SAAS,CAAG3B,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKxB,IAAI,CAAC,CACtD,GAAIsB,SAAS,EAAI,CAAC,CAAE,CAClBH,gBAAgB,CAACN,GAAG,SAAAF,MAAA,CAASW,SAAS,CAAE,CAAC,CAC3C,CACF,CAAC,CAAC,CACFJ,iBAAiB,CAACL,GAAG,CAAChB,KAAK,CAAC,CAE5BpB,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,cAAc,CAAEwC,iBAAiB,CACjCtC,aAAa,CAAEuC,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAkB,sBAAsB,CAAIxC,KAAK,EAAK,KAAAyC,eAAA,CACxC,KAAM,CAAAnB,gBAAgB,CAAG,GAAI,CAAAxC,GAAG,CAACH,cAAc,CAACI,aAAa,CAAC,CAC9D,KAAM,CAAAsC,iBAAiB,CAAG,GAAI,CAAAvC,GAAG,CAACH,cAAc,CAACE,cAAc,CAAC,CAEhE,KAAM,CAAAoB,OAAO,CAAGJ,cAAc,CAAC,CAAC,CAChC,KAAM,CAAAC,SAAS,CAAGH,cAAc,CAAC,CAAC,CAElC,CAAA8C,eAAA,CAAAxC,OAAO,CAACD,KAAK,CAAC,UAAAyC,eAAA,iBAAdA,eAAA,CAAgBvC,OAAO,CAACC,IAAI,EAAI,CAC9B,KAAM,CAAAsB,SAAS,CAAG3B,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKxB,IAAI,CAAC,CACtD,GAAIsB,SAAS,EAAI,CAAC,CAAE,CAClBH,gBAAgB,CAACE,MAAM,SAAAV,MAAA,CAASW,SAAS,CAAE,CAAC,CAC9C,CACF,CAAC,CAAC,CACFJ,iBAAiB,CAACG,MAAM,CAACxB,KAAK,CAAC,CAE/BpB,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IACZlD,cAAc,MACjBE,cAAc,CAAEwC,iBAAiB,CACjCtC,aAAa,CAAEuC,gBAAgB,EAChC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAoB,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,YAAY,CAAG,EAAE,CACvB,KAAM,CAAA7C,SAAS,CAAGH,cAAc,CAAC,CAAC,CAElC;AACAG,SAAS,CAACI,OAAO,CAAC,CAACC,IAAI,CAAES,KAAK,GAAK,CACjC,KAAM,CAAAC,OAAO,SAAAC,MAAA,CAAWF,KAAK,CAAE,CAC/B,GAAIjC,cAAc,CAACI,aAAa,CAACgC,GAAG,CAACF,OAAO,CAAC,CAAE,CAC7C8B,YAAY,CAACvC,IAAI,CAAC,CAChBQ,KAAK,CACLvC,IAAI,CAAE8B,IACR,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEFzB,UAAU,CAAC,CACTiE,YAAY,CACZC,MAAM,CAAEjE,cAAc,CAACK,cAAc,CACrCC,UAAU,CAAEN,cAAc,CAACM,UAAU,CACrCV,WAAW,CAAEI,cAAc,CAACU,kBAAkB,CAAGd,WAAW,CAAG,IAAI,CACnEe,UAAU,CACVb,UACF,CAAC,CAAC,CACJ,CAAC,CAED;AACA,KAAM,CAAAoE,kBAAkB,CAAIjD,KAAK,EAAK,CACpC,GAAIA,KAAK,GAAK,IAAI,EAAIA,KAAK,GAAKkD,SAAS,EAAIlD,KAAK,GAAK,EAAE,CAAE,MAAO,EAAE,CACpE,GAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7B,GAAIA,KAAK,CAACmD,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAnD,KAAK,CAACoD,CAAC,CAC7C,GAAIpD,KAAK,CAACmD,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAnD,KAAK,CAACqD,CAAC,CAC7C,GAAIrD,KAAK,CAACmD,cAAc,CAAC,GAAG,CAAC,EAAInD,KAAK,CAACmD,cAAc,CAAC,GAAG,CAAC,CAAE,MAAO,CAAAnD,KAAK,CAACoD,CAAC,CAC1E,GAAIpD,KAAK,CAACsD,IAAI,GAAKJ,SAAS,CAAE,MAAO,CAAAlD,KAAK,CAACsD,IAAI,CAC/C,GAAItD,KAAK,CAACuD,QAAQ,GAAKL,SAAS,CAAE,MAAO,CAAAlD,KAAK,CAACuD,QAAQ,CACvD,GAAIvD,KAAK,CAACA,KAAK,GAAKkD,SAAS,CAAE,MAAO,CAAAlD,KAAK,CAACA,KAAK,CACjD,MAAO,CAAAwD,MAAM,CAACxD,KAAK,CAAC,CACtB,CACA,MAAO,CAAAwD,MAAM,CAACxD,KAAK,CAAC,CACtB,CAAC,CAED,GAAI,CAACzB,MAAM,CAAE,MAAO,KAAI,CAExB,KAAM,CAAAkF,WAAW,CAAGxD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAc,MAAM,CAAGN,SAAS,CAAC,CAAC,CAE1B,mBACEvC,IAAA,QAAKwF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CvF,KAAA,QAAKsF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,eACzCvF,KAAA,QAAKsF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BzF,IAAA,OAAAyF,QAAA,CAAI,2EAAa,CAAI,CAAC,cACtBzF,IAAA,WAAQwF,SAAS,CAAC,WAAW,CAACE,OAAO,CAAEpF,OAAQ,CAAAmF,QAAA,CAAC,MAAC,CAAQ,CAAC,EACvD,CAAC,cAENvF,KAAA,QAAKsF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAE5BvF,KAAA,QAAKsF,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BvF,KAAA,QAAKsF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzF,IAAA,SAAMwF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wCAAQ,CAAM,CAAC,cAC9CzF,IAAA,WACE8B,KAAK,CAAEjB,cAAc,CAACM,UAAU,CAACC,KAAM,CACvCuE,QAAQ,CAAGC,CAAC,EAAK9E,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBM,UAAU,CAAA4C,aAAA,CAAAA,aAAA,IAAOlD,cAAc,CAACM,UAAU,MAAEC,KAAK,CAAEyE,MAAM,CAACD,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAC,EAAE,EAC5E,CAAE,CAAA2D,QAAA,CAEF9E,UAAU,CAACoF,GAAG,CAAC,CAACC,IAAI,CAAElD,KAAK,gBAC1B9C,IAAA,WAAoB8B,KAAK,CAAEgB,KAAM,CAAA2C,QAAA,CAAEO,IAAI,CAAC,CAAC,CAAC,EAA7BlD,KAAsC,CACpD,CAAC,CACI,CAAC,cACT9C,IAAA,SAAAyF,QAAA,CAAM,UAAG,CAAM,CAAC,cAChBzF,IAAA,WACE8B,KAAK,CAAEjB,cAAc,CAACM,UAAU,CAACE,GAAI,CACrCsE,QAAQ,CAAGC,CAAC,EAAK9E,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBM,UAAU,CAAA4C,aAAA,CAAAA,aAAA,IAAOlD,cAAc,CAACM,UAAU,MAAEE,GAAG,CAAEwE,MAAM,CAACD,CAAC,CAACE,MAAM,CAAChE,KAAK,CAAC,EAAE,EAC1E,CAAE,CAAA2D,QAAA,CAEF9E,UAAU,CAACoF,GAAG,CAAC,CAACC,IAAI,CAAElD,KAAK,gBAC1B9C,IAAA,WAAoB8B,KAAK,CAAEgB,KAAM,CAAA2C,QAAA,CAAEO,IAAI,CAAC,CAAC,CAAC,EAA7BlD,KAAsC,CACpD,CAAC,CACI,CAAC,EACN,CAAC,CAELrC,WAAW,CAACqB,KAAK,GAAK,IAAI,eACzB5B,KAAA,QAAKsF,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BzF,IAAA,SAAMwF,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wCAAQ,CAAM,CAAC,cAC9CvF,KAAA,SAAMsF,SAAS,CAAC,cAAc,CAAAC,QAAA,EAAEhF,WAAW,CAACyB,KAAK,CAAC,KAAG,CAACzB,WAAW,CAACqB,KAAK,EAAO,CAAC,cAC/E5B,KAAA,UAAOsF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACxCzF,IAAA,UACEiG,IAAI,CAAC,UAAU,CACfC,OAAO,CAAErF,cAAc,CAACU,kBAAmB,CAC3CoE,QAAQ,CAAGC,CAAC,EAAK9E,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBU,kBAAkB,CAAEqE,CAAC,CAACE,MAAM,CAACI,OAAO,EACrC,CAAE,CACJ,CAAC,iCAEJ,EAAO,CAAC,EACL,CACN,EACE,CAAC,cAGNlG,IAAA,QAAKwF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAC5B5C,MAAM,CAACkD,GAAG,CAAC7D,KAAK,EAAI,CACnB,KAAM,CAAAiE,SAAS,CAAGZ,WAAW,CAACrD,KAAK,CAAC,EAAI,EAAE,CAC1C,KAAM,CAAAkE,eAAe,CAAGvF,cAAc,CAACE,cAAc,CAACkC,GAAG,CAACf,KAAK,CAAC,CAEhE,mBACEhC,KAAA,QAAiBsF,SAAS,CAAC,eAAe,CAAAC,QAAA,eACxCvF,KAAA,QAAKsF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvF,KAAA,UAAOsF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC/BzF,IAAA,UACEiG,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEE,eAAgB,CACzBT,QAAQ,CAAEA,CAAA,GAAMrC,iBAAiB,CAACpB,KAAK,CAAE,CAC1C,CAAC,cACFhC,KAAA,SAAMsF,SAAS,CAAC,aAAa,CAAAC,QAAA,EAC1BvD,KAAK,CAAC,IAAE,CAACiE,SAAS,CAAC7E,MAAM,CAAC,SAC7B,EAAM,CAAC,EACF,CAAC,cACRpB,KAAA,QAAKsF,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5BzF,IAAA,WACEwF,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMlB,oBAAoB,CAACtC,KAAK,CAAE,CAAAuD,QAAA,CAC5C,cAED,CAAQ,CAAC,cACTzF,IAAA,WACEwF,SAAS,CAAC,YAAY,CACtBE,OAAO,CAAEA,CAAA,GAAMhB,sBAAsB,CAACxC,KAAK,CAAE,CAAAuD,QAAA,CAC9C,cAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,cAENzF,IAAA,QAAKwF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,CAC9BU,SAAS,CAACJ,GAAG,CAAC,CAAC1D,IAAI,CAAEgE,UAAU,GAAK,CACnC,KAAM,CAAArE,SAAS,CAAGH,cAAc,CAAC,CAAC,CAClC,KAAM,CAAAyE,WAAW,CAAGtE,SAAS,CAAC4B,SAAS,CAACC,CAAC,EAAIA,CAAC,GAAKxB,IAAI,CAAC,CACxD,KAAM,CAAAU,OAAO,SAAAC,MAAA,CAAWsD,WAAW,CAAE,CACrC,KAAM,CAAAC,cAAc,CAAG1F,cAAc,CAACI,aAAa,CAACgC,GAAG,CAACF,OAAO,CAAC,CAEhE,KAAM,CAAA+C,MAAM,CAAGf,kBAAkB,CAAC1C,IAAI,CAAC,SAAS,CAAC,CAAC,CAClD,KAAM,CAAAmE,WAAW,CAAGzB,kBAAkB,CAAC1C,IAAI,CAACoE,GAAG,CAAC,CAEhD,mBACEzG,IAAA,QAAsBwF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC9CvF,KAAA,UAAOsF,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACpCzF,IAAA,UACEiG,IAAI,CAAC,UAAU,CACfC,OAAO,CAAEK,cAAe,CACxBZ,QAAQ,CAAEA,CAAA,GAAM3B,gBAAgB,CAACsC,WAAW,CAAE,CAC/C,CAAC,cACFpG,KAAA,SAAMsF,SAAS,CAAC,oBAAoB,CAAAC,QAAA,eAClCzF,IAAA,SAAMwF,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAEK,MAAM,CAAO,CAAC,CAClDU,WAAW,eACVtG,KAAA,SAAMsF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,EAAC,sBAAK,CAACe,WAAW,EAAO,CAClE,EACG,CAAC,EACF,CAAC,EAbAH,UAcL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,GAxDEnE,KAyDL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,EACH,CAAC,cAENhC,KAAA,QAAKsF,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BvF,KAAA,QAAKsF,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBvF,KAAA,SAAAuF,QAAA,EAAM,sBAAK,CAACjE,UAAU,CAACE,UAAU,CAAC,oBAAG,EAAM,CAAC,cAC5CxB,KAAA,SAAAuF,QAAA,EAAM,4BAAM,CAACjE,UAAU,CAACG,UAAU,CAAC,QAAC,EAAM,CAAC,CAC1CH,UAAU,CAACI,aAAa,eACvB1B,KAAA,SAAAuF,QAAA,EAAM,gBAAI,CAACjE,UAAU,CAACI,aAAa,EAAO,CAC3C,EACE,CAAC,cAEN5B,IAAA,QAAKwF,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cAC/BvF,KAAA,UAAAuF,QAAA,EAAO,2BAEL,cAAAvF,KAAA,WACE4B,KAAK,CAAEjB,cAAc,CAACK,cAAe,CACrCyE,QAAQ,CAAGC,CAAC,EAAK9E,iBAAiB,CAAAiD,aAAA,CAAAA,aAAA,IAC7BlD,cAAc,MACjBK,cAAc,CAAE0E,CAAC,CAACE,MAAM,CAAChE,KAAK,EAC/B,CAAE,CAAA2D,QAAA,eAEHzF,IAAA,WAAQ8B,KAAK,CAAC,OAAO,CAAA2D,QAAA,CAAC,mBAAO,CAAQ,CAAC,cACtCzF,IAAA,WAAQ8B,KAAK,CAAC,KAAK,CAAA2D,QAAA,CAAC,iBAAK,CAAQ,CAAC,cAClCzF,IAAA,WAAQ8B,KAAK,CAAC,KAAK,CAAA2D,QAAA,CAAC,iBAAK,CAAQ,CAAC,EAC5B,CAAC,EACJ,CAAC,CACL,CAAC,cAENvF,KAAA,QAAKsF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BzF,IAAA,WAAQwF,SAAS,CAAC,YAAY,CAACE,OAAO,CAAEpF,OAAQ,CAAAmF,QAAA,CAAC,cAEjD,CAAQ,CAAC,cACTvF,KAAA,WACEsF,SAAS,CAAC,cAAc,CACxBE,OAAO,CAAEd,cAAe,CACxB8B,QAAQ,CAAElF,UAAU,CAACE,UAAU,GAAK,CAAE,CAAA+D,QAAA,EAErC5E,cAAc,CAACK,cAAc,CAACyF,WAAW,CAAC,CAAC,CAAC,cAC/C,EAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}